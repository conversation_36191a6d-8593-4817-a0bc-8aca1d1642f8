<?php
// This file was auto-generated from sdk-root/src/data/elasticloadbalancingv2/2015-12-01/paginators-1.json
return [ 'pagination' => [ 'DescribeListenerCertificates' => [ 'input_token' => 'Marker', 'output_token' => 'NextMarker', 'result_key' => 'Certificates', ], 'DescribeListeners' => [ 'input_token' => 'Marker', 'output_token' => 'NextMarker', 'result_key' => 'Listeners', ], 'DescribeLoadBalancers' => [ 'input_token' => 'Marker', 'output_token' => 'NextMarker', 'result_key' => 'LoadBalancers', ], 'DescribeRules' => [ 'input_token' => 'Marker', 'output_token' => 'NextMarker', 'result_key' => 'Rules', ], 'DescribeTargetGroups' => [ 'input_token' => 'Marker', 'output_token' => 'NextMarker', 'result_key' => 'TargetGroups', ], 'DescribeTrustStoreAssociations' => [ 'input_token' => 'Marker', 'limit_key' => 'PageSize', 'output_token' => 'NextMarker', ], 'DescribeTrustStoreRevocations' => [ 'input_token' => 'Marker', 'limit_key' => 'PageSize', 'output_token' => 'NextMarker', ], 'DescribeTrustStores' => [ 'input_token' => 'Marker', 'limit_key' => 'PageSize', 'output_token' => 'NextMarker', ], ],];
