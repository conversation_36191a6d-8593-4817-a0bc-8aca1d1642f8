{"__meta": {"id": "Xf496f3f93ac251581c631d09352eb6b5", "datetime": "2025-06-30 23:10:20", "utime": **********.802925, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.323739, "end": **********.802951, "duration": 0.47921204566955566, "duration_str": "479ms", "measures": [{"label": "Booting", "start": **********.323739, "relative_start": 0, "end": **********.720285, "relative_end": **********.720285, "duration": 0.3965458869934082, "duration_str": "397ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.720293, "relative_start": 0.39655399322509766, "end": **********.802953, "relative_end": 1.9073486328125e-06, "duration": 0.08265995979309082, "duration_str": "82.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45707168, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.026269999999999998, "accumulated_duration_str": "26.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.74684, "duration": 0.02515, "duration_str": "25.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.737}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.784832, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.737, "width_percent": 1.941}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7909548, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.678, "width_percent": 2.322}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1553644494 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1553644494\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1255365349 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1255365349\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-555158001 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-555158001\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1052796421 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751325019250%7C11%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhHeHZlMnJTMXAxU0hHN3MxVGxiQUE9PSIsInZhbHVlIjoiQjlMNUloeUs4UU5SUU1qdUdsWTUzcmN2U1lGbmcrYVhWM0RIVW9DS3dmS2l6d0tMOUtnV2EwVStiR0ZqakJQUENUalg3VVdtM3pVQzNnQkRBZUVGeHowb1dmZ1MyaSs1Z2I3SmpTc3dLQWRBYmhndnNhM0w0ajd2VDBCY2xidEcwTkU5N2tYYjdvME5JZDZXb25rdjRYSW45NTBJakNpZGpyMm1aNW1lWjc1TW95SjVRN2ZqRXE4M3krR0g1RnNzcmVEOTFFamYxcXZUVXJsMmpzcHhuaFNVSVJtWnU4VSsvNGlRUFA4a09Yc1lzN3hUUitQZXJwd0JMMTUreEVTMjFiWEtVS3BjdndrR1d5WTRHV3hpRUN4c2hhdnIyeHo4Yk5hZWx0ZGdzbDQ4YVM3Sm1wNGdGb2lKeHZieFhObEgyYUpINkhGcUI1ZVNpSEt5cFlUUEU5VlJLSzBxYm1nUzRZZ0JQYWNwcFpUeldDUVRVMXNJSjdDUmxYM0dWZWw0TktweG5iQVk0TUhySEUrZ2hoWkd5VHdab1IyZDh2NkRoazAwc2lKcXBXaWJyTS9TVEVjb3pVaGtkc21nd0xhUWhrMFVGY24rMWF4WGZWd3A0bHIrd1ZpNlQ2NGs1MnptM2tzWnkvTkpsN3ovbjhCUlhhSWJPTUtKY1Era09mR2QiLCJtYWMiOiIzNWFlODExNzcxM2JiNWIxOGYyOTBiNGNjZjk2MGFmNDA5Y2JlOWNjMWMyMWRiZDE2NzA5ZDg5NzE2MzBiNDQyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ikp5anlwL3RTREVyc1FaSVhBZitPNUE9PSIsInZhbHVlIjoiVUdOSXF5NUZlS2xvWFhTZmJ1UktneC9KczZtWTBWY0NPMFpjZDJCelVScGZSWjRxMUhOYVRFOGI5YTRsOG4rYzlYRVA2L1NHOWdpZStxQ0NBZUtHMWhaYUxNV3NiSTVYS3BoK1ZkdjhEN2pZdWZlUlpIN2tqQ2Fza2hNNE9RSlM0bUlEZ2toWHU5WmZxVFFLYVBTU0x5VndBbmJRQURVcXA4OTk4OTlzK3NtdjhtMFNOOHlnemZ6b1pyQ1FmelNxN2lFelhHY1V6V0plS3Y3dFhYcGlJLytKMWpSaVQ2bE9XVGMzZjJBNVo3ZnQ2M2tLYWZVMTdRZVVWVGRJaUFNSXhuQys2dVUza3JxN2ZwR0w0R3kvVk92UnZQTllGSVREMGo4TkE5TE0ycTF6N1JPVmJnMG5IS3kyUUE2dFdMMjlOZmZ5Wk1zU3JLUlpiUGN5V2N4c0h1SUh0UE93UThkeUQzOHROa2ljcUlWUmVSSnNGZmZzL1EzSTZRVVNGaHpvYko4a0VWamRZdmFjL3lvbGsxLzQ0dWdUcUkwaGg5Qnh4M3c0WTdyQnZQZnhhaUVLMkNQK0g0Z3JoRUNsZnpJbmhoL2JaWDJob1BPbjVXUTdVN0Nid00xd2I3M3F0cWtsRGpLSmFVN2dneGV4N0FoQ3ltOGhVSjhlT0NrMnF0d1oiLCJtYWMiOiIyM2IzOGYyYTEzODQwZmM3ZGQzN2M4YmJhMmFlM2MwMzkyMWNmZjY1NzJkMGRjMzZmMTg4ZjU1NTQ1ZThhMGI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1052796421\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-511171749 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-511171749\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2121910001 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:10:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRQVXdKQ2V2SmM5eDJJWFNRZXZxbnc9PSIsInZhbHVlIjoiV1VWQWdrVnc0aHNnQ2ZlZkxublB2OEdjNGhOQXkyaXVlWTRydHpqck1CaVF2dGkvUGF3bitHZEdxTVIydGI4MVdoaURDZ0c1WlQ2ZCtOOXM3L0hZdXFrV1VWT1BvV3UzRXdVekVNeGhpZ2lodlVBRW8xTDVPY0lnWHpPWFlHSDZEZXVOU2tRWlpGdVVyMnN6NEdrM1VSREp2QWlvUzV5VWtJQzlvaFNpREQvUlMwYnJUekk1YWp0SGt3MGJBOFJsU2RidGt1YnorVDdqa2lCK3FkMzBmZmpwbTBPemlDNlRDa0pTWGRjRElsRCthM05Tc2swdklnZXNKRTNnTVFhU0tFa1ZoaTJERWVsaDlWSTNHZTQxSG5mOHQ1Q0hxWUMxMHpXd1hncDY0Y2RVKzQwcmZZdmRlSVZnWE1rUXpkQVdYVThyZkZmb08rUzZkN1ltQUlBeTZTRk1CSkkrNGh5azViai8vNGs5WW1YUkF1ejRTMzN3TFN0cndPN0p1RkVJV3g1ZG9wMjh2R1pjeWo1L3paVVVOZUo0c0Q3QzNsTnpEQllmbmMzZEFjd0R4M2Q1YnkwV0tXN0ZWYzU2UDF3WXo5b0JvckhybUpKQzJBTVdVOWdlREVWSm5ON2VsSVlSV0hpYUVqR0lMdm1QR3dkWVBrbmhiL2UvT0dzVGZRa0kiLCJtYWMiOiJmOWYwNTlkMTZlMzMzZTljNDI4MDE5OTE0YzYwNmY1YzgxYWJhMTBjMjBkYmZjZTRhYTUwMjMyYzlkZjZjMDM1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjdkSzF0aEp0Ty9TeUdDOWo2YUZVYUE9PSIsInZhbHVlIjoieEVZMWVlWlJ1cFZzZ0NmK25nRnhPY1MrRjk1V25ldjNPSGJacGoweXE2K2FGNitobjhpRkVDVjVnTFBPVFZPRmVDWndxdGFmOG5kbWhoa1FBcVBGOHhNTTRsREhiNHhDcWpiZGhXVzNSVENHNllmWnh3RkF6cWxhcWJDUlNzdWdpOVI0czVkK0lLMzRudmpSTnFUQWpxbXZ4K0ZDOFJVYnpMUE5ta1NyRkIzNy9jMDI3US84ek5RdXJhQVE0dXVxWXNaWlNuVlhpM28zaWhjU2lMQ2VJY3RBY29JTEhZTThuYnVJZUFsTXYxdmQ0OEhUdldoN1p3SlY4T0swTGY0c1RRNDV4UTd5ZTdFT2ZxMTdMVGs1VlBCVXh3R2YxV2dNNG5ZYUxLTGFOenFJeGQ0Rml2RzhGd1NvMVNTNmtiaVR5UGx6RnVLSGpibWZMaThRbmZiZWp1ZWFBNURiUDdndmhrdzdNS09tUDRBSjIyNDZjUXdkZjFUdkRNUHplcjJFa2Qxd2FMN1poaXFiQWdYQjVDTGp4a2FNdEF2UHluZmFrT1hCMElobUdMWk1OWjdvY21vVGQxVzQ1alI0eUFzaTNaWVUwUkQwQ1Zuc1BlWllSdEtJVnVmU0M4ZFFmc0tMRCtTcnFEUm80RHBXdERiNU05eVFWUmZTWVY5RGxTdnciLCJtYWMiOiIyNWU3NzYzYTAwMDY2NzQ4MjIyMzQ2MjAzM2EwM2M3ZTA3NzBkMzk5OGIyM2I4ZTljZmEyYmVhNTdmNDJmZWNjIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRQVXdKQ2V2SmM5eDJJWFNRZXZxbnc9PSIsInZhbHVlIjoiV1VWQWdrVnc0aHNnQ2ZlZkxublB2OEdjNGhOQXkyaXVlWTRydHpqck1CaVF2dGkvUGF3bitHZEdxTVIydGI4MVdoaURDZ0c1WlQ2ZCtOOXM3L0hZdXFrV1VWT1BvV3UzRXdVekVNeGhpZ2lodlVBRW8xTDVPY0lnWHpPWFlHSDZEZXVOU2tRWlpGdVVyMnN6NEdrM1VSREp2QWlvUzV5VWtJQzlvaFNpREQvUlMwYnJUekk1YWp0SGt3MGJBOFJsU2RidGt1YnorVDdqa2lCK3FkMzBmZmpwbTBPemlDNlRDa0pTWGRjRElsRCthM05Tc2swdklnZXNKRTNnTVFhU0tFa1ZoaTJERWVsaDlWSTNHZTQxSG5mOHQ1Q0hxWUMxMHpXd1hncDY0Y2RVKzQwcmZZdmRlSVZnWE1rUXpkQVdYVThyZkZmb08rUzZkN1ltQUlBeTZTRk1CSkkrNGh5azViai8vNGs5WW1YUkF1ejRTMzN3TFN0cndPN0p1RkVJV3g1ZG9wMjh2R1pjeWo1L3paVVVOZUo0c0Q3QzNsTnpEQllmbmMzZEFjd0R4M2Q1YnkwV0tXN0ZWYzU2UDF3WXo5b0JvckhybUpKQzJBTVdVOWdlREVWSm5ON2VsSVlSV0hpYUVqR0lMdm1QR3dkWVBrbmhiL2UvT0dzVGZRa0kiLCJtYWMiOiJmOWYwNTlkMTZlMzMzZTljNDI4MDE5OTE0YzYwNmY1YzgxYWJhMTBjMjBkYmZjZTRhYTUwMjMyYzlkZjZjMDM1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjdkSzF0aEp0Ty9TeUdDOWo2YUZVYUE9PSIsInZhbHVlIjoieEVZMWVlWlJ1cFZzZ0NmK25nRnhPY1MrRjk1V25ldjNPSGJacGoweXE2K2FGNitobjhpRkVDVjVnTFBPVFZPRmVDWndxdGFmOG5kbWhoa1FBcVBGOHhNTTRsREhiNHhDcWpiZGhXVzNSVENHNllmWnh3RkF6cWxhcWJDUlNzdWdpOVI0czVkK0lLMzRudmpSTnFUQWpxbXZ4K0ZDOFJVYnpMUE5ta1NyRkIzNy9jMDI3US84ek5RdXJhQVE0dXVxWXNaWlNuVlhpM28zaWhjU2lMQ2VJY3RBY29JTEhZTThuYnVJZUFsTXYxdmQ0OEhUdldoN1p3SlY4T0swTGY0c1RRNDV4UTd5ZTdFT2ZxMTdMVGs1VlBCVXh3R2YxV2dNNG5ZYUxLTGFOenFJeGQ0Rml2RzhGd1NvMVNTNmtiaVR5UGx6RnVLSGpibWZMaThRbmZiZWp1ZWFBNURiUDdndmhrdzdNS09tUDRBSjIyNDZjUXdkZjFUdkRNUHplcjJFa2Qxd2FMN1poaXFiQWdYQjVDTGp4a2FNdEF2UHluZmFrT1hCMElobUdMWk1OWjdvY21vVGQxVzQ1alI0eUFzaTNaWVUwUkQwQ1Zuc1BlWllSdEtJVnVmU0M4ZFFmc0tMRCtTcnFEUm80RHBXdERiNU05eVFWUmZTWVY5RGxTdnciLCJtYWMiOiIyNWU3NzYzYTAwMDY2NzQ4MjIyMzQ2MjAzM2EwM2M3ZTA3NzBkMzk5OGIyM2I4ZTljZmEyYmVhNTdmNDJmZWNjIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2121910001\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2051777778 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2051777778\", {\"maxDepth\":0})</script>\n"}}