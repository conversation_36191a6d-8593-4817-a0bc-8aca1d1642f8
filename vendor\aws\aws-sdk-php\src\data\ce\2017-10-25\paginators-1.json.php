<?php
// This file was auto-generated from sdk-root/src/data/ce/2017-10-25/paginators-1.json
return [ 'pagination' => [ 'GetSavingsPlansCoverage' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'GetSavingsPlansUtilizationDetails' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'ListCostAllocationTagBackfillHistory' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'ListCostAllocationTags' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'ListCostCategoryDefinitions' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], ],];
