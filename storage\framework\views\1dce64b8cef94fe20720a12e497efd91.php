<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('أوامر الاستلام')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('الرئيسية')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('إدارة العمليات المالية')); ?></li>
    <li class="breadcrumb-item"><?php echo e(__('أوامر الاستلام')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('.datatable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
        },
        "order": [[ 6, "desc" ]], // Sort by created date
        "pageLength": 25,
        "responsive": true
    });

    // View details modal
    $('.view-details').on('click', function() {
        var orderId = $(this).data('order-id');
        var orderType = $(this).data('order-type');
        
        // You can implement AJAX call here to get order details
        $('#orderDetailsModal').modal('show');
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('أوامر الاستلام')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        <?php if(Auth::user()->can('manage warehouse') || Auth::user()->hasRole('Cashier')): ?>
            <a href="<?php echo e(route('receipt-order.create')); ?>" class="btn btn-sm btn-primary"
               data-bs-toggle="tooltip" title="<?php echo e(__('إنشاء أمر استلام جديد')); ?>">
                <i class="ti ti-plus"></i> <?php echo e(__('إنشاء أمر استلام')); ?>

            </a>
        <?php endif; ?>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('قائمة أوامر الاستلام')); ?></h5>
                </div>
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table datatable">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('رقم الأمر')); ?></th>
                                    <th><?php echo e(__('نوع الأمر')); ?></th>
                                    <th><?php echo e(__('المورد/المصدر')); ?></th>
                                    <th><?php echo e(__('المستودع')); ?></th>
                                    <th><?php echo e(__('المستخدم المنشئ')); ?></th>
                                    <th><?php echo e(__('المبلغ الإجمالي')); ?></th>
                                    <th><?php echo e(__('التاريخ')); ?></th>
                                    <th><?php echo e(__('تاريخ الإنشاء')); ?></th>
                                    <th><?php echo e(__('الإجراءات')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $receiptOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo e($order['reference_number']); ?></strong>
                                        </td>
                                        <td>
                                            <?php if($order['type'] === 'استلام بضاعة'): ?>
                                                <span class="badge bg-success"><?php echo e($order['type']); ?></span>
                                            <?php else: ?>
                                                <span class="badge bg-info"><?php echo e($order['type']); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($order['vendor_name']); ?></td>
                                        <td><?php echo e($order['warehouse_name']); ?></td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo e($order['creator_name']); ?></span>
                                        </td>
                                        <td>
                                            <?php if($order['total_amount'] > 0): ?>
                                                <?php echo e(number_format($order['total_amount'], 2)); ?>

                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e(\App\Models\Utility::getDateFormated($order['date'])); ?></td>
                                        <td><?php echo e($order['created_at']->format('Y-m-d H:i')); ?></td>
                                        <td>
                                            <?php if(Auth::user()->can('manage warehouse') || Auth::user()->can('show financial record') || Auth::user()->hasRole('company')): ?>
                                                <div class="action-btn bg-warning ms-2">
                                                    <a href="<?php echo e(route('receipt-order.edit', $order['id'])); ?>"
                                                       class="mx-3 btn btn-sm align-items-center"
                                                       data-bs-toggle="tooltip"
                                                       title="<?php echo e(__('تحرير الأمر')); ?>">
                                                        <i class="ti ti-edit text-white"></i>
                                                    </a>
                                                </div>
                                            <?php endif; ?>

                                            <div class="action-btn bg-info ms-2">
                                                <a href="<?php echo e(route('receipt-order.show', $order['id'])); ?>"
                                                   class="mx-3 btn btn-sm align-items-center"
                                                   data-bs-toggle="tooltip"
                                                   title="<?php echo e(__('عرض التفاصيل')); ?>">
                                                    <i class="ti ti-eye text-white"></i>
                                                </a>
                                            </div>

                                            <div class="action-btn bg-secondary ms-2">
                                                <a href="<?php echo e(route('receipt-order.show', $order['id'])); ?>?print=1"
                                                   class="mx-3 btn btn-sm align-items-center"
                                                   data-bs-toggle="tooltip"
                                                   title="<?php echo e(__('طباعة الفاتورة')); ?>"
                                                   target="_blank">
                                                    <i class="ti ti-printer text-white"></i>
                                                </a>
                                            </div>

                                            <div class="action-btn bg-danger ms-2">
                                                <a href="<?php echo e(route('receipt-order.pdf', $order['id'])); ?>"
                                                   class="mx-3 btn btn-sm align-items-center"
                                                   data-bs-toggle="tooltip"
                                                   title="<?php echo e(__('تحميل PDF')); ?>">
                                                    <i class="ti ti-file-type-pdf text-white"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Details Modal -->
    <div class="modal fade" id="orderDetailsModal" tabindex="-1" aria-labelledby="orderDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="orderDetailsModalLabel"><?php echo e(__('تفاصيل أمر الاستلام')); ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="orderDetailsContent">
                        <!-- Order details will be loaded here -->
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden"><?php echo e(__('جاري التحميل...')); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('إغلاق')); ?></button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="theme-avtar bg-primary">
                            <i class="ti ti-package"></i>
                        </div>
                        <div class="ms-3">
                            <small class="text-muted"><?php echo e(__('إجمالي أوامر الاستلام')); ?></small>
                            <h6 class="m-0"><?php echo e($receiptOrders->count()); ?></h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="theme-avtar bg-success">
                            <i class="ti ti-truck-delivery"></i>
                        </div>
                        <div class="ms-3">
                            <small class="text-muted"><?php echo e(__('أوامر استلام البضاعة')); ?></small>
                            <h6 class="m-0"><?php echo e($receiptOrders->where('type', 'استلام بضاعة')->count()); ?></h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="theme-avtar bg-info">
                            <i class="ti ti-arrows-exchange"></i>
                        </div>
                        <div class="ms-3">
                            <small class="text-muted"><?php echo e(__('أوامر نقل البضاعة')); ?></small>
                            <h6 class="m-0"><?php echo e($receiptOrders->where('type', 'نقل بضاعة')->count()); ?></h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="theme-avtar bg-warning">
                            <i class="ti ti-currency-dollar"></i>
                        </div>
                        <div class="ms-3">
                            <small class="text-muted"><?php echo e(__('إجمالي القيمة')); ?></small>
                            <h6 class="m-0"><?php echo e(number_format($receiptOrders->sum('total_amount'), 2)); ?></h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('النشاط الأخير')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <?php $__currentLoopData = $receiptOrders->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <?php if($order['type'] === 'استلام بضاعة'): ?>
                                        <i class="ti ti-truck-delivery text-success"></i>
                                    <?php else: ?>
                                        <i class="ti ti-arrows-exchange text-info"></i>
                                    <?php endif; ?>
                                </div>
                                <div class="timeline-content">
                                    <h6 class="mb-1"><?php echo e($order['type']); ?> - <?php echo e($order['reference_number']); ?></h6>
                                    <p class="mb-1 text-muted">
                                        <?php echo e($order['vendor_name']); ?> → <?php echo e($order['warehouse_name']); ?>

                                        <?php if($order['total_amount'] > 0): ?>
                                            <span class="badge bg-primary"><?php echo e(number_format($order['total_amount'], 2)); ?></span>
                                        <?php endif; ?>
                                    </p>
                                    <small class="text-muted"><?php echo e($order['created_at']->diffForHumans()); ?></small>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('style-page'); ?>
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -30px;
    top: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #fff;
    border: 2px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -21px;
    top: 10px;
    bottom: 0;
    width: 2px;
    background: #ddd;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\erpq22\resources\views/receipt_order/index.blade.php ENDPATH**/ ?>