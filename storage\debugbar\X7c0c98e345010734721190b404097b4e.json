{"__meta": {"id": "X7c0c98e345010734721190b404097b4e", "datetime": "2025-06-30 22:40:46", "utime": **********.047689, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323245.564477, "end": **********.047703, "duration": 0.48322606086730957, "duration_str": "483ms", "measures": [{"label": "Booting", "start": 1751323245.564477, "relative_start": 0, "end": 1751323245.980954, "relative_end": 1751323245.980954, "duration": 0.4164769649505615, "duration_str": "416ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751323245.980967, "relative_start": 0.4164900779724121, "end": **********.047705, "relative_end": 1.9073486328125e-06, "duration": 0.06673789024353027, "duration_str": "66.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45349208, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0036599999999999996, "accumulated_duration_str": "3.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.022828, "duration": 0.00259, "duration_str": "2.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 70.765}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0377111, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 70.765, "width_percent": 12.842}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0410879, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 83.607, "width_percent": 16.393}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1946319734 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323149690%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRFWndDbmNtMTJwRkp0ZlhrRTRwTEE9PSIsInZhbHVlIjoieDhld3RBaTdsY2llckNVRCtvSWpXRDFnQU91emdOWjBUVm4zZWlUb3BEeS9CVWhvZ0JscGo5VFd3Rlg1cTFKWURVdEhtYklJaFd2aWJibUtONEovbVNqSGF4c0NET2R2RVl0cmx1aUJlSzdicHJ3dTdjcnRwT3IvUnFNdVFjZk9ramZkRExjTlBWazdDT3dtTTd6alhkb2ZkbUdid0RJWXErYVRPZ3FUc2FIY0xuOXNjaytSNENsNzVtd0NrUU5hOTJIendnUjdGRW9tNnN1b29ULzV2VlJTMzB4c1d6MkFySXJoNkp6RXR3enR1RUZDTG1ZVFUzYzdaT09zeitkbnhkTjd1M3VaZ3E0UVRUNWZpQ2ZRelh1RXFjYUIxd1FRblVZWE9zc01mM2JkNCtLcGJ1Qnk5djdaV0RrL0pOT2U3RVRsSndOVFBWcjc1MEdRSjZGZTUramw4UElNVWIvL0VhN2lkTFFkR0ZTdkw3ejl0Z0duejl1YlBvMVJMQjdVM3YxWWY1Q0xPWE1NMmF5RHVOUmtjSk1QUlhTM09mbk1URi9KSUZacEs2QTByM1dSRVR2OW1NY0lUU2cxV1JxSFBlOHQ0c3ZVSys5bGVuQXBCdWRoekdWUWxQcXpZT1RKeHpEbGN0RHVyNUZWVzYxNk9hOUVySDlKcTFzRTVPcW4iLCJtYWMiOiJjOTUzMjk2YjIxMmE2NTdjNWUzODE3ZDFjNGQ5MzNjNjFmOTZmNjI2ZDBmZTc1NzU2NDI3NzE0MjBiZTNhMzg3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkJydC9Vb01uc25xVVVOZGN5YXJOb3c9PSIsInZhbHVlIjoiN2d4ZXIwbDQ5OWVhbFNwKy9RcVIzTmpxV3BhcGtwaEZoTHVXMlR6YS8zSDRQRUFWSXg2OGlHNzZYTXFhSWNhVWRUZkJiWEZoNEZTSlJBYmtFeXc1NWM4YkJsS1JLK0hIY1Nld3VIb3UrUndYMGFhY0tGdG1EYUpPLzFVWk9MRHdaeHFnQXNhbnFoc1BxcExxVVBXRHc1eU8wbjU3NXYxc1dyN2Z1WjNjN1lMaVBhOU54cnVRUHF2RW52UXEya0VkUmdTNERiZFlMTFJMbVF4dXkwUFFUMHM2dG5UTHhOUS9WVDI4YlV0WjlhLy9JQW8zUUVWRk9KdzRVK2tYSzJFSW92L2hnL0o2eGx4UGZNaEcyUGcyY2Irc0swVFdjakRzRlh5NzhjVkRzK3dzSDgxUzdXQzJjaW94V0F6WjZLSjdnUHRKUnhQVlRndUk3Y3FNb0tSUUZ6NXJ4V3dsc2RiQk1KVEs1S01udlA2WVJRWkhyZzl5aWRVNXpMYnc3NUxwUWRoNXRkMUNUVDBxWVhraWtldnM0WExZYUtLTDNyVjJpTC9lenk5Wjh6TnREMHhhK1B6Rm5zRkNJZVRiVlRIMWFhOU1DRHNBZWZ1cEx1cGlVUjh3MmpEU1IzUC9yRmZEZTEvUFR2S3N6ejY0M2d4S1QzSFBnSmxWWk95RkVQUk4iLCJtYWMiOiIzZDhhMTE3ZmRiMTQwYzZiOTI1MDIwMTJiOTFiZmYyMzExMDIxZGExNWQ1MTc1NGRlMGY1MTA4NDQwMmUwNjA2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1946319734\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-888763510 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-888763510\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1442371063 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:40:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ill4MXpRL0hCTHpWTnJ2NHVVR3Y4WVE9PSIsInZhbHVlIjoiSk91MEtsbTlLM2hac0NrRUJWbmVxbGZicHVPdkN3RzE2ajA2dmlUdFJnc1BsMDZUa29tak9RUFZZNVFtd3MzWFFJU2dFaWE4cmhLbUszTEZiMDRpTVFLOG1YdlcxcE0rcnRmMGNoODhSOU5NQmVIY0tvNTdwVGh6OWFqZWJHZTNNSkNsNUYyWnllMnY5b3pGWlFvWG5RbVhhTGVZVWNDR09OWWtrTUtRcHE5SWZud0FrWXVzWmhGeTFvS3hDeUZ2bmsyTU5UK1BrK3V6d1RLaWRNRWtSYzN6LzQ3cmpXUWhVa2ttM2xoYkFtUmVSYTFlS1ZpWFQ3M3lHNnlQWVhZa2dYQVlUcnpwNXloQmFPb1kzVE5BTHFuK09VTUJ1MHZWZTcxNzBidVByUFl0T2V0N1hsOFYvTlRvM3gyR3BxbHU0Q3hZQTk4Q0ExUTNSYzE2QzZxaGZ2NWdjdmZQbFAwTytSOE10RGhiS1o3TU5JOEd0djl2OE4rcmxnc0FWaUtTMEZ0T0hSRDcrZkR5UHV1TU5SdWtncEZLME9NQzN1QkdqL2Z3S2tPa1pXTFJ6SFRlY3UwUU5wcTJRaHBlL1gzbmpxNmVZUmpFd0JFUDU5VGF1bm5Ea3d6RTZ3WlNKOGdFRk9oZzNUQS9HUkc3cS9xeWUwN2Z4MHdqZnp2eUpjeFgiLCJtYWMiOiI2YWI2MDk3MzcyNGM5ZWMyMjY3ZjI3YWMzZGJiNDhhMjYwMTIxMzVlZGJhMDVjZDkwYWVhNzUyOGRmZWIzYjNiIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlF2S3p6Z0ZjUHovOUJ2bFRhWXBxNmc9PSIsInZhbHVlIjoieC9KVWtISnJTNGl1UmUzZ2R4MEdiVE9vNXBwU1NwV1prcGVZYmtpeHk3SzN5MTNreWQrMkMzNEt6MUNpd2U0UUxadXdQcnJ2dStUdDRGNkFKTDZCcmw5NG9CdlFhL1ZYTGRrS08rZUszNzZiZUhkK0tRNE9zQlVWMTBZdWtRTmk3VGJ6bWZCQmRUOEJRQncrcWxaMms0RXljN2lnNVNCZ1hzaTAvcnM4SUV3ejRDaGoyZmdKTFd4elpyKzRscVhNTVRvM1Z1R05qYVBsRFNsNTkrYXMwUHYyblZLbmgvcXJ6VnhrL3JvaFB3RWl0MUxRd0lLNGtJVUNGZDI2d0NMa2VBSStxRUNCRml1TUhMcDk2WnNxSlhXY1Fka0ZUNkNvL1k2WEl0V29ScGpQR0pzN3ZTWXl2LzhJcGhqMEZXL1htdEpDU2x3VnRtZnR5aTFZV3JuaytoZlFDdWY3aVBGMkpENnluK1ZoWkYrMFkwcHNBUGY5Sm5kQ1owUzlSekovMDFjMjB6eWZyUUlCVFZ5bDI4UHRqUVcra2F5NWZGK2R4alErOWJ4VDJ0VWhSVE5qaVJCbUFTVU4vRGVneEdubElGOVBmN2JtMGlKR0VWVjdaWERLdkFTZ0paYkVNZUZrRldCU0dZb3IxeURVb1BNT3BJVVdyaTJGM3YrcWJwMWwiLCJtYWMiOiIyZjM2M2ExN2E2OTJiYjM4NTQ0ZDZmZTk3MzM2NmVjODg3Y2RlNDhiMWU5NWQ2MDI0ZjUzYTZlNDY1YWFjZDJhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ill4MXpRL0hCTHpWTnJ2NHVVR3Y4WVE9PSIsInZhbHVlIjoiSk91MEtsbTlLM2hac0NrRUJWbmVxbGZicHVPdkN3RzE2ajA2dmlUdFJnc1BsMDZUa29tak9RUFZZNVFtd3MzWFFJU2dFaWE4cmhLbUszTEZiMDRpTVFLOG1YdlcxcE0rcnRmMGNoODhSOU5NQmVIY0tvNTdwVGh6OWFqZWJHZTNNSkNsNUYyWnllMnY5b3pGWlFvWG5RbVhhTGVZVWNDR09OWWtrTUtRcHE5SWZud0FrWXVzWmhGeTFvS3hDeUZ2bmsyTU5UK1BrK3V6d1RLaWRNRWtSYzN6LzQ3cmpXUWhVa2ttM2xoYkFtUmVSYTFlS1ZpWFQ3M3lHNnlQWVhZa2dYQVlUcnpwNXloQmFPb1kzVE5BTHFuK09VTUJ1MHZWZTcxNzBidVByUFl0T2V0N1hsOFYvTlRvM3gyR3BxbHU0Q3hZQTk4Q0ExUTNSYzE2QzZxaGZ2NWdjdmZQbFAwTytSOE10RGhiS1o3TU5JOEd0djl2OE4rcmxnc0FWaUtTMEZ0T0hSRDcrZkR5UHV1TU5SdWtncEZLME9NQzN1QkdqL2Z3S2tPa1pXTFJ6SFRlY3UwUU5wcTJRaHBlL1gzbmpxNmVZUmpFd0JFUDU5VGF1bm5Ea3d6RTZ3WlNKOGdFRk9oZzNUQS9HUkc3cS9xeWUwN2Z4MHdqZnp2eUpjeFgiLCJtYWMiOiI2YWI2MDk3MzcyNGM5ZWMyMjY3ZjI3YWMzZGJiNDhhMjYwMTIxMzVlZGJhMDVjZDkwYWVhNzUyOGRmZWIzYjNiIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlF2S3p6Z0ZjUHovOUJ2bFRhWXBxNmc9PSIsInZhbHVlIjoieC9KVWtISnJTNGl1UmUzZ2R4MEdiVE9vNXBwU1NwV1prcGVZYmtpeHk3SzN5MTNreWQrMkMzNEt6MUNpd2U0UUxadXdQcnJ2dStUdDRGNkFKTDZCcmw5NG9CdlFhL1ZYTGRrS08rZUszNzZiZUhkK0tRNE9zQlVWMTBZdWtRTmk3VGJ6bWZCQmRUOEJRQncrcWxaMms0RXljN2lnNVNCZ1hzaTAvcnM4SUV3ejRDaGoyZmdKTFd4elpyKzRscVhNTVRvM1Z1R05qYVBsRFNsNTkrYXMwUHYyblZLbmgvcXJ6VnhrL3JvaFB3RWl0MUxRd0lLNGtJVUNGZDI2d0NMa2VBSStxRUNCRml1TUhMcDk2WnNxSlhXY1Fka0ZUNkNvL1k2WEl0V29ScGpQR0pzN3ZTWXl2LzhJcGhqMEZXL1htdEpDU2x3VnRtZnR5aTFZV3JuaytoZlFDdWY3aVBGMkpENnluK1ZoWkYrMFkwcHNBUGY5Sm5kQ1owUzlSekovMDFjMjB6eWZyUUlCVFZ5bDI4UHRqUVcra2F5NWZGK2R4alErOWJ4VDJ0VWhSVE5qaVJCbUFTVU4vRGVneEdubElGOVBmN2JtMGlKR0VWVjdaWERLdkFTZ0paYkVNZUZrRldCU0dZb3IxeURVb1BNT3BJVVdyaTJGM3YrcWJwMWwiLCJtYWMiOiIyZjM2M2ExN2E2OTJiYjM4NTQ0ZDZmZTk3MzM2NmVjODg3Y2RlNDhiMWU5NWQ2MDI0ZjUzYTZlNDY1YWFjZDJhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1442371063\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}