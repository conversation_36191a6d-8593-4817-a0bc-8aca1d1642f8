{"__meta": {"id": "Xbfd063943d93dc604ed759b325194a41", "datetime": "2025-06-30 23:10:48", "utime": **********.483909, "method": "GET", "uri": "/add-to-cart/2354/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751325047.964444, "end": **********.483926, "duration": 0.5194821357727051, "duration_str": "519ms", "measures": [{"label": "Booting", "start": 1751325047.964444, "relative_start": 0, "end": **********.373176, "relative_end": **********.373176, "duration": 0.40873217582702637, "duration_str": "409ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.373192, "relative_start": 0.40874814987182617, "end": **********.483928, "relative_end": 1.9073486328125e-06, "duration": 0.11073589324951172, "duration_str": "111ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48660264, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00713, "accumulated_duration_str": "7.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.425129, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 27.209}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4390929, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 27.209, "width_percent": 8.275}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.456568, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 35.484, "width_percent": 9.116}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.459505, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 44.6, "width_percent": 7.714}, {"sql": "select * from `product_services` where `product_services`.`id` = '2354' limit 1", "type": "query", "params": [], "bindings": ["2354"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4649358, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 52.314, "width_percent": 6.031}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2354 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2354", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.469515, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 58.345, "width_percent": 36.606}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.473637, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 94.951, "width_percent": 5.049}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-815088556 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-815088556\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.463866, "xdebug_link": null}]}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:3 [\n  2352 => array:9 [\n    \"name\" => \"مجسم شخصيات مختلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"2352\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2353 => array:8 [\n    \"name\" => \"مفتاح علبه\"\n    \"quantity\" => 1\n    \"price\" => \"6.00\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"id\" => \"2353\"\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n  ]\n  2354 => array:8 [\n    \"name\" => \"العاب شكل بطه\"\n    \"quantity\" => 1\n    \"price\" => \"6.50\"\n    \"tax\" => 0\n    \"subtotal\" => 6.5\n    \"id\" => \"2354\"\n    \"originalquantity\" => 9\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2354/pos", "status_code": "<pre class=sf-dump id=sf-dump-1834098241 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1834098241\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1774663689 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1774663689\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1911302522 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1911302522\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1729847579 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751325027058%7C14%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpybFFtWVRjOS9DWEx1Y0kyNEJBNVE9PSIsInZhbHVlIjoiUGFEWlN2SHZyRmJsb3djaWY3UmRwNnFmYWsydHpjcUc0cXZoYTlrNDUzQ0dEeFBwaHVFT2JaV3dSOTNvelp4eWpHZ3NyS3Qrdm5rSWw1TWRYWmV2N1Q1YjYvQndrcVdDZ094dEtNeXJDS2dhSFRBSm1jUTJKRW9HTE1HM0s5dE5UREsrY2NUMzNPY3RUZjlqTlMrK05Va1Q0eUpJSDBqdU1FYnJsR0h0d1h0b2VoUTdXRmpkUWRhU0ZpT2tNd0ZXNk5yTHRVdURTYnM3bzA0QnArRUhqcmw1ZmVoaGFvM29RVElSM3dZSmlsR1pKOGtKdHE1bUhyR1BXRFJnZzNuWHlFQm1FdFFDY2RuRGcxUWdSaS9wbzNwMmNTL2NyOVhxL0VMN1VWd1NhVEorVjRNR052dVpXWC9ub0cxcXpTbXpPS0VpNVNweTB4VlNmVTd4Tys4V2xyTEQ5M1c3dDE1aDhXbEpqR2VnRHJqUnM2cFpqbWpsV2dvWUdoaXpnOXpuQjI4NlVidE03MHNUOTIxZEgvRlBKT2doRTZ0citid3FGUHpjOXdrQ1VqQXhvSy93S1pkUEtsMU5oZ0MvRFpVa24zMGZ2T1B1dk9aV1ZZWUZTcFIzSHBvWjV4cWMzQTMxVCtGbDZLQ3RJaFMyTGxOZVNxaVV2S1VDSFhHRTlLTUoiLCJtYWMiOiJiMjhmZWIxZGNhYmYxNGMyZDYxMTJkZTk3YjBhZWExZWM1NTY5ZjgwZjI3MGJkYTAwNWY3YTdlY2ViNTQ4MzYxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InhzLzhlSXVsdE9xQlRQSDk4V0hET0E9PSIsInZhbHVlIjoiQlZDN0xZVDlDdzJObjJWNDhjcGxBb0M3NG1EZFpISE1uVUxhMnBVVTV2eTZrZkh4UzIweVNOcHpLU0xSc1NVWE1FcGRHb2Z5cXZkRzNvZXRDNExUb1JkcllqaTJBNk5xbGc4cU5tOE1Db0lWVk94a0lJdHZyczhtTUU4WUhBYmZueXZzZFYyaW1OWUtNblpYcXVOQXVMenIxVERjd1pDNnVaejRwSHNqczZaZ3ZhR09BM0p1enNzejlxMXRyK2tROS9LY2VzbEV4Z2ZBa2hKSDl2alJFbnhBTURQTzBOOFFvdHJNeUhJVVpaUWhkR2xocG9pQ0EzVHRFb3daK1ZGZWhYS0N1MFhJVVlNWFNmalBJZmE5YzlTYjVMZjNVUVBaQ0FpOUQyU2wwVE1SL1p2U3paSkhPQitlaWVFVXJZcERTeFNsS0dRWitWazg4Z2tVWHp0eWVreVFNSzFYekVpOFgwUTNkWWxQUWlGODBuU3VjZGZBcEU3aUVoV0hDN3cwWE90L1JhZHlENllYd0xjbHJ4TEJFUlJEdHhEVCtQMUZxKzZ6TnVIQjNUV3VlUHJWbUZ4OEYxQ0ppZTQ0WWZHSU1MYUIwMDhKNlc2OWx2TWtPYmpNZTVyQmx4QmozRjBGL3VlK0pSVTJoZkhtSmFYclhJOUpjRm5IRlJ1bzFwVVMiLCJtYWMiOiIzNTllOWY5N2ZjZjQ2NTU5Njc2NWMxYjAwOTUzMGI3Nzk3YzczNzA3ZmIwNTNiODZiMjIyYzM3ODI5MDZhZTYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1729847579\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1813370814 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1813370814\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1363534693 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:10:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxGR0lzTUpEVGp2UVVSenltZUxzR0E9PSIsInZhbHVlIjoiVS9NK1dSbUxqQjYrTVJMbXM3bnRPV2lVNC9jZFI1amxBSEx6MmhNTXZuQSszVGFubGZmbWV6bVhpYjBJVU0xL3o4VDNLQ3hHMENhY3h5ekE5U1lydk45dFY4bVFRNEdRd0k3UW0raWRNUWR3OE9mdTc0bHNMUCtoZHFuRXZQSU93VlRMUWY0SG05TUZKenIxQXRFTkRkcldiL1pzWDZHM3Y2c21hUlE5NnJTVFRTOHNEM240TW1RWndtb05MTFZlazRKRUJncFBibHZ3amtxb0JQTXd0R1FRT0N3cklCV3VjeWVrS2VwUFNJVVlQQTBYczZvYzV5UFFQaDVWK0p3NjBUQlc2NW1oeXBNMUpmWnlxZjZiTVZKNEJjMVRWWWpPK0dwcDdkUTkzQ3ZNaTRlQzlnc3FvUHBWcEM2N1YvUURzZFA5Vmc5cTdBeXNvaUR0b1BhVVdtWVNTb0JLUWNtclV0ZkQ4WHBlT081cmpzYUNSZTlHVG5zSXUybHVJWlQ4NlUvdVhwcXRZbUdBalNMa0F2VWxKVmtQVnhxdGR4TzZWd2VQZ2RCcmVCWnRnbFhvN2p5YU1WVE1vcVRZeE1NYkVZaHN3WHJVUkhUSkhQSkJITzFjWGJFNkR5bExlbWJuQUdLQUkxQnZmbW1jOWhoOHl4TFNsblNGVEJyUm1YNFIiLCJtYWMiOiJhYmIyZmM5NzU5Nzk1ZTQzZmZjNzQyMTAzNjk1ZTE3M2M0NmZhMWE3YTFjOWYyZjUwOGYzZjdlMzllOTZkMDM5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImREZDBIa08yVElHNXI1bkhqMWIzMWc9PSIsInZhbHVlIjoia3gwNEg3bFlLdXlrY044ejdLbkFKWVc3YlRLOEo2QjhnZUZwdytZT1FYc1o1NVJsaG1lK1djZm53QWNVTk1SVitSVWs0T280SE9BeS9tcnJtMGpzckpLckxGWHhlcERZdjlaQjBCMVgwYjhtcFVkWVRPTGJmbksxTEZBSUVVTVZHbWhPK1JIdHhZYW8rRVRtUXNvVXFETEZXdGoyR0hEUTlNejVHUjMySkk5SWtYQkNIcTNKRVV0bktPdXJLd0FRUFp3U3hidVVSREFsSUZIWXFjVkp1cks2ZmRuQTN3djl6R2V0UTlMTXhDaUlGeWZNS2RLSElVMnlHNG16UmNRZWZIWUdIK1VQQWJ4WWRQZ0dsV3hKSGIvTndLdS9KYUNvSzhPeXNEVFp2dkV1UDV0Z29iVEFpK09CLzNFdEdEa3VHY2NvTWR3RjRKRnBLbDVTempxN2xqSTJrWHBGNXRCaXRUcHNPcW1qb1MvVDhRcytobFlGc0gzRVRMdUpRWHhYcDNkcnk5bjc2NDdyVERPK20rWEhRTHRCSUlra1BocmM5d2hmQnR3Q2F0Mzd2N1JhNmR6K1V1c0ZJU3BUQi9SWWNJTUFiQ0lleGxtbUtCK1R3ckxVREhCbHlMMVpiek9Ja2Q0UHV1L2hZSFkwaUdibVpXd2o1bk5zb2lWQmNURGMiLCJtYWMiOiIzMjkxOTZiMjJkNDFjOTlmYzExNTA0ZDY5YTY1MzYzOWQ5OTI4NzA2OGU1Y2JlNTFhN2UxZmNlMWIxZGY3MTI0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxGR0lzTUpEVGp2UVVSenltZUxzR0E9PSIsInZhbHVlIjoiVS9NK1dSbUxqQjYrTVJMbXM3bnRPV2lVNC9jZFI1amxBSEx6MmhNTXZuQSszVGFubGZmbWV6bVhpYjBJVU0xL3o4VDNLQ3hHMENhY3h5ekE5U1lydk45dFY4bVFRNEdRd0k3UW0raWRNUWR3OE9mdTc0bHNMUCtoZHFuRXZQSU93VlRMUWY0SG05TUZKenIxQXRFTkRkcldiL1pzWDZHM3Y2c21hUlE5NnJTVFRTOHNEM240TW1RWndtb05MTFZlazRKRUJncFBibHZ3amtxb0JQTXd0R1FRT0N3cklCV3VjeWVrS2VwUFNJVVlQQTBYczZvYzV5UFFQaDVWK0p3NjBUQlc2NW1oeXBNMUpmWnlxZjZiTVZKNEJjMVRWWWpPK0dwcDdkUTkzQ3ZNaTRlQzlnc3FvUHBWcEM2N1YvUURzZFA5Vmc5cTdBeXNvaUR0b1BhVVdtWVNTb0JLUWNtclV0ZkQ4WHBlT081cmpzYUNSZTlHVG5zSXUybHVJWlQ4NlUvdVhwcXRZbUdBalNMa0F2VWxKVmtQVnhxdGR4TzZWd2VQZ2RCcmVCWnRnbFhvN2p5YU1WVE1vcVRZeE1NYkVZaHN3WHJVUkhUSkhQSkJITzFjWGJFNkR5bExlbWJuQUdLQUkxQnZmbW1jOWhoOHl4TFNsblNGVEJyUm1YNFIiLCJtYWMiOiJhYmIyZmM5NzU5Nzk1ZTQzZmZjNzQyMTAzNjk1ZTE3M2M0NmZhMWE3YTFjOWYyZjUwOGYzZjdlMzllOTZkMDM5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImREZDBIa08yVElHNXI1bkhqMWIzMWc9PSIsInZhbHVlIjoia3gwNEg3bFlLdXlrY044ejdLbkFKWVc3YlRLOEo2QjhnZUZwdytZT1FYc1o1NVJsaG1lK1djZm53QWNVTk1SVitSVWs0T280SE9BeS9tcnJtMGpzckpLckxGWHhlcERZdjlaQjBCMVgwYjhtcFVkWVRPTGJmbksxTEZBSUVVTVZHbWhPK1JIdHhZYW8rRVRtUXNvVXFETEZXdGoyR0hEUTlNejVHUjMySkk5SWtYQkNIcTNKRVV0bktPdXJLd0FRUFp3U3hidVVSREFsSUZIWXFjVkp1cks2ZmRuQTN3djl6R2V0UTlMTXhDaUlGeWZNS2RLSElVMnlHNG16UmNRZWZIWUdIK1VQQWJ4WWRQZ0dsV3hKSGIvTndLdS9KYUNvSzhPeXNEVFp2dkV1UDV0Z29iVEFpK09CLzNFdEdEa3VHY2NvTWR3RjRKRnBLbDVTempxN2xqSTJrWHBGNXRCaXRUcHNPcW1qb1MvVDhRcytobFlGc0gzRVRMdUpRWHhYcDNkcnk5bjc2NDdyVERPK20rWEhRTHRCSUlra1BocmM5d2hmQnR3Q2F0Mzd2N1JhNmR6K1V1c0ZJU3BUQi9SWWNJTUFiQ0lleGxtbUtCK1R3ckxVREhCbHlMMVpiek9Ja2Q0UHV1L2hZSFkwaUdibVpXd2o1bk5zb2lWQmNURGMiLCJtYWMiOiIzMjkxOTZiMjJkNDFjOTlmYzExNTA0ZDY5YTY1MzYzOWQ5OTI4NzA2OGU1Y2JlNTFhN2UxZmNlMWIxZGY3MTI0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1363534693\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1218838856 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2352</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1605;&#1580;&#1587;&#1605; &#1588;&#1582;&#1589;&#1610;&#1575;&#1578; &#1605;&#1582;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2352</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2353</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1605;&#1601;&#1578;&#1575;&#1581; &#1593;&#1604;&#1576;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2353</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2354</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1588;&#1603;&#1604; &#1576;&#1591;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2354</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1218838856\", {\"maxDepth\":0})</script>\n"}}