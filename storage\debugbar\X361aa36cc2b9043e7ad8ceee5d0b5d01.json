{"__meta": {"id": "X361aa36cc2b9043e7ad8ceee5d0b5d01", "datetime": "2025-06-30 22:39:09", "utime": **********.903302, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.460824, "end": **********.903326, "duration": 0.4425020217895508, "duration_str": "443ms", "measures": [{"label": "Booting", "start": **********.460824, "relative_start": 0, "end": **********.839022, "relative_end": **********.839022, "duration": 0.37819790840148926, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.839031, "relative_start": 0.3782069683074951, "end": **********.903339, "relative_end": 1.2874603271484375e-05, "duration": 0.06430792808532715, "duration_str": "64.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45721624, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00435, "accumulated_duration_str": "4.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.868144, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 56.092}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8803232, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 56.092, "width_percent": 19.08}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.886621, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 75.172, "width_percent": 24.828}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1438765393 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1438765393\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2039612148 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2039612148\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1640453945 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1640453945\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; _clsk=eiqg7d%7C1751323143882%7C2%7C1%7Co.clarity.ms%2Fcollect; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlBQaFRCdlJQRW9yYkZycDBMSWY4QVE9PSIsInZhbHVlIjoiVWFIT2k2S0NDTmx1YVc3U0tvTUZQZmo4OC82S0dpOVlNSkdMQUJNMkd5a0dPTHYxbXBTcGgzZ3RJM3h2UkcvUE04eEFyTDZFYk13WEtTeTB3WmpaVDM2ZitPYjBCZ1pnZncweHhvcXdSTkZJY1V4dUVoTGpMUENqbmlCMHJqeVJqV1EvZ1gvbjhFVGNjeVRnRTU0bzY0MWl4V1VDMzd0N0NmZlZGbnhnK3IrV0JyMis3eTJiZDVzeXN5Rm9haE9VTGFCNEUvcmQzNlBISlcvYVRLcDJPWTkxUjlwUGxQQkVzNUR1elljTDFCQllvT2RTYlE2cjJUb3RjQThqOGlDeW52UVBxeW5sYXdDeXdQRDk3SkhHZDlTWUczSnBDWVI2cUw5YmloRytTRFpseE5RaGlpTDZldjNwa1dhbDZTaHE1eFJoaXpUU0lPOWtraTlmaEpXa09lQVNNODJjVG9oVDcwVEpST2o5K1JoY09lOHhyTWNIQmhWUGF1cVkrdGRUTlFISUlKN1BNRzIwN2l2RTVLclVYam5EeU8vaHNWYmE5T25YV2NKTkMwQkNRZE0zRktlWjd4ZWgwMUlXM2tkelFpS2MyK0JmTWxiQzFsWkozNlFLaU5qNXA2SW9xaWI2NlpXb1FITElsV0wvYWx0V1ErTlM1K1VkcG5KTmszRjAiLCJtYWMiOiIwZjJlNGFmNmFjNWUwNzM2MGMzZWY2ZTQ4YjE3ZGQzMTBiNzRiZWY4NmQ0ZmI5ZmJiZGQ4OTFhZmFhZGU4ODY3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii9HSCtyeFNsZ1hDZG05d1MvakRIVVE9PSIsInZhbHVlIjoiK1lENThhM2hVTSswTWFYSnQ4b1llSUlBSnhXZENaTDBqcHdFa1JCaWNRL0JiV3QxMXU0Q05ZYTJrVDcxdGlURmZqMXNoMWFSbWFqSUd0TjkraXFoN2xpRS9LQStHblpYN1QzaXhiN0xFM1lZMzN2QjZtRktxQURlNlUrTW9Uc3YxZkUvb0RuaG13SjBKQ0w4NGhFdVhQUTgwQXlYTDlMd2VGTlhnb0VuZzRzbERndVd2NUllUi9oRnZtVGx0L25pOTJ2MW5zSmZOVGd0QVhteFVnK01WRGJmbmZTZVB2Sm9RM216QURla0lhMzQvUmk0QUdRMHdnUy9SK3A3bWUvWVhGSlc2UjRJVUEwak9FSXhwOU5udnRieHlXL2pwWGw3c1V4MTRUV3NtNFJnOHh1VTBZbmgwMk9xZDRWMnA1NVZJTlhmUEk5KzNUUVJlWi80Nm54V3B0aDBEREwwVWt4bFdlNnNlQnhLS3hMVmhQRnNKaXlFTVVMMkZ0ZHRKYyt2eUUxTVhicVk1N3ZueTAzc2k4bzVNNnJLak5iNVMvTS9QRVp6dEl4ak92ZThKcE1lNkVlY3ZXc2h2R3dxTkxLVEVqTUlqaDRRL2p1bWdWYjZtcTBTb3k4TDNESHM1SVd4b2dRUUx0amJ4enN0enZ4OG1VTkRNb0dFd05GM2ZZalYiLCJtYWMiOiJjMzBlYmFkNDI4MjMwNDcyYTI1NTFmYTFkMTdlYmE4YmU2MDY5MTlmYzhiOWM3ZWM4MmQzMWIzY2JjMDljOTcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1146598699 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1146598699\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-433513592 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVyVk4wdkl3ajhmaGlsT2ZtL1pIUEE9PSIsInZhbHVlIjoidk9DcGlEZzZvRzNCTXRKZ0E2Z3Nldm43bHNBRG5jZUIzWFdidGkwclVpTTg5MWd4bllmemhTb2JHTWI0M3ZBM1N4SmZWcWhFdTM1RW80VlBvWExGUUR2VlQwY1dTQnZBMUhWYjlvV0xqeXdaNlZPWWpEa2pFemsxNEJGdjE2MDNDNXlGekVZRTdKT3N0azI5SUYxZGY4QXdxQ2VXc0tjOFFrUzIwOFZra2NtK05uTTV3WUdRcEtVVy96WnBaUmRMbEV3VjJ2a01vYzI3aVZYbFVuWDZVNTBPd1hXUkoxT2xrOVgzN0loQU5kb3JpcysycG5kS1JYUWc3azhBZnJoTmdnNmlDUklpdnM2VStIRDRVNzdEMGdLWWtaWTNqMDNhSmtlVEl1SVNVeTBQNzRvNS9OYTVOc2xpdWgvQVprYUNoQTl3dlUycytNR1BDV1BZdDdQZHpxVVJzYmRmdTBIVng0bFUzUXhkZWovNGtHSzRiSGw3SUczY3VEZ05aZldKLzAvb1luaVpVYllka2QxY3lGMms1MlNmTEtzM1I1bzZ5OGUzaEwzVWxXbUJUc0x0ZlN2RlF5V0dab0l1cWlVTFMxTVh2VXBzSHI1ayszazZ6U2NNRU05S2ZDYmIza0MyWFRKSjRnVTlzWCtmSGpTeVc0elRudkdSUmRveTV1QU0iLCJtYWMiOiI1ODEwMmE3MDhhMDRjMjVmZDg0NGNmMGQzMTU0YzlhYWEzNjQ5MDk1ZWZhNGM4OTg1Nzc5ODJmNGE2MWM1MDJkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImhNb1IxU0VLeGRCbkpERjRZSTI0TWc9PSIsInZhbHVlIjoid0pucEQ1UGJUUWZ3Q0tPOUlFV0RXSWdzamFwVGpqQmp4b282SEtMK3JQaUh4OUw2VUd6OTZBNDZ6SFZZTjF4em03N2FCTkdvdEVqcWh6OU9CNGJXRCtwZVhYVlNzS1NHNGFMUzFBUzVqRVdUVXAwbHRPcWJYOW0zNGhVemlOVTA1Q3Rsb3VycFczTFhWdWd3a1JJWE5vYWFvZERVL1ZZeHFXU0I5SUFwdWxYU05EdjZHUkRxY2tDWmdtQ0NMdDMrVS9RUTdQbUxkYTMxdG1OU2poQUIvaTJncXBMM043QkJrYkxsT1ZNVGRxWk9XVkVOZ3BYZlRiN0lVR2ZqYmtNTWZ0U2hXREJrK0srbVlxUFRXT2hTRDBJd2FiWWJlM3FhbFQxK3cvdmpDbUd1OFV5MTlMejBZMEJKOXdzeGFkc3RtVFFNWWtYeEIvb1NtZTRmSUN5YjR6NTNBdy9meUJhUlhna2Rqa25PakwyUjlyTm4zVDJOclo0a1JybnU2a01pcUNidDQ0Q3pMRmJWNlkrTWJDWmpIV0Nna1RvSkViSFJkRTErQWRQdzU0UEZrMWRjajBLc2tseEtEcGdxcWE0T1R0U0pRTjlIWWZQcEVrdzM2bjJBTXhKczltV1hpdFFEOFpaWTZ4VEU2U2hlbkszYlZlSGx4NUVSQURkUWxmY3EiLCJtYWMiOiJhMTU0N2FlMmY1NjdhODljZDA5N2NjOGE2MzRjOTQ5YTA5MTVhNWU0MjA5ZTU5NjgzN2ZiMmE1MTk1YjZkMjg3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVyVk4wdkl3ajhmaGlsT2ZtL1pIUEE9PSIsInZhbHVlIjoidk9DcGlEZzZvRzNCTXRKZ0E2Z3Nldm43bHNBRG5jZUIzWFdidGkwclVpTTg5MWd4bllmemhTb2JHTWI0M3ZBM1N4SmZWcWhFdTM1RW80VlBvWExGUUR2VlQwY1dTQnZBMUhWYjlvV0xqeXdaNlZPWWpEa2pFemsxNEJGdjE2MDNDNXlGekVZRTdKT3N0azI5SUYxZGY4QXdxQ2VXc0tjOFFrUzIwOFZra2NtK05uTTV3WUdRcEtVVy96WnBaUmRMbEV3VjJ2a01vYzI3aVZYbFVuWDZVNTBPd1hXUkoxT2xrOVgzN0loQU5kb3JpcysycG5kS1JYUWc3azhBZnJoTmdnNmlDUklpdnM2VStIRDRVNzdEMGdLWWtaWTNqMDNhSmtlVEl1SVNVeTBQNzRvNS9OYTVOc2xpdWgvQVprYUNoQTl3dlUycytNR1BDV1BZdDdQZHpxVVJzYmRmdTBIVng0bFUzUXhkZWovNGtHSzRiSGw3SUczY3VEZ05aZldKLzAvb1luaVpVYllka2QxY3lGMms1MlNmTEtzM1I1bzZ5OGUzaEwzVWxXbUJUc0x0ZlN2RlF5V0dab0l1cWlVTFMxTVh2VXBzSHI1ayszazZ6U2NNRU05S2ZDYmIza0MyWFRKSjRnVTlzWCtmSGpTeVc0elRudkdSUmRveTV1QU0iLCJtYWMiOiI1ODEwMmE3MDhhMDRjMjVmZDg0NGNmMGQzMTU0YzlhYWEzNjQ5MDk1ZWZhNGM4OTg1Nzc5ODJmNGE2MWM1MDJkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImhNb1IxU0VLeGRCbkpERjRZSTI0TWc9PSIsInZhbHVlIjoid0pucEQ1UGJUUWZ3Q0tPOUlFV0RXSWdzamFwVGpqQmp4b282SEtMK3JQaUh4OUw2VUd6OTZBNDZ6SFZZTjF4em03N2FCTkdvdEVqcWh6OU9CNGJXRCtwZVhYVlNzS1NHNGFMUzFBUzVqRVdUVXAwbHRPcWJYOW0zNGhVemlOVTA1Q3Rsb3VycFczTFhWdWd3a1JJWE5vYWFvZERVL1ZZeHFXU0I5SUFwdWxYU05EdjZHUkRxY2tDWmdtQ0NMdDMrVS9RUTdQbUxkYTMxdG1OU2poQUIvaTJncXBMM043QkJrYkxsT1ZNVGRxWk9XVkVOZ3BYZlRiN0lVR2ZqYmtNTWZ0U2hXREJrK0srbVlxUFRXT2hTRDBJd2FiWWJlM3FhbFQxK3cvdmpDbUd1OFV5MTlMejBZMEJKOXdzeGFkc3RtVFFNWWtYeEIvb1NtZTRmSUN5YjR6NTNBdy9meUJhUlhna2Rqa25PakwyUjlyTm4zVDJOclo0a1JybnU2a01pcUNidDQ0Q3pMRmJWNlkrTWJDWmpIV0Nna1RvSkViSFJkRTErQWRQdzU0UEZrMWRjajBLc2tseEtEcGdxcWE0T1R0U0pRTjlIWWZQcEVrdzM2bjJBTXhKczltV1hpdFFEOFpaWTZ4VEU2U2hlbkszYlZlSGx4NUVSQURkUWxmY3EiLCJtYWMiOiJhMTU0N2FlMmY1NjdhODljZDA5N2NjOGE2MzRjOTQ5YTA5MTVhNWU0MjA5ZTU5NjgzN2ZiMmE1MTk1YjZkMjg3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-433513592\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1628178394 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1628178394\", {\"maxDepth\":0})</script>\n"}}