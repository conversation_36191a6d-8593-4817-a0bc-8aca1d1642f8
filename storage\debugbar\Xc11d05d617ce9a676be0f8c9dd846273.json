{"__meta": {"id": "Xc11d05d617ce9a676be0f8c9dd846273", "datetime": "2025-06-30 22:39:35", "utime": **********.468193, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.05036, "end": **********.468209, "duration": 0.***************, "duration_str": "418ms", "measures": [{"label": "Booting", "start": **********.05036, "relative_start": 0, "end": **********.4145, "relative_end": **********.4145, "duration": 0.*****************, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.414511, "relative_start": 0.****************, "end": **********.46821, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "53.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00336, "accumulated_duration_str": "3.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.443142, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.905}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.453469, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.905, "width_percent": 16.964}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.460672, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 78.869, "width_percent": 21.131}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751323174926%7C11%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRUWHhJREhCTGZnVitQTnNZVkFjS2c9PSIsInZhbHVlIjoid25yVHl2UzJjYlJreHdlNytIRWVBZ3RJNmVacmUxZURvS3IveFZWK0JML0VnaDZaVmFqRXdOZXUwQTRKUU55em9ONlJ5ayt4N1g1VGs3U0xpZG1SZlBhMi85U1JTMExyZCtXeHJjeDJDWkludklGQ3VrbTg3WWZPWitTR0dTUTFaVmpXWHBsV25LODBkK3JKUk5mOUJ5YUxiS3kyN25XdzlqaWt2ZDN4eEpIKzlUYmMveUF3b3pjZ3NLSnJ0U1IxeDNWanJkY280b2VIMCtxNXp6UFptMm92anYrbWo5U29JRUhGejlmKzJUanYzZXpyOFZ6cGhUcWg5N3ZhSmdwUHJ6Q2JxVkxJZkJsbjB6QnVrWDJod2JhS1cxVGZYcDhrZVdLYzdBVmtUVW13NTlGSzFxbzV3MGdzS24wa1lscVYraUVxWGFyTnVxcjc0Z0ErWERORVo5KzhrMmJFcmRHRDF1WGxsZUs4TTNMdktVRWx1NlpxMEhLekp5a1E5NWo2UWtrWlMrQUN4bnN2YlhoOXhNb3BUNmhNNzdUWFJHcjdIU0k2di9BSDllRHFNdFR2Q250aTlWdWxPMTBXd2F3UURwRUgveTFlVC9rQzhtQjEwY0txTGM2NTdzSGNxV3hlbFN4ZnhYM2psT3JEN0JXWDVjTkdlVmovWXkybE45YXMiLCJtYWMiOiI2ODgyYTUyZGY3ZWM5YTI0ZTA2Mzk5ZTg5M2YwZWVlZWY0ZTNkYTY2OGQ0YTY4ZmEzMDg5OTU3NTY4OGEzNWY4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlRkL0NvK0x2YnRvNGdleit6ejlqRnc9PSIsInZhbHVlIjoiYWZzUzlwTUJ2U3l3TktvcUx5VWZrUmM4K1R3U0dlQUJYMktRbXUxQXIwK2dxL3JBY1hkaUJ0Z3JzWXhYM0hDSkw1QTZWVUllWldvQnFvNU5CM0tCdWRhOU5OcmdzaHhCWWo2Y1NINHJRUURrTWVONndEa0FLeHpqMmpIOVJWekJFeXhGa2xvVnpCeUZHV2hkOUd4Nkp4TENVL3V6VGtYU0FwalZXaFFUMDBkTldxY0ZzSUwxYkVKZy9RUmxVeHovbXEwanlZUFovdFhtYkZ3MkE1S0lDcmJiMnR2UUlrd0g5VGZOVGprSUdqd3FTQkNEOCswU3gwQUdNdnVsRzQ3ZlI3SS92R09EZG5ZYkJJVDgxdjR0akFDdS9OTVpMNTA4anFRdGRLZVJnUysxQUZLS2hSak1lQjhSZndqWXE5QmdBMUFiLzN5UUZRa3RSaGgwTTZ4eGI4MlNIZG1rVkIvbTRoWHhzM2RsYU5wMkt1MFgrTzFPZisvNHd1SG1mSEtHdFY0aitobjE1QjVpckhObE5GM1pRd044Q1FXWENWSW05MG1vc3Y4Y3dsZU0wN3diQy9DS2ZWL1V6dGZyTnRsR09ZZFRCQnM0R3FxSlhiMEtDTVliZ3lNVnJRdy9VTGp0ZFllN0RhQkZUNk9SdC9BK3pndENMc29KSWpkdktYUDkiLCJtYWMiOiIyNzE1Y2M3MzhmZWRkYzRmYjhiZTVlMmIyZGRlMjZmM2UzODgxOWU5MmNmZTBhY2EyMDliODVjNzIzZmJmNWM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1050219636 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">K7EIYCLnalanTBUASCKMEOK1yUmooVr7ha2cCSMP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1050219636\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-342163706 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpyN0pVMGxkVHA5VnQySWdRUi84L1E9PSIsInZhbHVlIjoiSXRhczJrQ2lZQVNBUS9tcXhyUWxRNW12Slp5R2s5SWg5cWhPdnQvRjNvcjZUMmgycXZRVnIyVlFUOFhxYThIcnRtbFlyeTM4SmpBeGc0dnk1Q08xMTBPMXN4MjJ5OFNGazV1T1ZoNmdLemwvOGtDOVhEdXVzUEpyaDZJd3JCdjdjWU1UWTBERS81b0FBLzdISC9TTzBYVkQyT1RuZzkybE41VmJ5QUs3bHdRZ2NzSTNwb2pseTBpZ2xsT2E0cCtvVlZOZVRyTnB2STJWVEtiNXB2SDNCaGVUaG9FN2trUVdHUEk5VE5NVGUrV2RWK1NVS0c5MElXYXFJZkpENzErZzN6RFlwanpUR2E5THdQTUxnSy9sNmduN1RYa043REozdjZTcDdoV0xHblJ2a2xZWXFueHRFaytlbjAzSDRMREF3cU9sZ2FZNHVFZHR1QjNUTXlYYTJQQWJVWk5TLzRnamJEYXpaSE5WZW1WdXc5VkdiUk5nL3p2cFFrMnZwWm1RYnF6bm5CeW5tUXI4L0J2YjNHd1Nob1Q5OGFlb281QldNbUNldnNNUzVJdXRSRUZROVJRYXhvWmJxZTJzMG9BeUFJblNGYUlLUUptZFY1dEZFRjVXUVJpRjZVWEpUQXI1OTczSGZnSFZob1V6R1hkc0cyQkZKV1pHNStjSC9HTXAiLCJtYWMiOiJlMmIzNWQ2OWZmNzE5NjJjYTNlZjc2ZDBlZjM3MGY5ODQzMDYwZGFjNmUxODRjM2Q4NDdkZmYwMzk0YzE0MTYwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJKbEhiU2tVd3BDbHhzbHgxL0hMSUE9PSIsInZhbHVlIjoiUjRlY1B6Y1h5dzF0WDRqRVo0RnhzRVpkcjl5RitzTlp0em41dWFiWjI1bWZBK1pBbmg2Mi92WXhYSDcvYkV5VXYxQXZ1dkxkdzBxdExRdWZrWUhieHpIb1QvRGZPTVJxM1p4ZWJ5aE9JVWZuWHNEZERjOHViUnN2cnk2ZXJ4Ni93SjNEZ21CY0tNY0sra0NvZ0FtdVlBMi9acmdGSlJZQklKdzViSWlTaENDVms1eENBdmlDMWY0L1RaRUdBWU1qR1lXL3FLOUR3WFpLb05NUHZDRCtaTnZlRnQ3Z3hjTjIwUzNhSDNyNkZLeG9HNkJabEdqUFliZUUxQ3dTR1VUaXBIV0owRmVjbVhUcTFkUnBXNUpLY3lLeG1TdjVCbkdOMDVGMXNZMkpKeFJBckdmeUhqKzEwN1g0VzdCanZNZDJybEJJT2U5Z3Z5bWI0aDNHS3NwaXBsTTYwc05ORzhSbXg2M3paWmdzc2VoQzgrenVobDI5TzA2L1I5TXlTbDlzS2d5akxNZTN3T2ZWd3R5OEJGZW52V3E0YW1raTR2QXZBRjRFMTB2dWNIRndhNVV1cHhHcDBWQzhteERONEZ5bjhJdVVtdXFwQ2dFazAxTHZpYkZnWk9Wc0V2UzdxY1BIbkNPaU1yWlVqUkFQSkEvVzNWMEhnWEdkRkw5SnRsMVciLCJtYWMiOiI3N2U0NDg3NDRiZGQ1OTdlMDY0YTU5NDgyMTc5ZGVhYjA2M2I3ZWU1MGRhMWYyMGNkNzg2YjVhZDQzODg2N2E3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpyN0pVMGxkVHA5VnQySWdRUi84L1E9PSIsInZhbHVlIjoiSXRhczJrQ2lZQVNBUS9tcXhyUWxRNW12Slp5R2s5SWg5cWhPdnQvRjNvcjZUMmgycXZRVnIyVlFUOFhxYThIcnRtbFlyeTM4SmpBeGc0dnk1Q08xMTBPMXN4MjJ5OFNGazV1T1ZoNmdLemwvOGtDOVhEdXVzUEpyaDZJd3JCdjdjWU1UWTBERS81b0FBLzdISC9TTzBYVkQyT1RuZzkybE41VmJ5QUs3bHdRZ2NzSTNwb2pseTBpZ2xsT2E0cCtvVlZOZVRyTnB2STJWVEtiNXB2SDNCaGVUaG9FN2trUVdHUEk5VE5NVGUrV2RWK1NVS0c5MElXYXFJZkpENzErZzN6RFlwanpUR2E5THdQTUxnSy9sNmduN1RYa043REozdjZTcDdoV0xHblJ2a2xZWXFueHRFaytlbjAzSDRMREF3cU9sZ2FZNHVFZHR1QjNUTXlYYTJQQWJVWk5TLzRnamJEYXpaSE5WZW1WdXc5VkdiUk5nL3p2cFFrMnZwWm1RYnF6bm5CeW5tUXI4L0J2YjNHd1Nob1Q5OGFlb281QldNbUNldnNNUzVJdXRSRUZROVJRYXhvWmJxZTJzMG9BeUFJblNGYUlLUUptZFY1dEZFRjVXUVJpRjZVWEpUQXI1OTczSGZnSFZob1V6R1hkc0cyQkZKV1pHNStjSC9HTXAiLCJtYWMiOiJlMmIzNWQ2OWZmNzE5NjJjYTNlZjc2ZDBlZjM3MGY5ODQzMDYwZGFjNmUxODRjM2Q4NDdkZmYwMzk0YzE0MTYwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJKbEhiU2tVd3BDbHhzbHgxL0hMSUE9PSIsInZhbHVlIjoiUjRlY1B6Y1h5dzF0WDRqRVo0RnhzRVpkcjl5RitzTlp0em41dWFiWjI1bWZBK1pBbmg2Mi92WXhYSDcvYkV5VXYxQXZ1dkxkdzBxdExRdWZrWUhieHpIb1QvRGZPTVJxM1p4ZWJ5aE9JVWZuWHNEZERjOHViUnN2cnk2ZXJ4Ni93SjNEZ21CY0tNY0sra0NvZ0FtdVlBMi9acmdGSlJZQklKdzViSWlTaENDVms1eENBdmlDMWY0L1RaRUdBWU1qR1lXL3FLOUR3WFpLb05NUHZDRCtaTnZlRnQ3Z3hjTjIwUzNhSDNyNkZLeG9HNkJabEdqUFliZUUxQ3dTR1VUaXBIV0owRmVjbVhUcTFkUnBXNUpLY3lLeG1TdjVCbkdOMDVGMXNZMkpKeFJBckdmeUhqKzEwN1g0VzdCanZNZDJybEJJT2U5Z3Z5bWI0aDNHS3NwaXBsTTYwc05ORzhSbXg2M3paWmdzc2VoQzgrenVobDI5TzA2L1I5TXlTbDlzS2d5akxNZTN3T2ZWd3R5OEJGZW52V3E0YW1raTR2QXZBRjRFMTB2dWNIRndhNVV1cHhHcDBWQzhteERONEZ5bjhJdVVtdXFwQ2dFazAxTHZpYkZnWk9Wc0V2UzdxY1BIbkNPaU1yWlVqUkFQSkEvVzNWMEhnWEdkRkw5SnRsMVciLCJtYWMiOiI3N2U0NDg3NDRiZGQ1OTdlMDY0YTU5NDgyMTc5ZGVhYjA2M2I3ZWU1MGRhMWYyMGNkNzg2YjVhZDQzODg2N2E3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-342163706\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-83258935 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-83258935\", {\"maxDepth\":0})</script>\n"}}