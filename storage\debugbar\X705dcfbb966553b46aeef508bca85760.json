{"__meta": {"id": "X705dcfbb966553b46aeef508bca85760", "datetime": "2025-06-30 23:12:34", "utime": **********.79554, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.3625, "end": **********.795557, "duration": 0.4330570697784424, "duration_str": "433ms", "measures": [{"label": "Booting", "start": **********.3625, "relative_start": 0, "end": **********.727802, "relative_end": **********.727802, "duration": 0.36530208587646484, "duration_str": "365ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.727809, "relative_start": 0.3653090000152588, "end": **********.795559, "relative_end": 1.9073486328125e-06, "duration": 0.0677499771118164, "duration_str": "67.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45043352, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.017079999999999998, "accumulated_duration_str": "17.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7554, "duration": 0.0157, "duration_str": "15.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 91.92}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.779778, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 91.92, "width_percent": 3.22}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.785629, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.141, "width_percent": 4.859}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-102263282 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-102263282\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-231158032 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-231158032\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1069463290 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1069463290\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-827586304 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325150388%7C22%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkE3V3M5dHlEZURzWjgvK1A1elArcmc9PSIsInZhbHVlIjoiYndRaCtsV2xpNlMvbm5YUWFaVmVYajFubE1jbzFQRXJ3VHJjS0J2aFFKcXVxNmhGa0Fac0pJTVR5RTdKNTJmRUord3JMZ1NFUE8xSkdjcXViR3doTDVIK2lQT0g2Y1NnOSs4QVU3RGxsdzdiR1hVWkxJamV2bDB4SVI4QXpWL3RUTzNGZGxHTkVSUXJUV09QbW95NHVOcVp6QWQ1SVpzUmxDRFFLVnQ3ZXR4b0VHSFlYMytJSFpFS2VDdktFU2h2aFBNamdZTmRVbm9jN3dTQ3YvdklvVVZhc0NWU3FaUUgwbFEwMmg2WldOcmhYc1JJTjV3clpJUVNVOVd6N2ZvREo0T2gyQ3VJRStqRkN0bThRN0xNUEpzdldBWGw3eTJRcFVac2VjV20rMDkvUlJBMW0zYmt0ekw1dmNZOFFQQzRCNTc4a2lTSHRLWk9BendUMUEwdlBkQnFLM1VqZTBLTjU3dlJsQ2V6dkh2b1RFdkpheVhLTlU3MythcHhPd3JCbk9kSHY4bllzc0puZFJMd0VLNlV4OEFxVzRqZ2JqczJtZFJjeWdhQ3dQREx1eG1IQW9sdHdXZXp5SytNK2dXcjA1amEraFY0MEo3Vk42ZmFiOHZYUGRCRkI5anN6ZXNMT1R3aWFZU05meEtySVh4Z2hjNW5wR3laem0xbzVaV0UiLCJtYWMiOiJlZDI0MDQ5YWY4NjBjN2VjNTY0MDU5NTQ1ZGJjNzI0N2FjODc4NTIzZmQzOTJjNTgyNWU1N2I4ZjA5ZDRmMWNmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjJ4bDFKR2l4d0RCUENieVdBTmRnMHc9PSIsInZhbHVlIjoidUpaVW9pVzJCZDNXZEZhY0pNU0RzK0tRY3pjanNoS2Z6ZmsvcUZIWS9QYXM1NlI2ZEQwVGNuaHJWc2ZrMS9HaURGUGtBZnNvOElEZ1ppZEpKWlNrdXRCc3R1MlNNOEFTUkFZVVRpRmMvODJ6Vk1kYTc0RUtoWEQ3Ni9zckRjc0JodytzNTMwUldlaHA5OCtBeWRIK2hVN2NISm5vRkM4TE1BTUw2bkZJUjlHd2ZzL21PRWNITCthSjR1eFVZR0hVVldJVzMxV21sd21kOExENkduTzRYMVJmTXJIUDlNTjY0Nk5NQ2ltNTgxeERyOGZBR1ltZC9yQS9xNjEwNnorT084NEZZdW1IanNqTG9UV21QV2ZEVXhPc3dpcjFuUWhucXBDMFRHcnpTeU9zWUEzY3dGeUNQaG95MFQ5VFlmcm9GM3NBS2V4SjJIWUQxeVdURTlPVmtqeDZ4bHc0TExpaTEwWSsxNURmYUUzbUdKMFYwUUliSEVnSEpld1pBNGhTK3lFZUFZbXBlVmFkQUtsdHg2ZzlYWWNBUXFCUjBxNC9LTzhJWjd6M1AwUmErcDNmaG9sdzV5ZHJ2QlAraTZ6Uk9YN1ZONFZUZ1kxZVFJVzZYaldUSWFVUHIybnJQKzUrMnZNN2xPMHlrSDZnRGI0L2t4aUhYOGNjN1dsSWtqN0MiLCJtYWMiOiI0NDVjODM0YjgyOWM5ODMyZTg3N2IzZWFkMmIxYjZiYzAxNjA4MmJiMjk5MDFhZjVlZDUxOTQ2ZjhjOTkwYmMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-827586304\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-134642710 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0M1K0sdNadPD2LVrnt8LmBw227nA9F1U5e3ROaJK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-134642710\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-631469201 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:12:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFvVXFXc1owbzBrVlVsYUoyNERsMEE9PSIsInZhbHVlIjoiVURrZEJlT2xLaFN4dlNyZ3UyQ2pPMmd5K3hjS0xmanZSSFFUZ0VjUHVramFIcytkVlZIeEJjU1hoS1FkVndEMW44aktkNWdOajJUaGNtTFdleHNzU0JiRmRiL1hqaSs0eDE5YjR0M1hXYkRkcFlwM2Y0aUJOR2pOOWNKRGV4U21kUXB1SWZRaUZpQmhGK2hZTjRFdVVRWWNaNTQxQUhmQlF2SFAwVHluU0tCNDUwdWE2aldXeEFtdzJyNmVjNEtMWWdvWWkvZFY4Nld2cE52dGM2Z1VPd0svNDhEUzg3SkJ2N3g2NnY4dU9waFg5elowUVVuMFd5N05qV05rMFJ3eEVBZmY3SEJONzdPN3hGWEhoczlEdFd3c2syaUxFbHJGanNseHhHUVo3cm82dTdRdUVaRjBBVC9KMnhBZzBzNjlyOWtYTWpmQVZnVXlnUXphZXA5aElNYmNBRmFGRFltaU15VzdaWkxTUVJqQVlhbGtqODQvSERpSXdUVHZiTHVTU1lCNHh6L0NlRk1lSVNoZUZuL0l0bjVVeWlwQXEzMFZmVjVTRk9zS3ltbWZXRW1hUmhyQ1NWU3Y0RUxTVnFYTDdBUjhtb2FVS05kOWh4aEJTb3dERHhxdURIWk1lZ3JqcUNwQUlEOEMwS0p1WndOVWltdC9qQ0FPa21BVjJabHgiLCJtYWMiOiJkMDQyYzhmYWUyY2Y5OGIwMjI3NTU4OTM0MGYzZDVkODM2ZDY1Njg5N2E4ZjFjZDY4YzNhOTk1ZTgxZDhjZGJhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:12:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjRkWWVJdDlnTFJaejBiMG5pMUNLSXc9PSIsInZhbHVlIjoiVnFlSGkzQ2JVelN3ajBVTUN6Sy95NWcxK0kzRUc5RDNDN2FiTEpOenc1UDYzSk10a3lROWJJbEJMa2twdDN6bTdDZmFzMGNBZ1pJNjdkZ1RTQjNRTWtIeEhKVnN6NGFscmZuTGZIK2FQUk9qS1EzcnVQUm1uUXUydE9SVnJJa2R5d29qb1VzYUJxVkJzZ28rbWg1eldTNnZQRlVCd3NGVGVXaDY1NUI0b3YxditlcFEvZzF5N0FxUHhaMnU0TnltcFBROVhUYWxBa0REc3NqeXhPUWJxb25mZmdGUmdVYzlMcW92ekxvRUlXaWl5Q1NFTkNMVi9LZlM0UzZ3N1F3cU1WRmhQU3VSTjdWdytpS01hZ1dLeERmaGIxQUJuaUR6MFk1Y2ZtNlZCNEFUTloxTjU5T2h4Qmp0bEsyTWlQYTJZelZjR2JTbU0wbGhGVVV6N0J3R21UbFVuNEkxODVJNGFMWHFaQURNR28yZlVCQ1dwQTVIdGZnbXl4UnZJUzY2eVFvSy9nUGVvY1pZelpDcTVmYmgxV3V5b2N0eWM3ZitmODgyWldXWm9GZlBQWStMYlo4S0o5NnQ5TkVsTlR0ZkpYTXFuQ1ArZ1FkcGVwOXZvVmJBV24wYkUzL05WeHMyaGV2WmxZOFJnbmRzOFdlQWl5QzB4amVlWks4Nmd0eWQiLCJtYWMiOiI2YjIwNTMzYjkwYmJmZTViOGNjMTQ3M2VkMGJkNzkyNjBhYTI0ZTZmZWNmMDhhMGQwYzQwMzU3ZTg0Y2Y5ZGVjIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:12:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFvVXFXc1owbzBrVlVsYUoyNERsMEE9PSIsInZhbHVlIjoiVURrZEJlT2xLaFN4dlNyZ3UyQ2pPMmd5K3hjS0xmanZSSFFUZ0VjUHVramFIcytkVlZIeEJjU1hoS1FkVndEMW44aktkNWdOajJUaGNtTFdleHNzU0JiRmRiL1hqaSs0eDE5YjR0M1hXYkRkcFlwM2Y0aUJOR2pOOWNKRGV4U21kUXB1SWZRaUZpQmhGK2hZTjRFdVVRWWNaNTQxQUhmQlF2SFAwVHluU0tCNDUwdWE2aldXeEFtdzJyNmVjNEtMWWdvWWkvZFY4Nld2cE52dGM2Z1VPd0svNDhEUzg3SkJ2N3g2NnY4dU9waFg5elowUVVuMFd5N05qV05rMFJ3eEVBZmY3SEJONzdPN3hGWEhoczlEdFd3c2syaUxFbHJGanNseHhHUVo3cm82dTdRdUVaRjBBVC9KMnhBZzBzNjlyOWtYTWpmQVZnVXlnUXphZXA5aElNYmNBRmFGRFltaU15VzdaWkxTUVJqQVlhbGtqODQvSERpSXdUVHZiTHVTU1lCNHh6L0NlRk1lSVNoZUZuL0l0bjVVeWlwQXEzMFZmVjVTRk9zS3ltbWZXRW1hUmhyQ1NWU3Y0RUxTVnFYTDdBUjhtb2FVS05kOWh4aEJTb3dERHhxdURIWk1lZ3JqcUNwQUlEOEMwS0p1WndOVWltdC9qQ0FPa21BVjJabHgiLCJtYWMiOiJkMDQyYzhmYWUyY2Y5OGIwMjI3NTU4OTM0MGYzZDVkODM2ZDY1Njg5N2E4ZjFjZDY4YzNhOTk1ZTgxZDhjZGJhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:12:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjRkWWVJdDlnTFJaejBiMG5pMUNLSXc9PSIsInZhbHVlIjoiVnFlSGkzQ2JVelN3ajBVTUN6Sy95NWcxK0kzRUc5RDNDN2FiTEpOenc1UDYzSk10a3lROWJJbEJMa2twdDN6bTdDZmFzMGNBZ1pJNjdkZ1RTQjNRTWtIeEhKVnN6NGFscmZuTGZIK2FQUk9qS1EzcnVQUm1uUXUydE9SVnJJa2R5d29qb1VzYUJxVkJzZ28rbWg1eldTNnZQRlVCd3NGVGVXaDY1NUI0b3YxditlcFEvZzF5N0FxUHhaMnU0TnltcFBROVhUYWxBa0REc3NqeXhPUWJxb25mZmdGUmdVYzlMcW92ekxvRUlXaWl5Q1NFTkNMVi9LZlM0UzZ3N1F3cU1WRmhQU3VSTjdWdytpS01hZ1dLeERmaGIxQUJuaUR6MFk1Y2ZtNlZCNEFUTloxTjU5T2h4Qmp0bEsyTWlQYTJZelZjR2JTbU0wbGhGVVV6N0J3R21UbFVuNEkxODVJNGFMWHFaQURNR28yZlVCQ1dwQTVIdGZnbXl4UnZJUzY2eVFvSy9nUGVvY1pZelpDcTVmYmgxV3V5b2N0eWM3ZitmODgyWldXWm9GZlBQWStMYlo4S0o5NnQ5TkVsTlR0ZkpYTXFuQ1ArZ1FkcGVwOXZvVmJBV24wYkUzL05WeHMyaGV2WmxZOFJnbmRzOFdlQWl5QzB4amVlWks4Nmd0eWQiLCJtYWMiOiI2YjIwNTMzYjkwYmJmZTViOGNjMTQ3M2VkMGJkNzkyNjBhYTI0ZTZmZWNmMDhhMGQwYzQwMzU3ZTg0Y2Y5ZGVjIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:12:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-631469201\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1775262066 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1775262066\", {\"maxDepth\":0})</script>\n"}}