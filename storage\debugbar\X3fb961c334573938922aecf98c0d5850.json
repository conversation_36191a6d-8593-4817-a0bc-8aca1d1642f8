{"__meta": {"id": "X3fb961c334573938922aecf98c0d5850", "datetime": "2025-06-30 22:36:28", "utime": **********.238152, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751322987.811022, "end": **********.23817, "duration": 0.42714786529541016, "duration_str": "427ms", "measures": [{"label": "Booting", "start": 1751322987.811022, "relative_start": 0, "end": **********.171056, "relative_end": **********.171056, "duration": 0.3600339889526367, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.171066, "relative_start": 0.360044002532959, "end": **********.238172, "relative_end": 2.1457672119140625e-06, "duration": 0.06710600852966309, "duration_str": "67.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45141328, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0031599999999999996, "accumulated_duration_str": "3.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.21528, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.873}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.227657, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.873, "width_percent": 17.089}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.230855, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 81.962, "width_percent": 18.038}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:4 [\n  2352 => array:9 [\n    \"name\" => \"مجسم شخصيات مختلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"2352\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2353 => array:8 [\n    \"name\" => \"مفتاح علبه\"\n    \"quantity\" => 1\n    \"price\" => \"6.00\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"id\" => \"2353\"\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n  ]\n  2354 => array:8 [\n    \"name\" => \"العاب شكل بطه\"\n    \"quantity\" => 1\n    \"price\" => \"6.50\"\n    \"tax\" => 0\n    \"subtotal\" => 6.5\n    \"id\" => \"2354\"\n    \"originalquantity\" => 9\n    \"product_tax\" => \"-\"\n  ]\n  2355 => array:8 [\n    \"name\" => \"العاب اطفال\"\n    \"quantity\" => 1\n    \"price\" => \"6.50\"\n    \"tax\" => 0\n    \"subtotal\" => 6.5\n    \"id\" => \"2355\"\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1585941783 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1585941783\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-593509527 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=6nrx0y%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1aj6s00%7C1751322883410%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkYwNTdpdjhvYXlBVm9kMm9aRTNjQlE9PSIsInZhbHVlIjoiSTF3SWlodGV0emIrRkxpRk5JblBnS09rc1VUN2VqWlBCd2VRdUU3Y2daeHpDVGtaSjhTZElZZ3hTUFFuMHJaTEZwNVg4dXVTZEtWOFZ4VUtrZmxPWTNPaFBramFMeEhDYXQ0aUk5WHNZOWFhTm03UlFFdkQrTm41T1lxN2U0THg2Wjhsa3BCWDhKeHFUd0pmMVZFM1dlemZSOTkrY2t4MFVPeDFYL2kzOVZZa3pXRVlMTEdObXJ3WllqSGZhM09saWVURlJrcjBtWmo4cVJvbzNYbWpNTWVlNXcwVG5UNjlHSGRaMERqaUozMnJ5QUwwTXVSTFkvSVlEVXQ3T08yYTlHWWE3SEV2OThOSlA1eWoxZWZ3SXZUdXdPOFFRZkc5bkwzVG80b09aSlVFU1diR2lLbE0vT2UyK21na1VVaWVFbXVOZlgvU0lBS0ZKOXFKRFVpVVpDckxYb2cweGZSRnVINWN6dEU0YnBqVWhuQkFRN3JQTGZ3Mm43d1BjRmpqbGdENnhLMklHTlUvY0ZwMDlRVWdScjh4TjY4QTRoZnM1eEtsaW5MVTNqTi8vam5ZNUtXTFFuby9aMTB3dE9pZ1ZIQUp3ZXBiU0lFbWhXUjNtUXNiZXpMMlg3YmhMTDRBUU8vUHJ0ekMyakZGblF2bDRTMWlPUk9WRmd6ek8xeUEiLCJtYWMiOiJlYmMxMzMxZDdlMGI0NmI3NjQ0ODljZDQyODdkMzRiNTgzMjljOTg0ZDg4Y2ZiNjRhMzBmN2Y3MTc5ZGQ4NmUwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkJJOEVGYlRiQzBwK3ZwM2pUM05YWUE9PSIsInZhbHVlIjoiai82NThPNzZjUE1PbzNqaWVWRUFtTzdGOXVxNWpTN1IwV2VleVNkTEJ1QTN5SHZKQ1ZlNi9xT0R0SlRDRzArSmdhREdKZlVZdFVXTldWdXJFRkhtZ1ZnQVJCRzMrUFp2MFhRZHFoMUpYVWpYeno2N2NaL29uM01aUFpKVlZlVzlYQU5pWUVIZm4wTTF1VE5sU25Jb01OOXJ3NWVWSE1xVUo5Z0pLb00yWUR0dzAyaTFGQWFJYUVvUURjUmJhVDBqOHFKU2UzaFBKTGNSbmxmaDQveDkvODlHeStPNThteHN6ejFqN0t6d092TUVJbTlKZFUzVTFXY3ltQURQQ1daVWZ1VDJrNWtJY1BFTmVoVTBiL0NaWEE4UUkvV2xPZTF4ZjB2RkpSK01FdDlWWUtVNm5tWjFjM3BYTVd1aHdZZ2JDRXJEQ1RsRTdZS2hEeEV3TCtpNmpZQ25HUlR0bGxMSDhOYXQ0STZxem1MNElJZGxQTUQ3eTdLZDBkRUVFdVJMc3k3S0w5VEVOdkFxTVRVVzhqanFFV3pscmN3MUNNMENlY0Zpb0F1OHlLZjA3Vm5vTklFUjViVWxKbEMyU2pQeEJ5Sk12UUhLS2EzOXI4R2VRZm5iWEVuaHJYaU82UWgrcmJWck9sMWFxUnJXRitsVnlTeGhhYlBYdC9EVlVCaWkiLCJtYWMiOiI3ZmNhMDA4ZmU1ODE4YmQzNDczNDZmZTU1ZDA5YmFkZDQzNGQ4ZDVmMGZjYzRmNjNjMGE3NGRkNTA2MDdiOTZjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-593509527\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1222032010 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A9rdyCRm7SKpfljuMnVO7IpcgTBMITMjowAdsmJo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1222032010\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2015789519 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:36:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNYRnBDdFNpby9wUTVKVFhEQTNJZlE9PSIsInZhbHVlIjoiKzZ5MkZCZXZ0TlBDRUhSRjZ0TnYxVWhTMFFQeklQa3BzR2NpOTdrd2I1ajRlR3lseUFoMm5Cd0cwZWRuUkYrZU9ieDFHSGo1SnhMWmI4c1FVWmVJRzZORVFUSExIMEFKbkROWVlkOEx3L0p5bVRObnEvZEUzb3QwTjJFMkRvbFBhLzFpRjF1REUya0h0dmQ5SjhockduY3hiVGJxT2VpdGp4ZUEzOHpuN0EzMmlRRFhNcnNqVlJ5dnRiQmluTUdDRlMwR1BpbUpVZ0Fpd1N3RHZnOTVheTlWaXZ1cGczbDc0eEkrV3RrNldOUVlIU0g0aDZVY2VjNlNzS2o2S3oxR29keThINlp6MEFlMEZpajAxUHlCbVR4OWV5WCt5Q0lYM04zRU9lbWFMSkNoV25ZbzBSK0JvY1BKamFyMVpub3lYZW55UVU2c21JanZFeDFnNnozbjcySzZWU3VBamhOSTJrRTh0WGJmRFFFV3JzUFVkT3czRUJFa1pOU0ZuVUhka0sydFZIdGNZcm1iZGpKTUNqZTJpQmErSkdaTWJkN0NpTWtUS0RTSWtNR0kxUUYrclllTEg4WENscC82QjBsaFFvci91NFRMckpsWkVTQzk0MEZpVmcvQkZqakpaYmJ6T29tRUlYQ0dseE82TGdBdkN6NERGZTBJK3E4Sk5kNGQiLCJtYWMiOiI2M2RjYjM4OTNhNGY5ZmI4NzA4YTkwNTA2NTM3Yjg0NmQxMzBlMTRjMTY5N2Y4NzVjODg3NjllM2U3YWIzYmIxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ijl4aE95VFBmRENFUXJQMWFOZ3hZYlE9PSIsInZhbHVlIjoiTkFXSnJ5eStuVS9zM2ovY3JoMnhFQWs2RWtsdkdWM1ZqcDkybFZCeWFIWFp2S3RQZ1NCbUFmdk9venpmQ2VZZHdKcit4eXprR01NOWVWMzltanM5NkRBT0JrM0FHa3h1SzY1bjNPUDgvNjhYNnpNY1BZT0VsZFF0UlpWYTFTTklibHdyODJhT0xIcWZaZXVNelVMNUhDdmQ1MUJ2ZzZpT3kxSUloazlGbXZPOWNHQjByOXRiM3B5SmN4Y1E1WTJVZVpybHZPckJoQWk2Rk8xM3pwdHFKTG9ESk55ZlVuYTdaVDRpMTNuSmhlVXAvOUs0RzhNbHpOSmhmajkzOXJoQjQyZ2F4WmFyMWVBMm1GRm12Q1AySVFhTDM2MU5EaFVia2lXQzhjdTRiemZ2Q3J1Zi9jVUlzRFd6Tndua0NtVUtDbTlLYjVCcnBVR0ljZXBSRmJBaGFrQldvS1pMVTJkdS9ic0czNkRzUFZ3TWE2alI5b2cwV2dsWEttUi9rQjVzZDJGOXZBYmFESisxNXRCOGVmcW1SNDc4TU9RZVgweXlQRnZ2djdwQmVMdm5OMXllcWk0SEdQTGlaNkpBZGpiclNzUmJhYkhTOElFNGlyUHhjRWlzak9iMVp0WEV3OHJMSDZUMFFydlFYMGNZNXpSQmx3SCt1ajRSY2hpMTFKSkoiLCJtYWMiOiIyODVhNTI4MzBmMWM2YWUwMzRlMTdkMzk1MDk5NzZmZmZlMWNkNzcyZjRjYjJkNmQ4MWI3NzNiMDQwZTJjODg0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNYRnBDdFNpby9wUTVKVFhEQTNJZlE9PSIsInZhbHVlIjoiKzZ5MkZCZXZ0TlBDRUhSRjZ0TnYxVWhTMFFQeklQa3BzR2NpOTdrd2I1ajRlR3lseUFoMm5Cd0cwZWRuUkYrZU9ieDFHSGo1SnhMWmI4c1FVWmVJRzZORVFUSExIMEFKbkROWVlkOEx3L0p5bVRObnEvZEUzb3QwTjJFMkRvbFBhLzFpRjF1REUya0h0dmQ5SjhockduY3hiVGJxT2VpdGp4ZUEzOHpuN0EzMmlRRFhNcnNqVlJ5dnRiQmluTUdDRlMwR1BpbUpVZ0Fpd1N3RHZnOTVheTlWaXZ1cGczbDc0eEkrV3RrNldOUVlIU0g0aDZVY2VjNlNzS2o2S3oxR29keThINlp6MEFlMEZpajAxUHlCbVR4OWV5WCt5Q0lYM04zRU9lbWFMSkNoV25ZbzBSK0JvY1BKamFyMVpub3lYZW55UVU2c21JanZFeDFnNnozbjcySzZWU3VBamhOSTJrRTh0WGJmRFFFV3JzUFVkT3czRUJFa1pOU0ZuVUhka0sydFZIdGNZcm1iZGpKTUNqZTJpQmErSkdaTWJkN0NpTWtUS0RTSWtNR0kxUUYrclllTEg4WENscC82QjBsaFFvci91NFRMckpsWkVTQzk0MEZpVmcvQkZqakpaYmJ6T29tRUlYQ0dseE82TGdBdkN6NERGZTBJK3E4Sk5kNGQiLCJtYWMiOiI2M2RjYjM4OTNhNGY5ZmI4NzA4YTkwNTA2NTM3Yjg0NmQxMzBlMTRjMTY5N2Y4NzVjODg3NjllM2U3YWIzYmIxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ijl4aE95VFBmRENFUXJQMWFOZ3hZYlE9PSIsInZhbHVlIjoiTkFXSnJ5eStuVS9zM2ovY3JoMnhFQWs2RWtsdkdWM1ZqcDkybFZCeWFIWFp2S3RQZ1NCbUFmdk9venpmQ2VZZHdKcit4eXprR01NOWVWMzltanM5NkRBT0JrM0FHa3h1SzY1bjNPUDgvNjhYNnpNY1BZT0VsZFF0UlpWYTFTTklibHdyODJhT0xIcWZaZXVNelVMNUhDdmQ1MUJ2ZzZpT3kxSUloazlGbXZPOWNHQjByOXRiM3B5SmN4Y1E1WTJVZVpybHZPckJoQWk2Rk8xM3pwdHFKTG9ESk55ZlVuYTdaVDRpMTNuSmhlVXAvOUs0RzhNbHpOSmhmajkzOXJoQjQyZ2F4WmFyMWVBMm1GRm12Q1AySVFhTDM2MU5EaFVia2lXQzhjdTRiemZ2Q3J1Zi9jVUlzRFd6Tndua0NtVUtDbTlLYjVCcnBVR0ljZXBSRmJBaGFrQldvS1pMVTJkdS9ic0czNkRzUFZ3TWE2alI5b2cwV2dsWEttUi9rQjVzZDJGOXZBYmFESisxNXRCOGVmcW1SNDc4TU9RZVgweXlQRnZ2djdwQmVMdm5OMXllcWk0SEdQTGlaNkpBZGpiclNzUmJhYkhTOElFNGlyUHhjRWlzak9iMVp0WEV3OHJMSDZUMFFydlFYMGNZNXpSQmx3SCt1ajRSY2hpMTFKSkoiLCJtYWMiOiIyODVhNTI4MzBmMWM2YWUwMzRlMTdkMzk1MDk5NzZmZmZlMWNkNzcyZjRjYjJkNmQ4MWI3NzNiMDQwZTJjODg0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2015789519\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1452913866 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2352</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1605;&#1580;&#1587;&#1605; &#1588;&#1582;&#1589;&#1610;&#1575;&#1578; &#1605;&#1582;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2352</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2353</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1605;&#1601;&#1578;&#1575;&#1581; &#1593;&#1604;&#1576;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2353</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2354</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1588;&#1603;&#1604; &#1576;&#1591;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2354</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2355</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1575;&#1591;&#1601;&#1575;&#1604;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2355</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1452913866\", {\"maxDepth\":0})</script>\n"}}