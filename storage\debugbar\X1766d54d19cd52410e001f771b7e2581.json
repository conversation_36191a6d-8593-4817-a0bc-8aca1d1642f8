{"__meta": {"id": "X1766d54d19cd52410e001f771b7e2581", "datetime": "2025-06-30 22:39:39", "utime": **********.267003, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323178.794201, "end": **********.267022, "duration": 0.4728209972381592, "duration_str": "473ms", "measures": [{"label": "Booting", "start": 1751323178.794201, "relative_start": 0, "end": **********.207886, "relative_end": **********.207886, "duration": 0.4136850833892822, "duration_str": "414ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.2079, "relative_start": 0.4136991500854492, "end": **********.267024, "relative_end": 2.1457672119140625e-06, "duration": 0.059123992919921875, "duration_str": "59.12ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45028664, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00374, "accumulated_duration_str": "3.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2401452, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.636}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.252086, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.636, "width_percent": 19.519}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.258981, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.155, "width_percent": 16.845}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1359457589 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751323174926%7C11%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktXaDRLbzVMVWpzWHpoUkx6N2tVcUE9PSIsInZhbHVlIjoiVTlaWE9OR1AvanRLcFpucGVqLzloOTJJcDMvSlo0USs5VTdOMm1HU0xnK2xybmhWTVRnSmcwRldZa0NlM2pUYStIbWdZdm01d0IwT05Gbk8zeEc2MU9PZ0l5Sm1jNDJZNFY2UnRKb0RjWFBpWFFNeFBKQkZKTmNPY3J3VWh5S0Z6TS8wMWpscFhEKytTR3g3Sm9ZTDVPRVFHV05XdDRSS1E0MEdTdDZGZFNES1pQUE5Yc282aUtzUWw0NTVSUVNQeUpicldHdEVRblh0VmR0WDBGL3llb3czTTlsamhPVFRkUGdRbWMyaHBtTTZxYm0vRk5Vdk9ZZzgzSm54S2VKM1RNWkNDNjdKY0h4Yi96UDhXNDhWT29pWjVhK1R5N1hmQ216SHNaMkk0cE5Cbit2TlpHZFIxUGJsK1p6WmhEK2VLTnRTdkFrR0VORVQ2VW02VzdyZC9QQ0laMys3aGQ5bG8zMkd0aGUrVERZN3F1SGZkU3RMZjBiZnZaTXIxN0tLbTNwNWdRSk1CckFLQ1liQmNwNVFCNGdSK2xjOE8wc3NnR0pqNVZwOUQvWEhQQUJoYmluamhKTlNPN2hMTEIwTnFPWEhpN2ptNS9pOEdQa3Jqd3Zza0M3NmdkUkY2WmlSLzNpK1k0amZ2L0lYVmdFaG0zVTR3TC9FOFNLTDFTYnQiLCJtYWMiOiI3NmI0ODE4ZDRhN2Y1NzM3Y2MzZTIwMzZmZTA4YjNkODJiNzc3YTljMzI5NThkNWI4Y2Q2MmZjN2RlYTNjMjgzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlBpbjR5Z21UYWRCekZFWjFiM0hSN0E9PSIsInZhbHVlIjoibmx2MGhMb0V2SU0zKzZWRjhxR2JPTUUwbGhwRUFxa1RqSS9vR1lxU1VpdGIvWmM2VG9NY0grblZBUlVxNEpsU0s0cThtQ0VyWmVrZDlRbW5DcHNHaVFXVmFWaFVKWHl2WWY4dVhXZVJRdlE0Y0VEekVQRWdyYkFSZ0pHOHFMaEhWVUdHV281UStBWWphazBhRzliMit6YU5wUU9Ea2FnR1VWdk1xL1VlVFRGbXRaai9aekYwd2UvMHQ2QTRPUkVqL2NGbjFuYWdBc3hTNHAra0xKZUg4SnJzK3Ztd0t5RVN0QzE4N0NLMzFUQUorRDJYdjBQRHR2c042VWdjN2Jtd1NKanhQdmhZYTVTZDArTWZhOGVLU0ovS1pObTB1NUhFV0E3MkFFVWIybTFJUXd1QUF2cHBBR25xSHpGZm1lUWFRTTNyWU1VRUFTdkVQQ0RCUWl0YWdpbFlTZnA4VzJacjNqR2lMUHAwSHl6dTk2K21zNDN0aFU0eXZTSW5HdGEwdnJYa29obTlSSldZcEhvUGVTd3VZMmxyeitaVk5LcjlPTXJwL2lsSFhaU1l2Ty9QUC8vbXdlSW1LNERoek1GbWdOWE11MjcvL2E3dUsvdDFyYnA5Q1VRVElRQjdkOTZwVDM0dFA0QzRhVEF1bWdacmt4Tzk3UW5tU2pPY2g3NzEiLCJtYWMiOiI2YmQ3YjI4NTRmMTJkM2I1MzIzYTQyZmU2ZWNjODc3ZDRlYzgyYWZiMDFmNjAyZGI2NmNjYWNjYjJmOTNhOTU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1359457589\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2055394755 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0M1K0sdNadPD2LVrnt8LmBw227nA9F1U5e3ROaJK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2055394755\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1973220129 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVYbzNZeU5BSzZKSFRGNFhrV2R5TWc9PSIsInZhbHVlIjoiMEl1TjNvampMMTF4QWlycVVudzZ3YThlcjFNRWRHQS9SSWVOZFk4TkdFRkNWLzZHdXNhcjlvWFZZZGl5bHVYemxNUEl0ZzhtOC9PRURoL05Zd3VJcUd6WndLMlNMem5IZWlzd2M2RWN6ME03UEUvbmUyMXRMdDVMMG1zVXpWUzk4bVkwV2pxaTZ0V2JNdXpuck16VTJqSUhmVE1NRjZ4YzlmbnFSL2tGVDNpZ1JUdlZZT1pTQ1llUEdVcEtiYmVXTUswRmN0bnB4YWFxdVB2L2VYdm94Y2pyWXByNCtxNjBYVjlRWm9XWndmQ21zZVdDK0pWdEdMc0haQlk2QktLWTJwdmlBZi80Q2c2RVo2bUMvRW11cEh0VVgwWXJpZnVObUZWSnFtOHdiN1phWkQyNS8ya0Qvc0VKTGNCQ3FtVVAwUFBqaFkrRXFsd3dxTEhGamxDWWNPYUl0c2EwVFJrUHBkSVdBVVJNY2I2SjRTSjEzY1gya0MvYUhvVzIwQWMzUXFPbmhMUzkxSktwUEtDUkRQTTNtSWE5UXZOc1JYMEp5b1pNR3BjRGIwWXBTdFNPRzQxcVhBdXpmUTZTbVdKL3ZjS3lReklScGJVYWpKaXpkNmRIcU9qWTBsc0o4czNVazE5RWcrMThlaXJiMEY2U0xYTXNJZmhIRm14Mk1iRlgiLCJtYWMiOiIwZmEzYzZiMmE3NDhmZmEzOTlmNGUzOGIxOWQ2N2RhMjAxNWYxYmU2NzFiZWMwYWQ1Njc5ZWFlMjdiYjFkZmUyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IndBSW5RU0oxOFhDM1BmRTBEOHQ0OEE9PSIsInZhbHVlIjoiVjFDQWtKaW0xOThESDhsazV1VDRBV2ZNTXQ3Z0pmQzY1Zk5GYmxLZktPMWRoVHFJZ2FVK3YrZVplUGdYb3IvUHZlUTk3RmxkSEhaUHY4QWhTNm1GWFpXbWRSVW5XQ1IwUGt6aWFDZE9DVGxSc01UQVZvMXd5L0l4STBzaTl1elZ1Z1ZVeXk3ZVRvUkZiaDgvbWU2SzJGZmFmbnZzcEZoRjRseXpHUnc5QjJrcy9qcy96SnZxUk5kRFpseEQ2Y20wTjZqNHJSRXBaa0dUSll5S0VpUmlROWtaUTVmQ1d1cy80SHlhcysxbkZYcEh3a2FCTUxncDJMSmdsYnc0bUgvWDZadkR2bzM4Vyt1WFlndEpRVDhiRmYxQjNscFcrQ3d5QjVvRm8zb1BjR1o1Y2x1czR0TVcwZG1od0lRMU95OFRyMU9GNGtFbCtCRVdZS000ZjZtaFpqUC9GbUxvMEpTZDk1WStDVHkxU3FVbWpsaDRtYlZSblE2cTQzdm5BU3JwdXp6WktVMGJHNWE3aXJLQXhkOEM0L2NqZjk3akJTUHpDRytvemZqUEljU1ZWN2dGMTBLNFo3TGhuZ1NzWThBS1UxT21YWjRWRUQyVGlQR2VVMWcyd3p0R3RUbFJudW41Q2oyaVY0OXFkNVZLL0wyM0pKS1Ayck9ZbzM3VXhnMHAiLCJtYWMiOiI0NGZiYzI3NTE1MWQyZTkzYzBiNTg1OTBkNjkyZWE5OTU5ZTczMDZiODI2ZTBjZmJmMjFiMTAyN2Q2YzE3OWYxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVYbzNZeU5BSzZKSFRGNFhrV2R5TWc9PSIsInZhbHVlIjoiMEl1TjNvampMMTF4QWlycVVudzZ3YThlcjFNRWRHQS9SSWVOZFk4TkdFRkNWLzZHdXNhcjlvWFZZZGl5bHVYemxNUEl0ZzhtOC9PRURoL05Zd3VJcUd6WndLMlNMem5IZWlzd2M2RWN6ME03UEUvbmUyMXRMdDVMMG1zVXpWUzk4bVkwV2pxaTZ0V2JNdXpuck16VTJqSUhmVE1NRjZ4YzlmbnFSL2tGVDNpZ1JUdlZZT1pTQ1llUEdVcEtiYmVXTUswRmN0bnB4YWFxdVB2L2VYdm94Y2pyWXByNCtxNjBYVjlRWm9XWndmQ21zZVdDK0pWdEdMc0haQlk2QktLWTJwdmlBZi80Q2c2RVo2bUMvRW11cEh0VVgwWXJpZnVObUZWSnFtOHdiN1phWkQyNS8ya0Qvc0VKTGNCQ3FtVVAwUFBqaFkrRXFsd3dxTEhGamxDWWNPYUl0c2EwVFJrUHBkSVdBVVJNY2I2SjRTSjEzY1gya0MvYUhvVzIwQWMzUXFPbmhMUzkxSktwUEtDUkRQTTNtSWE5UXZOc1JYMEp5b1pNR3BjRGIwWXBTdFNPRzQxcVhBdXpmUTZTbVdKL3ZjS3lReklScGJVYWpKaXpkNmRIcU9qWTBsc0o4czNVazE5RWcrMThlaXJiMEY2U0xYTXNJZmhIRm14Mk1iRlgiLCJtYWMiOiIwZmEzYzZiMmE3NDhmZmEzOTlmNGUzOGIxOWQ2N2RhMjAxNWYxYmU2NzFiZWMwYWQ1Njc5ZWFlMjdiYjFkZmUyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IndBSW5RU0oxOFhDM1BmRTBEOHQ0OEE9PSIsInZhbHVlIjoiVjFDQWtKaW0xOThESDhsazV1VDRBV2ZNTXQ3Z0pmQzY1Zk5GYmxLZktPMWRoVHFJZ2FVK3YrZVplUGdYb3IvUHZlUTk3RmxkSEhaUHY4QWhTNm1GWFpXbWRSVW5XQ1IwUGt6aWFDZE9DVGxSc01UQVZvMXd5L0l4STBzaTl1elZ1Z1ZVeXk3ZVRvUkZiaDgvbWU2SzJGZmFmbnZzcEZoRjRseXpHUnc5QjJrcy9qcy96SnZxUk5kRFpseEQ2Y20wTjZqNHJSRXBaa0dUSll5S0VpUmlROWtaUTVmQ1d1cy80SHlhcysxbkZYcEh3a2FCTUxncDJMSmdsYnc0bUgvWDZadkR2bzM4Vyt1WFlndEpRVDhiRmYxQjNscFcrQ3d5QjVvRm8zb1BjR1o1Y2x1czR0TVcwZG1od0lRMU95OFRyMU9GNGtFbCtCRVdZS000ZjZtaFpqUC9GbUxvMEpTZDk1WStDVHkxU3FVbWpsaDRtYlZSblE2cTQzdm5BU3JwdXp6WktVMGJHNWE3aXJLQXhkOEM0L2NqZjk3akJTUHpDRytvemZqUEljU1ZWN2dGMTBLNFo3TGhuZ1NzWThBS1UxT21YWjRWRUQyVGlQR2VVMWcyd3p0R3RUbFJudW41Q2oyaVY0OXFkNVZLL0wyM0pKS1Ayck9ZbzM3VXhnMHAiLCJtYWMiOiI0NGZiYzI3NTE1MWQyZTkzYzBiNTg1OTBkNjkyZWE5OTU5ZTczMDZiODI2ZTBjZmJmMjFiMTAyN2Q2YzE3OWYxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1973220129\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-15******** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15********\", {\"maxDepth\":0})</script>\n"}}