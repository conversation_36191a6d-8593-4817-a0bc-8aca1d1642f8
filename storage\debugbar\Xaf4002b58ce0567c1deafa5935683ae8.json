{"__meta": {"id": "Xaf4002b58ce0567c1deafa5935683ae8", "datetime": "2025-06-30 23:12:46", "utime": **********.825569, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.380208, "end": **********.825582, "duration": 0.4453740119934082, "duration_str": "445ms", "measures": [{"label": "Booting", "start": **********.380208, "relative_start": 0, "end": **********.752962, "relative_end": **********.752962, "duration": 0.3727540969848633, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.752969, "relative_start": 0.3727610111236572, "end": **********.825584, "relative_end": 1.9073486328125e-06, "duration": 0.07261490821838379, "duration_str": "72.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45043352, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01926, "accumulated_duration_str": "19.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.783831, "duration": 0.01832, "duration_str": "18.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.119}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.812494, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.119, "width_percent": 2.7}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8185048, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.819, "width_percent": 2.181}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1235272305 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1235272305\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1798575641 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1798575641\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-566434003 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-566434003\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-669684895 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325159599%7C24%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5wT3l4bGg5UysrK2IrQzZSd2pxb2c9PSIsInZhbHVlIjoiU2ZDenhnblJjK0Fyd3FOOFIwTUxXdlBjeGxuMm1XYSt4Vjlrd3BmdnQyWkRFYXhNZXB0OFNvR2NQU25mUnNLckJJUHFYWW91eWVFRFZKUy9iME1iUEgxa241S05vM1RrNEtqMEdNcnNkYjhocFl3Z3ZXU2ZwYWUyUG1mZnRaV1lRQlAzbkxRK1RZaGppL25yamlaVWY1YUIzYVdYS200RG94Q0xDNjhkeUV3WE1ic2FHcndxUFpVajhzZ1B2NkNuc0ZEc3RodWtOdUo3a2NPVEJvZFh5NTFsMjV0WTV0bThzcDBHak9nVG9ER3RFZnM4WlZsWXdpSmNDL2NJUGhiWlh5azV6blV4R3BZZ000QklyMHRhMm1ZWllMMWRNSHZzeUo3NnJTOXdCMUl6WkxjRDBjd3VaS1BQcFo3dWRwai9qWFFoWjFwL0NmZ3ViTUlRcHppNmFMN2h4ZVMvcGZzc28xSi94Z0dlTmgrVTYxelNpUTNwcVc5M3djWHF6dzNNdHZIM3oyR2lMMlRMTGNiUUZXRWdQeXVwemZpZ1BvQ1hYTFNHcHV4VE0vQkJzT29hU0RkS2lNSzRjemFRSTRxa0hnRFNaL2p5Yjk1eWQvOWw0d1luQmtaQmVQYU5ualRHSG5PS0t4bGxncndqYm5DdmozUDZMbDdhdWVNbk95VEIiLCJtYWMiOiI0ZGQ4ZGFlNzNlMDgyN2VmMTZmOTE5OWE4NWVhZmZkMjQ2ZjllMTgwMGI0Nzk3Y2UyOGYxMDk0YzY0YjRlOTMwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im5nMzcySzk0TnFJMTVGUis2SDVhbmc9PSIsInZhbHVlIjoiMVdmaW1qV1REb2pSSTRrdUNtZWJWTXlUUU1nYmMvUms3SmZ6ajB0ZEN6REJnWk95eGxmSlE4eXBWWkhXQXpYdGcyQWVVa0pLbE1KODY4SWpCUE9ITlg5SEx6QVdZblI2ZDNqeXNxQVkwdjhGYmZUSXR6bHdRYVhvR1IrY2w4Q283RFRUWUpaMmhzbzkxWWhYYkRuOFJQaUwrdU1TczJhVkkwWmlBRE1rNzNTY1R4SGhYWTRLVVNNeXJTaExIODBlY0NhbExCdkxkR2E1MXQ5SVhBN04wQ3hSMGx4OVBIRzIraC90eE1TNE9Qd1BmRlJoZkk5Q1BjL3FEZW5vcXBvQzZlcVBKVFBXdHl4YkIrNEFTTWhlSHFyRWN2eGVHQnR3Wld1R0MxUkY3UUk5SFBVcFMxb1VOeFplK3d0T2R3QmVIaDJIZW93emYxUE9JSUlBR2xtSXpIWmllYkRnUDRBRUVoOVhNZzhndlVlWFVtaGV3aTZ2KzV3TGpNL3FFVjhQaGM2MnRRMUpQQnpNai8yUEVqR2ppWVFHbVo2RU9IOExwQy9ockZieUZxRFVSSU5WWDVXclBJVzEzZjZJSzhyaUp3dFdEQWt0UHlIV2ltdzZPSXpFaDFld2FZNlNqTE5lTi9sZFBTWStSY1RRRXFVQU4vM0k0K1NESmJUKzgxaUkiLCJtYWMiOiIxMTljMzFiYjFhMDk2MjBlOGM4MGFmZmNhZDdmMzI3NzljNzdmZDBkZTIwMjkyOWJhYjY0NDNiZWI3ZjY3MjRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-669684895\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1843233660 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0M1K0sdNadPD2LVrnt8LmBw227nA9F1U5e3ROaJK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1843233660\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1336210131 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:12:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1DWXA2NFQyL1oyNkMvVkJXSWQ2OFE9PSIsInZhbHVlIjoiUnBCSXVuR3ZaNWY4T3VxU3RwZTlWNTAvdmdKUlBjT3IwaEhSSDRiY3l5ZHBWOWhmcjgwalR0KzlDczRUdm9uN0w5bW9jaHBWdEhiMXVjSnU1TnJlUVlTdTVnS0liR2dOcnN0cnJWSU9DeFgxaTFhVVhVdHpXUmVEVXhabnZHMUpDbUdMRmJQSmxud013RDh0UHczWUpCcEc4M2FkS21XNC9HK1ArTWllRGFoY28zTXpNRjVNamE1dEt2Z3ZMV0lQZzlPb3RBZ0ZHVEx4SHZEbzgzMEdmajkzMmV1c2pteXR3SkVkaExYTjZ0MTVPVkN6SklqK3dINGRVdjRJekJpMmdySlM4dlNWKzdFQUVWK2pLTy9NVUVZYlZqZm85Wk1jVHdWd2NRQzFtMU9rYUhtUzd2OUdWamNNOUJTMzlLMVZOeHNNODEvNElhUUsraFh5VWpZcDB2bHUzQ1FabWpCb2FrM0ZIeW02WlJSSmtkRU1nU2F3Zkk1aHo3YTNMS2h6bitZWFRLRXJJaklGdDltVWpXZTVnYk82akU0SkNVTTRxMlhKY2x1RFlZZmRUbGFXU0ptOTBXOWFNOHF6bzJTYzE4ZFA0cHlsVlVsZXlEOUNTMzAydGtIV0l1dWt6M2tPVXJMRFRQaVhGNk80ZlJmaHN0VDR2OTVrNk1uSGM3MWgiLCJtYWMiOiI5NDkxODQ3NDdhMmRlY2NiNjRmZWFkNjIxZTE2MmQwODZkN2MyYmU5NmNhNmY2ZTk4ODNkNzdlZjVmOTA4NDBlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:12:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkV3QkdzWkF6T3ZJZHBRWEhVeFNKRWc9PSIsInZhbHVlIjoibVY2U1B6aHdtR3hMeWx0VE5DTUVxZ05qcWhBWUp6L2dJWDAxK0MwcmQ2K1RUejZQaGVIak9ndWFBRVliTTY2SitvbFJCUENSZ0NvOEQyNm1kOGdWWkhJeW11V2wwK2tsOU1SVmhxdkFiY2N3SzhTek5Vd21QUWoxZ3dGQWFRWWNaNmlPNFFJbVplZk5VZEQ5Y1J5anl1Yy9sbzE2RTdjU05WTWVHTjAzeDc0N0pHT1hKY1Z4WTYzb2drK1hRZCtYbVI5OTB4YkErUzJQc2pNcmlTenBvS0txT0tlZWgxTVp4c08zaUNjUHRNa2t3dG9kVVVQQUNQRUlaT2lHUTU1RllNWFNtTkR1dXcyMTRLRG5xSktDVUhiamQ3YmR6OTcrek80bXJkTTMvZEx3cGVzTWVmZm4zcndFNTBzZzA1dlppeUU1WVJFSWt3dzVqamZGbjF4dE5UZjl6MDM5WW5aQ3J3b09IY3JVbFFxbTVsdkRyaG1VdERlY0NUTzVkRS8zVjVZMUpmWjFPYUl4cklzNTJIb21MaFFYY3pEN2h6TFJHMkVYT1Q0WEZEamI3TFhGR29OUko2aE9mbHFvYzBjUmJFMStFUEsrZG5pYnVCaXorYnFZdkRjaktBbHovV3VuT2UrSVd0RGdsOHhlY21yMktJaDhyUzZ6aXVUT0djRW4iLCJtYWMiOiI1ZjFkMzhkNGE4OTQxZGNiYmQ3ZDgyYmNmNzI3NzgwOTE2NWExMWVhODgyMTVkYWQxYjFlZGNmY2YwNWM1NjFiIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:12:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1DWXA2NFQyL1oyNkMvVkJXSWQ2OFE9PSIsInZhbHVlIjoiUnBCSXVuR3ZaNWY4T3VxU3RwZTlWNTAvdmdKUlBjT3IwaEhSSDRiY3l5ZHBWOWhmcjgwalR0KzlDczRUdm9uN0w5bW9jaHBWdEhiMXVjSnU1TnJlUVlTdTVnS0liR2dOcnN0cnJWSU9DeFgxaTFhVVhVdHpXUmVEVXhabnZHMUpDbUdMRmJQSmxud013RDh0UHczWUpCcEc4M2FkS21XNC9HK1ArTWllRGFoY28zTXpNRjVNamE1dEt2Z3ZMV0lQZzlPb3RBZ0ZHVEx4SHZEbzgzMEdmajkzMmV1c2pteXR3SkVkaExYTjZ0MTVPVkN6SklqK3dINGRVdjRJekJpMmdySlM4dlNWKzdFQUVWK2pLTy9NVUVZYlZqZm85Wk1jVHdWd2NRQzFtMU9rYUhtUzd2OUdWamNNOUJTMzlLMVZOeHNNODEvNElhUUsraFh5VWpZcDB2bHUzQ1FabWpCb2FrM0ZIeW02WlJSSmtkRU1nU2F3Zkk1aHo3YTNMS2h6bitZWFRLRXJJaklGdDltVWpXZTVnYk82akU0SkNVTTRxMlhKY2x1RFlZZmRUbGFXU0ptOTBXOWFNOHF6bzJTYzE4ZFA0cHlsVlVsZXlEOUNTMzAydGtIV0l1dWt6M2tPVXJMRFRQaVhGNk80ZlJmaHN0VDR2OTVrNk1uSGM3MWgiLCJtYWMiOiI5NDkxODQ3NDdhMmRlY2NiNjRmZWFkNjIxZTE2MmQwODZkN2MyYmU5NmNhNmY2ZTk4ODNkNzdlZjVmOTA4NDBlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:12:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkV3QkdzWkF6T3ZJZHBRWEhVeFNKRWc9PSIsInZhbHVlIjoibVY2U1B6aHdtR3hMeWx0VE5DTUVxZ05qcWhBWUp6L2dJWDAxK0MwcmQ2K1RUejZQaGVIak9ndWFBRVliTTY2SitvbFJCUENSZ0NvOEQyNm1kOGdWWkhJeW11V2wwK2tsOU1SVmhxdkFiY2N3SzhTek5Vd21QUWoxZ3dGQWFRWWNaNmlPNFFJbVplZk5VZEQ5Y1J5anl1Yy9sbzE2RTdjU05WTWVHTjAzeDc0N0pHT1hKY1Z4WTYzb2drK1hRZCtYbVI5OTB4YkErUzJQc2pNcmlTenBvS0txT0tlZWgxTVp4c08zaUNjUHRNa2t3dG9kVVVQQUNQRUlaT2lHUTU1RllNWFNtTkR1dXcyMTRLRG5xSktDVUhiamQ3YmR6OTcrek80bXJkTTMvZEx3cGVzTWVmZm4zcndFNTBzZzA1dlppeUU1WVJFSWt3dzVqamZGbjF4dE5UZjl6MDM5WW5aQ3J3b09IY3JVbFFxbTVsdkRyaG1VdERlY0NUTzVkRS8zVjVZMUpmWjFPYUl4cklzNTJIb21MaFFYY3pEN2h6TFJHMkVYT1Q0WEZEamI3TFhGR29OUko2aE9mbHFvYzBjUmJFMStFUEsrZG5pYnVCaXorYnFZdkRjaktBbHovV3VuT2UrSVd0RGdsOHhlY21yMktJaDhyUzZ6aXVUT0djRW4iLCJtYWMiOiI1ZjFkMzhkNGE4OTQxZGNiYmQ3ZDgyYmNmNzI3NzgwOTE2NWExMWVhODgyMTVkYWQxYjFlZGNmY2YwNWM1NjFiIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:12:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1336210131\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2022381834 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2022381834\", {\"maxDepth\":0})</script>\n"}}