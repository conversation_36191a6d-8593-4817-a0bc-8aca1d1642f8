{"__meta": {"id": "Xd6b9bd34baff90c40f8aba19707a9e0c", "datetime": "2025-06-30 23:10:59", "utime": **********.887408, "method": "POST", "uri": "/pos-payment-type", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.495653, "end": **********.887424, "duration": 0.3917710781097412, "duration_str": "392ms", "measures": [{"label": "Booting", "start": **********.495653, "relative_start": 0, "end": **********.834021, "relative_end": **********.834021, "duration": 0.33836817741394043, "duration_str": "338ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.83403, "relative_start": 0.3383769989013672, "end": **********.887425, "relative_end": 9.5367431640625e-07, "duration": 0.05339503288269043, "duration_str": "53.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45424768, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@financialType", "namespace": null, "prefix": "", "where": [], "as": "pos.pos-payment-type", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=190\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:190-261</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00219, "accumulated_duration_str": "2.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.862383, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 83.562}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.872016, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 83.562, "width_percent": 16.438}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq22\\app\\Services\\FinancialRecordService.php", "line": 585}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 200}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.875725, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:585", "source": "app/Services/FinancialRecordService.php:585", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FServices%2FFinancialRecordService.php&line=585", "ajax": false, "filename": "FinancialRecordService.php", "line": "585"}, "connection": "kdmkjkqknb", "start_percent": 100, "width_percent": 0}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq22\\app\\Services\\FinancialRecordService.php", "line": 604}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 200}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.877424, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:604", "source": "app/Services/FinancialRecordService.php:604", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FServices%2FFinancialRecordService.php&line=604", "ajax": false, "filename": "FinancialRecordService.php", "line": "604"}, "connection": "kdmkjkqknb", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:3 [\n  2352 => array:9 [\n    \"name\" => \"مجسم شخصيات مختلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"2352\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2353 => array:8 [\n    \"name\" => \"مفتاح علبه\"\n    \"quantity\" => 1\n    \"price\" => \"6.00\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"id\" => \"2353\"\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n  ]\n  2354 => array:8 [\n    \"name\" => \"العاب شكل بطه\"\n    \"quantity\" => 1\n    \"price\" => \"6.50\"\n    \"tax\" => 0\n    \"subtotal\" => 6.5\n    \"id\" => \"2354\"\n    \"originalquantity\" => 9\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Undefined variable $result", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-1601290133 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1601290133\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1847921170 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1847921170\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>payment_type</span>\" => \"<span class=sf-dump-str title=\"8 characters\">deferred</span>\"\n  \"<span class=sf-dump-key>total_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">18.25</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-881714471 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">82</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2818 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751325027058%7C14%7C1%7Co.clarity.ms%2Fcollect; laravel_session=eyJpdiI6ImpxTklOWXhTYU1XWEJkQTBqVTRXZlE9PSIsInZhbHVlIjoiTXFGZEpQRXlGa212dDlvcjMyZUgzOTJTdTZUUld1Y0RIbVM4elBEck42T2RTa0tjV2NoaTJNekRxM3Vkc3lrR1dOeGt5blA0Ym55U2pNZWY5am1QNC9lMm5SZE1VcGh6OVBneXMxN2Y4Qmx1TWhKckg0OXNOdzJBV1ZmTzVubEw3cmlyUDRwVnhxYzd4NmhqaEJiZThRcHlESndxYTY0WWNKZ1pqeVdFSTZuZnRmZ1BDZy91c3ZCbWloYytBQnpaczBrTmh2S3Z1dllBcHJYMkhWOXM3alFFWENqVkhpc2Rnb1lHSlpwM2dNb0s5cUxpakE5OVNEbEZRZ0VUVDJBWFZKWmZyYjZRM2ZNL1BuR0l5L3BsYXhVbzEwQ1ZlaXYySVUrQnk5a0dvY1BKK2dwQnEzM3ppZnFlaW9XTW9Ga1pSQ29mZktuT3BjSnc5ampzYU1SdTIxNzd4Wms4eUJ6eWdrajg1UUhYS2puSkhMNDVRbEh3WENJazQ2Qzk0Vlk5cE5NYW1qQ0xCbWJBVHZ5ZUlzUzBGRTJCWGliL1ovUHlOejhGSHJzRS9UOC8wK0QvTTQ4WHdveVBkTE1CdHlWcnFrQWdDcU1jR0hscFd0SUgzYWRtbFRaT0pzTGUzSkVDS2k4YlhiU0s3b29aMU5tbWtnL1NUaWZ1VmhxYnZ3ZFoiLCJtYWMiOiJjNTgzZDkxNDlmYmY2OTAwMmNmYTUwNTg0OWMwNDQ3MTVmZWQ0YWFiNjk4ZTJkZDM4YTkzNjhiMzE3YzIyNjlmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InBRRTA3R2tLNjI4c0lrRkdnOUxmWlE9PSIsInZhbHVlIjoiUjlQVUFILzR4amtCNk45Z25ZUW4zRElzUjg5NC9DTm1Ka3l5OFJ1SXJCUDJBNXNsM1N4SWUwb24rQzNvZkxBMU91VmhOYzhKdGo4a3lYS1k1MGZDMVVCZnliWjRMNTVwakZ2TTVXRE5DWXlCSjh6NU5xb2VJdXB6UGhHWVlPcFhxeEtyemJaN2RtNCtnL2FUeGdYYTN4NUJJVng4V20rN2lIVTlTdWVKRjBFS2phblZGT1R4b2xKZFUyMGZhZ2ZQSHhEZTZ3UW1TZ0xiODE0dEJwTUtZVThlY0xMbk5TT1dKc0V3Vm1aRllLZHFQOU5vZFBialF2cWNiV0FYT2lNQlI4YU5BdEVLdnZFa0MvdSttS0g1K3N3dzEzU2ViTnJQRTJEVVkwZGVBa3JNNlRSeTNoSTNubUZqVE4rT09jRGFYVjhHdUdNYlh1VmRuby8ySW0rakhzL2ZCanVRSzBSY1p1ZDRFZVBSK1JoSnloK0IyR0JnS3l0N25LRkhjYk9uYkVWWVc2NmQ3TU0yZmh6eWNTelBocjBaTUFTK0RISXVpNExmc0M3QVRkcmp4QU1QRGJBRmpYcEY5dnJWb3BkbFRuRU9wWUl2a0NLK2dxc3hWTitJd1JIZDFBYThxaUI0R1d1bjVGZUs2WFpMMjRJYlZCRXF0YUV2MmlPL0VjMVIiLCJtYWMiOiI4NmU1NGMwZWViMjFkMDJlNWY4NzUxZTQ3ZTkyNTMxYWU4MGQ5ZjViZGFjYmJmN2Y4OTYxZjZmNzU2Y2U2ZWViIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlFPV3JBd0ZPMzIxMGFyV1ZRZGVaQWc9PSIsInZhbHVlIjoiSUFyMEQ5aUVGb3d3ZVhUWlZBak1KODVadXdhaDZmZmhPSXgwd3dYOW5OYjI2UHBodnFIUzJHOENKdG9EdVpzU2J1VFg1bWVCcGVrcDRnQXRlRldqRjRJbGxHeVJpMTZLakppYzZ3bkV0d0FPR1pHSEJpSnNmcStRT1Vhd3NJVUJZVkNWNTVWYUlOR3dMM0VYZEgrREJyMlpYamN2c3c1aW9nczA4Sko2bEo3VzlnaGY2V0JRSnFleFBiN2hDYlkzQSthNkJ2T254Rlc2QzZMMmpWeVZUUi96b0d6eWVJcGVpckVyOHZIM1VheEo1cVhXK0ZGVmY2eXdOVUZmQk9CNGtSS0xuRlZpSmxyYUdHV0JCSmFpT2p2eElwcEVIc2E1eGpxVUZFNUpQMC9pNHhUaUxiODAwZFJhSS9TT3NWWU56Tjk2YUljTzgyOHRCc3JzTnpFQ3RkOHgxZFNUVHBVZlNwRmcxUXduZVZTSUo4SFFDQTJQdGsrYUk5M1h3QzBaRGhGa3Q4SEF4MzRmZXNvWTBKZVR6MFZhZGE0YW9YcTZKWkdmL2FzZjgxTXR4MWo5ZlhvTFJwazR4UDRMU2RsRjZqS2ZaYXlJWGVUYTJMdkd1SFk4NzQ3alRoajRoNVV1SzEybGtqMWpBeGh4dTZlTXZTU2cybEhCWGdSYXk2SU4iLCJtYWMiOiI1NGMzNjk0ZGMxODU2NWIwYmY4ZjNjODFjMDMwYmZlM2MzNDY2ZTI0MTJlYjcxZGI0YjIyYWFlY2ZjNjRjN2M3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-881714471\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1127986928 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nMXQGGGSGQFIfGwlrnEYCl0LIB0DtCvcZqQRlbGj</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1127986928\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2050202939 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:10:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFCbzlmeVlLMExra2FHaE1YcVBjWmc9PSIsInZhbHVlIjoia0tzZjVPcmNpeUZTVWxwZkNuT01qT3prSmdFcG52VkMrWi9YRmdXUEgwa2tWVDdMTldscG9xTmxjWUZpWHJHRzJFZEVxWGswSy90TmhwdkpycTJORkx4eFVOM3kwaW14Z2RCZWlSL3dXUzR5a0lHMytrY0VKZUZUYzNFWnU3dVpyTXdPbUZCRkxRVGRTV00xNkxBVHk5Y0Vma1RVUFVWZ2pHQnNwdjBpNlVNWFJzeE5sbTZhYWdpc0t6YngyUzJNQjY2c0IxNU94dVczUDRQNlg0Y1F3VkZEZW4xN1JVRmJWcUE0Z0hUOWhDQ0JoLy9tUTBYM1pZRDNLNy9hUldYTmVtSFNpSUdCNDNYcEFzbHFFc2Q1dU1ic2EzTnFLSGV5Z3R0c3N0V3hSdERrU2piWDhOdTB6R1p2ZDJDa3VLUDlDMUVkRUZ2Yjlzc3NwdUpPTzBtZnp0VE1oWjJhWEhYQ0RrZ1ExSmNyY3F5MEZkS3V4eTNTZGdrby9tRlZqbUFadThsaVpvWFpvQ3FZN0lPa1RaaC94aUtabTBBRFBoSG9jN3ZreU12ajhSNUZmNjFVSjNMWnQ5MVUvUHdubGI1TFVNUnV5dEpvRlF5N1JXZS9ZdENMV29sdy9Ob3hWaUpuQUN2QURReEMyZW1rTFRDSU5xMDM1S0RjZWMySWxVRmMiLCJtYWMiOiIwNjE1M2U4Nzk2NzFmMGUyOTFhMjAxYjM4OGMyNWQyZDE2OWRhMzdlYTllY2MyMGVmMGMwMmIwMmE2YzZkOTIzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im5SeURJR1VwTGRicndDdUc2QXgwZ2c9PSIsInZhbHVlIjoiQU1TVW1hZnJMVkdCb2FOSFEwV3ZlT2VSZXZ1SVUyYUhyVnViVmxNL2VJK0pNYjBTcjhWT3ZjTGg4V28xdVZPaGJRNVdIdlU4d3dDa1o3dUpVWmx3ZDlaZG9RU1daTHFwNWVCMEVSQ09vQTNrV3Q2YVVDWjFoTEUwQWFMckl0WXBUMFE5WEQ3Ry8vZTUvRjZaZDZrcmRsZ21uNXFsczVURTd3MitodzZTSzE1bnp2Q3d2N1R2MktHNjRVWXkvRVJCc243WU9lSUZHcGVGRWs1WU5lZ2ZhM0dVWmk4aUZEU29hMEFOR1RUQmRPQUJnQVlRcjk0Z2NrMTNiSWZHSitmSkRmL2tjbFkrcHRnQkJhbWRaOFdLdEorRU1MVk9UZzVUM01mUjFjK1M0Z3ZZTVJqZENlaUk1TWZNTnoxdlRFYmpjaUpxMzUvL3AxQ2ZNN1NFR1B2VDZOMVY1aHZSbm9xWUczT2V4aWYza3pXdU5RUFROTmhpMWF1VGVSYXJJSkd2NGErRTRpNG9ENERNZ1dEVG9SK3hMR2NnRytpbVVjeHE0cmtBN2xQQ0xtMmxON1JXR1BZbFdMR3BnK2xXL3lKVWRGeGJkR1QrbE5lektKMkJMUFMzOVRENjlQb1A1V3BtbVQ5NmlHQzVRVE5QMVIwUDN5MzJsOHZOVXhJSEdpVGIiLCJtYWMiOiJkNDBjZGQ3ZTg3ODkyZTYzNzkzMmE4Y2M0YjM4NGMzY2NhNGMyZDFiZTMwMjI1YmRiMGI4NjNjZjlkNmYwMzE2IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFCbzlmeVlLMExra2FHaE1YcVBjWmc9PSIsInZhbHVlIjoia0tzZjVPcmNpeUZTVWxwZkNuT01qT3prSmdFcG52VkMrWi9YRmdXUEgwa2tWVDdMTldscG9xTmxjWUZpWHJHRzJFZEVxWGswSy90TmhwdkpycTJORkx4eFVOM3kwaW14Z2RCZWlSL3dXUzR5a0lHMytrY0VKZUZUYzNFWnU3dVpyTXdPbUZCRkxRVGRTV00xNkxBVHk5Y0Vma1RVUFVWZ2pHQnNwdjBpNlVNWFJzeE5sbTZhYWdpc0t6YngyUzJNQjY2c0IxNU94dVczUDRQNlg0Y1F3VkZEZW4xN1JVRmJWcUE0Z0hUOWhDQ0JoLy9tUTBYM1pZRDNLNy9hUldYTmVtSFNpSUdCNDNYcEFzbHFFc2Q1dU1ic2EzTnFLSGV5Z3R0c3N0V3hSdERrU2piWDhOdTB6R1p2ZDJDa3VLUDlDMUVkRUZ2Yjlzc3NwdUpPTzBtZnp0VE1oWjJhWEhYQ0RrZ1ExSmNyY3F5MEZkS3V4eTNTZGdrby9tRlZqbUFadThsaVpvWFpvQ3FZN0lPa1RaaC94aUtabTBBRFBoSG9jN3ZreU12ajhSNUZmNjFVSjNMWnQ5MVUvUHdubGI1TFVNUnV5dEpvRlF5N1JXZS9ZdENMV29sdy9Ob3hWaUpuQUN2QURReEMyZW1rTFRDSU5xMDM1S0RjZWMySWxVRmMiLCJtYWMiOiIwNjE1M2U4Nzk2NzFmMGUyOTFhMjAxYjM4OGMyNWQyZDE2OWRhMzdlYTllY2MyMGVmMGMwMmIwMmE2YzZkOTIzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im5SeURJR1VwTGRicndDdUc2QXgwZ2c9PSIsInZhbHVlIjoiQU1TVW1hZnJMVkdCb2FOSFEwV3ZlT2VSZXZ1SVUyYUhyVnViVmxNL2VJK0pNYjBTcjhWT3ZjTGg4V28xdVZPaGJRNVdIdlU4d3dDa1o3dUpVWmx3ZDlaZG9RU1daTHFwNWVCMEVSQ09vQTNrV3Q2YVVDWjFoTEUwQWFMckl0WXBUMFE5WEQ3Ry8vZTUvRjZaZDZrcmRsZ21uNXFsczVURTd3MitodzZTSzE1bnp2Q3d2N1R2MktHNjRVWXkvRVJCc243WU9lSUZHcGVGRWs1WU5lZ2ZhM0dVWmk4aUZEU29hMEFOR1RUQmRPQUJnQVlRcjk0Z2NrMTNiSWZHSitmSkRmL2tjbFkrcHRnQkJhbWRaOFdLdEorRU1MVk9UZzVUM01mUjFjK1M0Z3ZZTVJqZENlaUk1TWZNTnoxdlRFYmpjaUpxMzUvL3AxQ2ZNN1NFR1B2VDZOMVY1aHZSbm9xWUczT2V4aWYza3pXdU5RUFROTmhpMWF1VGVSYXJJSkd2NGErRTRpNG9ENERNZ1dEVG9SK3hMR2NnRytpbVVjeHE0cmtBN2xQQ0xtMmxON1JXR1BZbFdMR3BnK2xXL3lKVWRGeGJkR1QrbE5lektKMkJMUFMzOVRENjlQb1A1V3BtbVQ5NmlHQzVRVE5QMVIwUDN5MzJsOHZOVXhJSEdpVGIiLCJtYWMiOiJkNDBjZGQ3ZTg3ODkyZTYzNzkzMmE4Y2M0YjM4NGMzY2NhNGMyZDFiZTMwMjI1YmRiMGI4NjNjZjlkNmYwMzE2IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2050202939\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2352</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1605;&#1580;&#1587;&#1605; &#1588;&#1582;&#1589;&#1610;&#1575;&#1578; &#1605;&#1582;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2352</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2353</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1605;&#1601;&#1578;&#1575;&#1581; &#1593;&#1604;&#1576;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2353</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2354</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1588;&#1603;&#1604; &#1576;&#1591;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2354</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Undefined variable $result</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}