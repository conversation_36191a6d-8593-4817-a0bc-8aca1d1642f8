{"__meta": {"id": "X5c9da6f1a9795688f7b760fdc3256be7", "datetime": "2025-06-30 22:40:46", "utime": **********.031464, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323245.565478, "end": **********.03148, "duration": 0.4660019874572754, "duration_str": "466ms", "measures": [{"label": "Booting", "start": 1751323245.565478, "relative_start": 0, "end": 1751323245.970548, "relative_end": 1751323245.970548, "duration": 0.40506982803344727, "duration_str": "405ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751323245.97056, "relative_start": 0.40508198738098145, "end": **********.031481, "relative_end": 9.5367431640625e-07, "duration": 0.06092095375061035, "duration_str": "60.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45707192, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00353, "accumulated_duration_str": "3.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0016289, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.006}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.012916, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.006, "width_percent": 19.263}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.021382, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.269, "width_percent": 14.731}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1912140657 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323149690%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRFWndDbmNtMTJwRkp0ZlhrRTRwTEE9PSIsInZhbHVlIjoieDhld3RBaTdsY2llckNVRCtvSWpXRDFnQU91emdOWjBUVm4zZWlUb3BEeS9CVWhvZ0JscGo5VFd3Rlg1cTFKWURVdEhtYklJaFd2aWJibUtONEovbVNqSGF4c0NET2R2RVl0cmx1aUJlSzdicHJ3dTdjcnRwT3IvUnFNdVFjZk9ramZkRExjTlBWazdDT3dtTTd6alhkb2ZkbUdid0RJWXErYVRPZ3FUc2FIY0xuOXNjaytSNENsNzVtd0NrUU5hOTJIendnUjdGRW9tNnN1b29ULzV2VlJTMzB4c1d6MkFySXJoNkp6RXR3enR1RUZDTG1ZVFUzYzdaT09zeitkbnhkTjd1M3VaZ3E0UVRUNWZpQ2ZRelh1RXFjYUIxd1FRblVZWE9zc01mM2JkNCtLcGJ1Qnk5djdaV0RrL0pOT2U3RVRsSndOVFBWcjc1MEdRSjZGZTUramw4UElNVWIvL0VhN2lkTFFkR0ZTdkw3ejl0Z0duejl1YlBvMVJMQjdVM3YxWWY1Q0xPWE1NMmF5RHVOUmtjSk1QUlhTM09mbk1URi9KSUZacEs2QTByM1dSRVR2OW1NY0lUU2cxV1JxSFBlOHQ0c3ZVSys5bGVuQXBCdWRoekdWUWxQcXpZT1RKeHpEbGN0RHVyNUZWVzYxNk9hOUVySDlKcTFzRTVPcW4iLCJtYWMiOiJjOTUzMjk2YjIxMmE2NTdjNWUzODE3ZDFjNGQ5MzNjNjFmOTZmNjI2ZDBmZTc1NzU2NDI3NzE0MjBiZTNhMzg3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkJydC9Vb01uc25xVVVOZGN5YXJOb3c9PSIsInZhbHVlIjoiN2d4ZXIwbDQ5OWVhbFNwKy9RcVIzTmpxV3BhcGtwaEZoTHVXMlR6YS8zSDRQRUFWSXg2OGlHNzZYTXFhSWNhVWRUZkJiWEZoNEZTSlJBYmtFeXc1NWM4YkJsS1JLK0hIY1Nld3VIb3UrUndYMGFhY0tGdG1EYUpPLzFVWk9MRHdaeHFnQXNhbnFoc1BxcExxVVBXRHc1eU8wbjU3NXYxc1dyN2Z1WjNjN1lMaVBhOU54cnVRUHF2RW52UXEya0VkUmdTNERiZFlMTFJMbVF4dXkwUFFUMHM2dG5UTHhOUS9WVDI4YlV0WjlhLy9JQW8zUUVWRk9KdzRVK2tYSzJFSW92L2hnL0o2eGx4UGZNaEcyUGcyY2Irc0swVFdjakRzRlh5NzhjVkRzK3dzSDgxUzdXQzJjaW94V0F6WjZLSjdnUHRKUnhQVlRndUk3Y3FNb0tSUUZ6NXJ4V3dsc2RiQk1KVEs1S01udlA2WVJRWkhyZzl5aWRVNXpMYnc3NUxwUWRoNXRkMUNUVDBxWVhraWtldnM0WExZYUtLTDNyVjJpTC9lenk5Wjh6TnREMHhhK1B6Rm5zRkNJZVRiVlRIMWFhOU1DRHNBZWZ1cEx1cGlVUjh3MmpEU1IzUC9yRmZEZTEvUFR2S3N6ejY0M2d4S1QzSFBnSmxWWk95RkVQUk4iLCJtYWMiOiIzZDhhMTE3ZmRiMTQwYzZiOTI1MDIwMTJiOTFiZmYyMzExMDIxZGExNWQ1MTc1NGRlMGY1MTA4NDQwMmUwNjA2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1912140657\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2067608485 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2067608485\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-16512986 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:40:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBuTVlUTnFNYlNSY2t0TzVBTXZWK0E9PSIsInZhbHVlIjoiMFBxSjdReHQ5aGZWL0JZNEhBZnFrZGxxRUNKekxmSGRncElOSXJXeEhhbWovZ2ZOTmJEMkticVBGVkVCcitWVmJPN0dUUlMzaWhJR2hBSEgwTWNKZ3N0M0ovQXB3dXZlU2gzeWM1K2VQMyt3THk0MVVjM0h1a3prQlk0ZXY4Nnd5Nyt4QWxnNzJ4d3BqVmhsRlVnWXQ0c0E4TWthUURubXNONHZRazJsMXpuU0c2Q0t5eHkrMzlra0pnbEJ4UTllUFlQNmE4M3hEQ20yOTBKV3pJNVlBVy96ZSswWmZtTmZLVThCVERpdjM1NXJtd0FYb3lVL1U4aW1zMmx6M09RMVFiL1hnelJsY0VTMkZhbkN2V3dKNDZCVnBvOWs5UG1wcUlTNWNwZ0VuQWY4MFBuU3FONUxQSUVGNFJ6UTEwRG9YZWc0OFoxdzRtd2NINXV4TEYrcU5pdEc1QXBhTE1TQVRWRzZ5UVJtd0F4dTVnMXNTMmZ1ZGptVFAvOGZLRFFpU0NESDhmbGtwbUxUVlFsMmloa0xsaWU2OU9xbmRKSUJnMWRGaU51Z0hhaWh6djRpTCtheElSWGdhd0tPRXNMZkROS2NSQ0lFZ2xSYkFmR042Q1ZlRzhOalRCRFd0SmNkRnI5UkRxNDBpMzRDS3NEUU1QK2NZTXVWZk8xL0Y5K2giLCJtYWMiOiIxYTFkZDRiY2U1MzcwN2ZlYzcxODg1ZTAzNmQwZWU5ZmFiM2EzYjA5NDlmYTlhNTFlMzkyODJiNmNjZjRhNWE0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkUrWU8vVlY5TDFTejRuNWNVWC9la1E9PSIsInZhbHVlIjoieDdnU3NROXhRSUVWU2tNNXlSOTlkVyt4TytFVnNrdjlUWnlvSU1hbGFmaXZwZUpXV29xaVF4RDVMMVNuemR6aTNQOUNVc0Fna0ZPT05XampVek1HYkxSV1BSS01HSitadThTTjk0NHowMm5GQ1grQ3EvSitjeXIrOCttSHBmdm1jZjVqbE5pVFdvWCs2di9FY0g2dzdqc3A0NjM1amN3R0s0T1JaZ1Ntem95M0ZwQ1Jya3M2OVRLVU5ubGUxWndCVDhpNmZyd3hGUjRTTVpMMjBZMTk5UktjQWpIczBiRytCNFBJNU9aekw0ZHJUZ2dZZE1zeDJQQ3FKSDZCUDVXT0pYZ3EydHJmbDhZY25MN016VWJGV29FY0lsZHB5N1RPVVhhZnJQZDdVNHlUSU90ZnF6Y2U3QlI5UEwxRVBrK1laMjdlMlN2ZnlQeWFhTGpmWGVUMktydk02SFUvOGpFTFNVTHpsZjhoVnVDQVN0eFJBSFh3TVI5YUcveVV0RCtGamNYVmZRNVJjRGZSMXcyY01VK3NuTURiQ1FwV0JvNnkva2tzYkxiM09sdVFiZ25qU1hmRVUxRERGRVlLeCsrRVg4UDdHSGNIRzdoSTBKUEZRSGN0TjIyejl2V3hOSGZ0NWM5Y25RTC9Ic3M1TmdzOGtRUkJZWURWam04REhWWW0iLCJtYWMiOiJhNGMxMDdmYzlkN2NjZjQ0ZGUxNmU2ZDg5NDFjMWQ4NjIyZGVlOGViYzdlMGIxZTRhZDI0NWZhYjNjNDdlOTZiIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBuTVlUTnFNYlNSY2t0TzVBTXZWK0E9PSIsInZhbHVlIjoiMFBxSjdReHQ5aGZWL0JZNEhBZnFrZGxxRUNKekxmSGRncElOSXJXeEhhbWovZ2ZOTmJEMkticVBGVkVCcitWVmJPN0dUUlMzaWhJR2hBSEgwTWNKZ3N0M0ovQXB3dXZlU2gzeWM1K2VQMyt3THk0MVVjM0h1a3prQlk0ZXY4Nnd5Nyt4QWxnNzJ4d3BqVmhsRlVnWXQ0c0E4TWthUURubXNONHZRazJsMXpuU0c2Q0t5eHkrMzlra0pnbEJ4UTllUFlQNmE4M3hEQ20yOTBKV3pJNVlBVy96ZSswWmZtTmZLVThCVERpdjM1NXJtd0FYb3lVL1U4aW1zMmx6M09RMVFiL1hnelJsY0VTMkZhbkN2V3dKNDZCVnBvOWs5UG1wcUlTNWNwZ0VuQWY4MFBuU3FONUxQSUVGNFJ6UTEwRG9YZWc0OFoxdzRtd2NINXV4TEYrcU5pdEc1QXBhTE1TQVRWRzZ5UVJtd0F4dTVnMXNTMmZ1ZGptVFAvOGZLRFFpU0NESDhmbGtwbUxUVlFsMmloa0xsaWU2OU9xbmRKSUJnMWRGaU51Z0hhaWh6djRpTCtheElSWGdhd0tPRXNMZkROS2NSQ0lFZ2xSYkFmR042Q1ZlRzhOalRCRFd0SmNkRnI5UkRxNDBpMzRDS3NEUU1QK2NZTXVWZk8xL0Y5K2giLCJtYWMiOiIxYTFkZDRiY2U1MzcwN2ZlYzcxODg1ZTAzNmQwZWU5ZmFiM2EzYjA5NDlmYTlhNTFlMzkyODJiNmNjZjRhNWE0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkUrWU8vVlY5TDFTejRuNWNVWC9la1E9PSIsInZhbHVlIjoieDdnU3NROXhRSUVWU2tNNXlSOTlkVyt4TytFVnNrdjlUWnlvSU1hbGFmaXZwZUpXV29xaVF4RDVMMVNuemR6aTNQOUNVc0Fna0ZPT05XampVek1HYkxSV1BSS01HSitadThTTjk0NHowMm5GQ1grQ3EvSitjeXIrOCttSHBmdm1jZjVqbE5pVFdvWCs2di9FY0g2dzdqc3A0NjM1amN3R0s0T1JaZ1Ntem95M0ZwQ1Jya3M2OVRLVU5ubGUxWndCVDhpNmZyd3hGUjRTTVpMMjBZMTk5UktjQWpIczBiRytCNFBJNU9aekw0ZHJUZ2dZZE1zeDJQQ3FKSDZCUDVXT0pYZ3EydHJmbDhZY25MN016VWJGV29FY0lsZHB5N1RPVVhhZnJQZDdVNHlUSU90ZnF6Y2U3QlI5UEwxRVBrK1laMjdlMlN2ZnlQeWFhTGpmWGVUMktydk02SFUvOGpFTFNVTHpsZjhoVnVDQVN0eFJBSFh3TVI5YUcveVV0RCtGamNYVmZRNVJjRGZSMXcyY01VK3NuTURiQ1FwV0JvNnkva2tzYkxiM09sdVFiZ25qU1hmRVUxRERGRVlLeCsrRVg4UDdHSGNIRzdoSTBKUEZRSGN0TjIyejl2V3hOSGZ0NWM5Y25RTC9Ic3M1TmdzOGtRUkJZWURWam04REhWWW0iLCJtYWMiOiJhNGMxMDdmYzlkN2NjZjQ0ZGUxNmU2ZDg5NDFjMWQ4NjIyZGVlOGViYzdlMGIxZTRhZDI0NWZhYjNjNDdlOTZiIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-16512986\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-4457598 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-4457598\", {\"maxDepth\":0})</script>\n"}}