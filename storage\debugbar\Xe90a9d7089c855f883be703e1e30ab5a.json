{"__meta": {"id": "Xe90a9d7089c855f883be703e1e30ab5a", "datetime": "2025-06-30 23:10:22", "utime": **********.808268, "method": "GET", "uri": "/pos-financial-record/opening-balance", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[23:10:22] LOG.info: Opening Balance Request Started {\n    \"user_id\": 22,\n    \"warehouse_id\": 8,\n    \"is_sale_session_new\": 1,\n    \"has_manage_pos_permission\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.795979, "xdebug_link": null, "collector": "log"}, {"message": "[23:10:22] LOG.info: Returning opening balance view", "message_html": null, "is_string": false, "label": "info", "time": **********.796942, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.37358, "end": **********.808285, "duration": 0.4347050189971924, "duration_str": "435ms", "measures": [{"label": "Booting", "start": **********.37358, "relative_start": 0, "end": **********.729922, "relative_end": **********.729922, "duration": 0.356342077255249, "duration_str": "356ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.72993, "relative_start": 0.3563499450683594, "end": **********.808287, "relative_end": 1.9073486328125e-06, "duration": 0.07835698127746582, "duration_str": "78.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52089392, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.financial_record.opening-balance", "param_count": null, "params": [], "start": **********.803755, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\resources\\views/pos/financial_record/opening-balance.blade.phppos.financial_record.opening-balance", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fresources%2Fviews%2Fpos%2Ffinancial_record%2Fopening-balance.blade.php&line=1", "ajax": false, "filename": "opening-balance.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.financial_record.opening-balance"}]}, "route": {"uri": "GET pos-financial-record/opening-balance", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@opinningBalace", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=263\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:263-324</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.003939999999999999, "accumulated_duration_str": "3.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.764689, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.898}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.775207, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.898, "width_percent": 11.168}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.789556, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 71.066, "width_percent": 19.543}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.791796, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 90.609, "width_percent": 9.391}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1625311157 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1625311157\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.795322, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1908317033 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1908317033\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.796858, "xdebug_link": null}]}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/pos-financial-record/opening-balance", "status_code": "<pre class=sf-dump id=sf-dump-186554928 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-186554928\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1909725055 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1909725055\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2127900533 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2127900533\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1720539475 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751325020724%7C12%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhxRXpoVHVSNUlIdjNWVVl1Y3J6VkE9PSIsInZhbHVlIjoiZlgrMFk1Sk5xbEZNa0w1SXM3WlN1S0N6MEZGNHEya2FMU0MvVUdKb044REFWVG45czRTaTRxV1BDL1VwV1EvT21tMkFLelZMeGdweXpMNmpaOGpZdGlhTlNtYjVjbG9KREh0RlZBS3IzTVErZEl6dzg1b1pWeHBpVzFtSnpVdmswRk04M3h2V2RZckEzbW5FcmtrTU50M09GV0ZSaGdaT0I3Y3Zub1pRWFhTa1pNaFhjT2RsTHk2b2t4dVlDc0NQR25OWXJRREw5WCtHZWJKc1Ywd01ZR0FPMlgvck5YcU1MM0Y5bDY3YVZzVEQ2ZHRFVllmTlFpenB3ME5TZWhTR0VvaFVnTk1QS1NhUGxnUUFWck9yS0RvTTVxK1lyY3pRcVM4SUo1ZHlOOU5Zc0lIVldqajNBZUU2OHQ2aWlabWljVVJZcG9sNElQYS9BWVhFckRsbEF6cU1GOSt0ckdqT0c2dnQ3dFAyOHhpaTRvU2JsT2NlYVBKaWhLdWZkZ2J2N1g0Ui9XN3RUTzNOUDFXeGxtOFNKQTlGQWswSE9icVJUeFlVOVRaN1o5NFppNkVvazJhdGlKOTFBN0cxcmZqVXc3WS9sMjBxS1B0YS9DTWdOQTlGeGNPeGRtU1RQOHVFSzV4Q040MmJHeWx1SVU2YjVHRUMzc3NtVHpGbkNiUVoiLCJtYWMiOiI5MGFiMDM3NmM4NGVlYzRjZGI1MTczZmU3M2VlZDFhYjY3OWJhN2I4NjkzN2VmYTUwY2Y4ZTlmOTgxZjUyYzMwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlJlU25aYjBXU0ZQcklOMjFrY0pDNkE9PSIsInZhbHVlIjoiaWpKa3NWNE9nU1dBcFd3Q002MC80TndmNFVLaGJpODB6MkxTWE51dmF1cVJ1dWwwS1VUL2h0Tm5qK1VLMHZHT0YzQklLZFNidDlyclQxK0NBc0VoL2YyelZibVlNZlNFNzN0Ni9hWXlZS2RYdGN0YXJkd21MVEFhMkd6WndjanV0bSt3THdRVG9ab2xLam9tYnFNNVMzNzdYSlhkNnJ6NFlOQlZXQldjeUwwK29pNXQ3Y3VuOVA0MGQ5S0x4ZXQvMktudTVFeGFURWkzQ1c3TWYreFpSeFBhaXdJREphTllRNVA0RHdkMGpLTVo5ek83R1dEN3FSZ3BYeVFFVXlPeUxMUFhJekZhM0lSc0lubEpFdzhLWnVkZUdUelQ2bTNzakJRVng3OW9Jdm8rdTR5VGUwWWhUcWVTU3ZqcHRxN1lmR0RpWVc3cUNmV0ZzdkxBbHp2ajhTQ3FqSlVKNW56eTBvUC91VUwyM3pROWtOdUMwb1pIem1YY0JmalhUc3lsOXB3bndpVUV5YWdKNWk1MzRtQ0Nla1BUdG9WSFN6aFB2RjBudy9QWEUvcHVQVU9PMXh2UFBEZUExQmhHZS9WdUx0YXZSVEcwOGdvNUtnQmJJbEk5eWJsZiszdmwyUFhMOWVQUWMrckk1Z3pkNFBPcEFKazN6ejZOcUVuU2hUbUkiLCJtYWMiOiJhNjJhZDVlYTdlMmE0YzkzZmI0ZDFlZjI2ODA4ZjA2ZmU4OWVhNmQwOWRkYzcwYTUyYTA2NzFlMjQxZjg5NmYxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1720539475\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-429822716 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-429822716\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1819825880 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:10:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBZRmNVeVVmbzBMR3dkc3Z5U1F4OVE9PSIsInZhbHVlIjoiZUgyMmZhd0c5bFl3VHlwSHVKbjJoTDdLcnhoek9XSHZUUEtwSTdYV1h5cjZRWWVGM0tLQm8zWFZtRitnVzJXai9SY0VtVG9ldjJQa2V1RysxM2RHKysyM0ZwNndlaVFSNFo5emk3dmtBREFLaXc1a3Z1LzA2a1BvL1Q4cVRVRlV3TFMrWWhqZjg1OXQzWnRiS3ZoVkc2bEJmZGt3cUxkMzI4Q2hoYXJBZC9oZEVqbHdEVTN2dWxSWlg5c2x2ZG1VNzFNa3pnQ2pRQy9BVFRVdHBVVlhhWFB5MkNwcGlUNzJxRTZXUWdiWldVQWsxNkt0ODRrN1lBSFVMeGhHOHlHdWNaSzVYbnNrVmw0Ti9ZMTZyV2g1ME5pQUN3aVpsOHh2VjUzVDRTQWpDN1YydkhTcTlvcGZraXVuRG9abHRsWGI0Vm95M2p1V29HM1JYeWg1QzBzRHFyQ3FiQzdWbkZPSitWYU13dnA1aXNzdWFYQml5T1JTcDhPRVdFVmI3c1dzQ1N6Y1ZHaVR0ZitCYk1zeXlBRmx5WjNuM2ZOcmFUT09JTXJYdlA4cTQzREE2cmJNanRJV0Q5c05ZSHdoUEZhdlZKM3hOZEs0cFIrQ1psWnpsZ3BLZHUyL0V4M2VxTmVKcWVTeUVBb093UGNiSkFQamFBZzZ2aWR1SmMwMFJ3SEQiLCJtYWMiOiI2Zjk5M2FkY2NlMzA5NWNjMTIwNGYyOThiZGQxNGNlNjRhODZjYzlhM2JhODk1ZDk2MWVmMjdhZDIxYzcyZWNjIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlpIU1lIWnNPb1Q1cHVtOVJhYUpIYWc9PSIsInZhbHVlIjoiYWdmeGl1ZXlWMHF4clhnMGM3T0pvVTdrL0hnOHpFVVFvVHlaNXA5YXZxdHFmN1R5UlNkNUpxS0NkVGRLRUdDMjNzZ291T0R0N1doWnhubmNRQm54VnZOS1ZlMDFyY3Zld3p5UkgyL1hKVWpENjZiTGlKbjFPQmNJN0JnMHdZZzNMODVoUWo5VFkrOWZML2VTTFFTb0xNbTdWNlRIZHdWcStXYnd4anRrK2h4SFJFcHpEaFZLU0k4SU9iZDhZZXJPVUs1ZjN1Z0RPWTQxaDFCNlBuT0t0SDFkcHlHUC8wTzRIaFYybmhscWxTK3hXVVhUeVROSlFBS01RV2RtbTlxcUlZVlB4eEFleCsxbUFMSUV1MktadzJiT2hRQ2p5NlBML2I2Q1NvREM0dkQ0NVVtMDFrZzduRGszUkd1elpETC9Zak8xeVI4Z3d3R0tiS01KT3JkTllxaUFVd3FWelRFTlVpa1lGQzg2bDI5UzJDUWR5dTRTNG5LZVBYSjVNSmtPNlZwMXBMZ3hmQm8rTUtGSGhCNmZZekpLM2NOMGNJemZFNEhvRk5rYzJXTlRhOFZxSFhlZlQyL0p3dG9aMjNZRHY4NWtaRlhoR1k1S0NORStmT2xjd2RxS2lqNFAxUEJObWRWa3hpbXQ2cW9XZi85RkdMMnlmSmo3b0F5R3U1QlQiLCJtYWMiOiI2NGI1OTFkOWFkZTczOWFiNzQyZmQ0MDkzODgxYzgzOGJiNThjODc5YWU3YmEzN2ExZDNkZTFlMTQzNGNlNzgzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBZRmNVeVVmbzBMR3dkc3Z5U1F4OVE9PSIsInZhbHVlIjoiZUgyMmZhd0c5bFl3VHlwSHVKbjJoTDdLcnhoek9XSHZUUEtwSTdYV1h5cjZRWWVGM0tLQm8zWFZtRitnVzJXai9SY0VtVG9ldjJQa2V1RysxM2RHKysyM0ZwNndlaVFSNFo5emk3dmtBREFLaXc1a3Z1LzA2a1BvL1Q4cVRVRlV3TFMrWWhqZjg1OXQzWnRiS3ZoVkc2bEJmZGt3cUxkMzI4Q2hoYXJBZC9oZEVqbHdEVTN2dWxSWlg5c2x2ZG1VNzFNa3pnQ2pRQy9BVFRVdHBVVlhhWFB5MkNwcGlUNzJxRTZXUWdiWldVQWsxNkt0ODRrN1lBSFVMeGhHOHlHdWNaSzVYbnNrVmw0Ti9ZMTZyV2g1ME5pQUN3aVpsOHh2VjUzVDRTQWpDN1YydkhTcTlvcGZraXVuRG9abHRsWGI0Vm95M2p1V29HM1JYeWg1QzBzRHFyQ3FiQzdWbkZPSitWYU13dnA1aXNzdWFYQml5T1JTcDhPRVdFVmI3c1dzQ1N6Y1ZHaVR0ZitCYk1zeXlBRmx5WjNuM2ZOcmFUT09JTXJYdlA4cTQzREE2cmJNanRJV0Q5c05ZSHdoUEZhdlZKM3hOZEs0cFIrQ1psWnpsZ3BLZHUyL0V4M2VxTmVKcWVTeUVBb093UGNiSkFQamFBZzZ2aWR1SmMwMFJ3SEQiLCJtYWMiOiI2Zjk5M2FkY2NlMzA5NWNjMTIwNGYyOThiZGQxNGNlNjRhODZjYzlhM2JhODk1ZDk2MWVmMjdhZDIxYzcyZWNjIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlpIU1lIWnNPb1Q1cHVtOVJhYUpIYWc9PSIsInZhbHVlIjoiYWdmeGl1ZXlWMHF4clhnMGM3T0pvVTdrL0hnOHpFVVFvVHlaNXA5YXZxdHFmN1R5UlNkNUpxS0NkVGRLRUdDMjNzZ291T0R0N1doWnhubmNRQm54VnZOS1ZlMDFyY3Zld3p5UkgyL1hKVWpENjZiTGlKbjFPQmNJN0JnMHdZZzNMODVoUWo5VFkrOWZML2VTTFFTb0xNbTdWNlRIZHdWcStXYnd4anRrK2h4SFJFcHpEaFZLU0k4SU9iZDhZZXJPVUs1ZjN1Z0RPWTQxaDFCNlBuT0t0SDFkcHlHUC8wTzRIaFYybmhscWxTK3hXVVhUeVROSlFBS01RV2RtbTlxcUlZVlB4eEFleCsxbUFMSUV1MktadzJiT2hRQ2p5NlBML2I2Q1NvREM0dkQ0NVVtMDFrZzduRGszUkd1elpETC9Zak8xeVI4Z3d3R0tiS01KT3JkTllxaUFVd3FWelRFTlVpa1lGQzg2bDI5UzJDUWR5dTRTNG5LZVBYSjVNSmtPNlZwMXBMZ3hmQm8rTUtGSGhCNmZZekpLM2NOMGNJemZFNEhvRk5rYzJXTlRhOFZxSFhlZlQyL0p3dG9aMjNZRHY4NWtaRlhoR1k1S0NORStmT2xjd2RxS2lqNFAxUEJObWRWa3hpbXQ2cW9XZi85RkdMMnlmSmo3b0F5R3U1QlQiLCJtYWMiOiI2NGI1OTFkOWFkZTczOWFiNzQyZmQ0MDkzODgxYzgzOGJiNThjODc5YWU3YmEzN2ExZDNkZTFlMTQzNGNlNzgzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1819825880\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-88101498 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-88101498\", {\"maxDepth\":0})</script>\n"}}