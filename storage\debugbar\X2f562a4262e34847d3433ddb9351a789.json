{"__meta": {"id": "X2f562a4262e34847d3433ddb9351a789", "datetime": "2025-06-30 22:36:53", "utime": **********.16876, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323012.717692, "end": **********.168784, "duration": 0.451092004776001, "duration_str": "451ms", "measures": [{"label": "Booting", "start": 1751323012.717692, "relative_start": 0, "end": **********.112942, "relative_end": **********.112942, "duration": 0.3952500820159912, "duration_str": "395ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.112951, "relative_start": 0.39525914192199707, "end": **********.168787, "relative_end": 3.0994415283203125e-06, "duration": 0.05583596229553223, "duration_str": "55.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45363392, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00267, "accumulated_duration_str": "2.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.148977, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.918}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.159859, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.918, "width_percent": 21.348}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.162544, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 87.266, "width_percent": 12.734}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1338795000 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1338795000\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-563847983 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-563847983\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-852960442 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-852960442\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=6nrx0y%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1aj6s00%7C1751323011454%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpSazVIR29GTlBHRTRVeUVuVGQvVVE9PSIsInZhbHVlIjoiUzNZVk1DUkZ1eGxIOE8wdytpeWVQeUNDSjZvbmEwemVJdVpxSDZ3enJBUHRIVkIxR1FRekxGcFllOE5xMVpCay8wT1ZhNHdHTEExYUtZUlRLTVJzMmhYMFdCaDdCQWRrRS9nYTB6c2NQRVZmZE5rRWhvK1RhQ0VSaDZscTBVRWNHVU1uajBQeEdUeWx5TjlCQnJwYUxHYjVrTUVXeVFYVW9kaW01UEVXaHRvbEJ1bnVIYy8wcTlUNXBKNGRkVHc1UU9ZdXdleGRLMmtWNnVKemlrRHF3MWppdWxDRG5ydGhkZ09oQ0dpUW93RTBGZUZzZm9lcDRnVEM0dFZTdXMzdVBxVnZ6RmRmR3lyd0NOcHZvN2c1dnBrSHNTL1J0eTFvUHJGQXR0OHQ2TEs4K3djWTFhbkZsaWJ2MXRjZy9oOVNSbkZnaHNZYjNGOFVTUjZmS1RBaWE5NHVwa1VPZ08yTGV5dERZTjlGN05yUnc2MGxobWJKUHhzeVRMSEhUTXVrblp2Smd5VStaZm1uUllLWllLMUpyLzI3aENwRmJhc1dMUE94WkR1VVR2NUhsNGFITEsyY3RFOCtjaUpMZmJoc0FPamE0WnhmeFdQTVY3RGFmL0ZqSDNMblJHOVgvQlNIUXlvWFM3a0RyU2N1UXowRkxWMkw1bUROalB5NkFVeHYiLCJtYWMiOiIzYTlhNTdmZjhkOWVkNzdkZGE3YmEwODA1ZmE0YmE0Y2VkMTZlNjkxY2YxODg4ZDkzOTFiMzA1NmVhODY1YmUzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlpmRUpqVWlUWlArVGtjbXBqdEIrUWc9PSIsInZhbHVlIjoiNEcvbnpHNXc3WC90QU10UStJMVFQQThac0grYVZkUjduZm5xWlg5QjNDbktKZDJ1aCtXNjRDaENOZWw3cWhCTlo5ajV3cHdId0daV3V6dGF4ZUpVekFmb1FjYUdhWnl5L3dRdXRHUmxPc3ZSTGZHSndwTUlSRWpPUEJzYmVydU91MWFHSGFDamN0aEJyK0Q1dUs2RWViVmREelY5YmFtNG0rc1BkREhqU1M2YTNjVnRhWDVmbjhkRHJNMDg1cXc1MUpOcTFrQTBZSW44MkxLNEE1SHVuK1ZMSEtqeklSR09TMnBGTGFCMEt2UjJrSDBmTVFRTk5mNnZFM0QvSEY4TWt6WDRvMVc3dU5VRGJsUFNpZHlIdlNyZ1hxVWhQSWh3OGpEOThNTk1CRmdjbTlkQ0I0a2xBaUt4ckF2YzU1WjdQYmprV3NNUHp4Yy9QV0QvdmJCRDZtbk5xTDRlbEJLUG1ueVRMTS9TVzhYdHRkR2JraUxHdkZPQUd4Z2d1MzlhT3pzNjVEdnRlc2FhajNXL3FCdlJoUzBuUU9XWDc3SWFlZHp6ekJUVzQzV2pHdWFFS3lDTW5mUWpLS1JJeC9vNFVxZ2Z3RTBqYmdhdTM0U2RIODByaks0d2lNOFpzZmZEZzlMVXdZaWQ1YnVBaHZYT3lmTnpiUkNQUytKc0t0VS8iLCJtYWMiOiI4YTE4YjZhY2FjOTJkNmQ0YjA0ODBmNDcxZDY5MTE2NzRjODk2MGY4ZGQwMzk4ZjQyMTgwNjNiMTcyZjA1ZDkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A9rdyCRm7SKpfljuMnVO7IpcgTBMITMjowAdsmJo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1518609283 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:36:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImROMUxudlRIeTIyaDBKVDBPSmd1K2c9PSIsInZhbHVlIjoiV3pVWlE1dVpLaDJOOHh4MFlJczhybFFQMm5lWmNxRlZMQU5QMzRCcmJvQnZ6YUU1TW52KzVMdlZtKzNHOG9lbTJqbFBuQmZmUDNIWEJ2OEtHM2tpSVgxNHUzZ2RkWkkvOUt6YTlWQ3FadGk4WEZzMkcyQTVOb0ZqaURFNUJwUWdUem9KK0JULzhYUi9YemZ3Vndpc2ZGTkNETHVmL3ZDQnplMkZCS2NhSnhsTjdXekhIMEY5bDVzTk4vUWFQNEE2ZkVpVjNSOElXcTVJL1V4U1JRTW9aSUZzcHlySjRNdHQzdm1Fb2wxaGxtVFJTSGwyckNKWGgvR1l0a2Y5eElJYjQzZWM3d0x5Mis0dStQdmVYR01ndmJqRTh2Z0oyOUlqWHkvVGFFUC9EWDdSckpaSDlNQ2orTHJRSzN3WVlrQTNSM1ZZNm5YYnlrM1hXbUNTb2xCa3NOTW4zVWxXMWdxMGtaKzU5aG5xNGQ0VWxILzNDUXdqN01MdWdSMU1samxoWnpHSmsvbkJUM21ialdrZTVzSlE2WklaWXpnT3JleVZHcnhzUXBpR3kyWERnMElHWFBGbVN5U3Z5K1pocjRPV3VIM1RibXF2QldRU20vVE8zYUJpbVBMU29iNStjcFB2OG8ra2FKbEp6SjZRcWw4ckRrclJSQnlzc3RSTXVGUWMiLCJtYWMiOiJmNzNkMGIwNmM2ZTg1ZmViZWJmMDIwMWNjNWYyNDgzN2M0MzFlMzc1NzNhYTVkODU3NGMzYjg0MjA3Zjk4OTZmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik1FbnlyVisxbitvUFdZM0pRNDBXTEE9PSIsInZhbHVlIjoiRnFTWDE1c0RpVU9NMG8wY3p0QkNHNmRPM3VkY1VDKzRkL1BDTTFTY0NGTXB1dExKTkZnc3ZDSXBHaVlIR0NXeHVoRnJ3QUtzeDBkYURBdkRzTFpGMFhMeHhiRldETUN2amhsSmNsaHg2Mi9FeWRudnRQdWtaM1FENDhKWHBPTWIwTmVabFNUSFV5YVQzY3crN3FaZmUySzllWkwvUkhxN3BRT2FBeS8zYVoxS29ZT2lRMnJvQjN2Q0l2WThjYVNHekx6V2dkMFFJTmRDdGFQUFhsT1ZxUFcyck1FSGREcVdCL0NwS1lPek9mQ1ZMQU1Jd3htVjhSRWR3SmI3a1lyRWJmSjVzZ2QxN01RaHY2V29jNmZ4VWZJbXpZU2dJRU5NbU4rNnpDYkVQZ2xtSjl1aHdNMGxjMFBDQmdIbjdwN2FwcDdWK2dGYndQazdJZW0zcWVNanZldjRVQjdCRmV4YXNGeUlEQlhHK09vY0ZCYUV4MjR2ZEE2alBFU3lJMkVLeWdBanhlbXhUN3d6QXdxU1V3bGZTeFQvRXloSUVPUUJ2T1dGa29IYkpNWmw1ajJuMUtyaTVQOWVzTHU2b2kxZk9vSlNPcUZ1bmhjMnVaS3U4UjRXS3hvYTFzaC9nd0g5MVI5cGF2YVF3czlscHJUUVRtdCtpb2VudyswYm5jREQiLCJtYWMiOiI2OTRiNWI4YTZhZjAyY2RkYzNjODNkMzUzNWQ0NmUxYmM2NjNmOGUyZDlkZjgyMDEzNTZmMGY2ZGEyZWQ5OWZjIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImROMUxudlRIeTIyaDBKVDBPSmd1K2c9PSIsInZhbHVlIjoiV3pVWlE1dVpLaDJOOHh4MFlJczhybFFQMm5lWmNxRlZMQU5QMzRCcmJvQnZ6YUU1TW52KzVMdlZtKzNHOG9lbTJqbFBuQmZmUDNIWEJ2OEtHM2tpSVgxNHUzZ2RkWkkvOUt6YTlWQ3FadGk4WEZzMkcyQTVOb0ZqaURFNUJwUWdUem9KK0JULzhYUi9YemZ3Vndpc2ZGTkNETHVmL3ZDQnplMkZCS2NhSnhsTjdXekhIMEY5bDVzTk4vUWFQNEE2ZkVpVjNSOElXcTVJL1V4U1JRTW9aSUZzcHlySjRNdHQzdm1Fb2wxaGxtVFJTSGwyckNKWGgvR1l0a2Y5eElJYjQzZWM3d0x5Mis0dStQdmVYR01ndmJqRTh2Z0oyOUlqWHkvVGFFUC9EWDdSckpaSDlNQ2orTHJRSzN3WVlrQTNSM1ZZNm5YYnlrM1hXbUNTb2xCa3NOTW4zVWxXMWdxMGtaKzU5aG5xNGQ0VWxILzNDUXdqN01MdWdSMU1samxoWnpHSmsvbkJUM21ialdrZTVzSlE2WklaWXpnT3JleVZHcnhzUXBpR3kyWERnMElHWFBGbVN5U3Z5K1pocjRPV3VIM1RibXF2QldRU20vVE8zYUJpbVBMU29iNStjcFB2OG8ra2FKbEp6SjZRcWw4ckRrclJSQnlzc3RSTXVGUWMiLCJtYWMiOiJmNzNkMGIwNmM2ZTg1ZmViZWJmMDIwMWNjNWYyNDgzN2M0MzFlMzc1NzNhYTVkODU3NGMzYjg0MjA3Zjk4OTZmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik1FbnlyVisxbitvUFdZM0pRNDBXTEE9PSIsInZhbHVlIjoiRnFTWDE1c0RpVU9NMG8wY3p0QkNHNmRPM3VkY1VDKzRkL1BDTTFTY0NGTXB1dExKTkZnc3ZDSXBHaVlIR0NXeHVoRnJ3QUtzeDBkYURBdkRzTFpGMFhMeHhiRldETUN2amhsSmNsaHg2Mi9FeWRudnRQdWtaM1FENDhKWHBPTWIwTmVabFNUSFV5YVQzY3crN3FaZmUySzllWkwvUkhxN3BRT2FBeS8zYVoxS29ZT2lRMnJvQjN2Q0l2WThjYVNHekx6V2dkMFFJTmRDdGFQUFhsT1ZxUFcyck1FSGREcVdCL0NwS1lPek9mQ1ZMQU1Jd3htVjhSRWR3SmI3a1lyRWJmSjVzZ2QxN01RaHY2V29jNmZ4VWZJbXpZU2dJRU5NbU4rNnpDYkVQZ2xtSjl1aHdNMGxjMFBDQmdIbjdwN2FwcDdWK2dGYndQazdJZW0zcWVNanZldjRVQjdCRmV4YXNGeUlEQlhHK09vY0ZCYUV4MjR2ZEE2alBFU3lJMkVLeWdBanhlbXhUN3d6QXdxU1V3bGZTeFQvRXloSUVPUUJ2T1dGa29IYkpNWmw1ajJuMUtyaTVQOWVzTHU2b2kxZk9vSlNPcUZ1bmhjMnVaS3U4UjRXS3hvYTFzaC9nd0g5MVI5cGF2YVF3czlscHJUUVRtdCtpb2VudyswYm5jREQiLCJtYWMiOiI2OTRiNWI4YTZhZjAyY2RkYzNjODNkMzUzNWQ0NmUxYmM2NjNmOGUyZDlkZjgyMDEzNTZmMGY2ZGEyZWQ5OWZjIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1518609283\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1447194972 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1447194972\", {\"maxDepth\":0})</script>\n"}}