{"__meta": {"id": "Xd4a4068799c8770233a0a4d9ce826c42", "datetime": "2025-06-30 22:43:51", "utime": **********.598609, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.129405, "end": **********.598624, "duration": 0.4692189693450928, "duration_str": "469ms", "measures": [{"label": "Booting", "start": **********.129405, "relative_start": 0, "end": **********.544508, "relative_end": **********.544508, "duration": 0.4151029586791992, "duration_str": "415ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.544522, "relative_start": 0.4151170253753662, "end": **********.598626, "relative_end": 1.9073486328125e-06, "duration": 0.054103851318359375, "duration_str": "54.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43872952, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00232, "accumulated_duration_str": "2.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.585818, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.741}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.591244, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 79.741, "width_percent": 20.259}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2352 => array:9 [\n    \"name\" => \"مجسم شخصيات مختلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"2352\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-2016063223 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2016063223\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1122998983 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1122998983\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323255132%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBVSWhmcXd6by9LclJQaTBQNXgxelE9PSIsInZhbHVlIjoibVR6ZVVFZHgxQk5TUUtLbzV6N2ppWVY2dmViUEsrYklBWnNrcFNWVHZYUkFjS2hqbHBhZ244aEVQS1VFTHNlV1RVWWEyV09tQUVJOEg4aElnbnF4L3lhc3FsdFhwSnRnY054M3ZKbWREYnNDbGlpRzFudlVRckhIVFZVcFpDY0wyMW4zRkhlNUIwQnl1TGV6enZDUXkvZm9odElWVHRubUFORmFvV1RaUXIyYmFia0YzODRrcktLMnF1L0pGSk92R3BOWnFSdldZVmdIMzdrZVcrUzloSkNJcTFvM1ZyN1NaZDR0WWdVenhqSXorcVIwM3ZYTFNITVd3MmV5VGRFaW5uZWNvcTlWQnk4VWNBQitVSlhyMXFGbjU1eEVYRm00TjFLQWZjZk9paHdlaGVITkZWR0Y4Z2FodVhxbTYrQzR6dDJaeGZ0cldhOWhkZ2Z4ckgzbnNzUEp6Wmx2dHhSTFNITFFTY0xHQmtObVNyUVc2L3phTUxvanNoYWNGM3g2VTNacnVJb2NrTWY3dWdTbjNId0JkRUtPVFNJVk5ySFRrYXlaNUVqbWNYamxUYVI5RUdIWUdLeWhnY2J2eHF0QzI0RTZjT2NRWmlpZ0F5d2pSVjhsTGRvLzdzc3U1bk11OXNXeG13LzVRQzdkTlNvOHMzZXpVOG1ONFpuUlV2ZEIiLCJtYWMiOiI0Y2Y3MTY2MzkyOTk5YzM4MTliYjE4MzA3NmY5NmZhNWQ4NzZlZDY4MWJlM2NmYTVkYTY0OWZjODgwMTE5ZjdjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InZYWTFmdjkydnY2eEFHY1BFMWFkQkE9PSIsInZhbHVlIjoidGRsYXdwZGJIdnBvTFpteGxpOWJvL0JHRitzenZqYVU0dyt4dmc3N0RGKzZiUzN0Y3BSTzFGTmRvVVNMdXJ3SWpmV0Z2N0d6UlV4RCtJS2lhaXl0eWdNbHFDV01JcVA2Mmhac0RYNCt3eU5OUVkyVkZTbE15U2RtWVUwdTQrRU9NVVZjOXg5UW5pbVR5QzBOeHd2WW51R1dFZ3kreW5aSFdoQlB6WXZXeFM2LzRXWXJKdnlrYmJEczZyOFhJRlF0K01jMTRmVGM4Tm1RL011V1NBZWswd1laN1NPOEdMREFHVG51SVI2akNoN1pKV1BwTXJaR28wakFxQisyR1lFV3BKeEtpNldhMFZ6RjBQa0x6WEU2V1JVUmk4ZFJOQlBnN3grQ2dWeXlZd2V0SHMrVzl3MVVNRE9NVUE5NGlYTEVoVXFUUWluTUpocXdwclJjTHNsMUpTejJDc2s3VWZwRk1PQ0hNdy9XanNVVzM0YTlnZjViZmNqZDlwWVh3WFIvc2xYK2h4QjRqdzdxWnJhUk84Y1NiOXM0SlAyRVE4Y1IrY1VuT3FKZWllK2x1UkRWNUphNU1MNlNKM0hjMXgrbkxWUEQ4WnExOGYzNkpEOWU1bWJMSzk1Lzc0Nzk5VlZRd3RTUUFPcTA2NTFnR1djR1llRzBoeFRQTEl5MGEraWUiLCJtYWMiOiJmNDM0ODc2NjJjMzkwZmUxZDg2MTYwNDk1MWVhNjg0OTdmNjUzMjFhZGYzYjYxYmZiOTQ4YzY4MzViM2E2YTkxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2049945809 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2049945809\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-947462610 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:43:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJUV0xEb1FjYy9HV3dCWXcxMWRJMmc9PSIsInZhbHVlIjoiWXAwOUNIQTMwdGJPUi9jM3ZEQzl4MEtxTncvWnRGaXFrSzBCT1V6dEg2dkJXQVZpL01FaXlWUzVNNVVrUlpuMnA5QkpGOW0vWkc2M0dhSk82Y1NVWVg3ZVovaGlaMHhramNobFJEVTZXcEJ5aDlxTEtsL1g2L2Zkb1hYMFlodkhnQjNrTXp0Wm5jS0FLMXJsakUxektnY2pIU2lsL0Rzc2RkNGJFb2dQTVNRSnU2REpaOHZLajhXRnJCSlVrWmpMc2hUcU1zR29jZkRZV3QwSmFNdkhqYmNNK3pBcjFodXZ1UGlGVGRIUmF0SjhLRm1VeWY1REw3YUs4RTB5ZGFKTVZmZ001Vks1RmJaMUVoQ3ZRVVNqbVI5WHllUDBDOHJPM2V0MkVlSVE2N0pKekdxcjFJS1pyMFB2WkpNUzE4YU03TEhlcVRielhlOG91d2JMajlTQnphQ1ppdkRXQ1NlYVVpa0xPc3crUnRTdFkwYzJ5RFF0RlRycE9ucnkzaVJpV0t2d29sNDdkVXNiR1lHclA3aTVYTzZJdzhEZXZjZ3Z4anA1cFhnbEVCZ2xTZTZKY00xRGdJWEpiM0JjNmFCcXN0T1NBZ1JLeUJ6WnRiTkw0WlAxNGYyQy93Z3Y1NDVlWlNEQmNNUmV2ZHFVNHYydzRhUnBobzFzQWUvVnNpcFgiLCJtYWMiOiJiYWQ1ZGMyZGY0ZGM3MjljN2M1ZDE4ODRkMWVhYmI0ZTNlZjc5ODMzMjg3YmM3NTE3ZjQ0MTdkYWU1ZDM1MTY0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:43:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkRpUVFJeTRVWXJIK281Q2djV3BlcFE9PSIsInZhbHVlIjoid2wvVFltL09ZZG0vK1U2bEVsbVh4b3NtTjNsTG83K2hxRy85LzUzK2poamlYV0F4aDNhMkkyVnVpOVptSi9UbUJJbWV3UldCQjJzSEg3WFk0bFBJbGJCYVFybHB6bTZPQU9Ib3B0MElDdXVscERFNnlPMjlia2EyNFNWR3dSQjBtQmUzSG04RjB5UDRlMGpCeVdFbS8zUnliUndBakFNZ2lxR2FIUWtSdHdBWm00ckdGQ2c1ai84dTZsSkFGRFZ3WWRyU0FRN21ZclJkZythVy9HV0ZwU0NoYU9pUzRtTjZjWjZ6RmR3Y1JzSkFPQjNzQjFXNGp2NUpDcjV0cFV6eEppMVFlcHBueGZFVkY0TEY0TlU5ZkJGcmNPYzRtWmFzdExBblhBVysySzBDRmxRRmlUZmFSYytMSTVsVzRKWFNzQXlMajN3Nml1NjFJdWpBcEZnSXBBWDVLSSs1cGx3eEtvVFRCTHlzcUR3S0dDVnh0Q3lCRlBINkYzM0pjWklFN1hRNHJPL0xoTE1BNmtXNkVXMEpYZ2J3b3NBaFpXT2IrRGJMWlN6Y3owNXp0a3huNkQrdTBUbklweEw2bTJjZnFqRXBDWHZWbGtlUm5JWkh5SzdKWHZsM2lKZWlzQVZKQnpOb1ZaUTQxZU9GU1RJaDJmMk5XWGNhUXVRZ3ZzNWYiLCJtYWMiOiI3NmU3ZGY2Y2FjOWUxODNjNzY5ZGY5Yjc1YjM2NzdjYzAwMGU4ZDYyMWEwZWNkZWVkMTllYmY4M2I1ODY1ZGMwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:43:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJUV0xEb1FjYy9HV3dCWXcxMWRJMmc9PSIsInZhbHVlIjoiWXAwOUNIQTMwdGJPUi9jM3ZEQzl4MEtxTncvWnRGaXFrSzBCT1V6dEg2dkJXQVZpL01FaXlWUzVNNVVrUlpuMnA5QkpGOW0vWkc2M0dhSk82Y1NVWVg3ZVovaGlaMHhramNobFJEVTZXcEJ5aDlxTEtsL1g2L2Zkb1hYMFlodkhnQjNrTXp0Wm5jS0FLMXJsakUxektnY2pIU2lsL0Rzc2RkNGJFb2dQTVNRSnU2REpaOHZLajhXRnJCSlVrWmpMc2hUcU1zR29jZkRZV3QwSmFNdkhqYmNNK3pBcjFodXZ1UGlGVGRIUmF0SjhLRm1VeWY1REw3YUs4RTB5ZGFKTVZmZ001Vks1RmJaMUVoQ3ZRVVNqbVI5WHllUDBDOHJPM2V0MkVlSVE2N0pKekdxcjFJS1pyMFB2WkpNUzE4YU03TEhlcVRielhlOG91d2JMajlTQnphQ1ppdkRXQ1NlYVVpa0xPc3crUnRTdFkwYzJ5RFF0RlRycE9ucnkzaVJpV0t2d29sNDdkVXNiR1lHclA3aTVYTzZJdzhEZXZjZ3Z4anA1cFhnbEVCZ2xTZTZKY00xRGdJWEpiM0JjNmFCcXN0T1NBZ1JLeUJ6WnRiTkw0WlAxNGYyQy93Z3Y1NDVlWlNEQmNNUmV2ZHFVNHYydzRhUnBobzFzQWUvVnNpcFgiLCJtYWMiOiJiYWQ1ZGMyZGY0ZGM3MjljN2M1ZDE4ODRkMWVhYmI0ZTNlZjc5ODMzMjg3YmM3NTE3ZjQ0MTdkYWU1ZDM1MTY0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:43:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkRpUVFJeTRVWXJIK281Q2djV3BlcFE9PSIsInZhbHVlIjoid2wvVFltL09ZZG0vK1U2bEVsbVh4b3NtTjNsTG83K2hxRy85LzUzK2poamlYV0F4aDNhMkkyVnVpOVptSi9UbUJJbWV3UldCQjJzSEg3WFk0bFBJbGJCYVFybHB6bTZPQU9Ib3B0MElDdXVscERFNnlPMjlia2EyNFNWR3dSQjBtQmUzSG04RjB5UDRlMGpCeVdFbS8zUnliUndBakFNZ2lxR2FIUWtSdHdBWm00ckdGQ2c1ai84dTZsSkFGRFZ3WWRyU0FRN21ZclJkZythVy9HV0ZwU0NoYU9pUzRtTjZjWjZ6RmR3Y1JzSkFPQjNzQjFXNGp2NUpDcjV0cFV6eEppMVFlcHBueGZFVkY0TEY0TlU5ZkJGcmNPYzRtWmFzdExBblhBVysySzBDRmxRRmlUZmFSYytMSTVsVzRKWFNzQXlMajN3Nml1NjFJdWpBcEZnSXBBWDVLSSs1cGx3eEtvVFRCTHlzcUR3S0dDVnh0Q3lCRlBINkYzM0pjWklFN1hRNHJPL0xoTE1BNmtXNkVXMEpYZ2J3b3NBaFpXT2IrRGJMWlN6Y3owNXp0a3huNkQrdTBUbklweEw2bTJjZnFqRXBDWHZWbGtlUm5JWkh5SzdKWHZsM2lKZWlzQVZKQnpOb1ZaUTQxZU9GU1RJaDJmMk5XWGNhUXVRZ3ZzNWYiLCJtYWMiOiI3NmU3ZGY2Y2FjOWUxODNjNzY5ZGY5Yjc1YjM2NzdjYzAwMGU4ZDYyMWEwZWNkZWVkMTllYmY4M2I1ODY1ZGMwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:43:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-947462610\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1109326226 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2352</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1605;&#1580;&#1587;&#1605; &#1588;&#1582;&#1589;&#1610;&#1575;&#1578; &#1605;&#1582;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2352</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1109326226\", {\"maxDepth\":0})</script>\n"}}