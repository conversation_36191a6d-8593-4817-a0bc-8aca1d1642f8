{"__meta": {"id": "X2d757f84cae5e79e70d9c0f447bf4f31", "datetime": "2025-06-30 23:10:15", "utime": **********.900256, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.297081, "end": **********.900273, "duration": 0.6031920909881592, "duration_str": "603ms", "measures": [{"label": "Booting", "start": **********.297081, "relative_start": 0, "end": **********.786096, "relative_end": **********.786096, "duration": 0.4890151023864746, "duration_str": "489ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.786107, "relative_start": 0.4890260696411133, "end": **********.900275, "relative_end": 1.9073486328125e-06, "duration": 0.11416792869567871, "duration_str": "114ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45709352, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022619999999999998, "accumulated_duration_str": "22.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.828705, "duration": 0.02085, "duration_str": "20.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.175}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8771582, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.175, "width_percent": 3.537}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8880482, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.712, "width_percent": 4.288}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2126668119 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751324907481%7C9%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImY2dDJ3dVVGWWZYcU8xWldHVHl1ZkE9PSIsInZhbHVlIjoiZmEwd2pGcUo4cVMycGhLOXdHN0VXNjBudjJScy9oaFNVcHlSRndqS0V4QUluaHA1U21mbkMyRnZYVTQ0bksvUWFIcjFFWHVHTUJGUmkyY3Bpa3dZdEQxRkdtOCtQOUFDNTJia001cDlWVHloTkp3UC9ML20rRVZMaUpBbWJZcFA5ajBuUnQyWVlyZS8zdVNHK3ZEZXY2TUY4ZjhMVWExSVl3Qis5K2xoT3RMT2FkRmlvU3lLcXJzMEp1cVhZT01Rd1dNaXZvenhtam9vTkFWUmREdFdENDd5YVhULzFYOWJtTkFLUG1FRS9lZnNadmo1SEVPb09hTUlwS2w4bzAraUJ2NW9TOGcwMFc1L3NXZHFTTXFsWW8wa3ByR0tuUlV4bGZaelRPS1ZEZW91NWwyUXN4dC82RzlFL0xDeE8wZU9UVFdIcXo3bDltWENJdFBLWDNrQVljMWFIbUlQR3N5aE9wbU5rdVJhY0VzdkF3Q2RjRkJnV2J5WFZIYzM5OGl4Y3dvenYvY2ZPcytYSk1aa2gxeXZsN3JmV1BkRmRvbmd1VXhUWE11OUtacnh0UVV0ZytONEtmNEcxQ054WlNaVFN3T0tVNE9IOG1odUtVdjM0ZWtXRW9semVTNVZvb3VJZnAwZHl0VHZadHlUenpmZEdLeTQ0cUFabG5ncGh4anEiLCJtYWMiOiIyNzU5NzJlYWIyOTk4MTA2NzE2NzIxZWU2OGJhNmIzZDVlNzE0Y2U4ZWYzYzhlNDhhZGMyZDYyMjAyMjI4ZGViIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii9jazB1czIzcjlPZGY1NTFtSnVPVmc9PSIsInZhbHVlIjoiWks5K3U4NHhyT0ZCYjZ3VjBIMWJxT1pNMHViMTBzS2F1TTlqNEVPcGJzdVo1WXFXejJvckNYNWIwWmF0cmJ6V1hZYk96ZGRjcFFzbS90QzFPM3AxT1ZSa2J3SmQwRnUxdFNQQkNSbFVuNWM1cDEyMFZaUXVuV2grR3ZVNUJZSnBxOUlKeDc0c2ZOaGlKTk1PRHprQjFEMmxDS2xuUXZrMTdKQ1ZhZE5VcWUvV016bDdFeDVmM05ISGRvcHhkSStxakRQeVVQVmdyaC9YL1AyYjJYQmozYVUyUXNjZFBPc0hrbkFhQVZnZ3AxYlJENkg1VFU3YTNkb096R0xTb0w2Zlk0enVQM2diZDRCV1Z0RU0xa05BMmdyTU5qbnptKzF2Q1F6Y0xVTjdLdkwzU3BleTF1RFZjcWRLL0JBd3pFUEp1dlFpeU1udExvRXBhNEEzWElzVXNXMlVKU2ZGRXdPeHlpeEd1UGtoVHc1RGIzNEM2NzF0V25XNWRFdUVSV1FhenQ4R1RJZUdpZ0dhUUtLdk92MjZWOUtvNnMzVlhjMkQ2WnNwVTdkcXNzaUxDRjdIV21VMStwemJyWlpmbHc2ajNRUHhWZ2w1T0ZUZiszZzA2aUFmR2VicEV5bHAzcmgzNDc1cXJuZXZZcnRSUnI4cjVJbndISHpOdlNDTGRiWHUiLCJtYWMiOiIwMDg1MDQzNGE4NmY3YWY0MmEzMDc4ODlhZDRlZDlmYzJmZWYxMjZmZjZlOTc0MWEwZDc1MjljYTczNTBiMDFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2126668119\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1760664256 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1760664256\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-492984615 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:10:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNlTDU0Z1R3M2xXb2wzaXZIUXV3SGc9PSIsInZhbHVlIjoialZVbEhIN3hWMnJ3bXViSHo0cUNDZ2orK2xXY1VISHJHSWo4VXhWd2RnUGkrVCt6RVJpRTVaRisxVzVZaWFTZ0Vub3lCbVVnbkxIdW5qd0VyOUpmY2pVenEwcGk4eDFHWnV1TlFPQSsyOEw2MXEzZmNDYUMvZzlhTFE3S0hMOS9JMzVGM1hwY2JTdUplbjRlZktRaTMrdGRNcElxZ2dadVhGY3ZCYitNaTYxWGhydmxDQVRSUnZUYWUwM3RBakNocUs0OTBaZjRSS01nRE1SZXpyRVExOXBDRzIwOWpTYkNuZk1TNFJZK1dYMWdYNVZUdUJRUHVzdGx5Z09XLzAvdzdkeHozWTI5NFRLdmkvSE1nL3BLWlhMeXVNM3NwVXNtNUFQZ1VRb21NNXRiVWp5Z0xHWHlaUE95TDBuTzEzbjUrTXVtZGpIWUdRcXVrSVY1L0NsZE9rVW9qOHJKSGhROXdCaktjKzNQQjEwUmYrMWdHanFyZzlJcmdUNEhpeXJzcXdFRlUxMjNDNnl1Y3lNeGI1M2JQanlZazg5K2VOamVNeEtxbG50SXAxZml4Nlc2ZGpLdklqRWFqaHdKNkVHUTUrN1lmRW5nTDI4MGZrWGN1ZEdEUmt0WUFmNWI1cEFUTlh4V3NEaUU5N21HMFlvRWdaOUZVb1RYR1Q0N1FmUCsiLCJtYWMiOiI5YzEyMDQ0OTNlNGFlNDVmODZkZWMzYzMxNjg5M2Y4MWRlOGIyYmVmMTU5NzA3NTI4ODcyN2ExMDZlYTZhM2MzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjBHVjkxNGp6b2I3TTJ6M2ZDZkRBcEE9PSIsInZhbHVlIjoiMGV0eE5uSXB5a0Zrd252KzJHV3JHQjNDRlR2M1lzWklNKzNXOTdoTU02WnQ4TE8yazJnSVZCeUpha25SaTJMdkY1RCsxak5VK0trZE9uTkRJSVpjSzVJcHk5NWt0Wi9EMlBVRWJWODYvaEpEWCtrVjlXQzNvVGxHUnB2YVVSOWkwdUJsTG9pUkNCd2ZVMUsyTXhVTDBLSGFjdlhpSkpRN3NqTkVwZlNEM09rZVFZL1pmUEJRMGVsUG5EN1hKN01MQjB4cHh4cHdxQVl5UkNNNFdFNmdpRHczbXBMQlNwemppeUVaVXFPTFpoRXZvbms2R3NBN3Rrc2ExbjB2cHJFdzVFcTl5UGZGUE5iaHFMRWw0M043V2dDRWphbXJQc0JzbUxudFpKOFN5d01uczRHS1hhQ2ZpQjNhU0taUFRKa0txT1QyWEYrWkxNZGlTa0pKb00zV0FETWpNV1NtalBOTmw4NTBGVDFpbEx4MzVQUGxtWFVPTUo4Y0RqZWtyOExiNWVPYWNaMW5rU1R6azYvTEJQRTZsNlQ5OTZKRFlTejJraGh1dFAxQTZhUSt2RlBTYkU1dVYxM1JxWEw3bVg4Q3VmQ0dYK3IydllET0RqQWZFYmRVbUJIaWlldUJiMm52bnZSdTFzSFk4cEl2SVl0cFRJMk1BVXFJRDhlOUpIWDMiLCJtYWMiOiJlMjVhODkwYjdhZTM0OGE2ZGRiMTgwNjJhOGE3N2VhYjRkN2RjNTJhMGRmZDFmZGYxYmZhZTU0MmQxYTU1MWIwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNlTDU0Z1R3M2xXb2wzaXZIUXV3SGc9PSIsInZhbHVlIjoialZVbEhIN3hWMnJ3bXViSHo0cUNDZ2orK2xXY1VISHJHSWo4VXhWd2RnUGkrVCt6RVJpRTVaRisxVzVZaWFTZ0Vub3lCbVVnbkxIdW5qd0VyOUpmY2pVenEwcGk4eDFHWnV1TlFPQSsyOEw2MXEzZmNDYUMvZzlhTFE3S0hMOS9JMzVGM1hwY2JTdUplbjRlZktRaTMrdGRNcElxZ2dadVhGY3ZCYitNaTYxWGhydmxDQVRSUnZUYWUwM3RBakNocUs0OTBaZjRSS01nRE1SZXpyRVExOXBDRzIwOWpTYkNuZk1TNFJZK1dYMWdYNVZUdUJRUHVzdGx5Z09XLzAvdzdkeHozWTI5NFRLdmkvSE1nL3BLWlhMeXVNM3NwVXNtNUFQZ1VRb21NNXRiVWp5Z0xHWHlaUE95TDBuTzEzbjUrTXVtZGpIWUdRcXVrSVY1L0NsZE9rVW9qOHJKSGhROXdCaktjKzNQQjEwUmYrMWdHanFyZzlJcmdUNEhpeXJzcXdFRlUxMjNDNnl1Y3lNeGI1M2JQanlZazg5K2VOamVNeEtxbG50SXAxZml4Nlc2ZGpLdklqRWFqaHdKNkVHUTUrN1lmRW5nTDI4MGZrWGN1ZEdEUmt0WUFmNWI1cEFUTlh4V3NEaUU5N21HMFlvRWdaOUZVb1RYR1Q0N1FmUCsiLCJtYWMiOiI5YzEyMDQ0OTNlNGFlNDVmODZkZWMzYzMxNjg5M2Y4MWRlOGIyYmVmMTU5NzA3NTI4ODcyN2ExMDZlYTZhM2MzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjBHVjkxNGp6b2I3TTJ6M2ZDZkRBcEE9PSIsInZhbHVlIjoiMGV0eE5uSXB5a0Zrd252KzJHV3JHQjNDRlR2M1lzWklNKzNXOTdoTU02WnQ4TE8yazJnSVZCeUpha25SaTJMdkY1RCsxak5VK0trZE9uTkRJSVpjSzVJcHk5NWt0Wi9EMlBVRWJWODYvaEpEWCtrVjlXQzNvVGxHUnB2YVVSOWkwdUJsTG9pUkNCd2ZVMUsyTXhVTDBLSGFjdlhpSkpRN3NqTkVwZlNEM09rZVFZL1pmUEJRMGVsUG5EN1hKN01MQjB4cHh4cHdxQVl5UkNNNFdFNmdpRHczbXBMQlNwemppeUVaVXFPTFpoRXZvbms2R3NBN3Rrc2ExbjB2cHJFdzVFcTl5UGZGUE5iaHFMRWw0M043V2dDRWphbXJQc0JzbUxudFpKOFN5d01uczRHS1hhQ2ZpQjNhU0taUFRKa0txT1QyWEYrWkxNZGlTa0pKb00zV0FETWpNV1NtalBOTmw4NTBGVDFpbEx4MzVQUGxtWFVPTUo4Y0RqZWtyOExiNWVPYWNaMW5rU1R6azYvTEJQRTZsNlQ5OTZKRFlTejJraGh1dFAxQTZhUSt2RlBTYkU1dVYxM1JxWEw3bVg4Q3VmQ0dYK3IydllET0RqQWZFYmRVbUJIaWlldUJiMm52bnZSdTFzSFk4cEl2SVl0cFRJMk1BVXFJRDhlOUpIWDMiLCJtYWMiOiJlMjVhODkwYjdhZTM0OGE2ZGRiMTgwNjJhOGE3N2VhYjRkN2RjNTJhMGRmZDFmZGYxYmZhZTU0MmQxYTU1MWIwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-492984615\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}