<?php
// This file was auto-generated from sdk-root/src/data/dataexchange/2017-07-25/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-07-25', 'endpointPrefix' => 'dataexchange', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS Data Exchange', 'serviceId' => 'DataExchange', 'signatureVersion' => 'v4', 'signingName' => 'dataexchange', 'uid' => 'dataexchange-2017-07-25', ], 'operations' => [ 'CancelJob' => [ 'name' => 'CancelJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/jobs/{JobId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'CancelJobRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateDataSet' => [ 'name' => 'CreateDataSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/data-sets', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateDataSetRequest', ], 'output' => [ 'shape' => 'CreateDataSetResponse', ], 'errors' => [ [ 'shape' => 'ServiceLimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateEventAction' => [ 'name' => 'CreateEventAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/event-actions', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateEventActionRequest', ], 'output' => [ 'shape' => 'CreateEventActionResponse', ], 'errors' => [ [ 'shape' => 'ServiceLimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateJob' => [ 'name' => 'CreateJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/jobs', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateJobRequest', ], 'output' => [ 'shape' => 'CreateJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateRevision' => [ 'name' => 'CreateRevision', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/data-sets/{DataSetId}/revisions', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateRevisionRequest', ], 'output' => [ 'shape' => 'CreateRevisionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteAsset' => [ 'name' => 'DeleteAsset', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/data-sets/{DataSetId}/revisions/{RevisionId}/assets/{AssetId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAssetRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteDataSet' => [ 'name' => 'DeleteDataSet', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/data-sets/{DataSetId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteDataSetRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteEventAction' => [ 'name' => 'DeleteEventAction', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/event-actions/{EventActionId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteEventActionRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteRevision' => [ 'name' => 'DeleteRevision', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/data-sets/{DataSetId}/revisions/{RevisionId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteRevisionRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetAsset' => [ 'name' => 'GetAsset', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/data-sets/{DataSetId}/revisions/{RevisionId}/assets/{AssetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAssetRequest', ], 'output' => [ 'shape' => 'GetAssetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetDataSet' => [ 'name' => 'GetDataSet', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/data-sets/{DataSetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataSetRequest', ], 'output' => [ 'shape' => 'GetDataSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetEventAction' => [ 'name' => 'GetEventAction', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/event-actions/{EventActionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEventActionRequest', ], 'output' => [ 'shape' => 'GetEventActionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetJob' => [ 'name' => 'GetJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/jobs/{JobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetJobRequest', ], 'output' => [ 'shape' => 'GetJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetRevision' => [ 'name' => 'GetRevision', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/data-sets/{DataSetId}/revisions/{RevisionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRevisionRequest', ], 'output' => [ 'shape' => 'GetRevisionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDataSetRevisions' => [ 'name' => 'ListDataSetRevisions', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/data-sets/{DataSetId}/revisions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataSetRevisionsRequest', ], 'output' => [ 'shape' => 'ListDataSetRevisionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDataSets' => [ 'name' => 'ListDataSets', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/data-sets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataSetsRequest', ], 'output' => [ 'shape' => 'ListDataSetsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListEventActions' => [ 'name' => 'ListEventActions', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/event-actions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEventActionsRequest', ], 'output' => [ 'shape' => 'ListEventActionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListJobs' => [ 'name' => 'ListJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListJobsRequest', ], 'output' => [ 'shape' => 'ListJobsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListRevisionAssets' => [ 'name' => 'ListRevisionAssets', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/data-sets/{DataSetId}/revisions/{RevisionId}/assets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRevisionAssetsRequest', ], 'output' => [ 'shape' => 'ListRevisionAssetsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], ], 'RevokeRevision' => [ 'name' => 'RevokeRevision', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/data-sets/{DataSetId}/revisions/{RevisionId}/revoke', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RevokeRevisionRequest', ], 'output' => [ 'shape' => 'RevokeRevisionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'SendApiAsset' => [ 'name' => 'SendApiAsset', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SendApiAssetRequest', ], 'output' => [ 'shape' => 'SendApiAssetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'endpoint' => [ 'hostPrefix' => 'api-fulfill.', ], ], 'SendDataSetNotification' => [ 'name' => 'SendDataSetNotification', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/data-sets/{DataSetId}/notification', 'responseCode' => 202, ], 'input' => [ 'shape' => 'SendDataSetNotificationRequest', ], 'output' => [ 'shape' => 'SendDataSetNotificationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartJob' => [ 'name' => 'StartJob', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v1/jobs/{JobId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartJobRequest', ], 'output' => [ 'shape' => 'StartJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'idempotent' => true, ], 'UpdateAsset' => [ 'name' => 'UpdateAsset', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v1/data-sets/{DataSetId}/revisions/{RevisionId}/assets/{AssetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAssetRequest', ], 'output' => [ 'shape' => 'UpdateAssetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateDataSet' => [ 'name' => 'UpdateDataSet', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v1/data-sets/{DataSetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDataSetRequest', ], 'output' => [ 'shape' => 'UpdateDataSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateEventAction' => [ 'name' => 'UpdateEventAction', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v1/event-actions/{EventActionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateEventActionRequest', ], 'output' => [ 'shape' => 'UpdateEventActionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateRevision' => [ 'name' => 'UpdateRevision', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v1/data-sets/{DataSetId}/revisions/{RevisionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateRevisionRequest', ], 'output' => [ 'shape' => 'UpdateRevisionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => '__string', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'Action' => [ 'type' => 'structure', 'members' => [ 'ExportRevisionToS3' => [ 'shape' => 'AutoExportRevisionToS3RequestDetails', ], ], ], 'ApiDescription' => [ 'type' => 'string', ], 'ApiGatewayApiAsset' => [ 'type' => 'structure', 'members' => [ 'ApiDescription' => [ 'shape' => 'ApiDescription', ], 'ApiEndpoint' => [ 'shape' => '__string', ], 'ApiId' => [ 'shape' => '__string', ], 'ApiKey' => [ 'shape' => '__string', ], 'ApiName' => [ 'shape' => '__string', ], 'ApiSpecificationDownloadUrl' => [ 'shape' => '__string', ], 'ApiSpecificationDownloadUrlExpiresAt' => [ 'shape' => 'Timestamp', ], 'ProtocolType' => [ 'shape' => 'ProtocolType', ], 'Stage' => [ 'shape' => '__string', ], ], ], 'Arn' => [ 'type' => 'string', ], 'AssetDestinationEntry' => [ 'type' => 'structure', 'required' => [ 'AssetId', 'Bucket', ], 'members' => [ 'AssetId' => [ 'shape' => 'Id', ], 'Bucket' => [ 'shape' => '__string', ], 'Key' => [ 'shape' => '__string', ], ], ], 'AssetDetails' => [ 'type' => 'structure', 'members' => [ 'S3SnapshotAsset' => [ 'shape' => 'S3SnapshotAsset', ], 'RedshiftDataShareAsset' => [ 'shape' => 'RedshiftDataShareAsset', ], 'ApiGatewayApiAsset' => [ 'shape' => 'ApiGatewayApiAsset', ], 'S3DataAccessAsset' => [ 'shape' => 'S3DataAccessAsset', ], 'LakeFormationDataPermissionAsset' => [ 'shape' => 'LakeFormationDataPermissionAsset', ], ], ], 'AssetEntry' => [ 'type' => 'structure', 'required' => [ 'Arn', 'AssetDetails', 'AssetType', 'CreatedAt', 'DataSetId', 'Id', 'Name', 'RevisionId', 'UpdatedAt', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'AssetDetails' => [ 'shape' => 'AssetDetails', ], 'AssetType' => [ 'shape' => 'AssetType', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'DataSetId' => [ 'shape' => 'Id', ], 'Id' => [ 'shape' => 'Id', ], 'Name' => [ 'shape' => 'AssetName', ], 'RevisionId' => [ 'shape' => 'Id', ], 'SourceId' => [ 'shape' => 'Id', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'AssetName' => [ 'type' => 'string', ], 'AssetSourceEntry' => [ 'type' => 'structure', 'required' => [ 'Bucket', 'Key', ], 'members' => [ 'Bucket' => [ 'shape' => '__string', ], 'Key' => [ 'shape' => '__string', ], ], ], 'AssetType' => [ 'type' => 'string', 'enum' => [ 'S3_SNAPSHOT', 'REDSHIFT_DATA_SHARE', 'API_GATEWAY_API', 'S3_DATA_ACCESS', 'LAKE_FORMATION_DATA_PERMISSION', ], ], 'AutoExportRevisionDestinationEntry' => [ 'type' => 'structure', 'required' => [ 'Bucket', ], 'members' => [ 'Bucket' => [ 'shape' => '__string', ], 'KeyPattern' => [ 'shape' => '__string', ], ], ], 'AutoExportRevisionToS3RequestDetails' => [ 'type' => 'structure', 'required' => [ 'RevisionDestination', ], 'members' => [ 'Encryption' => [ 'shape' => 'ExportServerSideEncryption', ], 'RevisionDestination' => [ 'shape' => 'AutoExportRevisionDestinationEntry', ], ], ], 'AwsAccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '.*/^[\\d]{12}$/.*', ], 'CancelJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'JobId', ], ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[\\x21-\\x7E]{1,64}', ], 'Code' => [ 'type' => 'string', 'enum' => [ 'ACCESS_DENIED_EXCEPTION', 'INTERNAL_SERVER_EXCEPTION', 'MALWARE_DETECTED', 'RESOURCE_NOT_FOUND_EXCEPTION', 'SERVICE_QUOTA_EXCEEDED_EXCEPTION', 'VALIDATION_EXCEPTION', 'MALWARE_SCAN_ENCRYPTED_FILE', ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => '__string', ], 'ResourceId' => [ 'shape' => '__string', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateDataSetRequest' => [ 'type' => 'structure', 'required' => [ 'AssetType', 'Description', 'Name', ], 'members' => [ 'AssetType' => [ 'shape' => 'AssetType', ], 'Description' => [ 'shape' => 'Description', ], 'Name' => [ 'shape' => 'Name', ], 'Tags' => [ 'shape' => 'MapOf__string', ], ], ], 'CreateDataSetResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'AssetType' => [ 'shape' => 'AssetType', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'Id' => [ 'shape' => 'Id', ], 'Name' => [ 'shape' => 'Name', ], 'Origin' => [ 'shape' => 'Origin', ], 'OriginDetails' => [ 'shape' => 'OriginDetails', ], 'SourceId' => [ 'shape' => 'Id', ], 'Tags' => [ 'shape' => 'MapOf__string', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'CreateEventActionRequest' => [ 'type' => 'structure', 'required' => [ 'Action', 'Event', ], 'members' => [ 'Action' => [ 'shape' => 'Action', ], 'Event' => [ 'shape' => 'Event', ], ], ], 'CreateEventActionResponse' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'Action', ], 'Arn' => [ 'shape' => 'Arn', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Event' => [ 'shape' => 'Event', ], 'Id' => [ 'shape' => 'Id', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'CreateJobRequest' => [ 'type' => 'structure', 'required' => [ 'Details', 'Type', ], 'members' => [ 'Details' => [ 'shape' => 'RequestDetails', ], 'Type' => [ 'shape' => 'Type', ], ], ], 'CreateJobResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Details' => [ 'shape' => 'ResponseDetails', ], 'Errors' => [ 'shape' => 'ListOfJobError', ], 'Id' => [ 'shape' => 'Id', ], 'State' => [ 'shape' => 'State', ], 'Type' => [ 'shape' => 'Type', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'CreateRevisionRequest' => [ 'type' => 'structure', 'required' => [ 'DataSetId', ], 'members' => [ 'Comment' => [ 'shape' => '__stringMin0Max16384', ], 'DataSetId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'DataSetId', ], 'Tags' => [ 'shape' => 'MapOf__string', ], ], ], 'CreateRevisionResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Comment' => [ 'shape' => '__stringMin0Max16384', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'DataSetId' => [ 'shape' => 'Id', ], 'Finalized' => [ 'shape' => '__boolean', ], 'Id' => [ 'shape' => 'Id', ], 'SourceId' => [ 'shape' => 'Id', ], 'Tags' => [ 'shape' => 'MapOf__string', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'RevocationComment' => [ 'shape' => '__stringMin10Max512', ], 'Revoked' => [ 'shape' => '__boolean', ], 'RevokedAt' => [ 'shape' => 'Timestamp', ], ], ], 'CreateS3DataAccessFromS3BucketRequestDetails' => [ 'type' => 'structure', 'required' => [ 'AssetSource', 'DataSetId', 'RevisionId', ], 'members' => [ 'AssetSource' => [ 'shape' => 'S3DataAccessAssetSourceEntry', ], 'DataSetId' => [ 'shape' => 'Id', ], 'RevisionId' => [ 'shape' => 'Id', ], ], ], 'CreateS3DataAccessFromS3BucketResponseDetails' => [ 'type' => 'structure', 'required' => [ 'AssetSource', 'DataSetId', 'RevisionId', ], 'members' => [ 'AssetSource' => [ 'shape' => 'S3DataAccessAssetSourceEntry', ], 'DataSetId' => [ 'shape' => 'Id', ], 'RevisionId' => [ 'shape' => 'Id', ], ], ], 'DataSetEntry' => [ 'type' => 'structure', 'required' => [ 'Arn', 'AssetType', 'CreatedAt', 'Description', 'Id', 'Name', 'Origin', 'UpdatedAt', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'AssetType' => [ 'shape' => 'AssetType', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'Id' => [ 'shape' => 'Id', ], 'Name' => [ 'shape' => 'Name', ], 'Origin' => [ 'shape' => 'Origin', ], 'OriginDetails' => [ 'shape' => 'OriginDetails', ], 'SourceId' => [ 'shape' => 'Id', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'DataUpdateRequestDetails' => [ 'type' => 'structure', 'members' => [ 'DataUpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'DatabaseLFTagPolicy' => [ 'type' => 'structure', 'required' => [ 'Expression', ], 'members' => [ 'Expression' => [ 'shape' => 'ListOfLFTags', ], ], ], 'DatabaseLFTagPolicyAndPermissions' => [ 'type' => 'structure', 'required' => [ 'Expression', 'Permissions', ], 'members' => [ 'Expression' => [ 'shape' => 'ListOfLFTags', ], 'Permissions' => [ 'shape' => 'ListOfDatabaseLFTagPolicyPermissions', ], ], ], 'DatabaseLFTagPolicyPermission' => [ 'type' => 'string', 'enum' => [ 'DESCRIBE', ], ], 'DeleteAssetRequest' => [ 'type' => 'structure', 'required' => [ 'AssetId', 'DataSetId', 'RevisionId', ], 'members' => [ 'AssetId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'AssetId', ], 'DataSetId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'DataSetId', ], 'RevisionId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'RevisionId', ], ], ], 'DeleteDataSetRequest' => [ 'type' => 'structure', 'required' => [ 'DataSetId', ], 'members' => [ 'DataSetId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'DataSetId', ], ], ], 'DeleteEventActionRequest' => [ 'type' => 'structure', 'required' => [ 'EventActionId', ], 'members' => [ 'EventActionId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'EventActionId', ], ], ], 'DeleteRevisionRequest' => [ 'type' => 'structure', 'required' => [ 'DataSetId', 'RevisionId', ], 'members' => [ 'DataSetId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'DataSetId', ], 'RevisionId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'RevisionId', ], ], ], 'DeprecationRequestDetails' => [ 'type' => 'structure', 'required' => [ 'DeprecationAt', ], 'members' => [ 'DeprecationAt' => [ 'shape' => 'Timestamp', ], ], ], 'Description' => [ 'type' => 'string', ], 'Details' => [ 'type' => 'structure', 'members' => [ 'ImportAssetFromSignedUrlJobErrorDetails' => [ 'shape' => 'ImportAssetFromSignedUrlJobErrorDetails', ], 'ImportAssetsFromS3JobErrorDetails' => [ 'shape' => 'ListOfAssetSourceEntry', ], ], ], 'Event' => [ 'type' => 'structure', 'members' => [ 'RevisionPublished' => [ 'shape' => 'RevisionPublished', ], ], ], 'EventActionEntry' => [ 'type' => 'structure', 'required' => [ 'Action', 'Arn', 'CreatedAt', 'Event', 'Id', 'UpdatedAt', ], 'members' => [ 'Action' => [ 'shape' => 'Action', ], 'Arn' => [ 'shape' => 'Arn', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Event' => [ 'shape' => 'Event', ], 'Id' => [ 'shape' => 'Id', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'ExceptionCause' => [ 'type' => 'string', 'enum' => [ 'InsufficientS3BucketPolicy', 'S3AccessDenied', ], ], 'ExportAssetToSignedUrlRequestDetails' => [ 'type' => 'structure', 'required' => [ 'AssetId', 'DataSetId', 'RevisionId', ], 'members' => [ 'AssetId' => [ 'shape' => 'Id', ], 'DataSetId' => [ 'shape' => 'Id', ], 'RevisionId' => [ 'shape' => 'Id', ], ], ], 'ExportAssetToSignedUrlResponseDetails' => [ 'type' => 'structure', 'required' => [ 'AssetId', 'DataSetId', 'RevisionId', ], 'members' => [ 'AssetId' => [ 'shape' => 'Id', ], 'DataSetId' => [ 'shape' => 'Id', ], 'RevisionId' => [ 'shape' => 'Id', ], 'SignedUrl' => [ 'shape' => '__string', ], 'SignedUrlExpiresAt' => [ 'shape' => 'Timestamp', ], ], ], 'ExportAssetsToS3RequestDetails' => [ 'type' => 'structure', 'required' => [ 'AssetDestinations', 'DataSetId', 'RevisionId', ], 'members' => [ 'AssetDestinations' => [ 'shape' => 'ListOfAssetDestinationEntry', ], 'DataSetId' => [ 'shape' => 'Id', ], 'Encryption' => [ 'shape' => 'ExportServerSideEncryption', ], 'RevisionId' => [ 'shape' => 'Id', ], ], ], 'ExportAssetsToS3ResponseDetails' => [ 'type' => 'structure', 'required' => [ 'AssetDestinations', 'DataSetId', 'RevisionId', ], 'members' => [ 'AssetDestinations' => [ 'shape' => 'ListOfAssetDestinationEntry', ], 'DataSetId' => [ 'shape' => 'Id', ], 'Encryption' => [ 'shape' => 'ExportServerSideEncryption', ], 'RevisionId' => [ 'shape' => 'Id', ], ], ], 'ExportRevisionsToS3RequestDetails' => [ 'type' => 'structure', 'required' => [ 'DataSetId', 'RevisionDestinations', ], 'members' => [ 'DataSetId' => [ 'shape' => 'Id', ], 'Encryption' => [ 'shape' => 'ExportServerSideEncryption', ], 'RevisionDestinations' => [ 'shape' => 'ListOfRevisionDestinationEntry', ], ], ], 'ExportRevisionsToS3ResponseDetails' => [ 'type' => 'structure', 'required' => [ 'DataSetId', 'RevisionDestinations', ], 'members' => [ 'DataSetId' => [ 'shape' => 'Id', ], 'Encryption' => [ 'shape' => 'ExportServerSideEncryption', ], 'RevisionDestinations' => [ 'shape' => 'ListOfRevisionDestinationEntry', ], 'EventActionArn' => [ 'shape' => '__string', ], ], ], 'ExportServerSideEncryption' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'KmsKeyArn' => [ 'shape' => '__string', ], 'Type' => [ 'shape' => 'ServerSideEncryptionTypes', ], ], ], 'GetAssetRequest' => [ 'type' => 'structure', 'required' => [ 'AssetId', 'DataSetId', 'RevisionId', ], 'members' => [ 'AssetId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'AssetId', ], 'DataSetId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'DataSetId', ], 'RevisionId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'RevisionId', ], ], ], 'GetAssetResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'AssetDetails' => [ 'shape' => 'AssetDetails', ], 'AssetType' => [ 'shape' => 'AssetType', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'DataSetId' => [ 'shape' => 'Id', ], 'Id' => [ 'shape' => 'Id', ], 'Name' => [ 'shape' => 'AssetName', ], 'RevisionId' => [ 'shape' => 'Id', ], 'SourceId' => [ 'shape' => 'Id', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'GetDataSetRequest' => [ 'type' => 'structure', 'required' => [ 'DataSetId', ], 'members' => [ 'DataSetId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'DataSetId', ], ], ], 'GetDataSetResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'AssetType' => [ 'shape' => 'AssetType', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'Id' => [ 'shape' => 'Id', ], 'Name' => [ 'shape' => 'Name', ], 'Origin' => [ 'shape' => 'Origin', ], 'OriginDetails' => [ 'shape' => 'OriginDetails', ], 'SourceId' => [ 'shape' => 'Id', ], 'Tags' => [ 'shape' => 'MapOf__string', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'GetEventActionRequest' => [ 'type' => 'structure', 'required' => [ 'EventActionId', ], 'members' => [ 'EventActionId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'EventActionId', ], ], ], 'GetEventActionResponse' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'Action', ], 'Arn' => [ 'shape' => 'Arn', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Event' => [ 'shape' => 'Event', ], 'Id' => [ 'shape' => 'Id', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'GetJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'JobId', ], ], ], 'GetJobResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Details' => [ 'shape' => 'ResponseDetails', ], 'Errors' => [ 'shape' => 'ListOfJobError', ], 'Id' => [ 'shape' => 'Id', ], 'State' => [ 'shape' => 'State', ], 'Type' => [ 'shape' => 'Type', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'GetRevisionRequest' => [ 'type' => 'structure', 'required' => [ 'DataSetId', 'RevisionId', ], 'members' => [ 'DataSetId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'DataSetId', ], 'RevisionId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'RevisionId', ], ], ], 'GetRevisionResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Comment' => [ 'shape' => '__stringMin0Max16384', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'DataSetId' => [ 'shape' => 'Id', ], 'Finalized' => [ 'shape' => '__boolean', ], 'Id' => [ 'shape' => 'Id', ], 'SourceId' => [ 'shape' => 'Id', ], 'Tags' => [ 'shape' => 'MapOf__string', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'RevocationComment' => [ 'shape' => '__stringMin10Max512', ], 'Revoked' => [ 'shape' => '__boolean', ], 'RevokedAt' => [ 'shape' => 'Timestamp', ], ], ], 'Id' => [ 'type' => 'string', ], 'ImportAssetFromApiGatewayApiRequestDetails' => [ 'type' => 'structure', 'required' => [ 'ApiId', 'ApiName', 'ApiSpecificationMd5Hash', 'DataSetId', 'ProtocolType', 'RevisionId', 'Stage', ], 'members' => [ 'ApiDescription' => [ 'shape' => 'ApiDescription', ], 'ApiId' => [ 'shape' => '__string', ], 'ApiKey' => [ 'shape' => '__string', ], 'ApiName' => [ 'shape' => '__string', ], 'ApiSpecificationMd5Hash' => [ 'shape' => '__stringMin24Max24PatternAZaZ094AZaZ092AZaZ093', ], 'DataSetId' => [ 'shape' => 'Id', ], 'ProtocolType' => [ 'shape' => 'ProtocolType', ], 'RevisionId' => [ 'shape' => 'Id', ], 'Stage' => [ 'shape' => '__string', ], ], ], 'ImportAssetFromApiGatewayApiResponseDetails' => [ 'type' => 'structure', 'required' => [ 'ApiId', 'ApiName', 'ApiSpecificationMd5Hash', 'ApiSpecificationUploadUrl', 'ApiSpecificationUploadUrlExpiresAt', 'DataSetId', 'ProtocolType', 'RevisionId', 'Stage', ], 'members' => [ 'ApiDescription' => [ 'shape' => 'ApiDescription', ], 'ApiId' => [ 'shape' => '__string', ], 'ApiKey' => [ 'shape' => '__string', ], 'ApiName' => [ 'shape' => '__string', ], 'ApiSpecificationMd5Hash' => [ 'shape' => '__stringMin24Max24PatternAZaZ094AZaZ092AZaZ093', ], 'ApiSpecificationUploadUrl' => [ 'shape' => '__string', ], 'ApiSpecificationUploadUrlExpiresAt' => [ 'shape' => 'Timestamp', ], 'DataSetId' => [ 'shape' => 'Id', ], 'ProtocolType' => [ 'shape' => 'ProtocolType', ], 'RevisionId' => [ 'shape' => 'Id', ], 'Stage' => [ 'shape' => '__string', ], ], ], 'ImportAssetFromSignedUrlJobErrorDetails' => [ 'type' => 'structure', 'required' => [ 'AssetName', ], 'members' => [ 'AssetName' => [ 'shape' => 'AssetName', ], ], ], 'ImportAssetFromSignedUrlRequestDetails' => [ 'type' => 'structure', 'required' => [ 'AssetName', 'DataSetId', 'Md5Hash', 'RevisionId', ], 'members' => [ 'AssetName' => [ 'shape' => 'AssetName', ], 'DataSetId' => [ 'shape' => 'Id', ], 'Md5Hash' => [ 'shape' => '__stringMin24Max24PatternAZaZ094AZaZ092AZaZ093', ], 'RevisionId' => [ 'shape' => 'Id', ], ], ], 'ImportAssetFromSignedUrlResponseDetails' => [ 'type' => 'structure', 'required' => [ 'AssetName', 'DataSetId', 'RevisionId', ], 'members' => [ 'AssetName' => [ 'shape' => 'AssetName', ], 'DataSetId' => [ 'shape' => 'Id', ], 'Md5Hash' => [ 'shape' => '__stringMin24Max24PatternAZaZ094AZaZ092AZaZ093', ], 'RevisionId' => [ 'shape' => 'Id', ], 'SignedUrl' => [ 'shape' => '__string', ], 'SignedUrlExpiresAt' => [ 'shape' => 'Timestamp', ], ], ], 'ImportAssetsFromLakeFormationTagPolicyRequestDetails' => [ 'type' => 'structure', 'required' => [ 'CatalogId', 'RoleArn', 'DataSetId', 'RevisionId', ], 'members' => [ 'CatalogId' => [ 'shape' => 'AwsAccountId', ], 'Database' => [ 'shape' => 'DatabaseLFTagPolicyAndPermissions', ], 'Table' => [ 'shape' => 'TableLFTagPolicyAndPermissions', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'DataSetId' => [ 'shape' => 'Id', ], 'RevisionId' => [ 'shape' => 'Id', ], ], ], 'ImportAssetsFromLakeFormationTagPolicyResponseDetails' => [ 'type' => 'structure', 'required' => [ 'CatalogId', 'RoleArn', 'DataSetId', 'RevisionId', ], 'members' => [ 'CatalogId' => [ 'shape' => 'AwsAccountId', ], 'Database' => [ 'shape' => 'DatabaseLFTagPolicyAndPermissions', ], 'Table' => [ 'shape' => 'TableLFTagPolicyAndPermissions', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'DataSetId' => [ 'shape' => 'Id', ], 'RevisionId' => [ 'shape' => 'Id', ], ], ], 'ImportAssetsFromRedshiftDataSharesRequestDetails' => [ 'type' => 'structure', 'required' => [ 'AssetSources', 'DataSetId', 'RevisionId', ], 'members' => [ 'AssetSources' => [ 'shape' => 'ListOfRedshiftDataShareAssetSourceEntry', ], 'DataSetId' => [ 'shape' => 'Id', ], 'RevisionId' => [ 'shape' => 'Id', ], ], ], 'ImportAssetsFromRedshiftDataSharesResponseDetails' => [ 'type' => 'structure', 'required' => [ 'AssetSources', 'DataSetId', 'RevisionId', ], 'members' => [ 'AssetSources' => [ 'shape' => 'ListOfRedshiftDataShareAssetSourceEntry', ], 'DataSetId' => [ 'shape' => 'Id', ], 'RevisionId' => [ 'shape' => 'Id', ], ], ], 'ImportAssetsFromS3RequestDetails' => [ 'type' => 'structure', 'required' => [ 'AssetSources', 'DataSetId', 'RevisionId', ], 'members' => [ 'AssetSources' => [ 'shape' => 'ListOfAssetSourceEntry', ], 'DataSetId' => [ 'shape' => 'Id', ], 'RevisionId' => [ 'shape' => 'Id', ], ], ], 'ImportAssetsFromS3ResponseDetails' => [ 'type' => 'structure', 'required' => [ 'AssetSources', 'DataSetId', 'RevisionId', ], 'members' => [ 'AssetSources' => [ 'shape' => 'ListOfAssetSourceEntry', ], 'DataSetId' => [ 'shape' => 'Id', ], 'RevisionId' => [ 'shape' => 'Id', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => '__string', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'JobEntry' => [ 'type' => 'structure', 'required' => [ 'Arn', 'CreatedAt', 'Details', 'Id', 'State', 'Type', 'UpdatedAt', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Details' => [ 'shape' => 'ResponseDetails', ], 'Errors' => [ 'shape' => 'ListOfJobError', ], 'Id' => [ 'shape' => 'Id', ], 'State' => [ 'shape' => 'State', ], 'Type' => [ 'shape' => 'Type', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'JobError' => [ 'type' => 'structure', 'required' => [ 'Code', 'Message', ], 'members' => [ 'Code' => [ 'shape' => 'Code', ], 'Details' => [ 'shape' => 'Details', ], 'LimitName' => [ 'shape' => 'JobErrorLimitName', ], 'LimitValue' => [ 'shape' => '__double', ], 'Message' => [ 'shape' => '__string', ], 'ResourceId' => [ 'shape' => '__string', ], 'ResourceType' => [ 'shape' => 'JobErrorResourceTypes', ], ], ], 'JobErrorLimitName' => [ 'type' => 'string', 'enum' => [ 'Assets per revision', 'Asset size in GB', 'Amazon Redshift datashare assets per revision', 'AWS Lake Formation data permission assets per revision', 'Amazon S3 data access assets per revision', ], ], 'JobErrorResourceTypes' => [ 'type' => 'string', 'enum' => [ 'REVISION', 'ASSET', 'DATA_SET', ], ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'KmsKeyToGrant' => [ 'type' => 'structure', 'required' => [ 'KmsKeyArn', ], 'members' => [ 'KmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'LFPermission' => [ 'type' => 'string', 'enum' => [ 'DESCRIBE', 'SELECT', ], ], 'LFResourceDetails' => [ 'type' => 'structure', 'members' => [ 'Database' => [ 'shape' => 'DatabaseLFTagPolicy', ], 'Table' => [ 'shape' => 'TableLFTagPolicy', ], ], ], 'LFResourceType' => [ 'type' => 'string', 'enum' => [ 'TABLE', 'DATABASE', ], ], 'LFTag' => [ 'type' => 'structure', 'required' => [ 'TagKey', 'TagValues', ], 'members' => [ 'TagKey' => [ 'shape' => 'String', ], 'TagValues' => [ 'shape' => 'ListOfLFTagValues', ], ], ], 'LFTagPolicyDetails' => [ 'type' => 'structure', 'required' => [ 'CatalogId', 'ResourceType', 'ResourceDetails', ], 'members' => [ 'CatalogId' => [ 'shape' => 'AwsAccountId', ], 'ResourceType' => [ 'shape' => 'LFResourceType', ], 'ResourceDetails' => [ 'shape' => 'LFResourceDetails', ], ], ], 'LakeFormationDataPermissionAsset' => [ 'type' => 'structure', 'required' => [ 'LakeFormationDataPermissionDetails', 'LakeFormationDataPermissionType', 'Permissions', ], 'members' => [ 'LakeFormationDataPermissionDetails' => [ 'shape' => 'LakeFormationDataPermissionDetails', ], 'LakeFormationDataPermissionType' => [ 'shape' => 'LakeFormationDataPermissionType', ], 'Permissions' => [ 'shape' => 'ListOfLFPermissions', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'LakeFormationDataPermissionDetails' => [ 'type' => 'structure', 'members' => [ 'LFTagPolicy' => [ 'shape' => 'LFTagPolicyDetails', ], ], ], 'LakeFormationDataPermissionType' => [ 'type' => 'string', 'enum' => [ 'LFTagPolicy', ], ], 'LakeFormationTagPolicyDetails' => [ 'type' => 'structure', 'members' => [ 'Database' => [ 'shape' => '__string', ], 'Table' => [ 'shape' => '__string', ], ], ], 'LimitName' => [ 'type' => 'string', 'enum' => [ 'Products per account', 'Data sets per account', 'Data sets per product', 'Revisions per data set', 'Assets per revision', 'Assets per import job from Amazon S3', 'Asset per export job from Amazon S3', 'Asset size in GB', 'Concurrent in progress jobs to export assets to Amazon S3', 'Concurrent in progress jobs to export assets to a signed URL', 'Concurrent in progress jobs to import assets from Amazon S3', 'Concurrent in progress jobs to import assets from a signed URL', 'Concurrent in progress jobs to export revisions to Amazon S3', 'Event actions per account', 'Auto export event actions per data set', 'Amazon Redshift datashare assets per import job from Redshift', 'Concurrent in progress jobs to import assets from Amazon Redshift datashares', 'Revisions per Amazon Redshift datashare data set', 'Amazon Redshift datashare assets per revision', 'Concurrent in progress jobs to import assets from an API Gateway API', 'Amazon API Gateway API assets per revision', 'Revisions per Amazon API Gateway API data set', 'Concurrent in progress jobs to import assets from an AWS Lake Formation tag policy', 'AWS Lake Formation data permission assets per revision', 'Revisions per AWS Lake Formation data permission data set', 'Revisions per Amazon S3 data access data set', 'Amazon S3 data access assets per revision', 'Concurrent in progress jobs to create Amazon S3 data access assets from S3 buckets', ], ], 'ListDataSetRevisionsRequest' => [ 'type' => 'structure', 'required' => [ 'DataSetId', ], 'members' => [ 'DataSetId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'DataSetId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListDataSetRevisionsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'Revisions' => [ 'shape' => 'ListOfRevisionEntry', ], ], ], 'ListDataSetsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'Origin' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'origin', ], ], ], 'ListDataSetsResponse' => [ 'type' => 'structure', 'members' => [ 'DataSets' => [ 'shape' => 'ListOfDataSetEntry', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEventActionsRequest' => [ 'type' => 'structure', 'members' => [ 'EventSourceId' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'eventSourceId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListEventActionsResponse' => [ 'type' => 'structure', 'members' => [ 'EventActions' => [ 'shape' => 'ListOfEventActionEntry', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListJobsRequest' => [ 'type' => 'structure', 'members' => [ 'DataSetId' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'dataSetId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'RevisionId' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'revisionId', ], ], ], 'ListJobsResponse' => [ 'type' => 'structure', 'members' => [ 'Jobs' => [ 'shape' => 'ListOfJobEntry', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListOfAssetDestinationEntry' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetDestinationEntry', ], ], 'ListOfAssetEntry' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetEntry', ], ], 'ListOfAssetSourceEntry' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetSourceEntry', ], ], 'ListOfDataSetEntry' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSetEntry', ], ], 'ListOfDatabaseLFTagPolicyPermissions' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatabaseLFTagPolicyPermission', ], ], 'ListOfEventActionEntry' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventActionEntry', ], ], 'ListOfJobEntry' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobEntry', ], ], 'ListOfJobError' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobError', ], ], 'ListOfKmsKeysToGrant' => [ 'type' => 'list', 'member' => [ 'shape' => 'KmsKeyToGrant', ], 'max' => 10, 'min' => 1, ], 'ListOfLFPermissions' => [ 'type' => 'list', 'member' => [ 'shape' => 'LFPermission', ], ], 'ListOfLFTagValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ListOfLFTags' => [ 'type' => 'list', 'member' => [ 'shape' => 'LFTag', ], ], 'ListOfLakeFormationTagPolicies' => [ 'type' => 'list', 'member' => [ 'shape' => 'LakeFormationTagPolicyDetails', ], ], 'ListOfRedshiftDataShareAssetSourceEntry' => [ 'type' => 'list', 'member' => [ 'shape' => 'RedshiftDataShareAssetSourceEntry', ], ], 'ListOfRedshiftDataShares' => [ 'type' => 'list', 'member' => [ 'shape' => 'RedshiftDataShareDetails', ], ], 'ListOfRevisionDestinationEntry' => [ 'type' => 'list', 'member' => [ 'shape' => 'RevisionDestinationEntry', ], ], 'ListOfRevisionEntry' => [ 'type' => 'list', 'member' => [ 'shape' => 'RevisionEntry', ], ], 'ListOfS3DataAccesses' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3DataAccessDetails', ], ], 'ListOfSchemaChangeDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaChangeDetails', ], ], 'ListOfTableTagPolicyLFPermissions' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableTagPolicyLFPermission', ], ], 'ListOf__string' => [ 'type' => 'list', 'member' => [ 'shape' => '__string', ], ], 'ListRevisionAssetsRequest' => [ 'type' => 'structure', 'required' => [ 'DataSetId', 'RevisionId', ], 'members' => [ 'DataSetId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'DataSetId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'RevisionId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'RevisionId', ], ], ], 'ListRevisionAssetsResponse' => [ 'type' => 'structure', 'members' => [ 'Assets' => [ 'shape' => 'ListOfAssetEntry', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], ], ], 'MapOf__string' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => '__string', ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 200, 'min' => 1, ], 'Name' => [ 'type' => 'string', ], 'NextToken' => [ 'type' => 'string', ], 'NotificationDetails' => [ 'type' => 'structure', 'members' => [ 'DataUpdate' => [ 'shape' => 'DataUpdateRequestDetails', ], 'Deprecation' => [ 'shape' => 'DeprecationRequestDetails', ], 'SchemaChange' => [ 'shape' => 'SchemaChangeRequestDetails', ], ], ], 'NotificationType' => [ 'type' => 'string', 'enum' => [ 'DATA_DELAY', 'DATA_UPDATE', 'DEPRECATION', 'SCHEMA_CHANGE', ], ], 'Origin' => [ 'type' => 'string', 'enum' => [ 'OWNED', 'ENTITLED', ], ], 'OriginDetails' => [ 'type' => 'structure', 'members' => [ 'ProductId' => [ 'shape' => '__string', ], ], ], 'ProtocolType' => [ 'type' => 'string', 'enum' => [ 'REST', ], ], 'RedshiftDataShareAsset' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => '__string', ], ], ], 'RedshiftDataShareAssetSourceEntry' => [ 'type' => 'structure', 'required' => [ 'DataShareArn', ], 'members' => [ 'DataShareArn' => [ 'shape' => '__string', ], ], ], 'RedshiftDataShareDetails' => [ 'type' => 'structure', 'required' => [ 'Arn', 'Database', ], 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'Database' => [ 'shape' => '__string', ], 'Function' => [ 'shape' => '__string', ], 'Table' => [ 'shape' => '__string', ], 'Schema' => [ 'shape' => '__string', ], 'View' => [ 'shape' => '__string', ], ], ], 'RequestDetails' => [ 'type' => 'structure', 'members' => [ 'ExportAssetToSignedUrl' => [ 'shape' => 'ExportAssetToSignedUrlRequestDetails', ], 'ExportAssetsToS3' => [ 'shape' => 'ExportAssetsToS3RequestDetails', ], 'ExportRevisionsToS3' => [ 'shape' => 'ExportRevisionsToS3RequestDetails', ], 'ImportAssetFromSignedUrl' => [ 'shape' => 'ImportAssetFromSignedUrlRequestDetails', ], 'ImportAssetsFromS3' => [ 'shape' => 'ImportAssetsFromS3RequestDetails', ], 'ImportAssetsFromRedshiftDataShares' => [ 'shape' => 'ImportAssetsFromRedshiftDataSharesRequestDetails', ], 'ImportAssetFromApiGatewayApi' => [ 'shape' => 'ImportAssetFromApiGatewayApiRequestDetails', ], 'CreateS3DataAccessFromS3Bucket' => [ 'shape' => 'CreateS3DataAccessFromS3BucketRequestDetails', ], 'ImportAssetsFromLakeFormationTagPolicy' => [ 'shape' => 'ImportAssetsFromLakeFormationTagPolicyRequestDetails', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => '__string', ], 'ResourceId' => [ 'shape' => '__string', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'DATA_SET', 'REVISION', 'ASSET', 'JOB', 'EVENT_ACTION', ], ], 'ResponseDetails' => [ 'type' => 'structure', 'members' => [ 'ExportAssetToSignedUrl' => [ 'shape' => 'ExportAssetToSignedUrlResponseDetails', ], 'ExportAssetsToS3' => [ 'shape' => 'ExportAssetsToS3ResponseDetails', ], 'ExportRevisionsToS3' => [ 'shape' => 'ExportRevisionsToS3ResponseDetails', ], 'ImportAssetFromSignedUrl' => [ 'shape' => 'ImportAssetFromSignedUrlResponseDetails', ], 'ImportAssetsFromS3' => [ 'shape' => 'ImportAssetsFromS3ResponseDetails', ], 'ImportAssetsFromRedshiftDataShares' => [ 'shape' => 'ImportAssetsFromRedshiftDataSharesResponseDetails', ], 'ImportAssetFromApiGatewayApi' => [ 'shape' => 'ImportAssetFromApiGatewayApiResponseDetails', ], 'CreateS3DataAccessFromS3Bucket' => [ 'shape' => 'CreateS3DataAccessFromS3BucketResponseDetails', ], 'ImportAssetsFromLakeFormationTagPolicy' => [ 'shape' => 'ImportAssetsFromLakeFormationTagPolicyResponseDetails', ], ], ], 'RevisionDestinationEntry' => [ 'type' => 'structure', 'required' => [ 'Bucket', 'RevisionId', ], 'members' => [ 'Bucket' => [ 'shape' => '__string', ], 'KeyPattern' => [ 'shape' => '__string', ], 'RevisionId' => [ 'shape' => 'Id', ], ], ], 'RevisionEntry' => [ 'type' => 'structure', 'required' => [ 'Arn', 'CreatedAt', 'DataSetId', 'Id', 'UpdatedAt', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Comment' => [ 'shape' => '__stringMin0Max16384', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'DataSetId' => [ 'shape' => 'Id', ], 'Finalized' => [ 'shape' => '__boolean', ], 'Id' => [ 'shape' => 'Id', ], 'SourceId' => [ 'shape' => 'Id', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'RevocationComment' => [ 'shape' => '__stringMin10Max512', ], 'Revoked' => [ 'shape' => '__boolean', ], 'RevokedAt' => [ 'shape' => 'Timestamp', ], ], ], 'RevisionPublished' => [ 'type' => 'structure', 'required' => [ 'DataSetId', ], 'members' => [ 'DataSetId' => [ 'shape' => 'Id', ], ], ], 'RevokeRevisionRequest' => [ 'type' => 'structure', 'required' => [ 'DataSetId', 'RevisionId', 'RevocationComment', ], 'members' => [ 'DataSetId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'DataSetId', ], 'RevisionId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'RevisionId', ], 'RevocationComment' => [ 'shape' => '__stringMin10Max512', ], ], ], 'RevokeRevisionResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Comment' => [ 'shape' => '__stringMin0Max16384', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'DataSetId' => [ 'shape' => 'Id', ], 'Finalized' => [ 'shape' => '__boolean', ], 'Id' => [ 'shape' => 'Id', ], 'SourceId' => [ 'shape' => 'Id', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'RevocationComment' => [ 'shape' => '__stringMin10Max512', ], 'Revoked' => [ 'shape' => '__boolean', ], 'RevokedAt' => [ 'shape' => 'Timestamp', ], ], ], 'RoleArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:iam::(\\d{12}):role\\/.+', ], 'S3DataAccessAsset' => [ 'type' => 'structure', 'required' => [ 'Bucket', ], 'members' => [ 'Bucket' => [ 'shape' => '__string', ], 'KeyPrefixes' => [ 'shape' => 'ListOf__string', ], 'Keys' => [ 'shape' => 'ListOf__string', ], 'S3AccessPointAlias' => [ 'shape' => '__string', ], 'S3AccessPointArn' => [ 'shape' => '__string', ], 'KmsKeysToGrant' => [ 'shape' => 'ListOfKmsKeysToGrant', ], ], ], 'S3DataAccessAssetSourceEntry' => [ 'type' => 'structure', 'required' => [ 'Bucket', ], 'members' => [ 'Bucket' => [ 'shape' => '__string', ], 'KeyPrefixes' => [ 'shape' => 'ListOf__string', ], 'Keys' => [ 'shape' => 'ListOf__string', ], 'KmsKeysToGrant' => [ 'shape' => 'ListOfKmsKeysToGrant', ], ], ], 'S3DataAccessDetails' => [ 'type' => 'structure', 'members' => [ 'KeyPrefixes' => [ 'shape' => 'ListOf__string', ], 'Keys' => [ 'shape' => 'ListOf__string', ], ], ], 'S3SnapshotAsset' => [ 'type' => 'structure', 'required' => [ 'Size', ], 'members' => [ 'Size' => [ 'shape' => '__doubleMin0', ], ], ], 'SchemaChangeDetails' => [ 'type' => 'structure', 'required' => [ 'Name', 'Type', ], 'members' => [ 'Name' => [ 'shape' => '__string', ], 'Type' => [ 'shape' => 'SchemaChangeType', ], 'Description' => [ 'shape' => '__string', ], ], ], 'SchemaChangeRequestDetails' => [ 'type' => 'structure', 'required' => [ 'SchemaChangeAt', ], 'members' => [ 'Changes' => [ 'shape' => 'ListOfSchemaChangeDetails', ], 'SchemaChangeAt' => [ 'shape' => 'Timestamp', ], ], ], 'SchemaChangeType' => [ 'type' => 'string', 'enum' => [ 'ADD', 'REMOVE', 'MODIFY', ], ], 'ScopeDetails' => [ 'type' => 'structure', 'members' => [ 'LakeFormationTagPolicies' => [ 'shape' => 'ListOfLakeFormationTagPolicies', ], 'RedshiftDataShares' => [ 'shape' => 'ListOfRedshiftDataShares', ], 'S3DataAccesses' => [ 'shape' => 'ListOfS3DataAccesses', ], ], ], 'SendApiAssetRequest' => [ 'type' => 'structure', 'required' => [ 'AssetId', 'DataSetId', 'RevisionId', ], 'members' => [ 'Body' => [ 'shape' => '__string', ], 'QueryStringParameters' => [ 'shape' => 'MapOf__string', 'location' => 'querystring', ], 'AssetId' => [ 'shape' => '__string', 'location' => 'header', 'locationName' => 'x-amzn-dataexchange-asset-id', ], 'DataSetId' => [ 'shape' => '__string', 'location' => 'header', 'locationName' => 'x-amzn-dataexchange-data-set-id', ], 'RequestHeaders' => [ 'shape' => 'MapOf__string', 'location' => 'headers', 'locationName' => 'x-amzn-dataexchange-header-', ], 'Method' => [ 'shape' => '__string', 'location' => 'header', 'locationName' => 'x-amzn-dataexchange-http-method', ], 'Path' => [ 'shape' => '__string', 'location' => 'header', 'locationName' => 'x-amzn-dataexchange-path', ], 'RevisionId' => [ 'shape' => '__string', 'location' => 'header', 'locationName' => 'x-amzn-dataexchange-revision-id', ], ], 'payload' => 'Body', ], 'SendApiAssetResponse' => [ 'type' => 'structure', 'members' => [ 'Body' => [ 'shape' => '__string', ], 'ResponseHeaders' => [ 'shape' => 'MapOf__string', 'location' => 'headers', 'locationName' => '', ], ], 'payload' => 'Body', ], 'SendDataSetNotificationRequest' => [ 'type' => 'structure', 'required' => [ 'DataSetId', 'Type', ], 'members' => [ 'Scope' => [ 'shape' => 'ScopeDetails', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'Comment' => [ 'shape' => '__stringMin0Max4096', ], 'DataSetId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'DataSetId', ], 'Details' => [ 'shape' => 'NotificationDetails', ], 'Type' => [ 'shape' => 'NotificationType', ], ], ], 'SendDataSetNotificationResponse' => [ 'type' => 'structure', 'members' => [], ], 'ServerSideEncryptionTypes' => [ 'type' => 'string', 'enum' => [ 'aws:kms', 'AES256', ], ], 'ServiceLimitExceededException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'LimitName' => [ 'shape' => 'LimitName', ], 'LimitValue' => [ 'shape' => '__double', ], 'Message' => [ 'shape' => '__string', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'StartJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'JobId', ], ], ], 'StartJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'State' => [ 'type' => 'string', 'enum' => [ 'WAITING', 'IN_PROGRESS', 'ERROR', 'COMPLETED', 'CANCELLED', 'TIMED_OUT', ], ], 'String' => [ 'type' => 'string', ], 'TableLFTagPolicy' => [ 'type' => 'structure', 'required' => [ 'Expression', ], 'members' => [ 'Expression' => [ 'shape' => 'ListOfLFTags', ], ], ], 'TableLFTagPolicyAndPermissions' => [ 'type' => 'structure', 'required' => [ 'Expression', 'Permissions', ], 'members' => [ 'Expression' => [ 'shape' => 'ListOfLFTags', ], 'Permissions' => [ 'shape' => 'ListOfTableTagPolicyLFPermissions', ], ], ], 'TableTagPolicyLFPermission' => [ 'type' => 'string', 'enum' => [ 'DESCRIBE', 'SELECT', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => '__string', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'Type' => [ 'type' => 'string', 'enum' => [ 'IMPORT_ASSETS_FROM_S3', 'IMPORT_ASSET_FROM_SIGNED_URL', 'EXPORT_ASSETS_TO_S3', 'EXPORT_ASSET_TO_SIGNED_URL', 'EXPORT_REVISIONS_TO_S3', 'IMPORT_ASSETS_FROM_REDSHIFT_DATA_SHARES', 'IMPORT_ASSET_FROM_API_GATEWAY_API', 'CREATE_S3_DATA_ACCESS_FROM_S3_BUCKET', 'IMPORT_ASSETS_FROM_LAKE_FORMATION_TAG_POLICY', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'ListOf__string', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UpdateAssetRequest' => [ 'type' => 'structure', 'required' => [ 'AssetId', 'DataSetId', 'Name', 'RevisionId', ], 'members' => [ 'AssetId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'AssetId', ], 'DataSetId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'DataSetId', ], 'Name' => [ 'shape' => 'AssetName', ], 'RevisionId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'RevisionId', ], ], ], 'UpdateAssetResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'AssetDetails' => [ 'shape' => 'AssetDetails', ], 'AssetType' => [ 'shape' => 'AssetType', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'DataSetId' => [ 'shape' => 'Id', ], 'Id' => [ 'shape' => 'Id', ], 'Name' => [ 'shape' => 'AssetName', ], 'RevisionId' => [ 'shape' => 'Id', ], 'SourceId' => [ 'shape' => 'Id', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateDataSetRequest' => [ 'type' => 'structure', 'required' => [ 'DataSetId', ], 'members' => [ 'DataSetId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'DataSetId', ], 'Description' => [ 'shape' => 'Description', ], 'Name' => [ 'shape' => 'Name', ], ], ], 'UpdateDataSetResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'AssetType' => [ 'shape' => 'AssetType', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'Id' => [ 'shape' => 'Id', ], 'Name' => [ 'shape' => 'Name', ], 'Origin' => [ 'shape' => 'Origin', ], 'OriginDetails' => [ 'shape' => 'OriginDetails', ], 'SourceId' => [ 'shape' => 'Id', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateEventActionRequest' => [ 'type' => 'structure', 'required' => [ 'EventActionId', ], 'members' => [ 'Action' => [ 'shape' => 'Action', ], 'EventActionId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'EventActionId', ], ], ], 'UpdateEventActionResponse' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'Action', ], 'Arn' => [ 'shape' => 'Arn', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Event' => [ 'shape' => 'Event', ], 'Id' => [ 'shape' => 'Id', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateRevisionRequest' => [ 'type' => 'structure', 'required' => [ 'DataSetId', 'RevisionId', ], 'members' => [ 'Comment' => [ 'shape' => '__stringMin0Max16384', ], 'DataSetId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'DataSetId', ], 'Finalized' => [ 'shape' => '__boolean', ], 'RevisionId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'RevisionId', ], ], ], 'UpdateRevisionResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Comment' => [ 'shape' => '__stringMin0Max16384', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'DataSetId' => [ 'shape' => 'Id', ], 'Finalized' => [ 'shape' => '__boolean', ], 'Id' => [ 'shape' => 'Id', ], 'SourceId' => [ 'shape' => 'Id', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'RevocationComment' => [ 'shape' => '__stringMin10Max512', ], 'Revoked' => [ 'shape' => '__boolean', ], 'RevokedAt' => [ 'shape' => 'Timestamp', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => '__string', ], 'ExceptionCause' => [ 'shape' => 'ExceptionCause', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], '__boolean' => [ 'type' => 'boolean', ], '__double' => [ 'type' => 'double', ], '__doubleMin0' => [ 'type' => 'double', ], '__string' => [ 'type' => 'string', ], '__stringMin0Max16384' => [ 'type' => 'string', 'max' => 16384, 'min' => 0, ], '__stringMin0Max4096' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, ], '__stringMin10Max512' => [ 'type' => 'string', 'max' => 512, 'min' => 10, ], '__stringMin24Max24PatternAZaZ094AZaZ092AZaZ093' => [ 'type' => 'string', 'max' => 24, 'min' => 24, 'pattern' => '(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?', ], ],];
