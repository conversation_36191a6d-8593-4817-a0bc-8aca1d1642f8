{"__meta": {"id": "X1e6d861c01961639ccb1897e69ef01c8", "datetime": "2025-06-30 23:10:20", "utime": **********.79169, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.323739, "end": **********.791704, "duration": 0.46796488761901855, "duration_str": "468ms", "measures": [{"label": "Booting", "start": **********.323739, "relative_start": 0, "end": **********.725129, "relative_end": **********.725129, "duration": 0.40138983726501465, "duration_str": "401ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.72514, "relative_start": 0.4014010429382324, "end": **********.791706, "relative_end": 2.1457672119140625e-06, "duration": 0.06656599044799805, "duration_str": "66.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45348680, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01661, "accumulated_duration_str": "16.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.756496, "duration": 0.01548, "duration_str": "15.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.197}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.781859, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.197, "width_percent": 4.576}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.785152, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 97.772, "width_percent": 2.228}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1673689279 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1673689279\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1121211341 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1121211341\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-581697930 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-581697930\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1971862628 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751325019250%7C11%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhHeHZlMnJTMXAxU0hHN3MxVGxiQUE9PSIsInZhbHVlIjoiQjlMNUloeUs4UU5SUU1qdUdsWTUzcmN2U1lGbmcrYVhWM0RIVW9DS3dmS2l6d0tMOUtnV2EwVStiR0ZqakJQUENUalg3VVdtM3pVQzNnQkRBZUVGeHowb1dmZ1MyaSs1Z2I3SmpTc3dLQWRBYmhndnNhM0w0ajd2VDBCY2xidEcwTkU5N2tYYjdvME5JZDZXb25rdjRYSW45NTBJakNpZGpyMm1aNW1lWjc1TW95SjVRN2ZqRXE4M3krR0g1RnNzcmVEOTFFamYxcXZUVXJsMmpzcHhuaFNVSVJtWnU4VSsvNGlRUFA4a09Yc1lzN3hUUitQZXJwd0JMMTUreEVTMjFiWEtVS3BjdndrR1d5WTRHV3hpRUN4c2hhdnIyeHo4Yk5hZWx0ZGdzbDQ4YVM3Sm1wNGdGb2lKeHZieFhObEgyYUpINkhGcUI1ZVNpSEt5cFlUUEU5VlJLSzBxYm1nUzRZZ0JQYWNwcFpUeldDUVRVMXNJSjdDUmxYM0dWZWw0TktweG5iQVk0TUhySEUrZ2hoWkd5VHdab1IyZDh2NkRoazAwc2lKcXBXaWJyTS9TVEVjb3pVaGtkc21nd0xhUWhrMFVGY24rMWF4WGZWd3A0bHIrd1ZpNlQ2NGs1MnptM2tzWnkvTkpsN3ovbjhCUlhhSWJPTUtKY1Era09mR2QiLCJtYWMiOiIzNWFlODExNzcxM2JiNWIxOGYyOTBiNGNjZjk2MGFmNDA5Y2JlOWNjMWMyMWRiZDE2NzA5ZDg5NzE2MzBiNDQyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ikp5anlwL3RTREVyc1FaSVhBZitPNUE9PSIsInZhbHVlIjoiVUdOSXF5NUZlS2xvWFhTZmJ1UktneC9KczZtWTBWY0NPMFpjZDJCelVScGZSWjRxMUhOYVRFOGI5YTRsOG4rYzlYRVA2L1NHOWdpZStxQ0NBZUtHMWhaYUxNV3NiSTVYS3BoK1ZkdjhEN2pZdWZlUlpIN2tqQ2Fza2hNNE9RSlM0bUlEZ2toWHU5WmZxVFFLYVBTU0x5VndBbmJRQURVcXA4OTk4OTlzK3NtdjhtMFNOOHlnemZ6b1pyQ1FmelNxN2lFelhHY1V6V0plS3Y3dFhYcGlJLytKMWpSaVQ2bE9XVGMzZjJBNVo3ZnQ2M2tLYWZVMTdRZVVWVGRJaUFNSXhuQys2dVUza3JxN2ZwR0w0R3kvVk92UnZQTllGSVREMGo4TkE5TE0ycTF6N1JPVmJnMG5IS3kyUUE2dFdMMjlOZmZ5Wk1zU3JLUlpiUGN5V2N4c0h1SUh0UE93UThkeUQzOHROa2ljcUlWUmVSSnNGZmZzL1EzSTZRVVNGaHpvYko4a0VWamRZdmFjL3lvbGsxLzQ0dWdUcUkwaGg5Qnh4M3c0WTdyQnZQZnhhaUVLMkNQK0g0Z3JoRUNsZnpJbmhoL2JaWDJob1BPbjVXUTdVN0Nid00xd2I3M3F0cWtsRGpLSmFVN2dneGV4N0FoQ3ltOGhVSjhlT0NrMnF0d1oiLCJtYWMiOiIyM2IzOGYyYTEzODQwZmM3ZGQzN2M4YmJhMmFlM2MwMzkyMWNmZjY1NzJkMGRjMzZmMTg4ZjU1NTQ1ZThhMGI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1971862628\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-865284410 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-865284410\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1386267958 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:10:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik50QVc0akI1a3JrSFVRQUFnTjYzRlE9PSIsInZhbHVlIjoiZVNKSGcyd0swUThHWjdtWU1uWlh6TmkyelZWV29UWGVoc2ZuaytMRisvNFhUQkVyN3J1LytJVk5HNEFqVzA4ekM3Ull4anl2Z1JzME9qWWhjTS9hMEFCOHJrTW9rUk9RV2xMK2Z4NXlpSmg4YTlKZjFDS3hGNFd0QWRER2FPRXRJQy9PMTJSb1BrR1hUNkpjSWZqQWFJdkxFMmdkQng4RFBTaDZVcUdlQXZOVE5lN1d1ekdLUWJ5bU5DbllZOFp0K045Nk9yL1ZQMUR4R2FYdGMxU3QyMWplZTZPTWhvZkhPSWZ0VExwZSt6VnpaZzVvUFRydmtRYmU4ZXVjYWpCOEI1U3dUUXJTbzQrZ1doN1RxZzYyZVdkai8xcURtRHkrV2l5aWJ3TWRSREwxTnEwa1RlRzA5c3FoNjh0aEJoRHVwQU5RRTlpMFNpZWxlZDhPdFhqVmpuZlEyNVZGSVk4MU5YZUVPVWh0cEhpRUJBaCtVcE1LaTlNcWhsQ2tWMk5DNFlWeEJNMDFvZG94ZmsvZXBmdEhPSkV6OXdvbWwxQ3cxYWNzR05YYmVUMjQrUEVxQ3IwTkVIYThHZ1NsaGR1WWZXMWV6Qms1clVOWC9CZkN3TGZsV2szMzR2WklQcHZOWVdyTUhmbXAwLzUyckxUcXdRdC9Ya2lxNnlKR2NUaEgiLCJtYWMiOiI4NmM3ZDE3Y2Y4ZDljZjk1NWY3MGQxYTUzMDdkZDM5ODdmOThlN2M1ODE0Njc0NzE0NWViNzQ3Y2Q5YzJmNTFkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IktqUHVjRjFtV3NGdVBhakUwRllhaUE9PSIsInZhbHVlIjoiNUh6RGxGamQ2LzVVSmhub3I3cFRlR1prbzErWHFDNDZMcU9NZ2djNm9IUHltMU1JZE11aHI5cmE1S1U4cmxZb1ZwMEVtK3JkNWRJeTlFZ3JaWjVpeDVDL09sNHhGemhieDAybzM4WDc0L1djNktwQUhGRk9PY3F1am0rWXZTVm82MnRCeVNVbU42VjJvbXBIdS9ZeTk1dHA5YjZ1N2Jhc2x3NzVHV0Z0Nzg5M01ERmhKWE5ubk8yS1B6NlpKamVUMkZRNWUzekpaSmdlSE1MWEk5RDhUYVF4cTBSNkoxNHoyellQYlpOSjV5VHlNNWk0cWNzOUxqc1I2NUg2WU1nUE9yeVRxa0NVMFkzSlBmbkk4eW9vS3VsL2J6WVdKa1A5SC9iaUVmMmtpZG1JUHI1RzlwZ2xKQ2JYK2M2U2VralU4N3ZjNk5hcUw3bVFhMC9TZnBZU2ZjZ2VwUGthbDFEUk9wU0JVQ1ZFUmpnVkJ5b2pYQVdzdHVISDdNQkxPQUxIYUpwRE9aOElQTXcxTVczL3B5RHZNTEF4TUE0S0xtMGxnTGQya1F1YXhiTldTbVprQTJXR1gwUEEvdWdndXpPR2ZCQzQzZ0FtMVdPRUhXRklTTEI4SjlLUzdhYXFHQWZGZXlBZWNjYjZjWTNiRDV2VDZUMmk1amRwR2lyNGdhMUciLCJtYWMiOiJkYjA2MzM2ZmU2NzVmMjgxMWM5NjlkODEyYzE1ODQ0NjVmOWE4N2RiZWJmYWY0YzJiYmFkMzM3MGQ3YzBiODNhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik50QVc0akI1a3JrSFVRQUFnTjYzRlE9PSIsInZhbHVlIjoiZVNKSGcyd0swUThHWjdtWU1uWlh6TmkyelZWV29UWGVoc2ZuaytMRisvNFhUQkVyN3J1LytJVk5HNEFqVzA4ekM3Ull4anl2Z1JzME9qWWhjTS9hMEFCOHJrTW9rUk9RV2xMK2Z4NXlpSmg4YTlKZjFDS3hGNFd0QWRER2FPRXRJQy9PMTJSb1BrR1hUNkpjSWZqQWFJdkxFMmdkQng4RFBTaDZVcUdlQXZOVE5lN1d1ekdLUWJ5bU5DbllZOFp0K045Nk9yL1ZQMUR4R2FYdGMxU3QyMWplZTZPTWhvZkhPSWZ0VExwZSt6VnpaZzVvUFRydmtRYmU4ZXVjYWpCOEI1U3dUUXJTbzQrZ1doN1RxZzYyZVdkai8xcURtRHkrV2l5aWJ3TWRSREwxTnEwa1RlRzA5c3FoNjh0aEJoRHVwQU5RRTlpMFNpZWxlZDhPdFhqVmpuZlEyNVZGSVk4MU5YZUVPVWh0cEhpRUJBaCtVcE1LaTlNcWhsQ2tWMk5DNFlWeEJNMDFvZG94ZmsvZXBmdEhPSkV6OXdvbWwxQ3cxYWNzR05YYmVUMjQrUEVxQ3IwTkVIYThHZ1NsaGR1WWZXMWV6Qms1clVOWC9CZkN3TGZsV2szMzR2WklQcHZOWVdyTUhmbXAwLzUyckxUcXdRdC9Ya2lxNnlKR2NUaEgiLCJtYWMiOiI4NmM3ZDE3Y2Y4ZDljZjk1NWY3MGQxYTUzMDdkZDM5ODdmOThlN2M1ODE0Njc0NzE0NWViNzQ3Y2Q5YzJmNTFkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IktqUHVjRjFtV3NGdVBhakUwRllhaUE9PSIsInZhbHVlIjoiNUh6RGxGamQ2LzVVSmhub3I3cFRlR1prbzErWHFDNDZMcU9NZ2djNm9IUHltMU1JZE11aHI5cmE1S1U4cmxZb1ZwMEVtK3JkNWRJeTlFZ3JaWjVpeDVDL09sNHhGemhieDAybzM4WDc0L1djNktwQUhGRk9PY3F1am0rWXZTVm82MnRCeVNVbU42VjJvbXBIdS9ZeTk1dHA5YjZ1N2Jhc2x3NzVHV0Z0Nzg5M01ERmhKWE5ubk8yS1B6NlpKamVUMkZRNWUzekpaSmdlSE1MWEk5RDhUYVF4cTBSNkoxNHoyellQYlpOSjV5VHlNNWk0cWNzOUxqc1I2NUg2WU1nUE9yeVRxa0NVMFkzSlBmbkk4eW9vS3VsL2J6WVdKa1A5SC9iaUVmMmtpZG1JUHI1RzlwZ2xKQ2JYK2M2U2VralU4N3ZjNk5hcUw3bVFhMC9TZnBZU2ZjZ2VwUGthbDFEUk9wU0JVQ1ZFUmpnVkJ5b2pYQVdzdHVISDdNQkxPQUxIYUpwRE9aOElQTXcxTVczL3B5RHZNTEF4TUE0S0xtMGxnTGQya1F1YXhiTldTbVprQTJXR1gwUEEvdWdndXpPR2ZCQzQzZ0FtMVdPRUhXRklTTEI4SjlLUzdhYXFHQWZGZXlBZWNjYjZjWTNiRDV2VDZUMmk1amRwR2lyNGdhMUciLCJtYWMiOiJkYjA2MzM2ZmU2NzVmMjgxMWM5NjlkODEyYzE1ODQ0NjVmOWE4N2RiZWJmYWY0YzJiYmFkMzM3MGQ3YzBiODNhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1386267958\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1056776585 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1056776585\", {\"maxDepth\":0})</script>\n"}}