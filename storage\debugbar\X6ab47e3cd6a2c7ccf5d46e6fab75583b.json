{"__meta": {"id": "X6ab47e3cd6a2c7ccf5d46e6fab75583b", "datetime": "2025-06-30 23:10:54", "utime": **********.094433, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751325053.711799, "end": **********.094448, "duration": 0.38264918327331543, "duration_str": "383ms", "measures": [{"label": "Booting", "start": 1751325053.711799, "relative_start": 0, "end": **********.049098, "relative_end": **********.049098, "duration": 0.337299108505249, "duration_str": "337ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.049107, "relative_start": 0.3373081684112549, "end": **********.09445, "relative_end": 1.9073486328125e-06, "duration": 0.04534292221069336, "duration_str": "45.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43893072, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0023499999999999997, "accumulated_duration_str": "2.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0816922, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.489}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.086661, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 71.489, "width_percent": 28.511}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:3 [\n  2352 => array:9 [\n    \"name\" => \"مجسم شخصيات مختلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"2352\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2353 => array:8 [\n    \"name\" => \"مفتاح علبه\"\n    \"quantity\" => 1\n    \"price\" => \"6.00\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"id\" => \"2353\"\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n  ]\n  2354 => array:8 [\n    \"name\" => \"العاب شكل بطه\"\n    \"quantity\" => 1\n    \"price\" => \"6.50\"\n    \"tax\" => 0\n    \"subtotal\" => 6.5\n    \"id\" => \"2354\"\n    \"originalquantity\" => 9\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1637819336 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1637819336\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1214152234 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1214152234\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2818 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751325027058%7C14%7C1%7Co.clarity.ms%2Fcollect; laravel_session=eyJpdiI6ImpxTklOWXhTYU1XWEJkQTBqVTRXZlE9PSIsInZhbHVlIjoiTXFGZEpQRXlGa212dDlvcjMyZUgzOTJTdTZUUld1Y0RIbVM4elBEck42T2RTa0tjV2NoaTJNekRxM3Vkc3lrR1dOeGt5blA0Ym55U2pNZWY5am1QNC9lMm5SZE1VcGh6OVBneXMxN2Y4Qmx1TWhKckg0OXNOdzJBV1ZmTzVubEw3cmlyUDRwVnhxYzd4NmhqaEJiZThRcHlESndxYTY0WWNKZ1pqeVdFSTZuZnRmZ1BDZy91c3ZCbWloYytBQnpaczBrTmh2S3Z1dllBcHJYMkhWOXM3alFFWENqVkhpc2Rnb1lHSlpwM2dNb0s5cUxpakE5OVNEbEZRZ0VUVDJBWFZKWmZyYjZRM2ZNL1BuR0l5L3BsYXhVbzEwQ1ZlaXYySVUrQnk5a0dvY1BKK2dwQnEzM3ppZnFlaW9XTW9Ga1pSQ29mZktuT3BjSnc5ampzYU1SdTIxNzd4Wms4eUJ6eWdrajg1UUhYS2puSkhMNDVRbEh3WENJazQ2Qzk0Vlk5cE5NYW1qQ0xCbWJBVHZ5ZUlzUzBGRTJCWGliL1ovUHlOejhGSHJzRS9UOC8wK0QvTTQ4WHdveVBkTE1CdHlWcnFrQWdDcU1jR0hscFd0SUgzYWRtbFRaT0pzTGUzSkVDS2k4YlhiU0s3b29aMU5tbWtnL1NUaWZ1VmhxYnZ3ZFoiLCJtYWMiOiJjNTgzZDkxNDlmYmY2OTAwMmNmYTUwNTg0OWMwNDQ3MTVmZWQ0YWFiNjk4ZTJkZDM4YTkzNjhiMzE3YzIyNjlmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik15SUZuYWxnNXdaQk9QREhnK2xSQ3c9PSIsInZhbHVlIjoiaDBHM1NNU3N6RVVjUFZGWHV1NGI1NGFyTXhwYWkvUm1PT3NiNExVaHlOQkVHc1lYOGwxcksrdG1DTkgva0swbUpjNWtoOERrbXROUTB1T0RDQ08wb3NkV2hIUktONTVPOXgrUDZwWGg4KzlXcmdTRGN2aitsZnlRWFdDUzRmRks4TFBuQ21UdFgxWk91OGJwb1BOQzZUek02ek00b21TTWEyOGlkQ0lIemdaNStUTmFaNUdEMmJzY1p4a2hQeTgvMWFxdGlwMmFuZEo3MDVFejJIdUM4TUdhNzJITFo0RVNmM1J6ZGVGb3I0NWQrVlE5cUlkQ1ZRNDNvUkQvVEdIR1lPZld4eElUY21oWjNMZDdDRlZKV291U0lwcHhuTXpPV25ycUp6djVnYWhTZmFlYytZVEtHZjNsODBibzk0SndwNVNzVU0wTFFONGxIYm9qaEZ2ZmFpTzkvUTN3d1dFNzdBdXMvVldjT3VJWGd1K09KN3hTaUowS2VZK2g1YXI4cUhmZXlpTTBybjl2V3B4enNHdVBEU1UwcmFiQUM1cU4ybG5SYnMwRUMxY0dpV2gyWU9mMUM2WXRTU1NLdHBnYnZNbW80MHJPaDFEUU9xY1FRS2JoQTZOOTFkUnYzczhTZk1ubFRHM0VIZERtRys1WDZHMWl6Y0lmNFhhYnF2OEMiLCJtYWMiOiI1ZTAyZmQ3ODg2MDk3N2Q4ZTFiY2U2NDU4MjE5YTJmMzdjOGVjNDY0OWQ1NjVhYWE4NGQxMWY4MDc3NjJkYTg4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjNNcE9xYUVjcWpzWFptSFNUSkgrc1E9PSIsInZhbHVlIjoiN2QvSWY2bTMrS2sxL1hNcHlDY2p4MHkvbEsrV20xeXRGdXVldkJWR0xNKzkwTDhyekpsZ1lVdXE4cWhlRmpqZlE3R2F6T2FQa0kvbUR3S0R3dktMUE5TSHA2WHhlcEM5YXY5bnViTVNRdU9lbm05ZjY5MDVqdGpPaVFhck81WVNYSzRydjkwdGJzVnlYd1F3VzZQQmxudDBMdGhnTFdCU2w4WjRuRG5JZDFScndIZkF6bXltaElkLzhpUjRTQW5mcVNlVytzaXVvMjlzU25MSE15dGpjb21vdVNNQU9sajFHMmVGSlJWcTNJdm1JOGdORXdXaDE2VnhYaUJaYXNWcUlERnFEN2FtWWxaRkhjNkNCK2ZOTkVTdG1LeUs4aXVNZ0tCZWlQZWowQ1pMdWJjUHNwVHBST01CNmU0bmVCRGQ5Z0NpcVYxREE3eEttcE9FaTJkOGJJT1o3a2FKTzdPUngreFF4bmJHcjVVWml5dGZ2aWRNNzBWakZoaFh1U21tMUhmSm4vMXJIZmwrLzZPODdXK2hmN3ZlbTRBd2FZcC9uSTdRNkJpdEY1d1U5Q0laSmhFbUNDWWZqWWZqVnVoQ1d5RWJCc3VyREdNOG91Zmh2SDYyYzRlaFNNZEZrM05Tb0JHNzhVTnFLVmlVdnhCTG1RNzdmNWlkYjVhQURVb0QiLCJtYWMiOiI4OTA5MThhYzk4YzRkNzA4NjUzNzhmZGM3YjlhOTQ2ODFmMWJlZTRhMGMwY2MzMTRlNzk4Njc2MjQ0NTRmNWQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1855142228 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nMXQGGGSGQFIfGwlrnEYCl0LIB0DtCvcZqQRlbGj</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1855142228\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1999033217 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:10:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZZSWJMaDJHT0Jrc1hzWVMvWGhOZEE9PSIsInZhbHVlIjoiYmRUaERrYWNORlF0S0xvajFXMFdZdU5ubU9SZkRDdVk1TFNKd3RLcXJmNTMvTGN4Q1k2SzlHampER1VHSko3Z3pCcTZ4dFIwM3pmQWduZUV5Z1hoTkIvY2hiS2ppNlpuYmZWQUtiQ090RmpNZG95TytDS2pWcWVnSjVOK0ExbnVXNnNuZlJ2cnJzUDByakpBZW4yZHl5NXUrenFldEFKRlpldTlMODZhcVRBaGw5dGtlZGgveUZ1TVJwTkdKWFp3R3l4L0YzaWF5REdRTE1YNDJHL01ieFJiVzM4ejdLTTlHY3dkOFlBajU4S1h5cDNxQTRMbmJ6OTEwYy9xMHl3b1VUNk10TVZ0L05sT0lKWnk0RU8vTTR3R2o1anlNYmVyZGdWZUlWNStQUHBaYWdjTGpPajlCaVBWUDN2ZmJjbmpwRkZZZUVaS1VEbEF1dWtlUGoyYURnamloTE9MMllVcldodWhSWXczQVBieDd4Nk80YW10ai9ZMFBSVWQyWjZtTFA3SE9Cc2FsYUIzNU14alY5Sy9lbDZlak5GUnNjdmpmSzFCV2FZL2phc2plaGZ5N05NTFVZT09hdkpCeXQ4ZTRMY3dKbkJkWUhXY0VhUmp6d0lNWHpaei9GRFplR2VEWkdCdTVWdDlQaWZpbkNzakx3MWJDejJneHpuMmRtMGwiLCJtYWMiOiI3N2EyMzlmNDk1N2M4MDhhNWRkZDIxZGUyN2ZjZjYwMzA1NjM5ZjRkYzRjYjAxZjFlYmJlMmNlMGYwOTI5ODQzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlB2bU96MVRjaWFMd3BzVFNOMkliZmc9PSIsInZhbHVlIjoieUcyaG92U0x5aHMxTFMxOUJvWElIVU9XUmdIcFpRN2d0cmh6NjBPUGxuWUhnd3JaQ09ZWFU3REt3a3o5NTFVRXRDQ2QwcnozVDVKTk1HYmZ3YkxoL0FBaWRzZFEweER0UEQyU2JsU2xDWEdYRzlvcWowVHZ5b01pMElCYlI0SHZ3YlpsV1RmSm5ITGF3NHpCUERCQ0xSVmpGR3YrcXdGUnNGaExXaXZkN3B3ZzNLMnJzVG5NMy96WEpLeHR3Ti9OVkVnTGdLVlAxcmU1bEcrWjNMQURGM1lQSzN4UmF6QjhGWnFWQ0pWbWEzeHNoeEo3MXdldXA3aXhlc0Nubmw3UmZ0elVXbWtOUWkzdjNucDY0R25GNUwzNy9LNzBXZ0xXOWRaZTRRTmljWHBpT3FmT0k3bGg2emQ3b1FCUkQxN3A3alJlZkNRQU1uSDRCQmI4ZVNpZmYrNFVveC9zcU50NEY3QlZraEhsNFcwTmVJMHUzWDJFT05NaGxsSlFPNHJIeUNvNnp1YjBuNWVyZytjQUFocjZDZWZQdTgxM0FUK05zQkdPazc5ODV1M2J4MzNVU0s3amUyMlJ2UVJNM0FHQ0lkUU9WTTVwUmhkUVU2UFRkcmlaQW8yZ0YrelpnRWZ3SFBWTllIQzRkd2xrVVg2M0tFYjhMRytjcDNaN3ZrcjAiLCJtYWMiOiI5ZDgyNjdmMWVkZGUwMjNlODZhMzYwNzRmM2VhZDJiNjliM2FhOGI1NTMyMGFiODQxMmUzNjM1MTNhNDQwZmYwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZZSWJMaDJHT0Jrc1hzWVMvWGhOZEE9PSIsInZhbHVlIjoiYmRUaERrYWNORlF0S0xvajFXMFdZdU5ubU9SZkRDdVk1TFNKd3RLcXJmNTMvTGN4Q1k2SzlHampER1VHSko3Z3pCcTZ4dFIwM3pmQWduZUV5Z1hoTkIvY2hiS2ppNlpuYmZWQUtiQ090RmpNZG95TytDS2pWcWVnSjVOK0ExbnVXNnNuZlJ2cnJzUDByakpBZW4yZHl5NXUrenFldEFKRlpldTlMODZhcVRBaGw5dGtlZGgveUZ1TVJwTkdKWFp3R3l4L0YzaWF5REdRTE1YNDJHL01ieFJiVzM4ejdLTTlHY3dkOFlBajU4S1h5cDNxQTRMbmJ6OTEwYy9xMHl3b1VUNk10TVZ0L05sT0lKWnk0RU8vTTR3R2o1anlNYmVyZGdWZUlWNStQUHBaYWdjTGpPajlCaVBWUDN2ZmJjbmpwRkZZZUVaS1VEbEF1dWtlUGoyYURnamloTE9MMllVcldodWhSWXczQVBieDd4Nk80YW10ai9ZMFBSVWQyWjZtTFA3SE9Cc2FsYUIzNU14alY5Sy9lbDZlak5GUnNjdmpmSzFCV2FZL2phc2plaGZ5N05NTFVZT09hdkpCeXQ4ZTRMY3dKbkJkWUhXY0VhUmp6d0lNWHpaei9GRFplR2VEWkdCdTVWdDlQaWZpbkNzakx3MWJDejJneHpuMmRtMGwiLCJtYWMiOiI3N2EyMzlmNDk1N2M4MDhhNWRkZDIxZGUyN2ZjZjYwMzA1NjM5ZjRkYzRjYjAxZjFlYmJlMmNlMGYwOTI5ODQzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlB2bU96MVRjaWFMd3BzVFNOMkliZmc9PSIsInZhbHVlIjoieUcyaG92U0x5aHMxTFMxOUJvWElIVU9XUmdIcFpRN2d0cmh6NjBPUGxuWUhnd3JaQ09ZWFU3REt3a3o5NTFVRXRDQ2QwcnozVDVKTk1HYmZ3YkxoL0FBaWRzZFEweER0UEQyU2JsU2xDWEdYRzlvcWowVHZ5b01pMElCYlI0SHZ3YlpsV1RmSm5ITGF3NHpCUERCQ0xSVmpGR3YrcXdGUnNGaExXaXZkN3B3ZzNLMnJzVG5NMy96WEpLeHR3Ti9OVkVnTGdLVlAxcmU1bEcrWjNMQURGM1lQSzN4UmF6QjhGWnFWQ0pWbWEzeHNoeEo3MXdldXA3aXhlc0Nubmw3UmZ0elVXbWtOUWkzdjNucDY0R25GNUwzNy9LNzBXZ0xXOWRaZTRRTmljWHBpT3FmT0k3bGg2emQ3b1FCUkQxN3A3alJlZkNRQU1uSDRCQmI4ZVNpZmYrNFVveC9zcU50NEY3QlZraEhsNFcwTmVJMHUzWDJFT05NaGxsSlFPNHJIeUNvNnp1YjBuNWVyZytjQUFocjZDZWZQdTgxM0FUK05zQkdPazc5ODV1M2J4MzNVU0s3amUyMlJ2UVJNM0FHQ0lkUU9WTTVwUmhkUVU2UFRkcmlaQW8yZ0YrelpnRWZ3SFBWTllIQzRkd2xrVVg2M0tFYjhMRytjcDNaN3ZrcjAiLCJtYWMiOiI5ZDgyNjdmMWVkZGUwMjNlODZhMzYwNzRmM2VhZDJiNjliM2FhOGI1NTMyMGFiODQxMmUzNjM1MTNhNDQwZmYwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1999033217\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1925599949 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2352</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1605;&#1580;&#1587;&#1605; &#1588;&#1582;&#1589;&#1610;&#1575;&#1578; &#1605;&#1582;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2352</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2353</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1605;&#1601;&#1578;&#1575;&#1581; &#1593;&#1604;&#1576;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2353</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2354</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1588;&#1603;&#1604; &#1576;&#1591;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2354</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1925599949\", {\"maxDepth\":0})</script>\n"}}