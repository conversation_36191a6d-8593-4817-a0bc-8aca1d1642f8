{"__meta": {"id": "Xe361f8f37780d883745cb9d481a3ad81", "datetime": "2025-06-30 23:13:26", "utime": **********.409346, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.921613, "end": **********.409363, "duration": 0.****************, "duration_str": "488ms", "measures": [{"label": "Booting", "start": **********.921613, "relative_start": 0, "end": **********.328735, "relative_end": **********.328735, "duration": 0.****************, "duration_str": "407ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.328747, "relative_start": 0.****************, "end": **********.409366, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "80.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020870000000000003, "accumulated_duration_str": "20.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.360378, "duration": 0.019870000000000002, "duration_str": "19.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.208}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3909519, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.208, "width_percent": 1.869}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.400482, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 97.077, "width_percent": 2.923}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325166735%7C25%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjVCcWVkSTVHL0I1a1cwSnFjaFZ3RlE9PSIsInZhbHVlIjoicHRYRmV6V2I1REJZZXNXVmRkWTE1OUwyZnFqNFdWTFJ4WmdVUG1zbG1GcTNIUjdsSHo3dHkzQ0ZDWmlIRFpNQWpUczhSUTJvemxiVHZXUWhkQnNuVko2bngrS0gvcURHUFVsZkJHcURwN2pWTFNVVEc1Y0phMVBUWGZrSS85cG5CQW4yQlZ5ZFIrZStINDVpc09YeU81QmFMRG9qRERVWC9sTFdVVElmV2IybndsZDlxLytMWmw5MTMrTUcvZDFZRW56NmtBa09INUhPcCtlK1M0UTlFVzI3ZzlGNytCSThWVTB3YzdmTDFNcHlINCtMTEhGTmNEbHdhMll1amNFT3phRTRVZmErNy9RQWM1QWQ0dHNUSm5zSXc0MHBqNEVyUXBJeWt3eThpM0l0NUQ4eS81dUdGVUFnL1ZsSFprTjV1Wmh5MUs4TEpweEVzT1pCTGpyWFZadkQvbjd0K1RMTkFhdVU4eHpZcWx5RWdDaWpYdE1nREF1MnJ0QVdHZDVja3FFeVcyUkhTV3RLL0FtSEk5NmoxSDZKNm0veFRqNllQRFBCa0lFNm1Fd2FnTEtFK0ppWk5aK2h3WU8rN29UVVJnektLaVpsQXJxa1ZTYTgyVndCSFlhZ3BjSTlWQytQbU01MjB5eHBCMnIwRXdCclFIaVFwVlU0NC9iMGNoUGMiLCJtYWMiOiJiZjdjMzM1NjllMTc1ZTY0MTRmYzdiNGZmNDIwMGE5OWI3YzMyN2FhNmNiYmI2MGY4YzBhYTBiZjIwMDY5ZTJhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkttME5zSnhTdjB0Q3RkcWt5MzYzd2c9PSIsInZhbHVlIjoiSTgwcE12WHg4cGpaZnc4TndiT0ZDbGdFL1FuZ0FTQ29WRVVhZzB6S3E4VjBjcDBaZXVuT20rU1ZHbGtrcTIzNlhycVlIN1F4c01naGZrT2tlaWR3T3EvSUY0VExWVzM1STlSUDZqUVgxM3BoSmVVbEU1aU1iMTFYOCtNemlTb2FueUJQK3Z6TWtETEZ5dzR5ZkZncktCU2hTOEpZdHFmT0hBVXN1cUNNelNVSjd2NkROUkRIdHlaalJqb25ZVXZLK3FoVnowMENEeGZsQnlPNEhIWW9xbUNsSE1TSWZUYlE0cHFKN1FnTXFPSC9nNFVtNXdveEJ5VEwxZ2prTlg2SSs2WDB4WHJYek9kRDdiaVRpNStHMGpYMkQzc3FsRFlhVHovRUxTekJ5M1BXcWNaeDUvSTVzWXlsaER6Tkhpa2hEQVhzOW1sZTRQcDZ0Zmcvb2ZYaTgwM2JGcWpJUUJLdDRUamdoUnhMb0tCMHcvaDBSQkVEL29qUW5oMHdJNSttOUxMVzNWdWZOb2xsKys4aU1WZ2Rsc2VZQWgxUGNjZFBmcUlpcnYrYWNjdUFyRm03QWVrL0hramp1SFgwK0hzQy9WQ3NQMmdkbkN3Yy9COTVUc09WaTJuYjhlYmhOTkxEYnF0aVBIelI4eXJJMzJ1MTE0U3dFK1U2a2ZEQ05NeFIiLCJtYWMiOiJlNDY4NDljMWE2MmUwYWI5NDMyOWJlNDFjNGZjOGNlMDdlOTkwNTcyNThhYWM4YWU4YWI0NTQzZDViNzZmNzA1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2108747492 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0aUeGT9vcZQoKvixhqnXLFDjX39UXyfabqLnLub</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2108747492\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1916275529 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:13:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVGbTFrMzYxWEZNQXhJdXBsemZyR2c9PSIsInZhbHVlIjoiNkx5eU1pSklTSHVobU4rMVBqSGhlb05CTzVNU25pOHFObkRpTnN2dlVpNjIvMlhPY2dCVGpKbWRYSHZVOGJxQXJuaktKZ0liZ1VnV3Noem0rMWJIUS9HS094aXJyMW51eTk3TUdXNnA2ZGdmSExNWnE2alFMUzYzUWgwUzNLOFNSNGFrZ2pKRUdUUERKTzhpRlE0Wmd6Q1pkdEMwTFd2WHV6Tk5BZjVuUXpCZjVlNmxmRlNzeUtFWWJrQ1M0SGc1bk5QV1Z2Mk5SM1VWc3MzSW9abXBIRzFnMzE1NndUc2VKWUtGUnVUU3VHMlJjR1I2WkI1aEJDd0NYZXRudnk5NE5ya2tPT0RWS3dmRERlaDI1ZXpkZXZINU9KZ2VMNW9HVTZEak9TTk93cWExQ2FEb012VHlnbWdGVDlvWXIrc1hqQVhkUGRsalA3WFE2N3FNREFYdXF0Sm8vcE9BLy9pSlEzZENjOEFzTXFOczk2YTRaUGpYSnh4TUdiRHNqek9yaS84bm1mYUtzT3B3VWY4WTRXL0hzeWFqQjY5czVvZHpBVXhFZW5FR2Vja1lIWkRKd25VSE01VzRGQUNBR2t2dFB5UFhZVnlVdDdqckZPOXltQnM4clhNOHFiMjdYUlltbks5bWxPTjFIa29wZ1VpQ29iUGpvWGJQTUh2OEFKVVgiLCJtYWMiOiI5ZGUzOTlmNGRkZDFkM2U3ZjFjYTNlZDhmYzg0YTg2N2RhZGFkYWNjZGVmODQ0Zjg2NTNlNjM2YzY4MTk1YWE2IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InVIV3F0SnZqUWE2OEJOWTJ6ZllUQmc9PSIsInZhbHVlIjoiV3gzQ3F5djYyVHlrdXBCd0RNcXFSa1B1cFR1VHRDR1FJZ0R3SHdoWU52eUpCNFhnRmlZUDNqbUVHMTFtamhRREFjdkNQYUl2S0lSYm5YNTM3WlpTTkIrTmpCcWN0Nk44ZTlOSldkT1F6TDd5OU5PK1J2emNNcTExQUExMDFDN1luYWsxVUpRMUZYRzFhQ2IzaFdhRm5KYWlxYTd0VCtQTC8rdFQ4S2VuYlEvdkloYnJ6MlJVRXdUUUY2WWk1anhWNTVPZ0Q3d0cyc0xGK1ovMTR5NzZ5d0I0SzlhdHBEa2Y5NjRzcnBJN0tLU0szbWY4c2QybmdQc3VHSjRHSGhTcGNyd3VtNEdXVUc4Ynl2eGlrT29zWW5jMUNtRjNwSXl3elh2VXpKQXV2ZTEvUEFkaUI2Qyt0WndhUG8wK0NEejBCZ1pSL29tNFA4ZFpZTTJWS3Vpd0tWcWRseFl0MjBnWGtqZys0NXpZaHJxc0dIZ3hlQ1RzMkpBTHZLMFpLUXp3aEVUSWJZakJJdENQVnVJRlJwbS9tS01iM2Z6VWZHTXZjaGFqZUFtT1p3clIxNVFXRitYMUdUVG5zTXJndzN6Q2ZLNTFXVTluOEpHKzdZdmh6QWo3R290VWcyUFRyL1FrQzZlaGx3Y3dscExvV2hYWU9OUEFrRWVVNkl1ZXpYN1kiLCJtYWMiOiIwZDMwYzVjNTBmMzYzOGZlOTkyOGIyZGRiNjRiMjU2NDJiYzM0ZDAxYWM5YmRjNWQ0OWE0YmNlOTVmNGRlZDU3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVGbTFrMzYxWEZNQXhJdXBsemZyR2c9PSIsInZhbHVlIjoiNkx5eU1pSklTSHVobU4rMVBqSGhlb05CTzVNU25pOHFObkRpTnN2dlVpNjIvMlhPY2dCVGpKbWRYSHZVOGJxQXJuaktKZ0liZ1VnV3Noem0rMWJIUS9HS094aXJyMW51eTk3TUdXNnA2ZGdmSExNWnE2alFMUzYzUWgwUzNLOFNSNGFrZ2pKRUdUUERKTzhpRlE0Wmd6Q1pkdEMwTFd2WHV6Tk5BZjVuUXpCZjVlNmxmRlNzeUtFWWJrQ1M0SGc1bk5QV1Z2Mk5SM1VWc3MzSW9abXBIRzFnMzE1NndUc2VKWUtGUnVUU3VHMlJjR1I2WkI1aEJDd0NYZXRudnk5NE5ya2tPT0RWS3dmRERlaDI1ZXpkZXZINU9KZ2VMNW9HVTZEak9TTk93cWExQ2FEb012VHlnbWdGVDlvWXIrc1hqQVhkUGRsalA3WFE2N3FNREFYdXF0Sm8vcE9BLy9pSlEzZENjOEFzTXFOczk2YTRaUGpYSnh4TUdiRHNqek9yaS84bm1mYUtzT3B3VWY4WTRXL0hzeWFqQjY5czVvZHpBVXhFZW5FR2Vja1lIWkRKd25VSE01VzRGQUNBR2t2dFB5UFhZVnlVdDdqckZPOXltQnM4clhNOHFiMjdYUlltbks5bWxPTjFIa29wZ1VpQ29iUGpvWGJQTUh2OEFKVVgiLCJtYWMiOiI5ZGUzOTlmNGRkZDFkM2U3ZjFjYTNlZDhmYzg0YTg2N2RhZGFkYWNjZGVmODQ0Zjg2NTNlNjM2YzY4MTk1YWE2IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InVIV3F0SnZqUWE2OEJOWTJ6ZllUQmc9PSIsInZhbHVlIjoiV3gzQ3F5djYyVHlrdXBCd0RNcXFSa1B1cFR1VHRDR1FJZ0R3SHdoWU52eUpCNFhnRmlZUDNqbUVHMTFtamhRREFjdkNQYUl2S0lSYm5YNTM3WlpTTkIrTmpCcWN0Nk44ZTlOSldkT1F6TDd5OU5PK1J2emNNcTExQUExMDFDN1luYWsxVUpRMUZYRzFhQ2IzaFdhRm5KYWlxYTd0VCtQTC8rdFQ4S2VuYlEvdkloYnJ6MlJVRXdUUUY2WWk1anhWNTVPZ0Q3d0cyc0xGK1ovMTR5NzZ5d0I0SzlhdHBEa2Y5NjRzcnBJN0tLU0szbWY4c2QybmdQc3VHSjRHSGhTcGNyd3VtNEdXVUc4Ynl2eGlrT29zWW5jMUNtRjNwSXl3elh2VXpKQXV2ZTEvUEFkaUI2Qyt0WndhUG8wK0NEejBCZ1pSL29tNFA4ZFpZTTJWS3Vpd0tWcWRseFl0MjBnWGtqZys0NXpZaHJxc0dIZ3hlQ1RzMkpBTHZLMFpLUXp3aEVUSWJZakJJdENQVnVJRlJwbS9tS01iM2Z6VWZHTXZjaGFqZUFtT1p3clIxNVFXRitYMUdUVG5zTXJndzN6Q2ZLNTFXVTluOEpHKzdZdmh6QWo3R290VWcyUFRyL1FrQzZlaGx3Y3dscExvV2hYWU9OUEFrRWVVNkl1ZXpYN1kiLCJtYWMiOiIwZDMwYzVjNTBmMzYzOGZlOTkyOGIyZGRiNjRiMjU2NDJiYzM0ZDAxYWM5YmRjNWQ0OWE0YmNlOTVmNGRlZDU3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1916275529\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-995908036 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995908036\", {\"maxDepth\":0})</script>\n"}}