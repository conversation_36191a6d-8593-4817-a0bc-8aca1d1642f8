{"__meta": {"id": "Xbd0e6292396ff8a661a61e50940a6973", "datetime": "2025-06-30 22:36:59", "utime": **********.97826, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.553371, "end": **********.978278, "duration": 0.*****************, "duration_str": "425ms", "measures": [{"label": "Booting", "start": **********.553371, "relative_start": 0, "end": **********.924754, "relative_end": **********.924754, "duration": 0.****************, "duration_str": "371ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.924763, "relative_start": 0.*****************, "end": **********.97828, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "53.52ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0034999999999999996, "accumulated_duration_str": "3.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.952485, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.286}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.963649, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.286, "width_percent": 14.286}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9709558, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 78.571, "width_percent": 21.429}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C**********449%7C9%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijl5WWxDR3c2U1RyZWpSbkxVaElEWVE9PSIsInZhbHVlIjoiSkRjUnBuSEduTDJVeENkSDRXWlRackxCa0RjSmxOTXlYWGg5bTIzWWE0NUlXVTNjcWRZRDdVZTVjVE0zSTdiV2Y3NDFsdlhudGlTK0J0L3lNTVU0UHBMMzNCNGV3NXFxZkUvcEFnVHpFRFdLeThacmNBRFNQUXJhd0lxdVhKb3ZTSVdidjhBNUt2QTJPN1hHRC9qbWRTMkVaVk9lYlFpOGd6Y3lPaUMzblRoSEZaRXRuYmJ4c0o3NmJLR0Y3UHg0d0FHTWtjRmh4VjhtZVNEbXdDSGdIYkpLWDlJWmNYMXRtQWdqbFdBSkdacFZNcmZEclJkUjhsOWRJTE1WdDdCT0ZhSUVQaG0yRGNTeTIzWGNjZzhocTNWd2hvcUNUZjZESm1YNnJzMU5lQUhvci9qcGgrRjl3N3ZmdmdhQ2NNWElWWUJhM2pmdmZZajRkWUhQRy9RdGttOWZzdkhJVzhud3RhZHJPYm9TamJXVEIxWnZWc0QyaU9NMlR3T2RNYzM2bWpGc0E3V1NuY2RVMHF2eW9LVjhYT2crWnFkTEdYbGRyL1NmYVVLdlAzQlE1bEtkdHoyL1FYdVZEcXpXRytQeGNoOHEvMm1mQWl1UU81bXFFQ3VMd2llZVVndHA3OWpKQy9rMzdYNThCNHFpeDdYWnB3dnlvb25rVkhLSkc2K2siLCJtYWMiOiI4YTc5OTM0N2ZjNzk2NTBkMGY5OGE4MTQwZDYyMDJmYjUxNTBhZWZiZmM5YzliNWI1MWY3MmU5YjAzMGUxNTFhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InMzYVR1amxLQ0hKYXovQjh4OTJPVEE9PSIsInZhbHVlIjoiTVM5ZHNBbmQ5S3hKNExBYzVDTFBtWHAvRVhkdk1VZ01rZmRVM2F3dyswSzdpMkgreGZ3V0FSQkxWT0F2RDFYTDVDU2hWUGpwOVdSWWU5TGpaOXRFZDZrUWFwc1NTckRuZFVBS1ZaTkZNSmU1cDJkak0vbUxkL1F1Nmo3SHo1OTNPQUQrTkJqZHhOQ1p6V0VDU3VGbjFkNXNudUlSanNpZEtsclVZMndqSDMrQWxXckRqRzdNajB3Nk5nK0I0RWQwby9JR090dWxtRTZQanBrNVI1M25xNlQvZjZqQlNFR1NCb0pXRmtiUjJJaFl4TVp2Z1ZHai9WY25mdTJPZG0vejFWY29ZL0VqT0RSeXpRbnUxMWhFb3BpTC84QzhpRXZtWUhTZFlJU3ZLUWhsQmpjTE9yWWRlR2ZQcGY1Sy9aY0FyNTZRREp6SG13ZkJNZkEvSWVMendHdFMvaTYrUTVaLzBGYzErMGpOUkJvcnhTaElEeUR2WmY4U0RZWGd0dUE0WkVqY0NyNStjR0ZmcEhuTHlDRGdRc0s0d2JkMEIvZFEvWUJuTU1Va0kxalZBRG9JSXZ1NkllRUNyOUlPVWVBVWZOYmwrektwVTZSL0tIWFdWZUU4aExCanNoTFo0REJUYTZ3dVgwUTFSN1g3YWQ3a1N3cUhFeTd4MU1kZWZPRDkiLCJtYWMiOiJhMTdkODNmN2ZhOTJmYTAxZTk3M2IwNWIxNWJiNzkwODdkYWMzMzRkODc5ZWNlZDY5YTU0NDdjZmQ3ZTIwZWJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">K7EIYCLnalanTBUASCKMEOK1yUmooVr7ha2cCSMP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-982315765 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:36:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNReGRRUTkxN0k3V3U0NmsxTjJlaHc9PSIsInZhbHVlIjoialcyaWNpV1V2MGkrRlo1bXVubWtzdllSRC8rQ1FGS2QrYjlKUWRzUVNvTngxbXdxbk1sQ2pBWE0zWFpQbzFvb01ENUZtQ2J5djZoUnNpUENKZ3JxSHE2NHBMQzlwQ3grV2NTdFZYQWJrWDBRbytWUTdOQjdhTVVkSnBRRE56b2grU0dhMUd6U0lLd2NxRmtmc29rODhSL1J4OExZRFp3MkJwSkRIcXUwWktvWVVqdVppRkhCcHZ2YmpiSHFHdWZSS0dQd3YvelY4ZVZTRUgvZmtUL2pwSUFiQVBTQXMzNXo0RTEwNzl0T3E1eVk2bm1ZbEY4aFdXMCtlOExXSkEzL1lSK1piSE1wUFNxVkZRRHQxRjlZTEdCNkJKTmU5MVl0Z1FDRy9Ya1lEZEtnMWMvUHVZM0RBWm1FajJVdlNWeEdidTdWalNETThpTUdPc3pxZ29CWG9zUDN2dDQyUUJYMXViUjY5TUw1b2NDcFVyWEJFSnBVY3BRdnVrY3VHeWdWRVN0cVBCWHpQaktubTdWckE2RzVxcjFoRW8rUFRRRFJaaVZCbVN3MTZsM1ErckhYQ0wweWRPc0JPWHpTakdRdWhCc3JiNHdOVE9MSTZOWGFpaWYrWXZ2Yit6c3FYc0lXOGRib0xrc1ZjZlBNOEs5emNQdVVralNvTW9OR2pjUlIiLCJtYWMiOiI0ZmU1M2U1OTRlMWZhMzRmYjRmYmUyZjhiNzc1OGM4NGUyZThkODkzNGM0MGE3Zjg2MzQ1ZjdmNzQ2ZjI2YzNhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjVvNUVZZm9IQ2YwZUZDWjVxNUFmRGc9PSIsInZhbHVlIjoiZEwvYXdBMlVMVkZjc2VweFdDUURlNHZZcnBXVmV6dVlYZDIrNzdFV015VTRsaFJOT2lQZ08zZjVZS0dydmxTTXNsVWpueURZWGJvTk8wZ29mUUh5N1IrMU13dHd4bXJrR1gwTGQwNkczMlJwTm9oS3czWG9waFJNUzU0QXJhOVdZekpSOFozcktjWEdyd2NwSzd1TnlDZXB2NkFLdW1CWXBWcFFENU9ENGtMOEFtS0RVc2w1ODdBdEtST0hraGh0YUdTb2JubDFlM2VGZE44LzRqdWRWN09XSkVISDdraCtHdVhkVCs0ci9qU3N3VWdnZEd5K2V0WlFaWUZGdStnRmtaU1o5bnBLMkhONk1OWnZiamtXUkliUzY5dzFaS3VkeHdWdVdVckhkV2xWUWpuSUFQb3NVcU5reEhiZGorcncyS25FRHpMNlpXQzJFSmh1K0JzSzM3RVhMYzJvK1c2SXJ5NXFqL29tajZHellVVEE4UjB3aWRhNnh1Umx0TUhmNGw1cUR5Qk5oTmJnZkpsTmZZa3pHT0RHZlJYN1gvT2Q5UHZBUEVzQTFJTUo2T0lDaVhiSi9tbjRkeGsraWhiV3BwendHODBzcUZsNEVHMnlNbEp2c09WMUorUjFBQldpVnUwbHpWSk5mcEYzbStRS3JpdytqcXlJQlhmVXYwU3QiLCJtYWMiOiIwZTJkNzExMWFjNWNlM2Y0ZDQyZTJkMWFlMzcxNjczZjMwYmRhMTlhODhlMTM4YzVhOGIyMjI4MzFkMjUzNTJhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNReGRRUTkxN0k3V3U0NmsxTjJlaHc9PSIsInZhbHVlIjoialcyaWNpV1V2MGkrRlo1bXVubWtzdllSRC8rQ1FGS2QrYjlKUWRzUVNvTngxbXdxbk1sQ2pBWE0zWFpQbzFvb01ENUZtQ2J5djZoUnNpUENKZ3JxSHE2NHBMQzlwQ3grV2NTdFZYQWJrWDBRbytWUTdOQjdhTVVkSnBRRE56b2grU0dhMUd6U0lLd2NxRmtmc29rODhSL1J4OExZRFp3MkJwSkRIcXUwWktvWVVqdVppRkhCcHZ2YmpiSHFHdWZSS0dQd3YvelY4ZVZTRUgvZmtUL2pwSUFiQVBTQXMzNXo0RTEwNzl0T3E1eVk2bm1ZbEY4aFdXMCtlOExXSkEzL1lSK1piSE1wUFNxVkZRRHQxRjlZTEdCNkJKTmU5MVl0Z1FDRy9Ya1lEZEtnMWMvUHVZM0RBWm1FajJVdlNWeEdidTdWalNETThpTUdPc3pxZ29CWG9zUDN2dDQyUUJYMXViUjY5TUw1b2NDcFVyWEJFSnBVY3BRdnVrY3VHeWdWRVN0cVBCWHpQaktubTdWckE2RzVxcjFoRW8rUFRRRFJaaVZCbVN3MTZsM1ErckhYQ0wweWRPc0JPWHpTakdRdWhCc3JiNHdOVE9MSTZOWGFpaWYrWXZ2Yit6c3FYc0lXOGRib0xrc1ZjZlBNOEs5emNQdVVralNvTW9OR2pjUlIiLCJtYWMiOiI0ZmU1M2U1OTRlMWZhMzRmYjRmYmUyZjhiNzc1OGM4NGUyZThkODkzNGM0MGE3Zjg2MzQ1ZjdmNzQ2ZjI2YzNhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjVvNUVZZm9IQ2YwZUZDWjVxNUFmRGc9PSIsInZhbHVlIjoiZEwvYXdBMlVMVkZjc2VweFdDUURlNHZZcnBXVmV6dVlYZDIrNzdFV015VTRsaFJOT2lQZ08zZjVZS0dydmxTTXNsVWpueURZWGJvTk8wZ29mUUh5N1IrMU13dHd4bXJrR1gwTGQwNkczMlJwTm9oS3czWG9waFJNUzU0QXJhOVdZekpSOFozcktjWEdyd2NwSzd1TnlDZXB2NkFLdW1CWXBWcFFENU9ENGtMOEFtS0RVc2w1ODdBdEtST0hraGh0YUdTb2JubDFlM2VGZE44LzRqdWRWN09XSkVISDdraCtHdVhkVCs0ci9qU3N3VWdnZEd5K2V0WlFaWUZGdStnRmtaU1o5bnBLMkhONk1OWnZiamtXUkliUzY5dzFaS3VkeHdWdVdVckhkV2xWUWpuSUFQb3NVcU5reEhiZGorcncyS25FRHpMNlpXQzJFSmh1K0JzSzM3RVhMYzJvK1c2SXJ5NXFqL29tajZHellVVEE4UjB3aWRhNnh1Umx0TUhmNGw1cUR5Qk5oTmJnZkpsTmZZa3pHT0RHZlJYN1gvT2Q5UHZBUEVzQTFJTUo2T0lDaVhiSi9tbjRkeGsraWhiV3BwendHODBzcUZsNEVHMnlNbEp2c09WMUorUjFBQldpVnUwbHpWSk5mcEYzbStRS3JpdytqcXlJQlhmVXYwU3QiLCJtYWMiOiIwZTJkNzExMWFjNWNlM2Y0ZDQyZTJkMWFlMzcxNjczZjMwYmRhMTlhODhlMTM4YzVhOGIyMjI4MzFkMjUzNTJhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-982315765\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-118359989 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-118359989\", {\"maxDepth\":0})</script>\n"}}