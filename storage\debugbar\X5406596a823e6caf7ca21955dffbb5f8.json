{"__meta": {"id": "X5406596a823e6caf7ca21955dffbb5f8", "datetime": "2025-06-30 23:10:27", "utime": **********.139468, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751325026.68546, "end": **********.139482, "duration": 0.4540219306945801, "duration_str": "454ms", "measures": [{"label": "Booting", "start": 1751325026.68546, "relative_start": 0, "end": **********.083341, "relative_end": **********.083341, "duration": 0.39788079261779785, "duration_str": "398ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.083352, "relative_start": 0.3978919982910156, "end": **********.139484, "relative_end": 1.9073486328125e-06, "duration": 0.056131839752197266, "duration_str": "56.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45724040, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0028100000000000004, "accumulated_duration_str": "2.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.111543, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 57.651}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.12287, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 57.651, "width_percent": 18.505}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.129961, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 76.157, "width_percent": 23.843}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2007141023 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2007141023\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1711260189 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1711260189\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-933071474 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-933071474\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-394228896 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751325022992%7C13%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImFObGhTcG4raUNyYkxWejRUNm9RTHc9PSIsInZhbHVlIjoiUmQzR09ldGp3aXhaTlhMQTI1aDZZbGNzVVl0NkErSHJ4bFc1bG8rM2x4TW1RdEdpZkdlSEVMdzc3MXFCVVROUGIza1hDQTMwOW9OQW5GSjZLZzJXK1lnZFJTMnpSeUFuOXE0UU9aWTAyRjArNFdsay9wNmlNNlBUa1hhUUZralVYZUZ0OHoxU3JTNU1MUTZDRUlncloyT2E0TDNYaFQrdHY2SHlBaW03aE1mVUVieitvcWRsVXVwZ0V6cS9mZTBZSTN1Y1lqY09Ba1IrMFFGb25nUytyTkQrbGhidFV4ZFNHOFV1OVdUNzV4cEdzdmtveERmblk3blhXZjJDWG1Jb2hhMjdKeEJERkIvcUhyS3Y5bG8xbkFidlVrcXBvN1FPdkE1OG9SM2V3c0VoNVhTSk9nZlpoaGZRVGtDME55eXlMS2pvMXRGMTV3T0Zid2Ftdm54cDl6a3FHWUJKT2RvZWlycnpsZkNibkcvbUNNd1U3MktOQnJTMDEySk9NUFp0cGJ0RU54OEsrVVVoRTZyY2ZzdWViZEhtdUFNQnk4Ym5xYzNJcExXS1hRR0NRL3ZUb0ZrdytvdUZPc08zQmdIcHpidXIvS0h1dDlaWXVpRGlwZ29NMTRyYmhabWNnTXdqVTYxREM5Yit3aWFDWG9zbU9MY1RSU3UweVBsNURMVXEiLCJtYWMiOiJjYmY4YjE4NWExNDc1NDU5ZjQ0MTk0MjA1ZGU2M2JhN2M5NjhlYWQyN2Y3Y2U1MjJiNGEwYzVhNTNhMGVjMzQyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InJ5VW5td2phWUFHdURJMDdhbjJtcEE9PSIsInZhbHVlIjoiVit0OWtXeE1wMVJVQ1ZuZlJnejZQNjJvaStPSkwrdi9iN3ZiKytFWWI2K0FOdGc1cnhWTER4MmdWekJXTzRFcFkxaytobXFZbCtRcEZ6ckZ3VmhmU1oxQUJPV2gvUzFHb2hhM0lCZEJGSEtSMG5rWUVpQXlmMDZMbXhPK1VmdnZITDUxdUgwQUtwVzV1bVgreXEzbUxxeDI5dGRzUTBlODV1T20rZ2xBWmNzSmZqaS9ieDBwaTVpSUNpL3lHV1hpVnJINmY4V3psajdxTG4yTjdZRUlkUXlvRzVDclRtNEVzckdzMGVoTmh5TGpybGkrU21oeVAramVJY0R3L0RZWEYvZ3ZzY3F3RTdpSEZyQjFNRXpYdXlRWVZJMHcybWVjVUMxRmFsQ2xoc1Q5elZiK3A5b2Y4ZVIreDRXTkpVUkxiaWl5UWFnZ3hWME51eElDTk54YStvUkl5cEIyOXo4L25abmdVaVkzME96ZkJib0dqWUlEWU8rYy9ySzdwY2U4STRLZTFaekxSVVdERTVWcWVUenp4Q1d0dzFSOHBiSHpkT3BjZ3phZ3hGcE5iRnZUUjNnckg0ZlVLMTl1aWxldDF4eUp1ZWxkSHVlRjM4Wnd2SVJUQmNnVGsxVjVJL0FrREZ3U25jMTdJV1o2MTRsRktKL1ZsVG1pU3l5aXc1WkIiLCJtYWMiOiI1YzRjNDExNjY4NTZmZWEyYTRlN2QxZTA3YzA3OGZhNTJlNjYxZDk5YjgzODI1OWFjMTRkN2M1NDVhZjQ2OTlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-394228896\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-242964163 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-242964163\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-106961864 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:10:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNnUW9KU2ZUbHcrVytQckVDbDIrVnc9PSIsInZhbHVlIjoiUmlDQnA5S1A2TnA0U0NMeWI2aUp3VHRIb2ZYa2ZIei9sOW9kVFNHSW1OYmpCeDZLTmlkV2ZFQytUZFMvZTVFRUQxTWNoOWhPUk41U1pNOFVmUnZJK1RGbWN0QjVGanFXZTl2SVhkUzU0ZmhxeE5GaWZVWW9pclNrK0piNlpoT1d0clpRMit5ZUpieWo1WTd5SFRaT09uSkc4NnJpVGJmMzk5YlVVRFQ5R1p6TE44UEcwRnFJV3dXdlkxWWlwMEJmZ0FuTFNab29qalNCWFBiVGo2b1g1VFZtSm5pSlNBclF1dmZuZ1JVczBadDdTS0U1L0VIWGNaRzlzL1BuM2Juc2dXbk0rSzFpVklsK3lqak9DWnc4Q25PaEl5MXNYcHpkTm9zZlhJSDQrQzNLRzRPQjJBR1BSdlAxR1AvSC82ajZzb3hTZ1V2a1ppQ2c4MFFMaW9rMEFZUDVVVWxHeFl4alkwVEZqelU2cTJXbGxmK2x4Z0dlVHJIVDBxR0hVVWhkb2xvN1FOOHBMMHorQXp1OTNobzF4UFlKRldoMXcrdGF3TXZlVTFFS0h5ZERSM2tJM1JCdG13VEZEL3VKODkxdXBDN2FmRElxOU9MbUxUeDEydHlsK0huSFpueXBSaGN5SkJRWXVqT2diMUJQNFRKS0duaWNFZUYyY2FFRm1xNGQiLCJtYWMiOiI5NWJkY2E0OWE0NmQ3MzAwMDA3MGRhZDE5ODFhYWI0N2QzOGZhNTg5MmQxNDFjMTc2OTdkYzkwZjNmY2VlZmU5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjFwMlhVS1R0WUhSRDI2KzIwV25XTXc9PSIsInZhbHVlIjoiWUdSc2E4Rk9KVzkvVnR6Q3JzbXN1WWw4VnRsS0JoZnZtODZkRFZFYkJWYjZLaXZDYkJqTjYvU01zK1B1cG5yem9TZnRQaklVZlArb1p0NFYzQ1pjcmpxait3eDB1MHR1ZE1ESlF2MUFhQWF1VmhHYldiNlZTZjBiTEtiS2huMmxMcUR3K1l1K25XM08rNEw5TWROQStIcDJzcVZETlhCM2dDNndxOFZiakJXYkpyWFkwRmdxQjhVb0lEdDhlN2wwRHZFaDdYQnUzYjkyYjd1bU56Y09NL0FRMXhkc2lhcGh1Qk51Q3p6c01IVmhkd0JSM3FpZDhvOUtYN2dzR0QxVjluR1NXaUNQaWJQSEpieTNJTTljZXJ1MDZOdk0xMVNwUU9UdEl3bk9ZS2Z3dkROMlJtT2dKUTQ5VVArOFJvRGhUM0VqSlN6MWptbGRiazJrVjVDUkgwVjJBVEkwdmFFTHpwU1hydWdYZVE5bW8xT3NCRUVxQWRXMW9pZHZ3UEltQ3RTZi9XYTNaeVZlQ2JUalp4c0dCWHIySENnS2xRNTBWaHZRVTdodmEwQ1grajNOWWtRbGZVVERKN08vMUZRYjlPNU02SUY5MmhIcXV0Q25ZeGhWUDR0TVZxWmFNSlMzbU5CUlFRcncybGtaNHhFQkdkcDJhYVNwYkR2S3RkQ1ciLCJtYWMiOiJkYzQ2NjNkZjNlZGQ4OGFlMzQ2MWJjMzUzMmRjYmQyNjNlMzE0NGU0OGM5NzZjNGEyMzM0ZWY4ZDJmZmY5OTdiIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNnUW9KU2ZUbHcrVytQckVDbDIrVnc9PSIsInZhbHVlIjoiUmlDQnA5S1A2TnA0U0NMeWI2aUp3VHRIb2ZYa2ZIei9sOW9kVFNHSW1OYmpCeDZLTmlkV2ZFQytUZFMvZTVFRUQxTWNoOWhPUk41U1pNOFVmUnZJK1RGbWN0QjVGanFXZTl2SVhkUzU0ZmhxeE5GaWZVWW9pclNrK0piNlpoT1d0clpRMit5ZUpieWo1WTd5SFRaT09uSkc4NnJpVGJmMzk5YlVVRFQ5R1p6TE44UEcwRnFJV3dXdlkxWWlwMEJmZ0FuTFNab29qalNCWFBiVGo2b1g1VFZtSm5pSlNBclF1dmZuZ1JVczBadDdTS0U1L0VIWGNaRzlzL1BuM2Juc2dXbk0rSzFpVklsK3lqak9DWnc4Q25PaEl5MXNYcHpkTm9zZlhJSDQrQzNLRzRPQjJBR1BSdlAxR1AvSC82ajZzb3hTZ1V2a1ppQ2c4MFFMaW9rMEFZUDVVVWxHeFl4alkwVEZqelU2cTJXbGxmK2x4Z0dlVHJIVDBxR0hVVWhkb2xvN1FOOHBMMHorQXp1OTNobzF4UFlKRldoMXcrdGF3TXZlVTFFS0h5ZERSM2tJM1JCdG13VEZEL3VKODkxdXBDN2FmRElxOU9MbUxUeDEydHlsK0huSFpueXBSaGN5SkJRWXVqT2diMUJQNFRKS0duaWNFZUYyY2FFRm1xNGQiLCJtYWMiOiI5NWJkY2E0OWE0NmQ3MzAwMDA3MGRhZDE5ODFhYWI0N2QzOGZhNTg5MmQxNDFjMTc2OTdkYzkwZjNmY2VlZmU5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjFwMlhVS1R0WUhSRDI2KzIwV25XTXc9PSIsInZhbHVlIjoiWUdSc2E4Rk9KVzkvVnR6Q3JzbXN1WWw4VnRsS0JoZnZtODZkRFZFYkJWYjZLaXZDYkJqTjYvU01zK1B1cG5yem9TZnRQaklVZlArb1p0NFYzQ1pjcmpxait3eDB1MHR1ZE1ESlF2MUFhQWF1VmhHYldiNlZTZjBiTEtiS2huMmxMcUR3K1l1K25XM08rNEw5TWROQStIcDJzcVZETlhCM2dDNndxOFZiakJXYkpyWFkwRmdxQjhVb0lEdDhlN2wwRHZFaDdYQnUzYjkyYjd1bU56Y09NL0FRMXhkc2lhcGh1Qk51Q3p6c01IVmhkd0JSM3FpZDhvOUtYN2dzR0QxVjluR1NXaUNQaWJQSEpieTNJTTljZXJ1MDZOdk0xMVNwUU9UdEl3bk9ZS2Z3dkROMlJtT2dKUTQ5VVArOFJvRGhUM0VqSlN6MWptbGRiazJrVjVDUkgwVjJBVEkwdmFFTHpwU1hydWdYZVE5bW8xT3NCRUVxQWRXMW9pZHZ3UEltQ3RTZi9XYTNaeVZlQ2JUalp4c0dCWHIySENnS2xRNTBWaHZRVTdodmEwQ1grajNOWWtRbGZVVERKN08vMUZRYjlPNU02SUY5MmhIcXV0Q25ZeGhWUDR0TVZxWmFNSlMzbU5CUlFRcncybGtaNHhFQkdkcDJhYVNwYkR2S3RkQ1ciLCJtYWMiOiJkYzQ2NjNkZjNlZGQ4OGFlMzQ2MWJjMzUzMmRjYmQyNjNlMzE0NGU0OGM5NzZjNGEyMzM0ZWY4ZDJmZmY5OTdiIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-106961864\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-621757795 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-621757795\", {\"maxDepth\":0})</script>\n"}}