{"__meta": {"id": "X4e4b0f7263f66da786dd02adbab91413", "datetime": "2025-06-30 23:06:17", "utime": **********.689231, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.282136, "end": **********.689244, "duration": 0.4071080684661865, "duration_str": "407ms", "measures": [{"label": "Booting", "start": **********.282136, "relative_start": 0, "end": **********.63874, "relative_end": **********.63874, "duration": 0.35660409927368164, "duration_str": "357ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.638749, "relative_start": 0.3566129207611084, "end": **********.689246, "relative_end": 1.9073486328125e-06, "duration": 0.05049705505371094, "duration_str": "50.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45139368, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00266, "accumulated_duration_str": "2.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.669001, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.662}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6796231, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.662, "width_percent": 17.293}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.682575, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 81.955, "width_percent": 18.045}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2353 => array:9 [\n    \"name\" => \"مفتاح علبه\"\n    \"quantity\" => 1\n    \"price\" => \"6.00\"\n    \"id\" => \"2353\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1401175981 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1401175981\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-882401572 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323255132%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InB3MGZuVVA2aG9ZZk83Y2ZLKzRMTWc9PSIsInZhbHVlIjoiWitwVW1PWkNVSm9DWGFpc0UxMkI2SVp5S0V4ellJa1IrUDZrSFc0TGxCNi9FencvU1UybVIrbDFhU3REc3FqbWgwSWswaWJvWXYzaUNmRW5CYUFqMFkxTWFWTUlMVmdBT3VzaXMxV0cvZFVJYlJTK3JMUUZKSWd6S3BHblhHaVF3WU8wd2V6K041dWV0RUx5UHJEVllBQ2poS2N4YXZIeFYvVEplNWhueWxUK0lSZ3kwTTRYYzAvOHhFY3BiWm43K1V2TzVzSTJSNFRXMzNZQUJtRFlhYUp5dDQwcWIyWnUrT1pWN3YxejhKREwzY1R3QUdpeEdDaytqNC80eHk2NGdJNytNK3U2QzZvZEtFQS85dzA5UkJhNWVNNHMrVWs4cWhoUUhtZ0NVT3pNbTFFN2dNVkpWN21tTis3UklMTTI3SmUyV0FIWG1zNDZucUN3Yzd3MmVXUGJNVkJ2UzNEWFdKYU41TUZxMHFFUFFlZnBiV3c1akZ3eHdyY2RSVGx1SVhWZGI2OGtnODI1eW1OaXBab3BNd3ZoN3dWSGxMU0VJeENYWE9GVVBiYjhsdUdKVUVLM0pNWDk1blUwM3lGVHUySGhYVTBhdFd0ZjdSU0RpbTVOUU9MbjR4MW9DdElFTmNIZjdOT2NXbENMdXNmNWRpRkxBQ2t0UEplaGZaUUQiLCJtYWMiOiIwMmUwNGUyNTYxNjBmODEwMTI3ZmM2ZTJiNWNhMjAxNTQ1MmIxOGM0Yzg2YWIwODI4YzNhOWZhYTc2ZDc2ZDEyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InRXdlZMY2g4MUEwdkQwNThPWFVOTkE9PSIsInZhbHVlIjoiSCsyNFd6eklZeGUyeGxHT3VWdHZYcmErbmRUK1ZyRi96R1VEQUd5TitPWWVxdmV3U3c3SmxsalB4eDdFWFBEd2xZZkhYc2JQdzNpWkxaT2E1YnhBYVQ4aEtWSzRHejAwVW1pYVRFd2duNVBpR0RwU3lPTDk3MkFmM3Z4c1pzM3kwSW1ydlErMkV6V1E5R2svekt0Y1lML1daZ2wzSGY4WUJnV0Nqa3hZOW1rWUw3ZkdVdC80cnVGVERrMDRKTHg5blBiUlZ3NkpTaU84UXNWSi8yMlp2VndNdjdubzE1YTNMbnc5M3pHVmlGdG1EZmVNYXllYll4cTZ4NFpadGRRTmxpNzlpdjlMV2Foa0o3S0c4Mi9IL250SGZiSGJ5WjY5N1B6ZFRrV3Y1VDJPSld0WEFhUDV2U2JoOVdlbVhhWUNEc2tVZytzU1BXYzNOazVtRThvRFBjV0pONFVvWEVWb0dUVkd2OUF4NTIvYXZKQjF4WmxzaFRuQ0VlN1MzcERZSURCZjU1bXJIOGtoSHQxSU9xZHNrRDJGSE5tRUg3ZUJvSmVhV1h1TE85ZlV2NGRNWlJHMjN5a0FxZlJZdGJ6aHZxeXhiWFRWU3hRRXZEZzJEcHJCR0NTSTVmMEVQQkZNUjI3Nk82NVZvcElsMEJCV2ZDREFFeE80dFpDSXpWZ0QiLCJtYWMiOiIyZjgxYjU3YTc0NDljYzI3Y2JkNTc5OWU0YTczMWJmMjUzNGUxZWY2OGFmZDNiOWEwNzE4NTVmMTA3YzUzNjY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-882401572\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-288158154 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-288158154\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-561241951 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:06:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjV2aTZBNTBUelh5ZEcvbm1vREhHTHc9PSIsInZhbHVlIjoiMGFqWGZ0THlsZStoUU9FNXRVQmVwRkxmdTFYcFNPSm9peGNkclpHK2Z3VXZOY2JoWlp1UVBPUlhrbVBwVi9vQ0hHT1lHWmRJOXFHRnMvZVhpSWMzNzlnZWZPK0x2bmR4VU1tWEN2b0M0czk0YW1RN0FRUDcrOFlzcCt3MWFDZkdzTEUxSkovUVRnNWpNVklqS2ZBcGNUY28zWnJhNjZzV25FcVU1Smk0YWxoeXZEcndRQ08rYVh6ekZBSzQrYTNxYi93L3dSb3NES2lZSkpHYjVYcEZJN3lMcmdtSHNzV0NKTTZFVFVnelY4N0VFRXNEbHc5Q090ZUViZ1Q0SG10alI1SThxRkUwK2VkVFpPRTB6SDFVdXBHZmwxb21YYWs1c21rVjRyb1RmdVpDRFZzWUFTZzhSUHV0TmRBNzduZ1hyMGRNc1pnMHJYWGJ3T3RFQlRCNHpXbFJDUXhLMWI4QUdQSXZyejEyN3p3QkQzbFNnOUxhOGF1YzY3VGhNVU5xcUFzaHh5RW9oQURwSG9ucmFkZGVzTU9ndjI0cE8wT3d3c1BiNTFHNjVZamp5TkxLVy8wdEVGd2d5TnFIcTVTSk1PZlVFODdKOHpCMnRPRk8wVlc1cEJYMFdESGltamVWRSswNW5naWVCS1hlOXhYOG11ZW1ic2J6LzRuckhQZXgiLCJtYWMiOiI5M2JkZWJlNDAzNTI5YjAxMzg1MGQ3YjgyN2M2YzMwNDhlZGE5NjM3MmMxMTZjNzg2ZGVhNTM5ZDFjZDc4ZjI0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:06:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlQ3VDJZT2FKaGZUQkIvSTNvUUppTHc9PSIsInZhbHVlIjoiZUc0MUNpb0FpTXRoblhaTndKc0Rib3NYeWw2WXlnZU5LMEpLSzd1bjhaeXZLVjRicWx6WDNzNXpaaWlOc09DaWZZU1lOV0tBUmVnNmNUZ0tpWHdGSjd4eUp1Q0lBVTF0dG5tZHlrZTRvb1FZZWI2UnNTNHFkbHBjb1p4aFpkMlp0US9XNTkrcEZMazd6TlMvc2Rqa00zS3oyVkx5UksvVnhybEJIT3RzTGszUklSV2l1SnJ4aXppRTZrNVJFODJMY2ZLZXRUcmxGeFJJckdEaGdYTS9WWFhuQmlWYjBFZ1FrNWZibFhBcWVMb1RkVzNQaC8rdTJlc3BxazRyRVBlQ1dmbGVJdnBpYTFLT2Y5QlNXekphc01pdFRJajd0Q0M5TXZmaUhvdjdZQWZ5MThpNDQrUWM4TGsydGMzSjNidXViWmd2SmxNUlFQb3kxQUlpUmk1MzZmSDRBSzd6WmVPdElsSGVUcVNTWS95aHN3bTZDVDM5Q0JndWF0Kzk4ckxDVE41aStsaFNOb1BtdGx5K2xxK21IeFYxRVMwYUtHVWZxSTZvVVJVWUFYRG1palVwbW0vb1UwWUNqZ3BvdS8wZXpnV0NHWGh3Y2dQVnhSa0k3UUlod0F1TmFBaWQzSmltS3dxbXV1cFpWNnZRK3VCYityVGRuOGxlWC9qTXF0V2IiLCJtYWMiOiI1NDI5ZDc1YjA0OTM1ZDc1NjI1MGYzMTUwMGRhZDQ4OTlmNmUwOWJhNjA2NGIxMjRiYmIwN2M2NDlmYTI2ZmEyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:06:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjV2aTZBNTBUelh5ZEcvbm1vREhHTHc9PSIsInZhbHVlIjoiMGFqWGZ0THlsZStoUU9FNXRVQmVwRkxmdTFYcFNPSm9peGNkclpHK2Z3VXZOY2JoWlp1UVBPUlhrbVBwVi9vQ0hHT1lHWmRJOXFHRnMvZVhpSWMzNzlnZWZPK0x2bmR4VU1tWEN2b0M0czk0YW1RN0FRUDcrOFlzcCt3MWFDZkdzTEUxSkovUVRnNWpNVklqS2ZBcGNUY28zWnJhNjZzV25FcVU1Smk0YWxoeXZEcndRQ08rYVh6ekZBSzQrYTNxYi93L3dSb3NES2lZSkpHYjVYcEZJN3lMcmdtSHNzV0NKTTZFVFVnelY4N0VFRXNEbHc5Q090ZUViZ1Q0SG10alI1SThxRkUwK2VkVFpPRTB6SDFVdXBHZmwxb21YYWs1c21rVjRyb1RmdVpDRFZzWUFTZzhSUHV0TmRBNzduZ1hyMGRNc1pnMHJYWGJ3T3RFQlRCNHpXbFJDUXhLMWI4QUdQSXZyejEyN3p3QkQzbFNnOUxhOGF1YzY3VGhNVU5xcUFzaHh5RW9oQURwSG9ucmFkZGVzTU9ndjI0cE8wT3d3c1BiNTFHNjVZamp5TkxLVy8wdEVGd2d5TnFIcTVTSk1PZlVFODdKOHpCMnRPRk8wVlc1cEJYMFdESGltamVWRSswNW5naWVCS1hlOXhYOG11ZW1ic2J6LzRuckhQZXgiLCJtYWMiOiI5M2JkZWJlNDAzNTI5YjAxMzg1MGQ3YjgyN2M2YzMwNDhlZGE5NjM3MmMxMTZjNzg2ZGVhNTM5ZDFjZDc4ZjI0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:06:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlQ3VDJZT2FKaGZUQkIvSTNvUUppTHc9PSIsInZhbHVlIjoiZUc0MUNpb0FpTXRoblhaTndKc0Rib3NYeWw2WXlnZU5LMEpLSzd1bjhaeXZLVjRicWx6WDNzNXpaaWlOc09DaWZZU1lOV0tBUmVnNmNUZ0tpWHdGSjd4eUp1Q0lBVTF0dG5tZHlrZTRvb1FZZWI2UnNTNHFkbHBjb1p4aFpkMlp0US9XNTkrcEZMazd6TlMvc2Rqa00zS3oyVkx5UksvVnhybEJIT3RzTGszUklSV2l1SnJ4aXppRTZrNVJFODJMY2ZLZXRUcmxGeFJJckdEaGdYTS9WWFhuQmlWYjBFZ1FrNWZibFhBcWVMb1RkVzNQaC8rdTJlc3BxazRyRVBlQ1dmbGVJdnBpYTFLT2Y5QlNXekphc01pdFRJajd0Q0M5TXZmaUhvdjdZQWZ5MThpNDQrUWM4TGsydGMzSjNidXViWmd2SmxNUlFQb3kxQUlpUmk1MzZmSDRBSzd6WmVPdElsSGVUcVNTWS95aHN3bTZDVDM5Q0JndWF0Kzk4ckxDVE41aStsaFNOb1BtdGx5K2xxK21IeFYxRVMwYUtHVWZxSTZvVVJVWUFYRG1palVwbW0vb1UwWUNqZ3BvdS8wZXpnV0NHWGh3Y2dQVnhSa0k3UUlod0F1TmFBaWQzSmltS3dxbXV1cFpWNnZRK3VCYityVGRuOGxlWC9qTXF0V2IiLCJtYWMiOiI1NDI5ZDc1YjA0OTM1ZDc1NjI1MGYzMTUwMGRhZDQ4OTlmNmUwOWJhNjA2NGIxMjRiYmIwN2M2NDlmYTI2ZmEyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:06:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-561241951\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2353</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1605;&#1601;&#1578;&#1575;&#1581; &#1593;&#1604;&#1576;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2353</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}