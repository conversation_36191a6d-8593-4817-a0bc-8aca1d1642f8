<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FinancialRecord extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'opening_balance',
        'current_cash',
        'overnetwork_cash',
        'delivery_cash',
        'total_cash',
        'deficit',
        'received_advance',
        'shift_id',
        'created_by',
        'updated_by',
    ];

    /**
     * Get the creator of the record.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the updater of the record.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function shift()
    {
        return $this->belongsTo(Shift::class, 'shift_id', 'id');
    }

    /**
     * Calculate total cash excluding deferred amounts
     * total_cash = opening_balance + current_cash (only actual cash)
     * delivery_cash is now used for deferred payments and should not be included in total_cash
     */
    public function calculateTotalCash()
    {
        // Old calculation (commented out):
        // return $this->opening_balance + $this->current_cash + $this->overnetwork_cash + $this->delivery_cash;

        // New calculation: exclude delivery_cash (now used for deferred payments)
        return $this->opening_balance + $this->current_cash;
    }

    /**
     * Get deferred amount (stored in delivery_cash field)
     */
    public function getDeferredAmountAttribute()
    {
        return $this->delivery_cash;
    }

    /**
     * Set deferred amount (store in delivery_cash field)
     */
    public function setDeferredAmountAttribute($value)
    {
        $this->delivery_cash = $value;
    }

}
