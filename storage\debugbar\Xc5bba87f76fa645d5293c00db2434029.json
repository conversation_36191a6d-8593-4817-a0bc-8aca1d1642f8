{"__meta": {"id": "Xc5bba87f76fa645d5293c00db2434029", "datetime": "2025-06-30 23:08:27", "utime": **********.580519, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.108542, "end": **********.580538, "duration": 0.4719960689544678, "duration_str": "472ms", "measures": [{"label": "Booting", "start": **********.108542, "relative_start": 0, "end": **********.474674, "relative_end": **********.474674, "duration": 0.3661320209503174, "duration_str": "366ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.474683, "relative_start": 0.36614108085632324, "end": **********.580539, "relative_end": 9.5367431640625e-07, "duration": 0.10585594177246094, "duration_str": "106ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45724040, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0023, "accumulated_duration_str": "2.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.547132, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.217}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.557088, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.217, "width_percent": 18.696}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.562804, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.913, "width_percent": 16.087}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2127262346 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2127262346\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-892932720 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-892932720\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-497982622 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-497982622\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1546766481 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751324895055%7C8%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IitmQU1KZVQ2L0poRjJvNHhpT20xSkE9PSIsInZhbHVlIjoibHE0cVlrYVNUbTAvc0VmWWdXUEdmMGxQUEVKL1BFYjRGRUs1TElKTGVFRFkxSFdZdDAwTjA2Y2hEalRNV2JUNWMzWVROMXVwRENLd29WU2MyVGwwYWpDT3h4MTV3REwweWw3ZmlUVWN0Z3lXYUJLV0hwcGVMN0RSSW9UZjNCeWhITXA1VzdUN2M5b1dLa2pHelR4SXNaU0o2N1VoS1BhcGRXRFMyeHlQVXZkVzVHRmVEOUZBR2VmTllUdVN4NlMwS2k0dXlMUlNkVVRCcDRqQUQvVnVISXNBb3QwRWNVWEtINHFlUzhRMVlOVUpmSCtHVmlKY3VRK1RDeDc5bkF2aHltQjBEdlBvOW1OMVpYMzFURGc5bE01TEZoaitnVkR6ZStqTHpoSVdIc2RXTXRoTWtFT2dnZ1VHR3hsSlhIaVRvZlc0VFU3Rk01NEZmMVNqMkNCSzNWcHNadzhkSjUzM1ZTSlhGS21qYy9GZFhsSW1SM2ltbGozNFY1QWQ0Y2ZsK0t1V3c0b2V0YzVpZjUxcWFuWVNIaGZJRHE0aklmUTU1NHhoanZER2NMdkMyQ1hvZXVHbDlmaTRSRnV4Sm5iUUFzaEZtR1dyNWxNaFpPUjdaNUN1NklQc1pNRmlaS0plOXBISzAvL0dSQ3dsZ1NkUVdtL0RPL2VwbjlMREVYeWMiLCJtYWMiOiJjMDM3MmNiMDRkNWMwNTk1M2JhZGMxZWQ1MTk1YmU4MzMwMmEyMWEzNDczN2ExZTkyNmQ1NTIzZmE4MjFhZTY0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlFSRTBTb0wrMm5mZnMvaTFiNDBaSnc9PSIsInZhbHVlIjoiaHJiUlpaN012VjRRdENWeFJWYUFvTFlSUWlZbFZXeEllV21heTJlRGc2MTVTSjU5aXZjNkxZZFNSanB2UGxlK1AxS0VzcmR0L1ZFbjBtbnRMUjd6YVcyMGVmc3RlYUZxTzk2dW55N0Z3NEdEMjdFQnYwQUNEZFNQRzluK25OZE0rdXZJNXFOU3l0cU41RDZkQ3NOQ05BNjVmUktaeXdONTMzdlhQaXB4QkVETTRYM3RPYnJaUWdoQlN1TlVnV09JaGlyTEZidXZXZDZrSHF6SFdEL2lpazZTMG1zTUkvYWFnT0d2ZmE1d3dRaU16dXZSVUN2VDRXSy85RGx0VWtTSzRweW9PR3E3cENLYitONnpnb29XQmMrOGhWZXhiRmxPcW1HMHR6Qk1GV3ZVN1pPcHQxbGNOaFFUK2pNMllDRWNobXdZYlhlOWNRSUhvZUJmdEpROFMrdThENFNyR1dTcTlDV0FUcDBLNXVOLzl4SGsrNmdNNWFnb3JQL1AwN0FQdmdlbW41bDdvZk9VZHZ2ckxpaXdSeS9kOTY1Y2ZKRE5UUE9hRWlpNk5TcGRvdUU4QWduWEFpNFFkYTFQdFRvc2F6VVJXeStZMFZkL0R2bmV4bnpLWFhOMVpzVkZjOHNYaUVOaTA3b2Y0M2pVemx2SXliTmVZck9WMzZzT2lTZ1IiLCJtYWMiOiJjNDUwODc2NGI2ZmY2MjBiZWRhNmE3ZTAwZjVlN2IzODM2NTkyMDVjNjI2OTIzOTAxNGFlYmQ0MWQ1ZTljZjQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1546766481\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:08:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtvZ1FBaHZVSTFHbUduUmtuVndENXc9PSIsInZhbHVlIjoicXJxM0JFWFllVUxWVXhrd3FVYVZNZlVYZVkwbi9DbXZXRjBFN2RkdWUwYjQ5UFhDL3J2cUNtS0dZUENFVGlxUUYwZzg3MGdmOVRKVmpzZkxHTm1kTUdKcDlmSklVbkwzWTI1emg4MWg2NXZ0VFc0TFFiYXNkTlAyeVprZUZZZlV4S2grSC8wRG9leXpIYUtCUnY1UDhKTE1wWVZQQzFjUkJJZEhBNklkVS9TcFc3SGtoOFJNU0taUEF0WURnTlBrODAzeFJFaGpiSzArblIvT1pOMFF4K2RhaUp1RmcvN2puaTNnWGx6R1dQWG5vWXpwMWt6TkV2aG1Nbk8vdHZPRzBMRHNKUUZ0d0VsUVNVV21vNFE5L2cvUEYrcXlUZklJMVE5cmVhY0FFM2Q2ODZzMHBSZ0JvZVlXcEQxYnh1VHFUK0NZVjAvTDNTRDZISkk1SjhVWGg4VU5KcHduQjBHMVVtWUNvYWw3aHZheThRV0oyaHArTXFZMGpyTEZGTHVSOFJRM0tDRnlpdVN5d001MmFydzJKY1owT1RVNUNHc0tqUExEbGdtVEVtdk8vbFhRdTJWUkpvNFg5Tm9yV1F6RHMwUDlDS1lNcEpYUlp2OUtkLytYakRpSUhQUmppVWIzUWNBUnNZbVRJRHI5bGxLVkY1U2pJbiswdHdWWE9taDIiLCJtYWMiOiJiMDEwZmU0ZGY1YjNkYTRiZTM5MzI4ZTMzZDhjZTUzYmZmOTk1ZmE1NGMwZDQ3NWY1NmRhYmQyMzc5MmZkYjYxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:08:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IitvRC9waG1Ud0p4ZUtBTkJFVDhjZlE9PSIsInZhbHVlIjoiQ1R4SXllV2JQSlRjUWtMd0ordjdCY3dCTHRiWTc0bGRVaktHL3lPRisrb1VGcVEzT3NjYTNrcnpOQjI5aW1ZQXhrNlVrVzEyRTd3dFFwdlZyWGhPS3FXMVhOdktDR0lXTUQwV0Q1NHlVWWNXVHhzbXR3NTh1dkdlT1AyVjIzbjZhKzMrWnBjT2Y5V2pFYzMxSXYyUFFxYldkaE1Pd2F5ZVVVUFJmRHJqS3VvQ0JJUmdHbUlkdmx2bE1MeUl2VEdWc01MRkxKYi9WbWxhTFV1cmVZVEFYRDZPQUdaYjNNbTl0WGZHajNZMTJVYnNrVjZoSVVKYTlsVklHNG1xZ0pYQUI3VU9uUXpZRnY4YjlrRjhKYUx0N2p2bzBZdXJjM2FMazhrOHlWek8zdXZNYTIyZ3ovempEbzFXek9IQUNOWmFrLzV5NmxBSnNXRjlIWG1yL05HREplOU8yUFFYb3JEOEVxKyt0Qm9xK2pXWTdFUUs1V3Bxd0ZsRmJvYzRLRklkMkFhL045M0Uxd1pVNkdyY1E3cGk4K25vTWdiYWExOUhPVk01ZnYzcVRDeG14QkNHMm95RGN3NGZ3M0lOeFdZNkhsVjhydEF1aVVGdk5EaGNicGVkTzFqOXBtK05WYTNZTGVTY0pqM3lnYU9nNnpyTGt3Umo4dU9QQk1sTVpDVjIiLCJtYWMiOiIxOWExZGI5Y2RiYzZjM2U0ODE3NjZiOTQ5ZTk5NmZhZmVlYjBiNzBmYzQ5NzFmYzc0MTdkNDFjNGM4Y2JmMTc3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:08:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtvZ1FBaHZVSTFHbUduUmtuVndENXc9PSIsInZhbHVlIjoicXJxM0JFWFllVUxWVXhrd3FVYVZNZlVYZVkwbi9DbXZXRjBFN2RkdWUwYjQ5UFhDL3J2cUNtS0dZUENFVGlxUUYwZzg3MGdmOVRKVmpzZkxHTm1kTUdKcDlmSklVbkwzWTI1emg4MWg2NXZ0VFc0TFFiYXNkTlAyeVprZUZZZlV4S2grSC8wRG9leXpIYUtCUnY1UDhKTE1wWVZQQzFjUkJJZEhBNklkVS9TcFc3SGtoOFJNU0taUEF0WURnTlBrODAzeFJFaGpiSzArblIvT1pOMFF4K2RhaUp1RmcvN2puaTNnWGx6R1dQWG5vWXpwMWt6TkV2aG1Nbk8vdHZPRzBMRHNKUUZ0d0VsUVNVV21vNFE5L2cvUEYrcXlUZklJMVE5cmVhY0FFM2Q2ODZzMHBSZ0JvZVlXcEQxYnh1VHFUK0NZVjAvTDNTRDZISkk1SjhVWGg4VU5KcHduQjBHMVVtWUNvYWw3aHZheThRV0oyaHArTXFZMGpyTEZGTHVSOFJRM0tDRnlpdVN5d001MmFydzJKY1owT1RVNUNHc0tqUExEbGdtVEVtdk8vbFhRdTJWUkpvNFg5Tm9yV1F6RHMwUDlDS1lNcEpYUlp2OUtkLytYakRpSUhQUmppVWIzUWNBUnNZbVRJRHI5bGxLVkY1U2pJbiswdHdWWE9taDIiLCJtYWMiOiJiMDEwZmU0ZGY1YjNkYTRiZTM5MzI4ZTMzZDhjZTUzYmZmOTk1ZmE1NGMwZDQ3NWY1NmRhYmQyMzc5MmZkYjYxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:08:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IitvRC9waG1Ud0p4ZUtBTkJFVDhjZlE9PSIsInZhbHVlIjoiQ1R4SXllV2JQSlRjUWtMd0ordjdCY3dCTHRiWTc0bGRVaktHL3lPRisrb1VGcVEzT3NjYTNrcnpOQjI5aW1ZQXhrNlVrVzEyRTd3dFFwdlZyWGhPS3FXMVhOdktDR0lXTUQwV0Q1NHlVWWNXVHhzbXR3NTh1dkdlT1AyVjIzbjZhKzMrWnBjT2Y5V2pFYzMxSXYyUFFxYldkaE1Pd2F5ZVVVUFJmRHJqS3VvQ0JJUmdHbUlkdmx2bE1MeUl2VEdWc01MRkxKYi9WbWxhTFV1cmVZVEFYRDZPQUdaYjNNbTl0WGZHajNZMTJVYnNrVjZoSVVKYTlsVklHNG1xZ0pYQUI3VU9uUXpZRnY4YjlrRjhKYUx0N2p2bzBZdXJjM2FMazhrOHlWek8zdXZNYTIyZ3ovempEbzFXek9IQUNOWmFrLzV5NmxBSnNXRjlIWG1yL05HREplOU8yUFFYb3JEOEVxKyt0Qm9xK2pXWTdFUUs1V3Bxd0ZsRmJvYzRLRklkMkFhL045M0Uxd1pVNkdyY1E3cGk4K25vTWdiYWExOUhPVk01ZnYzcVRDeG14QkNHMm95RGN3NGZ3M0lOeFdZNkhsVjhydEF1aVVGdk5EaGNicGVkTzFqOXBtK05WYTNZTGVTY0pqM3lnYU9nNnpyTGt3Umo4dU9QQk1sTVpDVjIiLCJtYWMiOiIxOWExZGI5Y2RiYzZjM2U0ODE3NjZiOTQ5ZTk5NmZhZmVlYjBiNzBmYzQ5NzFmYzc0MTdkNDFjNGM4Y2JmMTc3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:08:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1721820280 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1721820280\", {\"maxDepth\":0})</script>\n"}}