{"__meta": {"id": "X52e567561ab04b9d8abfc8e8a4ed8d20", "datetime": "2025-06-30 22:40:54", "utime": **********.066634, "method": "POST", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.572235, "end": **********.066654, "duration": 0.4944188594818115, "duration_str": "494ms", "measures": [{"label": "Booting", "start": **********.572235, "relative_start": 0, "end": **********.933892, "relative_end": **********.933892, "duration": 0.36165690422058105, "duration_str": "362ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.933903, "relative_start": 0.3616678714752197, "end": **********.066655, "relative_end": 9.5367431640625e-07, "duration": 0.1327519416809082, "duration_str": "133ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50900888, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@SetOpeningBalance", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=134\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:134-167</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.06216000000000001, "accumulated_duration_str": "62.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.968239, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 2.687}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.978236, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 2.687, "width_percent": 0.756}, {"sql": "insert into `shifts` (`shift_opening_balance`, `is_closed`, `created_by`, `warehouse_id`, `updated_at`, `created_at`) values ('100', 0, 22, 8, '2025-06-30 22:40:53', '2025-06-30 22:40:53')", "type": "query", "params": [], "bindings": ["100", "0", "22", "8", "2025-06-30 22:40:53", "2025-06-30 22:40:53"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 147}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.98963, "duration": 0.05453, "duration_str": "54.53ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:147", "source": "app/Http/Controllers/FinancialRecordController.php:147", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=147", "ajax": false, "filename": "FinancialRecordController.php", "line": "147"}, "connection": "kdmkjkqknb", "start_percent": 3.443, "width_percent": 87.725}, {"sql": "select * from `financial_records` where (`shift_id` = 74) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["74"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.045942, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 91.168, "width_percent": 1.03}, {"sql": "insert into `financial_records` (`shift_id`, `opening_balance`, `created_by`, `updated_at`, `created_at`) values (74, '100', 22, '2025-06-30 22:40:54', '2025-06-30 22:40:54')", "type": "query", "params": [], "bindings": ["74", "100", "22", "2025-06-30 22:40:54", "2025-06-30 22:40:54"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.047862, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 92.198, "width_percent": 3.652}, {"sql": "update `users` set `is_sale_session_new` = 0, `users`.`updated_at` = '2025-06-30 22:40:54' where `id` = 22", "type": "query", "params": [], "bindings": ["0", "2025-06-30 22:40:54", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 162}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.051214, "duration": 0.0025800000000000003, "duration_str": "2.58ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:162", "source": "app/Http/Controllers/FinancialRecordController.php:162", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=162", "ajax": false, "filename": "FinancialRecordController.php", "line": "162"}, "connection": "kdmkjkqknb", "start_percent": 95.849, "width_percent": 4.151}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "success": "تم تحديث الرصيد المفتوح بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>opening_balance</span>\" => \"<span class=sf-dump-str title=\"3 characters\">100</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1683905169 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">67</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Iis4TDlJbkhpQm5iRlhxN2Z3U0tYUnc9PSIsInZhbHVlIjoiRHM0ZnEzM0ZJTWVFNWNQUGw3ejZzQU5GNllYcUw5QVl1MVZWQlZZUExydU1tV1AvOG9WNVZCSnljcGhpcXQ0cXlCTVNoSGhoOHp6dlkwZHMrblhkc3JmbVhwdnlpSHlZczB5ZGJhRHlPMWFucDVVaHNlYmNZRC9lTCt3VW9IM0pCTTV0NWp2dTJLeDJ1S1M4VUJ1TmRWWEhXaGY3Y0hBL0NvZHFSMFNhR1ZWcE40UHFNR0xHL242VTVDTjFXQi96MUxBZHdBbW03UXJZdHk5U2VpempoaWxNNnU3aXVhN1VmbjBvQjdUODM0eTN1azhMb2xxZGdYSTBseFNENmVrK3FuUklFSDkzcHg1OUZJL3gySFFHVG9ZRitESkdPQjI0YTBMclpzZzZ0UVR0UkJHVGV0UzBGbFJjR3NXbjk5M29RUGUzUzA5b3RLanp2ajFMMkVQVEdLK0xFTzRRZTZtS3RwaFRoeFc4RFhoZndYYjhURGZNQXhBS3VrVXdNSHl2S1JEUG9KQ1J2dllJWHZRdnlIdEhRVzRxV1JYbkxqWW14N052SS8yb2tHRndkdE82T0RsY2Q0SDBNYzJiM3NWQW5XdEl4ZGEydFdIcFlOdGxqL2JoOFBNLzhSclJuaHdORHhGcitlQjJZTENHWnZGK0I0bkpxS08yc3pxTElORzQiLCJtYWMiOiJhMzk2Njk5MzVmZmNmMWI1YzAwYmJkMzJkZGIxZmQwZGMyOTY4MmJiM2EwMWQ5YTMxMDdhNTk4Y2M1NjE5Y2Q5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InR0NUZ6eTlhYjZCbzFHbXRjNG84UlE9PSIsInZhbHVlIjoiSkVqY0hjUGtRa2h1MkFrZUhOd3FrK1NlcFFLc0wwQk5Odys5WDZoY2hpUUVMenFjY3dpcHJ2N25URGJEV3RkUDZ5ZDBZQVJwTnpxU3d5UmY3bFBjT3h5MVU4UUNXWStBUXhaR3B5QWRETnBuNlNURFFzdXM2VDZzNXRkd1Q4YTNJTGtCZnN4RjluN2lnTFF5RlRSMzU0N0RhVUYwdTZIUWVkWW1pN1J2U05XS2hNNHFmTTNoWmQ0ZUdLOWtQaEhXbzJrbk9wemNQbUxYS1N3QTliam0vM25YTWVrY1lRdnNSUEtYTlI3ZXFDNDNSMEp3YlpRQ2pJUGdveUV6cTRndE5NSDROTnNTa1Q4L1RvRVlaS0E4a0wvblZEZWlVWGxjRkRMWHpUM2dSS1diM1NpNytvd2xFWFhIbCs4MjAzRGtzMDFHRHF5aXdPQTQvSDRSNDlWS0ZuTCtmTlFxaEVRbFMzMGtTV0doa3NPQ29HYlN0eGg5ZmNvSEZQYVh2cXFLZFUrVnJzR0laNjNDSEJ4cTd6bmZjYk10UTBIb0hUdU9yZWNxL1p4YUNZajNDVjdIRUVKZ0hWVG1EZXVQeGY2blBuR2VLaUNkTkVZT3o5U3RVQ1BCaTJ2bkNmSTlaOEh4S1BlTnhpTGw1bDBYUVVqejVvSks0UVY1anRTcmlUV2QiLCJtYWMiOiIwM2E5NzU4Y2VmYzIzNGVmMmQ3ZjQ3ODg4MTIyZGQ5NjViNmQ5MTRkZTAwNmUxMzVkYTEwNzM2ZGFjMmYxNzVkIiwidGFnIjoiIn0%3D; _clsk=eiqg7d%7C1751323250612%7C5%7C1%7Co.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1683905169\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-511827945 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-511827945\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1267933271 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:40:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imx0L0ZkdC8vRys1Ymo2ZmpoSHJPSmc9PSIsInZhbHVlIjoiZkdqWkY3TFRVcjQyYXErY1lndHNIYXRFK1Q4SzVLa3lUNy9MOVVGVUJ2Nm56c094T21ZTGhBVFRyOWV1OHFWemVxWlArUFkrLzFlOGNDekptQXYxMWhnUjM5TkxYL0xocS9qUmlGeXd1aS9kTWN2dHJsSHg4a08zLzJYODZDazJxb2NQMWk0SVA3TnlMdUNQOHRaaEp1N3JONU1IVXJkSzhzNS85ckdzWDlpZGhSWGJia3poTGZwNGlTTW1YRVRpdGlZY213clhQL3djQXNlWTN6am8rUEtRVmRkTWJZUVh3cTF5b3ZkTkhpVEJpSGlFcUZiV1pTTWFyd3EweUFSYmN5U2dKbjJBRXZaSzVaM0ZRc3hxQkdZTm5ZSzJhZmxmTVlZZy94T3piQUY0QTR1REZCYjMxS3M2L3V6MUg3K0s2YkVMZXpLK08rbkNqbm5mWU0wdENsOUx5d3FpWjlIL1F1ZWlzRy90dGVaZHNTeVBPZm9CRFRJM1lBRllETnNsNms0NFpzWGhPZ2xCWjJiRWd2bS9kSHp0NXdQTTV3b29IUC9FQUtUS0M4em1IRmFnVXlpMFEzMWw3ZEhwREsxSUZsQ2laTWFnL1ZWZzJFV0lFVFh5TitIS1Yxa1htdWpEanNuUzE0QWxRTlZIanBMZ1l4b21DRFM5V29peWo3dkUiLCJtYWMiOiIzNjcyY2VkMTI1YjI5YjNiNGI5ZjIyMjQ2ODI5NjU2NTlmZDQ2NTdhZmYwMGJkODBiODk1NzFjZmQwMzk4OTEwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ilpac2p5VmltMW9Zb3pLTzFPV0ZsUXc9PSIsInZhbHVlIjoicjFNY3VaYUZ6bFpNOTRrU3dTQ1dyYnIzeU9RL3lXRTFheXlGWGpRdlBmVzlqRTF3N09KVnArTXdnVGVjaS9XT2dyejFHQm1zcnZWMkcwMDI3RWR0TG1xbUhCSStWVHhrRWdNaWQrY2NPbnNmQXcyNEtobXY4ZEZSRDVPd3RwbitUZGdQVmwycEU5RmU1ZDMyMlI2bXVDU2lWYlNock96VmRpSUVJQVVPN08wVHliTG5zNHFBTGtONDZPRzhNN2tldXJLOXJBT2dJVERUM3hrTTR5Q2toWkM1OCtCYzVwVWV2M2UrUXR4eVlxYXVzWUYxcDlwYW14REFZVDdlTTI1ZG5mZkNtenJxSk95QUY3aStzVEtKdzNsZFlsZFF5TFZuL3Z4R3l3UlI3VWFKU2JOZ0RBNUNIOUsxNExIZ3kxWGJLcGEvUmFySFFleks1dTF0RlJrVi91Q3FOdEZVbUdSOExFeDFiZmVWSHYzNU1MWVFuT0pFUDNsL0lLL3hMOWJteUp5Rkx1c1d2MDNhT3Jtdlh1NHp6MWtQVUUwK1YvbUZiVUIrMEszUTJyc2Y4TjBURTNqLzRhcDhFaTRnOUprYkVBY2pZZGJtL25QY2FsdEluQXFFUDF5OStTc2hzd1JWVjh6aW5HTUlJamZoNUo2cUpKMzBOVFkxcEd4UkJkbnYiLCJtYWMiOiI4NTAzZjBhNGMwNzNmMGZjNjMwMjYwMjc3OTZmYjA4YTM0MzY5NDljNDc3MWQxNjZhZjdkZGEwODI2YTY0N2NkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imx0L0ZkdC8vRys1Ymo2ZmpoSHJPSmc9PSIsInZhbHVlIjoiZkdqWkY3TFRVcjQyYXErY1lndHNIYXRFK1Q4SzVLa3lUNy9MOVVGVUJ2Nm56c094T21ZTGhBVFRyOWV1OHFWemVxWlArUFkrLzFlOGNDekptQXYxMWhnUjM5TkxYL0xocS9qUmlGeXd1aS9kTWN2dHJsSHg4a08zLzJYODZDazJxb2NQMWk0SVA3TnlMdUNQOHRaaEp1N3JONU1IVXJkSzhzNS85ckdzWDlpZGhSWGJia3poTGZwNGlTTW1YRVRpdGlZY213clhQL3djQXNlWTN6am8rUEtRVmRkTWJZUVh3cTF5b3ZkTkhpVEJpSGlFcUZiV1pTTWFyd3EweUFSYmN5U2dKbjJBRXZaSzVaM0ZRc3hxQkdZTm5ZSzJhZmxmTVlZZy94T3piQUY0QTR1REZCYjMxS3M2L3V6MUg3K0s2YkVMZXpLK08rbkNqbm5mWU0wdENsOUx5d3FpWjlIL1F1ZWlzRy90dGVaZHNTeVBPZm9CRFRJM1lBRllETnNsNms0NFpzWGhPZ2xCWjJiRWd2bS9kSHp0NXdQTTV3b29IUC9FQUtUS0M4em1IRmFnVXlpMFEzMWw3ZEhwREsxSUZsQ2laTWFnL1ZWZzJFV0lFVFh5TitIS1Yxa1htdWpEanNuUzE0QWxRTlZIanBMZ1l4b21DRFM5V29peWo3dkUiLCJtYWMiOiIzNjcyY2VkMTI1YjI5YjNiNGI5ZjIyMjQ2ODI5NjU2NTlmZDQ2NTdhZmYwMGJkODBiODk1NzFjZmQwMzk4OTEwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ilpac2p5VmltMW9Zb3pLTzFPV0ZsUXc9PSIsInZhbHVlIjoicjFNY3VaYUZ6bFpNOTRrU3dTQ1dyYnIzeU9RL3lXRTFheXlGWGpRdlBmVzlqRTF3N09KVnArTXdnVGVjaS9XT2dyejFHQm1zcnZWMkcwMDI3RWR0TG1xbUhCSStWVHhrRWdNaWQrY2NPbnNmQXcyNEtobXY4ZEZSRDVPd3RwbitUZGdQVmwycEU5RmU1ZDMyMlI2bXVDU2lWYlNock96VmRpSUVJQVVPN08wVHliTG5zNHFBTGtONDZPRzhNN2tldXJLOXJBT2dJVERUM3hrTTR5Q2toWkM1OCtCYzVwVWV2M2UrUXR4eVlxYXVzWUYxcDlwYW14REFZVDdlTTI1ZG5mZkNtenJxSk95QUY3aStzVEtKdzNsZFlsZFF5TFZuL3Z4R3l3UlI3VWFKU2JOZ0RBNUNIOUsxNExIZ3kxWGJLcGEvUmFySFFleks1dTF0RlJrVi91Q3FOdEZVbUdSOExFeDFiZmVWSHYzNU1MWVFuT0pFUDNsL0lLL3hMOWJteUp5Rkx1c1d2MDNhT3Jtdlh1NHp6MWtQVUUwK1YvbUZiVUIrMEszUTJyc2Y4TjBURTNqLzRhcDhFaTRnOUprYkVBY2pZZGJtL25QY2FsdEluQXFFUDF5OStTc2hzd1JWVjh6aW5HTUlJamZoNUo2cUpKMzBOVFkxcEd4UkJkbnYiLCJtYWMiOiI4NTAzZjBhNGMwNzNmMGZjNjMwMjYwMjc3OTZmYjA4YTM0MzY5NDljNDc3MWQxNjZhZjdkZGEwODI2YTY0N2NkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1267933271\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"29 characters\">&#1578;&#1605; &#1578;&#1581;&#1583;&#1610;&#1579; &#1575;&#1604;&#1585;&#1589;&#1610;&#1583; &#1575;&#1604;&#1605;&#1601;&#1578;&#1608;&#1581; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}