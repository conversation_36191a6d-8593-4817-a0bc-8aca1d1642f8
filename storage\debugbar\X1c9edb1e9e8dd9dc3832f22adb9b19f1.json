{"__meta": {"id": "X1c9edb1e9e8dd9dc3832f22adb9b19f1", "datetime": "2025-06-30 22:36:40", "utime": **********.203367, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751322999.729249, "end": **********.20339, "duration": 0.47414088249206543, "duration_str": "474ms", "measures": [{"label": "Booting", "start": 1751322999.729249, "relative_start": 0, "end": **********.128218, "relative_end": **********.128218, "duration": 0.3989689350128174, "duration_str": "399ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.128227, "relative_start": 0.39897799491882324, "end": **********.203405, "relative_end": 1.5020370483398438e-05, "duration": 0.07517790794372559, "duration_str": "75.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45723808, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0155, "accumulated_duration_str": "15.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.156476, "duration": 0.01444, "duration_str": "14.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.161}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1797879, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.161, "width_percent": 2.387}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.186621, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.548, "width_percent": 4.452}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2047660486 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=6nrx0y%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1aj6s00%7C1751322883410%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImhKQ0t2ak5BMEtwUmY1dk52NFloNXc9PSIsInZhbHVlIjoieGxLa1FzTktNVXhYT0dyUWFMQi9VZ2lGc0hpQ0IrZ3JUWkp1U1V0TzhmRmtSMU5FQTU3V1VKUFptRCt1VFljZG9VS0RVV1Zkd0s5OWZQS0YyZk1QOTdWWmFaeGZHSEprM2NUU1V2WFhnS0NJQ2dOanNubCsvcklEQzJoRmpiZTFRZVlkMVBUa0xqbWErTlI5SyszbTJPcEEvWkswM0VlUUM5TVJ4bC9LbklhNWlGcTlkby94cHRZRTVIOUpCWlJYK2c2MUorL0s4dUVERFRoYkNKWnFydzR3Y3cyM0ZKNEpHYkZqTXg3NlNJQ0lXeUZqcmVuZ1lWR29yMHZ6VlFPZGYyYmc1N05nQTdUNkM1ZXhpYndKVlNJdGNsdlZNNm54RURRbndmWVdKemQ1Q2JOK1k0T1l2RWpNU0NKV2tJMytBcFcrSTFIcUNldzlEV29mektjVWFSOHY2M3YwN2xFbDIwcFEyeE8xa0U4Mk0yVXF0SmUvaWYrRnBtNWhLMWdHb1RXemVlQjl2R284YTlUQVhRMDdsNXlqenpjWXBXeW51MEhiczVEbXdTZVptMFJpZTVMdjBwU25CL0tEMjNwc3ZNRlhya09QSzVFL1Fmc0lGc29wcHdQdmFMbU1xcFRuazJTeWlvYitHbzdPQ2NGeGIzVUx3dk5GaE5xTy9qYmwiLCJtYWMiOiI1MmQ4ZGQ0MTc2M2VjM2I1NDM0ODY5MzI5NDkxYTI5YWRlNTUzYzc5NmZhZDFlZmFhY2Y4NjhlZGM4YmI5NWY1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InIvUDBVYmNZemp2cGUvdXFZRXpMcGc9PSIsInZhbHVlIjoiYm1BamYrc3cwWkdGOW9YOFJjU2MvMFk2VVpLTE9GYjIrOE1VN3BUeGRqRWFid1kycVJNWkZhS09ERUVPZ3pQL2NUOW5RN3RCdko3RFFaeVNvYUNoNTFaTUNpdmNLblgrQzlCcHQwRWgwNDZOZlJDakptYWw5WnZMZU5ObmpQRWNRU0R3NWg2T0lFaUVQdkEzVXltcG15dC9MamVpdHNaeVFVazhNNHBTdDV3SlFwTVNvR1BqRFJ6c0t0MlVGcFRvYUpqWmRKU0Fab1pHN1ZrUVQzcy91RWNSdGt5S001RjB6UWRQTUdQNXNzN3NXbzI1TEtsNSttb3dIWTJQaTBzNmQ0VGtiWjB5YkQvS28zYVVHYXZzL2RKZHhWSTBLb3lJeVpRVCtjd093QTJaYW85WE0xQkUyUXNwUWcveStDKzVoV0RVOS8zeFRxV0FKYnMwZzlPRGZmN2Q5V1BvWkV3cDFBR21mRzhJSmhUWkFqTUVMMEFvdFRPU2lJMmtSbFMxRGpSd21vbVhBSU9zQ0lNV1dIVkJHbSt2dE9hUTEvK01aU2U3d0pINFZIOUVVUmJrZHFPMHErTlJGNW5VM053emlnM3VHdzZrNDZtQ2tlMmV2UWQ0ZFE5bzNsODJtbjJnV212L0tZYnUzVTVvOGFEVkVoWXAyV3pScVdIQUxSZTMiLCJtYWMiOiJjZDViMzUyMDFmY2FkN2UwZDZmNzI1YmQ1YTMxMjNlMGU5N2MwNzQwNzg4ZjcwOGMwYTc1MDg4NGY3NGE0NGZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2047660486\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1346844133 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A9rdyCRm7SKpfljuMnVO7IpcgTBMITMjowAdsmJo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1346844133\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-837159334 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:36:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFEaS9Va2tWZTFodnBSbnBSS1FiL0E9PSIsInZhbHVlIjoiUnNFV2sxQlhKcFduaW1temY4Z2pWeDR1bysxb01lYzN4UDJWKys4OUh4V0RHaEVoaVljZzUrVTdSZ2FEb1l5Zkdkc3JBeEFmRGhSM0t4YmVSUXdyWGQ0bGFwWGVCY0VUQ2JIQlhXUVo4a3FTbG9Pay8zTGdqSFh4ZHNQWll6K3BXa1FVTkN2b201eFkzb2RPeStTclhmaW1qTXkzNE1IOElyclNoY296NkFaK2hYSlFhcmRLMkFTKzdaODhOZU1NcFZuR2s3dDRYQ25uVjgzOHcwV2FwMVp4bU1valY1YkI2RFU5M01DZ2JrVk90QmlSUlVIZWxxS1lRdWY2QUFONmtzalhiT0hpam83MUxiaTY0MTJUbE1HWnRuN0xJY24weGVKSHJxNVV2ekVGNmZsYU1HcmtMZnNSdjBvQmVmbXJ1bXFLV1BNeVVod25zUXlyd0d1MWdHNXhUdXh4TE14d2F3aWRHQzFYSjNpK2liUURFY2RMcXFHdi93ckh5d28zSjZhNTRybi9wcU8zZ3B4WnY3b0V2eGtRdE1KTmxhNFU4cWpnekFmV05uUzUwMjJjVEEzd0dINDNNR1lZL3ZEY2dteVVmd1VpSlBQK1BmdmVRSUR0c0F0ek5qQ3RJWU5NWExpc0d0T3M2end0RW1GTXFJUjdkMGpaKzJlNTlYeFQiLCJtYWMiOiIyODc1NDFhZWYxZWNmM2IwYzhlNjhhNDliMzVhYzE0OGZiZjBhZWUxOWRmZjIyMzg0YTQwMDdlNmFiNTM3NTY2IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjFFa3orVWFLQk1KT3E2UUwxWkcyOGc9PSIsInZhbHVlIjoia2NiY01od2VqWVlXRzhGeXBET0xuSi9sTC9QcEx3ZEo3SVRmZmJObU04dFBKYmVjVEZlL2hJLzQ5WUtkNHB0TXhia1l5M0N4RlIwd3JaTWRLQnlrcXdjbm1qbXNQcUFMVDZlSUJjK0Z3OVB0SVlUUE1CcGJJU2JTWk10TnIyN3RtVFp1eXoyczYwRUw3OVFSNlFzZW5mWUZrRFFLZFZ1NkZlVjJsVXdrNi90bGpTdEpxTU45amd2N05nZUhWZG40MjF4SzVQaE92RlZvZWhiMDR4ejlLKzBxT2w5YWh6Y0VYYXZ1MEgxeDU2cExqKzBlQy9ReFJvM2V4WDRxUmJzS0tyOThsN0VKeUlzQ1ZKUEg1UklJQ1ZEY2N6OWdyS21tTW4xNEFkSEE2MENKYUVHbFdoVTdGK2RIQlM2dmlJZjZBdWp0ZXZTTkpyM1EwM0JDWmJEOWhRVExKdFZnajdwZWhCK01RTTd0RGlhTXE3bkl2dzgzdE5xTk5qdThRSnhNblNOVERmcngvS2VBazh6TTlPNTFCdmw1UGM0YTlwQ2s4d2pKU20rUTFvaXhpQUJBaW9ybldSK21UUERFMGRIZ1l3REZxYm40eE9WYTFVRjFZWndNRHpuSG5vKzg5N1E0SXoxMks3VU1kdTVmaWRaTFpDMmJWQ3NDQUI5RTdGUkQiLCJtYWMiOiI1NWUyNTc2M2I4NThiMWM5NTA4MWQyODRmZjA4NzQ0MWM5ZmMxNjFlY2ZiOTc4OWIzYjAwY2RmMWJjNjhmNzgxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFEaS9Va2tWZTFodnBSbnBSS1FiL0E9PSIsInZhbHVlIjoiUnNFV2sxQlhKcFduaW1temY4Z2pWeDR1bysxb01lYzN4UDJWKys4OUh4V0RHaEVoaVljZzUrVTdSZ2FEb1l5Zkdkc3JBeEFmRGhSM0t4YmVSUXdyWGQ0bGFwWGVCY0VUQ2JIQlhXUVo4a3FTbG9Pay8zTGdqSFh4ZHNQWll6K3BXa1FVTkN2b201eFkzb2RPeStTclhmaW1qTXkzNE1IOElyclNoY296NkFaK2hYSlFhcmRLMkFTKzdaODhOZU1NcFZuR2s3dDRYQ25uVjgzOHcwV2FwMVp4bU1valY1YkI2RFU5M01DZ2JrVk90QmlSUlVIZWxxS1lRdWY2QUFONmtzalhiT0hpam83MUxiaTY0MTJUbE1HWnRuN0xJY24weGVKSHJxNVV2ekVGNmZsYU1HcmtMZnNSdjBvQmVmbXJ1bXFLV1BNeVVod25zUXlyd0d1MWdHNXhUdXh4TE14d2F3aWRHQzFYSjNpK2liUURFY2RMcXFHdi93ckh5d28zSjZhNTRybi9wcU8zZ3B4WnY3b0V2eGtRdE1KTmxhNFU4cWpnekFmV05uUzUwMjJjVEEzd0dINDNNR1lZL3ZEY2dteVVmd1VpSlBQK1BmdmVRSUR0c0F0ek5qQ3RJWU5NWExpc0d0T3M2end0RW1GTXFJUjdkMGpaKzJlNTlYeFQiLCJtYWMiOiIyODc1NDFhZWYxZWNmM2IwYzhlNjhhNDliMzVhYzE0OGZiZjBhZWUxOWRmZjIyMzg0YTQwMDdlNmFiNTM3NTY2IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjFFa3orVWFLQk1KT3E2UUwxWkcyOGc9PSIsInZhbHVlIjoia2NiY01od2VqWVlXRzhGeXBET0xuSi9sTC9QcEx3ZEo3SVRmZmJObU04dFBKYmVjVEZlL2hJLzQ5WUtkNHB0TXhia1l5M0N4RlIwd3JaTWRLQnlrcXdjbm1qbXNQcUFMVDZlSUJjK0Z3OVB0SVlUUE1CcGJJU2JTWk10TnIyN3RtVFp1eXoyczYwRUw3OVFSNlFzZW5mWUZrRFFLZFZ1NkZlVjJsVXdrNi90bGpTdEpxTU45amd2N05nZUhWZG40MjF4SzVQaE92RlZvZWhiMDR4ejlLKzBxT2w5YWh6Y0VYYXZ1MEgxeDU2cExqKzBlQy9ReFJvM2V4WDRxUmJzS0tyOThsN0VKeUlzQ1ZKUEg1UklJQ1ZEY2N6OWdyS21tTW4xNEFkSEE2MENKYUVHbFdoVTdGK2RIQlM2dmlJZjZBdWp0ZXZTTkpyM1EwM0JDWmJEOWhRVExKdFZnajdwZWhCK01RTTd0RGlhTXE3bkl2dzgzdE5xTk5qdThRSnhNblNOVERmcngvS2VBazh6TTlPNTFCdmw1UGM0YTlwQ2s4d2pKU20rUTFvaXhpQUJBaW9ybldSK21UUERFMGRIZ1l3REZxYm40eE9WYTFVRjFZWndNRHpuSG5vKzg5N1E0SXoxMks3VU1kdTVmaWRaTFpDMmJWQ3NDQUI5RTdGUkQiLCJtYWMiOiI1NWUyNTc2M2I4NThiMWM5NTA4MWQyODRmZjA4NzQ0MWM5ZmMxNjFlY2ZiOTc4OWIzYjAwY2RmMWJjNjhmNzgxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-837159334\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}