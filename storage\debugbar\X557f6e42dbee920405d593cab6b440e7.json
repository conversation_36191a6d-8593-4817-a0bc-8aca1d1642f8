{"__meta": {"id": "X557f6e42dbee920405d593cab6b440e7", "datetime": "2025-06-30 23:13:32", "utime": **********.930764, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.470001, "end": **********.930781, "duration": 0.46077990531921387, "duration_str": "461ms", "measures": [{"label": "Booting", "start": **********.470001, "relative_start": 0, "end": **********.871982, "relative_end": **********.871982, "duration": 0.4019811153411865, "duration_str": "402ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.871993, "relative_start": 0.4019920825958252, "end": **********.930783, "relative_end": 2.1457672119140625e-06, "duration": 0.058789968490600586, "duration_str": "58.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45026504, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00318, "accumulated_duration_str": "3.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.904078, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 58.176}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.914991, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 58.176, "width_percent": 16.352}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.921199, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 74.528, "width_percent": 25.472}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-656514075 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325209071%7C27%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijhrb25Hc3VzMmYrZ28vUmk3V3hRZ1E9PSIsInZhbHVlIjoieGJvYzhxRnNKVmtuUTlLdlhubG4yanV4SlUvMmV5T2NlbVIyNlVubjVzNW04b2EzQjFFRTRlSVZwZjBnTUpkc1Q1TDBoWHoyZElpSnNxYzl3S1hnWVc5WGh0N1dqOTh6OVZhcGhtMllyUUxpbVl5SDkvQURvaGxyQ0RkdzFsNzRHemlwdGNvMUdja2p4dDAxS2NYZFZZeTNHRTRYWGs2VU9NMVlPeng5UjVaTzRYb1NHZ2Ntd3dSajBRRXZZbHF3ZDFhM2Q2ZklJTE5EMndVL3dkeGhaVStZQTEyRzczZnhWWkVnNUtBQnFIdDFWcjVsOW50R3V1M01pU1cxWG8zUk1ZSU9sRnkyTzdmaXhRTkZRVzJTc0gwOG0wZ0l0SVI4WGQ5RFovUGY1RGZFMllTUkQvWlJFVHZadHVVeXVGVnNwSVp5ZVpVcG1vMHBaRGJYQmdaTW1pQ3R1WEdDT1VOK2trZm9iVWQ4L0NZSUV0U0RwV09CbkwvN1U2ZUlWdmM1RE01SDhWMERlTFBHbHhuL0VqRjl1VlJmbWpzN0FxQ3FXTHBjN1k1MDVHWG8wWVRvQ3AvelBoVTN2VXVTRnVnb0MyUDk5Q0hJOXZPRkJVR1ZEeS9xVW1tM0xKbW53U2VLL1J4STF3U2hSZVVCS0d4WlJNWlVaY1k0bEJjNlU2dTciLCJtYWMiOiI4OGFkZjY5NDA3OTQyOTg0ZDk0M2NjMDExMzE3NGQ2YzJlMWUyY2QxNzk0ZmEwYjZhZWNiZmYzNmYyNGQ3MmE3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVTZTVlZE5RaCtHY3FqKzAyTVE3SEE9PSIsInZhbHVlIjoidVltTzBjd0o4U3o2cUtMNklHZEt5VENZWGtkSWJmOEpCV0lPNUE2MTNpS1JQalovRlRZQnBVV0EySmZVcTJUQWdaOGtjUURVTFdMeFVWWkVwRk5BMnBEK0JFY3d6TGpOOE9FMlNiY1BybWZjT0t0NXlocU1hZ1YwdkJqWWdxcStUWDV6Y2EvQzV4dFc2ZnpEa042TEZoYzhEWll0cWFrK3YramxER252SnR6Q3JTN0J3bHYzbzYrUTVpWm5VdGlqUVNSQmtKN0EwaUlXZ3RkQVZ5V0hKUnNkaFJTN1V5bTh2c3J3a2p0dzVGUS9CWlVLOHNRSTY2dmxKTXRVdEl6bndCVGZ3RmVpakhXK0YxUEtEWFhzMVNadjFJd2dtcXNDUzJ6SUQzOW1xZ1R1ZCtTZ01ma1l0bG9OdlpPM0ZyYjArMEJiWWg4YXB4c3dGZzZybjJFWWZYeW1IWTJPUFJHbHBGRFZzUUxwdUhyWGk1dzBLUXBhUUUxV2hXaE9GWHErZHF3c3duMmlORlNQZm9kcUd2SVdObC90cW8xcUxIVG9CeXl1OFlWSnFjYnZyV0x4eUFTNjRBelJMZWFMTm4yaEpzSlVKZUFLK1M5OHR1ZDBUWTF0THZ6U3lLQzJvMkpXZWlRbFpBMURGdzlmS255c21SMC9rbmFMaytnRG1oMngiLCJtYWMiOiI4OWNkNGY2NWFhYzViZjIwOWE3MWM2NGJjYmE1YzNmN2Q2ZjNmMTkxZWRmMzZjNTM0ODQzYTA3ZmY2NDVjNDUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-656514075\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1653558252 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">H4YK57QUXyJ63T0CnkmwYWGCllGQ9A8V3d5cZCUG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1653558252\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-653957930 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:13:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNlRm8zQWJhVWxsRHB2UzRpL2t1WEE9PSIsInZhbHVlIjoiWnZDMCtMMWVzWDhUQWM3SlZUUWxOOTNmYWdrQ0prcm1xcXV1U1k1VngrZUxPOGRXc3huMzVKcmJqQTJQOTczUHlPVTE2dW94cVh3Zm9SbzBhaWpnWmtqcDU4TTVoQkhTY1VVYjFYTmVHb1phOVlxNDJzTHA2WU44NjlZMzJPc3B2NFJrQkpoVG9yTHhEV0E3R1NTWGRxWEZ1STJsZkVuWEN0K29KcjdLU0VOakw3TU5wRGFJZVYwc1hTMy9pTG4zYXJ5K2RkRDFKSitmcW13VHFGRG5sMExVSzU2RUllTDVsRmE1Q3NYbUUya0V2aHl6U2lOL25qWHJwb1M1RDVMQWJQT0R6R0VjMjVCYWRsN1dYbWsxVXF6R1hNdnB4dUUwclZFZTlhVVVaQmpQOVJjQjY3cERQRTZyYUxjeGJKaTZIbE1Dcy9hQnhKVFRTeXRBc0l3VHgyRU1INlZvK3VneHF1SnRMdnhZTU1FYnIzZWJReUc4aGFQSXJtNjdoUFprUmNmYmFTRkxacXY5eHVyb0F3TFhaZjdOM3I2UEkrdTJ3WUY2Tk4rUEJXNTgvUDJBRDNDeFo5N2R0QXJ2RTRkeWhkUDdydFRUN0JrVDBQdFd3anRkQ09xYjhZZlQvN0FLVFlNajlIU2cxeFR3dGNHMHVCcDUrUTJlZ25zUDlZNzAiLCJtYWMiOiI2M2JhYmQ2MzY0ZTAzZDIzNTFiZmJjM2VlOTljMmRiM2U0ZGJhZDA1OWM2MWQ4YWRkNTRlZDQ1OWVhZThhMTA4IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImVvQVliVXBIcHh1c2xHajdRVmVNN2c9PSIsInZhbHVlIjoiL3ZONDI4OWxDUnY5MHlXNE5vN3FmM1RycWJvZW9UWkNxL2treWJSVHpETno3NEJXVVRqRSsvWHNqTkliVFZ5aGV6dGIrQzFScXJHTkxXaXJ3QUxCa1huNTBia1I5YlNSTnJlaWZqK0Q2aXArZEFqUmVDMVRjcWFueTNuNlJNVGJaZGJ1dy9RWExXUXZXWndmaUc3bU83dVkxUjZmak0zM2FQNzVMd21Vb1ZSYmVmdEJWWDhKTEovby9jRi9iRkp6WGFta3pQL29JVlpNdzh0eVlwWmg0eVhJMFQ0SjM1UmZ6K08wU2pQYktZblc4QWp0VUtOcmNkekp1NlI0ZmVOWHl5NGMrbUtzdHhnenNQYUtkOFZBbG90TkFMZVlRbjBQNWhIZ096RjRTUGN3cUgwbjhBcHhqNlNGaDVkUWpLYXV5VHFLamQrOU4zY2ZjcHBtNHpNQ0M5dGtsTW5SM2w4Sm4xYXNOdE5lZWlxckFWTU1OODA3bGtIWTNuWGhMVzdVOTZCNlhRck1raWNicVl2ZFVYb2RCcERESUwrTDdITXREZy8rUjFmaStPOU1nSG00VjRzOHRwNnpFNktJdkVEQks5N1NQM05tZ0gvUmpBY0tDNlFmZzVoeml1TDhPYlA0UEhXMDFNRk16UkFtTEdWZ25SUDBURlBHMHowVnpvQzUiLCJtYWMiOiJiNTdlYjMxMmRhNTQ0OGVmOTE4NTdjNTNiYTI1MWNkZjFiYzllY2FjYzczMWEwOTgwZjU0MDcyNjNkODAwOTYzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNlRm8zQWJhVWxsRHB2UzRpL2t1WEE9PSIsInZhbHVlIjoiWnZDMCtMMWVzWDhUQWM3SlZUUWxOOTNmYWdrQ0prcm1xcXV1U1k1VngrZUxPOGRXc3huMzVKcmJqQTJQOTczUHlPVTE2dW94cVh3Zm9SbzBhaWpnWmtqcDU4TTVoQkhTY1VVYjFYTmVHb1phOVlxNDJzTHA2WU44NjlZMzJPc3B2NFJrQkpoVG9yTHhEV0E3R1NTWGRxWEZ1STJsZkVuWEN0K29KcjdLU0VOakw3TU5wRGFJZVYwc1hTMy9pTG4zYXJ5K2RkRDFKSitmcW13VHFGRG5sMExVSzU2RUllTDVsRmE1Q3NYbUUya0V2aHl6U2lOL25qWHJwb1M1RDVMQWJQT0R6R0VjMjVCYWRsN1dYbWsxVXF6R1hNdnB4dUUwclZFZTlhVVVaQmpQOVJjQjY3cERQRTZyYUxjeGJKaTZIbE1Dcy9hQnhKVFRTeXRBc0l3VHgyRU1INlZvK3VneHF1SnRMdnhZTU1FYnIzZWJReUc4aGFQSXJtNjdoUFprUmNmYmFTRkxacXY5eHVyb0F3TFhaZjdOM3I2UEkrdTJ3WUY2Tk4rUEJXNTgvUDJBRDNDeFo5N2R0QXJ2RTRkeWhkUDdydFRUN0JrVDBQdFd3anRkQ09xYjhZZlQvN0FLVFlNajlIU2cxeFR3dGNHMHVCcDUrUTJlZ25zUDlZNzAiLCJtYWMiOiI2M2JhYmQ2MzY0ZTAzZDIzNTFiZmJjM2VlOTljMmRiM2U0ZGJhZDA1OWM2MWQ4YWRkNTRlZDQ1OWVhZThhMTA4IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImVvQVliVXBIcHh1c2xHajdRVmVNN2c9PSIsInZhbHVlIjoiL3ZONDI4OWxDUnY5MHlXNE5vN3FmM1RycWJvZW9UWkNxL2treWJSVHpETno3NEJXVVRqRSsvWHNqTkliVFZ5aGV6dGIrQzFScXJHTkxXaXJ3QUxCa1huNTBia1I5YlNSTnJlaWZqK0Q2aXArZEFqUmVDMVRjcWFueTNuNlJNVGJaZGJ1dy9RWExXUXZXWndmaUc3bU83dVkxUjZmak0zM2FQNzVMd21Vb1ZSYmVmdEJWWDhKTEovby9jRi9iRkp6WGFta3pQL29JVlpNdzh0eVlwWmg0eVhJMFQ0SjM1UmZ6K08wU2pQYktZblc4QWp0VUtOcmNkekp1NlI0ZmVOWHl5NGMrbUtzdHhnenNQYUtkOFZBbG90TkFMZVlRbjBQNWhIZ096RjRTUGN3cUgwbjhBcHhqNlNGaDVkUWpLYXV5VHFLamQrOU4zY2ZjcHBtNHpNQ0M5dGtsTW5SM2w4Sm4xYXNOdE5lZWlxckFWTU1OODA3bGtIWTNuWGhMVzdVOTZCNlhRck1raWNicVl2ZFVYb2RCcERESUwrTDdITXREZy8rUjFmaStPOU1nSG00VjRzOHRwNnpFNktJdkVEQks5N1NQM05tZ0gvUmpBY0tDNlFmZzVoeml1TDhPYlA0UEhXMDFNRk16UkFtTEdWZ25SUDBURlBHMHowVnpvQzUiLCJtYWMiOiJiNTdlYjMxMmRhNTQ0OGVmOTE4NTdjNTNiYTI1MWNkZjFiYzllY2FjYzczMWEwOTgwZjU0MDcyNjNkODAwOTYzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-653957930\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-10******** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-10********\", {\"maxDepth\":0})</script>\n"}}