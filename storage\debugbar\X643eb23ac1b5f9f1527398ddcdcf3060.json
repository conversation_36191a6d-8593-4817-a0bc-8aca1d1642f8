{"__meta": {"id": "X643eb23ac1b5f9f1527398ddcdcf3060", "datetime": "2025-06-30 22:36:53", "utime": **********.22108, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323012.719703, "end": **********.221119, "duration": 0.5014159679412842, "duration_str": "501ms", "measures": [{"label": "Booting", "start": 1751323012.719703, "relative_start": 0, "end": **********.135783, "relative_end": **********.135783, "duration": 0.4160799980163574, "duration_str": "416ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.135793, "relative_start": 0.4160900115966797, "end": **********.221124, "relative_end": 5.0067901611328125e-06, "duration": 0.08533096313476562, "duration_str": "85.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45707168, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022510000000000002, "accumulated_duration_str": "22.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.163686, "duration": 0.02104, "duration_str": "21.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.47}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1974611, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.47, "width_percent": 2.843}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.206067, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.313, "width_percent": 3.687}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-299059714 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-299059714\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-679429952 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-679429952\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-474650326 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-474650326\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=6nrx0y%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1aj6s00%7C1751323011454%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpSazVIR29GTlBHRTRVeUVuVGQvVVE9PSIsInZhbHVlIjoiUzNZVk1DUkZ1eGxIOE8wdytpeWVQeUNDSjZvbmEwemVJdVpxSDZ3enJBUHRIVkIxR1FRekxGcFllOE5xMVpCay8wT1ZhNHdHTEExYUtZUlRLTVJzMmhYMFdCaDdCQWRrRS9nYTB6c2NQRVZmZE5rRWhvK1RhQ0VSaDZscTBVRWNHVU1uajBQeEdUeWx5TjlCQnJwYUxHYjVrTUVXeVFYVW9kaW01UEVXaHRvbEJ1bnVIYy8wcTlUNXBKNGRkVHc1UU9ZdXdleGRLMmtWNnVKemlrRHF3MWppdWxDRG5ydGhkZ09oQ0dpUW93RTBGZUZzZm9lcDRnVEM0dFZTdXMzdVBxVnZ6RmRmR3lyd0NOcHZvN2c1dnBrSHNTL1J0eTFvUHJGQXR0OHQ2TEs4K3djWTFhbkZsaWJ2MXRjZy9oOVNSbkZnaHNZYjNGOFVTUjZmS1RBaWE5NHVwa1VPZ08yTGV5dERZTjlGN05yUnc2MGxobWJKUHhzeVRMSEhUTXVrblp2Smd5VStaZm1uUllLWllLMUpyLzI3aENwRmJhc1dMUE94WkR1VVR2NUhsNGFITEsyY3RFOCtjaUpMZmJoc0FPamE0WnhmeFdQTVY3RGFmL0ZqSDNMblJHOVgvQlNIUXlvWFM3a0RyU2N1UXowRkxWMkw1bUROalB5NkFVeHYiLCJtYWMiOiIzYTlhNTdmZjhkOWVkNzdkZGE3YmEwODA1ZmE0YmE0Y2VkMTZlNjkxY2YxODg4ZDkzOTFiMzA1NmVhODY1YmUzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlpmRUpqVWlUWlArVGtjbXBqdEIrUWc9PSIsInZhbHVlIjoiNEcvbnpHNXc3WC90QU10UStJMVFQQThac0grYVZkUjduZm5xWlg5QjNDbktKZDJ1aCtXNjRDaENOZWw3cWhCTlo5ajV3cHdId0daV3V6dGF4ZUpVekFmb1FjYUdhWnl5L3dRdXRHUmxPc3ZSTGZHSndwTUlSRWpPUEJzYmVydU91MWFHSGFDamN0aEJyK0Q1dUs2RWViVmREelY5YmFtNG0rc1BkREhqU1M2YTNjVnRhWDVmbjhkRHJNMDg1cXc1MUpOcTFrQTBZSW44MkxLNEE1SHVuK1ZMSEtqeklSR09TMnBGTGFCMEt2UjJrSDBmTVFRTk5mNnZFM0QvSEY4TWt6WDRvMVc3dU5VRGJsUFNpZHlIdlNyZ1hxVWhQSWh3OGpEOThNTk1CRmdjbTlkQ0I0a2xBaUt4ckF2YzU1WjdQYmprV3NNUHp4Yy9QV0QvdmJCRDZtbk5xTDRlbEJLUG1ueVRMTS9TVzhYdHRkR2JraUxHdkZPQUd4Z2d1MzlhT3pzNjVEdnRlc2FhajNXL3FCdlJoUzBuUU9XWDc3SWFlZHp6ekJUVzQzV2pHdWFFS3lDTW5mUWpLS1JJeC9vNFVxZ2Z3RTBqYmdhdTM0U2RIODByaks0d2lNOFpzZmZEZzlMVXdZaWQ1YnVBaHZYT3lmTnpiUkNQUytKc0t0VS8iLCJtYWMiOiI4YTE4YjZhY2FjOTJkNmQ0YjA0ODBmNDcxZDY5MTE2NzRjODk2MGY4ZGQwMzk4ZjQyMTgwNjNiMTcyZjA1ZDkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A9rdyCRm7SKpfljuMnVO7IpcgTBMITMjowAdsmJo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:36:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9LUjdmc0c3ZExUUXBVWG5pZ1dTVFE9PSIsInZhbHVlIjoiQ2EveXBNUHl6b2R5VUJYb0l4cFRFRkI0S3AzZEZNRHNFaHc0ZFo3a3dyYzNlbWZ1WjBiVkN5dEtPeUV4ZW1Wd3U4MldGZEszU2VDd3FpYVM0eEdsS0JkcXZGS1ZZcVZJSjZ3MjNtSU1Ia1k3dldvWWRrL2wvU2hpM3dRM1lvWEI1YTZWSFRjQmxoMmFQMnBrR0R4NWVXMEVmbUhjU2s3RUV6UnpSWVVtczBTa0dWZkJDZUhQbUFlRjRCb0cwRkx4QkhTTU9sM3hZQldqcVVKWmthWDdFbm1iNCtaN1cvVVZER1M3QWU3YnJ3VElVeEJNOCtsU1Uzb2x0MzQxUTlBVlR6M3N3eGxQL1R0ckowOHhXVWQycFdkZHBSNXNSRTdyWE03bHNua1gzWjlWa2IrL1NrWkxWdGZlU3F4cTlISGhPdWVhenpwU1FqZE5rZS9HTnBiakorUHFCckhYRVBrWkZIU2Z6VCtIQWc3NW40aG9zdmJBd1lYc1h3bVRId09HM1d0UjBWVW9ENzl4VHR1MnhCS28xdmI3WGo1ZmVkak9IakV2RmlCQkRXSUVKYUUvQVA4Z1FnMnEvRlhpRGtldGQraThFSmtEVUtFamxRTUZyNkxRVXZ6QnppL3JtTjhNUCtVOE44bWFGV0pqbGN6SFhOWXZIVVByWDZNeCtzU0IiLCJtYWMiOiIyYmRiMWUxMWFjYWUyYjIzMDhjNmM2MzkzZDljZTNhZDlhNmY4MGNjMzk4ZjEzMTIyNGZkNjA1OGQxMjBmZDhmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImlRbDlTMFNJbVRVdHErWXAwU2Z4ZFE9PSIsInZhbHVlIjoiTFNteHZ1VjlIcjgxZGk5Z240eVAySHBzM3czZmxNUnJmSnYzYkNPWTk3UUtJQllKL1puWUxZRW9mQnJ0U014UU4yQVlyQWdRUzl0YnIzOWFWZTZDaEk1NmdOaWFndm9sWFBZODBxVDU3bWI2c1FEYVNGY0tQVUpZT3FyVkdjUUpTMGk3MFN1Q2ptYzhIYVp4K0FyaHk3Z0hGTjZqSkMvVXg5R2xUZUhCS25xR3czODFzcnc0bGtad29TRlIvUzBGOFlhT0JCWVdtTkY1c1RhOXFoVXducEUwUll1bmdxeFdOQlkrcFh3RjR4QXNhMTkyZmtKNXVWbVFEd0ZwSm9Jb255dUEzdTdvT2VHRGZjVHNVNFRJSS9wS1VaeUs2UXRnS3did2hudWE2bjh3RXNXUEZvc2IrZ2hvMnV4cDJ3MzZhbXlnNUx4SXQvaVI1TVVNeGdJSENTYjYrTnN5dVowK2xmUlBJOHRpNk12TzZiSStVUHl6eEVUSHkrSEFsNjAyNkFXQjFFdEU3Sis0TXJJemNNM3lmWXJKbW41NGVnbWJoOHBLUzBKRFlEbkFOV0l1Y0Vnbmx6VkFEeG5JNFVMYzc0K0xMZ2ZCREtjZEpYbU0yV0czMFMvOHJaYndSM3ozVDRBbkp6Nm1kc1hiYk1BZHlUZzJzSzBCR05TblNodzUiLCJtYWMiOiI0YTU4NGI2MGJjNzA1NzA2NmQ2NWJiMzRiNTQ1YTE0OWZiNTNmNWE0ZDNlZjhhZTFlNmEzNWQyMWQ1MGY0MzE0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9LUjdmc0c3ZExUUXBVWG5pZ1dTVFE9PSIsInZhbHVlIjoiQ2EveXBNUHl6b2R5VUJYb0l4cFRFRkI0S3AzZEZNRHNFaHc0ZFo3a3dyYzNlbWZ1WjBiVkN5dEtPeUV4ZW1Wd3U4MldGZEszU2VDd3FpYVM0eEdsS0JkcXZGS1ZZcVZJSjZ3MjNtSU1Ia1k3dldvWWRrL2wvU2hpM3dRM1lvWEI1YTZWSFRjQmxoMmFQMnBrR0R4NWVXMEVmbUhjU2s3RUV6UnpSWVVtczBTa0dWZkJDZUhQbUFlRjRCb0cwRkx4QkhTTU9sM3hZQldqcVVKWmthWDdFbm1iNCtaN1cvVVZER1M3QWU3YnJ3VElVeEJNOCtsU1Uzb2x0MzQxUTlBVlR6M3N3eGxQL1R0ckowOHhXVWQycFdkZHBSNXNSRTdyWE03bHNua1gzWjlWa2IrL1NrWkxWdGZlU3F4cTlISGhPdWVhenpwU1FqZE5rZS9HTnBiakorUHFCckhYRVBrWkZIU2Z6VCtIQWc3NW40aG9zdmJBd1lYc1h3bVRId09HM1d0UjBWVW9ENzl4VHR1MnhCS28xdmI3WGo1ZmVkak9IakV2RmlCQkRXSUVKYUUvQVA4Z1FnMnEvRlhpRGtldGQraThFSmtEVUtFamxRTUZyNkxRVXZ6QnppL3JtTjhNUCtVOE44bWFGV0pqbGN6SFhOWXZIVVByWDZNeCtzU0IiLCJtYWMiOiIyYmRiMWUxMWFjYWUyYjIzMDhjNmM2MzkzZDljZTNhZDlhNmY4MGNjMzk4ZjEzMTIyNGZkNjA1OGQxMjBmZDhmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImlRbDlTMFNJbVRVdHErWXAwU2Z4ZFE9PSIsInZhbHVlIjoiTFNteHZ1VjlIcjgxZGk5Z240eVAySHBzM3czZmxNUnJmSnYzYkNPWTk3UUtJQllKL1puWUxZRW9mQnJ0U014UU4yQVlyQWdRUzl0YnIzOWFWZTZDaEk1NmdOaWFndm9sWFBZODBxVDU3bWI2c1FEYVNGY0tQVUpZT3FyVkdjUUpTMGk3MFN1Q2ptYzhIYVp4K0FyaHk3Z0hGTjZqSkMvVXg5R2xUZUhCS25xR3czODFzcnc0bGtad29TRlIvUzBGOFlhT0JCWVdtTkY1c1RhOXFoVXducEUwUll1bmdxeFdOQlkrcFh3RjR4QXNhMTkyZmtKNXVWbVFEd0ZwSm9Jb255dUEzdTdvT2VHRGZjVHNVNFRJSS9wS1VaeUs2UXRnS3did2hudWE2bjh3RXNXUEZvc2IrZ2hvMnV4cDJ3MzZhbXlnNUx4SXQvaVI1TVVNeGdJSENTYjYrTnN5dVowK2xmUlBJOHRpNk12TzZiSStVUHl6eEVUSHkrSEFsNjAyNkFXQjFFdEU3Sis0TXJJemNNM3lmWXJKbW41NGVnbWJoOHBLUzBKRFlEbkFOV0l1Y0Vnbmx6VkFEeG5JNFVMYzc0K0xMZ2ZCREtjZEpYbU0yV0czMFMvOHJaYndSM3ozVDRBbkp6Nm1kc1hiYk1BZHlUZzJzSzBCR05TblNodzUiLCJtYWMiOiI0YTU4NGI2MGJjNzA1NzA2NmQ2NWJiMzRiNTQ1YTE0OWZiNTNmNWE0ZDNlZjhhZTFlNmEzNWQyMWQ1MGY0MzE0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2030897501 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2030897501\", {\"maxDepth\":0})</script>\n"}}