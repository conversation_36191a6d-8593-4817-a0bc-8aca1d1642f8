{"__meta": {"id": "X80f5824599267e20d99f96b730206b2a", "datetime": "2025-06-30 22:41:43", "utime": **********.57583, "method": "PATCH", "uri": "/update-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.140105, "end": **********.575844, "duration": 0.43573904037475586, "duration_str": "436ms", "measures": [{"label": "Booting", "start": **********.140105, "relative_start": 0, "end": **********.503636, "relative_end": **********.503636, "duration": 0.36353087425231934, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.503648, "relative_start": 0.3635430335998535, "end": **********.575846, "relative_end": 1.9073486328125e-06, "duration": 0.07219791412353516, "duration_str": "72.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47531864, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PATCH update-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@updateCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1570\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1570-1633</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00382, "accumulated_duration_str": "3.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.536023, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 50.524}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.546437, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 50.524, "width_percent": 13.613}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.561072, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 64.136, "width_percent": 21.728}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.563573, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.864, "width_percent": 14.136}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1038498315 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1038498315\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.567765, "xdebug_link": null}]}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2352 => array:9 [\n    \"name\" => \"مجسم شخصيات مختلفه\"\n    \"quantity\" => \"1\"\n    \"price\" => \"5.75\"\n    \"id\" => \"2352\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 39\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/update-cart", "status_code": "<pre class=sf-dump id=sf-dump-1414265646 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1414265646\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2021901849 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2021901849\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2352</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>discount</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-118512439 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323255132%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFwSDNiaFdjcG82NHNZSCtrS0NiekE9PSIsInZhbHVlIjoiaWk2T25seDZJdXprVlE2N0o2VWR5VzRUSlRHVnN5QURadWk5ZW5RdFhDR2FUam5HYTVBN2tQWnF5QWdmRkxlQnFqYW1ZNExzT0EvRVZ6dGExZDR6V0xURkRtOEZrU1NLNXdSTWx5dVZ1ME9VbkM1UzQ3RkpiOHNZN3lhWFMzcXpaVElUOHZtL2dQWnIvVGMzSTRMbG1UMkd2SlVrSWZRQVI4QkZwSVNmeDBHd1BpWDByTGNRZzg0SXVFRVFuNlNRL3NjYmEvdS9rMklzY2xFalRhQ0pZNnlBNUtvbUl5UmdUamkxNjJ5SWRVVUtKNlAzM1lKUzJnT2ZrZzhVZk1wQjBFbmNlOXN2eDQ1NGNTNENOMzgzZlB0UW9xVFdHMGl1RmJ6VkNUQmJoK3JPTzdWVTUyNklxKzAyRG1ZM2RNQ09lWVRIOFBHbk95Q1ovL2Y3ZjNQKyt5VnFjaHpsdzZ6OU45SHlObUpuNnQyK1dzeTVmbDQ2dTVnSnJFa3drbkxjcjFlZ1hBSk5aZmNIOFNtYVZVSE4yLzJHOEZaQzMxV0Q0NEhZQWx1S0ovWjZ1TWJkb0Rtd2thVTlYK0JzTC8yRGIzMkJHTjdlWGVMdWkzL3d2Yzdod2xrdzh5QXMwVUlkY0lpeUh2YWN5NHlJb3pRbk9odUlHK2RQYkNKaWVEVloiLCJtYWMiOiIxZDRiZDIwYWNkN2ExMjRiMTExNGU5OWQwZGQ1ZGFhYWYxZDIwNDU3NTgyNzkyODllMmNhNDY4NzhiOGFhNmIwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InRvVjJtNk9tbnB5R2REVzhOQnJySmc9PSIsInZhbHVlIjoiVHVpbGVYVHJ2VUNnbjJUQTZkSGtxUmxIOFVNeUtFVnVtMnh5K3pqSEpPOWNFVDhFZXVwc2IwTmxnRlFQYlE5c0JsNUZuL1NvcHFBOGlIdHlaYjkyeTFBSncrY1ovSXpVY3d6Z1A3S1dpcHN3WEFPcFBzMHNuWGxQTVdlUnZjQ09qMStPSW5Pcmh0aHFKOGVBVzdUY0d3czljZnR2MXdNTXZxeWpqQ3U5VHU4RTRhVWpmMHpreUlVQTdqMkgvZUFDMXpSdC9PMEFtVlZqb1h5V2M1Q2psbWc5S0ZscE8rY2hJVFVsZDRzN0dxOFMxRktOS2p0TldxT29qMFY0UlV4U21MTTBGZjQ4TFE2ZE1Uc2RrY1M5c1pab1g1TkFoQWRqblNDZENvWDVNcDhiMTR3NlBEVWRlaVNJTlJYd1V1UDFXU05oNDIzKzIzb3dhWE1pbWFvWlJBd21HbEJCbHdDdVVWbCthSXBxMVRCa0NZVVNUMUFhc2NPTitXYjZqRmNZaEVJZVR5MXRocmRsSTkxU0dWVHRrenhuZSs1NjNTTi9SN2F6RldNS2hscDFXa1BIcmtlVU9xY1NkMGhRbGF5b2h3SmR5NFhjbUpqZUZ2QXcwdzlTL3U0b2xTZk9WVGJRaU9nT29nM2kzQW9XNzk1WEpvMzZQOE9GNVpHbVNWNHgiLCJtYWMiOiJiMzdmZTFhMjlmMmJkYjY2MjczYzUxMmEyNTdlODQ4NjM0MmU2ODI4ZTU4YjUwYTI4NGRjODdiMGQzMmJlMTBjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-118512439\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1596165457 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1596165457\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-781836877 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:41:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFFd0VsMm9xdm1OelFGdWtWNlFZNFE9PSIsInZhbHVlIjoialFSZ1NUZDFidmZIdm5IM1dmTlp5dW03Z1BtVzRzajk1dE0wVjFZemdsNWZMVXNCVkVlSERSTmF1V2k1WW92U05tbXRrK2pqK1JRSnpnZXJSSW1udkhUQW8wVWtzQXlHM2FwcSt0MXN4MHBCWjVTNkYzMUMwSUNZajVxcnNKaXdPYnN4bUdmZ3FmOFAwVW5DakhiTHkwTDFlaEdVZGVBenVtUzMxWjZWZS9uajJ2N1M5aUg3dFFzNFl5ZDd1Nnl1YzJnSU9wNDIyYXYvZTdUaVQwNzM2b2IwNEpjQ3FOMENtanlwQVVCMWp0ckd5ekVveUI0TFNBejh1MUxSNVZVNVZXYzMveFh1emZwcjJwR00zSnZxc2JjN3hVdFR1cmd3L3lBeU1DcmQ4d1ljdDVLNlJFMzN3cjV6QVF2TWZ6RVJNeG9qMkZJc0hDRDBicXRnb0ZzN255ZzlGTjdmR040OEJTbXY2U1FudzBXREdvK3JVb3FJekF6bjBXbWcxMEJlMXI3MzI4djlOV0drU3N2MVRFd1ZUOXZsdHVYeWdBK0Iva2daUmZPZkpDaVQ0MFFUeXUxbWpRanQzYjNMcmt6NUxGQ1BxMERtVjEybXhMWmwzaDV5Y3hPdnRKL215WW1GWnV4dW9WbU9HNEFYQUNVMTJMbFB1RUFYZVRuM0hXTEEiLCJtYWMiOiJlYTNiZTQ3ZGIxM2Y1NGJmMGMyYWI5NzQ4OTdlY2ZlMTlkNGNhOTBkZThiNTRiOTJiYWUzZmUzYWNiNzA1YmUyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:41:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkFQQmhIeDlkT3hlTzRET1k0bEhOM2c9PSIsInZhbHVlIjoiTmlqcFFOb2Fxa0o4RlhuSzdhQ0h0NjM0TnNHVlRCOXgxYVdpanVxQU9LOERTbi9BbDN4NXFKK2xMVGtvYWRyV29PR0hGR1cweVF2YTZoUjliQXYyazk5elY2ME1pOXpMRlVBV1I5WkxUMktVOTBCaTdJUFQvelhnRHYvakZtMXdhc1JSd0w2NGYxUGlna2c3RUhMMkpnNHNmdUg5RGM4ZUNJV1VGNTN6QVJQV3hJeVdma29zejVFeStwOVh4UlFBcmpCdmhuaG8xOHlRZUNZWWtkSkJTMnFrUDcybEJmVkF1SURpbGs3ZnZuZ3hCc3FiQzhKK2ppR3NmSHl6OFRrK2NDUDVRdExFQXBQUmxqMWtrc0lHUDFqeVlmcWdtZXIxcm42QXBWRkpyNndZRGFnR3BWUDlXN3ZSQUZBbVRYNWZaKzNiUk5WcVdUNXAvTVY0U2M2b25IeHBnTjkwUFdUbkxJdzljYVU3dU1rQXozczRLMFdXUkZuMzh5c1RyWDhHQmd3WXZaMGp1c2dRL2Ixc29DY21PenFNdVdYWWNhYlZPYjZpcEc2OGlWRmdESUQ2dkQ2czNsOHBCYlEyaU1DS1lQZDZnenF3T0RIeVJnazhCSkdva2ZPbVpXa0JPNHdhRHZWSWpwNGhsOXVJMmppMGRQRkx0Qys4ZGwraWV1VEYiLCJtYWMiOiJiNDZkZTFkN2U4NTJjMjcyNjQ4ZmE1NWIyNmM2M2Q5NTQyY2Y1YzA4N2E3MmNhOGJhOTAzYzgyZmE5ZDE3OTViIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:41:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFFd0VsMm9xdm1OelFGdWtWNlFZNFE9PSIsInZhbHVlIjoialFSZ1NUZDFidmZIdm5IM1dmTlp5dW03Z1BtVzRzajk1dE0wVjFZemdsNWZMVXNCVkVlSERSTmF1V2k1WW92U05tbXRrK2pqK1JRSnpnZXJSSW1udkhUQW8wVWtzQXlHM2FwcSt0MXN4MHBCWjVTNkYzMUMwSUNZajVxcnNKaXdPYnN4bUdmZ3FmOFAwVW5DakhiTHkwTDFlaEdVZGVBenVtUzMxWjZWZS9uajJ2N1M5aUg3dFFzNFl5ZDd1Nnl1YzJnSU9wNDIyYXYvZTdUaVQwNzM2b2IwNEpjQ3FOMENtanlwQVVCMWp0ckd5ekVveUI0TFNBejh1MUxSNVZVNVZXYzMveFh1emZwcjJwR00zSnZxc2JjN3hVdFR1cmd3L3lBeU1DcmQ4d1ljdDVLNlJFMzN3cjV6QVF2TWZ6RVJNeG9qMkZJc0hDRDBicXRnb0ZzN255ZzlGTjdmR040OEJTbXY2U1FudzBXREdvK3JVb3FJekF6bjBXbWcxMEJlMXI3MzI4djlOV0drU3N2MVRFd1ZUOXZsdHVYeWdBK0Iva2daUmZPZkpDaVQ0MFFUeXUxbWpRanQzYjNMcmt6NUxGQ1BxMERtVjEybXhMWmwzaDV5Y3hPdnRKL215WW1GWnV4dW9WbU9HNEFYQUNVMTJMbFB1RUFYZVRuM0hXTEEiLCJtYWMiOiJlYTNiZTQ3ZGIxM2Y1NGJmMGMyYWI5NzQ4OTdlY2ZlMTlkNGNhOTBkZThiNTRiOTJiYWUzZmUzYWNiNzA1YmUyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:41:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkFQQmhIeDlkT3hlTzRET1k0bEhOM2c9PSIsInZhbHVlIjoiTmlqcFFOb2Fxa0o4RlhuSzdhQ0h0NjM0TnNHVlRCOXgxYVdpanVxQU9LOERTbi9BbDN4NXFKK2xMVGtvYWRyV29PR0hGR1cweVF2YTZoUjliQXYyazk5elY2ME1pOXpMRlVBV1I5WkxUMktVOTBCaTdJUFQvelhnRHYvakZtMXdhc1JSd0w2NGYxUGlna2c3RUhMMkpnNHNmdUg5RGM4ZUNJV1VGNTN6QVJQV3hJeVdma29zejVFeStwOVh4UlFBcmpCdmhuaG8xOHlRZUNZWWtkSkJTMnFrUDcybEJmVkF1SURpbGs3ZnZuZ3hCc3FiQzhKK2ppR3NmSHl6OFRrK2NDUDVRdExFQXBQUmxqMWtrc0lHUDFqeVlmcWdtZXIxcm42QXBWRkpyNndZRGFnR3BWUDlXN3ZSQUZBbVRYNWZaKzNiUk5WcVdUNXAvTVY0U2M2b25IeHBnTjkwUFdUbkxJdzljYVU3dU1rQXozczRLMFdXUkZuMzh5c1RyWDhHQmd3WXZaMGp1c2dRL2Ixc29DY21PenFNdVdYWWNhYlZPYjZpcEc2OGlWRmdESUQ2dkQ2czNsOHBCYlEyaU1DS1lQZDZnenF3T0RIeVJnazhCSkdva2ZPbVpXa0JPNHdhRHZWSWpwNGhsOXVJMmppMGRQRkx0Qys4ZGwraWV1VEYiLCJtYWMiOiJiNDZkZTFkN2U4NTJjMjcyNjQ4ZmE1NWIyNmM2M2Q5NTQyY2Y1YzA4N2E3MmNhOGJhOTAzYzgyZmE5ZDE3OTViIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:41:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-781836877\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-44451930 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2352</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1605;&#1580;&#1587;&#1605; &#1588;&#1582;&#1589;&#1610;&#1575;&#1578; &#1605;&#1582;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2352</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>39</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-44451930\", {\"maxDepth\":0})</script>\n"}}