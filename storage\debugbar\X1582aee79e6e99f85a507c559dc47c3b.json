{"__meta": {"id": "X1582aee79e6e99f85a507c559dc47c3b", "datetime": "2025-06-30 23:14:11", "utime": **********.485686, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.013105, "end": **********.485702, "duration": 0.4725971221923828, "duration_str": "473ms", "measures": [{"label": "Booting", "start": **********.013105, "relative_start": 0, "end": **********.422554, "relative_end": **********.422554, "duration": 0.40944910049438477, "duration_str": "409ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.422567, "relative_start": 0.40946197509765625, "end": **********.485703, "relative_end": 9.5367431640625e-07, "duration": 0.06313610076904297, "duration_str": "63.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45017552, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027600000000000003, "accumulated_duration_str": "2.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 23 limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.459662, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.667}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.47067, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.667, "width_percent": 19.928}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (23) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.476806, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.594, "width_percent": 13.406}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WuYcucCgl4bBOLSdLTqnajP2UNBzqFh0FtUk6GCo", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "23", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-900230424 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-900230424\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-644529817 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-644529817\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-156112019 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WuYcucCgl4bBOLSdLTqnajP2UNBzqFh0FtUk6GCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-156112019\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-231554669 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325245466%7C33%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImF1eWpRWkRSc3RWTVhkTDJvcjZHaWc9PSIsInZhbHVlIjoibTZtcjdEeFh4aW4rL01KdnpqQ1RsMnBVRFJHeS9qT2V0MW94RUp0dnNYR3FlYnBpbWJGTWlqTCttTlVkcEFnWSszZ3lHbnFsVXBQT3MyRGJPMmRWZC9xR2tHUUhCampvMjA1bUwrd2tyU2Qxbkx5THhsTjVpazVlc0pkYnc0b1Fhd3F4dVhjVi9kSEp3MTNPd2swMDNRNVVmeTE2NThSWlowWG5Tc0NVbHpybXFZK3hmNk1oQ3dLRFA4bUpzeC9qRFJrRUFvQWdTeEFBNno2V1planB3UWE4TCs2TGNudDFGZCtCOWozdjBWKzkrZWRJRVlxOUpJL2FSQ1hPTFBMZGcyTEd0RStZVkc4dXU4NExzTllzdjFvaWdNbUE4UE10VXloamI3K0xKazJqNE4waVRQRXQ2bmZiTVhSb25SRWsyM1JTSlU4V2x5OVFRRjhneG9GVjhta1g3YTFCUGJCS3VJd0lDVzZpYjhROEtPUHlOTWNQR0F3Mlc5OURzdVd6ODVpMmpnUXlGbkhhUHhZbm5OMEltK2xoRFMrVitIVkJvY3plMXpqd2V4U1NDcFM0bUhNc0JPT1VMV2pMVXRucHlXNklad3VjZ3BoQ3VuQVF2MnhmVElMS1lXSTdyWUNMNGRwckFZRjJHcnZDSHE0M1dZZGE1YTFjd3VQT3dIQ3oiLCJtYWMiOiIwMDhhZTQ2ODdhNjY3ODAyZTczZDcxZWVmYWM1NTFjY2U4NmI1ZDJlMjcwOGU1MjkxOTIyMzNiNzZmZjI5ZGJhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IldVaUJNTWFBbnlxZExpRkxtZVdqNkE9PSIsInZhbHVlIjoiQnd3dFF3UXBVd1M5V2RtYzRrbHBrMDM4THBiY1FBbzVkL3lyUklQVmJDbks0SzJLR0hjd3RtSmtGbmRvak93ZjNHZTVwZGpxZDRoc2x4am5XMlNqeVAreXdzZ1ZUMzFiSlRwaG1HNlJoYzV5MXI3YStaZHJmQzBEbjExOGsvN2JWUng4Z0U5ZnAvV2ZhYzBoWlIyRWhvc3RjUTFKTWpuZ1cySGpwbjRiTS90dVpWanl4NkJUOEhVR2Nmcm9vOXJHQlhnNGJhb0IrM3JBeStUQ2Y1OHcrNjJZa1NWaTlqVFltMWgrU09iVjRKS0lsNUhLd0s2QWd4S2ppNSt0VnRzYnA4djNPTEQ2MGRqNmlHT0cxMEtqWEI4SWhOZG5UaGxyL2loN1dkRC9FYzVNQ0tGQUo2ZmVOWVlkaHZDb0F6TTU5UlE3eit6WkdPb3BVUFdlM3NydHVaSFJEdUIyZld1UTJGVGdqVWx1WnJDZlpMZldCQTl1ZktQTVB5bWtZaERDT0QvUndJM0dQbzFFT09wSXpTTFdSVGtDTkRqdHZ3cm5TZWNQMy9IZTBldTh1SjlzYk5IYU41dlNwakxpeU9MeERmRkp5MzRwVXduSEFWckFwRWZlWWdONW1HTEtIUGVkOEgvaTgva3pDc29maEcyeWwzczZiQk1ucUh0clNMWWkiLCJtYWMiOiJmOTU1ZThmNzZlZDQzOTkxZmJlZTZlMzExZmEwOWZhNWNkODM4MDE2MjBiYzM5ZGE4Yzc2NWM1Y2EyNzcyOWQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-231554669\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1595563351 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WuYcucCgl4bBOLSdLTqnajP2UNBzqFh0FtUk6GCo</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">m2aShS6RaaTcTNL9OqnWxuHeTxyTGRJLBdK2tqlv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1595563351\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-740197694 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:14:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZIaDM4UTFybm5pUGlFeUQyeHlhK2c9PSIsInZhbHVlIjoiQ1lsK05nS1lQZ1RUVSt4ZlRSaWVqOWxLVVk3YzJzazducTZnZitwcTlSbFRUTmdPL21tVjFiT2xvZFMzSnRtTm1mMTJiSGxielczRkViRGVvQTRBenFqU2VOQXJhTUFBVEhOd1NNc0VnY09rbU1TMGx0NmlJK3hlQ3UrZFptbTZiL2dtUFNyTFZJckxnTll1STRSdDlTcnZoTUNGL0hlUVBycnFMN05YM25waS9oeDhCNWhMemRSeWdDQVpuaHA5WEMvNytLNlBoUFZLa1JkSngzRVJ2MmYwQ3o5bFIvZDdSV2JyQmtIYVJOL1RGUFRzSzdpUzJxWWJ4aXp6SXVYMThSM2NLRXlkdnp1V05mVkwyL1hxT1cra2xNZ0ZMMjAvWGV1WUdBTS9zRUg0b2JNNDl6c2w3MDY0Z2hPVXNZVzBJRDNjV0s2UnJzZWY1VXBDT1lqV3lVRG9xYmVSeVJiWmFYZll3RnM2YXFkOFBYU0RWWkd3N0JPREw1K1oxemlCQUltRW1MU2tLV0VpSUFONDgyMy9MaXZiZGRJSnRXV0VTUzNQTzdFTk50SWFYazl2Q0Q3aVNHdWx4OE9KWmthSGI4WVR3SERMbHg1Y05jUldJUU9NV1YyYlFQMjVmL2gyK01CamtBTWluTzlVci9JbmpoQ2o2ZitRY1VNTDFGM1EiLCJtYWMiOiJjMWNmOGQ3OWQ5YzdiYTZjYmQ0MjJiM2YzYTE2NzBhYjJlMzdjNzc1MWIzMjc4YjNjN2MxMDQ2ZDI4ZDk5Y2IyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZOTExpTjgvWVdjZGJrWUpYWGdXRlE9PSIsInZhbHVlIjoiQjZPNVZSbjNmeWYvWEl3WkpJZTdxenVnYXlybGVrUE5kakxpZWFGdEJ1c2c4SXNXbUJPWEd5cDl3eVhJSXJkL2w1Z3FlZHBhNFhOY2k2SFRHcUsxS0ExcWtXUlFNMm1KOTZNUmFzTXdDc1Q2cFNycWFSdTNSVXcwRGJkeU5rQlhOdWM1ZzdJZWxLQkJYNHErY3FnTE54QWR4d21SRi9VNjJSSnVyM1k0WFI2RzBnWm5WUEN5N0RSUDFNMlNEQjgwN0lQV3RJRUswajRoL3dvOHFaUVl6MUw3dkMrQzlucmVObWpLZHZDOUtEaFlGaGJGSURkNWN4T3E3QU8rM08waC9KRVluUTRQcFhYcmdMRnhyQVptazcwQU9RaWJsS3pPSGVmcmZwTnVTNUJZSDdGeUJwREF6ZU45SWdNN3hDU2cxUVhzSjRGM05BdmpNRGhUcTlvUzY2VFk0STFKVmFDenJaNGxhU2VUM1NRL3pQZmtIem56STJETE9GTVhVMzFmUnNXY1hnU2xLUHlJYjkzbTRzYUxUSnhkcmplREY3bGxMTmRqN293WG56bC9sNXNFbVFlUXVENWhrcWFTeXlycExoYlNCUTFTejZrN0RyR0ZBYmN5R1BNWW1tME9yMCtmMDFvY0dKRkpUbHFKeG5hM21xbVJENzFVdTJ3bEszTGMiLCJtYWMiOiI3MWMxZjVkOTYwYTZiZmM5OGFiZThlYjJmNDc1ZjMxNDM2YWZmZWZkOWM0NGJiYzYxNDI2OWEyMDY1YmU2YWI3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZIaDM4UTFybm5pUGlFeUQyeHlhK2c9PSIsInZhbHVlIjoiQ1lsK05nS1lQZ1RUVSt4ZlRSaWVqOWxLVVk3YzJzazducTZnZitwcTlSbFRUTmdPL21tVjFiT2xvZFMzSnRtTm1mMTJiSGxielczRkViRGVvQTRBenFqU2VOQXJhTUFBVEhOd1NNc0VnY09rbU1TMGx0NmlJK3hlQ3UrZFptbTZiL2dtUFNyTFZJckxnTll1STRSdDlTcnZoTUNGL0hlUVBycnFMN05YM25waS9oeDhCNWhMemRSeWdDQVpuaHA5WEMvNytLNlBoUFZLa1JkSngzRVJ2MmYwQ3o5bFIvZDdSV2JyQmtIYVJOL1RGUFRzSzdpUzJxWWJ4aXp6SXVYMThSM2NLRXlkdnp1V05mVkwyL1hxT1cra2xNZ0ZMMjAvWGV1WUdBTS9zRUg0b2JNNDl6c2w3MDY0Z2hPVXNZVzBJRDNjV0s2UnJzZWY1VXBDT1lqV3lVRG9xYmVSeVJiWmFYZll3RnM2YXFkOFBYU0RWWkd3N0JPREw1K1oxemlCQUltRW1MU2tLV0VpSUFONDgyMy9MaXZiZGRJSnRXV0VTUzNQTzdFTk50SWFYazl2Q0Q3aVNHdWx4OE9KWmthSGI4WVR3SERMbHg1Y05jUldJUU9NV1YyYlFQMjVmL2gyK01CamtBTWluTzlVci9JbmpoQ2o2ZitRY1VNTDFGM1EiLCJtYWMiOiJjMWNmOGQ3OWQ5YzdiYTZjYmQ0MjJiM2YzYTE2NzBhYjJlMzdjNzc1MWIzMjc4YjNjN2MxMDQ2ZDI4ZDk5Y2IyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZOTExpTjgvWVdjZGJrWUpYWGdXRlE9PSIsInZhbHVlIjoiQjZPNVZSbjNmeWYvWEl3WkpJZTdxenVnYXlybGVrUE5kakxpZWFGdEJ1c2c4SXNXbUJPWEd5cDl3eVhJSXJkL2w1Z3FlZHBhNFhOY2k2SFRHcUsxS0ExcWtXUlFNMm1KOTZNUmFzTXdDc1Q2cFNycWFSdTNSVXcwRGJkeU5rQlhOdWM1ZzdJZWxLQkJYNHErY3FnTE54QWR4d21SRi9VNjJSSnVyM1k0WFI2RzBnWm5WUEN5N0RSUDFNMlNEQjgwN0lQV3RJRUswajRoL3dvOHFaUVl6MUw3dkMrQzlucmVObWpLZHZDOUtEaFlGaGJGSURkNWN4T3E3QU8rM08waC9KRVluUTRQcFhYcmdMRnhyQVptazcwQU9RaWJsS3pPSGVmcmZwTnVTNUJZSDdGeUJwREF6ZU45SWdNN3hDU2cxUVhzSjRGM05BdmpNRGhUcTlvUzY2VFk0STFKVmFDenJaNGxhU2VUM1NRL3pQZmtIem56STJETE9GTVhVMzFmUnNXY1hnU2xLUHlJYjkzbTRzYUxUSnhkcmplREY3bGxMTmRqN293WG56bC9sNXNFbVFlUXVENWhrcWFTeXlycExoYlNCUTFTejZrN0RyR0ZBYmN5R1BNWW1tME9yMCtmMDFvY0dKRkpUbHFKeG5hM21xbVJENzFVdTJ3bEszTGMiLCJtYWMiOiI3MWMxZjVkOTYwYTZiZmM5OGFiZThlYjJmNDc1ZjMxNDM2YWZmZWZkOWM0NGJiYzYxNDI2OWEyMDY1YmU2YWI3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-740197694\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-31283520 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WuYcucCgl4bBOLSdLTqnajP2UNBzqFh0FtUk6GCo</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-31283520\", {\"maxDepth\":0})</script>\n"}}