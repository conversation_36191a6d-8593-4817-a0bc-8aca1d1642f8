<?php
// This file was auto-generated from sdk-root/src/data/entityresolution/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'endpointPrefix' => 'entityresolution', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceAbbreviation' => 'AWSEntityResolution', 'serviceFullName' => 'AWS EntityResolution', 'serviceId' => 'EntityResolution', 'signatureVersion' => 'v4', 'signingName' => 'entityresolution', 'uid' => 'entityresolution-2018-05-10', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AddPolicyStatement' => [ 'name' => 'AddPolicyStatement', 'http' => [ 'method' => 'POST', 'requestUri' => '/policies/{arn}/{statementId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AddPolicyStatementInput', ], 'output' => [ 'shape' => 'AddPolicyStatementOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'BatchDeleteUniqueId' => [ 'name' => 'BatchDeleteUniqueId', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/matchingworkflows/{workflowName}/uniqueids', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDeleteUniqueIdInput', ], 'output' => [ 'shape' => 'BatchDeleteUniqueIdOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateIdMappingWorkflow' => [ 'name' => 'CreateIdMappingWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/idmappingworkflows', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateIdMappingWorkflowInput', ], 'output' => [ 'shape' => 'CreateIdMappingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExceedsLimitException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateIdNamespace' => [ 'name' => 'CreateIdNamespace', 'http' => [ 'method' => 'POST', 'requestUri' => '/idnamespaces', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateIdNamespaceInput', ], 'output' => [ 'shape' => 'CreateIdNamespaceOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExceedsLimitException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateMatchingWorkflow' => [ 'name' => 'CreateMatchingWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/matchingworkflows', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMatchingWorkflowInput', ], 'output' => [ 'shape' => 'CreateMatchingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExceedsLimitException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateSchemaMapping' => [ 'name' => 'CreateSchemaMapping', 'http' => [ 'method' => 'POST', 'requestUri' => '/schemas', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSchemaMappingInput', ], 'output' => [ 'shape' => 'CreateSchemaMappingOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExceedsLimitException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteIdMappingWorkflow' => [ 'name' => 'DeleteIdMappingWorkflow', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/idmappingworkflows/{workflowName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteIdMappingWorkflowInput', ], 'output' => [ 'shape' => 'DeleteIdMappingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeleteIdNamespace' => [ 'name' => 'DeleteIdNamespace', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/idnamespaces/{idNamespaceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteIdNamespaceInput', ], 'output' => [ 'shape' => 'DeleteIdNamespaceOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeleteMatchingWorkflow' => [ 'name' => 'DeleteMatchingWorkflow', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/matchingworkflows/{workflowName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMatchingWorkflowInput', ], 'output' => [ 'shape' => 'DeleteMatchingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeletePolicyStatement' => [ 'name' => 'DeletePolicyStatement', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/policies/{arn}/{statementId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePolicyStatementInput', ], 'output' => [ 'shape' => 'DeletePolicyStatementOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeleteSchemaMapping' => [ 'name' => 'DeleteSchemaMapping', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/schemas/{schemaName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSchemaMappingInput', ], 'output' => [ 'shape' => 'DeleteSchemaMappingOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'GetIdMappingJob' => [ 'name' => 'GetIdMappingJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/idmappingworkflows/{workflowName}/jobs/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIdMappingJobInput', ], 'output' => [ 'shape' => 'GetIdMappingJobOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetIdMappingWorkflow' => [ 'name' => 'GetIdMappingWorkflow', 'http' => [ 'method' => 'GET', 'requestUri' => '/idmappingworkflows/{workflowName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIdMappingWorkflowInput', ], 'output' => [ 'shape' => 'GetIdMappingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetIdNamespace' => [ 'name' => 'GetIdNamespace', 'http' => [ 'method' => 'GET', 'requestUri' => '/idnamespaces/{idNamespaceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIdNamespaceInput', ], 'output' => [ 'shape' => 'GetIdNamespaceOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetMatchId' => [ 'name' => 'GetMatchId', 'http' => [ 'method' => 'POST', 'requestUri' => '/matchingworkflows/{workflowName}/matches', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMatchIdInput', ], 'output' => [ 'shape' => 'GetMatchIdOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetMatchingJob' => [ 'name' => 'GetMatchingJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/matchingworkflows/{workflowName}/jobs/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMatchingJobInput', ], 'output' => [ 'shape' => 'GetMatchingJobOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetMatchingWorkflow' => [ 'name' => 'GetMatchingWorkflow', 'http' => [ 'method' => 'GET', 'requestUri' => '/matchingworkflows/{workflowName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMatchingWorkflowInput', ], 'output' => [ 'shape' => 'GetMatchingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetPolicy' => [ 'name' => 'GetPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/policies/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPolicyInput', ], 'output' => [ 'shape' => 'GetPolicyOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetProviderService' => [ 'name' => 'GetProviderService', 'http' => [ 'method' => 'GET', 'requestUri' => '/providerservices/{providerName}/{providerServiceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetProviderServiceInput', ], 'output' => [ 'shape' => 'GetProviderServiceOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetSchemaMapping' => [ 'name' => 'GetSchemaMapping', 'http' => [ 'method' => 'GET', 'requestUri' => '/schemas/{schemaName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSchemaMappingInput', ], 'output' => [ 'shape' => 'GetSchemaMappingOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListIdMappingJobs' => [ 'name' => 'ListIdMappingJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/idmappingworkflows/{workflowName}/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIdMappingJobsInput', ], 'output' => [ 'shape' => 'ListIdMappingJobsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListIdMappingWorkflows' => [ 'name' => 'ListIdMappingWorkflows', 'http' => [ 'method' => 'GET', 'requestUri' => '/idmappingworkflows', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIdMappingWorkflowsInput', ], 'output' => [ 'shape' => 'ListIdMappingWorkflowsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListIdNamespaces' => [ 'name' => 'ListIdNamespaces', 'http' => [ 'method' => 'GET', 'requestUri' => '/idnamespaces', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIdNamespacesInput', ], 'output' => [ 'shape' => 'ListIdNamespacesOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListMatchingJobs' => [ 'name' => 'ListMatchingJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/matchingworkflows/{workflowName}/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMatchingJobsInput', ], 'output' => [ 'shape' => 'ListMatchingJobsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListMatchingWorkflows' => [ 'name' => 'ListMatchingWorkflows', 'http' => [ 'method' => 'GET', 'requestUri' => '/matchingworkflows', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMatchingWorkflowsInput', ], 'output' => [ 'shape' => 'ListMatchingWorkflowsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListProviderServices' => [ 'name' => 'ListProviderServices', 'http' => [ 'method' => 'GET', 'requestUri' => '/providerservices', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListProviderServicesInput', ], 'output' => [ 'shape' => 'ListProviderServicesOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListSchemaMappings' => [ 'name' => 'ListSchemaMappings', 'http' => [ 'method' => 'GET', 'requestUri' => '/schemas', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSchemaMappingsInput', ], 'output' => [ 'shape' => 'ListSchemaMappingsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceInput', ], 'output' => [ 'shape' => 'ListTagsForResourceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'PutPolicy' => [ 'name' => 'PutPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/policies/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutPolicyInput', ], 'output' => [ 'shape' => 'PutPolicyOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'StartIdMappingJob' => [ 'name' => 'StartIdMappingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/idmappingworkflows/{workflowName}/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartIdMappingJobInput', ], 'output' => [ 'shape' => 'StartIdMappingJobOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExceedsLimitException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'StartMatchingJob' => [ 'name' => 'StartMatchingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/matchingworkflows/{workflowName}/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartMatchingJobInput', ], 'output' => [ 'shape' => 'StartMatchingJobOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExceedsLimitException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceInput', ], 'output' => [ 'shape' => 'TagResourceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceInput', ], 'output' => [ 'shape' => 'UntagResourceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateIdMappingWorkflow' => [ 'name' => 'UpdateIdMappingWorkflow', 'http' => [ 'method' => 'PUT', 'requestUri' => '/idmappingworkflows/{workflowName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateIdMappingWorkflowInput', ], 'output' => [ 'shape' => 'UpdateIdMappingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdateIdNamespace' => [ 'name' => 'UpdateIdNamespace', 'http' => [ 'method' => 'PUT', 'requestUri' => '/idnamespaces/{idNamespaceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateIdNamespaceInput', ], 'output' => [ 'shape' => 'UpdateIdNamespaceOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdateMatchingWorkflow' => [ 'name' => 'UpdateMatchingWorkflow', 'http' => [ 'method' => 'PUT', 'requestUri' => '/matchingworkflows/{workflowName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMatchingWorkflowInput', ], 'output' => [ 'shape' => 'UpdateMatchingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdateSchemaMapping' => [ 'name' => 'UpdateSchemaMapping', 'http' => [ 'method' => 'PUT', 'requestUri' => '/schemas/{schemaName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSchemaMappingInput', ], 'output' => [ 'shape' => 'UpdateSchemaMappingOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AddPolicyStatementInput' => [ 'type' => 'structure', 'required' => [ 'action', 'arn', 'effect', 'principal', 'statementId', ], 'members' => [ 'action' => [ 'shape' => 'StatementActionList', ], 'arn' => [ 'shape' => 'VeniceGlobalArn', 'location' => 'uri', 'locationName' => 'arn', ], 'condition' => [ 'shape' => 'StatementCondition', ], 'effect' => [ 'shape' => 'StatementEffect', ], 'principal' => [ 'shape' => 'StatementPrincipalList', ], 'statementId' => [ 'shape' => 'StatementId', 'location' => 'uri', 'locationName' => 'statementId', ], ], ], 'AddPolicyStatementOutput' => [ 'type' => 'structure', 'required' => [ 'arn', 'token', ], 'members' => [ 'arn' => [ 'shape' => 'VeniceGlobalArn', ], 'policy' => [ 'shape' => 'PolicyDocument', ], 'token' => [ 'shape' => 'PolicyToken', ], ], ], 'AttributeMatchingModel' => [ 'type' => 'string', 'enum' => [ 'ONE_TO_ONE', 'MANY_TO_MANY', ], ], 'AttributeName' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^[a-zA-Z_0-9- \\t]*$', ], 'AwsAccountId' => [ 'type' => 'string', 'pattern' => '^\\d{12}$', ], 'AwsAccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsAccountId', ], ], 'BatchDeleteUniqueIdInput' => [ 'type' => 'structure', 'required' => [ 'uniqueIds', 'workflowName', ], 'members' => [ 'inputSource' => [ 'shape' => 'BatchDeleteUniqueIdInputInputSourceString', 'location' => 'header', 'locationName' => 'inputSource', ], 'uniqueIds' => [ 'shape' => 'UniqueIdList', 'location' => 'header', 'locationName' => 'uniqueIds', ], 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'BatchDeleteUniqueIdInputInputSourceString' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-us-gov|aws-cn):glue:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(table/[a-zA-Z_0-9-]{1,255}/[a-zA-Z_0-9-]{1,255})$', ], 'BatchDeleteUniqueIdOutput' => [ 'type' => 'structure', 'required' => [ 'deleted', 'disconnectedUniqueIds', 'errors', 'status', ], 'members' => [ 'deleted' => [ 'shape' => 'DeletedUniqueIdList', ], 'disconnectedUniqueIds' => [ 'shape' => 'DisconnectedUniqueIdsList', ], 'errors' => [ 'shape' => 'DeleteUniqueIdErrorsList', ], 'status' => [ 'shape' => 'DeleteUniqueIdStatus', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CreateIdMappingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'idMappingTechniques', 'inputSourceConfig', 'workflowName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'idMappingTechniques' => [ 'shape' => 'IdMappingTechniques', ], 'inputSourceConfig' => [ 'shape' => 'IdMappingWorkflowInputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingWorkflowOutputSourceConfig', ], 'roleArn' => [ 'shape' => 'IdMappingRoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'CreateIdMappingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'idMappingTechniques', 'inputSourceConfig', 'workflowArn', 'workflowName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'idMappingTechniques' => [ 'shape' => 'IdMappingTechniques', ], 'inputSourceConfig' => [ 'shape' => 'IdMappingWorkflowInputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingWorkflowOutputSourceConfig', ], 'roleArn' => [ 'shape' => 'IdMappingRoleArn', ], 'workflowArn' => [ 'shape' => 'IdMappingWorkflowArn', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'CreateIdNamespaceInput' => [ 'type' => 'structure', 'required' => [ 'idNamespaceName', 'type', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'idMappingWorkflowProperties' => [ 'shape' => 'IdNamespaceIdMappingWorkflowPropertiesList', ], 'idNamespaceName' => [ 'shape' => 'EntityName', ], 'inputSourceConfig' => [ 'shape' => 'IdNamespaceInputSourceConfig', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], 'type' => [ 'shape' => 'IdNamespaceType', ], ], ], 'CreateIdNamespaceOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'idNamespaceArn', 'idNamespaceName', 'type', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'idMappingWorkflowProperties' => [ 'shape' => 'IdNamespaceIdMappingWorkflowPropertiesList', ], 'idNamespaceArn' => [ 'shape' => 'IdNamespaceArn', ], 'idNamespaceName' => [ 'shape' => 'EntityName', ], 'inputSourceConfig' => [ 'shape' => 'IdNamespaceInputSourceConfig', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], 'type' => [ 'shape' => 'IdNamespaceType', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'CreateMatchingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'inputSourceConfig', 'outputSourceConfig', 'resolutionTechniques', 'roleArn', 'workflowName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'incrementalRunConfig' => [ 'shape' => 'IncrementalRunConfig', ], 'inputSourceConfig' => [ 'shape' => 'InputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'OutputSourceConfig', ], 'resolutionTechniques' => [ 'shape' => 'ResolutionTechniques', ], 'roleArn' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagMap', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'CreateMatchingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'inputSourceConfig', 'outputSourceConfig', 'resolutionTechniques', 'roleArn', 'workflowArn', 'workflowName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'incrementalRunConfig' => [ 'shape' => 'IncrementalRunConfig', ], 'inputSourceConfig' => [ 'shape' => 'InputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'OutputSourceConfig', ], 'resolutionTechniques' => [ 'shape' => 'ResolutionTechniques', ], 'roleArn' => [ 'shape' => 'String', ], 'workflowArn' => [ 'shape' => 'MatchingWorkflowArn', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'CreateSchemaMappingInput' => [ 'type' => 'structure', 'required' => [ 'mappedInputFields', 'schemaName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'mappedInputFields' => [ 'shape' => 'SchemaInputAttributes', ], 'schemaName' => [ 'shape' => 'EntityName', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateSchemaMappingOutput' => [ 'type' => 'structure', 'required' => [ 'description', 'mappedInputFields', 'schemaArn', 'schemaName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'mappedInputFields' => [ 'shape' => 'SchemaInputAttributes', ], 'schemaArn' => [ 'shape' => 'SchemaMappingArn', ], 'schemaName' => [ 'shape' => 'EntityName', ], ], ], 'DeleteIdMappingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'DeleteIdMappingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], ], 'DeleteIdNamespaceInput' => [ 'type' => 'structure', 'required' => [ 'idNamespaceName', ], 'members' => [ 'idNamespaceName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'idNamespaceName', ], ], ], 'DeleteIdNamespaceOutput' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], ], 'DeleteMatchingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'DeleteMatchingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], ], 'DeletePolicyStatementInput' => [ 'type' => 'structure', 'required' => [ 'arn', 'statementId', ], 'members' => [ 'arn' => [ 'shape' => 'VeniceGlobalArn', 'location' => 'uri', 'locationName' => 'arn', ], 'statementId' => [ 'shape' => 'StatementId', 'location' => 'uri', 'locationName' => 'statementId', ], ], ], 'DeletePolicyStatementOutput' => [ 'type' => 'structure', 'required' => [ 'arn', 'token', ], 'members' => [ 'arn' => [ 'shape' => 'VeniceGlobalArn', ], 'policy' => [ 'shape' => 'PolicyDocument', ], 'token' => [ 'shape' => 'PolicyToken', ], ], ], 'DeleteSchemaMappingInput' => [ 'type' => 'structure', 'required' => [ 'schemaName', ], 'members' => [ 'schemaName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'schemaName', ], ], ], 'DeleteSchemaMappingOutput' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], ], 'DeleteUniqueIdError' => [ 'type' => 'structure', 'required' => [ 'errorType', 'uniqueId', ], 'members' => [ 'errorType' => [ 'shape' => 'DeleteUniqueIdErrorType', ], 'uniqueId' => [ 'shape' => 'UniqueId', ], ], ], 'DeleteUniqueIdErrorType' => [ 'type' => 'string', 'enum' => [ 'SERVICE_ERROR', 'VALIDATION_ERROR', ], ], 'DeleteUniqueIdErrorsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeleteUniqueIdError', ], ], 'DeleteUniqueIdStatus' => [ 'type' => 'string', 'enum' => [ 'COMPLETED', 'ACCEPTED', ], ], 'DeletedUniqueId' => [ 'type' => 'structure', 'required' => [ 'uniqueId', ], 'members' => [ 'uniqueId' => [ 'shape' => 'UniqueId', ], ], ], 'DeletedUniqueIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeletedUniqueId', ], ], 'Description' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'DisconnectedUniqueIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UniqueId', ], ], 'Document' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'EntityName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z_0-9-]*$', ], 'EntityNameOrIdMappingWorkflowArn' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z_0-9-=+/]*$|^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(idmappingworkflow/[a-zA-Z_0-9-]{1,255})$', ], 'EntityNameOrIdNamespaceArn' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z_0-9-=+/]*$|^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(idnamespace/[a-zA-Z_0-9-]{1,255})$', ], 'ErrorDetails' => [ 'type' => 'structure', 'members' => [ 'errorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ExceedsLimitException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'quotaName' => [ 'shape' => 'String', ], 'quotaValue' => [ 'shape' => 'Integer', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'GetIdMappingJobInput' => [ 'type' => 'structure', 'required' => [ 'jobId', 'workflowName', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'workflowName' => [ 'shape' => 'EntityNameOrIdMappingWorkflowArn', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'GetIdMappingJobOutput' => [ 'type' => 'structure', 'required' => [ 'jobId', 'startTime', 'status', ], 'members' => [ 'endTime' => [ 'shape' => 'Timestamp', ], 'errorDetails' => [ 'shape' => 'ErrorDetails', ], 'jobId' => [ 'shape' => 'JobId', ], 'metrics' => [ 'shape' => 'IdMappingJobMetrics', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingJobOutputSourceConfig', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'JobStatus', ], ], ], 'GetIdMappingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'GetIdMappingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'idMappingTechniques', 'inputSourceConfig', 'updatedAt', 'workflowArn', 'workflowName', ], 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'idMappingTechniques' => [ 'shape' => 'IdMappingTechniques', ], 'inputSourceConfig' => [ 'shape' => 'IdMappingWorkflowInputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingWorkflowOutputSourceConfig', ], 'roleArn' => [ 'shape' => 'IdMappingRoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'workflowArn' => [ 'shape' => 'IdMappingWorkflowArn', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'GetIdNamespaceInput' => [ 'type' => 'structure', 'required' => [ 'idNamespaceName', ], 'members' => [ 'idNamespaceName' => [ 'shape' => 'EntityNameOrIdNamespaceArn', 'location' => 'uri', 'locationName' => 'idNamespaceName', ], ], ], 'GetIdNamespaceOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'idNamespaceArn', 'idNamespaceName', 'type', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'idMappingWorkflowProperties' => [ 'shape' => 'IdNamespaceIdMappingWorkflowPropertiesList', ], 'idNamespaceArn' => [ 'shape' => 'IdNamespaceArn', ], 'idNamespaceName' => [ 'shape' => 'EntityName', ], 'inputSourceConfig' => [ 'shape' => 'IdNamespaceInputSourceConfig', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], 'type' => [ 'shape' => 'IdNamespaceType', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'GetMatchIdInput' => [ 'type' => 'structure', 'required' => [ 'record', 'workflowName', ], 'members' => [ 'applyNormalization' => [ 'shape' => 'Boolean', ], 'record' => [ 'shape' => 'RecordAttributeMap', ], 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'GetMatchIdOutput' => [ 'type' => 'structure', 'members' => [ 'matchId' => [ 'shape' => 'String', ], 'matchRule' => [ 'shape' => 'String', ], ], ], 'GetMatchingJobInput' => [ 'type' => 'structure', 'required' => [ 'jobId', 'workflowName', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'GetMatchingJobOutput' => [ 'type' => 'structure', 'required' => [ 'jobId', 'startTime', 'status', ], 'members' => [ 'endTime' => [ 'shape' => 'Timestamp', ], 'errorDetails' => [ 'shape' => 'ErrorDetails', ], 'jobId' => [ 'shape' => 'JobId', ], 'metrics' => [ 'shape' => 'JobMetrics', ], 'outputSourceConfig' => [ 'shape' => 'JobOutputSourceConfig', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'JobStatus', ], ], ], 'GetMatchingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'GetMatchingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'inputSourceConfig', 'outputSourceConfig', 'resolutionTechniques', 'roleArn', 'updatedAt', 'workflowArn', 'workflowName', ], 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'incrementalRunConfig' => [ 'shape' => 'IncrementalRunConfig', ], 'inputSourceConfig' => [ 'shape' => 'InputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'OutputSourceConfig', ], 'resolutionTechniques' => [ 'shape' => 'ResolutionTechniques', ], 'roleArn' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagMap', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'workflowArn' => [ 'shape' => 'MatchingWorkflowArn', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'GetPolicyInput' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'VeniceGlobalArn', 'location' => 'uri', 'locationName' => 'arn', ], ], ], 'GetPolicyOutput' => [ 'type' => 'structure', 'required' => [ 'arn', 'token', ], 'members' => [ 'arn' => [ 'shape' => 'VeniceGlobalArn', ], 'policy' => [ 'shape' => 'PolicyDocument', ], 'token' => [ 'shape' => 'PolicyToken', ], ], ], 'GetProviderServiceInput' => [ 'type' => 'structure', 'required' => [ 'providerName', 'providerServiceName', ], 'members' => [ 'providerName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'providerName', ], 'providerServiceName' => [ 'shape' => 'ProviderServiceArn', 'location' => 'uri', 'locationName' => 'providerServiceName', ], ], ], 'GetProviderServiceOutput' => [ 'type' => 'structure', 'required' => [ 'anonymizedOutput', 'providerEndpointConfiguration', 'providerEntityOutputDefinition', 'providerName', 'providerServiceArn', 'providerServiceDisplayName', 'providerServiceName', 'providerServiceType', ], 'members' => [ 'anonymizedOutput' => [ 'shape' => 'Boolean', ], 'providerComponentSchema' => [ 'shape' => 'ProviderComponentSchema', ], 'providerConfigurationDefinition' => [ 'shape' => 'Document', ], 'providerEndpointConfiguration' => [ 'shape' => 'ProviderEndpointConfiguration', ], 'providerEntityOutputDefinition' => [ 'shape' => 'Document', ], 'providerIdNameSpaceConfiguration' => [ 'shape' => 'ProviderIdNameSpaceConfiguration', ], 'providerIntermediateDataAccessConfiguration' => [ 'shape' => 'ProviderIntermediateDataAccessConfiguration', ], 'providerJobConfiguration' => [ 'shape' => 'Document', ], 'providerName' => [ 'shape' => 'EntityName', ], 'providerServiceArn' => [ 'shape' => 'ProviderServiceArn', ], 'providerServiceDisplayName' => [ 'shape' => 'ProviderServiceDisplayName', ], 'providerServiceName' => [ 'shape' => 'EntityName', ], 'providerServiceType' => [ 'shape' => 'ServiceType', ], ], ], 'GetSchemaMappingInput' => [ 'type' => 'structure', 'required' => [ 'schemaName', ], 'members' => [ 'schemaName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'schemaName', ], ], ], 'GetSchemaMappingOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'hasWorkflows', 'mappedInputFields', 'schemaArn', 'schemaName', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'hasWorkflows' => [ 'shape' => 'Boolean', ], 'mappedInputFields' => [ 'shape' => 'SchemaInputAttributes', ], 'schemaArn' => [ 'shape' => 'SchemaMappingArn', ], 'schemaName' => [ 'shape' => 'EntityName', ], 'tags' => [ 'shape' => 'TagMap', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'IdMappingJobMetrics' => [ 'type' => 'structure', 'members' => [ 'inputRecords' => [ 'shape' => 'Integer', ], 'recordsNotProcessed' => [ 'shape' => 'Integer', ], 'totalMappedRecords' => [ 'shape' => 'Integer', ], 'totalMappedSourceRecords' => [ 'shape' => 'Integer', ], 'totalMappedTargetRecords' => [ 'shape' => 'Integer', ], 'totalRecordsProcessed' => [ 'shape' => 'Integer', ], ], ], 'IdMappingJobOutputSource' => [ 'type' => 'structure', 'required' => [ 'outputS3Path', 'roleArn', ], 'members' => [ 'KMSArn' => [ 'shape' => 'KMSArn', ], 'outputS3Path' => [ 'shape' => 'S3Path', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'IdMappingJobOutputSourceConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdMappingJobOutputSource', ], 'max' => 1, 'min' => 1, ], 'IdMappingRoleArn' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '^$|^arn:aws:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+$', ], 'IdMappingRuleBasedProperties' => [ 'type' => 'structure', 'required' => [ 'attributeMatchingModel', 'recordMatchingModel', 'ruleDefinitionType', ], 'members' => [ 'attributeMatchingModel' => [ 'shape' => 'AttributeMatchingModel', ], 'recordMatchingModel' => [ 'shape' => 'RecordMatchingModel', ], 'ruleDefinitionType' => [ 'shape' => 'IdMappingWorkflowRuleDefinitionType', ], 'rules' => [ 'shape' => 'IdMappingRuleBasedPropertiesRulesList', ], ], ], 'IdMappingRuleBasedPropertiesRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Rule', ], 'max' => 25, 'min' => 1, ], 'IdMappingTechniques' => [ 'type' => 'structure', 'required' => [ 'idMappingType', ], 'members' => [ 'idMappingType' => [ 'shape' => 'IdMappingType', ], 'providerProperties' => [ 'shape' => 'ProviderProperties', ], 'ruleBasedProperties' => [ 'shape' => 'IdMappingRuleBasedProperties', ], ], ], 'IdMappingType' => [ 'type' => 'string', 'enum' => [ 'PROVIDER', 'RULE_BASED', ], ], 'IdMappingWorkflowArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(idmappingworkflow/[a-zA-Z_0-9-]{1,255})$', ], 'IdMappingWorkflowInputSource' => [ 'type' => 'structure', 'required' => [ 'inputSourceARN', ], 'members' => [ 'inputSourceARN' => [ 'shape' => 'IdMappingWorkflowInputSourceInputSourceARNString', ], 'schemaName' => [ 'shape' => 'EntityName', ], 'type' => [ 'shape' => 'IdNamespaceType', ], ], ], 'IdMappingWorkflowInputSourceConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdMappingWorkflowInputSource', ], 'max' => 20, 'min' => 1, ], 'IdMappingWorkflowInputSourceInputSourceARNString' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(idnamespace/[a-zA-Z_0-9-]{1,255})$|^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(matchingworkflow/[a-zA-Z_0-9-]{1,255})$|^arn:(aws|aws-us-gov|aws-cn):glue:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(table/[a-zA-Z_0-9-]{1,255}/[a-zA-Z_0-9-]{1,255})$', ], 'IdMappingWorkflowList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdMappingWorkflowSummary', ], ], 'IdMappingWorkflowOutputSource' => [ 'type' => 'structure', 'required' => [ 'outputS3Path', ], 'members' => [ 'KMSArn' => [ 'shape' => 'KMSArn', ], 'outputS3Path' => [ 'shape' => 'S3Path', ], ], ], 'IdMappingWorkflowOutputSourceConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdMappingWorkflowOutputSource', ], 'max' => 1, 'min' => 1, ], 'IdMappingWorkflowRuleDefinitionType' => [ 'type' => 'string', 'enum' => [ 'SOURCE', 'TARGET', ], ], 'IdMappingWorkflowRuleDefinitionTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdMappingWorkflowRuleDefinitionType', ], ], 'IdMappingWorkflowSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'updatedAt', 'workflowArn', 'workflowName', ], 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'workflowArn' => [ 'shape' => 'IdMappingWorkflowArn', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'IdNamespaceArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(idnamespace/[a-zA-Z_0-9-]{1,255})$', ], 'IdNamespaceIdMappingWorkflowMetadata' => [ 'type' => 'structure', 'required' => [ 'idMappingType', ], 'members' => [ 'idMappingType' => [ 'shape' => 'IdMappingType', ], ], ], 'IdNamespaceIdMappingWorkflowMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdNamespaceIdMappingWorkflowMetadata', ], 'max' => 1, 'min' => 1, ], 'IdNamespaceIdMappingWorkflowProperties' => [ 'type' => 'structure', 'required' => [ 'idMappingType', ], 'members' => [ 'idMappingType' => [ 'shape' => 'IdMappingType', ], 'providerProperties' => [ 'shape' => 'NamespaceProviderProperties', ], 'ruleBasedProperties' => [ 'shape' => 'NamespaceRuleBasedProperties', ], ], ], 'IdNamespaceIdMappingWorkflowPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdNamespaceIdMappingWorkflowProperties', ], 'max' => 1, 'min' => 1, ], 'IdNamespaceInputSource' => [ 'type' => 'structure', 'required' => [ 'inputSourceARN', ], 'members' => [ 'inputSourceARN' => [ 'shape' => 'IdNamespaceInputSourceInputSourceARNString', ], 'schemaName' => [ 'shape' => 'EntityName', ], ], ], 'IdNamespaceInputSourceConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdNamespaceInputSource', ], 'max' => 20, 'min' => 0, ], 'IdNamespaceInputSourceInputSourceARNString' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(idnamespace/[a-zA-Z_0-9-]{1,255})$|^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(matchingworkflow/[a-zA-Z_0-9-]{1,255})$|^arn:(aws|aws-us-gov|aws-cn):glue:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(table/[a-zA-Z_0-9-]{1,255}/[a-zA-Z_0-9-]{1,255})$', ], 'IdNamespaceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdNamespaceSummary', ], ], 'IdNamespaceSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'idNamespaceArn', 'idNamespaceName', 'type', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'idMappingWorkflowProperties' => [ 'shape' => 'IdNamespaceIdMappingWorkflowMetadataList', ], 'idNamespaceArn' => [ 'shape' => 'IdNamespaceArn', ], 'idNamespaceName' => [ 'shape' => 'EntityName', ], 'type' => [ 'shape' => 'IdNamespaceType', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'IdNamespaceType' => [ 'type' => 'string', 'enum' => [ 'SOURCE', 'TARGET', ], ], 'IncrementalRunConfig' => [ 'type' => 'structure', 'members' => [ 'incrementalRunType' => [ 'shape' => 'IncrementalRunType', ], ], ], 'IncrementalRunType' => [ 'type' => 'string', 'enum' => [ 'IMMEDIATE', ], ], 'InputSource' => [ 'type' => 'structure', 'required' => [ 'inputSourceARN', 'schemaName', ], 'members' => [ 'applyNormalization' => [ 'shape' => 'Boolean', ], 'inputSourceARN' => [ 'shape' => 'InputSourceInputSourceARNString', ], 'schemaName' => [ 'shape' => 'EntityName', ], ], ], 'InputSourceConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputSource', ], 'max' => 20, 'min' => 1, ], 'InputSourceInputSourceARNString' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(idnamespace/[a-zA-Z_0-9-]{1,255})$|^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(matchingworkflow/[a-zA-Z_0-9-]{1,255})$|^arn:(aws|aws-us-gov|aws-cn):glue:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(table/[a-zA-Z_0-9-]{1,255}/[a-zA-Z_0-9-]{1,255})$', ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'IntermediateSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'intermediateS3Path', ], 'members' => [ 'intermediateS3Path' => [ 'shape' => 'S3Path', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'JobId' => [ 'type' => 'string', 'pattern' => '^[a-f0-9]{32}$', ], 'JobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobSummary', ], ], 'JobMetrics' => [ 'type' => 'structure', 'members' => [ 'inputRecords' => [ 'shape' => 'Integer', ], 'matchIDs' => [ 'shape' => 'Integer', ], 'recordsNotProcessed' => [ 'shape' => 'Integer', ], 'totalRecordsProcessed' => [ 'shape' => 'Integer', ], ], ], 'JobOutputSource' => [ 'type' => 'structure', 'required' => [ 'outputS3Path', 'roleArn', ], 'members' => [ 'KMSArn' => [ 'shape' => 'KMSArn', ], 'outputS3Path' => [ 'shape' => 'S3Path', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'JobOutputSourceConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobOutputSource', ], 'max' => 1, 'min' => 1, ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'SUCCEEDED', 'FAILED', 'QUEUED', ], ], 'JobSummary' => [ 'type' => 'structure', 'required' => [ 'jobId', 'startTime', 'status', ], 'members' => [ 'endTime' => [ 'shape' => 'Timestamp', ], 'jobId' => [ 'shape' => 'JobId', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'JobStatus', ], ], ], 'KMSArn' => [ 'type' => 'string', 'pattern' => '^arn:aws:kms:.*:[0-9]+:.*$', ], 'ListIdMappingJobsInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'maxResults' => [ 'shape' => 'ListIdMappingJobsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'workflowName' => [ 'shape' => 'EntityNameOrIdMappingWorkflowArn', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'ListIdMappingJobsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'ListIdMappingJobsOutput' => [ 'type' => 'structure', 'members' => [ 'jobs' => [ 'shape' => 'JobList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIdMappingWorkflowsInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListIdMappingWorkflowsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListIdMappingWorkflowsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, ], 'ListIdMappingWorkflowsOutput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'workflowSummaries' => [ 'shape' => 'IdMappingWorkflowList', ], ], ], 'ListIdNamespacesInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListIdNamespacesInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListIdNamespacesInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, ], 'ListIdNamespacesOutput' => [ 'type' => 'structure', 'members' => [ 'idNamespaceSummaries' => [ 'shape' => 'IdNamespaceList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMatchingJobsInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'maxResults' => [ 'shape' => 'ListMatchingJobsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'ListMatchingJobsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'ListMatchingJobsOutput' => [ 'type' => 'structure', 'members' => [ 'jobs' => [ 'shape' => 'JobList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMatchingWorkflowsInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListMatchingWorkflowsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListMatchingWorkflowsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, ], 'ListMatchingWorkflowsOutput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'workflowSummaries' => [ 'shape' => 'MatchingWorkflowList', ], ], ], 'ListProviderServicesInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListProviderServicesInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'providerName' => [ 'shape' => 'EntityName', 'location' => 'querystring', 'locationName' => 'providerName', ], ], ], 'ListProviderServicesInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 15, ], 'ListProviderServicesOutput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'providerServiceSummaries' => [ 'shape' => 'ProviderServiceList', ], ], ], 'ListSchemaMappingsInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListSchemaMappingsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListSchemaMappingsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, ], 'ListSchemaMappingsOutput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'schemaList' => [ 'shape' => 'SchemaMappingList', ], ], ], 'ListTagsForResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'VeniceGlobalArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceOutput' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'MatchPurpose' => [ 'type' => 'string', 'enum' => [ 'IDENTIFIER_GENERATION', 'INDEXING', ], ], 'MatchingWorkflowArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(matchingworkflow/[a-zA-Z_0-9-]{1,255})$', ], 'MatchingWorkflowList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatchingWorkflowSummary', ], ], 'MatchingWorkflowSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'resolutionType', 'updatedAt', 'workflowArn', 'workflowName', ], 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'resolutionType' => [ 'shape' => 'ResolutionType', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'workflowArn' => [ 'shape' => 'MatchingWorkflowArn', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'NamespaceProviderProperties' => [ 'type' => 'structure', 'required' => [ 'providerServiceArn', ], 'members' => [ 'providerConfiguration' => [ 'shape' => 'Document', ], 'providerServiceArn' => [ 'shape' => 'ProviderServiceArn', ], ], ], 'NamespaceRuleBasedProperties' => [ 'type' => 'structure', 'members' => [ 'attributeMatchingModel' => [ 'shape' => 'AttributeMatchingModel', ], 'recordMatchingModels' => [ 'shape' => 'RecordMatchingModelList', ], 'ruleDefinitionTypes' => [ 'shape' => 'IdMappingWorkflowRuleDefinitionTypeList', ], 'rules' => [ 'shape' => 'NamespaceRuleBasedPropertiesRulesList', ], ], ], 'NamespaceRuleBasedPropertiesRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Rule', ], 'max' => 25, 'min' => 1, ], 'NextToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[a-zA-Z_0-9-=+/]*$', ], 'OutputAttribute' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'hashed' => [ 'shape' => 'Boolean', ], 'name' => [ 'shape' => 'AttributeName', ], ], ], 'OutputSource' => [ 'type' => 'structure', 'required' => [ 'output', 'outputS3Path', ], 'members' => [ 'KMSArn' => [ 'shape' => 'KMSArn', ], 'applyNormalization' => [ 'shape' => 'Boolean', ], 'output' => [ 'shape' => 'OutputSourceOutputList', ], 'outputS3Path' => [ 'shape' => 'S3Path', ], ], ], 'OutputSourceConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputSource', ], 'max' => 1, 'min' => 1, ], 'OutputSourceOutputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputAttribute', ], 'max' => 750, 'min' => 0, ], 'PolicyDocument' => [ 'type' => 'string', 'max' => 40960, 'min' => 1, ], 'PolicyToken' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$', ], 'ProviderComponentSchema' => [ 'type' => 'structure', 'members' => [ 'providerSchemaAttributes' => [ 'shape' => 'ProviderSchemaAttributes', ], 'schemas' => [ 'shape' => 'Schemas', ], ], ], 'ProviderEndpointConfiguration' => [ 'type' => 'structure', 'members' => [ 'marketplaceConfiguration' => [ 'shape' => 'ProviderMarketplaceConfiguration', ], ], 'union' => true, ], 'ProviderIdNameSpaceConfiguration' => [ 'type' => 'structure', 'members' => [ 'description' => [ 'shape' => 'String', ], 'providerSourceConfigurationDefinition' => [ 'shape' => 'Document', ], 'providerTargetConfigurationDefinition' => [ 'shape' => 'Document', ], ], ], 'ProviderIntermediateDataAccessConfiguration' => [ 'type' => 'structure', 'members' => [ 'awsAccountIds' => [ 'shape' => 'AwsAccountIdList', ], 'requiredBucketActions' => [ 'shape' => 'RequiredBucketActionsList', ], ], ], 'ProviderMarketplaceConfiguration' => [ 'type' => 'structure', 'required' => [ 'assetId', 'dataSetId', 'listingId', 'revisionId', ], 'members' => [ 'assetId' => [ 'shape' => 'String', ], 'dataSetId' => [ 'shape' => 'String', ], 'listingId' => [ 'shape' => 'String', ], 'revisionId' => [ 'shape' => 'String', ], ], ], 'ProviderProperties' => [ 'type' => 'structure', 'required' => [ 'providerServiceArn', ], 'members' => [ 'intermediateSourceConfiguration' => [ 'shape' => 'IntermediateSourceConfiguration', ], 'providerConfiguration' => [ 'shape' => 'Document', ], 'providerServiceArn' => [ 'shape' => 'ProviderServiceArn', ], ], ], 'ProviderSchemaAttribute' => [ 'type' => 'structure', 'required' => [ 'fieldName', 'type', ], 'members' => [ 'fieldName' => [ 'shape' => 'AttributeName', ], 'hashing' => [ 'shape' => 'Boolean', ], 'subType' => [ 'shape' => 'AttributeName', ], 'type' => [ 'shape' => 'SchemaAttributeType', ], ], ], 'ProviderSchemaAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProviderSchemaAttribute', ], ], 'ProviderServiceArn' => [ 'type' => 'string', 'max' => 255, 'min' => 20, 'pattern' => '^arn:(aws|aws-us-gov|aws-cn):(entityresolution):([a-z]{2}-[a-z]{1,10}-[0-9])::providerservice/([a-zA-Z0-9_-]{1,255})/([a-zA-Z0-9_-]{1,255})$', ], 'ProviderServiceDisplayName' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'ProviderServiceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProviderServiceSummary', ], ], 'ProviderServiceSummary' => [ 'type' => 'structure', 'required' => [ 'providerName', 'providerServiceArn', 'providerServiceDisplayName', 'providerServiceName', 'providerServiceType', ], 'members' => [ 'providerName' => [ 'shape' => 'EntityName', ], 'providerServiceArn' => [ 'shape' => 'ProviderServiceArn', ], 'providerServiceDisplayName' => [ 'shape' => 'ProviderServiceDisplayName', ], 'providerServiceName' => [ 'shape' => 'EntityName', ], 'providerServiceType' => [ 'shape' => 'ServiceType', ], ], ], 'PutPolicyInput' => [ 'type' => 'structure', 'required' => [ 'arn', 'policy', ], 'members' => [ 'arn' => [ 'shape' => 'VeniceGlobalArn', 'location' => 'uri', 'locationName' => 'arn', ], 'policy' => [ 'shape' => 'PolicyDocument', ], 'token' => [ 'shape' => 'PolicyToken', ], ], ], 'PutPolicyOutput' => [ 'type' => 'structure', 'required' => [ 'arn', 'token', ], 'members' => [ 'arn' => [ 'shape' => 'VeniceGlobalArn', ], 'policy' => [ 'shape' => 'PolicyDocument', ], 'token' => [ 'shape' => 'PolicyToken', ], ], ], 'RecordAttributeMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'RecordAttributeMapKeyString', ], 'value' => [ 'shape' => 'RecordAttributeMapValueString', ], 'sensitive' => true, ], 'RecordAttributeMapKeyString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^[a-zA-Z_0-9- \\t]*$', ], 'RecordAttributeMapValueString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^[a-zA-Z_0-9-./@ ()+\\t]*$', ], 'RecordMatchingModel' => [ 'type' => 'string', 'enum' => [ 'ONE_SOURCE_TO_ONE_TARGET', 'MANY_SOURCE_TO_ONE_TARGET', ], ], 'RecordMatchingModelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecordMatchingModel', ], ], 'RequiredBucketActionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ResolutionTechniques' => [ 'type' => 'structure', 'required' => [ 'resolutionType', ], 'members' => [ 'providerProperties' => [ 'shape' => 'ProviderProperties', ], 'resolutionType' => [ 'shape' => 'ResolutionType', ], 'ruleBasedProperties' => [ 'shape' => 'RuleBasedProperties', ], ], ], 'ResolutionType' => [ 'type' => 'string', 'enum' => [ 'RULE_MATCHING', 'ML_MATCHING', 'PROVIDER', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RoleArn' => [ 'type' => 'string', 'max' => 512, 'min' => 32, 'pattern' => '^arn:aws:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+$', ], 'Rule' => [ 'type' => 'structure', 'required' => [ 'matchingKeys', 'ruleName', ], 'members' => [ 'matchingKeys' => [ 'shape' => 'RuleMatchingKeysList', ], 'ruleName' => [ 'shape' => 'RuleRuleNameString', ], ], ], 'RuleBasedProperties' => [ 'type' => 'structure', 'required' => [ 'attributeMatchingModel', 'rules', ], 'members' => [ 'attributeMatchingModel' => [ 'shape' => 'AttributeMatchingModel', ], 'matchPurpose' => [ 'shape' => 'MatchPurpose', ], 'rules' => [ 'shape' => 'RuleBasedPropertiesRulesList', ], ], ], 'RuleBasedPropertiesRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Rule', ], 'max' => 25, 'min' => 1, ], 'RuleMatchingKeysList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeName', ], 'max' => 15, 'min' => 1, ], 'RuleRuleNameString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^[a-zA-Z_0-9- \\t]*$', ], 'S3Path' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^s3://[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9](/.*)?$', ], 'SchemaAttributeType' => [ 'type' => 'string', 'enum' => [ 'NAME', 'NAME_FIRST', 'NAME_MIDDLE', 'NAME_LAST', 'ADDRESS', 'ADDRESS_STREET1', 'ADDRESS_STREET2', 'ADDRESS_STREET3', 'ADDRESS_CITY', 'ADDRESS_STATE', 'ADDRESS_COUNTRY', 'ADDRESS_POSTALCODE', 'PHONE', 'PHONE_NUMBER', 'PHONE_COUNTRYCODE', 'EMAIL_ADDRESS', 'UNIQUE_ID', 'DATE', 'STRING', 'PROVIDER_ID', ], ], 'SchemaInputAttribute' => [ 'type' => 'structure', 'required' => [ 'fieldName', 'type', ], 'members' => [ 'fieldName' => [ 'shape' => 'AttributeName', ], 'groupName' => [ 'shape' => 'AttributeName', ], 'hashed' => [ 'shape' => 'Boolean', ], 'matchKey' => [ 'shape' => 'AttributeName', ], 'subType' => [ 'shape' => 'AttributeName', ], 'type' => [ 'shape' => 'SchemaAttributeType', ], ], ], 'SchemaInputAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaInputAttribute', ], 'max' => 35, 'min' => 2, ], 'SchemaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SchemaMappingArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(schemamapping/[a-zA-Z_0-9-]{1,255})$', ], 'SchemaMappingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaMappingSummary', ], ], 'SchemaMappingSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'hasWorkflows', 'schemaArn', 'schemaName', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'hasWorkflows' => [ 'shape' => 'Boolean', ], 'schemaArn' => [ 'shape' => 'SchemaMappingArn', ], 'schemaName' => [ 'shape' => 'EntityName', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'Schemas' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaList', ], ], 'ServiceType' => [ 'type' => 'string', 'enum' => [ 'ASSIGNMENT', 'ID_MAPPING', ], ], 'StartIdMappingJobInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'outputSourceConfig' => [ 'shape' => 'IdMappingJobOutputSourceConfig', ], 'workflowName' => [ 'shape' => 'EntityNameOrIdMappingWorkflowArn', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'StartIdMappingJobOutput' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingJobOutputSourceConfig', ], ], ], 'StartMatchingJobInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'StartMatchingJobOutput' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], ], ], 'StatementAction' => [ 'type' => 'string', 'max' => 64, 'min' => 3, 'pattern' => '^(entityresolution:[a-zA-Z0-9]+)$', ], 'StatementActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatementAction', ], 'min' => 1, ], 'StatementCondition' => [ 'type' => 'string', 'max' => 40960, 'min' => 1, ], 'StatementEffect' => [ 'type' => 'string', 'enum' => [ 'Allow', 'Deny', ], ], 'StatementId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[0-9A-Za-z]+$', ], 'StatementPrincipal' => [ 'type' => 'string', 'max' => 64, 'min' => 12, 'pattern' => '^(\\d{12})|([a-z0-9\\.]+)$', ], 'StatementPrincipalList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatementPrincipal', ], 'min' => 1, ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'VeniceGlobalArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UniqueId' => [ 'type' => 'string', 'max' => 760, 'min' => 1, 'pattern' => '^[a-zA-Z_0-9-+=/,]*$', ], 'UniqueIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UniqueId', ], ], 'UntagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'VeniceGlobalArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateIdMappingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'idMappingTechniques', 'inputSourceConfig', 'workflowName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'idMappingTechniques' => [ 'shape' => 'IdMappingTechniques', ], 'inputSourceConfig' => [ 'shape' => 'IdMappingWorkflowInputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingWorkflowOutputSourceConfig', ], 'roleArn' => [ 'shape' => 'IdMappingRoleArn', ], 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'UpdateIdMappingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'idMappingTechniques', 'inputSourceConfig', 'workflowArn', 'workflowName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'idMappingTechniques' => [ 'shape' => 'IdMappingTechniques', ], 'inputSourceConfig' => [ 'shape' => 'IdMappingWorkflowInputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingWorkflowOutputSourceConfig', ], 'roleArn' => [ 'shape' => 'IdMappingRoleArn', ], 'workflowArn' => [ 'shape' => 'IdMappingWorkflowArn', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'UpdateIdNamespaceInput' => [ 'type' => 'structure', 'required' => [ 'idNamespaceName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'idMappingWorkflowProperties' => [ 'shape' => 'IdNamespaceIdMappingWorkflowPropertiesList', ], 'idNamespaceName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'idNamespaceName', ], 'inputSourceConfig' => [ 'shape' => 'IdNamespaceInputSourceConfig', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'UpdateIdNamespaceOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'idNamespaceArn', 'idNamespaceName', 'type', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'idMappingWorkflowProperties' => [ 'shape' => 'IdNamespaceIdMappingWorkflowPropertiesList', ], 'idNamespaceArn' => [ 'shape' => 'IdNamespaceArn', ], 'idNamespaceName' => [ 'shape' => 'EntityName', ], 'inputSourceConfig' => [ 'shape' => 'IdNamespaceInputSourceConfig', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'type' => [ 'shape' => 'IdNamespaceType', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateMatchingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'inputSourceConfig', 'outputSourceConfig', 'resolutionTechniques', 'roleArn', 'workflowName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'incrementalRunConfig' => [ 'shape' => 'IncrementalRunConfig', ], 'inputSourceConfig' => [ 'shape' => 'InputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'OutputSourceConfig', ], 'resolutionTechniques' => [ 'shape' => 'ResolutionTechniques', ], 'roleArn' => [ 'shape' => 'String', ], 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'UpdateMatchingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'inputSourceConfig', 'outputSourceConfig', 'resolutionTechniques', 'roleArn', 'workflowName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'incrementalRunConfig' => [ 'shape' => 'IncrementalRunConfig', ], 'inputSourceConfig' => [ 'shape' => 'InputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'OutputSourceConfig', ], 'resolutionTechniques' => [ 'shape' => 'ResolutionTechniques', ], 'roleArn' => [ 'shape' => 'String', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'UpdateSchemaMappingInput' => [ 'type' => 'structure', 'required' => [ 'mappedInputFields', 'schemaName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'mappedInputFields' => [ 'shape' => 'SchemaInputAttributes', ], 'schemaName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'schemaName', ], ], ], 'UpdateSchemaMappingOutput' => [ 'type' => 'structure', 'required' => [ 'mappedInputFields', 'schemaArn', 'schemaName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'mappedInputFields' => [ 'shape' => 'SchemaInputAttributes', ], 'schemaArn' => [ 'shape' => 'SchemaMappingArn', ], 'schemaName' => [ 'shape' => 'EntityName', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'VeniceGlobalArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:((schemamapping|matchingworkflow|idmappingworkflow|idnamespace)/[a-zA-Z_0-9-]{1,255})$', ], ],];
