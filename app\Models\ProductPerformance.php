<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductPerformance extends Model
{
    protected $table = 'product_performance';

    protected $fillable = [
        'product_id',
        'warehouse_id',
        'sales_velocity',
        'profit_margin',
        'stock_turnover',
        'days_to_stockout',
        'reorder_point',
        'performance_score',
        'total_sold',
        'total_revenue',
        'analysis_date',
        'created_by',
    ];

    protected $casts = [
        'sales_velocity' => 'decimal:2',
        'profit_margin' => 'decimal:2',
        'stock_turnover' => 'decimal:2',
        'performance_score' => 'decimal:2',
        'total_revenue' => 'decimal:2',
        'analysis_date' => 'date',
    ];

    /**
     * العلاقة مع المنتج
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(ProductService::class, 'product_id');
    }

    /**
     * العلاقة مع المستودع
     */
    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(\App\Models\warehouse::class);
    }

    /**
     * العلاقة مع المستخدم المنشئ
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * تصنيف الأداء بناءً على النقاط
     */
    public function getPerformanceRatingAttribute(): string
    {
        if ($this->performance_score >= 80) return 'ممتاز';
        if ($this->performance_score >= 60) return 'جيد';
        if ($this->performance_score >= 40) return 'متوسط';
        if ($this->performance_score >= 20) return 'ضعيف';
        return 'سيء جداً';
    }

    /**
     * لون تصنيف الأداء
     */
    public function getPerformanceColorAttribute(): string
    {
        if ($this->performance_score >= 80) return 'success';
        if ($this->performance_score >= 60) return 'primary';
        if ($this->performance_score >= 40) return 'warning';
        if ($this->performance_score >= 20) return 'danger';
        return 'dark';
    }

    /**
     * تحديد ما إذا كان المنتج يحتاج إعادة طلب
     */
    public function getNeedsReorderAttribute(): bool
    {
        return $this->days_to_stockout <= 7 && $this->days_to_stockout > 0;
    }

    /**
     * تحديد ما إذا كان المنتج نفد من المخزون
     */
    public function getIsOutOfStockAttribute(): bool
    {
        return $this->days_to_stockout <= 0;
    }

    /**
     * فلترة الأداء حسب المنتج
     */
    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * فلترة الأداء حسب المستودع
     */
    public function scopeForWarehouse($query, $warehouseId)
    {
        if ($warehouseId) {
            return $query->where('warehouse_id', $warehouseId);
        }
        return $query;
    }

    /**
     * فلترة الأداء حسب تاريخ التحليل
     */
    public function scopeForDate($query, $date)
    {
        return $query->where('analysis_date', $date);
    }

    /**
     * فلترة الأداء حسب المنشئ
     */
    public function scopeForCreator($query, $creatorId)
    {
        return $query->where('created_by', $creatorId);
    }

    /**
     * فلترة المنتجات عالية الأداء
     */
    public function scopeHighPerformance($query)
    {
        return $query->where('performance_score', '>=', 80);
    }

    /**
     * فلترة المنتجات منخفضة الأداء
     */
    public function scopeLowPerformance($query)
    {
        return $query->where('performance_score', '<', 40);
    }

    /**
     * فلترة المنتجات التي تحتاج إعادة طلب
     */
    public function scopeNeedsReorder($query)
    {
        return $query->where('days_to_stockout', '<=', 7)
                    ->where('days_to_stockout', '>', 0);
    }

    /**
     * فلترة المنتجات نافدة المخزون
     */
    public function scopeOutOfStock($query)
    {
        return $query->where('days_to_stockout', '<=', 0);
    }

    /**
     * ترتيب حسب الأداء (الأعلى أولاً)
     */
    public function scopeTopPerformers($query)
    {
        return $query->orderBy('performance_score', 'desc');
    }

    /**
     * ترتيب حسب الإيرادات (الأعلى أولاً)
     */
    public function scopeTopRevenue($query)
    {
        return $query->orderBy('total_revenue', 'desc');
    }

    /**
     * حساب نقاط الأداء للمنتج
     */
    public static function calculatePerformanceScore($salesVelocity, $profitMargin, $stockTurnover): float
    {
        // وزن كل عامل في النقاط النهائية
        $velocityWeight = 0.4; // 40%
        $marginWeight = 0.3;   // 30%
        $turnoverWeight = 0.3; // 30%

        // تحويل القيم إلى نقاط من 0-100
        $velocityScore = min(($salesVelocity / 10) * 100, 100); // افتراض أن 10 وحدات/يوم = 100%
        $marginScore = min($profitMargin * 2, 100); // افتراض أن 50% هامش ربح = 100%
        $turnoverScore = min(($stockTurnover / 12) * 100, 100); // افتراض أن 12 دورة/سنة = 100%

        return round(
            ($velocityScore * $velocityWeight) +
            ($marginScore * $marginWeight) +
            ($turnoverScore * $turnoverWeight),
            2
        );
    }
}
