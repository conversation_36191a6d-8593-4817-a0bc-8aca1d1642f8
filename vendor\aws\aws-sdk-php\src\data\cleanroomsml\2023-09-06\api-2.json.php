<?php
// This file was auto-generated from sdk-root/src/data/cleanroomsml/2023-09-06/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-09-06', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'cleanrooms-ml', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS Clean Rooms ML', 'serviceId' => 'CleanRoomsML', 'signatureVersion' => 'v4', 'signingName' => 'cleanrooms-ml', 'uid' => 'cleanroomsml-2023-09-06', ], 'operations' => [ 'CreateAudienceModel' => [ 'name' => 'CreateAudienceModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/audience-model', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAudienceModelRequest', ], 'output' => [ 'shape' => 'CreateAudienceModelResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateConfiguredAudienceModel' => [ 'name' => 'CreateConfiguredAudienceModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/configured-audience-model', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateConfiguredAudienceModelRequest', ], 'output' => [ 'shape' => 'CreateConfiguredAudienceModelResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateTrainingDataset' => [ 'name' => 'CreateTrainingDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/training-dataset', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateTrainingDatasetRequest', ], 'output' => [ 'shape' => 'CreateTrainingDatasetResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteAudienceGenerationJob' => [ 'name' => 'DeleteAudienceGenerationJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/audience-generation-job/{audienceGenerationJobArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAudienceGenerationJobRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteAudienceModel' => [ 'name' => 'DeleteAudienceModel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/audience-model/{audienceModelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAudienceModelRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteConfiguredAudienceModel' => [ 'name' => 'DeleteConfiguredAudienceModel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/configured-audience-model/{configuredAudienceModelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteConfiguredAudienceModelRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteConfiguredAudienceModelPolicy' => [ 'name' => 'DeleteConfiguredAudienceModelPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/configured-audience-model/{configuredAudienceModelArn}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteConfiguredAudienceModelPolicyRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteTrainingDataset' => [ 'name' => 'DeleteTrainingDataset', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/training-dataset/{trainingDatasetArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteTrainingDatasetRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'GetAudienceGenerationJob' => [ 'name' => 'GetAudienceGenerationJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/audience-generation-job/{audienceGenerationJobArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAudienceGenerationJobRequest', ], 'output' => [ 'shape' => 'GetAudienceGenerationJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAudienceModel' => [ 'name' => 'GetAudienceModel', 'http' => [ 'method' => 'GET', 'requestUri' => '/audience-model/{audienceModelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAudienceModelRequest', ], 'output' => [ 'shape' => 'GetAudienceModelResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetConfiguredAudienceModel' => [ 'name' => 'GetConfiguredAudienceModel', 'http' => [ 'method' => 'GET', 'requestUri' => '/configured-audience-model/{configuredAudienceModelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConfiguredAudienceModelRequest', ], 'output' => [ 'shape' => 'GetConfiguredAudienceModelResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetConfiguredAudienceModelPolicy' => [ 'name' => 'GetConfiguredAudienceModelPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/configured-audience-model/{configuredAudienceModelArn}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConfiguredAudienceModelPolicyRequest', ], 'output' => [ 'shape' => 'GetConfiguredAudienceModelPolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetTrainingDataset' => [ 'name' => 'GetTrainingDataset', 'http' => [ 'method' => 'GET', 'requestUri' => '/training-dataset/{trainingDatasetArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTrainingDatasetRequest', ], 'output' => [ 'shape' => 'GetTrainingDatasetResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAudienceExportJobs' => [ 'name' => 'ListAudienceExportJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/audience-export-job', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAudienceExportJobsRequest', ], 'output' => [ 'shape' => 'ListAudienceExportJobsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAudienceGenerationJobs' => [ 'name' => 'ListAudienceGenerationJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/audience-generation-job', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAudienceGenerationJobsRequest', ], 'output' => [ 'shape' => 'ListAudienceGenerationJobsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAudienceModels' => [ 'name' => 'ListAudienceModels', 'http' => [ 'method' => 'GET', 'requestUri' => '/audience-model', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAudienceModelsRequest', ], 'output' => [ 'shape' => 'ListAudienceModelsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListConfiguredAudienceModels' => [ 'name' => 'ListConfiguredAudienceModels', 'http' => [ 'method' => 'GET', 'requestUri' => '/configured-audience-model', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConfiguredAudienceModelsRequest', ], 'output' => [ 'shape' => 'ListConfiguredAudienceModelsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTrainingDatasets' => [ 'name' => 'ListTrainingDatasets', 'http' => [ 'method' => 'GET', 'requestUri' => '/training-dataset', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTrainingDatasetsRequest', ], 'output' => [ 'shape' => 'ListTrainingDatasetsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PutConfiguredAudienceModelPolicy' => [ 'name' => 'PutConfiguredAudienceModelPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/configured-audience-model/{configuredAudienceModelArn}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutConfiguredAudienceModelPolicyRequest', ], 'output' => [ 'shape' => 'PutConfiguredAudienceModelPolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'StartAudienceExportJob' => [ 'name' => 'StartAudienceExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/audience-export-job', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartAudienceExportJobRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'StartAudienceGenerationJob' => [ 'name' => 'StartAudienceGenerationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/audience-generation-job', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartAudienceGenerationJobRequest', ], 'output' => [ 'shape' => 'StartAudienceGenerationJobResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateConfiguredAudienceModel' => [ 'name' => 'UpdateConfiguredAudienceModel', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/configured-audience-model/{configuredAudienceModelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateConfiguredAudienceModelRequest', ], 'output' => [ 'shape' => 'UpdateConfiguredAudienceModelResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '[0-9]{12}', ], 'AnalysisTemplateArn' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'pattern' => 'arn:aws[-a-z]*:cleanrooms:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:membership/[\\d\\w-]+/analysistemplate/[\\d\\w-]+', ], 'AudienceDestination' => [ 'type' => 'structure', 'required' => [ 's3Destination', ], 'members' => [ 's3Destination' => [ 'shape' => 'S3ConfigMap', ], ], ], 'AudienceExportJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudienceExportJobSummary', ], ], 'AudienceExportJobStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_PENDING', 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', ], ], 'AudienceExportJobSummary' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'name', 'audienceGenerationJobArn', 'audienceSize', 'status', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'name' => [ 'shape' => 'NameString', ], 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', ], 'audienceSize' => [ 'shape' => 'AudienceSize', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'status' => [ 'shape' => 'AudienceExportJobStatus', ], 'statusDetails' => [ 'shape' => 'StatusDetails', ], 'outputLocation' => [ 'shape' => 'S3Path', ], ], ], 'AudienceGenerationJobArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:audience-generation-job/[-a-zA-Z0-9_/.]+', ], 'AudienceGenerationJobDataSource' => [ 'type' => 'structure', 'required' => [ 'roleArn', ], 'members' => [ 'dataSource' => [ 'shape' => 'S3ConfigMap', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'sqlParameters' => [ 'shape' => 'ProtectedQuerySQLParameters', ], ], ], 'AudienceGenerationJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudienceGenerationJobSummary', ], ], 'AudienceGenerationJobStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_PENDING', 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', 'DELETE_PENDING', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', ], ], 'AudienceGenerationJobSummary' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'audienceGenerationJobArn', 'name', 'status', 'configuredAudienceModelArn', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', ], 'name' => [ 'shape' => 'NameString', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'status' => [ 'shape' => 'AudienceGenerationJobStatus', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'startedBy' => [ 'shape' => 'AccountId', ], ], ], 'AudienceModelArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:audience-model/[-a-zA-Z0-9_/.]+', ], 'AudienceModelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudienceModelSummary', ], ], 'AudienceModelStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_PENDING', 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', 'DELETE_PENDING', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', ], ], 'AudienceModelSummary' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'audienceModelArn', 'name', 'trainingDatasetArn', 'status', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], 'name' => [ 'shape' => 'NameString', ], 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', ], 'status' => [ 'shape' => 'AudienceModelStatus', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'AudienceQualityMetrics' => [ 'type' => 'structure', 'required' => [ 'relevanceMetrics', ], 'members' => [ 'relevanceMetrics' => [ 'shape' => 'RelevanceMetrics', ], 'recallMetric' => [ 'shape' => 'Double', ], ], ], 'AudienceSize' => [ 'type' => 'structure', 'required' => [ 'type', 'value', ], 'members' => [ 'type' => [ 'shape' => 'AudienceSizeType', ], 'value' => [ 'shape' => 'AudienceSizeValue', ], ], ], 'AudienceSizeBins' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudienceSizeValue', ], 'max' => 25, 'min' => 1, ], 'AudienceSizeConfig' => [ 'type' => 'structure', 'required' => [ 'audienceSizeType', 'audienceSizeBins', ], 'members' => [ 'audienceSizeType' => [ 'shape' => 'AudienceSizeType', ], 'audienceSizeBins' => [ 'shape' => 'AudienceSizeBins', ], ], ], 'AudienceSizeType' => [ 'type' => 'string', 'enum' => [ 'ABSOLUTE', 'PERCENTAGE', ], ], 'AudienceSizeValue' => [ 'type' => 'integer', 'box' => true, 'max' => 20000000, 'min' => 1, ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ColumnName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_](([a-zA-Z0-9_ ]+-)*([a-zA-Z0-9_ ]+))?', ], 'ColumnSchema' => [ 'type' => 'structure', 'required' => [ 'columnName', 'columnTypes', ], 'members' => [ 'columnName' => [ 'shape' => 'ColumnName', ], 'columnTypes' => [ 'shape' => 'ColumnTypeList', ], ], ], 'ColumnType' => [ 'type' => 'string', 'enum' => [ 'USER_ID', 'ITEM_ID', 'TIMESTAMP', 'CATEGORICAL_FEATURE', 'NUMERICAL_FEATURE', ], ], 'ColumnTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnType', ], 'max' => 1, 'min' => 1, ], 'ConfiguredAudienceModelArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:configured-audience-model/[-a-zA-Z0-9_/.]+', ], 'ConfiguredAudienceModelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfiguredAudienceModelSummary', ], ], 'ConfiguredAudienceModelOutputConfig' => [ 'type' => 'structure', 'required' => [ 'destination', 'roleArn', ], 'members' => [ 'destination' => [ 'shape' => 'AudienceDestination', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'ConfiguredAudienceModelStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', ], ], 'ConfiguredAudienceModelSummary' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'name', 'audienceModelArn', 'outputConfig', 'configuredAudienceModelArn', 'status', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'name' => [ 'shape' => 'NameString', ], 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], 'outputConfig' => [ 'shape' => 'ConfiguredAudienceModelOutputConfig', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'status' => [ 'shape' => 'ConfiguredAudienceModelStatus', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'trainingDatasetArn', ], 'members' => [ 'trainingDataStartTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'trainingDataEndTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'name' => [ 'shape' => 'NameString', ], 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'tags' => [ 'shape' => 'TagMap', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'CreateAudienceModelResponse' => [ 'type' => 'structure', 'required' => [ 'audienceModelArn', ], 'members' => [ 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], ], ], 'CreateConfiguredAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'audienceModelArn', 'outputConfig', 'sharedAudienceMetrics', ], 'members' => [ 'name' => [ 'shape' => 'NameString', ], 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], 'outputConfig' => [ 'shape' => 'ConfiguredAudienceModelOutputConfig', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'sharedAudienceMetrics' => [ 'shape' => 'MetricsList', ], 'minMatchingSeedSize' => [ 'shape' => 'MinMatchingSeedSize', ], 'audienceSizeConfig' => [ 'shape' => 'AudienceSizeConfig', ], 'tags' => [ 'shape' => 'TagMap', ], 'childResourceTagOnCreatePolicy' => [ 'shape' => 'TagOnCreatePolicy', ], ], ], 'CreateConfiguredAudienceModelResponse' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], ], ], 'CreateTrainingDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'roleArn', 'trainingData', ], 'members' => [ 'name' => [ 'shape' => 'NameString', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'trainingData' => [ 'shape' => 'CreateTrainingDatasetRequestTrainingDataList', ], 'tags' => [ 'shape' => 'TagMap', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'CreateTrainingDatasetRequestTrainingDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Dataset', ], 'max' => 1, 'min' => 1, ], 'CreateTrainingDatasetResponse' => [ 'type' => 'structure', 'required' => [ 'trainingDatasetArn', ], 'members' => [ 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', ], ], ], 'DataSource' => [ 'type' => 'structure', 'required' => [ 'glueDataSource', ], 'members' => [ 'glueDataSource' => [ 'shape' => 'GlueDataSource', ], ], ], 'Dataset' => [ 'type' => 'structure', 'required' => [ 'type', 'inputConfig', ], 'members' => [ 'type' => [ 'shape' => 'DatasetType', ], 'inputConfig' => [ 'shape' => 'DatasetInputConfig', ], ], ], 'DatasetInputConfig' => [ 'type' => 'structure', 'required' => [ 'schema', 'dataSource', ], 'members' => [ 'schema' => [ 'shape' => 'DatasetInputConfigSchemaList', ], 'dataSource' => [ 'shape' => 'DataSource', ], ], ], 'DatasetInputConfigSchemaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnSchema', ], 'max' => 100, 'min' => 1, ], 'DatasetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Dataset', ], ], 'DatasetType' => [ 'type' => 'string', 'enum' => [ 'INTERACTIONS', ], ], 'DeleteAudienceGenerationJobRequest' => [ 'type' => 'structure', 'required' => [ 'audienceGenerationJobArn', ], 'members' => [ 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', 'location' => 'uri', 'locationName' => 'audienceGenerationJobArn', ], ], ], 'DeleteAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'audienceModelArn', ], 'members' => [ 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', 'location' => 'uri', 'locationName' => 'audienceModelArn', ], ], ], 'DeleteConfiguredAudienceModelPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'uri', 'locationName' => 'configuredAudienceModelArn', ], ], ], 'DeleteConfiguredAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'uri', 'locationName' => 'configuredAudienceModelArn', ], ], ], 'DeleteTrainingDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'trainingDatasetArn', ], 'members' => [ 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', 'location' => 'uri', 'locationName' => 'trainingDatasetArn', ], ], ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'GetAudienceGenerationJobRequest' => [ 'type' => 'structure', 'required' => [ 'audienceGenerationJobArn', ], 'members' => [ 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', 'location' => 'uri', 'locationName' => 'audienceGenerationJobArn', ], ], ], 'GetAudienceGenerationJobResponse' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'audienceGenerationJobArn', 'name', 'status', 'configuredAudienceModelArn', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', ], 'name' => [ 'shape' => 'NameString', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'status' => [ 'shape' => 'AudienceGenerationJobStatus', ], 'statusDetails' => [ 'shape' => 'StatusDetails', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'seedAudience' => [ 'shape' => 'AudienceGenerationJobDataSource', ], 'includeSeedInOutput' => [ 'shape' => 'Boolean', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'metrics' => [ 'shape' => 'AudienceQualityMetrics', ], 'startedBy' => [ 'shape' => 'AccountId', ], 'tags' => [ 'shape' => 'TagMap', ], 'protectedQueryIdentifier' => [ 'shape' => 'String', ], ], ], 'GetAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'audienceModelArn', ], 'members' => [ 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', 'location' => 'uri', 'locationName' => 'audienceModelArn', ], ], ], 'GetAudienceModelResponse' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'audienceModelArn', 'name', 'trainingDatasetArn', 'status', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'trainingDataStartTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'trainingDataEndTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], 'name' => [ 'shape' => 'NameString', ], 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', ], 'status' => [ 'shape' => 'AudienceModelStatus', ], 'statusDetails' => [ 'shape' => 'StatusDetails', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'tags' => [ 'shape' => 'TagMap', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'GetConfiguredAudienceModelPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'uri', 'locationName' => 'configuredAudienceModelArn', ], ], ], 'GetConfiguredAudienceModelPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', 'configuredAudienceModelPolicy', 'policyHash', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'configuredAudienceModelPolicy' => [ 'shape' => 'ResourcePolicy', ], 'policyHash' => [ 'shape' => 'Hash', ], ], ], 'GetConfiguredAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'uri', 'locationName' => 'configuredAudienceModelArn', ], ], ], 'GetConfiguredAudienceModelResponse' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'configuredAudienceModelArn', 'name', 'audienceModelArn', 'outputConfig', 'status', 'sharedAudienceMetrics', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'name' => [ 'shape' => 'NameString', ], 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], 'outputConfig' => [ 'shape' => 'ConfiguredAudienceModelOutputConfig', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'status' => [ 'shape' => 'ConfiguredAudienceModelStatus', ], 'sharedAudienceMetrics' => [ 'shape' => 'MetricsList', ], 'minMatchingSeedSize' => [ 'shape' => 'MinMatchingSeedSize', ], 'audienceSizeConfig' => [ 'shape' => 'AudienceSizeConfig', ], 'tags' => [ 'shape' => 'TagMap', ], 'childResourceTagOnCreatePolicy' => [ 'shape' => 'TagOnCreatePolicy', ], ], ], 'GetTrainingDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'trainingDatasetArn', ], 'members' => [ 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', 'location' => 'uri', 'locationName' => 'trainingDatasetArn', ], ], ], 'GetTrainingDatasetResponse' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'trainingDatasetArn', 'name', 'trainingData', 'status', 'roleArn', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', ], 'name' => [ 'shape' => 'NameString', ], 'trainingData' => [ 'shape' => 'DatasetList', ], 'status' => [ 'shape' => 'TrainingDatasetStatus', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'GlueDataSource' => [ 'type' => 'structure', 'required' => [ 'tableName', 'databaseName', ], 'members' => [ 'tableName' => [ 'shape' => 'GlueTableName', ], 'databaseName' => [ 'shape' => 'GlueDatabaseName', ], 'catalogId' => [ 'shape' => 'AccountId', ], ], ], 'GlueDatabaseName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_](([a-zA-Z0-9_]+-)*([a-zA-Z0-9_]+))?', ], 'GlueTableName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_](([a-zA-Z0-9_ ]+-)*([a-zA-Z0-9_ ]+))?', ], 'Hash' => [ 'type' => 'string', 'max' => 128, 'min' => 64, 'pattern' => '[0-9a-f]+', ], 'IamRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:iam::[0-9]{12}:role/.+', ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:kms:[-a-z0-9]+:[0-9]{12}:key/.+', ], 'ListAudienceExportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', 'location' => 'querystring', 'locationName' => 'audienceGenerationJobArn', ], ], ], 'ListAudienceExportJobsResponse' => [ 'type' => 'structure', 'required' => [ 'audienceExportJobs', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'audienceExportJobs' => [ 'shape' => 'AudienceExportJobList', ], ], ], 'ListAudienceGenerationJobsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'querystring', 'locationName' => 'configuredAudienceModelArn', ], 'collaborationId' => [ 'shape' => 'UUID', 'location' => 'querystring', 'locationName' => 'collaborationId', ], ], ], 'ListAudienceGenerationJobsResponse' => [ 'type' => 'structure', 'required' => [ 'audienceGenerationJobs', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'audienceGenerationJobs' => [ 'shape' => 'AudienceGenerationJobList', ], ], ], 'ListAudienceModelsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAudienceModelsResponse' => [ 'type' => 'structure', 'required' => [ 'audienceModels', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'audienceModels' => [ 'shape' => 'AudienceModelList', ], ], ], 'ListConfiguredAudienceModelsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListConfiguredAudienceModelsResponse' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModels', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'configuredAudienceModels' => [ 'shape' => 'ConfiguredAudienceModelList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TaggableArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListTrainingDatasetsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListTrainingDatasetsResponse' => [ 'type' => 'structure', 'required' => [ 'trainingDatasets', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'trainingDatasets' => [ 'shape' => 'TrainingDatasetList', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MetricsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SharedAudienceMetrics', ], 'max' => 1, 'min' => 1, ], 'MinMatchingSeedSize' => [ 'type' => 'integer', 'box' => true, 'max' => 500000, 'min' => 25, ], 'NameString' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '(?!\\s*$)[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t]*', ], 'NextToken' => [ 'type' => 'string', 'max' => 10240, 'min' => 1, ], 'ParameterKey' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[0-9a-zA-Z_]+', ], 'ParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ParameterKey', ], 'value' => [ 'shape' => 'ParameterValue', ], ], 'ParameterValue' => [ 'type' => 'string', 'max' => 250, 'min' => 0, ], 'PolicyExistenceCondition' => [ 'type' => 'string', 'enum' => [ 'POLICY_MUST_EXIST', 'POLICY_MUST_NOT_EXIST', ], ], 'ProtectedQuerySQLParameters' => [ 'type' => 'structure', 'members' => [ 'queryString' => [ 'shape' => 'ProtectedQuerySQLParametersQueryStringString', ], 'analysisTemplateArn' => [ 'shape' => 'AnalysisTemplateArn', ], 'parameters' => [ 'shape' => 'ParameterMap', ], ], 'sensitive' => true, ], 'ProtectedQuerySQLParametersQueryStringString' => [ 'type' => 'string', 'max' => 90000, 'min' => 0, ], 'PutConfiguredAudienceModelPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', 'configuredAudienceModelPolicy', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'uri', 'locationName' => 'configuredAudienceModelArn', ], 'configuredAudienceModelPolicy' => [ 'shape' => 'ResourcePolicy', ], 'previousPolicyHash' => [ 'shape' => 'Hash', ], 'policyExistenceCondition' => [ 'shape' => 'PolicyExistenceCondition', ], ], ], 'PutConfiguredAudienceModelPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelPolicy', 'policyHash', ], 'members' => [ 'configuredAudienceModelPolicy' => [ 'shape' => 'ResourcePolicy', ], 'policyHash' => [ 'shape' => 'Hash', ], ], ], 'RelevanceMetric' => [ 'type' => 'structure', 'required' => [ 'audienceSize', ], 'members' => [ 'audienceSize' => [ 'shape' => 'AudienceSize', ], 'score' => [ 'shape' => 'Double', ], ], ], 'RelevanceMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelevanceMetric', ], ], 'ResourceDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t\\r\\n]*', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourcePolicy' => [ 'type' => 'string', 'max' => 20480, 'min' => 1, ], 'S3ConfigMap' => [ 'type' => 'structure', 'required' => [ 's3Uri', ], 'members' => [ 's3Uri' => [ 'shape' => 'S3Path', ], ], ], 'S3Path' => [ 'type' => 'string', 'max' => 1285, 'min' => 1, 'pattern' => 's3://.+', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SharedAudienceMetrics' => [ 'type' => 'string', 'enum' => [ 'ALL', 'NONE', ], ], 'StartAudienceExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'audienceGenerationJobArn', 'audienceSize', ], 'members' => [ 'name' => [ 'shape' => 'NameString', ], 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', ], 'audienceSize' => [ 'shape' => 'AudienceSize', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'StartAudienceGenerationJobRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'configuredAudienceModelArn', 'seedAudience', ], 'members' => [ 'name' => [ 'shape' => 'NameString', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'seedAudience' => [ 'shape' => 'AudienceGenerationJobDataSource', ], 'includeSeedInOutput' => [ 'shape' => 'Boolean', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'StartAudienceGenerationJobResponse' => [ 'type' => 'structure', 'required' => [ 'audienceGenerationJobArn', ], 'members' => [ 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', ], ], ], 'StatusDetails' => [ 'type' => 'structure', 'members' => [ 'statusCode' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'String' => [ 'type' => 'string', ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagOnCreatePolicy' => [ 'type' => 'string', 'enum' => [ 'FROM_PARENT_RESOURCE', 'NONE', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TaggableArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TaggableArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:(training-dataset|audience-model|configured-audience-model|audience-generation-job)/[-a-zA-Z0-9_/.]+', ], 'TrainingDatasetArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:training-dataset/[-a-zA-Z0-9_/.]+', ], 'TrainingDatasetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrainingDatasetSummary', ], ], 'TrainingDatasetStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', ], ], 'TrainingDatasetSummary' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'trainingDatasetArn', 'name', 'status', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', ], 'name' => [ 'shape' => 'NameString', ], 'status' => [ 'shape' => 'TrainingDatasetStatus', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'UUID' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TaggableArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateConfiguredAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'uri', 'locationName' => 'configuredAudienceModelArn', ], 'outputConfig' => [ 'shape' => 'ConfiguredAudienceModelOutputConfig', ], 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], 'sharedAudienceMetrics' => [ 'shape' => 'MetricsList', ], 'minMatchingSeedSize' => [ 'shape' => 'MinMatchingSeedSize', ], 'audienceSizeConfig' => [ 'shape' => 'AudienceSizeConfig', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'UpdateConfiguredAudienceModelResponse' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], ],];
