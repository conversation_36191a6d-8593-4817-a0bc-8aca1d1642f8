{"__meta": {"id": "X96e45659163368d8371a5d9119642784", "datetime": "2025-06-30 23:06:56", "utime": **********.864746, "method": "POST", "uri": "/pos-payment-type", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.455228, "end": **********.864767, "duration": 0.40953898429870605, "duration_str": "410ms", "measures": [{"label": "Booting", "start": **********.455228, "relative_start": 0, "end": **********.804775, "relative_end": **********.804775, "duration": 0.3495469093322754, "duration_str": "350ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.804784, "relative_start": 0.34955596923828125, "end": **********.864769, "relative_end": 1.9073486328125e-06, "duration": 0.05998492240905762, "duration_str": "59.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45419832, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@financialType", "namespace": null, "prefix": "", "where": [], "as": "pos.pos-payment-type", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=190\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:190-261</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00214, "accumulated_duration_str": "2.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.83708, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 77.57}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.848086, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 77.57, "width_percent": 22.43}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq22\\app\\Services\\FinancialRecordService.php", "line": 585}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 200}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.852148, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:585", "source": "app/Services/FinancialRecordService.php:585", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FServices%2FFinancialRecordService.php&line=585", "ajax": false, "filename": "FinancialRecordService.php", "line": "585"}, "connection": "kdmkjkqknb", "start_percent": 100, "width_percent": 0}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq22\\app\\Services\\FinancialRecordService.php", "line": 604}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 200}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.854043, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:604", "source": "app/Services/FinancialRecordService.php:604", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FServices%2FFinancialRecordService.php&line=604", "ajax": false, "filename": "FinancialRecordService.php", "line": "604"}, "connection": "kdmkjkqknb", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2353 => array:9 [\n    \"name\" => \"مفتاح علبه\"\n    \"quantity\" => 1\n    \"price\" => \"6.00\"\n    \"id\" => \"2353\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Undefined variable $result", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1196103013 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1196103013\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1566014612 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>payment_type</span>\" => \"<span class=sf-dump-str title=\"8 characters\">deferred</span>\"\n  \"<span class=sf-dump-key>total_price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.00</span>\"\n  \"<span class=sf-dump-key>deferred_customer_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">hmd</span>\"\n  \"<span class=sf-dump-key>deferred_due_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-01</span>\"\n  \"<span class=sf-dump-key>deferred_notes</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1566014612\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1475719191 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">153</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323255132%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IllOZXVTQzAxSk1GQTMrWDc1VGQ0V0E9PSIsInZhbHVlIjoiTUZPK3ZjNW1STEZ2U1RZTitobytFdHpXSjUvUE1Hd0JiMW5JR2RScmt1Ulp2WjVYZE9zbFJpOXRVR01xQXlxT1ZVQkgyY2VnNFZvbVdsLzJpZEpBK3ZCeXZTM0hCYXhMUk1oK0lYSzNFeXgwaUhVWEllcUFRSHlEUnE1aEkzUkJDSXFBUnl4SGlFODhsb1hZbXNWZStyRmpKTC9hZE5OQzRpYlUxK1pTRDV4ZnNHbEFQSzVJSWExZFIzM21qVm1FUVRJaCt6K0FDd3dxaDlIQnB0OElsQUNYZkV1QWpCdUZFckxDMDU4eDFhTVRUMVFvTW1KeUZCazBmNVFJZFAzWVVkQ1RNUld2VitOamg1K3M3RHpZcHNwanhXMTJod3FjdU5FMDVLNDIyMnhUc2JQR1ZvbU1IbkRnbURhMlNQV1RpVzJRZTF3V3dJZ2RrcWoxbms4UXR3dTV6YTJYcVlERC9hbzU1TElrYjlXOGZ3RUJLSENwd2lza2xOWU5kUDdQcmVneS9sU3ZabTNCbW9kaXZFT2FmUWt3MS8rTXhhZWpXUkVpYmY5eURKN1g3VlYrQ2l0TjBvK25MR0dGWGFqWTFzYjNUWUVtbUlQK1FTVnliWW9FNVdBL3dxVm9FNHpsa1lvbWxMNVNiRXdUZWRiWVpGOWRuV2JxbkttRlRqdnkiLCJtYWMiOiI3Mzg4ZDVlMDBiOTcwNjE2ZDI1MjBkZGQ0ODFjNjU3NGE0OWMyOGU0YTU1MzJiMDZlNzc3ODg0Y2JiZWIwOTk0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImVjcUpJT3EzR1JqRG1nNXdaS3d1N3c9PSIsInZhbHVlIjoibTA2ejdSalBCclBkcGVJU2pPVWVjajdwdkx5NDhvSzVTUVYyclBSdWlzdHQ0anpFRFZnR2dSQzVvNVdNUGpER3lEQlV2WnVpQzlIK3dGMTUyVG9IT2JMWlBJTmdPU3ZCV0V0ZHhOY0NhbDlOL3I5OTVXYVQ3QXVzZHFWUGl6TGNMbFJhaStiM0hLOVJ5Zk81VWo1NXJzb0JFV29Ia0xVam8yTE03Z1hyaVA0SVJIZUdJRlcxZzBZc01YQWIvdGQ0VVNhK3cwK2phbXJ4REQrZGJGaS9iYW9jVHhZNjVIQ3VFUmNYcnM4K2tUeDFGWFE4N0RXWWlGbHVVNTRZeEZ4SVRIdjNXVjZUdDF2R0tJWVFVTW9hNnJOQTBRT2pqTG9MdjZBNW5CTHRGU0tkbmRaSXMyNjNtbnExb3BtUU5KV25tMXFKYy9aWnlwQ2ozb0lNR0NjN01tUXlWMlRjU0lXeXM0K1hnSEVzL2laSXg5THJnNHorTjhXWitadytGNVd6cDkzSkcvN0plR1BZWk40N2FjdWE3MGd5azg3aHJUdDFyU0tsRUxwVlZMTFI5Yk8ycGdMdUFRNzkyMDViWXhMbkhZNXkvRDB4eGhhYWVxQjY5U2pFUS94Z1VGRVNNMCtuTFJrWnNEUEo1TWtoZnFOUG11M3p3SEpJREJkdUE1VFYiLCJtYWMiOiI1NjU2MmFkZmEwY2FiMDE4M2EzYjYxOTc0ZWZmMjM2NjFjNGIzNDU1MTY5Y2U3MTM0OWMxZGRkNzU2NDk2OTBjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1475719191\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-16323862 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-16323862\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:06:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFtYzlGWlZqa2xTQjZBcGpVRVAvcWc9PSIsInZhbHVlIjoiQ1dHWDIrbHRvMkNrWHNtaS9FQ3dqSklhNDFlS0JFK1RHWmZvaFpadHN4V3NtN00wbTJ2Y2pNM2tUZ3hKOFdSM3oyY2x6OUtGMFVIS1h2Um1yVkVBMnRKS3BiNXJQYmZRZmNNK242MnFXU3ptOWZMNityN2JGS01HWlJTYXhiTDZ4bFJXM0J2L3pVaHpLRitFeTZFS0g1ek1MMlhmMnlEOEE5WFZxaHlpVEpIcmQwTVRvNVo4Y3RNWW1NY05XaHlTM2JjSU9aT0xCTHlIVzlTQ2svcGhCMkEwb1gxNmFBSWFodHNKZW16bEdYZDlsc2dRWlpFblJKYXVSblJNRkwrdkNvUk51YzFvYkpRQ09KQ0o2SnpiaFkrdWxKMHlTQ3I1TDZVNStzTDFRY0lQUVMzQ2Z1dHduWlVPT1JodGV6ZHdaQS9IQk5xWjI3MTJVbEJwQWVCd01TS1ZKaGhFOXpNeGNYcmlhU0lBeFdJNHBIaG5Ldk5heEw1L0gwNllYM3JYN0RjNnFMSWFXTlZFWTdCeUkxbk1OMlJWQ205bjcrbDZwSjZCd2kwODhLS1JwcVNDUHRQeXhETVhlcitsSHBLQ05PZ2lULzZkS082eFFNVGc5Wlc2U0JzUmFxUkRBVzhKQng3RDJGWFlPM3hmeDVwMFRsaDNWU21FYkVTTzIvYmMiLCJtYWMiOiI3YWYxYmIwYWFmNDBjMzI0YzU4NjFmN2JlZmJmMDliNDFkMGVmOWQ4NjE4OTA2NmJiNzQxOTJjMzRmYzA4OWIxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:06:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjRoeWdycVRmc1g5VjV0bG8wS05ZUlE9PSIsInZhbHVlIjoiSEtSVkNxR0VDUkFJSzV1NXNWTkl3b2xBSEFYWFlqZHVzZHVURXNuV280Tm5uZ2N0ZHoyZC9iOWxCRmpjMTJ6ME84U29xVVE0aEV4QVRzVmIrMkxiakQvTHhoUDRqekR2cUhLR3FjcldMZUFxVWF4ZWdxY3JMNlhkalBJNFZHRnB5ekVwWEtNRS9mVWdKVHBYQmNPUzRCbHFrWVlEUHZqeFBYUFVSSVhVb01rWlVNN1QwVDZMd05sOUJjNWZnSFhzQXR3Y1V3Nlp1RERSOVJiYWJJQlUrdmt4TnY4RzE2TDZoWjh0aDhjZFdCaHNPQVlyZDhSYnovRXZ2cExQbGVTRFVFWVBna0k2NzRSQXE4Q1ZneDdndXI1SGpRbmM0eHNFdEx0cFhQMm91NnYzQXZBV2U0Yko1dy9CN1ozVmxaYWJrclFWZzhkTnhjenZqaGJFb2ZuVlN3OXlqVTF3ZU1LRlZkQ3VZWDVPM1lOVjlOdUFNOGZPZHpZWGlYVkxiazFRK01BOGczVmVqRURjWFpJZVMzdlp6RmFTdEhaTVJheHg3MmlnakFuSG82QjNkbFZMeTY0ZVpDd0R2Q1BQUG9lVWk0Y3ZxQktLWHZqYUNWSHlJMm9OWUNkOFRGQ3NhSzB2VWd6c1JXdzlveU1kVjlmb0hXYndVRTZmTkxWMmU5STUiLCJtYWMiOiIxODZmNDU1MzAzNmVmMmM4NzJiNjY5ZDc5ZDAyN2M1NjY0YTM3YjQ2YjM2YmY1MzJiM2JmZjVlYTAyYzVlOWEzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:06:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFtYzlGWlZqa2xTQjZBcGpVRVAvcWc9PSIsInZhbHVlIjoiQ1dHWDIrbHRvMkNrWHNtaS9FQ3dqSklhNDFlS0JFK1RHWmZvaFpadHN4V3NtN00wbTJ2Y2pNM2tUZ3hKOFdSM3oyY2x6OUtGMFVIS1h2Um1yVkVBMnRKS3BiNXJQYmZRZmNNK242MnFXU3ptOWZMNityN2JGS01HWlJTYXhiTDZ4bFJXM0J2L3pVaHpLRitFeTZFS0g1ek1MMlhmMnlEOEE5WFZxaHlpVEpIcmQwTVRvNVo4Y3RNWW1NY05XaHlTM2JjSU9aT0xCTHlIVzlTQ2svcGhCMkEwb1gxNmFBSWFodHNKZW16bEdYZDlsc2dRWlpFblJKYXVSblJNRkwrdkNvUk51YzFvYkpRQ09KQ0o2SnpiaFkrdWxKMHlTQ3I1TDZVNStzTDFRY0lQUVMzQ2Z1dHduWlVPT1JodGV6ZHdaQS9IQk5xWjI3MTJVbEJwQWVCd01TS1ZKaGhFOXpNeGNYcmlhU0lBeFdJNHBIaG5Ldk5heEw1L0gwNllYM3JYN0RjNnFMSWFXTlZFWTdCeUkxbk1OMlJWQ205bjcrbDZwSjZCd2kwODhLS1JwcVNDUHRQeXhETVhlcitsSHBLQ05PZ2lULzZkS082eFFNVGc5Wlc2U0JzUmFxUkRBVzhKQng3RDJGWFlPM3hmeDVwMFRsaDNWU21FYkVTTzIvYmMiLCJtYWMiOiI3YWYxYmIwYWFmNDBjMzI0YzU4NjFmN2JlZmJmMDliNDFkMGVmOWQ4NjE4OTA2NmJiNzQxOTJjMzRmYzA4OWIxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:06:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjRoeWdycVRmc1g5VjV0bG8wS05ZUlE9PSIsInZhbHVlIjoiSEtSVkNxR0VDUkFJSzV1NXNWTkl3b2xBSEFYWFlqZHVzZHVURXNuV280Tm5uZ2N0ZHoyZC9iOWxCRmpjMTJ6ME84U29xVVE0aEV4QVRzVmIrMkxiakQvTHhoUDRqekR2cUhLR3FjcldMZUFxVWF4ZWdxY3JMNlhkalBJNFZHRnB5ekVwWEtNRS9mVWdKVHBYQmNPUzRCbHFrWVlEUHZqeFBYUFVSSVhVb01rWlVNN1QwVDZMd05sOUJjNWZnSFhzQXR3Y1V3Nlp1RERSOVJiYWJJQlUrdmt4TnY4RzE2TDZoWjh0aDhjZFdCaHNPQVlyZDhSYnovRXZ2cExQbGVTRFVFWVBna0k2NzRSQXE4Q1ZneDdndXI1SGpRbmM0eHNFdEx0cFhQMm91NnYzQXZBV2U0Yko1dy9CN1ozVmxaYWJrclFWZzhkTnhjenZqaGJFb2ZuVlN3OXlqVTF3ZU1LRlZkQ3VZWDVPM1lOVjlOdUFNOGZPZHpZWGlYVkxiazFRK01BOGczVmVqRURjWFpJZVMzdlp6RmFTdEhaTVJheHg3MmlnakFuSG82QjNkbFZMeTY0ZVpDd0R2Q1BQUG9lVWk0Y3ZxQktLWHZqYUNWSHlJMm9OWUNkOFRGQ3NhSzB2VWd6c1JXdzlveU1kVjlmb0hXYndVRTZmTkxWMmU5STUiLCJtYWMiOiIxODZmNDU1MzAzNmVmMmM4NzJiNjY5ZDc5ZDAyN2M1NjY0YTM3YjQ2YjM2YmY1MzJiM2JmZjVlYTAyYzVlOWEzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:06:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2353</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1605;&#1601;&#1578;&#1575;&#1581; &#1593;&#1604;&#1576;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2353</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Undefined variable $result</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}