<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductExpiryDate extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'warehouse_id',
        'expiry_date',
        'created_by',
    ];

    /**
     * Obtener el producto asociado a esta fecha de caducidad.
     */
    public function product()
    {
        return $this->belongsTo(ProductService::class, 'product_id');
    }

    /**
     * Obtener el almacén asociado a esta fecha de caducidad.
     */
    public function warehouse()
    {
        return $this->belongsTo(warehouse::class, 'warehouse_id');
    }

    /**
     * Obtener el estado de caducidad del producto.
     *
     * @return array Contiene 'status' y 'badge_class'
     */
    public function getExpiryStatus()
    {
        $today = date('Y-m-d');
        $expiry_date = $this->expiry_date;
        $status = '';
        $badge_class = '';

        if ($expiry_date < $today) {
            $status = __('منتهي الصلاحية');
            $badge_class = 'badge bg-danger';
        } elseif ($expiry_date <= date('Y-m-d', strtotime('+60 days'))) {
            $status = __('ينتهي قريباً');
            $badge_class = 'badge bg-warning';
        } else {
            $status = __('صالح');
            $badge_class = 'badge bg-success';
        }

        return [
            'status' => $status,
            'badge_class' => $badge_class
        ];
    }
}
