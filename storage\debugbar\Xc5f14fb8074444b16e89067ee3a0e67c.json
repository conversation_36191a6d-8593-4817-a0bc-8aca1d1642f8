{"__meta": {"id": "Xc5f14fb8074444b16e89067ee3a0e67c", "datetime": "2025-06-30 22:39:08", "utime": **********.838213, "method": "GET", "uri": "/cookie-consent?cookie%5B%5D=necessary", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.137643, "end": **********.83823, "duration": 0.7005867958068848, "duration_str": "701ms", "measures": [{"label": "Booting", "start": **********.137643, "relative_start": 0, "end": **********.484887, "relative_end": **********.484887, "duration": 0.3472437858581543, "duration_str": "347ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.484897, "relative_start": 0.34725379943847656, "end": **********.838232, "relative_end": 2.1457672119140625e-06, "duration": 0.3533351421356201, "duration_str": "353ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50257704, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET cookie-consent", "middleware": "web", "controller": "App\\Http\\Controllers\\SystemController@CookieConsent", "namespace": null, "prefix": "", "where": [], "as": "cookie-consent", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=2370\" onclick=\"\">app/Http/Controllers/SystemController.php:2370-2422</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00196, "accumulated_duration_str": "1.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\SystemController.php", "line": 2373}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.531851, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "gyNYmbyKwm6eXaWnA6K4gEOnwLBYtCKxV8kdvO29", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/cookie-consent", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-124781743 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">necessary</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-124781743\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-347331132 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-347331132\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; XSRF-TOKEN=eyJpdiI6InF6eDg0VlpBMDE0QkdnMHNZTVNtS0E9PSIsInZhbHVlIjoiOGdLc0p5NHdKZ0VwUnluVmgvQnZQd3VmMHZMWEpoYm1INTdqMXVSU0xuR3dXSHRUNDF2bU01dnJpZ2IvQ1ovR0tKQnR4cUIrYVBnRW9yemtucDV6QXJHaUJ4dFVZaUhENW9nSXZWaGRzMGdNdXRVaEljY2tBejJMaUhiK3dCaWNyY1hRUkhxaDFKU3ZQaHcybGdoVmRaOW9SWTM5R1E4Q2t3c2pJNUp5dnJJdTZQdnBQbTRiVFpKRnhPdUNqVUlkQTExeUFwOEFHajlaNnN1M2oyeVJkam1IUHVkb2NJZTQ5WGtTaUQvMmVzTm5zdlQzSGRrYjQ2Y2lHNnhOV2MvbzBqSXBTam5HdUhKeEcvK2JEYk5Gdlh3d29DRW4yOTRDcThORnp2WDJsd25NanloejNnOU55Yk1FVXZQRHp0WGJScHU2bjNWQlFqVzRNWW9FOG10UXhRTlQrY0FFZmNXd1NsT0swMllKUXptUitpV0pZZFZCVjk1M3FvbDN3bkZzbklUNTFNZzlLNEh5eHd6elNaM0lVS3laNjhLNWtaZXdMVEN2NnYxM0xMSXluUm5vSFJHb0xlaFlDb1piTXhGWTNUb3diaWpQUGFLUHNvYVhCMUFCQ2I1SkowazZBMHU4MnVVY1hGaHN5YWdWbGM0b1R2TU1IS2N6YW1FK1NvNFMiLCJtYWMiOiI3NmM3NDQzN2FiM2ExNTE2ODg5MGY1MjcwN2MzOTA2NTNiZmRlODFmMWQ0YWI3ZDhhY2JlMzA1N2YzN2M1NDAxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkZTV0ZEK01ZTmVjRG5vYVJnVWxYRlE9PSIsInZhbHVlIjoiQ2JqTXRlVHBQTFErblorNkd1cFVhQlBCZXNSTURMVVRrNk01OGgxM1oza0ttSXoyNUEwYlZyb2drbERBSUlJbVpleCs5WCszclpVVmd0RDV5QncvanRRSnc4MXFRbTlscUdYVjF1WklzVjR3U2l4anA3UEhKaldYZ0ZlSGFGY2t1d3QxMGxkUldYdklxS3FoQUZhbVBpRlpkcUlyNUJXN2ljK28wVTRXRFQ4MW56UFo5VUxnVXYrU01WUnhTWVlZVkNNS1lEaTc2NG1RbEl4R3huODBieTNMRzdBd3MrbFozUFdtbzcvS1ZCUDRHU3kzblE2SWpxMXg3THB0dnZ6MkFQWjdUQUMvU2o1WEsxTWZHL2tOZjVYaU9pZk5YczNsNDZyNmNwQkgrUGhYMlJ1NCtZdFQ1d05hWjMxc0FIYk1nSkV3OFhIUlE0ZEN2VURQM0FoYXFiNUEzeVNHZDRxRjhJeVdDVGFxU3UrNlhQZXZWNHFIUnRmTjlyZkhWc09hb2JzRmwzV0RmdHdESTBHYlFiSEdJMUJDTGhvV3ltR3JxajIyYkNEMGh3WisrZWcyVmMyQ2R5cVpxS25SdVdaVlpLL2xqczdyalpqM3ZiVDVkU1kyaWo1T0RtTDRzYmp6NzBXdWRvbHcweHVIeFlXcTBZcTNtMnY5VyszQzI2L2MiLCJtYWMiOiI5MzQxY2YzOGM4MWM1ODUyZGY0MjJhZjYzZDIxOTZkMGU5ZGJlZDgwMjUyZWFkZjkyNWIwMTRiMmM1N2FjZmEzIiwidGFnIjoiIn0%3D; _clsk=eiqg7d%7C1751323143882%7C2%7C1%7Co.clarity.ms%2Fcollect; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1468488341 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RtHbcRqD8V7q1fDzvV41Fzq9te9B2VUCpYlSqZiL</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">91LwqcXsNZmfmRkjBpCz6hEenAJOpgI6hGqx9k4u</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1468488341\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1019774408 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZaN0RXQnE5TUh2NFBuSWlFdWtWMGc9PSIsInZhbHVlIjoiY0JGSmljT3h0VWFTSmRYL2FmNjB4ZytDWHhvM1RIdndDRks4bjNrNnFsSVpUaWVESmlEdEJMcmxHM2hqUlZidGlLd240THNaa1c3UllDa3JBK3NiZmM2Q0dyOGJVNG5sM3hacnE1cEZNTUs0b1lTSkE3ZHljUDRWNlhuVnVoTk5PclE4S0lncDlUZFRSMWlVT2pXVjB5TS9zSG9EL1dCd245Qk5DeE9IYmh2UlYvM2lIMHhRV21iSHdTY0dEYUZFSE1FdnVOdzZVNWJ1TmNPa015RWM4cE82cVVZSG9BdDF0V2lCNForL2tEYUZ6MTVra1p3SlBCWkR5QWcyUHNqQlNEWlNMN3NnK3lHNWp3MXp3VkJhMGhiUUhTUmc3S2k0anEwNnF0VStRU2t1QnFjZnJoN25VVzQ2WTM5N3ZoK2RQVU1rR25DSDN2RDFKN0xWbnVFbllOOS9kRDFyUlc5QTRvUmZ6d1dMNVhRT3Y3dFdGdDlIZExvLzl1R1ZJTXVVOFlrdjZBRG84eWsyQ2pHdDZnc0JQZExlZ1lIKzluMXZTNjBtaDZDYlRwekFuWXY2a3BsYUFpUXdpMkE0NzRzZGdzSVN3VkZyUEVBUndianVmNUF2VjYvNjlrc29nWGhIUHVyUU5OMmJlZWh4TmYyTS8wSk9YbStqY3FYTkJmQkoiLCJtYWMiOiIyNWE4YjBlMGVkNTIzZDc1M2JiOTkzMGQzZDRlMjdiOWNkNDdjMjhlMmYxNTdkZWIyZTUzZDZmZmFkMWNkYTFlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImFXd1NKbklPQ0swRFpYR3AweHlSWGc9PSIsInZhbHVlIjoiVTFJOVV3VTBTU0pwcnVGOGV3NnVHdUNsa0RydklHZVhiVmF6SzFNa2Uxa2tLd3hvZmlaSWoyNFgxSHg5bWg2anVhSTdPOU1wbGxQMzZVVkpOei9yL2hRRm1kZ0RKdy9PVlg1VzlnRWZIeCthOUxkTGtyczVOR3FQV3JaaEVYdnQ1RXRNUVJDWXhLWEhzZkUrNkc2Znd5NVBHbktVNW5tWHZaSkE4YWZJM3VSRmQzQ3hnU3RUanJ5c0NwNDVocjVCSDFrRDRxbnBVNmRIWDV1TGdtbWRIaFhkRFpqbngyLy9lS01sVWVvekRkRWdvNkI4cGtuaGxjdG8rWlA0RGZ0OWp5S2d3TVdpc2hIWi91emRpcmhHN0dRK21XZTJUVEluTmRzaXVsZ3VvWm05eXJucFlCSE13T1o5dU02M0ozbWFiYXpDS2xvT0x2ZE41TS9mK0pwVDNUcXllRmM3ZnRuNURrbnNEZGF2b21uRFN2aVdaL1k4OTA4RnZoRGxPY0ZsNFJLM2txMVVHcnFLaXJaWm1Hd29vOXd5QklPdGRiTkF0NXlVSDdDVUIra1RYTHZoZExDRWlpaGpybXpvRGxVKzM0Nnl6N1J3NXhoK2NaZ0VGMGc5MmsvY2hHb2E1aWZOZlhQRzRlUlgweUdBMHJURk1DaG84U1hmYTB2NVgzUUkiLCJtYWMiOiJiZmU1Zjc4Y2Q3ZWZmNjZmZmZiYjgzOGEyYmUxNjkyNTg3ZDg0OGIzODA1OGE5NGYwMWIwN2I1MDJkNjUxMzEwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZaN0RXQnE5TUh2NFBuSWlFdWtWMGc9PSIsInZhbHVlIjoiY0JGSmljT3h0VWFTSmRYL2FmNjB4ZytDWHhvM1RIdndDRks4bjNrNnFsSVpUaWVESmlEdEJMcmxHM2hqUlZidGlLd240THNaa1c3UllDa3JBK3NiZmM2Q0dyOGJVNG5sM3hacnE1cEZNTUs0b1lTSkE3ZHljUDRWNlhuVnVoTk5PclE4S0lncDlUZFRSMWlVT2pXVjB5TS9zSG9EL1dCd245Qk5DeE9IYmh2UlYvM2lIMHhRV21iSHdTY0dEYUZFSE1FdnVOdzZVNWJ1TmNPa015RWM4cE82cVVZSG9BdDF0V2lCNForL2tEYUZ6MTVra1p3SlBCWkR5QWcyUHNqQlNEWlNMN3NnK3lHNWp3MXp3VkJhMGhiUUhTUmc3S2k0anEwNnF0VStRU2t1QnFjZnJoN25VVzQ2WTM5N3ZoK2RQVU1rR25DSDN2RDFKN0xWbnVFbllOOS9kRDFyUlc5QTRvUmZ6d1dMNVhRT3Y3dFdGdDlIZExvLzl1R1ZJTXVVOFlrdjZBRG84eWsyQ2pHdDZnc0JQZExlZ1lIKzluMXZTNjBtaDZDYlRwekFuWXY2a3BsYUFpUXdpMkE0NzRzZGdzSVN3VkZyUEVBUndianVmNUF2VjYvNjlrc29nWGhIUHVyUU5OMmJlZWh4TmYyTS8wSk9YbStqY3FYTkJmQkoiLCJtYWMiOiIyNWE4YjBlMGVkNTIzZDc1M2JiOTkzMGQzZDRlMjdiOWNkNDdjMjhlMmYxNTdkZWIyZTUzZDZmZmFkMWNkYTFlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImFXd1NKbklPQ0swRFpYR3AweHlSWGc9PSIsInZhbHVlIjoiVTFJOVV3VTBTU0pwcnVGOGV3NnVHdUNsa0RydklHZVhiVmF6SzFNa2Uxa2tLd3hvZmlaSWoyNFgxSHg5bWg2anVhSTdPOU1wbGxQMzZVVkpOei9yL2hRRm1kZ0RKdy9PVlg1VzlnRWZIeCthOUxkTGtyczVOR3FQV3JaaEVYdnQ1RXRNUVJDWXhLWEhzZkUrNkc2Znd5NVBHbktVNW5tWHZaSkE4YWZJM3VSRmQzQ3hnU3RUanJ5c0NwNDVocjVCSDFrRDRxbnBVNmRIWDV1TGdtbWRIaFhkRFpqbngyLy9lS01sVWVvekRkRWdvNkI4cGtuaGxjdG8rWlA0RGZ0OWp5S2d3TVdpc2hIWi91emRpcmhHN0dRK21XZTJUVEluTmRzaXVsZ3VvWm05eXJucFlCSE13T1o5dU02M0ozbWFiYXpDS2xvT0x2ZE41TS9mK0pwVDNUcXllRmM3ZnRuNURrbnNEZGF2b21uRFN2aVdaL1k4OTA4RnZoRGxPY0ZsNFJLM2txMVVHcnFLaXJaWm1Hd29vOXd5QklPdGRiTkF0NXlVSDdDVUIra1RYTHZoZExDRWlpaGpybXpvRGxVKzM0Nnl6N1J3NXhoK2NaZ0VGMGc5MmsvY2hHb2E1aWZOZlhQRzRlUlgweUdBMHJURk1DaG84U1hmYTB2NVgzUUkiLCJtYWMiOiJiZmU1Zjc4Y2Q3ZWZmNjZmZmZiYjgzOGEyYmUxNjkyNTg3ZDg0OGIzODA1OGE5NGYwMWIwN2I1MDJkNjUxMzEwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1019774408\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1547434041 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gyNYmbyKwm6eXaWnA6K4gEOnwLBYtCKxV8kdvO29</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1547434041\", {\"maxDepth\":0})</script>\n"}}