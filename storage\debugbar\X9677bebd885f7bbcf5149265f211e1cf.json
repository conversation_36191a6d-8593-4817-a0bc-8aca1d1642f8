{"__meta": {"id": "X9677bebd885f7bbcf5149265f211e1cf", "datetime": "2025-06-30 22:36:51", "utime": **********.115864, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323010.662461, "end": **********.115882, "duration": 0.45342087745666504, "duration_str": "453ms", "measures": [{"label": "Booting", "start": 1751323010.662461, "relative_start": 0, "end": **********.05024, "relative_end": **********.05024, "duration": 0.38777899742126465, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.050252, "relative_start": 0.3877909183502197, "end": **********.115885, "relative_end": 3.0994415283203125e-06, "duration": 0.06563305854797363, "duration_str": "65.63ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45707192, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00359, "accumulated_duration_str": "3.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.085123, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 57.66}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.095053, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 57.66, "width_percent": 13.37}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.101134, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 71.031, "width_percent": 28.969}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-562917622 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-562917622\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1715084424 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1715084424\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1326564262 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1326564262\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-364736708 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=6nrx0y%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1aj6s00%7C1751323007905%7C5%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkduZG90RGFSWWtxVFlaQUwrT2FMQ2c9PSIsInZhbHVlIjoibzdPSGFBTERVYS96NjQvVzBCdmxhNVZnb2ZOSUFRS1pLVFltV3Z1bjBXZ3Y5aXZrZ2JHS2dBWUY0ajh3c0hTeFZleHQvdlJIOEhpT3FHcUp4VFJYdG9qYkQ5VWRONU9JN3FVUEc2UE85TzFUTFNWbzlxVGpqd0MyUTVnWDVyT2ZnZUtoYks4M0lXelcvZklGWkQ1djRMeHFUQnZML0NWYTU4ODExSVNUU1ZSWHI1TG1sWS9DMDltQ1ZWcGF6STdNc1cyaEc4UjJka1ExRlRzYUJRTkpuTThHNjdpdmd2dE9YdGNEbHBQaGhFM0QwTWxmbXdhK0JxN2Y0dDVyRkVIVTRJa3d3ZGVGZjcrbEx1ZzVOb3h3VDZtWXQyRHlFdHhFQ0lxdVYwQUh5cGFVdklmaWNTMnZNU21sTW50YWFjY3lPdmYwZFNQN3NMSEZxMXEwMEUvRklleVdwQWNJOGhDRTV0eWFCTndJZmlkVUNlZW5wdS9mSC8yUGgwMkhFWHoxdVh4d1VBd0xTaElNczJuOUtjUHhob1RzWnU3L0tpTDNyeUZienFyMHRJdXVMd0c0bWZuUkd0Rzd3Z0Q4TVFNbUdzUGtXN3Nxc2QzcHhjMlpxbG40YUduWEt6VFhlOXpwUGN4S1YzU2QvcTZjYkplSlIxRmcyZmFqaVhZWDdOaWYiLCJtYWMiOiJhNTcxMzVlZDFiZWEyOWQxODFkMTBlOTQ5N2EwMGQyMjQ1NTEyYmY2MzcwZDVkYWI5YTMyZDQwYmU2MTE0ZDE2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjFWK016RHhIZGNMQ2ZvWWsrc21GWFE9PSIsInZhbHVlIjoiOGNTOXFEdVhOaXY4Zytzem05SHJrZk1ZQlVCdlMxYjhkeEgzYXc2dVNkbjhPdjdCaDByaDVyMjNFY09lZGxNdXZieFFoc2pYUXdCUmFkQ2RwdkhWdHovVlJyb0l4dGE1OW5rYmgrZGtIbVNRakZHQUMreXZqcmVGWjNWT3FYeVh4TEhONjZBTGlkRTRBUkdQdjdHalhkVVdBdXV5ZHFRNm9hQTFKQVZXNEpMbUlvWkYxb1VYZ21wR2ZyNm9lZkdyNGI5b20xYXhIWHhEMmxGVDhaUUdSaUUrakFUdENwOUhxMjNUK3VoOFludVZTNGxRNjlrWHRmTGJxWk1CU3pyeEF4bVVCWWsyZWJNa2dTZTNIRHBZbTdURmNLVUd5RFRiVm1iWitIc1JadXBsTzNIbkt3dmtESHVDNnYwWnpqVm9LaVh0bXNQT210OWw0YmZ0b0hER2x2MVRuNFp5eThPdlJPbmFEU0JzVUZTOXRhRVZGakN0eTlGTm1TMlBoQ0cwTGw1c0ZNVVdYR0JxRzJyc2U0L0M5UmU5SHV5QjM3ZDlISGZtRDZoRGszZFVrZnJmWENvNFVPc2ZWSHBXTCszMXVOK2VCT1EvRDRXbGFCOVVOZUpRNVliczFFMmt1a2NDOEFqZE9YazVjODlJYkxHN25ZVXFtZ0dCZzBWRTVyMmkiLCJtYWMiOiJiOWZiMDRlMmU3N2ZjMmI1YjU4NzZlNWE1ZTAyOTJkZGI0MTYwZjc4MzdhNzZiZDVhYjVhMzUzZDA0MmY5NjIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-364736708\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-60055082 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A9rdyCRm7SKpfljuMnVO7IpcgTBMITMjowAdsmJo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-60055082\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-529718869 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:36:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJwV1IxREliT1VsUEFYdk8wQTVHZmc9PSIsInZhbHVlIjoiY1RmU1JaRyszRXJrQzhsTkw2Q2lINU1lOTd3QjZ3OHdocWxkVEx3WlBiWU45Q09BcHdxc2ZiOHd2UVRleUdkQytIdW5iTkY0T0ExUSt1b3h0cUJldXg3MEhrNlNUOXNKVzRCeTAzNUFvaVN1Nlo3dEtTZ3Z2TFNEcG5qTmppL0wzT2NlQS9hNUhGWTBLQkpNT051ZlQycWIrbnRWNHpaOG5ORERGUXhJSXN0KzNDUlhiRFBaY1cwaDVTQzh6a1BlcWdURktYZzYwZjNSZXRDTGtId05SRzlCMEhmNUYwa3ZFMEpsNFZ4NHY1TytKcmMzRWd2Nlp1ZW9ua0hJRUZIL0NibnpHY28vU1UxUC9qTlpHV3pPYXp4MGowS3BSS3JMTkx2ZUhEVlRRMS9LL0NIenBiejBMYlhGbDhUQ2pHNGE0MjNRa09jVzFkaUEyOUkxK0l6U3FXaEZUOExaYzRhM2t2VjVFRk1ZUGhkcnF6bHdPcHdXVGdTTkdETjJjYVNmcEhFWGZZY1Y0TERuZEpqTVFqSzRkYThRRGtHR0hiMWVIZ2JYcXRsdDhvd1hVZlRudUI1enZtUnpIYXpDczdIQzQyeWhNQzVQQkdweEswdDN6MXp5R2JZZ1ZJYzByLzhhMXdxV1ZaTzRWSitZcUFyUHZ4MUhvQTNEWGdCaUllRlkiLCJtYWMiOiI0YWIzNWM4ZmZjN2ZjM2I5OGIxMTg2MGJkZTg0MzZjOGQwN2U5YzUxMWJmMmI0MDE5Y2ZmODAxMzlkYWRjZTk2IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikt4a3kwMEFySXFSWUNwdkxFODZFQlE9PSIsInZhbHVlIjoiQmRSckR6YlNGRCt5cU5sVkNGeFduWDIxMlVNVk5YWlZqNWNGNzZ3Y1ZwVjkwdlJCV1JOZ1RkTjc3aXlxUjlEVm96WFcvYk9UdVI3WHQzTnFqS0EzQXFOOWFPUjdSNm9vVS9Dall3NXdxb0NFMXRqTzYwTkdwZEpQMWFZSnZIb0tVVDlxdjl4OXFtNDNVSm0vbnhmSnZCT0psYTBGd21hK0toYVkxU0FmaWR4dlV0ZjRLRkdOUHpQYkdXMHNEV21RN0JnaVVSSytwQ2NWd0dwSENqNDFQTTZ5OHJveHFEd1owbDV6YmxDZXBIMlVLT3hRNENnbUVIemVQNHdaN1ZIM1I5dHVud091Q2hxSnpBbTRHbEtOMlk2dWM5VEx4SHFkU0wycDJ5U3MzczIwN29xbTkrNVdpOFpLUHNYakUzczJ4RGdmeTdNSXROQzdLbDg5STFTVnBMbWZBeXBYTkNacGwyRW96Tis3VWdVUWRGMzF3elozQXdJaDVqNHErZVFTK2N2QnF0UGpvUjRDa0xkOVdtUm9tSHVSb2hUV253SjllbkpJMEFJWWdsYUFhQisybGpLR2N4RWhXVEMrL3VWaFFyOTh5cTY0aHp3b2F1MGVBV1h3MjZFUUQxQUw4eENXRHV3a1MyVktVQ0NzWFdES1FiNVJyMU5yVGNRVnh0L08iLCJtYWMiOiJiYWViNGZmMTA4YTE0YTgxNjZkZTZjYTQyNTU0M2VjNTYxM2VhYjFlZDczYjJkZjhhZDQwOTRlZWIxNzYzNDBiIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJwV1IxREliT1VsUEFYdk8wQTVHZmc9PSIsInZhbHVlIjoiY1RmU1JaRyszRXJrQzhsTkw2Q2lINU1lOTd3QjZ3OHdocWxkVEx3WlBiWU45Q09BcHdxc2ZiOHd2UVRleUdkQytIdW5iTkY0T0ExUSt1b3h0cUJldXg3MEhrNlNUOXNKVzRCeTAzNUFvaVN1Nlo3dEtTZ3Z2TFNEcG5qTmppL0wzT2NlQS9hNUhGWTBLQkpNT051ZlQycWIrbnRWNHpaOG5ORERGUXhJSXN0KzNDUlhiRFBaY1cwaDVTQzh6a1BlcWdURktYZzYwZjNSZXRDTGtId05SRzlCMEhmNUYwa3ZFMEpsNFZ4NHY1TytKcmMzRWd2Nlp1ZW9ua0hJRUZIL0NibnpHY28vU1UxUC9qTlpHV3pPYXp4MGowS3BSS3JMTkx2ZUhEVlRRMS9LL0NIenBiejBMYlhGbDhUQ2pHNGE0MjNRa09jVzFkaUEyOUkxK0l6U3FXaEZUOExaYzRhM2t2VjVFRk1ZUGhkcnF6bHdPcHdXVGdTTkdETjJjYVNmcEhFWGZZY1Y0TERuZEpqTVFqSzRkYThRRGtHR0hiMWVIZ2JYcXRsdDhvd1hVZlRudUI1enZtUnpIYXpDczdIQzQyeWhNQzVQQkdweEswdDN6MXp5R2JZZ1ZJYzByLzhhMXdxV1ZaTzRWSitZcUFyUHZ4MUhvQTNEWGdCaUllRlkiLCJtYWMiOiI0YWIzNWM4ZmZjN2ZjM2I5OGIxMTg2MGJkZTg0MzZjOGQwN2U5YzUxMWJmMmI0MDE5Y2ZmODAxMzlkYWRjZTk2IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikt4a3kwMEFySXFSWUNwdkxFODZFQlE9PSIsInZhbHVlIjoiQmRSckR6YlNGRCt5cU5sVkNGeFduWDIxMlVNVk5YWlZqNWNGNzZ3Y1ZwVjkwdlJCV1JOZ1RkTjc3aXlxUjlEVm96WFcvYk9UdVI3WHQzTnFqS0EzQXFOOWFPUjdSNm9vVS9Dall3NXdxb0NFMXRqTzYwTkdwZEpQMWFZSnZIb0tVVDlxdjl4OXFtNDNVSm0vbnhmSnZCT0psYTBGd21hK0toYVkxU0FmaWR4dlV0ZjRLRkdOUHpQYkdXMHNEV21RN0JnaVVSSytwQ2NWd0dwSENqNDFQTTZ5OHJveHFEd1owbDV6YmxDZXBIMlVLT3hRNENnbUVIemVQNHdaN1ZIM1I5dHVud091Q2hxSnpBbTRHbEtOMlk2dWM5VEx4SHFkU0wycDJ5U3MzczIwN29xbTkrNVdpOFpLUHNYakUzczJ4RGdmeTdNSXROQzdLbDg5STFTVnBMbWZBeXBYTkNacGwyRW96Tis3VWdVUWRGMzF3elozQXdJaDVqNHErZVFTK2N2QnF0UGpvUjRDa0xkOVdtUm9tSHVSb2hUV253SjllbkpJMEFJWWdsYUFhQisybGpLR2N4RWhXVEMrL3VWaFFyOTh5cTY0aHp3b2F1MGVBV1h3MjZFUUQxQUw4eENXRHV3a1MyVktVQ0NzWFdES1FiNVJyMU5yVGNRVnh0L08iLCJtYWMiOiJiYWViNGZmMTA4YTE0YTgxNjZkZTZjYTQyNTU0M2VjNTYxM2VhYjFlZDczYjJkZjhhZDQwOTRlZWIxNzYzNDBiIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-529718869\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1939102656 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1939102656\", {\"maxDepth\":0})</script>\n"}}