{"__meta": {"id": "Xecab1228805b8ce79ef0de831d16475b", "datetime": "2025-06-30 23:13:52", "utime": **********.756853, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.326305, "end": **********.756867, "duration": 0.43056201934814453, "duration_str": "431ms", "measures": [{"label": "Booting", "start": **********.326305, "relative_start": 0, "end": **********.700525, "relative_end": **********.700525, "duration": 0.3742201328277588, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.700533, "relative_start": 0.37422800064086914, "end": **********.756869, "relative_end": 2.1457672119140625e-06, "duration": 0.056336164474487305, "duration_str": "56.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041168, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00267, "accumulated_duration_str": "2.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.733335, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.292}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.743602, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.292, "width_percent": 17.978}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.749337, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.27, "width_percent": 15.73}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-964443353 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-964443353\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2007609685 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2007609685\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-761784537 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-761784537\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-540644674 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325222314%7C30%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZpOXBRNlJSZGVnZFFOaDREZk5pcmc9PSIsInZhbHVlIjoiZW1XVmc5Z0k5REFmNys4RERERmtLdWdyVnpXZHNkV0E4UFdzWTF0cEtOYldKTkVNK1h3YmE5cHI1TXBqdU9hS2xLM0hHeXBMSlBtbnZVQlM3K1NjbE5nTFlpQmdnMUlPbjhHbkx0TERHblVYZ0R3c3pWVnd2cnBpbUdCMk9RUmJVbUNYKzJpRUFDenppZlRKcUM4NTlTWU14NzRoMXJQZVBGSTdDUzdBb1NSb29xeFRLK3EyU0JndVJ5b3M1VzhZWk9raXhUVW8wU3JkelA2UnltQTRPNGVqRXZhM1FPSW1EWEZGRTAwSW51RDRMYUQvLzJ6TklwakFYRHJhdzlocE45Y056OXdEaHdEZWxHTW9scjVBTVE1VHlPVlNjbFkvN2FzSVdaSGdtS0ZLc3lEa2ZTeGFhbVNybW14Vzk3Q2wrcE9LN1Myek0rWUhEZEFzMXhkbEhJbWlPZVNZTE1YRmFlOEhJdm1nTzRwU3ZncEJqRXBiZmNGWjB1UFF0UFNaQ1VnRExZZU5aeHhLQjQ3QU1ic2t1bjVEK05rUE4wWXlMd04yRHhjMGtka0draUk1VTFuelRVQXM0NkJzb3ZlQlhIOU5SRmxkVUI3KzVrTHh6TC9ORmRDT2hWb2kzcjg3UUU1azM2d1hlTjBES3ZGR0dRUEs1bTUxRlRoVTJIQnMiLCJtYWMiOiIzMmI0NjYxMDEzMTIxNDZkMWU2MTYwNjhhMTcwNjZjYTM1MGMxYzM1YTBjOWEyYWJiZDk0NjUxMjJiOTdkYTUyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZQSW1QbGFZQzY2TXV2MWMyOEZIc0E9PSIsInZhbHVlIjoiMEtGdjVPRnpRRmtTdXNhMmVGQnBzcUhJbXdvVDV2dDJDMmFOUStrdVBRTWc5cE9iWTJpelFPMTMwdmRWbEo3ZU00TW9kMXdycWJhQUtubkhkQmQ5VW9Sa0tWK2Q5dncrRkhoc1Jna1hZZkt5NGNHendxTVpLcDdJQm51elpuMHFkREpBeUpXaUprUGUzV3dDemRBcS8yRWVsdURPNHRYWUp6SjBWOWxFaHpSd2dGYWRKaG5qYmk4TThIcmpmd3djNVNnbW9sYXpRL2ljQlBRb1JPa2lzK3FVdXlaazZZWm1TNm9HVVV4UTh1ZHM2OTBSakNqaU9OZGZSVVNjamNtd3hmb055N1FtdlZUbGJlRjgzZEhyOFFGY2szZ2dOU0dzWjEzRW1SL2NEZjVBbVFxdDBOSlFpZnRtZkhwYWszSFRNMG9rNlg5MUZvODJBbitSR0d1alZSQkxnY3ozSlBkcWh5cWpiZ243WmpxeUlKYTRKQmJaVmdYVzlqL3RBU25jSUpFaU4xOFdtV1lVTUNLUlVFdjlHeG50TVdaY1FFd0wvRFV5S3Z0T3JuWlRmUXQyMmV2TnpSTEFJOHUyaWYrVWtCV3lieUdDbzRzN05id21VMXZhNS9IMjZhQ1NYTVVseHdGYTFEdkFCREo2RTlwaisrcFdtdkZ5bnVEMzRLS3YiLCJtYWMiOiI3OGJhZmVkZDY5YzY4Njc3YWNmZTA1ZjY5ZGIzNGVkMjdiYTNiODk1YmRiODU2MzNkYjNiNjljOTRhM2FmMjM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-540644674\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-695359022 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">H4YK57QUXyJ63T0CnkmwYWGCllGQ9A8V3d5cZCUG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-695359022\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-510582120 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:13:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlB3MVgyK3h6M2h2VVQyUG4xLzJwK0E9PSIsInZhbHVlIjoialhkUGJ2RHlGMXE2dVdTNEtleXZJMHZWVlBoTGtua000L21Yd3FTMytGdXU5WlVXM2QzdWhQb1JJeWJlZWlhWTMwUVpOVEZqbUYxM0lQYUx3RFRFRTk5T2t0UmJZZHRmMnNXNnVsRFhmQVY4QWM1Z3ZPOW1iaVlUdTN4V1BBY1Z2RmUxTURKTjFYSzJGRk1MVDhReVBKNDZDdFFVQ05zU1EvdDFYZkI1dlNkRGhsNy9xb2lESVlLSDFiWVNaejdiSm5WMkZndHdtdXVMOFdDYUptdTIzeStRMUVQeUtqOFg2UTIzVG1PNUR6VFFEYmZPNW03UWNGMXR4aEw4TUZwTU9menZoSzdXWnNwK1lqOWF1dnNEQmJWY0JUWVplT1ZibzgzclFYWTBOSXlqSWh4bHhBdkI0NG9oM2NSQWxhOFhIQWN3ZFZ3U1JkZVBTV3VmKzhjaHFMZjhIcldCbjZZdEE1SWFqdDdTdXA5N1lCdG1ud2xnNjRaZDBMMUR5L01xVC9NT3JwdUw2VGI0UnVOTkU1dHNHMTFHNWJVaWh1Ylh4cmo1YitLQWZQWVE5TWR3MUV1VTVQdk93TWN2NjFLTmxmK2pZZGdyNHpsa09IN3dvSTR4WEF1MXZQK0pZNWVJYm5FMU8yTnFKTzlobzZDZTIxZ0QvWFJ4UmdDcDYzUXciLCJtYWMiOiI0YTBiZDE0MzE0NzE5NDNlYThhOWQxOWIwYjE0Yjc1MTk3ZTI1YTk1OTA2NmFmNjBhMmRlZGMzOGJhMmE0NTc2IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImQ5aDQwc0xVdVlGT1F0RER3TXNWUXc9PSIsInZhbHVlIjoiT1g4RjFIclJlb1krdjlJUjVWWTZXZGt0dk9LcXlCcVBjdnJxQ3k2OTJBV3hkNjQ2bW8rZnRtcWkwMGw4aU4zaGw0dzJhVFR5QVByczBFR29CNEFwVGduRnpIc3paQW11R09WaFJPdUJEalR4QlA5bnVncmtuU0UvSGd1TEU0SDc1NjlWU1ZXL1NvbWpGY1d5MFBZN1JBZERnSnR0REYwcHhMOG5UbkhnVjE1UEpiWW5nZFlFblNuOWNnTS9XOUVRVHNlUUJrZjh1aEFxc1FGM2FHZjdCLzYrdC9qZm9ZRzY1bWZjT1FnZTc0eVcvZVQyVnV0QUo4WEFGRlVrN2FwOGQ1TS8zSFJ5ayt3M3g3MXpZdllwbzRMSjhleFk1MGw3cEdiU1VCdkFGL1duT1FIa3hhNW1SNFBKQm9ldWdid2prbDhDZU9sTStSM2JPWmNiNkhUaDc5K1UwVkFoc0FLTFROdHdxSEZzZjVvaElucndQL09CWE1wKzRGdFdHYVYvalZOS1lyRXJiVUtLUzVlWGtDSlNCL2o2K2xJVTRsc083QXh2WWNQS24yU2xSUDViSEZ1WEY3WVZIT0Z4bXc3bVhSK1pBa1c2bzdQa3VsbkN1VVVscWhITmJ2SnRVdlFYWDFXMG8vc1RLWmZVeTJkS2VtcU0zZGNGOTlWblNXUFciLCJtYWMiOiI5ZWJjNGY4Njg3YzVmMjcyMzIwNjg1Zjc0MTA5YmFlNmI0Yzc1NjQ5ODQ0NTI3MTlhM2UwMmFhYmQyZjNiOGI1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlB3MVgyK3h6M2h2VVQyUG4xLzJwK0E9PSIsInZhbHVlIjoialhkUGJ2RHlGMXE2dVdTNEtleXZJMHZWVlBoTGtua000L21Yd3FTMytGdXU5WlVXM2QzdWhQb1JJeWJlZWlhWTMwUVpOVEZqbUYxM0lQYUx3RFRFRTk5T2t0UmJZZHRmMnNXNnVsRFhmQVY4QWM1Z3ZPOW1iaVlUdTN4V1BBY1Z2RmUxTURKTjFYSzJGRk1MVDhReVBKNDZDdFFVQ05zU1EvdDFYZkI1dlNkRGhsNy9xb2lESVlLSDFiWVNaejdiSm5WMkZndHdtdXVMOFdDYUptdTIzeStRMUVQeUtqOFg2UTIzVG1PNUR6VFFEYmZPNW03UWNGMXR4aEw4TUZwTU9menZoSzdXWnNwK1lqOWF1dnNEQmJWY0JUWVplT1ZibzgzclFYWTBOSXlqSWh4bHhBdkI0NG9oM2NSQWxhOFhIQWN3ZFZ3U1JkZVBTV3VmKzhjaHFMZjhIcldCbjZZdEE1SWFqdDdTdXA5N1lCdG1ud2xnNjRaZDBMMUR5L01xVC9NT3JwdUw2VGI0UnVOTkU1dHNHMTFHNWJVaWh1Ylh4cmo1YitLQWZQWVE5TWR3MUV1VTVQdk93TWN2NjFLTmxmK2pZZGdyNHpsa09IN3dvSTR4WEF1MXZQK0pZNWVJYm5FMU8yTnFKTzlobzZDZTIxZ0QvWFJ4UmdDcDYzUXciLCJtYWMiOiI0YTBiZDE0MzE0NzE5NDNlYThhOWQxOWIwYjE0Yjc1MTk3ZTI1YTk1OTA2NmFmNjBhMmRlZGMzOGJhMmE0NTc2IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImQ5aDQwc0xVdVlGT1F0RER3TXNWUXc9PSIsInZhbHVlIjoiT1g4RjFIclJlb1krdjlJUjVWWTZXZGt0dk9LcXlCcVBjdnJxQ3k2OTJBV3hkNjQ2bW8rZnRtcWkwMGw4aU4zaGw0dzJhVFR5QVByczBFR29CNEFwVGduRnpIc3paQW11R09WaFJPdUJEalR4QlA5bnVncmtuU0UvSGd1TEU0SDc1NjlWU1ZXL1NvbWpGY1d5MFBZN1JBZERnSnR0REYwcHhMOG5UbkhnVjE1UEpiWW5nZFlFblNuOWNnTS9XOUVRVHNlUUJrZjh1aEFxc1FGM2FHZjdCLzYrdC9qZm9ZRzY1bWZjT1FnZTc0eVcvZVQyVnV0QUo4WEFGRlVrN2FwOGQ1TS8zSFJ5ayt3M3g3MXpZdllwbzRMSjhleFk1MGw3cEdiU1VCdkFGL1duT1FIa3hhNW1SNFBKQm9ldWdid2prbDhDZU9sTStSM2JPWmNiNkhUaDc5K1UwVkFoc0FLTFROdHdxSEZzZjVvaElucndQL09CWE1wKzRGdFdHYVYvalZOS1lyRXJiVUtLUzVlWGtDSlNCL2o2K2xJVTRsc083QXh2WWNQS24yU2xSUDViSEZ1WEY3WVZIT0Z4bXc3bVhSK1pBa1c2bzdQa3VsbkN1VVVscWhITmJ2SnRVdlFYWDFXMG8vc1RLWmZVeTJkS2VtcU0zZGNGOTlWblNXUFciLCJtYWMiOiI5ZWJjNGY4Njg3YzVmMjcyMzIwNjg1Zjc0MTA5YmFlNmI0Yzc1NjQ5ODQ0NTI3MTlhM2UwMmFhYmQyZjNiOGI1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-510582120\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-523487115 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-523487115\", {\"maxDepth\":0})</script>\n"}}