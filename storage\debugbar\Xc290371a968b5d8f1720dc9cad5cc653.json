{"__meta": {"id": "Xc290371a968b5d8f1720dc9cad5cc653", "datetime": "2025-06-30 22:39:44", "utime": **********.526878, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.078235, "end": **********.526895, "duration": 0.44866013526916504, "duration_str": "449ms", "measures": [{"label": "Booting", "start": **********.078235, "relative_start": 0, "end": **********.46729, "relative_end": **********.46729, "duration": 0.3890550136566162, "duration_str": "389ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.4673, "relative_start": 0.3890650272369385, "end": **********.526897, "relative_end": 1.9073486328125e-06, "duration": 0.059597015380859375, "duration_str": "59.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45043376, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0028199999999999996, "accumulated_duration_str": "2.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.502253, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.794}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.513773, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.794, "width_percent": 14.539}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.519527, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.333, "width_percent": 16.667}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2101741361 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2101741361\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1098893207 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1098893207\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-398181063 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-398181063\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-15252523 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/branch-cash-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751323179076%7C12%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImNFa0xkd1c0TERyMHUxcUdDYVh5c3c9PSIsInZhbHVlIjoidWhvR3hWL1ZDaGpnU3dNYVd0Y2ttc0hpbDF5cnYwSU9ndkNjMUdhWG81SkFVZVgyMGNyelVIejE0Y3ZDdlZXc3NMOVJwc0FLMWxyUDZLclZxRXoyRHhwRjVTaDdFVVBZM2ZtSkZpOW9ZdWV6RVBiRkpJOTkzRTcveDhPU3REbm5HdTQ2dE5oSXZPUFZhcGY3SE5yZnMrcERycDI3OTIrNWNXTHZxbTBWQnA5SUpFcExKVFFPS09GV2wzWFVSdjBuaWpzY01KWEx5L3kzYzhVUVVZL1BacjZ3TFZZZW1taGJmSU9wYkZJMCt3cyt6VDFVZ2tMVkxJelAyS08xUUozTHhnQ1VJVGNpb25sY2lGV2FXeTlCZjNETnp6YVJxOGFUeE9VWW1ld1Zjd2tFbklqbG9xV3RWNVlZTjRrM2o4cjZITXZvZjJWc1NBOUhKNmtxYXM2WXU0NW5jNTFXNUxLVDlnR0FrdnR1Ly8zU0ZVM1NpUEY3bmlxbVlJWmFEaHp1MUdkeWRHdW4zQ01mRFNybFZmbk5yUzlpVjQrbExvQjhRTDFuNWRPMXpKMkhNWXdJdWpvOGVsdVBxUTNNZEVkQm8rSjY0YzVXamk5MUk1aU96Wk12d3AyR2IyM0ZhRzZoMWhyV0U2TjlzV2tMUzBGTkRwNzEzSHQvZ2lUeG9MOFEiLCJtYWMiOiJkZWFmYTY1M2EwODRiZjk5NzI3YWMyMWQyZmM1ODEzN2JlODFhZjgwNzk0MDMxNmJjZTM0YzYwODFhNDk0MWExIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ilp0MFYybUFZR1N4RllOUVlnVDMyMVE9PSIsInZhbHVlIjoiSnVtVjhaVmc1WC9qSllsQitwTWx3eURLRkpXb3Vlblc2RzVCYlFvUVNzczJvVDcrQkluVnNRQ3FCWWpGTElyMWo5WDJsWFpuVU9vUWtGLzZlTkZuUHdGaDhvOUlVNWhKR2J5M0hyellzUFlhclJtelpUVDNFSnNGWG43endYMllRS1kyRjRhQmFKV1BYdXYrK3B3cWZNRVFTSEZpK0QvWTZIdlB5RHkwblErR0U1UzRzcldxOTYyWjV1ODZpUVhwM2FUY2NQVEMzTEhXL09XSXFPbXJWaWRSbFRIVWZBNkorTFZqM3ZReGIzYVdUZC8yUE9odFI0blFwRk0rVm9jR3FoaFBKWWwzb285UFE3QVJWNFVSNnpvQ2xIaWtFZU5wWXBvMitzcWQrUC90U1hUdUo4R2tuTGVwMmxvMzc1blE1UG9LdHg5ZDNKN0lFK3FmdlpSaXEzTE5NbDFLeVpEd2hIMVN5dENzOHpuRXRabEt2a1hQNUYzcUNvL1ExN1BaV1pPSjdRTDd3SSsyTUpybFhjZlJaODhyeEVnMXErZHFOcWh0em1mTldEaEJYM1p4bG9CRjNWblpTSG10NVpxa2lJTkRjb0R6eGl2aHNXdkdtQ0FSa2sxeXJ0SWQzdkowL2M2dFNnT20xdFJwdlF1VnhRYmJnYngya1hSb0dyQlIiLCJtYWMiOiJjMDBkMTA1NTdjZjFjODBlZjdiZDIxZmJkOWJhMzk5ZTk3NmMwYWM5ZTI3Yzc2ODAyM2RjYTE5OGYxNWM4MzU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15252523\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2007734742 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0M1K0sdNadPD2LVrnt8LmBw227nA9F1U5e3ROaJK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2007734742\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2046314996 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNTdGRrQklBTFAzeTRrRnF1ZGRJMEE9PSIsInZhbHVlIjoicmFBMmNlVkZvOWprbEtTejV6UEhrbE5FWG5CWVZEdWJ4bVNMLzdvR2M5ek9CWm9RMlBQL3VwMEFEUHNOZ1U5YmFwazJNcWo3VHQ0YjdxdXFTWTlqWitHR3RSUy9NZzRKdmdmZ0NjZkIvdkhvVlkyNXAyaTcxekVuWTBVNTEwQWQwQkxDQk1TY1BKQWp6UWVwNTlzVDZZTjMrMTk1dStXRmplR3FhdHFSR3ZWbVZOSWRBNWFqRkhNbVZSQndkMnd5RGwraUlnbm93VE96R0Z4M2RyblRXNjlyVW9qVmtNdXNhNm11c2RrTUJtVW1IK3BzeVJ6Rkd2Q2xRLzcvbmFYeHlVaU95WXhDVnpDZDdmaXVCTXZRbDVBei9xVHcwamhVeXRXUEtvVmFENm1rRlhWdW9vVGFqWW93UDJjeXIwZjZiQnRPS3JodWVjZU02QzRQL25rb1AyYy9kNmZFRlUzMGdEdnRPcWpGN0xmUVdTaVZuaUNZeTZ2ZzZEV3c1Mkovb21MbDJPZXNMazF2V3FIYU84NXljam9ESHQwMnRESW5sZ2lBNENQeklUSEZVdklaL0RsVUQ0YnN0b0UxMDFYcVpFdnhlcTk3TndZVUZHOUVMVUhpK3NKRjZXNFhlYTBHTEUzTWx1Ly9TRzd3dWtVNVJtT1puemZyS3ArejA5dTEiLCJtYWMiOiI0ZTA1MjgxYmJjZDc1YjVkNGU3MGQ5NjIwYmY0MTlhNWQzODQ2ZWExODc5NWVmMWY5YTc4YjFkZDIxNmViNTgxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImQ5c1dXdm5rOElhaU4xclk4MmtRTEE9PSIsInZhbHVlIjoiWlByREFOYmNOQThHaTNWdmJKbVA3QUJJeHdkcGRWU21IK2NRbHdNRGpINmw1Um9FRVhxdTVGY084aW80VGtKT0tpalZTaE1SbEo5QU9TMndyNnF4ZmVtbTJ5WXZkNzJXV2JkSEJyeCtad1krTm1SWHV1Uk1Nd2FvaGVyRlZ3dGt4U2JzMlFsbndjYU5qakM4NXl4ZWErdHZYQzZhcW45OEFIWWZxcFBBOTkzb0FTUVVIRWtWYU0wbGJrV0pDMllMRVd5Vi92OXFsOE1EVFdXWk1vZjdtZWVlTGR6TU1yTEZnUnhudVROcG1aWW1xWGQ4YjVMUTNEQ05oMmI4dFpNeHprSmlvZGFWeUx2NEVVQU5lQXNacU5XUms4dXhCclI2cExwY0Nrbldmdng5ZWp3K3djeDJXQUdwdEU4R25uYTJRbGNOOEdoYjJ0M2RmKzlobmpwbXlIL1V4RlFXdW43bDNuWlJjWXNydHM4VHVqUWcyeVNVVzJtbkZPVm1sd1NQUU9pNjVjeE5nREN0VmVHa0tUanV5ZDl2MVFaMkJFaHlZeTZ3UlNOYnQ4RFFGYkY3VkJFb2dFZURyM0I1THN4ajNFRkpWZjRUbGRSSzVrUG8renBBSDFydkVzUnVFZGZJVWp4NjVIS080OEZNdEpRNEJ1enpVUEpXUFUzZ3JLTjIiLCJtYWMiOiJmYTllNTA0ZGU5MzlhOGViZTU2ZmQ4Y2VhMjJkOWUyMmYzMDQ5ODViYzdlMTljMmRmMDNhOTM0NzU3MTZiZjAyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNTdGRrQklBTFAzeTRrRnF1ZGRJMEE9PSIsInZhbHVlIjoicmFBMmNlVkZvOWprbEtTejV6UEhrbE5FWG5CWVZEdWJ4bVNMLzdvR2M5ek9CWm9RMlBQL3VwMEFEUHNOZ1U5YmFwazJNcWo3VHQ0YjdxdXFTWTlqWitHR3RSUy9NZzRKdmdmZ0NjZkIvdkhvVlkyNXAyaTcxekVuWTBVNTEwQWQwQkxDQk1TY1BKQWp6UWVwNTlzVDZZTjMrMTk1dStXRmplR3FhdHFSR3ZWbVZOSWRBNWFqRkhNbVZSQndkMnd5RGwraUlnbm93VE96R0Z4M2RyblRXNjlyVW9qVmtNdXNhNm11c2RrTUJtVW1IK3BzeVJ6Rkd2Q2xRLzcvbmFYeHlVaU95WXhDVnpDZDdmaXVCTXZRbDVBei9xVHcwamhVeXRXUEtvVmFENm1rRlhWdW9vVGFqWW93UDJjeXIwZjZiQnRPS3JodWVjZU02QzRQL25rb1AyYy9kNmZFRlUzMGdEdnRPcWpGN0xmUVdTaVZuaUNZeTZ2ZzZEV3c1Mkovb21MbDJPZXNMazF2V3FIYU84NXljam9ESHQwMnRESW5sZ2lBNENQeklUSEZVdklaL0RsVUQ0YnN0b0UxMDFYcVpFdnhlcTk3TndZVUZHOUVMVUhpK3NKRjZXNFhlYTBHTEUzTWx1Ly9TRzd3dWtVNVJtT1puemZyS3ArejA5dTEiLCJtYWMiOiI0ZTA1MjgxYmJjZDc1YjVkNGU3MGQ5NjIwYmY0MTlhNWQzODQ2ZWExODc5NWVmMWY5YTc4YjFkZDIxNmViNTgxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImQ5c1dXdm5rOElhaU4xclk4MmtRTEE9PSIsInZhbHVlIjoiWlByREFOYmNOQThHaTNWdmJKbVA3QUJJeHdkcGRWU21IK2NRbHdNRGpINmw1Um9FRVhxdTVGY084aW80VGtKT0tpalZTaE1SbEo5QU9TMndyNnF4ZmVtbTJ5WXZkNzJXV2JkSEJyeCtad1krTm1SWHV1Uk1Nd2FvaGVyRlZ3dGt4U2JzMlFsbndjYU5qakM4NXl4ZWErdHZYQzZhcW45OEFIWWZxcFBBOTkzb0FTUVVIRWtWYU0wbGJrV0pDMllMRVd5Vi92OXFsOE1EVFdXWk1vZjdtZWVlTGR6TU1yTEZnUnhudVROcG1aWW1xWGQ4YjVMUTNEQ05oMmI4dFpNeHprSmlvZGFWeUx2NEVVQU5lQXNacU5XUms4dXhCclI2cExwY0Nrbldmdng5ZWp3K3djeDJXQUdwdEU4R25uYTJRbGNOOEdoYjJ0M2RmKzlobmpwbXlIL1V4RlFXdW43bDNuWlJjWXNydHM4VHVqUWcyeVNVVzJtbkZPVm1sd1NQUU9pNjVjeE5nREN0VmVHa0tUanV5ZDl2MVFaMkJFaHlZeTZ3UlNOYnQ4RFFGYkY3VkJFb2dFZURyM0I1THN4ajNFRkpWZjRUbGRSSzVrUG8renBBSDFydkVzUnVFZGZJVWp4NjVIS080OEZNdEpRNEJ1enpVUEpXUFUzZ3JLTjIiLCJtYWMiOiJmYTllNTA0ZGU5MzlhOGViZTU2ZmQ4Y2VhMjJkOWUyMmYzMDQ5ODViYzdlMTljMmRmMDNhOTM0NzU3MTZiZjAyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2046314996\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1189204895 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/branch-cash-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1189204895\", {\"maxDepth\":0})</script>\n"}}