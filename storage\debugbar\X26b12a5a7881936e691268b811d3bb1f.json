{"__meta": {"id": "X26b12a5a7881936e691268b811d3bb1f", "datetime": "2025-06-30 22:40:46", "utime": 1751323246.007647, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.565478, "end": 1751323246.007671, "duration": 0.44219303131103516, "duration_str": "442ms", "measures": [{"label": "Booting", "start": **********.565478, "relative_start": 0, "end": **********.940644, "relative_end": **********.940644, "duration": 0.3751659393310547, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.940655, "relative_start": 0.37517690658569336, "end": 1751323246.007674, "relative_end": 2.86102294921875e-06, "duration": 0.06701898574829102, "duration_str": "67.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45723808, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0036799999999999997, "accumulated_duration_str": "3.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9725778, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.207}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9847312, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.207, "width_percent": 17.12}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.992438, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.326, "width_percent": 14.674}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-406212588 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323149690%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRFWndDbmNtMTJwRkp0ZlhrRTRwTEE9PSIsInZhbHVlIjoieDhld3RBaTdsY2llckNVRCtvSWpXRDFnQU91emdOWjBUVm4zZWlUb3BEeS9CVWhvZ0JscGo5VFd3Rlg1cTFKWURVdEhtYklJaFd2aWJibUtONEovbVNqSGF4c0NET2R2RVl0cmx1aUJlSzdicHJ3dTdjcnRwT3IvUnFNdVFjZk9ramZkRExjTlBWazdDT3dtTTd6alhkb2ZkbUdid0RJWXErYVRPZ3FUc2FIY0xuOXNjaytSNENsNzVtd0NrUU5hOTJIendnUjdGRW9tNnN1b29ULzV2VlJTMzB4c1d6MkFySXJoNkp6RXR3enR1RUZDTG1ZVFUzYzdaT09zeitkbnhkTjd1M3VaZ3E0UVRUNWZpQ2ZRelh1RXFjYUIxd1FRblVZWE9zc01mM2JkNCtLcGJ1Qnk5djdaV0RrL0pOT2U3RVRsSndOVFBWcjc1MEdRSjZGZTUramw4UElNVWIvL0VhN2lkTFFkR0ZTdkw3ejl0Z0duejl1YlBvMVJMQjdVM3YxWWY1Q0xPWE1NMmF5RHVOUmtjSk1QUlhTM09mbk1URi9KSUZacEs2QTByM1dSRVR2OW1NY0lUU2cxV1JxSFBlOHQ0c3ZVSys5bGVuQXBCdWRoekdWUWxQcXpZT1RKeHpEbGN0RHVyNUZWVzYxNk9hOUVySDlKcTFzRTVPcW4iLCJtYWMiOiJjOTUzMjk2YjIxMmE2NTdjNWUzODE3ZDFjNGQ5MzNjNjFmOTZmNjI2ZDBmZTc1NzU2NDI3NzE0MjBiZTNhMzg3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkJydC9Vb01uc25xVVVOZGN5YXJOb3c9PSIsInZhbHVlIjoiN2d4ZXIwbDQ5OWVhbFNwKy9RcVIzTmpxV3BhcGtwaEZoTHVXMlR6YS8zSDRQRUFWSXg2OGlHNzZYTXFhSWNhVWRUZkJiWEZoNEZTSlJBYmtFeXc1NWM4YkJsS1JLK0hIY1Nld3VIb3UrUndYMGFhY0tGdG1EYUpPLzFVWk9MRHdaeHFnQXNhbnFoc1BxcExxVVBXRHc1eU8wbjU3NXYxc1dyN2Z1WjNjN1lMaVBhOU54cnVRUHF2RW52UXEya0VkUmdTNERiZFlMTFJMbVF4dXkwUFFUMHM2dG5UTHhOUS9WVDI4YlV0WjlhLy9JQW8zUUVWRk9KdzRVK2tYSzJFSW92L2hnL0o2eGx4UGZNaEcyUGcyY2Irc0swVFdjakRzRlh5NzhjVkRzK3dzSDgxUzdXQzJjaW94V0F6WjZLSjdnUHRKUnhQVlRndUk3Y3FNb0tSUUZ6NXJ4V3dsc2RiQk1KVEs1S01udlA2WVJRWkhyZzl5aWRVNXpMYnc3NUxwUWRoNXRkMUNUVDBxWVhraWtldnM0WExZYUtLTDNyVjJpTC9lenk5Wjh6TnREMHhhK1B6Rm5zRkNJZVRiVlRIMWFhOU1DRHNBZWZ1cEx1cGlVUjh3MmpEU1IzUC9yRmZEZTEvUFR2S3N6ejY0M2d4S1QzSFBnSmxWWk95RkVQUk4iLCJtYWMiOiIzZDhhMTE3ZmRiMTQwYzZiOTI1MDIwMTJiOTFiZmYyMzExMDIxZGExNWQ1MTc1NGRlMGY1MTA4NDQwMmUwNjA2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-406212588\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-47654644 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-47654644\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1759041461 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:40:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZKYlFZSHRmSWtYcGtZZ3pLOEhnZnc9PSIsInZhbHVlIjoidExqUmhWVzlReFNRV2UvbHZsSmdROXhueWVlK0w3VGd3V0JZTkloMlpRWkJrUXpDc3M0VmU3bzliV0lpQU9ubExSUjNQK3draHlkQ1NOR2JWNzB5djRYdWpBM2NZY0xGckRydVJvNk05bGhwMjR3M1hQYTNFZTY4aURrWXJ1L2tYcFhlWmw4WHZTYkVrdU5BUjZNSkNHUmNBbjdocVczWkdwS3hLVmFQWGthWmhkNG5hUUkxRVZmcFZCR1NTRFhxb3pXNUZ2dC9GQm1DblJxQXFMZmhvd2NRREE1aHJKbEdlNjY5SnIrNzNwMHp5K3hpVzJ2ZXVhdHhUSFFCbjVqQzU0VkdBTk5wK2lEandwRTd4UDRVY2s3ZG1sbDVlaEIwcXo5UStGOENpY29GQkp6cFhrVjhKRmpLb1VNZ3BxOUl0cXNZTWkvRlU5YjRQYzhOVkhZTnVBTUlKVEF2STc2eUtLTWlDaFJURVBBbGJWblJSaDFneitBbC9NWjZLazFKdGdiVVdoMXZCZllGYmEyR1lJUVJhQzZkcDg0Y0UvMnRKOGgxSW1tQ2RyMlBQZ3pzL3B1bEpiblJYMmoyMTd0SWprcklyU1BFSE5QS2sxN0F5c00vNnl5MWtIL21VSXdyRDljeHBkQUZ4NWZiY2dUbjNXUVpaNkpsTnZ2ZFhQdUciLCJtYWMiOiI0YmE0YjkxYjE0YzQxMjY2Y2Q5OTBjZmQ3MzY4NDAyMzk5ZjFjYjE2NmZjYTM1MzlmOTA1YmRjYmNkNWQ3ZDAwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJ6dHE0YUw0eWZSS3NoV2NoUHpFK1E9PSIsInZhbHVlIjoieEd0OW5KWS84cUZLK0dQK2dTakQvNk9NYzBRRGtVcjJtMWcwTllmeVI1SXJVcS9HbVRUMU5PNzlvM3ZFd2VSYWdvUVZPdVRNRHRvTk5WSFdQekptbFRqaDYzRmZLQVk2SEdTaHB0aFk3WFBFRGxJNU0wZVVlUlBRS2xscVM2c0NRb3krcnJ1WUlOK0x4eUdxOWxSaUNkMktkQmlNbUYwakxTU29xZnJrNEY1eWxYV2ZMYlV1NzN5emlTR0xRVFBDaWdMTmR3cjZKV2o3L0U0MVNYZW1kZ2xYcGtNUWo5M1R5WFA5QzFiZkNiZ2E2M3Q5U3hhY1dtZ2dxa0Erb0psMUNzd01KOHpEb2VRdmFRT0FDbTh5ZDNOVnd0VFVwR1hQRjlxK0dVbDRybXZNUVBxTS9ZMnI1RnhOakRadTdGN3k0ZVRjckdVbXg2TDRjT2k5NTU3RzA2QlJNSWxMRzZXbWhiRUsvVUFyVVB0VnhCOG4xcGhZTEFRcDVSYkJ3TWwrMWp6QkMzM3pvM244b1BIQ3NFeTdnY0pxOVhUbmwzVFZRbE9wL214NUMrazFWVE1Pb3pUbVBteUVtTmR5Ykd6TysvV21ONDkrYktIb1R3ckcyTG9jVy9kS0RQSFgvKzdzWm1CQWhCdFVpYjJ6VkM4Q3I2cVVwVTE4NDZ2NEdPa0siLCJtYWMiOiI4MDFhZDAyYzA5ODMzYTNlYjZlNDUwNjBlNTA1ZjA0NzNlZTBjNDMwNTUxYzAzNTdmZTY3N2QyYjUyNzFjYTYzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZKYlFZSHRmSWtYcGtZZ3pLOEhnZnc9PSIsInZhbHVlIjoidExqUmhWVzlReFNRV2UvbHZsSmdROXhueWVlK0w3VGd3V0JZTkloMlpRWkJrUXpDc3M0VmU3bzliV0lpQU9ubExSUjNQK3draHlkQ1NOR2JWNzB5djRYdWpBM2NZY0xGckRydVJvNk05bGhwMjR3M1hQYTNFZTY4aURrWXJ1L2tYcFhlWmw4WHZTYkVrdU5BUjZNSkNHUmNBbjdocVczWkdwS3hLVmFQWGthWmhkNG5hUUkxRVZmcFZCR1NTRFhxb3pXNUZ2dC9GQm1DblJxQXFMZmhvd2NRREE1aHJKbEdlNjY5SnIrNzNwMHp5K3hpVzJ2ZXVhdHhUSFFCbjVqQzU0VkdBTk5wK2lEandwRTd4UDRVY2s3ZG1sbDVlaEIwcXo5UStGOENpY29GQkp6cFhrVjhKRmpLb1VNZ3BxOUl0cXNZTWkvRlU5YjRQYzhOVkhZTnVBTUlKVEF2STc2eUtLTWlDaFJURVBBbGJWblJSaDFneitBbC9NWjZLazFKdGdiVVdoMXZCZllGYmEyR1lJUVJhQzZkcDg0Y0UvMnRKOGgxSW1tQ2RyMlBQZ3pzL3B1bEpiblJYMmoyMTd0SWprcklyU1BFSE5QS2sxN0F5c00vNnl5MWtIL21VSXdyRDljeHBkQUZ4NWZiY2dUbjNXUVpaNkpsTnZ2ZFhQdUciLCJtYWMiOiI0YmE0YjkxYjE0YzQxMjY2Y2Q5OTBjZmQ3MzY4NDAyMzk5ZjFjYjE2NmZjYTM1MzlmOTA1YmRjYmNkNWQ3ZDAwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJ6dHE0YUw0eWZSS3NoV2NoUHpFK1E9PSIsInZhbHVlIjoieEd0OW5KWS84cUZLK0dQK2dTakQvNk9NYzBRRGtVcjJtMWcwTllmeVI1SXJVcS9HbVRUMU5PNzlvM3ZFd2VSYWdvUVZPdVRNRHRvTk5WSFdQekptbFRqaDYzRmZLQVk2SEdTaHB0aFk3WFBFRGxJNU0wZVVlUlBRS2xscVM2c0NRb3krcnJ1WUlOK0x4eUdxOWxSaUNkMktkQmlNbUYwakxTU29xZnJrNEY1eWxYV2ZMYlV1NzN5emlTR0xRVFBDaWdMTmR3cjZKV2o3L0U0MVNYZW1kZ2xYcGtNUWo5M1R5WFA5QzFiZkNiZ2E2M3Q5U3hhY1dtZ2dxa0Erb0psMUNzd01KOHpEb2VRdmFRT0FDbTh5ZDNOVnd0VFVwR1hQRjlxK0dVbDRybXZNUVBxTS9ZMnI1RnhOakRadTdGN3k0ZVRjckdVbXg2TDRjT2k5NTU3RzA2QlJNSWxMRzZXbWhiRUsvVUFyVVB0VnhCOG4xcGhZTEFRcDVSYkJ3TWwrMWp6QkMzM3pvM244b1BIQ3NFeTdnY0pxOVhUbmwzVFZRbE9wL214NUMrazFWVE1Pb3pUbVBteUVtTmR5Ykd6TysvV21ONDkrYktIb1R3ckcyTG9jVy9kS0RQSFgvKzdzWm1CQWhCdFVpYjJ6VkM4Q3I2cVVwVTE4NDZ2NEdPa0siLCJtYWMiOiI4MDFhZDAyYzA5ODMzYTNlYjZlNDUwNjBlNTA1ZjA0NzNlZTBjNDMwNTUxYzAzNTdmZTY3N2QyYjUyNzFjYTYzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1759041461\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}