{"__meta": {"id": "X076e9fc2a31f54402bba3619b1e9c125", "datetime": "2025-06-30 22:42:23", "utime": **********.812097, "method": "PATCH", "uri": "/update-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.367636, "end": **********.812112, "duration": 0.4444761276245117, "duration_str": "444ms", "measures": [{"label": "Booting", "start": **********.367636, "relative_start": 0, "end": **********.745949, "relative_end": **********.745949, "duration": 0.3783130645751953, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.745959, "relative_start": 0.3783230781555176, "end": **********.812114, "relative_end": 1.9073486328125e-06, "duration": 0.06615495681762695, "duration_str": "66.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47533216, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PATCH update-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@updateCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1570\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1570-1633</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0032899999999999995, "accumulated_duration_str": "3.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.775174, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 55.319}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.785428, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 55.319, "width_percent": 14.286}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.7990148, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 69.605, "width_percent": 17.933}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.801293, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.538, "width_percent": 12.462}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1195882711 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1195882711\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.805034, "xdebug_link": null}]}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:3 [\n  2353 => array:9 [\n    \"name\" => \"مفتاح علبه\"\n    \"quantity\" => \"1\"\n    \"price\" => \"6.00\"\n    \"id\" => \"2353\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2354 => array:8 [\n    \"name\" => \"العاب شكل بطه\"\n    \"quantity\" => \"1\"\n    \"price\" => \"6.50\"\n    \"tax\" => 0\n    \"subtotal\" => 6.5\n    \"id\" => \"2354\"\n    \"originalquantity\" => 10\n    \"product_tax\" => \"-\"\n  ]\n  2355 => array:8 [\n    \"name\" => \"العاب اطفال\"\n    \"quantity\" => 1\n    \"price\" => \"6.50\"\n    \"tax\" => 0\n    \"subtotal\" => 6.5\n    \"id\" => \"2355\"\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/update-cart", "status_code": "<pre class=sf-dump id=sf-dump-1714729094 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1714729094\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1979414648 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2354</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>discount</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1979414648\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-319896930 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323255132%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Inljci9aZ2VCY0xSNGJxZTRmWkJSalE9PSIsInZhbHVlIjoidXVwTEZidDczT09xWVBuZFhqenZFVU1RNUhlSThkN1VHVVpXTDNpWXpZMGhyWlk3Z0ZXZ3RvSGR4K0ZYcGR0UnpSTzhUUWFzQ1kzejMxZE1RRDNlVm5DbTVhNzltalhoQ0tOc2JUVytIZmNhZ0xadUk4cEhMeVJPWGFXbE54Y3Z2V1BMK1I2eTVPNURGcVF3Tmh1R2c1eUdIaTFQTWtJUGJEdTVHcnkxVkN5UFRubEZyYVhMTW5xdlIxbUlGZWNQOTRIZXBTMnBEdGUvMjJKYVczdnFwMkRqNi90anpKRDlkTUJUaUhhcEQycG5lTWFFWTk2T1NmdysxcW81ejdVbEhpYnN3cnQ3eWhSd3lzQys5WWFId0NOWjNQRDN3SnlRY3g2TUtZN3dyMCtVN05oY2dLdjRWcjljUWRRMW11bzdFYWd2ZTNMTjdxZ0N4dFNNY1BibVNCUmw5WE1zaFhLUzViV2VEb3RnUUFmSGcyRUd2TzFRVFMyY2dZdU11WU05Y3I0NDJRRllweTdFMmd1aGpldE9FNmN1a0RPVmF4bDNaaW10Z2JidGE1ckxqN25xdFJQaWY1Q01NZHFtbzhuSyt6cWFqaUJjWUNqQi9nQzBVODBOSTVST1BZM2lEUlFNTkwrT1BMUmlkUUVYUzhvL1M5bFpOZWVhTXBUQjkrMGwiLCJtYWMiOiI2MjYzMzA0YTI5NjI3ZWYxMDE0MTNiNWVjZTZhMjc0NDQ3ODYxYzVjZmZmMTFlNGNiZTE2MGZkNTExYTM0ZjU4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InhISG9xeU5VUU9OVXpISjdURU1WbVE9PSIsInZhbHVlIjoicWdFOGhYUElhNmVXT0NWQjB4bEFQWDZ2RFNEUkNIbzBZQXUrOTF4OW44MXRtd2lYZ2J3MmtZbzdkTzBxS25JUjREWnhPUmMwaHFScTJjR1NuSzR4VTZQbjhFb2tKWTAyMFovaXM2YzlNL3huMm9PU2dseWpUbmphUmZyRXlzUHo2bVJJcURFVXQ1R3h2WWlpR2JxVXlPKy94MHB3bFJpa0Mzb2U1SXdmV3VPWmZlQVNDdmM4cUFyckdhUXhSclBFc0M5dGNkY00wM2h6Vlk4WjFPTmV1UnhpYmtZd3R6TTZ2bGF6eEp6K0s4dzhRWG5pS1VKZGsvQkN1b0MrUWNPYXZ6OHU5WjdRQk9SUHUxSkFmRWZ5STY1eUtOV3g4dW5yYVZTeDM0STlkMkRUaUFTSFNjYkVMRnhYRXdMdEhDT0dTMGVVbjJQQ0hGck43b0Z5SHdEUUw5aXc2bUxGUitKWkJaeGNhOG5GNjY1TS9IcXo2bDUyVVZWRUJ0UmxBTjh2RU5xRUFsbU00WWMxWitOT04zMWFJbjdTNmtocFFac2czQjBvaHFjVTc1RHRuam5jWTNlYkJTVFZJSDNqK2J2RDJVRnpraFpWVG1VSGphcTh0OXlTT2lONUNnYWpHN21rMmF3NmUxTGd5eTVSU3FvSWorU0wycHNsOEFDN0tEb2siLCJtYWMiOiJhOTE4M2ZjMTZjYmI2OWI2OWNjZTIwZWEzNzhkMzRkN2M3ODQ2ZmNhNDIzY2ViNWIxMTc3NGNiNWFkZTFjOGY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-319896930\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1908064702 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1908064702\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-275418431 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:42:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iko2WkM3WklETmhML1NqUWduRStMMmc9PSIsInZhbHVlIjoiaHFZcjNCWE1kV084QmxXSDVmRFZEMFU1QjZUVlh5N01aQzRQLzY3OUxxL053ME9ROWd0VjNRaExiUlNhWk4xUEc3QmZmYjVMNko5YU1XSXFGdFQxek9xM21TcFEzMHlpd1g2Q1BWckgranhia216Zm9ZcXN1YzM3NXRXTkRWaHZnRW0wS2dsZlVRRDBXZncxM2lTUisvMlJ1WFFuN3lVQ2dZWlc3R0QvRlUvRXUxRk9UVjZBWGpiSHJhcUhLODF5alhxZ0d0WXZnWFJkdzVZcHlhOVYzMmJENW5aTXBDbXNHdmZTTGtEU2paNm9GUmc5MmZmYXFFbVRyRUdMWnJFNUNXUUFTQnJEOVNZeU51RXUwUEl0TGtYT2tBSHhZd3M2eFloOXB1bS9FbHdlODNQMHlqalV0ZVJHQnY4V2lRc1lTY1VQTUJJWWhMSTVkWXdwVGtFT2JqQ0czU2lUdGVLR2JlWERqTE5sZUxKWkJUb3FTUDR4NXlzelFsTnRYdW1DaVBKQWRuU3BTeW44eDJCV1hyYmdPbjg1cG9Uc2ZqUHc1UGNUMVdnTk5Rc3Zxd1JUZGVWbG5ZYkJJWXNGY2JuTnU3UklackJzMzQzNE5ISXBjOFVzRXk2a2JOMXc4WFNaU0hQNUxBUGNtcU1qVVM2WkIzb3dEb0FwcWxtV043YmciLCJtYWMiOiJkMjg2NmFkZWRjYTYyNDkxYWZmNGZlNDc1MDJlN2U5Njk2OGRiOWIzMGUwZjExNjdlNGVkZTBkYzdkOGZmYWU3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:42:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InhLRm5BY2FIMnVLSzd3UTU0SlVNR1E9PSIsInZhbHVlIjoiMGt3UU50UVVORXBVSUpMdTFGc1FvcXF1RVc0QUl5RGFkUUZWaGlnQjQzc01NZzF4MDVRVHhpSG41ZWxkbkpEbTJEclI3UFUvQ2JWMHNGd0E1WlV6VzZESE92OHVUMTBaS2tteEtaNGtONU0waHZNL1prN1JBOHc5c3NVZHVZVWprSnN5SlF4ZC9HeUk2YXNld3htQWhhTW0zSUJNQXRIMmphbjV2emdRQmNwOE4rbG0zMlVaSnBzWDNkbWZrUTUyTCtZeVg3SHYwYlZsZ3dpSUVkWFgxTTRERnZYbXBBU2llR25PZEdZQjRBTHFzelMzbEhCMHRLTzRZUjBDZzRrZUpEL2ZVRXlsdnZ6NUtHb21sejhUeFV6eTNZWFhpV0RzVHpsT1lOQTNmd3hqcWgvR3JnTU50dzhIM2paSzI3YWVRUm5vaTRJUEZWaHdoTmV3TnFNa1dRSXRZdUlUeW9jczl1dURDQWZJQ0NlV010Z2tibURyRDhnU0hnVW55ckJBNzF5Q0pmOGZQR01IcERTSnpPdjAxRkFuYzEvT2cyRDBNSHo0WUE4QWVVQ09hY0VGTVZqMmdPdmIzeHNVQ3hLbHdBZElSalRVVjJ2c1IwTTIvdkQzcXJBNTY0VGJnVXRIWFVnYWUyQzd1dG5WNjJvcTVJMHY3UlVsUmZWMzUrY3oiLCJtYWMiOiIyM2YwMWNlMTQzNzE3MDk3OGMxZmE2MDI4Y2QyOGY3YzkyYmYyODkxYmNiYzU3NGY5NjI0YjBlYWQwOTlhNDE2IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:42:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iko2WkM3WklETmhML1NqUWduRStMMmc9PSIsInZhbHVlIjoiaHFZcjNCWE1kV084QmxXSDVmRFZEMFU1QjZUVlh5N01aQzRQLzY3OUxxL053ME9ROWd0VjNRaExiUlNhWk4xUEc3QmZmYjVMNko5YU1XSXFGdFQxek9xM21TcFEzMHlpd1g2Q1BWckgranhia216Zm9ZcXN1YzM3NXRXTkRWaHZnRW0wS2dsZlVRRDBXZncxM2lTUisvMlJ1WFFuN3lVQ2dZWlc3R0QvRlUvRXUxRk9UVjZBWGpiSHJhcUhLODF5alhxZ0d0WXZnWFJkdzVZcHlhOVYzMmJENW5aTXBDbXNHdmZTTGtEU2paNm9GUmc5MmZmYXFFbVRyRUdMWnJFNUNXUUFTQnJEOVNZeU51RXUwUEl0TGtYT2tBSHhZd3M2eFloOXB1bS9FbHdlODNQMHlqalV0ZVJHQnY4V2lRc1lTY1VQTUJJWWhMSTVkWXdwVGtFT2JqQ0czU2lUdGVLR2JlWERqTE5sZUxKWkJUb3FTUDR4NXlzelFsTnRYdW1DaVBKQWRuU3BTeW44eDJCV1hyYmdPbjg1cG9Uc2ZqUHc1UGNUMVdnTk5Rc3Zxd1JUZGVWbG5ZYkJJWXNGY2JuTnU3UklackJzMzQzNE5ISXBjOFVzRXk2a2JOMXc4WFNaU0hQNUxBUGNtcU1qVVM2WkIzb3dEb0FwcWxtV043YmciLCJtYWMiOiJkMjg2NmFkZWRjYTYyNDkxYWZmNGZlNDc1MDJlN2U5Njk2OGRiOWIzMGUwZjExNjdlNGVkZTBkYzdkOGZmYWU3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:42:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InhLRm5BY2FIMnVLSzd3UTU0SlVNR1E9PSIsInZhbHVlIjoiMGt3UU50UVVORXBVSUpMdTFGc1FvcXF1RVc0QUl5RGFkUUZWaGlnQjQzc01NZzF4MDVRVHhpSG41ZWxkbkpEbTJEclI3UFUvQ2JWMHNGd0E1WlV6VzZESE92OHVUMTBaS2tteEtaNGtONU0waHZNL1prN1JBOHc5c3NVZHVZVWprSnN5SlF4ZC9HeUk2YXNld3htQWhhTW0zSUJNQXRIMmphbjV2emdRQmNwOE4rbG0zMlVaSnBzWDNkbWZrUTUyTCtZeVg3SHYwYlZsZ3dpSUVkWFgxTTRERnZYbXBBU2llR25PZEdZQjRBTHFzelMzbEhCMHRLTzRZUjBDZzRrZUpEL2ZVRXlsdnZ6NUtHb21sejhUeFV6eTNZWFhpV0RzVHpsT1lOQTNmd3hqcWgvR3JnTU50dzhIM2paSzI3YWVRUm5vaTRJUEZWaHdoTmV3TnFNa1dRSXRZdUlUeW9jczl1dURDQWZJQ0NlV010Z2tibURyRDhnU0hnVW55ckJBNzF5Q0pmOGZQR01IcERTSnpPdjAxRkFuYzEvT2cyRDBNSHo0WUE4QWVVQ09hY0VGTVZqMmdPdmIzeHNVQ3hLbHdBZElSalRVVjJ2c1IwTTIvdkQzcXJBNTY0VGJnVXRIWFVnYWUyQzd1dG5WNjJvcTVJMHY3UlVsUmZWMzUrY3oiLCJtYWMiOiIyM2YwMWNlMTQzNzE3MDk3OGMxZmE2MDI4Y2QyOGY3YzkyYmYyODkxYmNiYzU3NGY5NjI0YjBlYWQwOTlhNDE2IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:42:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-275418431\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2027669709 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2353</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1605;&#1601;&#1578;&#1575;&#1581; &#1593;&#1604;&#1576;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2353</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2354</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1588;&#1603;&#1604; &#1576;&#1591;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2354</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2355</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1575;&#1591;&#1601;&#1575;&#1604;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2355</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2027669709\", {\"maxDepth\":0})</script>\n"}}