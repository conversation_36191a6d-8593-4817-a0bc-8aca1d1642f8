{"__meta": {"id": "Xb28b96ebf12b83ace8111dd233281944", "datetime": "2025-06-30 22:41:45", "utime": **********.33977, "method": "GET", "uri": "/pos-payment-type?vc_name=10&user_id=&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323304.873922, "end": **********.339783, "duration": 0.46586084365844727, "duration_str": "466ms", "measures": [{"label": "Booting", "start": 1751323304.873922, "relative_start": 0, "end": **********.237486, "relative_end": **********.237486, "duration": 0.36356377601623535, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.237497, "relative_start": 0.3635749816894531, "end": **********.339785, "relative_end": 2.1457672119140625e-06, "duration": 0.10228800773620605, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53421616, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.bill_type", "param_count": null, "params": [], "start": **********.335248, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\resources\\views/pos/bill_type.blade.phppos.bill_type", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fresources%2Fviews%2Fpos%2Fbill_type.blade.php&line=1", "ajax": false, "filename": "bill_type.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.bill_type"}]}, "route": {"uri": "GET pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@posBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1416\" onclick=\"\">app/Http/Controllers/PosController.php:1416-1524</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.00532, "accumulated_duration_str": "5.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.272341, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 33.647}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.282752, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 33.647, "width_percent": 9.023}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.297198, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 42.669, "width_percent": 12.03}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.299726, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 54.699, "width_percent": 8.459}, {"sql": "select * from `customers` where `name` = '10' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["10", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\PosController.php", "line": 1427}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.305191, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1427", "source": "app/Http/Controllers/PosController.php:1427", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1427", "ajax": false, "filename": "PosController.php", "line": "1427"}, "connection": "kdmkjkqknb", "start_percent": 63.158, "width_percent": 12.97}, {"sql": "select * from `warehouses` where `id` = '8' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\PosController.php", "line": 1428}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.30812, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1428", "source": "app/Http/Controllers/PosController.php:1428", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1428", "ajax": false, "filename": "PosController.php", "line": "1428"}, "connection": "kdmkjkqknb", "start_percent": 76.128, "width_percent": 7.143}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\PosController.php", "line": 523}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\PosController.php", "line": 1432}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3112228, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "PosController.php:523", "source": "app/Http/Controllers/PosController.php:523", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FPosController.php&line=523", "ajax": false, "filename": "PosController.php", "line": "523"}, "connection": "kdmkjkqknb", "start_percent": 83.271, "width_percent": 7.895}, {"sql": "select * from `pos` where `created_by` = 15 order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\PosController.php", "line": 1511}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.326039, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1511", "source": "app/Http/Controllers/PosController.php:1511", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1511", "ajax": false, "filename": "PosController.php", "line": "1511"}, "connection": "kdmkjkqknb", "start_percent": 91.165, "width_percent": 8.835}]}, "models": {"data": {"App\\Models\\Pos": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-466933363 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-466933363\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.303744, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-888418403 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-888418403\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.310553, "xdebug_link": null}]}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2352 => array:9 [\n    \"name\" => \"مجسم شخصيات مختلفه\"\n    \"quantity\" => \"1\"\n    \"price\" => \"5.75\"\n    \"id\" => \"2352\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 39\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-1479930412 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1479930412\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1837934672 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1837934672\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1588169786 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1588169786\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1460477811 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323255132%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFFd0VsMm9xdm1OelFGdWtWNlFZNFE9PSIsInZhbHVlIjoialFSZ1NUZDFidmZIdm5IM1dmTlp5dW03Z1BtVzRzajk1dE0wVjFZemdsNWZMVXNCVkVlSERSTmF1V2k1WW92U05tbXRrK2pqK1JRSnpnZXJSSW1udkhUQW8wVWtzQXlHM2FwcSt0MXN4MHBCWjVTNkYzMUMwSUNZajVxcnNKaXdPYnN4bUdmZ3FmOFAwVW5DakhiTHkwTDFlaEdVZGVBenVtUzMxWjZWZS9uajJ2N1M5aUg3dFFzNFl5ZDd1Nnl1YzJnSU9wNDIyYXYvZTdUaVQwNzM2b2IwNEpjQ3FOMENtanlwQVVCMWp0ckd5ekVveUI0TFNBejh1MUxSNVZVNVZXYzMveFh1emZwcjJwR00zSnZxc2JjN3hVdFR1cmd3L3lBeU1DcmQ4d1ljdDVLNlJFMzN3cjV6QVF2TWZ6RVJNeG9qMkZJc0hDRDBicXRnb0ZzN255ZzlGTjdmR040OEJTbXY2U1FudzBXREdvK3JVb3FJekF6bjBXbWcxMEJlMXI3MzI4djlOV0drU3N2MVRFd1ZUOXZsdHVYeWdBK0Iva2daUmZPZkpDaVQ0MFFUeXUxbWpRanQzYjNMcmt6NUxGQ1BxMERtVjEybXhMWmwzaDV5Y3hPdnRKL215WW1GWnV4dW9WbU9HNEFYQUNVMTJMbFB1RUFYZVRuM0hXTEEiLCJtYWMiOiJlYTNiZTQ3ZGIxM2Y1NGJmMGMyYWI5NzQ4OTdlY2ZlMTlkNGNhOTBkZThiNTRiOTJiYWUzZmUzYWNiNzA1YmUyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkFQQmhIeDlkT3hlTzRET1k0bEhOM2c9PSIsInZhbHVlIjoiTmlqcFFOb2Fxa0o4RlhuSzdhQ0h0NjM0TnNHVlRCOXgxYVdpanVxQU9LOERTbi9BbDN4NXFKK2xMVGtvYWRyV29PR0hGR1cweVF2YTZoUjliQXYyazk5elY2ME1pOXpMRlVBV1I5WkxUMktVOTBCaTdJUFQvelhnRHYvakZtMXdhc1JSd0w2NGYxUGlna2c3RUhMMkpnNHNmdUg5RGM4ZUNJV1VGNTN6QVJQV3hJeVdma29zejVFeStwOVh4UlFBcmpCdmhuaG8xOHlRZUNZWWtkSkJTMnFrUDcybEJmVkF1SURpbGs3ZnZuZ3hCc3FiQzhKK2ppR3NmSHl6OFRrK2NDUDVRdExFQXBQUmxqMWtrc0lHUDFqeVlmcWdtZXIxcm42QXBWRkpyNndZRGFnR3BWUDlXN3ZSQUZBbVRYNWZaKzNiUk5WcVdUNXAvTVY0U2M2b25IeHBnTjkwUFdUbkxJdzljYVU3dU1rQXozczRLMFdXUkZuMzh5c1RyWDhHQmd3WXZaMGp1c2dRL2Ixc29DY21PenFNdVdYWWNhYlZPYjZpcEc2OGlWRmdESUQ2dkQ2czNsOHBCYlEyaU1DS1lQZDZnenF3T0RIeVJnazhCSkdva2ZPbVpXa0JPNHdhRHZWSWpwNGhsOXVJMmppMGRQRkx0Qys4ZGwraWV1VEYiLCJtYWMiOiJiNDZkZTFkN2U4NTJjMjcyNjQ4ZmE1NWIyNmM2M2Q5NTQyY2Y1YzA4N2E3MmNhOGJhOTAzYzgyZmE5ZDE3OTViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1460477811\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2059785757 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2059785757\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2101319565 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:41:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndQNHJLZUlCR3NQWGdIRms3ak1qR3c9PSIsInZhbHVlIjoiaUhpY2M4Z2tCV3ljdGpQT0tZUVp3dGZMd25tMk1CbDE0RmRzTmlURlIvWXAzN0RjbE5lTkE1TmppeDlENE1pSkJXeGxoYm1YVGFhRkQxNmNtMlBDd09tNFlCRjNiQ29iRTlxZ01nRTUxdEg1akFCVkxidjNINGJYVnJGOFN2c1dYeEdyRisyV2ViSEtaRVpBYzA5U0xCaTJyMXBZaFlHdmRnQ3U5em9ubzdidjlaZGVMUlkzQnQ0MkR4Ly9tQzFJNkt4cnlyY2JPcVhab0kvREk0YkNXMS9uOXhIVFJ0UnBUYlBmcTVuU0lJZnUrWXQ0Vno3OXllZGRpTUxnWllRMjRidG9USEt1RndXNkpsRndEL1FrSEdtVEVRS0svZHBsTFd4MnlTNGdJanJ6ZnQwVXRXTnNSTEEyK24ycGtuR0NxL3dkZjR3Vkx0TTRtQWI0MElWTUIzdzErUUVQTUV0RFFjdDlNZEZNMHhGQThRd2prU0tkbXFLRnVEMUNsaVBwTkd5VldtSm5lRThGZUVmZUtWdXB6dk0wdHVHMTEyTVVRMGFqejlaVkt6bjNiTFk0SEtKZ0xreVMzaU9rckhFRmMzZlNtWG5sQWdYdUR4WTlOcUVneldReFpXWGJHNmtHMDJFMHdPRHk1S1pOZ3Vrak9sVGthOUhXTU53Nkh5bDQiLCJtYWMiOiI0MjBjMDVlZmE1YjQzZTc4N2ZjZDc1MTA5YmJkYWU1MTExNDEzZjBmZTc3NDEzNGE2YTE1YWYxY2YxMTZmNGRlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:41:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkxNVmtXWWZzeDcyWUZaTEt2UC8vL1E9PSIsInZhbHVlIjoiRWJ0S1pMSWdYSVVpYU5GZ2FCbHZzT1NsdXY0YVNsekE0QzREbDVSaER4VnI1a2p1TlI5aVBZZC9TZ1orQWZRTzJOWkVVWlpEaVFBZERmekZFcVNWOGhNelpGMmlRZm1SMVNlV1VLdkYrdUwxZjF0c0VaUkZtUE0wMm9FRWdmNHhVUnBiWXpIcmhFeXZwaCtnUzJ3ODhQOHd3L2RqbzVUcFJuVFdZdmtzU3o4d0RKRDFNYmJjRThSY0tpbCtSOTFsamJKYThITVhkQXhBTFg2ZEpIb29DN0tRZHYyd3hwdC9zb3dFRkE1V3lNTUlUSDFNRVZ2TVNuT1lVcnZ2UEV5enBHdEZ3Tml2SDZiejJmbjQwNHpoR2lNMFRTcGpWQjdObjJaeWhScXkrT1p3b0ZoL01STW9wNVRGa0o3elpKc2J5dWF4Mjd0Mzc1Q0NYWXJxcDQvOTRWRFNVTW12Z0tuaEQ4eHJzUXJ5dXI5eGk3Y1dtT1BkWGQrQnFGR1JuK09LbEphcGlkUTNodW9lZUVvMzF2aFN5Q0pOTWk4OSs2dUVQUWdubzA2dFBPVytjYW85UG9yY1gveW8rc3BkQ3NvRFFHUVhUeWR6bDJlZjc5SjdWcW1YT2Vocmx6dmo2Vnd2ek1nV3p3aVkwWHVWS3oxa0tRME5jcFR4UWdreTJnWEwiLCJtYWMiOiJmNTYzOTdhNDdiY2U4MDQ2NzRkODRmODc3Zjg2NjNiNWMzZTJjNDYxYWExNDcwMDA3N2Q3ZWYwMmQ5NDkzN2RmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:41:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndQNHJLZUlCR3NQWGdIRms3ak1qR3c9PSIsInZhbHVlIjoiaUhpY2M4Z2tCV3ljdGpQT0tZUVp3dGZMd25tMk1CbDE0RmRzTmlURlIvWXAzN0RjbE5lTkE1TmppeDlENE1pSkJXeGxoYm1YVGFhRkQxNmNtMlBDd09tNFlCRjNiQ29iRTlxZ01nRTUxdEg1akFCVkxidjNINGJYVnJGOFN2c1dYeEdyRisyV2ViSEtaRVpBYzA5U0xCaTJyMXBZaFlHdmRnQ3U5em9ubzdidjlaZGVMUlkzQnQ0MkR4Ly9tQzFJNkt4cnlyY2JPcVhab0kvREk0YkNXMS9uOXhIVFJ0UnBUYlBmcTVuU0lJZnUrWXQ0Vno3OXllZGRpTUxnWllRMjRidG9USEt1RndXNkpsRndEL1FrSEdtVEVRS0svZHBsTFd4MnlTNGdJanJ6ZnQwVXRXTnNSTEEyK24ycGtuR0NxL3dkZjR3Vkx0TTRtQWI0MElWTUIzdzErUUVQTUV0RFFjdDlNZEZNMHhGQThRd2prU0tkbXFLRnVEMUNsaVBwTkd5VldtSm5lRThGZUVmZUtWdXB6dk0wdHVHMTEyTVVRMGFqejlaVkt6bjNiTFk0SEtKZ0xreVMzaU9rckhFRmMzZlNtWG5sQWdYdUR4WTlOcUVneldReFpXWGJHNmtHMDJFMHdPRHk1S1pOZ3Vrak9sVGthOUhXTU53Nkh5bDQiLCJtYWMiOiI0MjBjMDVlZmE1YjQzZTc4N2ZjZDc1MTA5YmJkYWU1MTExNDEzZjBmZTc3NDEzNGE2YTE1YWYxY2YxMTZmNGRlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:41:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkxNVmtXWWZzeDcyWUZaTEt2UC8vL1E9PSIsInZhbHVlIjoiRWJ0S1pMSWdYSVVpYU5GZ2FCbHZzT1NsdXY0YVNsekE0QzREbDVSaER4VnI1a2p1TlI5aVBZZC9TZ1orQWZRTzJOWkVVWlpEaVFBZERmekZFcVNWOGhNelpGMmlRZm1SMVNlV1VLdkYrdUwxZjF0c0VaUkZtUE0wMm9FRWdmNHhVUnBiWXpIcmhFeXZwaCtnUzJ3ODhQOHd3L2RqbzVUcFJuVFdZdmtzU3o4d0RKRDFNYmJjRThSY0tpbCtSOTFsamJKYThITVhkQXhBTFg2ZEpIb29DN0tRZHYyd3hwdC9zb3dFRkE1V3lNTUlUSDFNRVZ2TVNuT1lVcnZ2UEV5enBHdEZ3Tml2SDZiejJmbjQwNHpoR2lNMFRTcGpWQjdObjJaeWhScXkrT1p3b0ZoL01STW9wNVRGa0o3elpKc2J5dWF4Mjd0Mzc1Q0NYWXJxcDQvOTRWRFNVTW12Z0tuaEQ4eHJzUXJ5dXI5eGk3Y1dtT1BkWGQrQnFGR1JuK09LbEphcGlkUTNodW9lZUVvMzF2aFN5Q0pOTWk4OSs2dUVQUWdubzA2dFBPVytjYW85UG9yY1gveW8rc3BkQ3NvRFFHUVhUeWR6bDJlZjc5SjdWcW1YT2Vocmx6dmo2Vnd2ek1nV3p3aVkwWHVWS3oxa0tRME5jcFR4UWdreTJnWEwiLCJtYWMiOiJmNTYzOTdhNDdiY2U4MDQ2NzRkODRmODc3Zjg2NjNiNWMzZTJjNDYxYWExNDcwMDA3N2Q3ZWYwMmQ5NDkzN2RmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:41:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2101319565\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1946227649 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2352</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1605;&#1580;&#1587;&#1605; &#1588;&#1582;&#1589;&#1610;&#1575;&#1578; &#1605;&#1582;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2352</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>39</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1946227649\", {\"maxDepth\":0})</script>\n"}}