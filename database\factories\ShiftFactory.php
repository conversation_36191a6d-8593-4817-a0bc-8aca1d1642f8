<?php

namespace Database\Factories;

use App\Models\Shift;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ShiftFactory extends Factory
{
    protected $model = Shift::class;

    public function definition()
    {
        return [
            'shift_opening_balance' => $this->faker->randomFloat(2, 100, 1000),
            'is_closed' => $this->faker->boolean(),
            'opened_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'closed_at' => $this->faker->optional()->dateTimeBetween('now', '+1 day'),
            'created_by' => User::factory(),
            'closed_by' => $this->faker->optional()->randomElement([User::factory()]),
            'warehouse_id' => $this->faker->numberBetween(1, 10),
        ];
    }

    public function open()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_closed' => false,
                'closed_at' => null,
                'closed_by' => null,
            ];
        });
    }

    public function closed()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_closed' => true,
                'closed_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
                'closed_by' => User::factory(),
            ];
        });
    }
}
