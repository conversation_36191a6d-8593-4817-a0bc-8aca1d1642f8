{"__meta": {"id": "X343aa2a9f51b2dc96dc97a779de61d67", "datetime": "2025-06-30 22:42:28", "utime": **********.506529, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.059617, "end": **********.506542, "duration": 0.44692492485046387, "duration_str": "447ms", "measures": [{"label": "Booting", "start": **********.059617, "relative_start": 0, "end": **********.463141, "relative_end": **********.463141, "duration": 0.40352392196655273, "duration_str": "404ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.463152, "relative_start": 0.4035348892211914, "end": **********.506543, "relative_end": 9.5367431640625e-07, "duration": 0.04339098930358887, "duration_str": "43.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43874384, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00225, "accumulated_duration_str": "2.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4951, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 86.667}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5001252, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 86.667, "width_percent": 13.333}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:3 [\n  2353 => array:9 [\n    \"name\" => \"مفتاح علبه\"\n    \"quantity\" => \"1\"\n    \"price\" => \"6.00\"\n    \"id\" => \"2353\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2354 => array:8 [\n    \"name\" => \"العاب شكل بطه\"\n    \"quantity\" => \"1\"\n    \"price\" => \"6.50\"\n    \"tax\" => 0\n    \"subtotal\" => 6.5\n    \"id\" => \"2354\"\n    \"originalquantity\" => 10\n    \"product_tax\" => \"-\"\n  ]\n  2355 => array:8 [\n    \"name\" => \"العاب اطفال\"\n    \"quantity\" => 1\n    \"price\" => \"6.50\"\n    \"tax\" => 0\n    \"subtotal\" => 6.5\n    \"id\" => \"2355\"\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1954191646 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1954191646\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1048623780 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1048623780\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1981647360 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1981647360\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1451680653 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323255132%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpNY3BGVkk3NWkrV3U3TXc5RHdDdUE9PSIsInZhbHVlIjoiaUpQeFI0WmNobEtMczEza0RNTjBCemdKV3hheEZMeEhRZzZNcHhhWmZnNFBubFZpa0lLSXk3R09XVnZoTEdsOG9WYVZHaVIzZHJTajYwTTNRMkRHamdLZk1JQUR5cFpYSDN6TlFXU2dwT2FhUFgyL2haeXlhYVlOTVIvbGRsNzdjWkE3MnVLa1hRaWtKalRsWFM4KzNzV1AzM01TNTBpenQ5WDJoSVVxa3JLNmRJVWkxVGJQNldOWklsYUZzYitEdHRPV052Ylg3QUIxbEVUdTA3U1Y1YTBUbzhaU01DRDR2ZlNDdzhSRjBuUUtZVlhnQUtuVXo4cStET09YZzd3SjhGMVF0Si94Z2FYM1hVQThiUC9xVG5mcWdFdUJHL095cnZGWS9wSDBwaWdHclhNdHVVTmdONG5TZ3FkUXk4c09rWllVZ0ZqWE5wNXZTT2wwMmxnYXAzdlFMMlUxTzk3MGZMR3JsL3JPNkhuTVN6czUyZ1VMTlNpdzl4Z1hoSXFxRXVjMlp3V0lWViszRjRPRTFpU2xmOHNJdEpMdkVQcHdoK3ZJTmRyT2tRSy81YU1aM1FjYyt4dS9QT0RMU0FNT3hEU2RnUHU0RmhQMG5GMzFuQUdCMnErL05aZDQwbEY2STBCMmJua3l2dHN2cGcwSXFodUtuMVU5TndqR1FHVFMiLCJtYWMiOiJmMmUyNTRkYmI4NTU1OTI0NDQxNWVlN2NjYmIyODA2M2QzYTM0YWQ0MzI2ODZhNjJiODJkMGU0NWM0MTY0NjM4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im12aG1CTzVTV3NPTzVUS0MreitUTnc9PSIsInZhbHVlIjoibjJhV3JrQ0drMnExZkUyUE41VllUNnI1ZjdSYlE2akVKTERPM25uelVXZWFhMzJhdTlGZkNKRFdmVk4vOFhTR2l2dCt3MFNEczV3TXhVNHBZVy93Wmp1UzJVUEMrbmpsY0dlNElDWEJRYnJONHNjZTdtdGxPODMzRG1MaHM5emJwcFJXZjBsZ0Fka3JMK3ZOa1prUHE3U0NlQ0tYd0xHeDdFa1ZsWHNoMXRBVENXR2lobWhqL0JLb0N4NDFsZXk3UjNGeHMrT2g5WTg1T0drTk9xYytZT2R0T3ozVlJ1U0wwaG1zNnc0UXZyS2Zwb3hPOXY0eEJwK2ZPdlphSjdqMEYwcCsySkFneHM5RUIvNCsyRFZHWE5wVTNnQnMvdmRWWldmOWlFZW5rbmkvWlRLT3pkRDZRYS9INE9ISkNLM0J5VUV3NVdBWkY0WGhVaUpFcGZ0eGZLak1iUk5sRnJTZ0VuVmp0VFhCeUFxV0xsNXlreHNNK29SeEdveUhOdUsxR2JIU0VwWDV6UW4xdEUzRUY2ZnNVTmE5Q3lvdHpFN2hGblA0S2lqZTFWME02cS95eGVIUHNYTVlRamZmTjZQUnlXM0R0dUtIeUh1aytnTDV6TlRBUGhTd01LYVJWN1NwUzMvajV3Q3Y0VS8zbmUzWnQ1YU5zcmd3d2dQQUJWV1YiLCJtYWMiOiI5YTdmYTdiNTcwZmY4OTRlZTlhMGE0YmYxNzJmMWMxOTNjNGMxMzI5OGZiYWMyNGM5MDhjZWZlYTEzMTc2ZTBjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1451680653\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1829459238 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1829459238\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1322647503 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:42:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZxdVU4MnFMSW4xUDgrbUg0dS91a1E9PSIsInZhbHVlIjoiK1pPcTNGM0Z0aU5Tbjkwd0ttSkhvV3FobHR0eGQydkNBZ1ZUSWhLUFpGYlVHYzFQT3FjRDB1QmgwOEhvdDF3TU5mOGswVjEramNzdyswbm5kem01L0xiSU51Z0dMMmVRc0hkTW9FTVR6MzJJY1prTUcyWWtvUWdzc1JxQnhtKy9HZmxEeDBSVUVkNnZtUFpMR0poaDlqT0pRMFluVjBtQkl1dHFMbEgzMWhtNnAwTkVzUDlUYUJRUVFHOU1rSmNjUTU4THR1L3drYTV5N1pXcnRwVDVyNUlCbVBNRzBnVzIwWlg2NnhSL1UrOU45QzRadk5XbEx2SnNSVVQ0S3AyWmtDbXdvMGpkTnhOUU9ZczZKS1RTeWlMOHJQSXZ5bUNHc3BpV01XWlVJQXErbUZYR1ZNNUVpNGhlanJWTjAybzlIbjkxVHlRYm4reFZvOGJwcWw4N0ZjNy96WkRXNWR3bURXV0twVTh6NzlSSEZ6RkxENHpkRDJqQlFJb0JjREU2TUZCcy9oR1FxeThaVEwrbk5GSHFmUFR6dExPd1QzMXFCK2pLSG1Rc2RvNDdRLzVDK0tvNG4rc2NJdkxDV3gzUWc1WkdnbkFEb1RldnZIMXdKUi9jQ3hhL3hiL1E1amhPNk1EZEdnMnpBaldOVlFQOWMxRjJOaXhta2pCWHJhSkQiLCJtYWMiOiI4MDRjMGM0MTFkM2FiYmY0ZTM1YmMzOGY4MzEyMGIwYzNmYmE3ZWNlN2UyMTc3YTg4NzBmNTg5ODJjNGNlYWQxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:42:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlVXdTcvUjczSy9CTXlTelF3Nlc0V1E9PSIsInZhbHVlIjoibk9IVGZ0UlcwWGxpdElPTUQyYVQ4Mkc1Nm41SHd1OTYxbFhvdHdYSzFWeldoQnNIbE5JRDVYSlBDTFdEdUtEUVhIS3dqK1E4VWJwM2s0dmhXcHpGanJDOVY3OG9PWlAzOWVEUTVLcnZpMWZoRTVKTXF2UDRXZWluRDlmZEV3MEMvZG9aRHNabVhWMWFDS1hKS3B1TG51ZmpWRlNGem5wRnJJRDVYdDkyL0dUTCszT0kwdHVQWS9xWVJuV1RrRStDM2RNZkE1SGRNYjVyYkhqSzJHM003dlh3NjgwenQ1NWNlRzhNMitCaFZ0MWpEd3JXODl1Wk4wYVVaWVEvNjdpbFRYNDk5MkRsQWY5ZFczRk1DMG52VmN5cFBjRmxCeHd2dkhNTXFNdjRBNy8rRjgyRi9QWUNtaENFcjh2b3FGU2xWRFQ5a0xvQm5kZyt2MG9sQ1dzTTlSbTFIcmJOaHJsM2NZRGx3VVlhbHJmajRWY1phQko4SkMvUGE0WGp0MFBRU0prRWN5NHFCR1Q3Y2lRK2x5eHhGNlVCTWdYY2tEa0dtLy9ZUUpDZ3NFUXJKSzlHOFJsNExDTWpvNzhFWjFUSTNkZldVUUxoUzdWL1lvSURQTnlVVFp4dlE1MGI1SG0wdndHRVo2YkFNU29SUUplZ1VVUGdBNWRBY0lRcXVWYSsiLCJtYWMiOiJlNDM4MmZjZTI4YWNlZGYxMDBiMmM0Y2UxOTVjMmM0Nzc4NTJiZjA3NzlhNDg5YTdkNTMyMzgzMjFjMzRiOWY3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:42:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZxdVU4MnFMSW4xUDgrbUg0dS91a1E9PSIsInZhbHVlIjoiK1pPcTNGM0Z0aU5Tbjkwd0ttSkhvV3FobHR0eGQydkNBZ1ZUSWhLUFpGYlVHYzFQT3FjRDB1QmgwOEhvdDF3TU5mOGswVjEramNzdyswbm5kem01L0xiSU51Z0dMMmVRc0hkTW9FTVR6MzJJY1prTUcyWWtvUWdzc1JxQnhtKy9HZmxEeDBSVUVkNnZtUFpMR0poaDlqT0pRMFluVjBtQkl1dHFMbEgzMWhtNnAwTkVzUDlUYUJRUVFHOU1rSmNjUTU4THR1L3drYTV5N1pXcnRwVDVyNUlCbVBNRzBnVzIwWlg2NnhSL1UrOU45QzRadk5XbEx2SnNSVVQ0S3AyWmtDbXdvMGpkTnhOUU9ZczZKS1RTeWlMOHJQSXZ5bUNHc3BpV01XWlVJQXErbUZYR1ZNNUVpNGhlanJWTjAybzlIbjkxVHlRYm4reFZvOGJwcWw4N0ZjNy96WkRXNWR3bURXV0twVTh6NzlSSEZ6RkxENHpkRDJqQlFJb0JjREU2TUZCcy9oR1FxeThaVEwrbk5GSHFmUFR6dExPd1QzMXFCK2pLSG1Rc2RvNDdRLzVDK0tvNG4rc2NJdkxDV3gzUWc1WkdnbkFEb1RldnZIMXdKUi9jQ3hhL3hiL1E1amhPNk1EZEdnMnpBaldOVlFQOWMxRjJOaXhta2pCWHJhSkQiLCJtYWMiOiI4MDRjMGM0MTFkM2FiYmY0ZTM1YmMzOGY4MzEyMGIwYzNmYmE3ZWNlN2UyMTc3YTg4NzBmNTg5ODJjNGNlYWQxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:42:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlVXdTcvUjczSy9CTXlTelF3Nlc0V1E9PSIsInZhbHVlIjoibk9IVGZ0UlcwWGxpdElPTUQyYVQ4Mkc1Nm41SHd1OTYxbFhvdHdYSzFWeldoQnNIbE5JRDVYSlBDTFdEdUtEUVhIS3dqK1E4VWJwM2s0dmhXcHpGanJDOVY3OG9PWlAzOWVEUTVLcnZpMWZoRTVKTXF2UDRXZWluRDlmZEV3MEMvZG9aRHNabVhWMWFDS1hKS3B1TG51ZmpWRlNGem5wRnJJRDVYdDkyL0dUTCszT0kwdHVQWS9xWVJuV1RrRStDM2RNZkE1SGRNYjVyYkhqSzJHM003dlh3NjgwenQ1NWNlRzhNMitCaFZ0MWpEd3JXODl1Wk4wYVVaWVEvNjdpbFRYNDk5MkRsQWY5ZFczRk1DMG52VmN5cFBjRmxCeHd2dkhNTXFNdjRBNy8rRjgyRi9QWUNtaENFcjh2b3FGU2xWRFQ5a0xvQm5kZyt2MG9sQ1dzTTlSbTFIcmJOaHJsM2NZRGx3VVlhbHJmajRWY1phQko4SkMvUGE0WGp0MFBRU0prRWN5NHFCR1Q3Y2lRK2x5eHhGNlVCTWdYY2tEa0dtLy9ZUUpDZ3NFUXJKSzlHOFJsNExDTWpvNzhFWjFUSTNkZldVUUxoUzdWL1lvSURQTnlVVFp4dlE1MGI1SG0wdndHRVo2YkFNU29SUUplZ1VVUGdBNWRBY0lRcXVWYSsiLCJtYWMiOiJlNDM4MmZjZTI4YWNlZGYxMDBiMmM0Y2UxOTVjMmM0Nzc4NTJiZjA3NzlhNDg5YTdkNTMyMzgzMjFjMzRiOWY3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:42:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1322647503\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1262736268 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2353</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1605;&#1601;&#1578;&#1575;&#1581; &#1593;&#1604;&#1576;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2353</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2354</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1588;&#1603;&#1604; &#1576;&#1591;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2354</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2355</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1575;&#1591;&#1601;&#1575;&#1604;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2355</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1262736268\", {\"maxDepth\":0})</script>\n"}}