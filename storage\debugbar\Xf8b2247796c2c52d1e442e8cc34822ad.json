{"__meta": {"id": "Xf8b2247796c2c52d1e442e8cc34822ad", "datetime": "2025-06-30 23:12:30", "utime": **********.480152, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.045686, "end": **********.480168, "duration": 0.4344820976257324, "duration_str": "434ms", "measures": [{"label": "Booting", "start": **********.045686, "relative_start": 0, "end": **********.427622, "relative_end": **********.427622, "duration": 0.38193607330322266, "duration_str": "382ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.427631, "relative_start": 0.3819448947906494, "end": **********.48017, "relative_end": 1.9073486328125e-06, "duration": 0.05253911018371582, "duration_str": "52.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041192, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00295, "accumulated_duration_str": "2.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.457035, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.186}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.467078, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.186, "width_percent": 14.237}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.472673, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.424, "width_percent": 14.576}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-2062337228 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2062337228\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1248613885 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1248613885\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-905842231 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-905842231\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-556806605 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325148990%7C21%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpuY3U4YTJDWjJFMGltbEZ3YXJyUlE9PSIsInZhbHVlIjoiOVlVRE1FTlBRVUdnditwUldnMFRmU01pV2N1VGFSSU11SExtamdXaFhsRjdrcEc1YVViOTR3L1NMWUxIK0tCN1BLMnJNcXkrcWluRFRhWlNjcjdhQTFtL01uSjgwK1lDb0h1RENodlVRYnFTMnVBZW50MEJkYVNHWGVYSWMwNFRyN0lqRXJsZUp1Ym1iczhhK09ETnlwQ1VtbVQzOFZVdnFvaTRsWEJJeGYwcWpmSWU4bm9wWk9mYlpzdjRjdTFrbzQ2RE1PTlJnWVV3UmhBckFxaVRBbnl3M0RrVFFrdnlJR2JnTGxUWFgwdUVpNFV3cjc4Y1E5aGVtSlFySHdNWTFwZUE5eFpmTi9GaEVEb0JKaVZVbWVmcFJqdkxhelB3ek5zRlc2TGE4MjlDUlpqSEpjZGRKR3FMTTduVnpraUd0VXpPWVg1SGJBM2ZtU1pWNWpjTWVQcW9URDBLd0ZCQm83TTF1YUZReUNHOXg1Z2hxM21NT1hSN3lWRThRdjBaVWwweTVEMVFpM1NUZDA1Qk5zb1lVL1V1eWdoQ3psQXpXYTBQcU56cWk2MEpXUFppUTZBTExIcFM5eDhtVm8zdkF5ajhhcVNCTWI4bjdmMHkzeGNoZ2dLd3pVNVpkMW5jTWRJUXlWMzU3KzUyMWJ5b3NyeHdYNTVuVXhsbDNqVXEiLCJtYWMiOiJhNTUzNjc0MDk4NDc3YWVhYmYzNmIyNTA3YzhlYTdjNGIwYjk2MTRhYzNkZjMyZWQ3M2I5MDEzZmZiYjY1OTc0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InY3WUlyQ1Q0SURBYXRaeEpISE9BVnc9PSIsInZhbHVlIjoiekFBZEJyV2pqWTNmNndzckRRaU9rWVhFazlnT3VwVUVlZlI0R1hSdlRQZGpqOXcvdDdyOXNVOENPQmFOakFXTmRpeFVtOW1VY0lWS0k5SUc2cXplL2tHUDJ0Z3NyK0VpeE1hVGl1ZnJoZFExZU5BSEFhVWZHUHBENHp2REROSjRreVRlWTlPSUEwUTEvMklXNVZxN2tVZ29memJXYnBybWg0cEV0bXlmd3d0U1FaM0pyZDVzb1o1KzJLMVB6RVRCOC9ySXVZSmxZV1plT1ZmYnNZYlZxdEFzdzFWMEdkWFZ4S3QvSUprd05OUTh5TGZ1eVR1NHQ3QkE0aFVKdTZtQzk0Tm5UYWw0eHFSaG5UdGVBTk1GRjN2ZnY1c2k1QlNMaHdTNjc2Nm03VmJwK3BmNE9sRnMza2diM1l2d0VQekZJTWZWRlhrMkF0M1NQUFZtQ05TTWZIRWVoZkF2bHpVTGJ6dlN6Zi9uVGcxc3NHMGI0NnR6RVhwSy9xbWdtejRxQStnbHBDRklUSGtuLy9FRjBLSkRhdStGZVFkZWd6UmxVODBVVFllQTVrZmE0RnlPY0JEY1FuZXJXTmpTZnZqQ2RmbGU5cDFJTTZUZG04TEF0TG53MHUyT3Z0R2xha2lubW4xeDQ1QURyNW04SFFCVzc3N2JvbW5mS1VCVUdmVGIiLCJtYWMiOiIwNWZiYmFjOTA3MTIyYTIzYTEwNGIyZTFmMjFhNTViZTNhZjI2YTVjOWM1MzBiNTk4M2NlYThiZjlkMjg3NGVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-556806605\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1184327107 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0M1K0sdNadPD2LVrnt8LmBw227nA9F1U5e3ROaJK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1184327107\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2103682591 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:12:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IngvVTV2L2RKRVAxdmM1QUJqQkxrd3c9PSIsInZhbHVlIjoiQldWZzNOaDd2K2Y1bDVlMXY2MjU2REZaV1dpcjQ5V3ZEVEREQUJOWlFQMEhsVEN1NXhQdzhla3hNY3UyMTY2eHRoM2kyNVg1ZVBkUDNXZCtzNG1sdTZJYXBqMkJPeWs3Znd0QmtqMWN1SVlMRGNXc2NNK094Y3F1cjlpbWJqQjhKcFdUZEFvQVVFQ0hwMUZRR0lHcisrTkQwYnAwWWRQcEJYbE5XVFljU24wK09HSloxdW9RQjB5b0VkVzhXNG85MENQdFBYZFdBMVF1ZWU4aXRJYTF5aGxoend6blA1RXBiOElqb0NhWWJFYW91Vm1VWCtwOCtxdzlDR3MxT1ZRVnArMkFXOFlxL1FINHdSaTVPSzRBWkovSHAwa25adTB1c21lOHhkTDhteWlpdlJpbVBRd0FkZ096NDR2cXF1Q3RnNGRJVmtZbFhUeHg3V2FJMHJNc3YzMVFsSnIram12QlVxdjRONzF2aG51V1ZKZnl3Z2NWL0ZIdmE2WkJrYnRqNDkrTm5NZUJ4NDRHVVJibmU2VUxWSzUvNTgwQ3VpZmtUVWxZV25zTFB0YUthZVpkb0x1dUppT1craHQ4QmdZZGFaejloVVBlYldnV3BsUklnMlV1TU92RVVCMU9jZFdjQWNMY0lSckZDcFpzN055QUFzczc2UnVHU05QS09vb00iLCJtYWMiOiI5MjRjYmM4Zjg1MmYzYjY1ZGY4Yjk3MGY4YTBmYWU2NzI3MjgyZWY3ZjRiZmQ1Y2FmZGEyYmRhYjUyYTM2ZjZkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:12:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlBqWStLVkp6ZXZSdXY4dTVTRWJ2aHc9PSIsInZhbHVlIjoiQWFuaFRVKzZUbm9ORk42ZG1QQkRmV0RNQmdNTmhic2FmLzhXa0xjN25nUm4vQ21hbWRkSERlK1FjWlFLQWR6S1d3QzVTMVpPZlBvU1V5RWhVY2gvR1Fvdk9sekc0NHYyUUdOZXc1cEZxQ1VLUmc5djArUnVTUWcwc2RQWjZOcmppSnh5Zlo2d1pYMnVOZmJnN29nZFVDN3ExRzUveFY2bXA2bFU2VFNxN3F5bEdqK0J3L2pTaVZBVHNZaFpYT0RFS0xBN2MzRGpHNE9GV3loajF3UFgrRlBsMVE0elg1VXlWTG9HNkdNT0drb1FJRFVrWHpsV1lSWEhWSWZKYzVIWlZDRE9ZaFoxeUUxYUJLa29qUEh5Sk9FS0ZMcTBpOGlaZkNzcXArZVk3Z2tlQTlnQWNKUDBpTXhSUDdyYStiYXRDL1BKTTJRRzMyQ0o4TXk1YzcyeDcwclVCWitZUkxRMDZYTkJacHZIVGgyOVpuRWFUMElHeUg1bGQ5Q1VmbHlwV1dBVk9VRUJlZitLMmg2dThxcDFJbHVMbndHQW9HTXdvdU53ak5OZXBmV2MybndkYTNtOFB3cUhRZWxKTFI2SFZhVmVOb3J6WE5Lc1NKMU0yM2J6MDFlWFo4c3NHWnZ6L3NMN2VNVmVqM2xRaVE5SFdmdWxyNlQzNGZWdEdGaEkiLCJtYWMiOiIzODdkNDJhOWMzZDYxMmJiNzM0YzBkOGE1YmEyMDU1OTlhOWI1OTE5MWIxOGNmNDczODMxNmVhYmY1NmM1N2JlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:12:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IngvVTV2L2RKRVAxdmM1QUJqQkxrd3c9PSIsInZhbHVlIjoiQldWZzNOaDd2K2Y1bDVlMXY2MjU2REZaV1dpcjQ5V3ZEVEREQUJOWlFQMEhsVEN1NXhQdzhla3hNY3UyMTY2eHRoM2kyNVg1ZVBkUDNXZCtzNG1sdTZJYXBqMkJPeWs3Znd0QmtqMWN1SVlMRGNXc2NNK094Y3F1cjlpbWJqQjhKcFdUZEFvQVVFQ0hwMUZRR0lHcisrTkQwYnAwWWRQcEJYbE5XVFljU24wK09HSloxdW9RQjB5b0VkVzhXNG85MENQdFBYZFdBMVF1ZWU4aXRJYTF5aGxoend6blA1RXBiOElqb0NhWWJFYW91Vm1VWCtwOCtxdzlDR3MxT1ZRVnArMkFXOFlxL1FINHdSaTVPSzRBWkovSHAwa25adTB1c21lOHhkTDhteWlpdlJpbVBRd0FkZ096NDR2cXF1Q3RnNGRJVmtZbFhUeHg3V2FJMHJNc3YzMVFsSnIram12QlVxdjRONzF2aG51V1ZKZnl3Z2NWL0ZIdmE2WkJrYnRqNDkrTm5NZUJ4NDRHVVJibmU2VUxWSzUvNTgwQ3VpZmtUVWxZV25zTFB0YUthZVpkb0x1dUppT1craHQ4QmdZZGFaejloVVBlYldnV3BsUklnMlV1TU92RVVCMU9jZFdjQWNMY0lSckZDcFpzN055QUFzczc2UnVHU05QS09vb00iLCJtYWMiOiI5MjRjYmM4Zjg1MmYzYjY1ZGY4Yjk3MGY4YTBmYWU2NzI3MjgyZWY3ZjRiZmQ1Y2FmZGEyYmRhYjUyYTM2ZjZkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:12:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlBqWStLVkp6ZXZSdXY4dTVTRWJ2aHc9PSIsInZhbHVlIjoiQWFuaFRVKzZUbm9ORk42ZG1QQkRmV0RNQmdNTmhic2FmLzhXa0xjN25nUm4vQ21hbWRkSERlK1FjWlFLQWR6S1d3QzVTMVpPZlBvU1V5RWhVY2gvR1Fvdk9sekc0NHYyUUdOZXc1cEZxQ1VLUmc5djArUnVTUWcwc2RQWjZOcmppSnh5Zlo2d1pYMnVOZmJnN29nZFVDN3ExRzUveFY2bXA2bFU2VFNxN3F5bEdqK0J3L2pTaVZBVHNZaFpYT0RFS0xBN2MzRGpHNE9GV3loajF3UFgrRlBsMVE0elg1VXlWTG9HNkdNT0drb1FJRFVrWHpsV1lSWEhWSWZKYzVIWlZDRE9ZaFoxeUUxYUJLa29qUEh5Sk9FS0ZMcTBpOGlaZkNzcXArZVk3Z2tlQTlnQWNKUDBpTXhSUDdyYStiYXRDL1BKTTJRRzMyQ0o4TXk1YzcyeDcwclVCWitZUkxRMDZYTkJacHZIVGgyOVpuRWFUMElHeUg1bGQ5Q1VmbHlwV1dBVk9VRUJlZitLMmg2dThxcDFJbHVMbndHQW9HTXdvdU53ak5OZXBmV2MybndkYTNtOFB3cUhRZWxKTFI2SFZhVmVOb3J6WE5Lc1NKMU0yM2J6MDFlWFo4c3NHWnZ6L3NMN2VNVmVqM2xRaVE5SFdmdWxyNlQzNGZWdEdGaEkiLCJtYWMiOiIzODdkNDJhOWMzZDYxMmJiNzM0YzBkOGE1YmEyMDU1OTlhOWI1OTE5MWIxOGNmNDczODMxNmVhYmY1NmM1N2JlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:12:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2103682591\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1936341257 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1936341257\", {\"maxDepth\":0})</script>\n"}}