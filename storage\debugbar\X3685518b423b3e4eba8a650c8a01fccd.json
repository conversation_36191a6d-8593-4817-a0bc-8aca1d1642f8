{"__meta": {"id": "X3685518b423b3e4eba8a650c8a01fccd", "datetime": "2025-06-30 22:39:12", "utime": **********.963135, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.469798, "end": **********.96315, "duration": 0.49335193634033203, "duration_str": "493ms", "measures": [{"label": "Booting", "start": **********.469798, "relative_start": 0, "end": **********.857709, "relative_end": **********.857709, "duration": 0.3879108428955078, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.857718, "relative_start": 0.38791990280151367, "end": **********.963151, "relative_end": 9.5367431640625e-07, "duration": 0.10543298721313477, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48184632, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03236, "accumulated_duration_str": "32.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.893199, "duration": 0.02596, "duration_str": "25.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.222}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9272099, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 80.222, "width_percent": 2.287}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.94155, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 82.509, "width_percent": 2.812}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.94388, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.321, "width_percent": 1.422}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9489849, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 86.743, "width_percent": 8.9}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.954247, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 95.643, "width_percent": 4.357}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1874515938 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1874515938\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.947877, "xdebug_link": null}]}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1983231715 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1983231715\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1613555624 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1613555624\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-210938297 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-210938297\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1988983382 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323149690%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImFFT3d1c0ZZWXNaVFROdSt6TjBRdkE9PSIsInZhbHVlIjoiOHgxSmV4em0zUisxcVRRTG1rMkZSUTJzWVh4ckxnRTNoRzJGbWpHOWlROFQyZkhIRmI1eE9ybjhqSUpXdHBMWExxc0NianF2b1hZYmV2L1lPL2M3b0dQTHJ3WHFQS2UwcndpVW9SMjdvR0ZZV3dXQllFdTVnTWQ2VG9CVmxVY2h4bkJ5VE9NREp5SE1TME9xNFdUdkJSa2pjU2ZzbS90NUx4UFBxckVEd01sOU5wVkdjKzY4czVTQnF6Y1REb21WcjJQdHJxYjR4YXplcWE4cEtwQW5qTjY3TmNRcFNmV09CZFFwZjN5RjdVTnBjWG9qb2cyOUczUXhPRGFLUWJ4U2svbGtVcmpzVUtxdEtZSjhnVkJHNDROdkxocXFIZXMwaTgzUFlyT0czR29PS2dQQVptMWM5YWxhcDAwcE5uV3pxS1NERnpOSDBhWGVuTEY5RXY1UDlhTVhKNDdLczh6Zk8vQmR6MWJJNnU2QXVHYWtSWHZUaEx1UUp5MGpVUlVyenhkNVVWSEFOeHdTL0Rwd1NOak9uNjFhclFRcUlHYmRMMGFEa1psZjRjbTR5Wmd1WVNTYVNGQTE0SEVBV204TTdFSk8xZUFzZlM4dGpDVDd0dEtNYkR1WWoyWlYrbUV3OTZ4QXA4NkVTc0VkOE8zYUVBbUQwbGpWY05PSVpWZ3kiLCJtYWMiOiJjMGE5YjU4NTE3NGYwZWI4ZmM1MGMyMzZiNmJiMTczYTA2MDFmNzk5MDZkMTc5YTg3NmNhYjgyZmQzMDQ0NTMwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjFodnhCRENSc1poU3o3YmpGckk2VEE9PSIsInZhbHVlIjoiSmVPNXVRcDNtTlNidFE5czhVMzNESnRmQmczUEZTKytiWVlzVTFwY01kSWhKbFJZZ2xzNk5pc0U2dzZjM2FpTTdnL3B2R3o0MUdDQnljcmZYSEpMMzZZRGJUbzk1UnJCMW9qSmpPYU1KWjhWNFRjd3k0Q0NadjNSMXNkTUg0bHhjTytnRHREbjBZTHRoQnRIRDJCMmg1WnRsUWkyOHZrM0l4SUxWdUVDQnBhRkFOVUU0NkxVVURUQ0g2Tnc5SWZFblYwNnlTZGZZVWFMbkNVb25Db1gxaDEwdlRJM2ROT3NFUWJ6Z0x5MTEvK0NGSHVHd1lzQnJmRXJjUnBjUkU4K3VoNGhObjljMUp6MU9GcVlTOUdCQXRTMlVZME4yckd2Mm52anRUY2JEbEIwcmJ5N1dxTlZmTVZBMS92aWVSMFgyWFhOMXNTYzkvL3c5byt6UnJib2dLd1B1TVhEK3Y3a0hEekRyVmxvRXZMZE5FczJnbklDWlJtODFGMUJxQXFQaEN3aStSZktFNlZUaUs3Vm5yQkplc2NDWHFUUDFvK3o2aUpwWVVhenNGK1I0SWV2YjZHbnFEd3NWYkp6dnNnNWtkNGxLTUh4b2NMeW8rVHdGc0R3LzhXOWd5VVhzd0tiYndORTdLb0lSZHU2aGdyNEpRQ1Y2aWd6cURqVlFwRVMiLCJtYWMiOiJiNTMwZWM1YjhiYmI4ZjZiZmJkOTI1YzNiN2IxMjYyZThjZWE2NmM2MDdhODU5MGEzNmJkMWFiMDMxYTQzMTY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1988983382\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1206719255 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxoT3M0K1puK0VMc3JUK1VoZFdkUFE9PSIsInZhbHVlIjoiVllwbDlmZDgzNmdSbTY1Zlc3RmxFMTIrSWFpZVdSV2dLeU1hNVE2VERoUHRYUytqajZZbEo2dUhXWnhPWmpRZ2huemxMZTZXQ3paL2l2TzM5SFVIWTZ4Y0JON1BUb2xrVzVmcm94NEZhV1lmNFArcFRac2ZzZGt4dDZFTUhIU1RCdlpnOW1kanNHZUhoaEVVUGZCWkhHbXhYS1dnbEx6ZDBCbU8vSFM1QXFGa29ZMXdlcDZCR0FpN0I4Mmowbk1Rd2ZNcUMvU3NLRXVxVGNiQllaWlZSaGRPZnVQTVdRZHdFR2pkZHA1a3hmVTBRM2Z4MVRBckQ4VGQ2OGNWUzN5bVV3VWtvRnJKU3QwZzJDaWFTK0Q5cHZLa0p6cXZjVzhJQi9LTGhZMU1nUlA0VHJkMnd5RDZiRGJnRThYVkkyTDNXSHZLdy9BREFMbzBuYnNMKzdvRUltUmRKTHJ0RENVcEFHdk1kSExsMHRnR1Z0LzBLeVMzYmlqa2ErRTJEWVU5b1pRY0dCY1A5MDgrWlNWM3cwdndTMnNVb1dKT1NUU1RPc2pwbkNsUzllSWh2Z29QbkpnL0hFTlN4OHJJRS9ZTmJDcE5DN0RsK1JCbkpCaFNxbTNtcDlJVzE1d0Vsa2E4dTVBWkZWQUx2STVkaDRLS0JHRnc5NzArVThESHBHR2UiLCJtYWMiOiJiMjcyMWY4ZDU0YTBjMGQ5NDhhY2FiZjM2OGYxYTlkZTg2MTgzOTczNDgyMGU5ZmQxMDcyZjU0NmJhZTBhNjZmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImsrTmhGYmQwamt4M1RQZU9pNnRVbUE9PSIsInZhbHVlIjoiSkVHaDhIMzJKN3Fjek9KR2psdUNqTWR4VUZSSkNjNUp1VEdDMmJJQjlhcDBmbjdrNDE4ajNEcUkwa2dqR2E5MWVQWkpBT2FNd0tUSWpZbHB1OEtjeHQ4MHUrRzc0TUFYUXEwV083UVR4cHNDRjZKUkdiNzdEY0JwYUhYZzhVT2JmUVJFZmFlZjhIN3dNWEppelE1UVJTZ01HdmhTeFZvaS95ZmFWMnl5dFpMSUdWU3gxaDJESUVDeVVPYUMyUnd5M2o5VEFoQXVaLzcreHo3clQ5RGU5am9EUUxwZnowc01qU3hrZ2JodWNHUFF5RmQzeEt1aFllRTFwaTVSSE9DUkxBQ0ZRendlbGVMZi9ua1FYYXhrMGloVDdRVEdDWFVOU1BHZ2RncStOUnNhbFEwWDV4YVNwUzliT1dTSW5CQTVGcXl0dG1QbWU3WWdkZWhOZUF4WGpnWGNEclFhVHgxQVN6SHNYazU5TWNJSkhEUWRBZW1uMEhna2hJZElTYVhUcHJlVlVXSkRnZmErYzllYnJDOXJhVWdHT0RJY1hNbEFjRHhjSzdZUjVablJ1dmsva1FUNGR6dGt6V0xiaUpHOFdydjNsM21mTTZ0eVh0QmR6QkIxTGt0dEJxSDl1YUdYRmtneGU2VHZKWGpkOHZEQmJnRWo4Q1o4UHN3aU1yU0IiLCJtYWMiOiJlNDkwMzZlM2MyZTUxM2ExZGVkMWZmYzJkNDk3NDQ0NmYxMzk0YmZhNzA1YTBiMTJjZDBhZjViODJkMGUyMzY5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxoT3M0K1puK0VMc3JUK1VoZFdkUFE9PSIsInZhbHVlIjoiVllwbDlmZDgzNmdSbTY1Zlc3RmxFMTIrSWFpZVdSV2dLeU1hNVE2VERoUHRYUytqajZZbEo2dUhXWnhPWmpRZ2huemxMZTZXQ3paL2l2TzM5SFVIWTZ4Y0JON1BUb2xrVzVmcm94NEZhV1lmNFArcFRac2ZzZGt4dDZFTUhIU1RCdlpnOW1kanNHZUhoaEVVUGZCWkhHbXhYS1dnbEx6ZDBCbU8vSFM1QXFGa29ZMXdlcDZCR0FpN0I4Mmowbk1Rd2ZNcUMvU3NLRXVxVGNiQllaWlZSaGRPZnVQTVdRZHdFR2pkZHA1a3hmVTBRM2Z4MVRBckQ4VGQ2OGNWUzN5bVV3VWtvRnJKU3QwZzJDaWFTK0Q5cHZLa0p6cXZjVzhJQi9LTGhZMU1nUlA0VHJkMnd5RDZiRGJnRThYVkkyTDNXSHZLdy9BREFMbzBuYnNMKzdvRUltUmRKTHJ0RENVcEFHdk1kSExsMHRnR1Z0LzBLeVMzYmlqa2ErRTJEWVU5b1pRY0dCY1A5MDgrWlNWM3cwdndTMnNVb1dKT1NUU1RPc2pwbkNsUzllSWh2Z29QbkpnL0hFTlN4OHJJRS9ZTmJDcE5DN0RsK1JCbkpCaFNxbTNtcDlJVzE1d0Vsa2E4dTVBWkZWQUx2STVkaDRLS0JHRnc5NzArVThESHBHR2UiLCJtYWMiOiJiMjcyMWY4ZDU0YTBjMGQ5NDhhY2FiZjM2OGYxYTlkZTg2MTgzOTczNDgyMGU5ZmQxMDcyZjU0NmJhZTBhNjZmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImsrTmhGYmQwamt4M1RQZU9pNnRVbUE9PSIsInZhbHVlIjoiSkVHaDhIMzJKN3Fjek9KR2psdUNqTWR4VUZSSkNjNUp1VEdDMmJJQjlhcDBmbjdrNDE4ajNEcUkwa2dqR2E5MWVQWkpBT2FNd0tUSWpZbHB1OEtjeHQ4MHUrRzc0TUFYUXEwV083UVR4cHNDRjZKUkdiNzdEY0JwYUhYZzhVT2JmUVJFZmFlZjhIN3dNWEppelE1UVJTZ01HdmhTeFZvaS95ZmFWMnl5dFpMSUdWU3gxaDJESUVDeVVPYUMyUnd5M2o5VEFoQXVaLzcreHo3clQ5RGU5am9EUUxwZnowc01qU3hrZ2JodWNHUFF5RmQzeEt1aFllRTFwaTVSSE9DUkxBQ0ZRendlbGVMZi9ua1FYYXhrMGloVDdRVEdDWFVOU1BHZ2RncStOUnNhbFEwWDV4YVNwUzliT1dTSW5CQTVGcXl0dG1QbWU3WWdkZWhOZUF4WGpnWGNEclFhVHgxQVN6SHNYazU5TWNJSkhEUWRBZW1uMEhna2hJZElTYVhUcHJlVlVXSkRnZmErYzllYnJDOXJhVWdHT0RJY1hNbEFjRHhjSzdZUjVablJ1dmsva1FUNGR6dGt6V0xiaUpHOFdydjNsM21mTTZ0eVh0QmR6QkIxTGt0dEJxSDl1YUdYRmtneGU2VHZKWGpkOHZEQmJnRWo4Q1o4UHN3aU1yU0IiLCJtYWMiOiJlNDkwMzZlM2MyZTUxM2ExZGVkMWZmYzJkNDk3NDQ0NmYxMzk0YmZhNzA1YTBiMTJjZDBhZjViODJkMGUyMzY5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1206719255\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1524495136 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1524495136\", {\"maxDepth\":0})</script>\n"}}