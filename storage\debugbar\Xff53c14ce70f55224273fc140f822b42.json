{"__meta": {"id": "Xff53c14ce70f55224273fc140f822b42", "datetime": "2025-06-30 22:35:57", "utime": **********.610787, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.031727, "end": **********.610801, "duration": 0.5790739059448242, "duration_str": "579ms", "measures": [{"label": "Booting", "start": **********.031727, "relative_start": 0, "end": **********.420069, "relative_end": **********.420069, "duration": 0.38834190368652344, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.420079, "relative_start": 0.3883519172668457, "end": **********.610803, "relative_end": 1.9073486328125e-06, "duration": 0.19072389602661133, "duration_str": "191ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48184632, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.09210000000000002, "accumulated_duration_str": "92.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.477497, "duration": 0.02366, "duration_str": "23.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 25.689}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5101311, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 25.689, "width_percent": 0.543}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.5255542, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 26.232, "width_percent": 0.706}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.527735, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 26.938, "width_percent": 0.489}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5331628, "duration": 0.06539, "duration_str": "65.39ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 27.427, "width_percent": 70.999}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.601168, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 98.426, "width_percent": 1.574}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1927463915 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927463915\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.53142, "xdebug_link": null}]}, "session": {"_token": "iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1069718596 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1069718596\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1219879602 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1219879602\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1523022577 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1523022577\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-203083905 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=6nrx0y%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1aj6s00%7C1751322883410%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjM4ek5PcEFhdmJnZGswYlEwYy9hYWc9PSIsInZhbHVlIjoiQWE2Zk9Fb0xXdVFyc0hsbThvYjYxaWM4WTRnTGhkWjZDaWFpTlhFVDVBODJzbjVJelpsbUtPdEk2SDVUcUVPSzBHK0ZKT09xZWNTWE94dHdBYUVaWHVibFRaSGVRM0VFeEdPc1dyS0x2aThjcEcyOENZaWhzL0Y3OEJKL29JSm9rT0xRaEVHZ3M0YzdORHF3K09Rbng4T3ZOUW5xUnJWcUcrVWJhTzhQZFFwSFlneTRIdDY4SE8xV3NPRloxaEZDK09OenhjYU5Tdlp3MEpCZTg4L1hiKzFTMCtOdFhoUi9xNlI4OWdMQXJEMmNvVkxycDVIVWh0Z2R6MExWalRKUm9hM0MweE91Vkt5OGlkb3BQaFNPSVRobjR6TFNndGpvMVlrZmNTUXNxVTZIZ010bmRzRnBSbHVPbklSaFZxOXZGU0lnelY1cDh2ekVZaWJQSnBwZTNuVlo0N0hpUkR4ZmxyYzhFTUkzNDVFQkpRdHJ3UXVBdS9PWXk1b1BNczFWQVN5NW9HZWFFK3FEU3lhS01YYXJyUnRQYkMxSXpyRUpRc2x5TlpPSXoyUUp2NUFQemhFbitFcFRkV3VGemZGUVJWai9jV2hOeEZzYU9GV0NTYnR3YWRBd2h6MzZvYkxLM3VGTTVpSFdCenpMWDVFRnBYM3hHUWlPb3hobXl3dlMiLCJtYWMiOiI4YTI0NjAwZTU2OWFlNTBkNDNlZTBhMWRiMzIyNTMzYTA0ZGJiOTRkNzFmYTViMzRjNjhmYTcwYzE0OTg3MGUzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkkwWEwva2NjZ3d5Q0R0TVNWVEQxWmc9PSIsInZhbHVlIjoiMTJZaUo5a1pVeVlwTjR0RGlGMU9pdzlYcU5WT1duVm1vRDk3R1BoNDBnYVFSMm5wdlU1dERYVVFSM2ZHTWl5elR1MzRPQmZrVE00WDNFMWRUaU9DZHA1ckVYR05CU3dKSXdyMFZ3QWFzU0xKanRlUVJ6UWtsTTVNQ2Q0OTdKVUxqR0VEUENZb0w0c2h3aEdLb2lyNkhFay9mNzNXQm9KYmwrSjV6NUtRNzkycWp1UExqZGpUL3Q2YmtWQ2VKSk4zaGJPK244UTRqQ2s1UzhseGY5Wm9qK0ZIT24xUUdWYS9qVmFqV1NNUEd5MGdzZ3VRRVBoUEM0WmdHMFpXSERuYnVBM0dicDN6YVMxZ01leEFxVTIzTFlCZ3BGSmQwTGx2dEJUVEVXRjhEbnNZQldOb1l1VlVlK1BHd3RMQWo5ZFYrMU1reHpHOEFNWHJqOWNFZlEzV2RHaEV2UnpLcCt1ZXFxemlwV2VwQnk2SGpyaFNIT2k3SDg4dTRLNFZPK0RaanlpWTRsVFdTNlY1bjNDcjN5Uk5DMTdOSWF6RVQxT0poUFdQRndha21oZy94RjJERXhxdTIrbWRwRWtQUFVJUU51MmJSamh6ODN4cURpS0xxRlNNNXdYUWxlTEtQRW92ZEVOZ1dxc2gvZUJ2bnJQY1FGd0Q5c1EzRGdRbmo4WEQiLCJtYWMiOiI3NWMwZTEyMmMwN2MzNzhhM2Q1YWU4NzExYmQ0MDExYWY4ZDA2MzY4NGQ1MTUwZjMyZDFiN2MwMWVmMGRiYmNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-203083905\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1564709776 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A9rdyCRm7SKpfljuMnVO7IpcgTBMITMjowAdsmJo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1564709776\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:35:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllKSTlkamlCdlpXUTNHNXBaL1pvWkE9PSIsInZhbHVlIjoiRzNQSExnZGxwZ0dDTWY5RHZQbTlYdFFEdEszK2NoRk4zRjlJMmd3bGFTS2paUC9MNzJ1bEh6VWxrZnRLSld2bmE1eHh5eVZqbExJY3RiclR6bk1TUFA2YWRJN01tcEFjQUx3UUpGOEZxdStLeFo3Z1oySmVSMmRGT0VBSXprbkhaS21aNUh4am1FdGFRT25ERVhxWHNPTUNqcThDbzlMMmF4dDBKOUhnYVhGVXJrZFBqaGJyTk4zZjBHSmhPalpBay9SZEt2ZjViMDhwQWZwWHRyRHhTSkg1ZTJjRktjYUFBZVg5WHZrQ2NjQmRDV1JiMFl5bHVNMjgrMVJnOUQ3K0xnVVFxcEtibVZGbnJEelVWQ2V1WElEbEhkbmhoQmtzQkxXUVQxWVdSZ2F2ODdWL0ErMUZjcElzQVpKajNxRTVvazFxTWZTRXRRcVlrajdzM1JBYThJclRkY1pQbjBJaGlYM3VxM0xjb3MwTU5xWC9SUFVFUTlieWFuVFZnZzduM3JBa05GY1V2Nm05ZEFWVkFmZUU2TVZhbmpMdk1tMHJ0Rms3UzdVVERwb2liUTg2ZGt1YVNtR01YSExWZ3V6dVFFVEhxT1Nud1lLMFM0dGNjdWZCSERRZ2lvNURjQlV5OHFoMC9KTkI0Z0UrMFp2aGFpaTZWTWdSczhacjM0Z2YiLCJtYWMiOiIyMGFlNjhlMjhiMGJmNGI3NjljN2Q0MjIzZDFhZjUyN2NlMmY4ZWQxMDI1ZjYxYzQwYzRhNjc5MzMwZjFhZGM4IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:35:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InhrV3lhWEwyWngxeGZRVG9OaGYxQXc9PSIsInZhbHVlIjoiWFJQcU5lbDZtaTFNdHVoOFRNcEFJMEhxK3hMU1lyOTkreDNhYyt4b2VTeW5RK0NIRWM4UjFZN2xMYUVMU3JTR2kwMytzRzRXd29xZmxybFJKdjlESjhONWxDMlZTaTFpZkNGZm1sQTBwR1FtbVV1NU5lRkRHTUFLYzlLYU80U1E3QUZCekprWlZPdXRreVRDcW90RjU2eWJ1YnhsWDRsM3FZZWQ4NUpNQ0JxR1E0aEFRM0w4TVU5QnNBclJ4WllaODBXT09oUEI4YjNRMkcvbXBFY3FVb29BTlg1N3FOb0lvYjMzaE5PTTVZMzhoOG1jYUhKTVRTNjFBdGlvZjg0UlNFZ0VSNmcrSFk1ck9CMkc0WjVHOGh5VHRza1NzOUNsdUhKSWU0NDdJWVMyLzdISzlNeEJTZVJaSFkwSDhGOUxyWE1YelZDQWtJQy8vVXpycDc0eE53b2ZuYWFQMnVUZm02aGRNWlR2TG5pcmFPZEl1VHgxempzK1E4ekNPZmxQL3FLRHIvMEs1YU5LTFpjRzRMU2NCTXFEZWJEQkpITmRwVjR6TFlzcHBrdmZiVXBHUTN6Rm1SSjc5ODNUR096QS9ISU0rUERTUGJ6K0ozUGJvR1JhRVJlMlFUWmNtVEZwTGZGUGRkaFFwSnl4WFM2c081SkRiMU1vQjVVUks3WHQiLCJtYWMiOiI1N2IwM2FkMTYyOGFkODgyNDkzNzdlZmIxMmNmNjg0OGJjNmUzMDRmNWU5NmJlZjQyN2U5NGQ0ZjZiOTI3NTEzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:35:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllKSTlkamlCdlpXUTNHNXBaL1pvWkE9PSIsInZhbHVlIjoiRzNQSExnZGxwZ0dDTWY5RHZQbTlYdFFEdEszK2NoRk4zRjlJMmd3bGFTS2paUC9MNzJ1bEh6VWxrZnRLSld2bmE1eHh5eVZqbExJY3RiclR6bk1TUFA2YWRJN01tcEFjQUx3UUpGOEZxdStLeFo3Z1oySmVSMmRGT0VBSXprbkhaS21aNUh4am1FdGFRT25ERVhxWHNPTUNqcThDbzlMMmF4dDBKOUhnYVhGVXJrZFBqaGJyTk4zZjBHSmhPalpBay9SZEt2ZjViMDhwQWZwWHRyRHhTSkg1ZTJjRktjYUFBZVg5WHZrQ2NjQmRDV1JiMFl5bHVNMjgrMVJnOUQ3K0xnVVFxcEtibVZGbnJEelVWQ2V1WElEbEhkbmhoQmtzQkxXUVQxWVdSZ2F2ODdWL0ErMUZjcElzQVpKajNxRTVvazFxTWZTRXRRcVlrajdzM1JBYThJclRkY1pQbjBJaGlYM3VxM0xjb3MwTU5xWC9SUFVFUTlieWFuVFZnZzduM3JBa05GY1V2Nm05ZEFWVkFmZUU2TVZhbmpMdk1tMHJ0Rms3UzdVVERwb2liUTg2ZGt1YVNtR01YSExWZ3V6dVFFVEhxT1Nud1lLMFM0dGNjdWZCSERRZ2lvNURjQlV5OHFoMC9KTkI0Z0UrMFp2aGFpaTZWTWdSczhacjM0Z2YiLCJtYWMiOiIyMGFlNjhlMjhiMGJmNGI3NjljN2Q0MjIzZDFhZjUyN2NlMmY4ZWQxMDI1ZjYxYzQwYzRhNjc5MzMwZjFhZGM4IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:35:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InhrV3lhWEwyWngxeGZRVG9OaGYxQXc9PSIsInZhbHVlIjoiWFJQcU5lbDZtaTFNdHVoOFRNcEFJMEhxK3hMU1lyOTkreDNhYyt4b2VTeW5RK0NIRWM4UjFZN2xMYUVMU3JTR2kwMytzRzRXd29xZmxybFJKdjlESjhONWxDMlZTaTFpZkNGZm1sQTBwR1FtbVV1NU5lRkRHTUFLYzlLYU80U1E3QUZCekprWlZPdXRreVRDcW90RjU2eWJ1YnhsWDRsM3FZZWQ4NUpNQ0JxR1E0aEFRM0w4TVU5QnNBclJ4WllaODBXT09oUEI4YjNRMkcvbXBFY3FVb29BTlg1N3FOb0lvYjMzaE5PTTVZMzhoOG1jYUhKTVRTNjFBdGlvZjg0UlNFZ0VSNmcrSFk1ck9CMkc0WjVHOGh5VHRza1NzOUNsdUhKSWU0NDdJWVMyLzdISzlNeEJTZVJaSFkwSDhGOUxyWE1YelZDQWtJQy8vVXpycDc0eE53b2ZuYWFQMnVUZm02aGRNWlR2TG5pcmFPZEl1VHgxempzK1E4ekNPZmxQL3FLRHIvMEs1YU5LTFpjRzRMU2NCTXFEZWJEQkpITmRwVjR6TFlzcHBrdmZiVXBHUTN6Rm1SSjc5ODNUR096QS9ISU0rUERTUGJ6K0ozUGJvR1JhRVJlMlFUWmNtVEZwTGZGUGRkaFFwSnl4WFM2c081SkRiMU1vQjVVUks3WHQiLCJtYWMiOiI1N2IwM2FkMTYyOGFkODgyNDkzNzdlZmIxMmNmNjg0OGJjNmUzMDRmNWU5NmJlZjQyN2U5NGQ0ZjZiOTI3NTEzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:35:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}