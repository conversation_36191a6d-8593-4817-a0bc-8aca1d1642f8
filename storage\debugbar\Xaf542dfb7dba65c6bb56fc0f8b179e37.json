{"__meta": {"id": "Xaf542dfb7dba65c6bb56fc0f8b179e37", "datetime": "2025-06-30 22:41:35", "utime": **********.601904, "method": "GET", "uri": "/add-to-cart/2352/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.108763, "end": **********.60193, "duration": 0.4931669235229492, "duration_str": "493ms", "measures": [{"label": "Booting", "start": **********.108763, "relative_start": 0, "end": **********.506137, "relative_end": **********.506137, "duration": 0.39737391471862793, "duration_str": "397ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.506146, "relative_start": 0.3973829746246338, "end": **********.601933, "relative_end": 3.0994415283203125e-06, "duration": 0.09578704833984375, "duration_str": "95.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48673752, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00706, "accumulated_duration_str": "7.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5478442, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 26.629}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.558919, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 26.629, "width_percent": 7.082}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.573528, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 33.711, "width_percent": 9.207}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.575886, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 42.918, "width_percent": 5.099}, {"sql": "select * from `product_services` where `product_services`.`id` = '2352' limit 1", "type": "query", "params": [], "bindings": ["2352"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.580825, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 48.017, "width_percent": 6.232}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2352 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2352", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.585135, "duration": 0.00281, "duration_str": "2.81ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 54.249, "width_percent": 39.802}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.589298, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 94.051, "width_percent": 5.949}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1547539752 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1547539752\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.579827, "xdebug_link": null}]}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2352 => array:9 [\n    \"name\" => \"مجسم شخصيات مختلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"2352\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 39\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2352/pos", "status_code": "<pre class=sf-dump id=sf-dump-1108053014 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1108053014\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1749119873 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1749119873\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1073491930 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1073491930\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323255132%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZ3OFVhQVFNODNkc01ldnV5RnNKbEE9PSIsInZhbHVlIjoiZHVuaUdWR21zOE5nMWR6NFlIelFZQ3MzYVAzQUVrRGlVMFFoYzZvc2podkhpTGdPSHJyamhWNkhrdFV4aDJOb1gxcGhhRU5Ec3B2c2Y1NGZDQ2FUeXJLMXpYd1NwVVBEVklKbE1pc0lGZzBSTGhrVjlOa01yV1JMOTMydVp5eXlYcmNSRXZ6NUgwZEg2L21VaXdNeFhFakhXK2dWWTJDQXBRd0pyUW1zc3lTYVczaDA0VGI0QjlOUTJJUDJxVkF6emNRZ2t3NGZvU1N5VmNsYU1MMGpyZm9PUHA5MTVIemdZNVVlYVQ5dEFzdElidTNvWHdTQWFtZnVXNFgrMTlzWkNPVWhoWEZmUGpwbnZIWjdmTHBCaEcyc0sxU0VNQWUyZzNnK2E3Qi8waU1NRDZwSVAzZUN1V0ZibmJIUXE1SDVrZVlsamt5RUlIbFZKUmdSUisvTnlJRWVRMkY2LzFLYnUrdVZVVDNTaXZ5TDNVQlppYmZLL2REYm4rZks4U1NDVE5sSVpIUW1USG4xNXhtcmsySmV5TXNNUmZqZ0ZTY1hOYitaN29BRmJWU3BZMFBrNUQ1TVZzSFFNdTVDN3dFOHVqeHNrSWRXOFlabVBIclVRVXdZbUdiYmVHK2thRUxVdUI3elQ4M0NNTlEyYXdUUzBBeXNaOHlVVGNxQ2ZWYjciLCJtYWMiOiIyMDBiMDE5MzgxMzZkZTE1ZTQyNDQwYTY0NzAwZDI1N2JmMTkyNjkyMWNlNmNkY2Q0NjRkZTU3ZDYwZjRhOTdhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkxzSXFjVmV3U1kyOFdpcTZwSS8vdFE9PSIsInZhbHVlIjoiZXU0ZXdZcnZiZ1VLVVJxMUZLTnEvRWNXbDNrQXRZanB1TUZ0NER2MnRPZVZ4anlQOEFsRmZaaWg5L2ZZSWVXMW4veldWV3hqUjJkK0JaTHA1YVV4UEhVMmRrVkt2VEdWOWY0OEExOVQxdm1VUFJUMkZVcTAzakpJNCtUVmNwL3pBQ29oY0ltTmZnMnBlQXNQVUwyR0Q3SGd0WXFqbE5tUTdSTUtubGsrQzd6UHRnRWhhRDA1RXEweFQySzJsb3NRRXBsSXpjUEtmMHV4ZTJXNGs0RE1HaDdUWm41ZDh4dnI0b3hoWE5lVFlCdE5vd1NuS2cxd3RINE0rV2ordXdPQ0w0L1RFRHNyOWhKZ3RrWDJBWUhMZWVsUjNUeGZSSitDdEt2aXRCUFNDZVNjSXJkK3Z1VHVNcUNEQ3Q4d2xTNmFvckRBTit1U1k4dFpZckVEU3VUQmdmcjZycU9zNGZBRm15Yjh0RGs5Mlc3a014WitKc2hGTjJibkFWaTB0cndXRkc5NksrVmw4WkpjdVpNcWJGQm5Ib2ZaZ1FMb0tmbEpodzEyYmVPYlBnYm9Wd0V2Tk9OdmRsdkEzdEtmTktRaHNMR2dTVzV1azZSRUZNTDZaendYcE11ZDdub2tNT1ZvS2U0RVAycEc1Tm9xa09MbmhqTzNUZjkxaUh0N1F5cWoiLCJtYWMiOiI2NTE5Y2U2MjI2MzEzNzAzNWMwN2NjZmVjNGM4Mjc1ODdlY2E3ZmFjYzE3MmRjMTc3YjcxMGVkM2YzNjk0MzEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-931053807 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931053807\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:41:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikk2cVVEMXA3UERIc3BqZzU3Vis3N0E9PSIsInZhbHVlIjoiMit1RUFBRjNyK2VnWDFET1NTVmdUOHFXVkdGSVdZUGhONmZ3bjVUV3V5cFlNMnlYMk1lUnp5aHEyaTVkTFBzaHFWdTJETmZGRDkvYnZVZ1lKb0hESkRvV1V6UmtnbW1ZUktabExaUGF6ZVZ4L29PeFNJdjFkd0I2NmRYbHR5TE1pVkZyOWRaRUg2a203Qk82Y3lUWnNPa3VDb0x5UEhVWmhGTVVLdjBqaDFJRisyTkR0NnZGZnhDUkNzYTlXUGxLdm1aU015UGFzT1N1Wm9mY0N5RFNIaXN5blBjMzJLSkdYM1B2R3FNZnE3V3QxZWtpVHd1WVZaaUpKUFBiUnk2eHA4WVoyY2pyc0dlVUtlT3V3UFowR0dYdzl1MGhBcURKbjRBWnZlcFQ5amU3aGd1WEV1Ny96ZkZtckpyMG1lbDk5YVV1WmFPSHZGTXhjVXJqWW80eG5qVi9kcFBSdlF4NXFuM09FTU9aeDBFNnZHc3cxOC9Wb2dSLzNBRXpKVlB3azhtVmNIODlZWllzTEFXbS9oTWJ0QnFKOUwybXNzKy9tNGo5L1NYT0tWK1N3b0dSc0d1MnBEUjNEZ1ZNOGpPLzZ3d1ZXR3dKdFFqYzV1MGtsQkYzZFZZeHovbHMwbHVQQXpXQmszOTNuRFdtQ0s5TUc1SXZjVzdNTmFSVW1GMlEiLCJtYWMiOiJmYWYzZGIxYmNmMTJiYTEyMWU3YzI5MDhiMzlkNzM4M2UwZWZjODI1MTY2ZWJhOWE0ZmViZDVkYTk3ZDU1ZDY1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:41:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkNTMGwzRXJRZ0FZTWk5TFpJak5NOHc9PSIsInZhbHVlIjoiUDR0SXZhZVhxWkQxZ0djNkpmUTdQT2hZQ0JISzU2clRTcHIwOElDMkcyZ1c0V2x2d2FnMUEyQnVOaUVaYmE1bXBldHhXNjYvaHk4TnlUZ2VxaWtLRXZVOEIvS0ZlRlJTZ2ZodzZtRnIycGIxVHlOcDQwa3ZtUjZtUDhKL2lzUUVNWDFyM3dmWDZTK0ljM283VlhaT2V2QkVTUlRpSjBXTUNhbW1YR1RBZ1o3d3Ryamg5NC9MM2pWU0dFOEVSSjhlb3V3WEJoYUx6Ulhmb2FTWG84WHFkT3N5VUZ6VFJpcjlxMnMyQXpmWlNtWmdaczBRQXQwbmRZM1huV1oxMWJibDRVL1NBZkRGK09zTlBZWHlkZEhZMjR0UStvUWVUbERnbUYxTWFlL09uVlNLT09NdlRZSFpWVEIrSXI3WHcwanV4Y2dVRlZFRWx0OVM0Z2lGT01uTWxoN3BJeWsrbUFiYnlQWWRxbzY3Tng0eUpyTy96WnJ5YXpTdDNyNTNlaWFiZkZIOERacldsakNOM2xUb3hvNFNaQVlOaHNSTDF5ZmQ2Y2loZlpBV0F3RnZLYjBmRFV1SHpPR2JVdVVveUlIZlVhWUFEcVRqMXFrazNPWUpaWGZPbUdLcTZqbTdiRVJ0T2NaNE1nejIrWUhmYldlbUFNTGJMbVB2QVFGcFpzajciLCJtYWMiOiJiMjc1MWQxZWJhMDZiMDAzNDc3MzY0NzFiYjJhMGQ0MThlNWQyODFkYTJiOWYzZjdjZWE3NDE5YmU5OGM4MWE2IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:41:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikk2cVVEMXA3UERIc3BqZzU3Vis3N0E9PSIsInZhbHVlIjoiMit1RUFBRjNyK2VnWDFET1NTVmdUOHFXVkdGSVdZUGhONmZ3bjVUV3V5cFlNMnlYMk1lUnp5aHEyaTVkTFBzaHFWdTJETmZGRDkvYnZVZ1lKb0hESkRvV1V6UmtnbW1ZUktabExaUGF6ZVZ4L29PeFNJdjFkd0I2NmRYbHR5TE1pVkZyOWRaRUg2a203Qk82Y3lUWnNPa3VDb0x5UEhVWmhGTVVLdjBqaDFJRisyTkR0NnZGZnhDUkNzYTlXUGxLdm1aU015UGFzT1N1Wm9mY0N5RFNIaXN5blBjMzJLSkdYM1B2R3FNZnE3V3QxZWtpVHd1WVZaaUpKUFBiUnk2eHA4WVoyY2pyc0dlVUtlT3V3UFowR0dYdzl1MGhBcURKbjRBWnZlcFQ5amU3aGd1WEV1Ny96ZkZtckpyMG1lbDk5YVV1WmFPSHZGTXhjVXJqWW80eG5qVi9kcFBSdlF4NXFuM09FTU9aeDBFNnZHc3cxOC9Wb2dSLzNBRXpKVlB3azhtVmNIODlZWllzTEFXbS9oTWJ0QnFKOUwybXNzKy9tNGo5L1NYT0tWK1N3b0dSc0d1MnBEUjNEZ1ZNOGpPLzZ3d1ZXR3dKdFFqYzV1MGtsQkYzZFZZeHovbHMwbHVQQXpXQmszOTNuRFdtQ0s5TUc1SXZjVzdNTmFSVW1GMlEiLCJtYWMiOiJmYWYzZGIxYmNmMTJiYTEyMWU3YzI5MDhiMzlkNzM4M2UwZWZjODI1MTY2ZWJhOWE0ZmViZDVkYTk3ZDU1ZDY1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:41:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkNTMGwzRXJRZ0FZTWk5TFpJak5NOHc9PSIsInZhbHVlIjoiUDR0SXZhZVhxWkQxZ0djNkpmUTdQT2hZQ0JISzU2clRTcHIwOElDMkcyZ1c0V2x2d2FnMUEyQnVOaUVaYmE1bXBldHhXNjYvaHk4TnlUZ2VxaWtLRXZVOEIvS0ZlRlJTZ2ZodzZtRnIycGIxVHlOcDQwa3ZtUjZtUDhKL2lzUUVNWDFyM3dmWDZTK0ljM283VlhaT2V2QkVTUlRpSjBXTUNhbW1YR1RBZ1o3d3Ryamg5NC9MM2pWU0dFOEVSSjhlb3V3WEJoYUx6Ulhmb2FTWG84WHFkT3N5VUZ6VFJpcjlxMnMyQXpmWlNtWmdaczBRQXQwbmRZM1huV1oxMWJibDRVL1NBZkRGK09zTlBZWHlkZEhZMjR0UStvUWVUbERnbUYxTWFlL09uVlNLT09NdlRZSFpWVEIrSXI3WHcwanV4Y2dVRlZFRWx0OVM0Z2lGT01uTWxoN3BJeWsrbUFiYnlQWWRxbzY3Tng0eUpyTy96WnJ5YXpTdDNyNTNlaWFiZkZIOERacldsakNOM2xUb3hvNFNaQVlOaHNSTDF5ZmQ2Y2loZlpBV0F3RnZLYjBmRFV1SHpPR2JVdVVveUlIZlVhWUFEcVRqMXFrazNPWUpaWGZPbUdLcTZqbTdiRVJ0T2NaNE1nejIrWUhmYldlbUFNTGJMbVB2QVFGcFpzajciLCJtYWMiOiJiMjc1MWQxZWJhMDZiMDAzNDc3MzY0NzFiYjJhMGQ0MThlNWQyODFkYTJiOWYzZjdjZWE3NDE5YmU5OGM4MWE2IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:41:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2352</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1605;&#1580;&#1587;&#1605; &#1588;&#1582;&#1589;&#1610;&#1575;&#1578; &#1605;&#1582;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2352</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>39</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}