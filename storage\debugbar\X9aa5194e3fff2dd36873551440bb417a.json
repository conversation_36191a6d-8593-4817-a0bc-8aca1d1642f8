{"__meta": {"id": "X9aa5194e3fff2dd36873551440bb417a", "datetime": "2025-06-30 22:40:55", "utime": **********.133469, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323254.674652, "end": **********.133485, "duration": 0.4588329792022705, "duration_str": "459ms", "measures": [{"label": "Booting", "start": 1751323254.674652, "relative_start": 0, "end": **********.074831, "relative_end": **********.074831, "duration": 0.4001789093017578, "duration_str": "400ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.07484, "relative_start": 0.40018796920776367, "end": **********.133487, "relative_end": 1.9073486328125e-06, "duration": 0.05864691734313965, "duration_str": "58.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45721904, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0032499999999999994, "accumulated_duration_str": "3.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.105388, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.923}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1159668, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.923, "width_percent": 13.231}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.121626, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.154, "width_percent": 21.846}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1569000770 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323250612%7C5%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpndTVjRHB3aEwxOThBZlh6WHNud0E9PSIsInZhbHVlIjoicDRwQW1MUGVzaSsrMUdrcmZzMG9XdlNPbXIya0ViOWo0UGoyRHpkbUEzVXBlcFR5QmFoVDVMVFBwRXpINWphcTBmL3BNV21OVWE3MXB5d0xkdEhWZGZWZjBwNUdmTXBNVVJQaTF2TlpqcUxjTHNiaHpGVlYzWjhuQk1oVFJFRTRRM1dKNUtjWnJ6clVBR3BsSUMrVnNFVXpmYjFISTlGNGRCNjVIckFFcUZ4MndKakhYZ2RuZVVGWVkrcmdVelNFWmNsanRrR1J1MWZxNUxETXZKN1o3K0FyZlpwOE1VbGxFVnBaQ3NIMmFBMGJyY1lIMUdOVHNQNTVWOFBuWUMrOExGalF4ZlNCcGZnelo3MGFLUU9lTWtVb0ZlQjM2NHZ5WDkwSGtKMi96bWNRbXlpbExYazd4UU1KOHQ1bllYZTJmckpFKzQ5M1c3a2ZRa0pGTXhJMnIwUDd2STVhSGhxeEZPZVhlK2RaeG0rRHU3NG5jRnprVndLalRENWtHbFQrOEVlTkNFdldpNGlLanh5MDg2ZTBUVndhcE00RDRmNzFlQTB5QzZRTHczRHJzUmlUVU1jSWEvVjZYQnBSdHN1aEU5QW1IY3dtVXQwaytnMUt1YVh1QVpWeFJRQkNabW5BcWdBUmpjd3BqbUs2SEdhRjNCZ3NoQzVOM3dwQnh6SVAiLCJtYWMiOiIyOTdmNWFhYjg4MmZmNzk2OTcxMTdhMmEzYzRjZmY5OTUyMDBiZTRiZWI1ZWFmZDc5YWE2YTAzY2FhNzhiMGRhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkhoZ3dXSTBpOUl3RWJkbEpEZlVLM0E9PSIsInZhbHVlIjoiNXJpaFNCTmgrK2RteTZLYVk3RTk1OFUxSVpDNzJzYzNWV3FXc3BwRy96ME9BNFU3SWlTQ1RDbFkyQUJMY2lFaTlja2JuN09DZEdQUHplOFloQTBKSjF4TTB1SGxZTDBqTVFlSFNURFU2dmFka0VFQS9VVTFrOHBoZEh5UUsxYzZSL3k0eEdlcTd4Y1BQUFJ4bVVjZ2wxK3VtdzhsOGRSbkFvakwyTWs5R0o4K3N5VFllUXdIWDdyRWdzbldOVU9lY3RnTENtMHZwUjAydElRRC9TcnRtTjZOSTRRcUtOeXEvbFczNjNST2ZEbmxFdVNiMEQrQUlOUXdkZ1A0LytZb2M2c0JSamJGQzRGbnNTOG84RHRQUmR2SE5mandwditRR3pSNmdjRWgycHdBZmFqNmVIcXhsejZNSTRoTW5WWFk5U244OFV6Q2FDLzNuY0J4NXh0ZmRKNjdSU2lRZ1lyZUZiZkZoL0dBakprVXUvSlBDVWRLZnVMM2NkRTR5K0FnZzFXMVFXbXdiUzNxbE1TZFJpZlFSNVJOeWFSMndtcDAvb1JuZTdRSlZVZVpjb3pRNGlFc2VtWjVXa1p2WTJ0dkw1aGdKRkMxU3NpdVdzOVlUWUExeHI0dlFoRko3N0s1UlZoUEtKUVR4dXgrVG1sMS8vanB1RGgrZW1rYS9BSlQiLCJtYWMiOiJkZmM2YmE4MWU2OWEwNTNjOTQ2ZmRkYjhhZTZlZGNkNjYwZjRkZTM1ZmFhNjE3NWNmYzNhZTQ1MTE0YjQwMTQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1569000770\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1317424097 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1317424097\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1922017132 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:40:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilc1N3RqQlRiN2pGQzMrUy9hTXRNT1E9PSIsInZhbHVlIjoiRkZ0emFHS2wydG9Ca3lVejJoZkZxR1ovaTVESnlQKytmak9jY2NRZHY0Umo4RU5Cb1ZkU1ltRGRwQm03UUxkQWJycGZnL1BMYmk4c2tMNmVFTzhnYllTTC9sNThtYUFHMVc3aG9RZUgzOXVaRTNBempoZXdLZXVyK2N2Y3I2a2ViUHprWktMbUVpcFZxR0owTjZuWTh2aDhSQ0h1YmlMekZZd3BNSlZiOTRUTkRxbTFxa2k0T081NkpaYlNDOUxsbGZrdXIvejBXcmYxUzU3NHlhSDBXaVIwNWNnS2xtS05TbHhLcnRQYlB2Zjc4WC9EbFJuS25hQlNsd0VLUUdTRCtRVW9KTFVjNmJJeVAza2k4bGpkejdTVTljR0pzYmFJS0dPTEdUN1E5YTBwVi9lSXM2aXFrdC92MUs0YUFVbkNEWmdoQkdZbGgxK3hBUTNONERRWC9sWFFSaW43UmxQck9ZYXFqN3ZudFRkdWZqSnVhbkZmdVZwelRjS053WTgwc1JRdndrcHJJMjlIalRVL2V5b3FNU0RQTENzeUg1dGxQdGY4NzhNUmd1dzRQSnRlZUdwRlRwUGlEbHkyL2dzSi9Zb1hvWVhTcVVDNkZzSWd0QWNmaXVLOEI4V1orUWZqSkNaK0FtZkpPWlR2TFRHNGptS2loQkZscFZiM3NhWmoiLCJtYWMiOiJiYTZkN2EzMGVlZGYyZWQxZTY0ZmQ4YmI3NjNjNmM4OTYzMGM3MTNhOGYzYmViN2Y2NzEwMDhjZWQyZTRjZTEzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZMMnBLeEVGMncwc1VNR2ZoNThZcmc9PSIsInZhbHVlIjoiYkczanVzWWZaVE5rNUhsYUd0Q0JzM1RhMDVJVDZBemEwdzFsMlNTYnhFeHhVSDduNXN6UTZydDVJeW9zaW1YSmFrWnlIeFRVL1FKME9FeDF1cEYrNHQvL0dWWkRrRGhIVTl1K3RCOUJVZ2l5MkZPTzZpVFF1aG9acDYzT05JTThCREhISjVwcXl3ZVEzbUFsZzdtUEh3VG8wQ0dkRGp3dS9BOThFVEpJdi8wUzFHRVBRamJTUElScktUQW9zL2E1QUlhR3lFOVNIQXl1QlBMYWpkS2hFSThYMFZYa0NRMFNCSzFDd3FRZWN6OW9GbzlWbUpiWXM1aXNYNUIvSkdzenRNdDdOd1FkYk5FRDhFdS9TNFc4SDlESnJ3dEY1ZHVvYWN6eFh1clV1WVZXeEM3YkVOMnF2NnZiS3pCWm4wSWxjMitaSHd6Uk1WUStRd3JsTzJVbFhESTRoa2szTmxLNnZHQ3ZXZndoclNFV0FvV3lZenoyQXVFejZJcjBvcjdGQmlhbHdqNFB6NGp3TFcwSmxjWVBxTmRVWmM5MFNzSjFkMjNWS29HK0pwd0VGV1NGN051d1NlNk1ueXdmTHlzVVdPR1JZL0pzU3hyU0xmTWxhdW53MWZneUNVb2VWTHJ5QXIwcCs2SmNKS2h6TnBsL2tkVi96eGd1c3R1Vnkrc2wiLCJtYWMiOiI2ZmY3OGEwYjQyNzM3NjljMmFjZWEyY2RmZWIyYjRhMjJiNjNiNmY1YmY5ODNmODNlNzZiNjhkNDMzMDYyZTA3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilc1N3RqQlRiN2pGQzMrUy9hTXRNT1E9PSIsInZhbHVlIjoiRkZ0emFHS2wydG9Ca3lVejJoZkZxR1ovaTVESnlQKytmak9jY2NRZHY0Umo4RU5Cb1ZkU1ltRGRwQm03UUxkQWJycGZnL1BMYmk4c2tMNmVFTzhnYllTTC9sNThtYUFHMVc3aG9RZUgzOXVaRTNBempoZXdLZXVyK2N2Y3I2a2ViUHprWktMbUVpcFZxR0owTjZuWTh2aDhSQ0h1YmlMekZZd3BNSlZiOTRUTkRxbTFxa2k0T081NkpaYlNDOUxsbGZrdXIvejBXcmYxUzU3NHlhSDBXaVIwNWNnS2xtS05TbHhLcnRQYlB2Zjc4WC9EbFJuS25hQlNsd0VLUUdTRCtRVW9KTFVjNmJJeVAza2k4bGpkejdTVTljR0pzYmFJS0dPTEdUN1E5YTBwVi9lSXM2aXFrdC92MUs0YUFVbkNEWmdoQkdZbGgxK3hBUTNONERRWC9sWFFSaW43UmxQck9ZYXFqN3ZudFRkdWZqSnVhbkZmdVZwelRjS053WTgwc1JRdndrcHJJMjlIalRVL2V5b3FNU0RQTENzeUg1dGxQdGY4NzhNUmd1dzRQSnRlZUdwRlRwUGlEbHkyL2dzSi9Zb1hvWVhTcVVDNkZzSWd0QWNmaXVLOEI4V1orUWZqSkNaK0FtZkpPWlR2TFRHNGptS2loQkZscFZiM3NhWmoiLCJtYWMiOiJiYTZkN2EzMGVlZGYyZWQxZTY0ZmQ4YmI3NjNjNmM4OTYzMGM3MTNhOGYzYmViN2Y2NzEwMDhjZWQyZTRjZTEzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZMMnBLeEVGMncwc1VNR2ZoNThZcmc9PSIsInZhbHVlIjoiYkczanVzWWZaVE5rNUhsYUd0Q0JzM1RhMDVJVDZBemEwdzFsMlNTYnhFeHhVSDduNXN6UTZydDVJeW9zaW1YSmFrWnlIeFRVL1FKME9FeDF1cEYrNHQvL0dWWkRrRGhIVTl1K3RCOUJVZ2l5MkZPTzZpVFF1aG9acDYzT05JTThCREhISjVwcXl3ZVEzbUFsZzdtUEh3VG8wQ0dkRGp3dS9BOThFVEpJdi8wUzFHRVBRamJTUElScktUQW9zL2E1QUlhR3lFOVNIQXl1QlBMYWpkS2hFSThYMFZYa0NRMFNCSzFDd3FRZWN6OW9GbzlWbUpiWXM1aXNYNUIvSkdzenRNdDdOd1FkYk5FRDhFdS9TNFc4SDlESnJ3dEY1ZHVvYWN6eFh1clV1WVZXeEM3YkVOMnF2NnZiS3pCWm4wSWxjMitaSHd6Uk1WUStRd3JsTzJVbFhESTRoa2szTmxLNnZHQ3ZXZndoclNFV0FvV3lZenoyQXVFejZJcjBvcjdGQmlhbHdqNFB6NGp3TFcwSmxjWVBxTmRVWmM5MFNzSjFkMjNWS29HK0pwd0VGV1NGN051d1NlNk1ueXdmTHlzVVdPR1JZL0pzU3hyU0xmTWxhdW53MWZneUNVb2VWTHJ5QXIwcCs2SmNKS2h6TnBsL2tkVi96eGd1c3R1Vnkrc2wiLCJtYWMiOiI2ZmY3OGEwYjQyNzM3NjljMmFjZWEyY2RmZWIyYjRhMjJiNjNiNmY1YmY5ODNmODNlNzZiNjhkNDMzMDYyZTA3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1922017132\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}