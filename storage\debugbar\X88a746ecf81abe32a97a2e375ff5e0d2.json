{"__meta": {"id": "X88a746ecf81abe32a97a2e375ff5e0d2", "datetime": "2025-06-30 22:40:50", "utime": **********.499915, "method": "GET", "uri": "/pos-financial-record/opening-balance", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[22:40:50] LOG.info: Opening Balance Request Started {\n    \"user_id\": 22,\n    \"warehouse_id\": 8,\n    \"is_sale_session_new\": 1,\n    \"has_manage_pos_permission\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.461558, "xdebug_link": null, "collector": "log"}, {"message": "[22:40:50] LOG.info: Returning opening balance view", "message_html": null, "is_string": false, "label": "info", "time": **********.462819, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751323249.994131, "end": **********.499935, "duration": 0.5058038234710693, "duration_str": "506ms", "measures": [{"label": "Booting", "start": 1751323249.994131, "relative_start": 0, "end": **********.392675, "relative_end": **********.392675, "duration": 0.3985438346862793, "duration_str": "399ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.392686, "relative_start": 0.39855480194091797, "end": **********.499937, "relative_end": 2.1457672119140625e-06, "duration": 0.10725116729736328, "duration_str": "107ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52144936, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.financial_record.opening-balance", "param_count": null, "params": [], "start": **********.469066, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\resources\\views/pos/financial_record/opening-balance.blade.phppos.financial_record.opening-balance", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fresources%2Fviews%2Fpos%2Ffinancial_record%2Fopening-balance.blade.php&line=1", "ajax": false, "filename": "opening-balance.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.financial_record.opening-balance"}]}, "route": {"uri": "GET pos-financial-record/opening-balance", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@opinningBalace", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=262\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:262-323</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00391, "accumulated_duration_str": "3.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4292269, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 45.78}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.439447, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 45.78, "width_percent": 21.739}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.454189, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 67.519, "width_percent": 20.46}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.4567041, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.98, "width_percent": 12.02}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-209892713 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-209892713\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.460872, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.462686, "xdebug_link": null}]}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/pos-financial-record/opening-balance", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323247055%7C4%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpFcUJzRk1tNC95NmV6SnhIR0RZYVE9PSIsInZhbHVlIjoiL3lveElmeVVzUmJmSzloZEVmSDFkdis5NjZSdk5mRVhDS0dzZjg1WW5yYW9KYVNVbTBTaEpwcGd1ZXJuMWtTaVJyTlpnUGxBSDU1b0hJLzJqOWtHLzUvTnV4MWk0Z2ZRT2RsUVVYRjQvTElzVnZyeGtZbUtQRVVWQUVYSTB2YmJha0RNUW1KRWJtT1RxNEo0TU1KQ3VjM1NqYVlYSjAyZCtTeEl1QW5LaUMxdjJld2loWlZ0amNESEdObU9FbWNMRGltdUt3UEtEN0RXdDBFZ2xsVzM1VGxPc0NRaXp0ckhoamhZUEVpeGQremVuandYblRuYjlrRUpINno5SW5hTGlaRlQ5Y1lWVWZvMDdWbWR0L0w3RTBOcVNmWGQ3ckd5d01HemlQaFBqeWxtbUJIQWVoNDJ6Tm1mYk45bFFsWS94UzhuU0FDYzg2RTAxMUlJNXcwK0lOVXlBZFRxTS9UUHNVNGFTTmNBdjY1eHJVOXc1Y0tERFB0QVFnQWZFb0VIY3dwZ3NQQzZzbXlwNWlQUmdQcVhXU1JMM3NJR1ZYdWVMNG9Sb1JCMUtEYk92a0FIZnpLUmRSN3JvUGVPMXhCUFB5VDN6aDJvK1ZaaVE3UVphUU5NczhGbHJWdm5XTENzNFBveENWT2hsTitDTUtlbXhWLzZsVWxNNkNna3ZVOVEiLCJtYWMiOiI0ZmVkNDQxZGVhNDZkODk5ZTI3MGI1NzU3MjQ5N2UwMWIxOTBkOGVhODg5NWUyNjAxMDkxMDQ2MWI2MmQxMWRlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IitYd09USDVpWi9pK05kTmR4WWRMblE9PSIsInZhbHVlIjoidmx0a0xweU95TjRjR09Qd0VpY0ZtVjJIT0x2VWdRWFE4WFRIMkl3S3ZneGo5Y1ZPc2VSb3Q3RzlpSHRvYXY4WlhGK1R4V0Mxa2lRb1ZHaER2Vm0reElqWGRSNkQzY1VrZ25KZTUxU1dGcU50bnEyZ2FmTFVJdys3UHVGbTRkQ3Q4K2VERGZaQ284V1d1WTFUbnlMbHlUUXlyVXFHek1ORmRoMTU4MHVHdkF0bjFnSFlKSkZxd1lkWWxsSUVmcUFjM2VnRW9JUytFalhQVm1WOGRrc3VBcWdEdnNXUXkydXBkVkxPd1VUWG5MZElEU3BydCtZcmVhREtKeGU1QnFzc2hrYzdnN2twT2VtTmw2MU1vTVJacUU4a0xKblpYWmVjdWh0eEtBUnpKZFppMy8zQktDUTF5azh6MEJHS0svaUw3Wm5JbmVWVG1TazNYTW1Hdk5yL3dRVUZOV2lXK1JBMUJxczJMaUE1cEZFeG5yR2xwNjZFNVRmTUNiNHlxSm5PSzFtbjltUDJJcGwyVnlrK2UwRVlsdUNJbjZLT201VmNrZy9WSU8xWEF5bnFRSUtjNTFqYkJyejZzOFVxNjgvVWl1Tk0yaWoyMllhK3NXd2hjc2dDK0N3aUVhaGZRY0cvVVB3Z2JGajVVVXNHUlVyVDUxTVBLUS9BK1AzU2NTVDkiLCJtYWMiOiJjMWIwNTdiM2YzMDg2MDg5ZTAxY2YxODY3NjUyMzE5YzlhODk1NGExMjk5M2FiZjY4MjlhMmYzM2Y2OWJjOTNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1557164374 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1557164374\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-148443170 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:40:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iis4TDlJbkhpQm5iRlhxN2Z3U0tYUnc9PSIsInZhbHVlIjoiRHM0ZnEzM0ZJTWVFNWNQUGw3ejZzQU5GNllYcUw5QVl1MVZWQlZZUExydU1tV1AvOG9WNVZCSnljcGhpcXQ0cXlCTVNoSGhoOHp6dlkwZHMrblhkc3JmbVhwdnlpSHlZczB5ZGJhRHlPMWFucDVVaHNlYmNZRC9lTCt3VW9IM0pCTTV0NWp2dTJLeDJ1S1M4VUJ1TmRWWEhXaGY3Y0hBL0NvZHFSMFNhR1ZWcE40UHFNR0xHL242VTVDTjFXQi96MUxBZHdBbW03UXJZdHk5U2VpempoaWxNNnU3aXVhN1VmbjBvQjdUODM0eTN1azhMb2xxZGdYSTBseFNENmVrK3FuUklFSDkzcHg1OUZJL3gySFFHVG9ZRitESkdPQjI0YTBMclpzZzZ0UVR0UkJHVGV0UzBGbFJjR3NXbjk5M29RUGUzUzA5b3RLanp2ajFMMkVQVEdLK0xFTzRRZTZtS3RwaFRoeFc4RFhoZndYYjhURGZNQXhBS3VrVXdNSHl2S1JEUG9KQ1J2dllJWHZRdnlIdEhRVzRxV1JYbkxqWW14N052SS8yb2tHRndkdE82T0RsY2Q0SDBNYzJiM3NWQW5XdEl4ZGEydFdIcFlOdGxqL2JoOFBNLzhSclJuaHdORHhGcitlQjJZTENHWnZGK0I0bkpxS08yc3pxTElORzQiLCJtYWMiOiJhMzk2Njk5MzVmZmNmMWI1YzAwYmJkMzJkZGIxZmQwZGMyOTY4MmJiM2EwMWQ5YTMxMDdhNTk4Y2M1NjE5Y2Q5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InR0NUZ6eTlhYjZCbzFHbXRjNG84UlE9PSIsInZhbHVlIjoiSkVqY0hjUGtRa2h1MkFrZUhOd3FrK1NlcFFLc0wwQk5Odys5WDZoY2hpUUVMenFjY3dpcHJ2N25URGJEV3RkUDZ5ZDBZQVJwTnpxU3d5UmY3bFBjT3h5MVU4UUNXWStBUXhaR3B5QWRETnBuNlNURFFzdXM2VDZzNXRkd1Q4YTNJTGtCZnN4RjluN2lnTFF5RlRSMzU0N0RhVUYwdTZIUWVkWW1pN1J2U05XS2hNNHFmTTNoWmQ0ZUdLOWtQaEhXbzJrbk9wemNQbUxYS1N3QTliam0vM25YTWVrY1lRdnNSUEtYTlI3ZXFDNDNSMEp3YlpRQ2pJUGdveUV6cTRndE5NSDROTnNTa1Q4L1RvRVlaS0E4a0wvblZEZWlVWGxjRkRMWHpUM2dSS1diM1NpNytvd2xFWFhIbCs4MjAzRGtzMDFHRHF5aXdPQTQvSDRSNDlWS0ZuTCtmTlFxaEVRbFMzMGtTV0doa3NPQ29HYlN0eGg5ZmNvSEZQYVh2cXFLZFUrVnJzR0laNjNDSEJ4cTd6bmZjYk10UTBIb0hUdU9yZWNxL1p4YUNZajNDVjdIRUVKZ0hWVG1EZXVQeGY2blBuR2VLaUNkTkVZT3o5U3RVQ1BCaTJ2bkNmSTlaOEh4S1BlTnhpTGw1bDBYUVVqejVvSks0UVY1anRTcmlUV2QiLCJtYWMiOiIwM2E5NzU4Y2VmYzIzNGVmMmQ3ZjQ3ODg4MTIyZGQ5NjViNmQ5MTRkZTAwNmUxMzVkYTEwNzM2ZGFjMmYxNzVkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iis4TDlJbkhpQm5iRlhxN2Z3U0tYUnc9PSIsInZhbHVlIjoiRHM0ZnEzM0ZJTWVFNWNQUGw3ejZzQU5GNllYcUw5QVl1MVZWQlZZUExydU1tV1AvOG9WNVZCSnljcGhpcXQ0cXlCTVNoSGhoOHp6dlkwZHMrblhkc3JmbVhwdnlpSHlZczB5ZGJhRHlPMWFucDVVaHNlYmNZRC9lTCt3VW9IM0pCTTV0NWp2dTJLeDJ1S1M4VUJ1TmRWWEhXaGY3Y0hBL0NvZHFSMFNhR1ZWcE40UHFNR0xHL242VTVDTjFXQi96MUxBZHdBbW03UXJZdHk5U2VpempoaWxNNnU3aXVhN1VmbjBvQjdUODM0eTN1azhMb2xxZGdYSTBseFNENmVrK3FuUklFSDkzcHg1OUZJL3gySFFHVG9ZRitESkdPQjI0YTBMclpzZzZ0UVR0UkJHVGV0UzBGbFJjR3NXbjk5M29RUGUzUzA5b3RLanp2ajFMMkVQVEdLK0xFTzRRZTZtS3RwaFRoeFc4RFhoZndYYjhURGZNQXhBS3VrVXdNSHl2S1JEUG9KQ1J2dllJWHZRdnlIdEhRVzRxV1JYbkxqWW14N052SS8yb2tHRndkdE82T0RsY2Q0SDBNYzJiM3NWQW5XdEl4ZGEydFdIcFlOdGxqL2JoOFBNLzhSclJuaHdORHhGcitlQjJZTENHWnZGK0I0bkpxS08yc3pxTElORzQiLCJtYWMiOiJhMzk2Njk5MzVmZmNmMWI1YzAwYmJkMzJkZGIxZmQwZGMyOTY4MmJiM2EwMWQ5YTMxMDdhNTk4Y2M1NjE5Y2Q5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InR0NUZ6eTlhYjZCbzFHbXRjNG84UlE9PSIsInZhbHVlIjoiSkVqY0hjUGtRa2h1MkFrZUhOd3FrK1NlcFFLc0wwQk5Odys5WDZoY2hpUUVMenFjY3dpcHJ2N25URGJEV3RkUDZ5ZDBZQVJwTnpxU3d5UmY3bFBjT3h5MVU4UUNXWStBUXhaR3B5QWRETnBuNlNURFFzdXM2VDZzNXRkd1Q4YTNJTGtCZnN4RjluN2lnTFF5RlRSMzU0N0RhVUYwdTZIUWVkWW1pN1J2U05XS2hNNHFmTTNoWmQ0ZUdLOWtQaEhXbzJrbk9wemNQbUxYS1N3QTliam0vM25YTWVrY1lRdnNSUEtYTlI3ZXFDNDNSMEp3YlpRQ2pJUGdveUV6cTRndE5NSDROTnNTa1Q4L1RvRVlaS0E4a0wvblZEZWlVWGxjRkRMWHpUM2dSS1diM1NpNytvd2xFWFhIbCs4MjAzRGtzMDFHRHF5aXdPQTQvSDRSNDlWS0ZuTCtmTlFxaEVRbFMzMGtTV0doa3NPQ29HYlN0eGg5ZmNvSEZQYVh2cXFLZFUrVnJzR0laNjNDSEJ4cTd6bmZjYk10UTBIb0hUdU9yZWNxL1p4YUNZajNDVjdIRUVKZ0hWVG1EZXVQeGY2blBuR2VLaUNkTkVZT3o5U3RVQ1BCaTJ2bkNmSTlaOEh4S1BlTnhpTGw1bDBYUVVqejVvSks0UVY1anRTcmlUV2QiLCJtYWMiOiIwM2E5NzU4Y2VmYzIzNGVmMmQ3ZjQ3ODg4MTIyZGQ5NjViNmQ5MTRkZTAwNmUxMzVkYTEwNzM2ZGFjMmYxNzVkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-148443170\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}