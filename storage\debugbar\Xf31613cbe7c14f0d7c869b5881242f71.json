{"__meta": {"id": "Xf31613cbe7c14f0d7c869b5881242f71", "datetime": "2025-06-30 23:14:02", "utime": **********.330483, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.944977, "end": **********.330496, "duration": 0.*****************, "duration_str": "386ms", "measures": [{"label": "Booting", "start": **********.944977, "relative_start": 0, "end": **********.278668, "relative_end": **********.278668, "duration": 0.***************, "duration_str": "334ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.27868, "relative_start": 0.*****************, "end": **********.330497, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "51.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00281, "accumulated_duration_str": "2.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.306781, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.498}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.316328, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.498, "width_percent": 12.456}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3234608, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 72.954, "width_percent": 27.046}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C**********921%7C32%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZHVTBJNXArSFcwS3NpdVBSSGxnWWc9PSIsInZhbHVlIjoieVFUNmdWYkRJTStnOElNTUdaeUFwdHNobmZGSDg2RmZVMzd5MmIydHZWS0o2cGxnR0NXVEFpWjNVVDdObTRNZEJkNURabWZ2M2I4cUFmbFlzOVpWYjZyaGp0U24yZ08xTG9xcGFkY2dOYzdYWWx5a1hsbGR6KzQxZWVMbk8rYzk4UEZnSE44OTVqbDhmUlVvVVlZTWJhdkY0cGdrdEliK3F0eGRxbmNTOU9sYjdXR0dlMkgvaWRTYjlraDdoLzlFUjVndlNHRG9nM1pGQk5JYkdINFJHNjQrZjRBeVd5L0FOaU9aZzc4SDZhbndiN1pGU1pDYWpGMVRDYnZnbDlWdWFCN29RZ014MlRHOFJNcUp2WVFkWlRmbUc4QzFqSjcxaVdNdEE3UmdwbWh6Q2VyTm9JbWNVQ0xLbVB0UGkzZERTV0NwNGhTakxiQUt5eWFmRVc5RllQSUU1b2VRdmlwNnNuOGZlZVNxNDJQOW00UGVRSWtNa3Y0V0Y3bW1rblRzUFFrSUJNSC9ReS9wMDA4bWdHYkdaKzBQcHJTWGdidlcwakZNekFhaU81T0Q1M0lzMk1vRnRiOHNqQzFUMG1JSk5LelZQNjV2QjBGRGpOc3Z0RmtwVnNJU1pvUmxLVFFZbEwxRlNLYXhiZ2g2YW80WVJ2MUtFbnc2RGNkR2NVVjkiLCJtYWMiOiJjMGExMmEyMTZkYTU1OWVlODdkYmIyOWJjOTIwNTU4MjFmZjM1MGExYWVlOGRjZmM3YTA4NTdhOWMzY2U1ZGNjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdTYkJVSlAySHkyWDNlU3hOaHJQQ3c9PSIsInZhbHVlIjoiUHowdHg3RHlNS04zZm9NMW5qay84UHFzVWpZM2hqTWZ3dkZEN3VPRjNUL2drbExYWktmOTBYalM5QmdaRW43RmcrNnQvZTFVRmNEbUZ5cWJuN1FUTEM2NFh4WHVhSFdqRUcxWGhwcFUyZlBrNHVQYjlhMG5DRktIcTA1dGxtNEdZc3g4UmxCTXk3TW1OSTdMenRpWmhuQ0ozY3pTTVNFYUU4d3ZpVE1VR2ZQWFdUV3J1NFpQMi9jUkVJTm1pNDJMYnQ2U2Z2Y2YvZEs2ZC9hZFdJRWYwa3RQRnpZV2tFUXoreXZWV0VzTUQ1dVlKNWxoR2JiZEhlWXl1OGVTYm1kWWFSai8rOVNQYXMxS2NyUFhWMUE5WTVOVkNNSkVkeTQ2QW92eDlJOHFLUmtBbWYwazM4M25IS1pmTVVVdzA1M3JmcTgwS1U3UncwYTd4Z2NweCtlcStxSWU0T0FTQTlxZ2o5UzF2RFhaQmlCUFptT0FUZm9uanl3cTNxYzNWUElaaEwxTmxqWitjdFRWY2k1WVJ3QXNHQzZ3WHpKT1JqZTJFVFN6a2hhNXlMcVVHL0RlTW1IMURqa0hpelZiSEQ3dndrUjBhYU1zdkhkZ1E1QUNvRVc4Rzdmc0VDTzZyU0JXT2JQMHdWT3VEUi9hRnFpS1M0UFRJNHZ2S1VTYzVpbnIiLCJtYWMiOiJhNDQwOTJlYTVkMWIyY2MwMmE4NzdkNDk5MzEzZWQ5NGRhNDIwYzRmYjU1ODllZTI3OGVjMGE4YjYwNjYxM2I1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1424465352 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LzQkrwbVBtmiKyPqbktIsF6wiC8bftN1cyLybXUc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1424465352\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-778869331 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:14:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxOeUQxSWlVUGF5cEE2N2pIWVN5M3c9PSIsInZhbHVlIjoiWWxlcnhiYTEyZ3Jud044ajh2NlRRb3lpWUwwR210OUxPWjRMTWt2QlVXM2tqbHA5dVhVWWw4OWlYWS8vTStyVzVUT1J3a29ya3BwN0FhOUllRUF1ZlU3dFBoYXFISFk4ajJOaGFnd1hDN0g2ZHExd2tOeitzcmFMOFdwUHJMaDZZaFlZblJhZC9NRGhJV3NJYURyc0k3M1lxUXBUdmIyMnRMNzZtRDlBVG84SWFYcVJqWHY0QXI4aFBqQUpyQlYybEFvZHRFS3MvanRkdUVaZUZsSzN0dTNrdTFrd0lJZUsyUzVObnhjcndZNnlDaFdWcmZ6aXRyd3A3bW5sRHJzYTNuR2cxc3VwTk9YMSt4RjZ0blZMTkNVRDZDd0tSTVIwTlZjVVBDenU4Q0JETGdVMGRodFoyNTlIL0Q4MU5Na2lUVCsrKzBOMllBYlhNazlqa0pDQkdxTjZFYTBQVW91OG9uL0JtWk9YazJxc05KT05CRENCQVBYeFhiY2ZmRUtxa3lCWm9QZnZoSmI2Y1pOU2tFRXBGWWovTlRKZjJVWUFWemNoQ3Z1YmFZSS93Y2ppd28veEFmM0hROUF1eDQ0VWlsQ1REaDJuL3NRS2NVZGVMa3dId2loLzBuTzNmQ29sZ0E1K0ZmYTg5TVVJODRnQzFCbFVhTmZGSWo3eWZheHQiLCJtYWMiOiI5ODIzY2JjMjQzYWUwYjE1ZmQ4NDNiNjNjNzIwNzk2ZGZlNDAxYTYyNjQ4MGJjOTk4MTYxNTYwYTIyNTY2NTZhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InVQRVlWS1NrNEV4cmFONVdnSkU0MUE9PSIsInZhbHVlIjoicnlmZE1HTWszYmF1bGZiSE5SMzdrZHdQcS9TTFc5RUdxdGU4UFpMQWYzbVBvdGdwUFRCaE9aTXB3b1lHS1FIdEJRSVV6VnNUK1FtR0lEK1FzaWJzNlZOOHZwd2JVN2tJMFRmWEtCVERLeWppT21JcjVkZy9IWEpmaGcrYmU5eElEVWYrUm8wbjVIM2hWNHBhYnR6WW1SVVZOQmk5QnliWENnRFN0Y0NEMFpvRnJNbTRwQmU2azc4SnhMYmkvSFlqU0hJR2hGOHhjM2xnMHpXNDNmbW9udUhacUlBS3hLWFJuMCtXSzVxTjZ4S2N4c3gwbzZaTE11a0lScEd2eXZYbmNidlNjRVFWb0lXTE9FaTJkTFRUbjZhbmhIVzl3K2tXc1cySVNiaVVlY05hSXNhenFnRlBtYUtISFNac2ROT1hrQk82SkpxRWdIcXZoTGpDUWlJU0pNNkF0c0pEUDZUYi84WEhJTUoyWmNEN1N3cU9Zbm9OdVR3aUh1YThpcHBKaVNuTDE5ZmdiRGt5R0YwVDBJVzZOWXZ0M3A4QTE5VXowTldKK1ludTNhRHpuMXZaWHpZak9PY08xVTFKbXFpbWlkaC9MV1lkenoybkp1MUVhTFpucC9SM2RjWlNQNVlqUVVhUmRPT2xvK0tJZVVaRFdIaHliY2UvSUhQd0paa1ciLCJtYWMiOiIwOTEzYjNlOTgxOTcxMzhkZjQ1YjJlZjkwYzIxZDc4MTk0YjJhMzY5Y2EwYWM5YTk5OWZiYmJkYTIzNGUzODVkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxOeUQxSWlVUGF5cEE2N2pIWVN5M3c9PSIsInZhbHVlIjoiWWxlcnhiYTEyZ3Jud044ajh2NlRRb3lpWUwwR210OUxPWjRMTWt2QlVXM2tqbHA5dVhVWWw4OWlYWS8vTStyVzVUT1J3a29ya3BwN0FhOUllRUF1ZlU3dFBoYXFISFk4ajJOaGFnd1hDN0g2ZHExd2tOeitzcmFMOFdwUHJMaDZZaFlZblJhZC9NRGhJV3NJYURyc0k3M1lxUXBUdmIyMnRMNzZtRDlBVG84SWFYcVJqWHY0QXI4aFBqQUpyQlYybEFvZHRFS3MvanRkdUVaZUZsSzN0dTNrdTFrd0lJZUsyUzVObnhjcndZNnlDaFdWcmZ6aXRyd3A3bW5sRHJzYTNuR2cxc3VwTk9YMSt4RjZ0blZMTkNVRDZDd0tSTVIwTlZjVVBDenU4Q0JETGdVMGRodFoyNTlIL0Q4MU5Na2lUVCsrKzBOMllBYlhNazlqa0pDQkdxTjZFYTBQVW91OG9uL0JtWk9YazJxc05KT05CRENCQVBYeFhiY2ZmRUtxa3lCWm9QZnZoSmI2Y1pOU2tFRXBGWWovTlRKZjJVWUFWemNoQ3Z1YmFZSS93Y2ppd28veEFmM0hROUF1eDQ0VWlsQ1REaDJuL3NRS2NVZGVMa3dId2loLzBuTzNmQ29sZ0E1K0ZmYTg5TVVJODRnQzFCbFVhTmZGSWo3eWZheHQiLCJtYWMiOiI5ODIzY2JjMjQzYWUwYjE1ZmQ4NDNiNjNjNzIwNzk2ZGZlNDAxYTYyNjQ4MGJjOTk4MTYxNTYwYTIyNTY2NTZhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InVQRVlWS1NrNEV4cmFONVdnSkU0MUE9PSIsInZhbHVlIjoicnlmZE1HTWszYmF1bGZiSE5SMzdrZHdQcS9TTFc5RUdxdGU4UFpMQWYzbVBvdGdwUFRCaE9aTXB3b1lHS1FIdEJRSVV6VnNUK1FtR0lEK1FzaWJzNlZOOHZwd2JVN2tJMFRmWEtCVERLeWppT21JcjVkZy9IWEpmaGcrYmU5eElEVWYrUm8wbjVIM2hWNHBhYnR6WW1SVVZOQmk5QnliWENnRFN0Y0NEMFpvRnJNbTRwQmU2azc4SnhMYmkvSFlqU0hJR2hGOHhjM2xnMHpXNDNmbW9udUhacUlBS3hLWFJuMCtXSzVxTjZ4S2N4c3gwbzZaTE11a0lScEd2eXZYbmNidlNjRVFWb0lXTE9FaTJkTFRUbjZhbmhIVzl3K2tXc1cySVNiaVVlY05hSXNhenFnRlBtYUtISFNac2ROT1hrQk82SkpxRWdIcXZoTGpDUWlJU0pNNkF0c0pEUDZUYi84WEhJTUoyWmNEN1N3cU9Zbm9OdVR3aUh1YThpcHBKaVNuTDE5ZmdiRGt5R0YwVDBJVzZOWXZ0M3A4QTE5VXowTldKK1ludTNhRHpuMXZaWHpZak9PY08xVTFKbXFpbWlkaC9MV1lkenoybkp1MUVhTFpucC9SM2RjWlNQNVlqUVVhUmRPT2xvK0tJZVVaRFdIaHliY2UvSUhQd0paa1ciLCJtYWMiOiIwOTEzYjNlOTgxOTcxMzhkZjQ1YjJlZjkwYzIxZDc4MTk0YjJhMzY5Y2EwYWM5YTk5OWZiYmJkYTIzNGUzODVkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-778869331\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1551865514 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1551865514\", {\"maxDepth\":0})</script>\n"}}