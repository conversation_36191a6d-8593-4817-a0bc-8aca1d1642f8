<?php

namespace Database\Factories;

use App\Models\FinancialRecord;
use App\Models\Shift;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class FinancialRecordFactory extends Factory
{
    protected $model = FinancialRecord::class;

    public function definition()
    {
        return [
            'opening_balance' => $this->faker->randomFloat(2, 500, 2000),
            'current_cash' => $this->faker->randomFloat(2, 100, 1000),
            'overnetwork_cash' => $this->faker->randomFloat(2, 50, 500),
            'delivery_cash' => $this->faker->randomFloat(2, 0, 300), // Now used for deferred amounts
            'total_cash' => $this->faker->randomFloat(2, 1000, 3000),
            'deficit' => $this->faker->randomFloat(2, 0, 100),
            'received_advance' => $this->faker->randomFloat(2, 0, 200),
            'shift_id' => Shift::factory(),
            'created_by' => User::factory(),
            'updated_by' => User::factory(),
        ];
    }

    public function withDeficit()
    {
        return $this->state(function (array $attributes) {
            return [
                'deficit' => $this->faker->randomFloat(2, 50, 200),
            ];
        });
    }

    public function withDeferredAmount()
    {
        return $this->state(function (array $attributes) {
            return [
                'delivery_cash' => $this->faker->randomFloat(2, 100, 500), // Deferred amount
            ];
        });
    }

    public function balanced()
    {
        return $this->state(function (array $attributes) {
            return [
                'deficit' => 0,
                'total_cash' => $attributes['opening_balance'] + $attributes['current_cash'],
            ];
        });
    }
}
