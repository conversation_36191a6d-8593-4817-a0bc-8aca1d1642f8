{"__meta": {"id": "Xec132c78351c46c5471d9f3d239b5eb0", "datetime": "2025-06-30 23:12:39", "utime": 1751325159.149973, "method": "GET", "uri": "/customer/11/edit", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.372453, "end": 1751325159.149986, "duration": 0.7775330543518066, "duration_str": "778ms", "measures": [{"label": "Booting", "start": **********.372453, "relative_start": 0, "end": **********.717801, "relative_end": **********.717801, "duration": 0.3453481197357178, "duration_str": "345ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.717809, "relative_start": 0.3453559875488281, "end": 1751325159.149988, "relative_end": 1.9073486328125e-06, "duration": 0.43217897415161133, "duration_str": "432ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51984480, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "1x customer.edit", "param_count": null, "params": [], "start": **********.795908, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\resources\\views/customer/edit.blade.phpcustomer.edit", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fresources%2Fviews%2Fcustomer%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "customer.edit"}, {"name": "1x components.required", "param_count": null, "params": [], "start": 1751325159.078261, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.required"}, {"name": "1x components.mobile", "param_count": null, "params": [], "start": 1751325159.07957, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\resources\\views/components/mobile.blade.phpcomponents.mobile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fresources%2Fviews%2Fcomponents%2Fmobile.blade.php&line=1", "ajax": false, "filename": "mobile.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.mobile"}]}, "route": {"uri": "GET customer/{customer}/edit", "middleware": "web, verified, auth, XSS, revalidate", "as": "customer.edit", "controller": "App\\Http\\Controllers\\CustomerController@edit", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=175\" onclick=\"\">app/Http/Controllers/CustomerController.php:175-191</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.00517, "accumulated_duration_str": "5.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7493582, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 42.747}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.760071, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 42.747, "width_percent": 12.573}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.773617, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 55.319, "width_percent": 11.219}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.7758262, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 66.538, "width_percent": 8.511}, {"sql": "select * from `customers` where `customers`.`id` = '11' limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\CustomerController.php", "line": 179}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.780688, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:179", "source": "app/Http/Controllers/CustomerController.php:179", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=179", "ajax": false, "filename": "CustomerController.php", "line": "179"}, "connection": "kdmkjkqknb", "start_percent": 75.048, "width_percent": 6.383}, {"sql": "select `custom_field_values`.`value`, `custom_fields`.`id` from `custom_field_values` inner join `custom_fields` on `custom_field_values`.`field_id` = `custom_fields`.`id` where `custom_fields`.`module` = 'customer' and `record_id` = 11", "type": "query", "params": [], "bindings": ["customer", "11"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/CustomField.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\CustomField.php", "line": 63}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\CustomerController.php", "line": 180}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.782459, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "CustomField.php:63", "source": "app/Models/CustomField.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FCustomField.php&line=63", "ajax": false, "filename": "CustomField.php", "line": "63"}, "connection": "kdmkjkqknb", "start_percent": 81.431, "width_percent": 10.251}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'customer'", "type": "query", "params": [], "bindings": ["15", "customer"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\CustomerController.php", "line": 182}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.784452, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:182", "source": "app/Http/Controllers/CustomerController.php:182", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=182", "ajax": false, "filename": "CustomerController.php", "line": "182"}, "connection": "kdmkjkqknb", "start_percent": 91.683, "width_percent": 3.868}, {"sql": "select `name`, `id` from `warehouses` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\CustomerController.php", "line": 183}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.786135, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:183", "source": "app/Http/Controllers/CustomerController.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=183", "ajax": false, "filename": "CustomerController.php", "line": "183"}, "connection": "kdmkjkqknb", "start_percent": 95.551, "width_percent": 4.449}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-262455160 data-indent-pad=\"  \"><span class=sf-dump-note>edit customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">edit customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-262455160\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.779691, "xdebug_link": null}]}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/customer/11/edit", "status_code": "<pre class=sf-dump id=sf-dump-1762043945 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1762043945\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-848260715 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-848260715\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-133352878 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-133352878\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-865237530 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325154748%7C23%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJwYWdLcFM5TnNxeHdMOU9JemRIYlE9PSIsInZhbHVlIjoidkNUbWhiVTV3c0hUM2lrVWZaaWhBZjdqMkdxU3NrOVFBRUJ1S1RteHVad0VCUjhvUVlzUGZsR0xtZkhibTNwNlg1NmdrUFNleUk3K3lwQXNnTno4OW14RFFTN3VFaDhOMGV0V0duR2JncUxyMXpnemlsc1p3NFhmZzMwRGRvNnJmeUVYOGtRYk1IUnAzaE02eVE2U1NQMENSTURmcWF3bGFQU3JzbDJ2dWZQRHE5OGl4SFg2MVBLc1lqSThsRmpZR1dtY0Z2L2ZTMkl1bHl6ZHBlbTltdFdhcEpXT05vbXZtbElpa3h3TkxJWmEzdFozTDNxRThLVmgwTnFRbEtzTWpvVDEyd1E2OFJoaUlTV0F6Z0RaL2lETGN2eGNodlJpWFlORDdFOWFtL282bHNrd0RqeS9jZ21PaXpDRTNnZ0JudERsYjJJTUkyZmhudGU4VGRkamczUVJTUUdHQnFvTU5OQjhISE9WaElUNXg4VURoN1Zpd3dXaER1ekpsQWMreTViQWRDdUMzTHFMUGpyK3FlOHIvREpPZjB2d2VKU0Ywc2VEZmRZN1NWRWZiZmFyU1lscFhLWHlnVmJseEFGUTRRNkJNSGNPbnRKeUsyK1VlYTR3a05zSDNjUWlYTDZNNFBid1F1NERQWDJMaklzd0NPQ09FVklWTmI5SEpCQ20iLCJtYWMiOiJmMTMwNTNkOGE4MDcxNjNiNmYyNjZjODExNWE0YjEwZTVkODdjMGEzNTJjNmFkMDg3YWMxZjlkNmZiZjhjMTVkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im9OU3BUOFhkRWhMSHRmaHB3RnFMWWc9PSIsInZhbHVlIjoiT3ZqWFJTVFFVY2w5Z09zdldDK1hsL3dmZ3pZclgxTWlYOGFHaUhKMTRicGFSdDZ4QkV5SCt6TUhjWkVNQVlMdy96OVhVbmJ6VjNsdnV2alAzVW1nQWs5RkVyZnQyUDkyM3FHb1JCdjkvdlQ3SlQ4ZlBJVWgvRldQclk1SlFRUEJ1L1M1U0EwOGNmT0NxSjMrcmFoUitGQVF0Vk1JbnJuVkxzRDZGTDYrRVZmV3dVTVE4bkRKVDhiam9wUHcweE1uak5QT0ZNVm1xcm5lcDIxYVR4cnRUN2VoMWwvUys5Sk5GYm1tZ25uSy9hSHhMOUdKUVcxS0I2R1pYL2ExRUwzZjlxZm1URnZNcVNFeG1pSjlySU41UlRHRW1hOHRvOWNibWRPdlhtTmdCSnVSRnkzNndidnV3MkJmeldJUWhqVStkT2xuUHNOUE1zd2lNWHhZUiticHoyWFZqVFBWM21XMmhhcThUVEw4V3JWVyszM1c0NmdZSks0MjNVblVQd2NOYXNzeldUd09URGNDS1JoeEc5aW5hTXhrUG1ibUNYSTMvb25CUmtCVGFnWjl2QUZrUjhtSzhtQmR0QmpoSFBjakEzb3F3MVpLRC9RQmlJd1I5ZjVWSFBnUWpvWHpiQlcrNzNzcWdwcHNOUkd2VzBCZTFSVE1Ec3AyNStxRDBUei8iLCJtYWMiOiIzZTQzYzAzNWMxOWM0MjA2MWZmNTk5ODA4ZDk4ZTQzZjdlOGY0ZGM1ODhiOWIwZmY5ZjQ3YjJkZjA0ZTJjZTQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-865237530\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1278907890 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0M1K0sdNadPD2LVrnt8LmBw227nA9F1U5e3ROaJK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1278907890\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-546186676 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:12:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVLbXZQYjJ3dDRYNTJ5RmdKUmxXREE9PSIsInZhbHVlIjoiSFVoS2FGaERKT1dRU1djSFkra1Y2OFh2bUJGdEI2eC9ScjFjR2JRQzlPclhZWWcxOGpNc1huMVlPVFJNemUwU1AvbnJscGpxcmhEZjBGN3VXQytGSFpTMGE0d3lhTElKZURtSFJhMDB6VWdRRmR2ZWVzVTU3TFBJaU92cURFMkRvdWE5VEF1VXo1T3ZwMzVCVkVrRWVmanpKSy9nN2I5ZjJpTzlyVnZEZG9IOWp1VXRLMHhIRkJad0tFYS9WNzlYR2I2a2d0aCt3U0FmZmo3RG9lMkFJMVB2K21xMm5BZ3V5bDAyZEtzSnpqNFFZTWV0bENyTFlIalZxTXNPdzMvNTZOWS92ck8razFCbDRkZXVSZjFXS2VTMVlSL3QyU2lubmFURmFuazgySFhZdjZBTDNhdkJSN3h0aHNpd1RjNDBUbHludE1FM3BIZG1IVmlEYndaOTI1di9aVitlb1FPeUEzRE5FdzZBZjM1U01OTlBqMGNxWVlDcGtnZFhKSXFKd0kwVDMvREJkVjBTczUzOXZJY3FQcklvdDNBOUtiUFJBYTNPUDRPMmMvRStVclBpaGlSdy9iaktSTVo5dTNkemFUZ0UwVHFPL2F1YS9XbG5zL05zMHNFamh2MmczRFlEMnNyVUx2Q2VLZXZ4clpSMWcydkFhdUZBQlB4ckE5dm4iLCJtYWMiOiI4ZjhiZWYyMmVhOWIzY2MzNjE5ZTJlNDhkY2FmZmE0ZGUzNmU5NmM3MDk1NjE5MWNjMmY1ZjE3YmRhNmY1ZmNiIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:12:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im1qeU1VYjBmOU9tM3phMGtkNkw2T1E9PSIsInZhbHVlIjoiTExvU3ZqbGJwQ3Z0cDkxVEMrM20vL3VEbWd2N0FzcXBndjJZSDA2ak9jRFNFNTQ1cmxmd3RSTW9xc0FMQVpkVXVld2NTcGpyeHM0SGJObnRnM29MQnpQTnJHRDhFNkdxN200ZW1tblJJaHFxYnFmWjZ3MEN3eDcvc1dBaGdQU2hmb1Nwbml6ZnJOTStQbHkyM2JYZUZRSWRKZUlrZnZEU21TcVYxUFZpWkJDU0VDVUl4NFRmMHdDR2tTNU5TQmIzOUIrMXpYQlhJRXVVY3ZrMWRWUHlYblZ1dCtSVC9lRmptZ1JDeGQzckhRYkVwQWpLKzVXaGZ1b0VmTVd4VGc4b2FVbHpiQXZzMTNwR0xGWUhYMDNkamN3WHowUlltcno0Y1djYzdkSHdLNzZ5UTlHclNDUHZKZjFQSzVxaWZ0OTh3amcvemZVRkp5MUNLZWV1cjNnMjdkeGpyY2FoQ1ZSOXNPb1M2VmFJVjc3SE5adW1Wbk5WekNYeUpFL1dMNjlhMFlMY0pIcCs1N3FUZ1pEamNBcW94b2gwdGUrTCt1UTlJVUdJRlAyOXRqdThWUlp0L1ZKOGJjdThRWFdrUDU2QU04bmdEbUpSdmtFZXA0MGlTNnM1MXhFSmd5MTd4VjVDWDBxcUJMbkMwaXZzZVB2Y1lGWDF3eUJtMVJFMHdaTEsiLCJtYWMiOiI5OWQ2NDhlZTY4ZDg3MTRjM2VhNTIzYTQzZjllYzNiNmUzNjEwNWU2ODYxMDVmYjcxMzg3Y2IxMWJkNTEwNjZlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:12:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVLbXZQYjJ3dDRYNTJ5RmdKUmxXREE9PSIsInZhbHVlIjoiSFVoS2FGaERKT1dRU1djSFkra1Y2OFh2bUJGdEI2eC9ScjFjR2JRQzlPclhZWWcxOGpNc1huMVlPVFJNemUwU1AvbnJscGpxcmhEZjBGN3VXQytGSFpTMGE0d3lhTElKZURtSFJhMDB6VWdRRmR2ZWVzVTU3TFBJaU92cURFMkRvdWE5VEF1VXo1T3ZwMzVCVkVrRWVmanpKSy9nN2I5ZjJpTzlyVnZEZG9IOWp1VXRLMHhIRkJad0tFYS9WNzlYR2I2a2d0aCt3U0FmZmo3RG9lMkFJMVB2K21xMm5BZ3V5bDAyZEtzSnpqNFFZTWV0bENyTFlIalZxTXNPdzMvNTZOWS92ck8razFCbDRkZXVSZjFXS2VTMVlSL3QyU2lubmFURmFuazgySFhZdjZBTDNhdkJSN3h0aHNpd1RjNDBUbHludE1FM3BIZG1IVmlEYndaOTI1di9aVitlb1FPeUEzRE5FdzZBZjM1U01OTlBqMGNxWVlDcGtnZFhKSXFKd0kwVDMvREJkVjBTczUzOXZJY3FQcklvdDNBOUtiUFJBYTNPUDRPMmMvRStVclBpaGlSdy9iaktSTVo5dTNkemFUZ0UwVHFPL2F1YS9XbG5zL05zMHNFamh2MmczRFlEMnNyVUx2Q2VLZXZ4clpSMWcydkFhdUZBQlB4ckE5dm4iLCJtYWMiOiI4ZjhiZWYyMmVhOWIzY2MzNjE5ZTJlNDhkY2FmZmE0ZGUzNmU5NmM3MDk1NjE5MWNjMmY1ZjE3YmRhNmY1ZmNiIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:12:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im1qeU1VYjBmOU9tM3phMGtkNkw2T1E9PSIsInZhbHVlIjoiTExvU3ZqbGJwQ3Z0cDkxVEMrM20vL3VEbWd2N0FzcXBndjJZSDA2ak9jRFNFNTQ1cmxmd3RSTW9xc0FMQVpkVXVld2NTcGpyeHM0SGJObnRnM29MQnpQTnJHRDhFNkdxN200ZW1tblJJaHFxYnFmWjZ3MEN3eDcvc1dBaGdQU2hmb1Nwbml6ZnJOTStQbHkyM2JYZUZRSWRKZUlrZnZEU21TcVYxUFZpWkJDU0VDVUl4NFRmMHdDR2tTNU5TQmIzOUIrMXpYQlhJRXVVY3ZrMWRWUHlYblZ1dCtSVC9lRmptZ1JDeGQzckhRYkVwQWpLKzVXaGZ1b0VmTVd4VGc4b2FVbHpiQXZzMTNwR0xGWUhYMDNkamN3WHowUlltcno0Y1djYzdkSHdLNzZ5UTlHclNDUHZKZjFQSzVxaWZ0OTh3amcvemZVRkp5MUNLZWV1cjNnMjdkeGpyY2FoQ1ZSOXNPb1M2VmFJVjc3SE5adW1Wbk5WekNYeUpFL1dMNjlhMFlMY0pIcCs1N3FUZ1pEamNBcW94b2gwdGUrTCt1UTlJVUdJRlAyOXRqdThWUlp0L1ZKOGJjdThRWFdrUDU2QU04bmdEbUpSdmtFZXA0MGlTNnM1MXhFSmd5MTd4VjVDWDBxcUJMbkMwaXZzZVB2Y1lGWDF3eUJtMVJFMHdaTEsiLCJtYWMiOiI5OWQ2NDhlZTY4ZDg3MTRjM2VhNTIzYTQzZjllYzNiNmUzNjEwNWU2ODYxMDVmYjcxMzg3Y2IxMWJkNTEwNjZlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:12:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-546186676\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-107918898 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-107918898\", {\"maxDepth\":0})</script>\n"}}