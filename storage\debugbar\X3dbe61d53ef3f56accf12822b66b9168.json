{"__meta": {"id": "X3dbe61d53ef3f56accf12822b66b9168", "datetime": "2025-06-30 22:36:48", "utime": **********.044586, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323007.548531, "end": **********.044602, "duration": 0.49607086181640625, "duration_str": "496ms", "measures": [{"label": "Booting", "start": 1751323007.548531, "relative_start": 0, "end": 1751323007.963841, "relative_end": 1751323007.963841, "duration": 0.4153099060058594, "duration_str": "415ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751323007.963851, "relative_start": 0.41531991958618164, "end": **********.044604, "relative_end": 2.1457672119140625e-06, "duration": 0.08075308799743652, "duration_str": "80.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45709576, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00325, "accumulated_duration_str": "3.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.004443, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.308}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.015791, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.308, "width_percent": 20.615}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.022442, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.923, "width_percent": 19.077}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1905996141 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1905996141\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1888604992 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1888604992\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-90028845 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-90028845\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1526631994 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=6nrx0y%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1aj6s00%7C1751323003065%7C4%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlkblBCTVpjeEdISFZ1TXNXM2VPcEE9PSIsInZhbHVlIjoiNzh5cnp2RTNsN1l4em85Qkh2bG10ZDNNZUpKNi9xbE5CeXUyTkpKQ1JjSE5YcFAyRkI4WWh4ZWkvcGFxNi9FOUZKVEFvRTNjSU4yY1VSNkRHMnJ1OG9SZ1NKeUFDSkUxMklqQ1p4M25oa0grV3hjVWJndUF1cHlUaWFxZzNMdDRQanpneStHZzdnTTNNV0RqQW5NNi9TYWRXMzJTMld2ZHkvNzhOaDAxUW9kWHFFdFoxc25wSWdaNk5LYWlzYjQvOEtVblY5aU9pamZGVHRnTFJXcEw0MlVqdXhaVWYwbGhSR2h5Nmw4WFU4dnJuemhWSlRFUFFUT09melBUaVdVdTQrNEgwcUpzZGRDbyt2WkUrM1pqWCtLdW9zWW85UjlVQWwyOENBYURvMUJlNXhDcUltOEtwYXVOQUVkb0c5OTJOVGtjRi9KTW9RVmJiNU0rUmFMQlJBZXBRSTBGa1BhVUZWbk51MHJ3RUlPbzQ1Y3JNbEE1c2t4d3V3VEZDVERQSjhtM0FWTWtQUkJHaVAveW1BeEp1cThPMlI0Q0tYelErbFdvVHY2SXpKVUtyUW02dnJuNU5NTXVxVzNlS0pxbG5XY0VmU2FUYU5QazlKS0xFeExYdnREbS8rVk1Uem9YN3psTmNzd3dFQVdFQzN1djBYR3c3bTd4NG53YXpHTngiLCJtYWMiOiIzNWY2YWE3Y2YwY2E3YTg5YjI3MDg5ZGQ4YzZlNGIyNWZhODFhODJiNDhhNTNkNTRiYzBhMjBlMmZjYTkxMTM4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ilp5Ui9KdWpkb2dTUlJKcmZWL2lSMnc9PSIsInZhbHVlIjoiaTkvWDZQZkVwS2ZXQlM2SW1ZMXZMODJOZytwYmpDaGQxZVNnREViaDdPOFVsTnp4R2xHQlpRcGlzTFJQT1ZFZHkwU3NOc2hsNW0vZDVwdHlGZnRsUmlVbCtMQ0oxWnNwcmVtTVVrdDdLZ0laL0E2V2ZMczZCUzJuYWNram8yUWdLUGJoUXZCQmVHVHVXNXhMdTkxN1NiQy81Y1ZoV2kyQW8xbTZJNTVtdXJZY3RHcUxtNmtVVmQ4ZFE0L3VPclpzUmFrNHUvNUlNZlNoOFN5YmVvcHlieVhNZ003dE90OHh4dXpZcFVkS0NJSHJ0QzFiNG5DUFBtYUU1NzVaT0Z1N3Myd3F2Nmo4c1FnSFdWMnN4c3JCR0kxWlR3UkFtTU9uR09UNDZ0SlFqTWN6ZTA3V2FjQUphZE9ocnFmSXZaTlhqT2NOZDlHY0gvZzJZNnJJQ3ZTR3dQMGFoOWtJMHFLTTdFUExFdWtmeGFtTkg2NmhYYUpRWWZZN1ZteSszS0dUUjZnaEJvaWo0Y1k3YmhOTDBUQ2VNMjMwNEF4U0I4czFORTVQY1hmOTRmMlZWN0tYMktoaTIrTlZiNXVrM3h1TDdaaTNzblV6UEZUMm5ZbGk2OGxkL3RVLzZLK1lDdVRTamhLVmhacVF1bXJ3TEdnRVdETzZTT0FqdEF4NXRJQjYiLCJtYWMiOiI0MjdhMGU2MzQwZmNhNzE1M2YwMWFkODI1YTI5OGEzY2EyOWJkNGJkZTI0OGZlZTA1MGYyYWRhMDE2ZmJlN2Q5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1526631994\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1786331619 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A9rdyCRm7SKpfljuMnVO7IpcgTBMITMjowAdsmJo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1786331619\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:36:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1hSTNPQ2FxbXVBNmhsK3hhL1picmc9PSIsInZhbHVlIjoiTjBmMGxlZERSWUpnRWhLczE4eXJkby91dkFYdDZvRmpvVXowQlZDbklCU3dRWUR5UlpiSGdGRnl2dmZQdU5YdlJiMjFYM004R1RtcVBuSEpOWGFiWFg3dW9OK2tlWk8zdDhGSVNMOWNhUTh2S0ZLWXp1Yk1KazRjNU4zSUwzcEhBclR1cEpSb0l3NHBpZG90Um90NkhKM2RtL0I2UkZWempsZnVMbTBTUGQ1TnY4OHJaSC9ob2xTMm9wWXF1cmFISGVIMzIvejlXMFlBMUNsbGxXOUtMNFUxWEovQW9oOUJEcUk4Y1hIenNYOWJmUkpzenB4M3BSYXpib0xuYkJUeVQwb2RhSHk5dnByT0FvZGdtdzRjb3lJQmRoakx2RnZlaGJGbDB4ZjVzZ2x0RFhRVGFMUS9IbWVYN0FzNjRNVVRRY0NEeU1aczBYSXZvVUNKTndjbTJKWnBVRXZ5c0xlK2kzQzNwNXZpN0Z2WjhaYmJrR1I2WUdrZTRlQ3pCZkxsTEdWY2Y2ZkNEcmhROUUzUll3NHFjNzczUDZYUWk4M2xJN3N6RyszdktkL0F2OXVqN2Noa3lFM0phT0Z0VGp0dXgwWjY2bTNBanhKUXFSSzVMVzZZek9aRlh6M2x5KzFqMEFzc293VE5jc0pSU0ZNOXJTQzlmWUJQYmVPczVvbmciLCJtYWMiOiIzZmIyN2EzOTQyZDA2NWRlOWFhNWU3Zjk2ZTZkY2UwOWM4MDkxMDc0ZGYxY2I0MTkyZTIwMTNiMDQ1OTM3MWQ1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZlRlkrU1VyMms5bXRST3l1Wnd2cXc9PSIsInZhbHVlIjoiTDc5RlpuZTIyVk96N1hkaXJSWUlSUXNGWElkRWF3KzR3ajlPT0Zmd0FmY0R3Rm9sNDF5dE5xeStuRjlveW84MVhteU9YTkNJKzlFcUlFLzVrcTl5cnM1TzUxTHV6YndNeEJIcUpVUXdWMWFWblJNN3RYdkdUS09wQVJ5ZFNqS0o0N3YxVG1zenU3S3FudlBNcTR6NVJqc3ZKbFlUOFVSR1gwTXFOTFZnY1EyVm1VOTE5MUtMRHNYL3VBa2VyMHJrZVZkblBpUEJvaVVsQTE0YzF3QVk3UXRGdzZLU00wTUxEVE5ZSXRVMzVWbkRaUWdnMTJzbXE0WkFwT29DRm9MSkpxM04xNjJkbjFNeUZQZkwzeTZEYkF5V0lGV1FnVFBoSFpCdENYUHJZWWMzODBkQm5vcEx0b205bjZJakkyN3pDOEwrbStubjY4OEFFTEhZVVRJdVo4K1ZMWCtORG12SG9SOFhFSEJ6OXFKcWZVTXBBU0JLU2xqekNiZk5ENjY1WEV4KzAyNDAzZjgybFV5NHpqUWhvekliR1E5U0plUXVSVGxvU1RhQXRiY1M1aEVOTW5ZREl6QmczZGxiZjJEZWlhSC9EWDN5R2dSVzd2ZTliYmw2dlIzN1FKVkJlRnc3Z2hJM0hod3JtdURtblhwbjMycG5tcStxUzZ4WW5uR20iLCJtYWMiOiIwNjkzOTdhMzFkZDQ2NDE1OGRhOGRhMjAyYzM4NDY4NGVkNTVmNDNhNzQ2N2YyMWE2Y2NlYmY2MjRlZDg1MjhhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1hSTNPQ2FxbXVBNmhsK3hhL1picmc9PSIsInZhbHVlIjoiTjBmMGxlZERSWUpnRWhLczE4eXJkby91dkFYdDZvRmpvVXowQlZDbklCU3dRWUR5UlpiSGdGRnl2dmZQdU5YdlJiMjFYM004R1RtcVBuSEpOWGFiWFg3dW9OK2tlWk8zdDhGSVNMOWNhUTh2S0ZLWXp1Yk1KazRjNU4zSUwzcEhBclR1cEpSb0l3NHBpZG90Um90NkhKM2RtL0I2UkZWempsZnVMbTBTUGQ1TnY4OHJaSC9ob2xTMm9wWXF1cmFISGVIMzIvejlXMFlBMUNsbGxXOUtMNFUxWEovQW9oOUJEcUk4Y1hIenNYOWJmUkpzenB4M3BSYXpib0xuYkJUeVQwb2RhSHk5dnByT0FvZGdtdzRjb3lJQmRoakx2RnZlaGJGbDB4ZjVzZ2x0RFhRVGFMUS9IbWVYN0FzNjRNVVRRY0NEeU1aczBYSXZvVUNKTndjbTJKWnBVRXZ5c0xlK2kzQzNwNXZpN0Z2WjhaYmJrR1I2WUdrZTRlQ3pCZkxsTEdWY2Y2ZkNEcmhROUUzUll3NHFjNzczUDZYUWk4M2xJN3N6RyszdktkL0F2OXVqN2Noa3lFM0phT0Z0VGp0dXgwWjY2bTNBanhKUXFSSzVMVzZZek9aRlh6M2x5KzFqMEFzc293VE5jc0pSU0ZNOXJTQzlmWUJQYmVPczVvbmciLCJtYWMiOiIzZmIyN2EzOTQyZDA2NWRlOWFhNWU3Zjk2ZTZkY2UwOWM4MDkxMDc0ZGYxY2I0MTkyZTIwMTNiMDQ1OTM3MWQ1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZlRlkrU1VyMms5bXRST3l1Wnd2cXc9PSIsInZhbHVlIjoiTDc5RlpuZTIyVk96N1hkaXJSWUlSUXNGWElkRWF3KzR3ajlPT0Zmd0FmY0R3Rm9sNDF5dE5xeStuRjlveW84MVhteU9YTkNJKzlFcUlFLzVrcTl5cnM1TzUxTHV6YndNeEJIcUpVUXdWMWFWblJNN3RYdkdUS09wQVJ5ZFNqS0o0N3YxVG1zenU3S3FudlBNcTR6NVJqc3ZKbFlUOFVSR1gwTXFOTFZnY1EyVm1VOTE5MUtMRHNYL3VBa2VyMHJrZVZkblBpUEJvaVVsQTE0YzF3QVk3UXRGdzZLU00wTUxEVE5ZSXRVMzVWbkRaUWdnMTJzbXE0WkFwT29DRm9MSkpxM04xNjJkbjFNeUZQZkwzeTZEYkF5V0lGV1FnVFBoSFpCdENYUHJZWWMzODBkQm5vcEx0b205bjZJakkyN3pDOEwrbStubjY4OEFFTEhZVVRJdVo4K1ZMWCtORG12SG9SOFhFSEJ6OXFKcWZVTXBBU0JLU2xqekNiZk5ENjY1WEV4KzAyNDAzZjgybFV5NHpqUWhvekliR1E5U0plUXVSVGxvU1RhQXRiY1M1aEVOTW5ZREl6QmczZGxiZjJEZWlhSC9EWDN5R2dSVzd2ZTliYmw2dlIzN1FKVkJlRnc3Z2hJM0hod3JtdURtblhwbjMycG5tcStxUzZ4WW5uR20iLCJtYWMiOiIwNjkzOTdhMzFkZDQ2NDE1OGRhOGRhMjAyYzM4NDY4NGVkNTVmNDNhNzQ2N2YyMWE2Y2NlYmY2MjRlZDg1MjhhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-472100102 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-472100102\", {\"maxDepth\":0})</script>\n"}}