{"__meta": {"id": "Xbec8d355b39cfc96b5a43fbf451960d7", "datetime": "2025-06-30 23:13:29", "utime": **********.610215, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.171569, "end": **********.610232, "duration": 0.****************, "duration_str": "439ms", "measures": [{"label": "Booting", "start": **********.171569, "relative_start": 0, "end": **********.526378, "relative_end": **********.526378, "duration": 0.****************, "duration_str": "355ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.526387, "relative_start": 0.*****************, "end": **********.610235, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "83.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022519999999999995, "accumulated_duration_str": "22.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.552289, "duration": 0.015619999999999998, "duration_str": "15.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.361}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5818498, "duration": 0.005849999999999999, "duration_str": "5.85ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.361, "width_percent": 25.977}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.597476, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 95.337, "width_percent": 4.663}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C**********071%7C27%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhSUXVham9mcVZZNFJzRlNSUENjVnc9PSIsInZhbHVlIjoiMnF4QWNzYjNIeDRlK09SQUxwYWZDUGx4d2JuN1E5MUpMbXBOZlppdGNGRThiOGhIdTBPUG9mOHVJVTBlV2ovQXg3eWdtQTJmVEZQWmhRbU45Z0w2dTZFa3JPelRQSUZBTUQ4cmFvWTRFRStoQTBwci9RTFc4U0puSElid3FhYVg1aktsbElvOHZBSS90VGI4RHk5MktIRzNPMEFtOU5zZHl0UTlUQ0RYSk0wOGxQd1l3YjRrU2MzYmNBRFl6NXJtZ3lZTE50YnVGNG1RdkZKTk9NTG5DVW56aVBoZE90TlV6KzVPL1lvRERCeW1iQnZRRWx6OStES3lEYldjNTM3WVY2Tnh0cXAyamJQSGxDeW1uR2dMWnhlWExNVWVIZ21iNnBVUjhHc2tHVzZudTZHemk1TzI0WkRXdXZDQ0Nyc2J4ZmNrNjZkWFlqc0hjQzkvODRQbTVDY0hVUWNKeUFpNTBYMlhvUEVxSmZZd2xHRUJvb0gwcFJpd1IwQzFwVXV2R3o5Y21sbElIeGJPbEVBeWFBR3VBY1h2SmMvVDAxOE5zMjZhenNpeVl0aGZ4TSsvbWRQUVRWNFRPYnVhcEtNTUlCYXNTTTM1WVIrRmFIcjFEMWNoTzMwZkxSRW5ZdjV3RndwTmdpMTYvSmxyMW55MFEzZjRyNkc1RG5NaEUxdjYiLCJtYWMiOiIwNzViMjY2YTUzN2M3MDNjNWE1NjlmNzNhMjdlMDJhODA5NzVkOWY0NjZjYTg5OThkMTBiNTgyYTFjY2E3MTIxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBkQVBZcmFDTksrNGNrdHRzRnozT2c9PSIsInZhbHVlIjoib3htSllGWkZoYzlVRUZvVWc1clRGMFk4dFEyZnI0ajRoTkhEU05EWjBPZzRic2lWTFN3MGZpZTJ0aDJpKzRRbzUrWm9qb2NEemJjcG1pT0hqcjFPaXhMc0IyYmdUNTJuVS8zOEFMSCsyaWRncVUxVWtsdENCWnRvcmJHSCsvbnVYZnRuMjlmdzh6amZ4NFJNY05ycDlkTG1lYlJ6ZEhCRUJrcWsrQ1N1V1ZYUG55eDBoNFdpMkx2bERUc3BCdzdRa1cwcGpkTStJdVRvcGQ0bEVJYUtTNXc5MndqcGlaaWtMMUpPcS95Z3AxdGNzSzU3azhXelhTdmt4dHl6NXg4UWhEcS9TMEhKU0tqVmlRZ2FpSU9CZE10M2NYbnJQY0Y0NzN1Q2wyRU9xNDhMT3hCMkQyL0xFc3lkMGpiak4yMGQ4N1QyZTJUejhpekVEU1dmQTRycWNYYVJxc3h2RXlBN0g4bG5UMjM2WHdsM3JMU1k5UEJPK2Q4QVE2UmR1S3A1eVRJdWo4SFZuUUU1cVBhNE53TXhyR01NMldVT3orMEp3aXFnaGd3RHhNOElPdlFUejkzdkFoZkRSN0J2UXkydEhpdTBKSm51U3ZjOHNGNmtGQ21rdU00bnhVT1Q0RWVRZ3lSUGhkZnlwcjJFSHhjUjg2dXBLRlVPQm00REp0WVgiLCJtYWMiOiI0YWViNmRlOWIyMTk0MTAzNGRjMjdmMzk2MTRmMzgzYmIzYTU3MTIyZWJkYTEzYjgwMjY1MGQ3NTJmYTcwMzY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1561259242 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0aUeGT9vcZQoKvixhqnXLFDjX39UXyfabqLnLub</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1561259242\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1710579132 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:13:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBQVzZFTXNidHQrdVRMcTBRSUxadmc9PSIsInZhbHVlIjoiTndPRENvNU5sK0JyK3JBeFNQcmVhSitENzNPNVpYeWVyTGtYeFA3RjNFaG5XRW42UlJtRWtzdUxwMTd5alI5YklJQm9kbHR3aUx3RDc5Y3ZQSzZWWGtUY0NHRlovMVNtRy9xSU93cVN6NlVBSThVaCtkUExPU0hmeXNDbU43cnErL0hZYWJ4d2g1L3l0VDlva1VScDBITWJCclVkdVhIOCtyV21FTTN1NCt5NzFFNndYenpiVllFVVIzdDQxMEVXcjVUSEZEZHRvVE14UGhCa2lrT1M2UkhaMmhETFV1OVlML003emgwOVQ3ckN2YUJTL0t1Mnl2WmEwSHFoVzBjdFFaMUZ6V2IzbmdsZmtYK0pROWFWdW9OTmFEUlJJaHlQUnp2NXFWVXdHcllOUmFUajNjdzZqZzV5REtURlZLRXBRMW0xU1lPYnJkdkk2aFlOOS9pdmpnbGZWcUt1d1dyRGNublBqVkNUV1lscnJMOENzWjFuUVplZmVDbXZRM3hXZVhRSmJlV3RYTFk0dGlqN08vanIrRXhxVkZaUnh0KzNaT29rakYxSjZjcnMxS01GeDlJcTFBNVh6QXNCOEZuTjM4SGZJMDV4QU5mTGNCalZNWjRQa1FnQnZGKzFlc1RQRHFTVnRBZ1ZBNmN3VnRvNy8wbEg5RUpRMWc3N0hLY1kiLCJtYWMiOiJhODk0MzUzMzVmNjA3N2UxZGNkODk0MzVmMzRlNWFlZWE2ZTUzNTUwYzI4OTk0NTc5YjMzOWY4YTFiYjg1YWY1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ii9ZMTNJbHdhQmhRcFB3OUg0UEpOeXc9PSIsInZhbHVlIjoidll3d0RGSlhkZ29NSXJ0QUhXRmVEY1BGckc0K3BJYmlPaEplWmZrUjZCUC9odnZlQVRydzl5aVNWdTdkWTlvLzRoenM4dlFiM2hxYWEvbi9hUFVZNk1OZTg3d2hnRFJ0Y1JzMEFVM3JpWHVMRE5ndlJzbXMxdVhPVTVQWm5naFJkenFZZkV3dEF0d3BIcUIweGxaUEtycm1iZ0tHemU3RGxWUlVaMTFoOEc3TEJsaDViSGpkMkpSNDNSZExzcUhCZ3ZRRlc5N1FaS1JFMUg0cWk1Ums5VG9MQ1pWOVI3TUszQVRSb29PQTAzMElmQTREYmZ4Nkd4ME5UYUJQR1kzWkZvNzBhL1M3K3FOaEdTMmRBVWlweXZMQ01GWXFpc1dvK0xHNmkvMnJwRlh5U2krUnlMb1Foa055ejllN3p2OGhTbis2Y3R4cVN3Z0phemVTaDg1TUxONlFtMC9XMmZyNXgrdWZndk9ZVkZ6QlV4Z0s5U05sd0drdHZUOW95eG5WMEFpMHRTbnpqMDRiTFFEWlZCWDFZRFpBS2c1SG53RkdPNjJNZ25tdWlmRWRoQ0dXVXE5VG8wQ29CMkJVMGdkVVFGalhyYndJNnA2T2swMncwRy9GQThCN1k0ODRFSnAvNWlOR1JRd0hzZThrbnR5NVNnVWlzWUNvNVMzaHBrMUQiLCJtYWMiOiI4YzE4M2MwYTM3MzRjMDEyODQ2NjQzMTAxM2ZmZmMwYWZkMTI0YjA0MmNlNTAxMTk0NzcwMWEwYjk2Nzk0N2YzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBQVzZFTXNidHQrdVRMcTBRSUxadmc9PSIsInZhbHVlIjoiTndPRENvNU5sK0JyK3JBeFNQcmVhSitENzNPNVpYeWVyTGtYeFA3RjNFaG5XRW42UlJtRWtzdUxwMTd5alI5YklJQm9kbHR3aUx3RDc5Y3ZQSzZWWGtUY0NHRlovMVNtRy9xSU93cVN6NlVBSThVaCtkUExPU0hmeXNDbU43cnErL0hZYWJ4d2g1L3l0VDlva1VScDBITWJCclVkdVhIOCtyV21FTTN1NCt5NzFFNndYenpiVllFVVIzdDQxMEVXcjVUSEZEZHRvVE14UGhCa2lrT1M2UkhaMmhETFV1OVlML003emgwOVQ3ckN2YUJTL0t1Mnl2WmEwSHFoVzBjdFFaMUZ6V2IzbmdsZmtYK0pROWFWdW9OTmFEUlJJaHlQUnp2NXFWVXdHcllOUmFUajNjdzZqZzV5REtURlZLRXBRMW0xU1lPYnJkdkk2aFlOOS9pdmpnbGZWcUt1d1dyRGNublBqVkNUV1lscnJMOENzWjFuUVplZmVDbXZRM3hXZVhRSmJlV3RYTFk0dGlqN08vanIrRXhxVkZaUnh0KzNaT29rakYxSjZjcnMxS01GeDlJcTFBNVh6QXNCOEZuTjM4SGZJMDV4QU5mTGNCalZNWjRQa1FnQnZGKzFlc1RQRHFTVnRBZ1ZBNmN3VnRvNy8wbEg5RUpRMWc3N0hLY1kiLCJtYWMiOiJhODk0MzUzMzVmNjA3N2UxZGNkODk0MzVmMzRlNWFlZWE2ZTUzNTUwYzI4OTk0NTc5YjMzOWY4YTFiYjg1YWY1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ii9ZMTNJbHdhQmhRcFB3OUg0UEpOeXc9PSIsInZhbHVlIjoidll3d0RGSlhkZ29NSXJ0QUhXRmVEY1BGckc0K3BJYmlPaEplWmZrUjZCUC9odnZlQVRydzl5aVNWdTdkWTlvLzRoenM4dlFiM2hxYWEvbi9hUFVZNk1OZTg3d2hnRFJ0Y1JzMEFVM3JpWHVMRE5ndlJzbXMxdVhPVTVQWm5naFJkenFZZkV3dEF0d3BIcUIweGxaUEtycm1iZ0tHemU3RGxWUlVaMTFoOEc3TEJsaDViSGpkMkpSNDNSZExzcUhCZ3ZRRlc5N1FaS1JFMUg0cWk1Ums5VG9MQ1pWOVI3TUszQVRSb29PQTAzMElmQTREYmZ4Nkd4ME5UYUJQR1kzWkZvNzBhL1M3K3FOaEdTMmRBVWlweXZMQ01GWXFpc1dvK0xHNmkvMnJwRlh5U2krUnlMb1Foa055ejllN3p2OGhTbis2Y3R4cVN3Z0phemVTaDg1TUxONlFtMC9XMmZyNXgrdWZndk9ZVkZ6QlV4Z0s5U05sd0drdHZUOW95eG5WMEFpMHRTbnpqMDRiTFFEWlZCWDFZRFpBS2c1SG53RkdPNjJNZ25tdWlmRWRoQ0dXVXE5VG8wQ29CMkJVMGdkVVFGalhyYndJNnA2T2swMncwRy9GQThCN1k0ODRFSnAvNWlOR1JRd0hzZThrbnR5NVNnVWlzWUNvNVMzaHBrMUQiLCJtYWMiOiI4YzE4M2MwYTM3MzRjMDEyODQ2NjQzMTAxM2ZmZmMwYWZkMTI0YjA0MmNlNTAxMTk0NzcwMWEwYjk2Nzk0N2YzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1710579132\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}