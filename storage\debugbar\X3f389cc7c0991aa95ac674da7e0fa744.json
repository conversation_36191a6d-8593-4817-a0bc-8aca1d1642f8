{"__meta": {"id": "X3f389cc7c0991aa95ac674da7e0fa744", "datetime": "2025-06-30 22:39:03", "utime": **********.406101, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323142.961386, "end": **********.406118, "duration": 0.4447319507598877, "duration_str": "445ms", "measures": [{"label": "Booting", "start": 1751323142.961386, "relative_start": 0, "end": **********.311179, "relative_end": **********.311179, "duration": 0.3497929573059082, "duration_str": "350ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.311347, "relative_start": 0.3499610424041748, "end": **********.406119, "relative_end": 1.1920928955078125e-06, "duration": 0.0947721004486084, "duration_str": "94.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45943656, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.376092, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.380922, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.397418, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.399732, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.02309, "accumulated_duration_str": "23.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.344629, "duration": 0.01736, "duration_str": "17.36ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 75.184}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3635871, "duration": 0.0029100000000000003, "duration_str": "2.91ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 75.184, "width_percent": 12.603}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.368471, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 87.787, "width_percent": 1.516}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\erpq22\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.376643, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 89.303, "width_percent": 1.862}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq22\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.3815641, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 91.165, "width_percent": 1.906}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq22\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.389996, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "kdmkjkqknb", "start_percent": 93.071, "width_percent": 2.469}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq22\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.3929641, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "kdmkjkqknb", "start_percent": 95.539, "width_percent": 1.472}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq22\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.394772, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 97.012, "width_percent": 1.602}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\erpq22\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\erpq22\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.398211, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 98.614, "width_percent": 1.386}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "RtHbcRqD8V7q1fDzvV41Fzq9te9B2VUCpYlSqZiL", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-2044901202 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2044901202\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-503012693 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-503012693\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-710625376 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-710625376\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1713178304 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; _clsk=eiqg7d%7C1751323138646%7C1%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InB1TVlxaHVQU050ZHc5MVduWGtubFE9PSIsInZhbHVlIjoiRUJLQnljOUFJdHBqMzVFSmZUVnVKTGtjSVMzNW01eVB0NWpOZzJTVy9SaDF4U3A1WklqQkJtWmY1NnloZ05pVm1kVzMxbks4MEpTcEZ3S0M2QVk3bVgybjVNUTZ3NlMrcVRienNRL013bkpXU0dvUzZ3VHMwNDNwWlZsc0NxMExOcm0rS1M2VXI1YklKN1RKNStrdkk4NHdUTFNRMjlpMTMrU1hpQ3pFc3ZzOXZxWGVTcVozWnF3UHJ3ZlZsQlRkVURtb3V4SVhPTWMyeFBSV1oxMHVackprZ2dYN3NkWEdCNVBNMFQ1YkN3Ly9xN1ptRHcyNWRYNlM4d1lLU3IwU2tWODZiUVVDRWZxRkNHVmNpelkzZkMxZmtBckwxc0htYjhPZGRObmpLOERqVisrWExZY1N6c2FGaGlLQ3A3M3FBejZOQS9Ec0thS0I2VERmcjk4aVVSaUY2ZytndHRqNXBza2c4SVVuNEZRTC9xQmpJcDQ4UGdwaDVmTjQrU1F0UzY0Z2tHUEphYm4rbjFEclRvQWZUUnVncXZxMGtEaW9CaDlJQmN5VGwyRnNtMC9JeTBHZkVEUDdGTHFkNkxsdmt5MkYxVjVGUUFDZGREVG5CRi9KSU85T3dvYU1RS1NyN2l6L3dUTGV4ODJFc0RQRld4VTdjbHljeW80NGRFRFUiLCJtYWMiOiJlNGRlYjdjZTcyN2JkOTg3ZmM1MzlkZjU2MzZiOTcwZTRkMTI0NTdhNjlkNTNiZmFmNWNkNDkyYzI2MzIwYjZmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImFvZ1VEQ2k2M0g4MXNEQjN5eFpldEE9PSIsInZhbHVlIjoibFZadnU1dTk1UmtOdkpKL2RzWnlwYkNYTTd2SGZqZUZ6d3dHUEpaelpldFZnVGtnY0dablI1d3ROS0tMOUdsb2lRV3E1cWVmSGtrRXZkRS9QeEpiRkRZa2pINjRHNjYrajZpcEFMUVY5SjRXclNNQVFrcHBMMStGdTc5LytmY3pQUUVnd3lBbW83czMxY0dzOU1NRmFGZ3JGVnlwZGFTanN5ZGh0UVlTNDAwMVl1VHcvWldQVkJlZGNPeEZMVTZ1REpGNXBabzFiUlk0QzcrS0ljM0VFZlphcmFHNnFIb05QM01EaU8ySnBSYlFrQ0lpbWRXTlJiMWMrSkw0emhjYTk4ZTNXak92N0RsRmpFUUNPZ1F0WW1CdGYwK3R6VnQ2Q1JQTDI1QktKanBGWGtYWjZKNTQ5SlZCZmR2eWlPVDUvNWM1ZjFkODdidkloeHNaRDcxb3Z4cnNodTdlQzhDaDBBZDdvUXJGNzJRd0dzMUl1dDloOSs1dDZUdm9Bend0QTkxODBTL2oxUmlERmhaNzBWdW5hWk56Tk9rcm8xOHZwZFZsdHh5enBxTWQreEhWOHdlakRiWDRzaHlQL243UXJFWnd1UnhIRFAxUGFaeFRSWmhvK0pleEl3MVUvMStyb25BYWRwME1mL3BGQkI4WFZWV1VDSDhUSko5WXdVWmMiLCJtYWMiOiIxMjlhNDYyZWEyZGQ3NzA1ZDkzMWQwZDI0MjNmNTEyOGMxOTZjYzM4MDNlZmEwZDAzZDBkNTNjYjdjMWUyMTVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1713178304\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1559664293 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RtHbcRqD8V7q1fDzvV41Fzq9te9B2VUCpYlSqZiL</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">91LwqcXsNZmfmRkjBpCz6hEenAJOpgI6hGqx9k4u</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1559664293\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1673521196 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InF6eDg0VlpBMDE0QkdnMHNZTVNtS0E9PSIsInZhbHVlIjoiOGdLc0p5NHdKZ0VwUnluVmgvQnZQd3VmMHZMWEpoYm1INTdqMXVSU0xuR3dXSHRUNDF2bU01dnJpZ2IvQ1ovR0tKQnR4cUIrYVBnRW9yemtucDV6QXJHaUJ4dFVZaUhENW9nSXZWaGRzMGdNdXRVaEljY2tBejJMaUhiK3dCaWNyY1hRUkhxaDFKU3ZQaHcybGdoVmRaOW9SWTM5R1E4Q2t3c2pJNUp5dnJJdTZQdnBQbTRiVFpKRnhPdUNqVUlkQTExeUFwOEFHajlaNnN1M2oyeVJkam1IUHVkb2NJZTQ5WGtTaUQvMmVzTm5zdlQzSGRrYjQ2Y2lHNnhOV2MvbzBqSXBTam5HdUhKeEcvK2JEYk5Gdlh3d29DRW4yOTRDcThORnp2WDJsd25NanloejNnOU55Yk1FVXZQRHp0WGJScHU2bjNWQlFqVzRNWW9FOG10UXhRTlQrY0FFZmNXd1NsT0swMllKUXptUitpV0pZZFZCVjk1M3FvbDN3bkZzbklUNTFNZzlLNEh5eHd6elNaM0lVS3laNjhLNWtaZXdMVEN2NnYxM0xMSXluUm5vSFJHb0xlaFlDb1piTXhGWTNUb3diaWpQUGFLUHNvYVhCMUFCQ2I1SkowazZBMHU4MnVVY1hGaHN5YWdWbGM0b1R2TU1IS2N6YW1FK1NvNFMiLCJtYWMiOiI3NmM3NDQzN2FiM2ExNTE2ODg5MGY1MjcwN2MzOTA2NTNiZmRlODFmMWQ0YWI3ZDhhY2JlMzA1N2YzN2M1NDAxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkZTV0ZEK01ZTmVjRG5vYVJnVWxYRlE9PSIsInZhbHVlIjoiQ2JqTXRlVHBQTFErblorNkd1cFVhQlBCZXNSTURMVVRrNk01OGgxM1oza0ttSXoyNUEwYlZyb2drbERBSUlJbVpleCs5WCszclpVVmd0RDV5QncvanRRSnc4MXFRbTlscUdYVjF1WklzVjR3U2l4anA3UEhKaldYZ0ZlSGFGY2t1d3QxMGxkUldYdklxS3FoQUZhbVBpRlpkcUlyNUJXN2ljK28wVTRXRFQ4MW56UFo5VUxnVXYrU01WUnhTWVlZVkNNS1lEaTc2NG1RbEl4R3huODBieTNMRzdBd3MrbFozUFdtbzcvS1ZCUDRHU3kzblE2SWpxMXg3THB0dnZ6MkFQWjdUQUMvU2o1WEsxTWZHL2tOZjVYaU9pZk5YczNsNDZyNmNwQkgrUGhYMlJ1NCtZdFQ1d05hWjMxc0FIYk1nSkV3OFhIUlE0ZEN2VURQM0FoYXFiNUEzeVNHZDRxRjhJeVdDVGFxU3UrNlhQZXZWNHFIUnRmTjlyZkhWc09hb2JzRmwzV0RmdHdESTBHYlFiSEdJMUJDTGhvV3ltR3JxajIyYkNEMGh3WisrZWcyVmMyQ2R5cVpxS25SdVdaVlpLL2xqczdyalpqM3ZiVDVkU1kyaWo1T0RtTDRzYmp6NzBXdWRvbHcweHVIeFlXcTBZcTNtMnY5VyszQzI2L2MiLCJtYWMiOiI5MzQxY2YzOGM4MWM1ODUyZGY0MjJhZjYzZDIxOTZkMGU5ZGJlZDgwMjUyZWFkZjkyNWIwMTRiMmM1N2FjZmEzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InF6eDg0VlpBMDE0QkdnMHNZTVNtS0E9PSIsInZhbHVlIjoiOGdLc0p5NHdKZ0VwUnluVmgvQnZQd3VmMHZMWEpoYm1INTdqMXVSU0xuR3dXSHRUNDF2bU01dnJpZ2IvQ1ovR0tKQnR4cUIrYVBnRW9yemtucDV6QXJHaUJ4dFVZaUhENW9nSXZWaGRzMGdNdXRVaEljY2tBejJMaUhiK3dCaWNyY1hRUkhxaDFKU3ZQaHcybGdoVmRaOW9SWTM5R1E4Q2t3c2pJNUp5dnJJdTZQdnBQbTRiVFpKRnhPdUNqVUlkQTExeUFwOEFHajlaNnN1M2oyeVJkam1IUHVkb2NJZTQ5WGtTaUQvMmVzTm5zdlQzSGRrYjQ2Y2lHNnhOV2MvbzBqSXBTam5HdUhKeEcvK2JEYk5Gdlh3d29DRW4yOTRDcThORnp2WDJsd25NanloejNnOU55Yk1FVXZQRHp0WGJScHU2bjNWQlFqVzRNWW9FOG10UXhRTlQrY0FFZmNXd1NsT0swMllKUXptUitpV0pZZFZCVjk1M3FvbDN3bkZzbklUNTFNZzlLNEh5eHd6elNaM0lVS3laNjhLNWtaZXdMVEN2NnYxM0xMSXluUm5vSFJHb0xlaFlDb1piTXhGWTNUb3diaWpQUGFLUHNvYVhCMUFCQ2I1SkowazZBMHU4MnVVY1hGaHN5YWdWbGM0b1R2TU1IS2N6YW1FK1NvNFMiLCJtYWMiOiI3NmM3NDQzN2FiM2ExNTE2ODg5MGY1MjcwN2MzOTA2NTNiZmRlODFmMWQ0YWI3ZDhhY2JlMzA1N2YzN2M1NDAxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkZTV0ZEK01ZTmVjRG5vYVJnVWxYRlE9PSIsInZhbHVlIjoiQ2JqTXRlVHBQTFErblorNkd1cFVhQlBCZXNSTURMVVRrNk01OGgxM1oza0ttSXoyNUEwYlZyb2drbERBSUlJbVpleCs5WCszclpVVmd0RDV5QncvanRRSnc4MXFRbTlscUdYVjF1WklzVjR3U2l4anA3UEhKaldYZ0ZlSGFGY2t1d3QxMGxkUldYdklxS3FoQUZhbVBpRlpkcUlyNUJXN2ljK28wVTRXRFQ4MW56UFo5VUxnVXYrU01WUnhTWVlZVkNNS1lEaTc2NG1RbEl4R3huODBieTNMRzdBd3MrbFozUFdtbzcvS1ZCUDRHU3kzblE2SWpxMXg3THB0dnZ6MkFQWjdUQUMvU2o1WEsxTWZHL2tOZjVYaU9pZk5YczNsNDZyNmNwQkgrUGhYMlJ1NCtZdFQ1d05hWjMxc0FIYk1nSkV3OFhIUlE0ZEN2VURQM0FoYXFiNUEzeVNHZDRxRjhJeVdDVGFxU3UrNlhQZXZWNHFIUnRmTjlyZkhWc09hb2JzRmwzV0RmdHdESTBHYlFiSEdJMUJDTGhvV3ltR3JxajIyYkNEMGh3WisrZWcyVmMyQ2R5cVpxS25SdVdaVlpLL2xqczdyalpqM3ZiVDVkU1kyaWo1T0RtTDRzYmp6NzBXdWRvbHcweHVIeFlXcTBZcTNtMnY5VyszQzI2L2MiLCJtYWMiOiI5MzQxY2YzOGM4MWM1ODUyZGY0MjJhZjYzZDIxOTZkMGU5ZGJlZDgwMjUyZWFkZjkyNWIwMTRiMmM1N2FjZmEzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1673521196\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RtHbcRqD8V7q1fDzvV41Fzq9te9B2VUCpYlSqZiL</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}