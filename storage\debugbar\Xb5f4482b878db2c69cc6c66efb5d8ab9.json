{"__meta": {"id": "Xb5f4482b878db2c69cc6c66efb5d8ab9", "datetime": "2025-06-30 23:06:19", "utime": **********.090849, "method": "GET", "uri": "/pos-payment-type?vc_name=10&user_id=&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.543153, "end": **********.090864, "duration": 0.5477108955383301, "duration_str": "548ms", "measures": [{"label": "Booting", "start": **********.543153, "relative_start": 0, "end": **********.9189, "relative_end": **********.9189, "duration": 0.3757469654083252, "duration_str": "376ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.91891, "relative_start": 0.37575697898864746, "end": **********.090866, "relative_end": 2.1457672119140625e-06, "duration": 0.17195606231689453, "duration_str": "172ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53422504, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.bill_type", "param_count": null, "params": [], "start": **********.021531, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\resources\\views/pos/bill_type.blade.phppos.bill_type", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fresources%2Fviews%2Fpos%2Fbill_type.blade.php&line=1", "ajax": false, "filename": "bill_type.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.bill_type"}]}, "route": {"uri": "GET pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@posBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1428\" onclick=\"\">app/Http/Controllers/PosController.php:1428-1536</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.005139999999999999, "accumulated_duration_str": "5.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.954986, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 32.685}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.967967, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 32.685, "width_percent": 13.23}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.983438, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 45.914, "width_percent": 11.479}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.985481, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 57.393, "width_percent": 8.171}, {"sql": "select * from `customers` where `name` = '10' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["10", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\PosController.php", "line": 1439}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.990121, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1439", "source": "app/Http/Controllers/PosController.php:1439", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1439", "ajax": false, "filename": "PosController.php", "line": "1439"}, "connection": "kdmkjkqknb", "start_percent": 65.564, "width_percent": 8.171}, {"sql": "select * from `warehouses` where `id` = '8' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\PosController.php", "line": 1440}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.992138, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1440", "source": "app/Http/Controllers/PosController.php:1440", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1440", "ajax": false, "filename": "PosController.php", "line": "1440"}, "connection": "kdmkjkqknb", "start_percent": 73.735, "width_percent": 7.004}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\PosController.php", "line": 535}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\PosController.php", "line": 1444}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9960861, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "PosController.php:535", "source": "app/Http/Controllers/PosController.php:535", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FPosController.php&line=535", "ajax": false, "filename": "PosController.php", "line": "535"}, "connection": "kdmkjkqknb", "start_percent": 80.739, "width_percent": 9.144}, {"sql": "select * from `pos` where `created_by` = 15 order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\PosController.php", "line": 1523}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.011751, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1523", "source": "app/Http/Controllers/PosController.php:1523", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1523", "ajax": false, "filename": "PosController.php", "line": "1523"}, "connection": "kdmkjkqknb", "start_percent": 89.883, "width_percent": 10.117}]}, "models": {"data": {"App\\Models\\Pos": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-622677391 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-622677391\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.989085, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1174728443 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1174728443\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.995229, "xdebug_link": null}]}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2353 => array:9 [\n    \"name\" => \"مفتاح علبه\"\n    \"quantity\" => 1\n    \"price\" => \"6.00\"\n    \"id\" => \"2353\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-1526719743 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1526719743\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1346983752 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1346983752\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-767269545 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323255132%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im55enIvN0tBUlhEYS9Vby93djAxYkE9PSIsInZhbHVlIjoiZkkrWGlNUkJQd2RWMWdyTCtWb2xLcElnTzlDSlFLcU5seW9uZUxwS2pibCticTJ4OXdyanVlM0djaWF1a2tFYy9Ed2RMaUlLVTZlcHF1Tm1YVDUwMmRsbkZpVDJ4czJma2hmZmR2ZUxlR1FCalVVOTkrNGlSZ1BDaHpwVmpxOTRCenlnRE1hcW5MZTNMTytMZEJwZ3F5eHVSZDlZTmRYaUgrTmI0Mit4QUZER01USWwra2luQWtIK1VINVo2OS9MSDI0MHUxbWxsYXNUaGZ6RWpFRTZRNVdUM3crZUVLUVN4bkZHdG1Va0RYNVUzblp2K1ZNUjZWOFpISE9ZQWNUWU56RTdIRUdabkx6S1RLQlYvL3BCVklFc2JYYVBWTEkwQzk3V3lYZVZYZEZ4dHhYRlVlaVB2d1RKNG1qamZwMUcyVVgwY2toaHp2Y3J1YUR2MDg0ZDRPWXl1LzlNbGMrSmhFdW5NQTdaSDU2US9xY0hOWUJpR2ZRdGRybzJoM0ZCeVNtRlkrWmg2SUZBU3Z5bk80Qm9ucHFIWERXbzB2Uk0xb0dqVGYxR1BTaTZwQTBLcXFlNDVIMXpYeFQ4Mjk2QkJDY3JOUzB5MXpNdVV3MEY3VlhNL2JTdnBQKytLZHRSdFN5S1pmQXZiWnVWMFYzSkI3YTU0bktFc3RqQWRvSFgiLCJtYWMiOiIyYzljN2Y5YzQ3NjQyNTE1ZjllNDg2MWNiMWFkMDkzMjEwOTlmMWJiZmE4MjMzMDQ3ZjBkYjQ2M2RkMzE1ZDQ4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImI5K1doaEsyVXdxTDdwTGtPcDJXWkE9PSIsInZhbHVlIjoiK3JwcDQwVHl4MWc0UVhlckcrSjNaM0RrcHNyQ3JneWZtNWV3ZWp3Vld0TGZhbnZUS3FnWHJDS2lGSk5IcEtzcG5pOEdzT21ZVk5tN1NWMHBCZDVoNEFiSmtrZmRwZ21oK21nYTZwUXNXNU9NV2lhbUF0ZSt5NGN2L2xCbnJ5b3VETlp1ZElEYmRhL0R0SGExUGZLeWNwN2VoV2VDZFBjU3RuSzhKMjQ4ZGoyQ0g5azc1TWs0bnJQcTYwWjBkUXRoUzZSc1dWVVNmaTFIcUVJR0VDcWtkTWQ5ZXIxbjl4Vkc5UjUraU9zVFhhdWJ2WmRURUlzd3p6S2Y3QktQTEkyZHhzcFc4am80MGVvL2VSWDVyd3RwbDNKb0R1V0lzdndaMFE3QU9tRm9WSmx4U25LaGJYSjVROWxMMHBXUEE1UGgxNll3bFFJOWNJb0FLT2MyYS8vMHJWOFkxaFFwbTh5c0taUnVZYSs1SXJyWkNyR2JlcFhRRW1qYXloOTFCSTQ3MlM4TnAzemNlWU1obW5kQXcrL0Jic3ZvNlUxRDZnYysxUSswZXNFNVg3YlJqRTJQRVVScFFuVGlEdXZWT0h5aTRyRjZTakk5Mnl1SzA0MFFYaHdZck03T09LNXV4UWpZMVJCV3BwSEhRYlJ2bHFONU0vVVJKb3c3UU9zRGNPUjYiLCJtYWMiOiJjMWI4OGU2ZWZjYTliYzNlMmUxNzhlZDc1Y2E1MzY4MTU2ZDlkNWFkZmEyODNmMDkxMWM5NDQyMTA2M2VmMTY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-767269545\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-99821518 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-99821518\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1096522318 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:06:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllOZXVTQzAxSk1GQTMrWDc1VGQ0V0E9PSIsInZhbHVlIjoiTUZPK3ZjNW1STEZ2U1RZTitobytFdHpXSjUvUE1Hd0JiMW5JR2RScmt1Ulp2WjVYZE9zbFJpOXRVR01xQXlxT1ZVQkgyY2VnNFZvbVdsLzJpZEpBK3ZCeXZTM0hCYXhMUk1oK0lYSzNFeXgwaUhVWEllcUFRSHlEUnE1aEkzUkJDSXFBUnl4SGlFODhsb1hZbXNWZStyRmpKTC9hZE5OQzRpYlUxK1pTRDV4ZnNHbEFQSzVJSWExZFIzM21qVm1FUVRJaCt6K0FDd3dxaDlIQnB0OElsQUNYZkV1QWpCdUZFckxDMDU4eDFhTVRUMVFvTW1KeUZCazBmNVFJZFAzWVVkQ1RNUld2VitOamg1K3M3RHpZcHNwanhXMTJod3FjdU5FMDVLNDIyMnhUc2JQR1ZvbU1IbkRnbURhMlNQV1RpVzJRZTF3V3dJZ2RrcWoxbms4UXR3dTV6YTJYcVlERC9hbzU1TElrYjlXOGZ3RUJLSENwd2lza2xOWU5kUDdQcmVneS9sU3ZabTNCbW9kaXZFT2FmUWt3MS8rTXhhZWpXUkVpYmY5eURKN1g3VlYrQ2l0TjBvK25MR0dGWGFqWTFzYjNUWUVtbUlQK1FTVnliWW9FNVdBL3dxVm9FNHpsa1lvbWxMNVNiRXdUZWRiWVpGOWRuV2JxbkttRlRqdnkiLCJtYWMiOiI3Mzg4ZDVlMDBiOTcwNjE2ZDI1MjBkZGQ0ODFjNjU3NGE0OWMyOGU0YTU1MzJiMDZlNzc3ODg0Y2JiZWIwOTk0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:06:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImVjcUpJT3EzR1JqRG1nNXdaS3d1N3c9PSIsInZhbHVlIjoibTA2ejdSalBCclBkcGVJU2pPVWVjajdwdkx5NDhvSzVTUVYyclBSdWlzdHQ0anpFRFZnR2dSQzVvNVdNUGpER3lEQlV2WnVpQzlIK3dGMTUyVG9IT2JMWlBJTmdPU3ZCV0V0ZHhOY0NhbDlOL3I5OTVXYVQ3QXVzZHFWUGl6TGNMbFJhaStiM0hLOVJ5Zk81VWo1NXJzb0JFV29Ia0xVam8yTE03Z1hyaVA0SVJIZUdJRlcxZzBZc01YQWIvdGQ0VVNhK3cwK2phbXJ4REQrZGJGaS9iYW9jVHhZNjVIQ3VFUmNYcnM4K2tUeDFGWFE4N0RXWWlGbHVVNTRZeEZ4SVRIdjNXVjZUdDF2R0tJWVFVTW9hNnJOQTBRT2pqTG9MdjZBNW5CTHRGU0tkbmRaSXMyNjNtbnExb3BtUU5KV25tMXFKYy9aWnlwQ2ozb0lNR0NjN01tUXlWMlRjU0lXeXM0K1hnSEVzL2laSXg5THJnNHorTjhXWitadytGNVd6cDkzSkcvN0plR1BZWk40N2FjdWE3MGd5azg3aHJUdDFyU0tsRUxwVlZMTFI5Yk8ycGdMdUFRNzkyMDViWXhMbkhZNXkvRDB4eGhhYWVxQjY5U2pFUS94Z1VGRVNNMCtuTFJrWnNEUEo1TWtoZnFOUG11M3p3SEpJREJkdUE1VFYiLCJtYWMiOiI1NjU2MmFkZmEwY2FiMDE4M2EzYjYxOTc0ZWZmMjM2NjFjNGIzNDU1MTY5Y2U3MTM0OWMxZGRkNzU2NDk2OTBjIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:06:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllOZXVTQzAxSk1GQTMrWDc1VGQ0V0E9PSIsInZhbHVlIjoiTUZPK3ZjNW1STEZ2U1RZTitobytFdHpXSjUvUE1Hd0JiMW5JR2RScmt1Ulp2WjVYZE9zbFJpOXRVR01xQXlxT1ZVQkgyY2VnNFZvbVdsLzJpZEpBK3ZCeXZTM0hCYXhMUk1oK0lYSzNFeXgwaUhVWEllcUFRSHlEUnE1aEkzUkJDSXFBUnl4SGlFODhsb1hZbXNWZStyRmpKTC9hZE5OQzRpYlUxK1pTRDV4ZnNHbEFQSzVJSWExZFIzM21qVm1FUVRJaCt6K0FDd3dxaDlIQnB0OElsQUNYZkV1QWpCdUZFckxDMDU4eDFhTVRUMVFvTW1KeUZCazBmNVFJZFAzWVVkQ1RNUld2VitOamg1K3M3RHpZcHNwanhXMTJod3FjdU5FMDVLNDIyMnhUc2JQR1ZvbU1IbkRnbURhMlNQV1RpVzJRZTF3V3dJZ2RrcWoxbms4UXR3dTV6YTJYcVlERC9hbzU1TElrYjlXOGZ3RUJLSENwd2lza2xOWU5kUDdQcmVneS9sU3ZabTNCbW9kaXZFT2FmUWt3MS8rTXhhZWpXUkVpYmY5eURKN1g3VlYrQ2l0TjBvK25MR0dGWGFqWTFzYjNUWUVtbUlQK1FTVnliWW9FNVdBL3dxVm9FNHpsa1lvbWxMNVNiRXdUZWRiWVpGOWRuV2JxbkttRlRqdnkiLCJtYWMiOiI3Mzg4ZDVlMDBiOTcwNjE2ZDI1MjBkZGQ0ODFjNjU3NGE0OWMyOGU0YTU1MzJiMDZlNzc3ODg0Y2JiZWIwOTk0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:06:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImVjcUpJT3EzR1JqRG1nNXdaS3d1N3c9PSIsInZhbHVlIjoibTA2ejdSalBCclBkcGVJU2pPVWVjajdwdkx5NDhvSzVTUVYyclBSdWlzdHQ0anpFRFZnR2dSQzVvNVdNUGpER3lEQlV2WnVpQzlIK3dGMTUyVG9IT2JMWlBJTmdPU3ZCV0V0ZHhOY0NhbDlOL3I5OTVXYVQ3QXVzZHFWUGl6TGNMbFJhaStiM0hLOVJ5Zk81VWo1NXJzb0JFV29Ia0xVam8yTE03Z1hyaVA0SVJIZUdJRlcxZzBZc01YQWIvdGQ0VVNhK3cwK2phbXJ4REQrZGJGaS9iYW9jVHhZNjVIQ3VFUmNYcnM4K2tUeDFGWFE4N0RXWWlGbHVVNTRZeEZ4SVRIdjNXVjZUdDF2R0tJWVFVTW9hNnJOQTBRT2pqTG9MdjZBNW5CTHRGU0tkbmRaSXMyNjNtbnExb3BtUU5KV25tMXFKYy9aWnlwQ2ozb0lNR0NjN01tUXlWMlRjU0lXeXM0K1hnSEVzL2laSXg5THJnNHorTjhXWitadytGNVd6cDkzSkcvN0plR1BZWk40N2FjdWE3MGd5azg3aHJUdDFyU0tsRUxwVlZMTFI5Yk8ycGdMdUFRNzkyMDViWXhMbkhZNXkvRDB4eGhhYWVxQjY5U2pFUS94Z1VGRVNNMCtuTFJrWnNEUEo1TWtoZnFOUG11M3p3SEpJREJkdUE1VFYiLCJtYWMiOiI1NjU2MmFkZmEwY2FiMDE4M2EzYjYxOTc0ZWZmMjM2NjFjNGIzNDU1MTY5Y2U3MTM0OWMxZGRkNzU2NDk2OTBjIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:06:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1096522318\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1001847298 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2353</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1605;&#1601;&#1578;&#1575;&#1581; &#1593;&#1604;&#1576;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2353</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1001847298\", {\"maxDepth\":0})</script>\n"}}