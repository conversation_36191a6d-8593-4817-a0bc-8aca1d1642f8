{"__meta": {"id": "Xf96830e2c841ffe72ca2c075dcde7cdf", "datetime": "2025-06-30 23:11:14", "utime": **********.508162, "method": "GET", "uri": "/pos-payment-type?vc_name=10&user_id=&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.071781, "end": **********.508175, "duration": 0.43639397621154785, "duration_str": "436ms", "measures": [{"label": "Booting", "start": **********.071781, "relative_start": 0, "end": **********.418821, "relative_end": **********.418821, "duration": 0.34704017639160156, "duration_str": "347ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.41883, "relative_start": 0.3470489978790283, "end": **********.508176, "relative_end": 1.1920928955078125e-06, "duration": 0.08934617042541504, "duration_str": "89.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48294048, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@posBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1422\" onclick=\"\">app/Http/Controllers/PosController.php:1422-1530</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.020439999999999996, "accumulated_duration_str": "20.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.453928, "duration": 0.018449999999999998, "duration_str": "18.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 90.264}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4806762, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 90.264, "width_percent": 2.25}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.494042, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 92.515, "width_percent": 4.452}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.4966362, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.967, "width_percent": 3.033}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-804537806 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-804537806\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.500531, "xdebug_link": null}]}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-2134311898 data-indent-pad=\"  \"><span class=sf-dump-num>404</span>\n</pre><script>Sfdump(\"sf-dump-2134311898\", {\"maxDepth\":0})</script>\n", "status_text": "Not Found", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-518622840 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-518622840\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-203842684 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-203842684\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-101108715 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2818 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751325027058%7C14%7C1%7Co.clarity.ms%2Fcollect; laravel_session=eyJpdiI6ImpxTklOWXhTYU1XWEJkQTBqVTRXZlE9PSIsInZhbHVlIjoiTXFGZEpQRXlGa212dDlvcjMyZUgzOTJTdTZUUld1Y0RIbVM4elBEck42T2RTa0tjV2NoaTJNekRxM3Vkc3lrR1dOeGt5blA0Ym55U2pNZWY5am1QNC9lMm5SZE1VcGh6OVBneXMxN2Y4Qmx1TWhKckg0OXNOdzJBV1ZmTzVubEw3cmlyUDRwVnhxYzd4NmhqaEJiZThRcHlESndxYTY0WWNKZ1pqeVdFSTZuZnRmZ1BDZy91c3ZCbWloYytBQnpaczBrTmh2S3Z1dllBcHJYMkhWOXM3alFFWENqVkhpc2Rnb1lHSlpwM2dNb0s5cUxpakE5OVNEbEZRZ0VUVDJBWFZKWmZyYjZRM2ZNL1BuR0l5L3BsYXhVbzEwQ1ZlaXYySVUrQnk5a0dvY1BKK2dwQnEzM3ppZnFlaW9XTW9Ga1pSQ29mZktuT3BjSnc5ampzYU1SdTIxNzd4Wms4eUJ6eWdrajg1UUhYS2puSkhMNDVRbEh3WENJazQ2Qzk0Vlk5cE5NYW1qQ0xCbWJBVHZ5ZUlzUzBGRTJCWGliL1ovUHlOejhGSHJzRS9UOC8wK0QvTTQ4WHdveVBkTE1CdHlWcnFrQWdDcU1jR0hscFd0SUgzYWRtbFRaT0pzTGUzSkVDS2k4YlhiU0s3b29aMU5tbWtnL1NUaWZ1VmhxYnZ3ZFoiLCJtYWMiOiJjNTgzZDkxNDlmYmY2OTAwMmNmYTUwNTg0OWMwNDQ3MTVmZWQ0YWFiNjk4ZTJkZDM4YTkzNjhiMzE3YzIyNjlmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ijc5bkZxR1ByWWRhNjNHZ0wyK3M0b3c9PSIsInZhbHVlIjoiNWloQ01EUHpxRzgzY1lQbjRSL0ZOcytGV3ZaZitoNXc0QjBPUC9ldzRPT0FFeDFZV0hqZ0hwV1dEa0xSelJOK05USkJudExKcmZLNjRUb0VQbkZHY0ZLL1I3UlZrWHVRYmx5dHJMMmhCY0t3RzZmVGZrZm1ZT1A5SWwzY0FIN1lPVVVVTUhWUGk4d3lwQkJtejc4WmNlVC9LQVM3aGpSUjdHR1picTRyOUYzS3hSbDdBMzRTWm9QYTJCRFVwVk5RRnNtdm5OWmkwM2hWUVdRZHM5QXExSlBFczVKRTJHQ2w5Q0NLeUk3UHdIbDVHWjdGTGRnL0U1dHY2YysrWFN6bERSQzArWHM4VjZOWUl4T01xdGw3UnVtTE5YSHloT2FncldzZTdDM2kzVGVrVWNNWGlJQnhLa0NQRDNXVTRsRmxpR3YvR2RlSTRTSVN3UU95OW5uajB0enFEWUQrMW1qL2VLenVLS0UvYllPRFNuVlRHcVJZaXI0TDdOUXIwa0JiaGhvNU9nNUsxbmxiaGx6M09aVHVQRDI1VDc1dzkzdUk4aTVBOWhQaDU1WVZMSUhDZ01HK0RuUnFvcExlMnhKd3VSd3RlaGZtSERjV1UzRGliRXhhUUlGYk5TeG81ZEx0VnBHWUNvdkd0V2tPQlJXdUZ6QXBzT1ltMTF3MWswWW0iLCJtYWMiOiI5Y2RhOTdhOTI5YTgwYTcwY2M3Zjg3YjBkMTExZmYwYjk5YWJkYzg0NDkwZTUyNWFiMGUwODNlNTg2NjQ2MTYzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlY2THk3NzRubEZ4MFBxcHFheG1wNlE9PSIsInZhbHVlIjoib1JIN1l4cHIzWGgrRkkrQjB0blpkaC81Rm5LcHhYV0pWcUFRRkgvWTU2ZnlKenhuTHM4c1kzRzJ2dTloRHgvV1A4c2dqendTZ1J6ZDhNYUxtOW45K1QvZzlJWXZBL2dGV0hwS04zTmNicXBiNUZIeStOU1gwbnM2WDFaeFg5Z1pSd0tkQWlWUy9KYmc2WDRFNmF2bWlhWXVuY25aS0ltSzc5L3pLWTBDRlB5WjlJNTU2eVUwelN4a2RhcUFrdFJpVzB6ZEVWQ0NqbXJnWnI2USt4aFZxNVRLaWk0RDI4Q1RtNWl5akQrRUpMVUFjOEpwT3plMFk5U0dmRG1adXVubFg4V3BrTUFxM1M5WW9XNnpGblUzanRvMDBMM2phQW5BVUthVkV6NEhiNUxham1DZzRINEtWclFaWTFTSkV2TmovVU11Ny81QVVmaGlWdFRMTkZVWHFFazJPbFBjcUpoOXRWaVE4WlpCVkhDRzIrUGRDa2NaRUgvbzFSbXRwcDMrMlhmK0Mya2dMRGJMZEd0NmtIM0M2OVNkd3RUZ1JvYlhxWktZZCtOUTh6citmcjVKS011TGNDa3VhUjg1UnJiQzZUWHRHTC9VRzFVeUROcjkrU2ZyUEhrWmNUV3FPbUFZcTRnU2Q1ZzVVNTRGcFd4YTNwMWIySzEyRlkwN3VRSmkiLCJtYWMiOiJiZDUyMzQzNDQ3YjM4ZTNlNTBiMWE1YjQzNmZhZmZjODkwNDczZGMzNjM0ZGUzZGRmMzMzMGY4NGNjYzI2OTdhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-101108715\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2112032786 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nMXQGGGSGQFIfGwlrnEYCl0LIB0DtCvcZqQRlbGj</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2112032786\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1460609385 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:11:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZVazBBY2RkSk9pY3lNamxvMUFsalE9PSIsInZhbHVlIjoiZ0hIWjZYaHkxWG12UnpxV0dUdjJsbExxMXRoTTRrQ1g1a1dhckw5NTRsUWRyUEFXOWsvY25ndGsrQjBrd2N4U29KOFpDSzM0R3crTnZ4cFdTdUs4akZ1M3BHQ01ON1N4Y2xoeFZiZ0Q3eEE4WWcrdkVXelY3SDV5VVNEVHpLWHA5bVp5SFgrQWN5QlhhZWFQTHQ5QjV1cVV0Qys0dDg2TUNzSXZSTWVtZ3N4ZWsxeVNXKzQzd3ZFNzRlT0tUaXhnUW9PdmxkL1JQOUJYMERmbXlDc1Y2UWZkRGN1WlJCT25hak9hUjE3ZlYwbmFBNHc4TU1LcWx4cEpjNWlET3FEVnFaL281ZjNxMTNWVk5nNFovWjhkLzZZZ2g3N2F3ZEdjVXJka1dsNS9MMzJEWExHMi9ITXhGblZYcXBORFgyUndzaER0dmptSUFYbU1FalczUzNUM05QNVMrVWRwSm0yZU1Fc3k4bjdTUFNiR2sraXhIcTIvOHRSZ1R5MXhUK1JYV000bXNPVlFXWTZ0ZWo2VHVwSGZ0aU9sWnM3b2I3MWxPTWNJc09LdWF0VWJJK0QyZlpnZ29KcHUrbFBsZ0lBVTZXamZoOXRaS0Y3UldCcVVYbjZhM3JrdTN3dWZvYkxjd3AvZUY2UEJrM09UbEVHd2tRRzBXblMzZDdHYkRueUMiLCJtYWMiOiIzZGEzZjljMmU3ZTYzNWIxYjBjYWNhNjg4MDEzYzNiM2ZjYzUxZWMzMzQ1YmQ4NWRhZDZjOWI5YmM4NjdjNDFkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:11:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjY4eC9VY0ZmanFkbEVUYWhMVzgwZEE9PSIsInZhbHVlIjoiYTMrSUZlZmlFbXd6RWJZdVNqMlMwVGF6QzkxWjd4TnVNdGxySCtQQmg3R1dPd0I3ZStpTW5mejRRTG5HWmV0V2JQTkhUWlBRQUZTbjBsT2ZnVFFBUjQ0WkVjQTRsd0lGOTUvSm9kYXpNbmFqNmx1Tm8rSlNIQVlwUDdTWkM2Q09Ba0lPNzR0NGZuSE5BdU0rTVN1WFlEV1FmVkpONEk4SVMxbWxuRDVxMXRWMk1aUUhBc0RQdE1PVkVxUmxGRmFIc25MSDVjSGNlY2ZiMlhYSWpZajdFa0l4eklKZzJRWDUrOWJJbXVYOWM3VjQzaXFrS2k5WHBXV3ovbFlxMHlzeFVoTFV1eS9CN3pQNGxtSGNrYUpDVEpSNmZVQitINjZDZ2wvUVNYZlFMSVlGM3pUcW5vNTBxdDVRMklYTTVOU3pWNkVzNnFXNGlGNDFVWWs5OVJlNHlMV2FYUmhVMkdkUng3Y1ZlMHd4UDRwUkFyM1lUZ2FjUzZrSzVaSVRYRW8veEN4NmdtSnhHWFhyamNETys0dmdpUlZiZGcyWHZ4azlEcmxtZXZuQmI2eTA5MlQ3aWxscFVZUldvbkh2M1ppRFJtNmFqWlo2L3VxbnkyRmxwV0Z0YThnS2V3ZEg2QnN0WFlRSjBvaGFiM3VVVU50bnB0NzJrTlZVTGZNalQvUEoiLCJtYWMiOiJjZmVhNDlkNjIxY2FkMWMwZGUyMDBjZjM0MGY0NDBlYjhkZGRmMzhlM2Y3YzRiZTg3MGMzNzEwOTAwZGYxYmIzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:11:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZVazBBY2RkSk9pY3lNamxvMUFsalE9PSIsInZhbHVlIjoiZ0hIWjZYaHkxWG12UnpxV0dUdjJsbExxMXRoTTRrQ1g1a1dhckw5NTRsUWRyUEFXOWsvY25ndGsrQjBrd2N4U29KOFpDSzM0R3crTnZ4cFdTdUs4akZ1M3BHQ01ON1N4Y2xoeFZiZ0Q3eEE4WWcrdkVXelY3SDV5VVNEVHpLWHA5bVp5SFgrQWN5QlhhZWFQTHQ5QjV1cVV0Qys0dDg2TUNzSXZSTWVtZ3N4ZWsxeVNXKzQzd3ZFNzRlT0tUaXhnUW9PdmxkL1JQOUJYMERmbXlDc1Y2UWZkRGN1WlJCT25hak9hUjE3ZlYwbmFBNHc4TU1LcWx4cEpjNWlET3FEVnFaL281ZjNxMTNWVk5nNFovWjhkLzZZZ2g3N2F3ZEdjVXJka1dsNS9MMzJEWExHMi9ITXhGblZYcXBORFgyUndzaER0dmptSUFYbU1FalczUzNUM05QNVMrVWRwSm0yZU1Fc3k4bjdTUFNiR2sraXhIcTIvOHRSZ1R5MXhUK1JYV000bXNPVlFXWTZ0ZWo2VHVwSGZ0aU9sWnM3b2I3MWxPTWNJc09LdWF0VWJJK0QyZlpnZ29KcHUrbFBsZ0lBVTZXamZoOXRaS0Y3UldCcVVYbjZhM3JrdTN3dWZvYkxjd3AvZUY2UEJrM09UbEVHd2tRRzBXblMzZDdHYkRueUMiLCJtYWMiOiIzZGEzZjljMmU3ZTYzNWIxYjBjYWNhNjg4MDEzYzNiM2ZjYzUxZWMzMzQ1YmQ4NWRhZDZjOWI5YmM4NjdjNDFkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:11:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjY4eC9VY0ZmanFkbEVUYWhMVzgwZEE9PSIsInZhbHVlIjoiYTMrSUZlZmlFbXd6RWJZdVNqMlMwVGF6QzkxWjd4TnVNdGxySCtQQmg3R1dPd0I3ZStpTW5mejRRTG5HWmV0V2JQTkhUWlBRQUZTbjBsT2ZnVFFBUjQ0WkVjQTRsd0lGOTUvSm9kYXpNbmFqNmx1Tm8rSlNIQVlwUDdTWkM2Q09Ba0lPNzR0NGZuSE5BdU0rTVN1WFlEV1FmVkpONEk4SVMxbWxuRDVxMXRWMk1aUUhBc0RQdE1PVkVxUmxGRmFIc25MSDVjSGNlY2ZiMlhYSWpZajdFa0l4eklKZzJRWDUrOWJJbXVYOWM3VjQzaXFrS2k5WHBXV3ovbFlxMHlzeFVoTFV1eS9CN3pQNGxtSGNrYUpDVEpSNmZVQitINjZDZ2wvUVNYZlFMSVlGM3pUcW5vNTBxdDVRMklYTTVOU3pWNkVzNnFXNGlGNDFVWWs5OVJlNHlMV2FYUmhVMkdkUng3Y1ZlMHd4UDRwUkFyM1lUZ2FjUzZrSzVaSVRYRW8veEN4NmdtSnhHWFhyamNETys0dmdpUlZiZGcyWHZ4azlEcmxtZXZuQmI2eTA5MlQ3aWxscFVZUldvbkh2M1ppRFJtNmFqWlo2L3VxbnkyRmxwV0Z0YThnS2V3ZEg2QnN0WFlRSjBvaGFiM3VVVU50bnB0NzJrTlZVTGZNalQvUEoiLCJtYWMiOiJjZmVhNDlkNjIxY2FkMWMwZGUyMDBjZjM0MGY0NDBlYjhkZGRmMzhlM2Y3YzRiZTg3MGMzNzEwOTAwZGYxYmIzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:11:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1460609385\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-993817348 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-993817348\", {\"maxDepth\":0})</script>\n"}}