{"__meta": {"id": "Xa6eb18b88199860d0507148024510767", "datetime": "2025-06-30 23:14:33", "utime": **********.090481, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751325272.676923, "end": **********.090497, "duration": 0.4135739803314209, "duration_str": "414ms", "measures": [{"label": "Booting", "start": 1751325272.676923, "relative_start": 0, "end": **********.038694, "relative_end": **********.038694, "duration": 0.3617708683013916, "duration_str": "362ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.038704, "relative_start": 0.36178088188171387, "end": **********.090499, "relative_end": 1.9073486328125e-06, "duration": 0.051795005798339844, "duration_str": "51.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45030704, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00288, "accumulated_duration_str": "2.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 23 limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.064008, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 57.639}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.077017, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 57.639, "width_percent": 18.056}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (23) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.082486, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 75.694, "width_percent": 24.306}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WuYcucCgl4bBOLSdLTqnajP2UNBzqFh0FtUk6GCo", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6IlFYS2NhMWttS09RMlV0eXVJVEd3bXc9PSIsInZhbHVlIjoiOHM5bjVYL1c0ejZkdXlyWnpCdlZuUT09IiwibWFjIjoiOWI4M2UzNjYyZTU3MjBmZDU3ZWY0YTRiYTYxYmFlZjBlMGU0NWZlMjA1NmY5YjVmYWUxODQ2ZDY5MzQyMzFiOCIsInRhZyI6IiJ9\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "23", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1148158382 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1148158382\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-556156968 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-556156968\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1679451293 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WuYcucCgl4bBOLSdLTqnajP2UNBzqFh0FtUk6GCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1679451293\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-809940101 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IlFYS2NhMWttS09RMlV0eXVJVEd3bXc9PSIsInZhbHVlIjoiOHM5bjVYL1c0ejZkdXlyWnpCdlZuUT09IiwibWFjIjoiOWI4M2UzNjYyZTU3MjBmZDU3ZWY0YTRiYTYxYmFlZjBlMGU0NWZlMjA1NmY5YjVmYWUxODQ2ZDY5MzQyMzFiOCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325270164%7C35%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBxRTB1eTUxMnJyc1NaOFJPaHBzanc9PSIsInZhbHVlIjoiMHFtdldQRnBablhva0tDVlA2eGVMaDk1UXA0RWhmTmhYY2xjVHZuV1VPV0hvZGl3cjVrTWJ1UlZ0TWVIZm8rRFF0cDNrRkZvNnNROE9GdkpvZ2tHUmhsY0VFMDk4ZFNZVXhLanZEZnVUZWNGaFExMVNjekR1Qy9vUitzU2lHZGpZa0ZHTm1sRkxqaWY4Z0NWajlITXFKVWo3N3BuTDlPUU12Wm5PNWo2S25Kb1hFdzFXTytVeHJCRm51blNPM0NqSUpnczFaTk90WXdRMzFYaGNDcWppQVdudWQ2NlFWVUNJK2x6VWs3R04vdDh4UzVsaTQweWRUSVJUNlhPYVFuS0lsUE56cStEZGY5Rk5iR1kydmZnTUhSckJiWTFYQWRrM1kyMHE5bUdHRm9YVmNCNTdpMlNYM1NlWEJMeDROUjA0a3FEblJtb0JNT1U1U2tLZmlJSk9oVDlTbWF4Q1dudmpUMjdIRmppL2dPd2NxaENpcU5yWk5JSzhFR2pMK0RPYm1iRjZ4TnZ0bGI3bDdsWDdQemZSL2J0RlhhanRsZE15eWJmaHdwNGNOTTRqOWY5OXJQOXpldzdFMkxIdkVrbzNla1doZUVvR2hQaUEyM3cycjU2WW1kWTl0eGhJcUVSTlNCRnhRekU2UXZRbEVsbkR0VVg2bGZUZEhBVmFLVzAiLCJtYWMiOiJiMTQwOWQxMzY1NWExYmU0N2I3MzNlNDg2ZGM1MWM3YmE0MmNhMmM0OTVhZTIzNzY1MWFmODJmNDMyNjYxYTVjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkxnZ0oxNlFRS3Y2UE8xV2FvUXJIK0E9PSIsInZhbHVlIjoielpNa2V5MGRNVTIyQnVWYWtjNjh4OGhUV3hOcmlTWVF1aXJhWjdINlFSVGVweC8xRHlVNjJZdWpYMVpJR2lUdVZpUHozWmJlWE1iMUZNWVBTRE1ZVjYvdTVuQVpkVXdNQy90cnhkWUtOMTVnZFJnRWdHNnZHVHJDQVkzUER6TllSVjh3Y1FlSmljaTNJZ0dtL0ZVSFpzWFFadnFjS0UwUWdTb1VOV2JRTkJ0RWFmd0RybWNDTkVWN0pEOXRTbE1Vd2VsWnB2MmJnOUtLRnlKd2c4cmhhcWlCYXprbmNDRERKTUU4SzhkU1pOaDBXN0k0QnVvaE00VDhvV1JxNFAyNE9kMmpmaHlzZWx0L3hsSEhXWWIxUTdjcEJZUDh4RzRNS0l1ZmhOaEU5TXZkZlcxWUQwdmN4aUltVndYeE9INytWb0pwaDhuU1lmM0FKQXNiNFJpTnM1NnIzeG4rMU9CeDJXbjVrWXUwL1oxMjZyVURsTk1jc2RHV2ErV3MzdjlLc1RaYzQ5ODZtcjN2UjJlMUp1MXNmTCtxdUVMYy9WZGhmNlBERUZYWnFGNDhmYS9HOXZLa1dmSS9TbTdDR0FVZVRJZlJwWVRaRWpHbEJaNmJmcFFaMldMSFdJRnNhNENSdzQveVBxUEVydSttR1VrdEpvcEhEd1IwZ3cwaUp4WlAiLCJtYWMiOiJmNTg5MjQzOTllOGIzMTg4ODg3NWMxZWQ5NmY2NDQ1NDViZWJmN2FmOTc4ODlhOTQyYTcwZGM1M2RkOGI4ZGMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-809940101\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1892844670 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WuYcucCgl4bBOLSdLTqnajP2UNBzqFh0FtUk6GCo</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">m2aShS6RaaTcTNL9OqnWxuHeTxyTGRJLBdK2tqlv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1892844670\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-873670459 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:14:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVVczlTczJCNGVhWjJiSkNZSXFOMkE9PSIsInZhbHVlIjoiV0RsQTVTMTJ5eHRTMXM0S0g0cmlXSzg0dnNuelhLcU5yclhMYnNOUVduR2s4Qm5jT1hnN3FEVUNHMmNaT0hPY0I4Y01BUTF4RWtlc3kxZnlzY1BKUGwxSkh2Z1Z2c0E2b0xRcysrYUs0TzRBL2plNlhQNFBabVNkQ0xUWmhnbG03OWVXSzlPNXU4Z0RpOXhzMm45T3Q3NEJnMjQ2a2FSQ2lOc3RscWRnY01kRWo0dHhMVEQ5alpWdys1aEF2L1k2WkFjN3VJSUwrK0JhWkUrZFZjczhuMUhHN0RRRzFtZWM0NVlCbmRDMW5VMUlWbHpYc0xZYklqQmNwTFNiV2h4TGIrZlk4WUx0ZW43bVozTHovZWdLKzV5dkpsMkN5blVnQWdPbGpLNE56TDFlb2tYa0ZacVZDeVlKUGlOSGsrZlVaV2ZOMXNmYy9HK25hV2pjQlkwRFBiQy9lN1B6Q2gzeVdER2JTZ1psRGFSVXMyeDhLeWdWSkJsWjc3VTVORndVZUJzMUJ2dXB2bVBNS3J6ZXk3WS9tRDRROE92KzFjK0NRTXZrbjNydkd2NEZxOVlBZFQ0V3J4UTQ3VG1YRnZSTDFvT2NLeGpVZk5KQ3VmZHV4OW9CY01ZWGhBSXNSNVcxcTgvOEcyVG5vcTdqOEdoUk04a0Q5SG8vaGNFM1I4SU4iLCJtYWMiOiJhZGY1ZGY4OTYzNWFmNzNiYTY4ZGU1MTQ1M2ViMTY2MjhiZDg5NGExNmJjN2M3Yjc4MWJmNjliNmNhNTdlZTkzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InNlbUFwR1p6eXloUVNPaytoZ0xoWVE9PSIsInZhbHVlIjoiZUUvUGpXbjZYc3MzSG5ORlBzUHJXYmk3WVAyOEpCdTZxaHk3ZERwUnBDWjA3MWEveVhaRjBveE15Q2JTRzZ2eHNXWGF3a2Y0VmgzVXpVRzdZV2xSNEFGUzRPbDB5azhvbWcvd3NlTm1hM3hNOEZDK3ZISjRlSXpSaUliQlNabEwyNHp0djNCT08wbGFtbzR3aGRPTlFjbWtmcnJ6UzZiMnZ2ZGtUTzl6alkvM0MyUjdKWWtPV2Y2Z3E0REdkYWJ4ajlhZkZkZ05LZ0k2bVpwYlNhN2ZMSHVzYTFTNUhha0xsNkYrSHczNDhTLzRjK2JiTitEY3RVNU9WMzFvWGhqV1M5Tmk2aW5oRzV6L1pKQllRbkttQS9HNUwrOTcwVjZIWGFPUmVoazVuMDVwblpBSWp2SjRCVklBMG9kWXhyTVAzMFZPOXoycUcxY25KQlI3U1VGMjJ3Z2hTZ2ZhaXFrcVhvZTZQWTBpK3lYK1RFTnZxNytZRDZqcklGZm9iS1ltc2Jqb3dvZjVFZnRMbktybzB0U2xRS3A0SUc0OHpwbkNkWXNXTFlnaWRkMUkwOUdRT0pST2hDc0NaY3pDUGk4SVo4REkrengyMnkwN3hUWHY5V1RId3YyYjcyM1ZwMzdkRC9tMXlTZkVDa1lta1pMc0E5RjlIRnQ5YysvMXZ5SHUiLCJtYWMiOiJjZGI2YjhhYWE5NTcyNjVhMTkwMDliYzNhYWI0NDA4YzMwMWVjYmEwOTZhMmQ4Yzg2YzQ2YTRjYTk2OTA0ZmFhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVVczlTczJCNGVhWjJiSkNZSXFOMkE9PSIsInZhbHVlIjoiV0RsQTVTMTJ5eHRTMXM0S0g0cmlXSzg0dnNuelhLcU5yclhMYnNOUVduR2s4Qm5jT1hnN3FEVUNHMmNaT0hPY0I4Y01BUTF4RWtlc3kxZnlzY1BKUGwxSkh2Z1Z2c0E2b0xRcysrYUs0TzRBL2plNlhQNFBabVNkQ0xUWmhnbG03OWVXSzlPNXU4Z0RpOXhzMm45T3Q3NEJnMjQ2a2FSQ2lOc3RscWRnY01kRWo0dHhMVEQ5alpWdys1aEF2L1k2WkFjN3VJSUwrK0JhWkUrZFZjczhuMUhHN0RRRzFtZWM0NVlCbmRDMW5VMUlWbHpYc0xZYklqQmNwTFNiV2h4TGIrZlk4WUx0ZW43bVozTHovZWdLKzV5dkpsMkN5blVnQWdPbGpLNE56TDFlb2tYa0ZacVZDeVlKUGlOSGsrZlVaV2ZOMXNmYy9HK25hV2pjQlkwRFBiQy9lN1B6Q2gzeVdER2JTZ1psRGFSVXMyeDhLeWdWSkJsWjc3VTVORndVZUJzMUJ2dXB2bVBNS3J6ZXk3WS9tRDRROE92KzFjK0NRTXZrbjNydkd2NEZxOVlBZFQ0V3J4UTQ3VG1YRnZSTDFvT2NLeGpVZk5KQ3VmZHV4OW9CY01ZWGhBSXNSNVcxcTgvOEcyVG5vcTdqOEdoUk04a0Q5SG8vaGNFM1I4SU4iLCJtYWMiOiJhZGY1ZGY4OTYzNWFmNzNiYTY4ZGU1MTQ1M2ViMTY2MjhiZDg5NGExNmJjN2M3Yjc4MWJmNjliNmNhNTdlZTkzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InNlbUFwR1p6eXloUVNPaytoZ0xoWVE9PSIsInZhbHVlIjoiZUUvUGpXbjZYc3MzSG5ORlBzUHJXYmk3WVAyOEpCdTZxaHk3ZERwUnBDWjA3MWEveVhaRjBveE15Q2JTRzZ2eHNXWGF3a2Y0VmgzVXpVRzdZV2xSNEFGUzRPbDB5azhvbWcvd3NlTm1hM3hNOEZDK3ZISjRlSXpSaUliQlNabEwyNHp0djNCT08wbGFtbzR3aGRPTlFjbWtmcnJ6UzZiMnZ2ZGtUTzl6alkvM0MyUjdKWWtPV2Y2Z3E0REdkYWJ4ajlhZkZkZ05LZ0k2bVpwYlNhN2ZMSHVzYTFTNUhha0xsNkYrSHczNDhTLzRjK2JiTitEY3RVNU9WMzFvWGhqV1M5Tmk2aW5oRzV6L1pKQllRbkttQS9HNUwrOTcwVjZIWGFPUmVoazVuMDVwblpBSWp2SjRCVklBMG9kWXhyTVAzMFZPOXoycUcxY25KQlI3U1VGMjJ3Z2hTZ2ZhaXFrcVhvZTZQWTBpK3lYK1RFTnZxNytZRDZqcklGZm9iS1ltc2Jqb3dvZjVFZnRMbktybzB0U2xRS3A0SUc0OHpwbkNkWXNXTFlnaWRkMUkwOUdRT0pST2hDc0NaY3pDUGk4SVo4REkrengyMnkwN3hUWHY5V1RId3YyYjcyM1ZwMzdkRC9tMXlTZkVDa1lta1pMc0E5RjlIRnQ5YysvMXZ5SHUiLCJtYWMiOiJjZGI2YjhhYWE5NTcyNjVhMTkwMDliYzNhYWI0NDA4YzMwMWVjYmEwOTZhMmQ4Yzg2YzQ2YTRjYTk2OTA0ZmFhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-873670459\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1097685814 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WuYcucCgl4bBOLSdLTqnajP2UNBzqFh0FtUk6GCo</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IlFYS2NhMWttS09RMlV0eXVJVEd3bXc9PSIsInZhbHVlIjoiOHM5bjVYL1c0ejZkdXlyWnpCdlZuUT09IiwibWFjIjoiOWI4M2UzNjYyZTU3MjBmZDU3ZWY0YTRiYTYxYmFlZjBlMGU0NWZlMjA1NmY5YjVmYWUxODQ2ZDY5MzQyMzFiOCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1097685814\", {\"maxDepth\":0})</script>\n"}}