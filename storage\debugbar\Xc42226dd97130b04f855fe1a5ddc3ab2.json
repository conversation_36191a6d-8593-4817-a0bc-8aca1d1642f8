{"__meta": {"id": "Xc42226dd97130b04f855fe1a5ddc3ab2", "datetime": "2025-06-30 22:36:51", "utime": **********.122586, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323010.662461, "end": **********.122607, "duration": 0.4601459503173828, "duration_str": "460ms", "measures": [{"label": "Booting", "start": 1751323010.662461, "relative_start": 0, "end": **********.05382, "relative_end": **********.05382, "duration": 0.3913588523864746, "duration_str": "391ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.05383, "relative_start": 0.3913688659667969, "end": **********.122609, "relative_end": 1.9073486328125e-06, "duration": 0.06877899169921875, "duration_str": "68.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45724064, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.018290000000000004, "accumulated_duration_str": "18.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0802839, "duration": 0.016890000000000002, "duration_str": "16.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.346}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.106133, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.346, "width_percent": 3.226}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1121912, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.571, "width_percent": 4.429}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-491874251 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-491874251\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1115596532 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1115596532\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-428945592 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-428945592\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-860149974 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=6nrx0y%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1aj6s00%7C1751323007905%7C5%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkduZG90RGFSWWtxVFlaQUwrT2FMQ2c9PSIsInZhbHVlIjoibzdPSGFBTERVYS96NjQvVzBCdmxhNVZnb2ZOSUFRS1pLVFltV3Z1bjBXZ3Y5aXZrZ2JHS2dBWUY0ajh3c0hTeFZleHQvdlJIOEhpT3FHcUp4VFJYdG9qYkQ5VWRONU9JN3FVUEc2UE85TzFUTFNWbzlxVGpqd0MyUTVnWDVyT2ZnZUtoYks4M0lXelcvZklGWkQ1djRMeHFUQnZML0NWYTU4ODExSVNUU1ZSWHI1TG1sWS9DMDltQ1ZWcGF6STdNc1cyaEc4UjJka1ExRlRzYUJRTkpuTThHNjdpdmd2dE9YdGNEbHBQaGhFM0QwTWxmbXdhK0JxN2Y0dDVyRkVIVTRJa3d3ZGVGZjcrbEx1ZzVOb3h3VDZtWXQyRHlFdHhFQ0lxdVYwQUh5cGFVdklmaWNTMnZNU21sTW50YWFjY3lPdmYwZFNQN3NMSEZxMXEwMEUvRklleVdwQWNJOGhDRTV0eWFCTndJZmlkVUNlZW5wdS9mSC8yUGgwMkhFWHoxdVh4d1VBd0xTaElNczJuOUtjUHhob1RzWnU3L0tpTDNyeUZienFyMHRJdXVMd0c0bWZuUkd0Rzd3Z0Q4TVFNbUdzUGtXN3Nxc2QzcHhjMlpxbG40YUduWEt6VFhlOXpwUGN4S1YzU2QvcTZjYkplSlIxRmcyZmFqaVhZWDdOaWYiLCJtYWMiOiJhNTcxMzVlZDFiZWEyOWQxODFkMTBlOTQ5N2EwMGQyMjQ1NTEyYmY2MzcwZDVkYWI5YTMyZDQwYmU2MTE0ZDE2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjFWK016RHhIZGNMQ2ZvWWsrc21GWFE9PSIsInZhbHVlIjoiOGNTOXFEdVhOaXY4Zytzem05SHJrZk1ZQlVCdlMxYjhkeEgzYXc2dVNkbjhPdjdCaDByaDVyMjNFY09lZGxNdXZieFFoc2pYUXdCUmFkQ2RwdkhWdHovVlJyb0l4dGE1OW5rYmgrZGtIbVNRakZHQUMreXZqcmVGWjNWT3FYeVh4TEhONjZBTGlkRTRBUkdQdjdHalhkVVdBdXV5ZHFRNm9hQTFKQVZXNEpMbUlvWkYxb1VYZ21wR2ZyNm9lZkdyNGI5b20xYXhIWHhEMmxGVDhaUUdSaUUrakFUdENwOUhxMjNUK3VoOFludVZTNGxRNjlrWHRmTGJxWk1CU3pyeEF4bVVCWWsyZWJNa2dTZTNIRHBZbTdURmNLVUd5RFRiVm1iWitIc1JadXBsTzNIbkt3dmtESHVDNnYwWnpqVm9LaVh0bXNQT210OWw0YmZ0b0hER2x2MVRuNFp5eThPdlJPbmFEU0JzVUZTOXRhRVZGakN0eTlGTm1TMlBoQ0cwTGw1c0ZNVVdYR0JxRzJyc2U0L0M5UmU5SHV5QjM3ZDlISGZtRDZoRGszZFVrZnJmWENvNFVPc2ZWSHBXTCszMXVOK2VCT1EvRDRXbGFCOVVOZUpRNVliczFFMmt1a2NDOEFqZE9YazVjODlJYkxHN25ZVXFtZ0dCZzBWRTVyMmkiLCJtYWMiOiJiOWZiMDRlMmU3N2ZjMmI1YjU4NzZlNWE1ZTAyOTJkZGI0MTYwZjc4MzdhNzZiZDVhYjVhMzUzZDA0MmY5NjIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-860149974\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-212025349 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A9rdyCRm7SKpfljuMnVO7IpcgTBMITMjowAdsmJo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-212025349\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:36:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjN4T05Qc2xxaUc4SmdIb0t1aG1jc3c9PSIsInZhbHVlIjoiZ0FWZ3l3WVBkdlNEa2RlOVFiN0g4VGtwODY0U0l4dVVIVktjME00RU02WDNUSGIxeTJWd3hYOHRubXVmV0E0UVRuSUlXMFlCVnZWVmVoTVRHQVBHK1VSdFhRT2UyMERHWTBEUXY4UXFyQ20xaXl4Rk9POWo5SEdXVXBIanJETEhDK1pZOS8zSVpna292b2E2Qk4zNGN4ajI4RFlxVWNBZ2MxQ055a2h6NDBISm8vUmE2Vjh4OWFVYUM2Nzl6ak5IUjBxRUh1dXlxY0FycDRpUXBSSms3L3J5V095WWw5T3JVN3JEQ3dBaGVGWit0NVN2RVo4VGdNSytQTFhzdEl5eCtDdzFoS3Q0MVFwYU0yaGtmc3Z3c3JySVpadElobFE3WmlJbUVXSEo0VnpTM1djRjdDUFp0MnhHeC9STTZTRDdRQm1ZUkcwM3NZZjh1SlNyQk1XeDNmMG9zLzVvTXcxRFI3Wm9QT1FRTXBtRXRDNU5tSkMvRE5DaXlsaFpTcDJJc25wNGNaYWJXcE93WGtMOEtyeGtXOU5Za0lJcG5TVjB2Y3pYMmovL1F2ZmxjbTVUR1JMYWxnSU5CTlNXblBaMzlpT1l6aXVtb2JDbDhETHdlUGVzY0tDSkNmOXRmNkNKeTlFQVlOcldaWWNUSDVvMW9TQmVJS0dYQU01YzRUOGoiLCJtYWMiOiIzNzU3NDI5NWYyNjg4NjAyMDJlNGIzZGMyMGVkZWUzZmRmMGY0MjE2ODVhYmUxMDQwOWYwNDYxMjE5YjQyY2Y5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im9TcE1RN3dBRkVzYTRhTDlnRUNGZFE9PSIsInZhbHVlIjoiSndzSi9DZ1J2M1d1Q2FBOGl5OFBJTm9NQnpEL05GWWRVTE5JSmJ2YU9uZ2IvcUlPbkI5bTdsTWhYVkl4K2xrU3hxbVRZMjJvTUNCd3ZqcXNqV0JYR2lGN1MyZ2VQMlNXU29nM3ZZVHNnYTk0VVlIRXd4cEhCV2pJc0Y0YjE4NEd3UDdqbDJhV0NEcnJZT3g5QzdvQkV1ZnA5TmdPOXgveHdNK21PV0hiUDZ5RjRSK1djN3djb0tUcTdpSFIyMXdxQzNhSDVRZ3pPcFRPWWx5RmhKSU5hbm9uUHp0MWdRcVF1dXdkd21ick4zRHZGdzF2Vmt0Q2pEaUJLNkU2SzRFcFlEM01ndEZWVTFtSmdVb3ErWTVPN2xCaENHZ29lOW5oMlRONFUzckpOSXJOd21PMExSbU1vU1NLUmRRNVRERlBYdXdSM0RZdWduK3ptb1dhNEFmZHRUb0RuNUxyeVZPbU8zVUJvdVZPOHRGK21VY0RZaGdhWmtxVDkrL0hQK1BwUG9Sdk1EVXRpbHE3Z2daVG9mbW9POTB6VE4wQnZ6Y3RIOHNHQS9oWUQwaTJkbDBGblNubllWZkhJZkRXbXBPalpPM0JrY3ZoM21nZ0ZKZkdHckFVM041am5BRFM3bUFWRDA2OHM5VTU3M1QxMkhKZXdIam1kZDkyVDR6aTJ2OU0iLCJtYWMiOiI4ODQwNDcwNzc2ZDFmY2ExN2MzZGQzMWQ2NzlkMDY3MWIwYWFiN2UxNGFkOGU1YjQxZDVmYjc3Y2VjZGYyMDc1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjN4T05Qc2xxaUc4SmdIb0t1aG1jc3c9PSIsInZhbHVlIjoiZ0FWZ3l3WVBkdlNEa2RlOVFiN0g4VGtwODY0U0l4dVVIVktjME00RU02WDNUSGIxeTJWd3hYOHRubXVmV0E0UVRuSUlXMFlCVnZWVmVoTVRHQVBHK1VSdFhRT2UyMERHWTBEUXY4UXFyQ20xaXl4Rk9POWo5SEdXVXBIanJETEhDK1pZOS8zSVpna292b2E2Qk4zNGN4ajI4RFlxVWNBZ2MxQ055a2h6NDBISm8vUmE2Vjh4OWFVYUM2Nzl6ak5IUjBxRUh1dXlxY0FycDRpUXBSSms3L3J5V095WWw5T3JVN3JEQ3dBaGVGWit0NVN2RVo4VGdNSytQTFhzdEl5eCtDdzFoS3Q0MVFwYU0yaGtmc3Z3c3JySVpadElobFE3WmlJbUVXSEo0VnpTM1djRjdDUFp0MnhHeC9STTZTRDdRQm1ZUkcwM3NZZjh1SlNyQk1XeDNmMG9zLzVvTXcxRFI3Wm9QT1FRTXBtRXRDNU5tSkMvRE5DaXlsaFpTcDJJc25wNGNaYWJXcE93WGtMOEtyeGtXOU5Za0lJcG5TVjB2Y3pYMmovL1F2ZmxjbTVUR1JMYWxnSU5CTlNXblBaMzlpT1l6aXVtb2JDbDhETHdlUGVzY0tDSkNmOXRmNkNKeTlFQVlOcldaWWNUSDVvMW9TQmVJS0dYQU01YzRUOGoiLCJtYWMiOiIzNzU3NDI5NWYyNjg4NjAyMDJlNGIzZGMyMGVkZWUzZmRmMGY0MjE2ODVhYmUxMDQwOWYwNDYxMjE5YjQyY2Y5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im9TcE1RN3dBRkVzYTRhTDlnRUNGZFE9PSIsInZhbHVlIjoiSndzSi9DZ1J2M1d1Q2FBOGl5OFBJTm9NQnpEL05GWWRVTE5JSmJ2YU9uZ2IvcUlPbkI5bTdsTWhYVkl4K2xrU3hxbVRZMjJvTUNCd3ZqcXNqV0JYR2lGN1MyZ2VQMlNXU29nM3ZZVHNnYTk0VVlIRXd4cEhCV2pJc0Y0YjE4NEd3UDdqbDJhV0NEcnJZT3g5QzdvQkV1ZnA5TmdPOXgveHdNK21PV0hiUDZ5RjRSK1djN3djb0tUcTdpSFIyMXdxQzNhSDVRZ3pPcFRPWWx5RmhKSU5hbm9uUHp0MWdRcVF1dXdkd21ick4zRHZGdzF2Vmt0Q2pEaUJLNkU2SzRFcFlEM01ndEZWVTFtSmdVb3ErWTVPN2xCaENHZ29lOW5oMlRONFUzckpOSXJOd21PMExSbU1vU1NLUmRRNVRERlBYdXdSM0RZdWduK3ptb1dhNEFmZHRUb0RuNUxyeVZPbU8zVUJvdVZPOHRGK21VY0RZaGdhWmtxVDkrL0hQK1BwUG9Sdk1EVXRpbHE3Z2daVG9mbW9POTB6VE4wQnZ6Y3RIOHNHQS9oWUQwaTJkbDBGblNubllWZkhJZkRXbXBPalpPM0JrY3ZoM21nZ0ZKZkdHckFVM041am5BRFM3bUFWRDA2OHM5VTU3M1QxMkhKZXdIam1kZDkyVDR6aTJ2OU0iLCJtYWMiOiI4ODQwNDcwNzc2ZDFmY2ExN2MzZGQzMWQ2NzlkMDY3MWIwYWFiN2UxNGFkOGU1YjQxZDVmYjc3Y2VjZGYyMDc1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-182744468 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-182744468\", {\"maxDepth\":0})</script>\n"}}