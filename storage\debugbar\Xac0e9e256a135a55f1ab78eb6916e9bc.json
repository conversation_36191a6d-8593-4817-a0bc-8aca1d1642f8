{"__meta": {"id": "Xac0e9e256a135a55f1ab78eb6916e9bc", "datetime": "2025-06-30 23:08:11", "utime": **********.342291, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751324890.898573, "end": **********.342311, "duration": 0.4437379837036133, "duration_str": "444ms", "measures": [{"label": "Booting", "start": 1751324890.898573, "relative_start": 0, "end": **********.291091, "relative_end": **********.291091, "duration": 0.3925180435180664, "duration_str": "393ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.2911, "relative_start": 0.39252710342407227, "end": **********.342313, "relative_end": 2.1457672119140625e-06, "duration": 0.05121302604675293, "duration_str": "51.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45363416, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0026899999999999997, "accumulated_duration_str": "2.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.321883, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.428}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.332238, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.428, "width_percent": 17.844}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3352342, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 83.271, "width_percent": 16.729}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-656757556 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323255132%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhkMUZpQVZLazdqRmFCeEtQVkNkWHc9PSIsInZhbHVlIjoiRVN6SDM4S0V3TjBhMVNhdHM4aW9Idkc0TnNZdnZxZjUwVUZqUTI0K0srTldJSkduN1lyVlNMMm90cFJhZEFpQkJBMmVremg4Ums2WjNxbk9rNWE4T2VIVUcxWFRnT3NJeW5LbS9KWkJPZFZvUEZSOWVENGFGamlqeDhtdEJaTEJObmJUVjhRbFFvcEliU3JaYjlNb1dsWE9FSTc3cGt4VkZOeVB5MHlocVBzaVBzbVczOWFUbTd6eXd6bmlYS0FuUlI0VStPUWI0T3dVT0Q2cGd0WHVTU3dtVGVDbmJ4UlRnQ2hLMTdad3BETzZZRU1td3BIU0w2V1E1czFYS1h0Y21uMDdVQzFsc3p5KzljdXNKbm1FRFZUNHRMcEtHRStVdFVpelVGSFFhSENMcFNETDkvUFJKRXExbVE0ZHBOY0N4QlpRQS9xdlNHa1ExNUw0SUQ4d1RvOTRaREc4MUNJQkg2Nmx4Rzk3L01IdlZ2K2V1WVl2S1A1eVBzaXVvdzRDM2xCbTZyaGlyRUJ3ek02b1VSUVJjaTFsVWZmV3d4TmEyQ3Qyb2dwNXYrMHlBMEtzTUF5UlZWNUVhQytUellBdFc3dWNnaXlDZjJjZVYvV3BESGMvRjRRSkdSWXEzc3pJVEJLVHBlK242T2dxUzJENnlDQkNQaDUycXNFTG9vMlIiLCJtYWMiOiIwZjcyM2QxZTg2MzU1YjIxOTZiNDQxYmYxZDlkNDNmOWU5OTVjNzc2MmUyMTZkMGRiNTMyYmJjOWY5ZDEyMDQ1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IitmaWVMczBMaEtPN0lUbUZDT2s5alE9PSIsInZhbHVlIjoidGNWVUpOWHBOa0VMQkVGckNnbGV3MFJuQzlwdmxjZ3lOTXJMelI1MDF2OUdMa2YzeklkRlExd2RWVlZlSThZVzFub3ozMXdiejNkOVZqU3pUUjJ0eFlmUW85RlYxbkNoMzc0MXVKdXJmQk5GL1dtakc2WnV2UXF4QmtvL3JOU3BsclR1eDdVYUphamJrQmo5YVYyZ1hxeG1MNlZmbnRKK244NkMyRXJBWXpjR05oVGJCc1EySC9PckFwMDhIL3QvenEvZU52RWRpMnZYMzVINHplVVJXK05oTG1mazFVRXVyZmxMcU42U1JlcFR6a2FHM2JHcTBDbkJDMVV0VCt6bnRUZUJZMkFRMSt3S1BkVFBQdlMzRFB3V1JtMHJaQ2IzT0ttdFh2MjhSbFVaTTUrR1pTeE93MVpvOU1ISVd4cjkvbXdSMC9LT0JpdC9qNzJoRWFXVysyVm1MOVkrZmxmdGxkMkwvRStTeHpKazBUWGdwVjNlRWJhUXpWcGNKV0lyVW9NSEpFWHNjaHVYdFVCU1VTWHcyb2x5c2pVVTNUaWNUN0RBUTlnbzA5MHU4NE1lOE02UTVUaU5ROVhZbVZDckJ0dHNHaGM0Zm9ueUlNSno3eERMSDhQb28xOFNWazVzSHY0MzJxaDhocHY5THlGbnYyWlUzR2pRbXRudTJSZHciLCJtYWMiOiI1MzA4ZjI2NTJiMTNkZDljYmNmOWRhYjVkZTlhZTg4N2UxMTNhMjJjOGFkZTBiNjJhNGMyYjIzODkxZmJkMDRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-656757556\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1310410993 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1310410993\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-467739677 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:08:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFKbVJEcDgrTFcxY1F0SGcrKy9SZ2c9PSIsInZhbHVlIjoiTHpIMXlRWTcwOVhpeHhqTUc4VWg5UjhvT2FsSmhlQkpwVlR1aDRaZzRLUG1HUSs5dHl2Z1pPeW5rNG1Lb3RJN2VMekt3c3hOSUdyL2orZ2ZGWVEwMnBCTnZOZVN3ZTEwbU5xdzhFbUd6WEFIbUtMYTNEOGp6cDE0a2ttSHMza2ZaV2NJTXZFOHlrdWNvaHVsU1M4VTRHV09pSWVlbVZJV2V2NnU3UEJNcE5RSXJCMEc5bllJTUtGOXUxQTdKY3VqS0pxOVlvRW5FV0ZaNUF3V0k1Tmk3czNKcEFpRGphazdVT3ZiYmlVTXBNQjVyb0VZWCs5ekRCejVoR05oMHhrMjJsTnIwSnB6N3N6L2tvdS9lMTN1VTVESWZBV2U5TjRTZ250cE5rcUVUUHJjNnZlOGdoRkczM2NkL0ZTWHJKNG1LUVZWdFl6R0YwR3daa0NPZjJIOURnbDJkR2licXMxak43RytFUmhuZVkxV1lpU3dOdmRGY3BJNE12TDAyMlFhWFF1blRpR01ZdURUYXZZNXpoM2VHcUM1YlR1Tzg4OFIrSFE4c1BMTFRzSXJWM1dGYVA3a1Y1aGVWd3RQdE1uNUdGNzM1S2QvZDdvT0NNcVlhSnhsRUtmVSt6SDJIbGFDUnFrWG83TFoxS2xUNDY1c2plNXFJME9jT25tZDBjTUwiLCJtYWMiOiI5YmQ4ZmZkYzZlYjlkMmIyY2M2NDgwNmMwMDNiZGEyZTE2NDBkNzJlNjMzMGZiZGFjMjJkZjZiZDk4NDJjMGE0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:08:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjJlb05hUG9JbW9WdU1ES1pFdzJNOWc9PSIsInZhbHVlIjoiMzd0NWhYZ05wMldsMmtzTXNSRk9KODdXSk0xU1JrTGRZNTBMZzE5TThEcFJ2WUFkdDhOckd6VjlrWjk0UWZaMGw5VS9aTDFTMGx5QmM5Y0xiQXR2RTZldWZ1bWo5Wk5VeDlFbWkzUTl6UkROWCtqODBTQXpQc2hWSjVsOGMxN0FaeXgyRnV3SjNSVDVsM0dXSE56Tm5qMjd4VkxmWlFuSEp6Sm8xcTQ5TnJiK3U4VERmRExQMUNLUU5leFZoNWZsWXlteEV2c1hUYktOamliVzJXTTVxK3puR2JycWVYV3NuMXlZUWd2by9hcXpLQmNZUFZXWXlrOFl3QndFL3NTa3Qvem03SnJTV3pHUUJKanI3UExKbGFvNm0xaExmWEZyeEUzTnpEUFExR01KSTltNmlybEtWdEZjMWlKQ0hBNy9XMml5YVV6STBFUXFrL0RYRnRBaGJzbTZCMndORW9EZi85ZlJ0L3diYlV6K2daWUlkekg5NjlXS1c2NXRtQ2svMkRBSGpUQzZMbjdybkR5VzRZME56RCtzTEQ2a1lBWkZ1d3RtSmFucnVOTVhwSSt2MXlrRCswaWEzT3lveElOaWlBOTZsTDRuZUJrSmF6aU1HeXp2UlBxcHVJNWNETklORWxvUDU0czY2dVllckw4Z1dtZ2gxb0F6a0ltYnovS1QiLCJtYWMiOiI1YmY1ZmE0ODM0ZDIwNDllZGQ3ZmVkYTQ5OWJhYjIyNTljZDE1ODI1M2E5NWU2MTIxOTc4NTI3ZjVmOTAwNjA5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:08:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFKbVJEcDgrTFcxY1F0SGcrKy9SZ2c9PSIsInZhbHVlIjoiTHpIMXlRWTcwOVhpeHhqTUc4VWg5UjhvT2FsSmhlQkpwVlR1aDRaZzRLUG1HUSs5dHl2Z1pPeW5rNG1Lb3RJN2VMekt3c3hOSUdyL2orZ2ZGWVEwMnBCTnZOZVN3ZTEwbU5xdzhFbUd6WEFIbUtMYTNEOGp6cDE0a2ttSHMza2ZaV2NJTXZFOHlrdWNvaHVsU1M4VTRHV09pSWVlbVZJV2V2NnU3UEJNcE5RSXJCMEc5bllJTUtGOXUxQTdKY3VqS0pxOVlvRW5FV0ZaNUF3V0k1Tmk3czNKcEFpRGphazdVT3ZiYmlVTXBNQjVyb0VZWCs5ekRCejVoR05oMHhrMjJsTnIwSnB6N3N6L2tvdS9lMTN1VTVESWZBV2U5TjRTZ250cE5rcUVUUHJjNnZlOGdoRkczM2NkL0ZTWHJKNG1LUVZWdFl6R0YwR3daa0NPZjJIOURnbDJkR2licXMxak43RytFUmhuZVkxV1lpU3dOdmRGY3BJNE12TDAyMlFhWFF1blRpR01ZdURUYXZZNXpoM2VHcUM1YlR1Tzg4OFIrSFE4c1BMTFRzSXJWM1dGYVA3a1Y1aGVWd3RQdE1uNUdGNzM1S2QvZDdvT0NNcVlhSnhsRUtmVSt6SDJIbGFDUnFrWG83TFoxS2xUNDY1c2plNXFJME9jT25tZDBjTUwiLCJtYWMiOiI5YmQ4ZmZkYzZlYjlkMmIyY2M2NDgwNmMwMDNiZGEyZTE2NDBkNzJlNjMzMGZiZGFjMjJkZjZiZDk4NDJjMGE0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:08:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjJlb05hUG9JbW9WdU1ES1pFdzJNOWc9PSIsInZhbHVlIjoiMzd0NWhYZ05wMldsMmtzTXNSRk9KODdXSk0xU1JrTGRZNTBMZzE5TThEcFJ2WUFkdDhOckd6VjlrWjk0UWZaMGw5VS9aTDFTMGx5QmM5Y0xiQXR2RTZldWZ1bWo5Wk5VeDlFbWkzUTl6UkROWCtqODBTQXpQc2hWSjVsOGMxN0FaeXgyRnV3SjNSVDVsM0dXSE56Tm5qMjd4VkxmWlFuSEp6Sm8xcTQ5TnJiK3U4VERmRExQMUNLUU5leFZoNWZsWXlteEV2c1hUYktOamliVzJXTTVxK3puR2JycWVYV3NuMXlZUWd2by9hcXpLQmNZUFZXWXlrOFl3QndFL3NTa3Qvem03SnJTV3pHUUJKanI3UExKbGFvNm0xaExmWEZyeEUzTnpEUFExR01KSTltNmlybEtWdEZjMWlKQ0hBNy9XMml5YVV6STBFUXFrL0RYRnRBaGJzbTZCMndORW9EZi85ZlJ0L3diYlV6K2daWUlkekg5NjlXS1c2NXRtQ2svMkRBSGpUQzZMbjdybkR5VzRZME56RCtzTEQ2a1lBWkZ1d3RtSmFucnVOTVhwSSt2MXlrRCswaWEzT3lveElOaWlBOTZsTDRuZUJrSmF6aU1HeXp2UlBxcHVJNWNETklORWxvUDU0czY2dVllckw4Z1dtZ2gxb0F6a0ltYnovS1QiLCJtYWMiOiI1YmY1ZmE0ODM0ZDIwNDllZGQ3ZmVkYTQ5OWJhYjIyNTljZDE1ODI1M2E5NWU2MTIxOTc4NTI3ZjVmOTAwNjA5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:08:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-467739677\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}