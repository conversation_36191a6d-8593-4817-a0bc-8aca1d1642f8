<?php

namespace App\Http\Controllers;

use App\Models\Shift;
use App\Models\ReceiptVoucher;
use App\Models\PaymentVoucher;
use App\Models\Pos;
use App\Models\PosPayment;
use App\Models\warehouse;
use App\Models\User;
use App\Models\FinancialRecord;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdvancedCashManagementController extends Controller
{
    /**
     * Display the advanced cash management dashboard
     */
    public function index(Request $request)
    {
        // Get filter parameters
        $warehouseId = $request->input('warehouse_id');
        $userId = $request->input('user_id');
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->endOfMonth()->format('Y-m-d'));

        // Get warehouses and users for filters
        $warehouses = warehouse::where('created_by', Auth::user()->creatorId())->get();
        $users = User::where('created_by', Auth::user()->creatorId())->get();

        return view('financial_operations.advanced_cash_management.index', compact(
            'warehouses', 
            'users', 
            'warehouseId', 
            'userId', 
            'startDate', 
            'endDate'
        ));
    }

    /**
     * Get quick statistics for the dashboard
     */
    public function getQuickStats(Request $request)
    {
        $warehouseId = $request->input('warehouse_id');
        $userId = $request->input('user_id');
        $startDate = $request->input('start_date', Carbon::today()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::today()->format('Y-m-d'));
        $creatorId = Auth::user()->creatorId();

        // Daily receipts (from receipt vouchers + POS cash sales)
        // First try direct query, then with relationship if needed
        $receiptVouchersQuery = ReceiptVoucher::where('status', 'accepted')
            ->whereBetween('date', [$startDate, $endDate]);

        if ($warehouseId) {
            $receiptVouchersQuery->where('warehouse_id', $warehouseId);
        }
        if ($userId) {
            $receiptVouchersQuery->where('created_by', $userId);
        }

        $receiptVouchersTotal = $receiptVouchersQuery->sum('payment_amount') ?? 0;

        // POS cash sales - simplified query (excluding deferred payments)
        $posCashQuery = PosPayment::join('pos', 'pos_payments.pos_id', '=', 'pos.id')
            ->whereBetween('pos.pos_date', [$startDate, $endDate])
            ->where('pos_payments.payment_type', '!=', 'deferred'); // Exclude deferred payments

        if ($warehouseId) {
            $posCashQuery->where('pos.warehouse_id', $warehouseId);
        }
        if ($userId) {
            $posCashQuery->where('pos.created_by', $userId);
        }

        $posCashTotal = $posCashQuery->sum('pos_payments.cash_amount') ?? 0;

        // POS deferred sales - separate query for deferred payments
        $posDeferredQuery = PosPayment::join('pos', 'pos_payments.pos_id', '=', 'pos.id')
            ->whereBetween('pos.pos_date', [$startDate, $endDate])
            ->where('pos_payments.payment_type', '=', 'deferred'); // Only deferred payments

        if ($warehouseId) {
            $posDeferredQuery->where('pos.warehouse_id', $warehouseId);
        }
        if ($userId) {
            $posDeferredQuery->where('pos.created_by', $userId);
        }

        $posDeferredTotal = $posDeferredQuery->sum('pos_payments.amount') ?? 0;

        $dailyReceipts = $receiptVouchersTotal + $posCashTotal;

        // Daily payments (from payment vouchers) - simplified query
        $paymentVouchersQuery = PaymentVoucher::where('status', 'accepted')
            ->whereBetween('date', [$startDate, $endDate]);

        if ($warehouseId) {
            $paymentVouchersQuery->where('warehouse_id', $warehouseId);
        }
        if ($userId) {
            $paymentVouchersQuery->where('created_by', $userId);
        }

        $dailyPayments = $paymentVouchersQuery->sum('payment_amount') ?? 0;

        // Net cash (excluding deferred amounts)
        $netCash = $dailyReceipts - $dailyPayments;

        // Open shifts count - simplified query
        $openShiftsQuery = Shift::where('is_closed', false);

        if ($warehouseId) {
            $openShiftsQuery->where('warehouse_id', $warehouseId);
        }
        if ($userId) {
            $openShiftsQuery->where('created_by', $userId);
        }

        $openShifts = $openShiftsQuery->count();

        // Calculate total deficit for open shifts
        $totalDeficitQuery = FinancialRecord::whereHas('shift', function ($query) {
                $query->where('is_closed', false);
            });

        if ($warehouseId) {
            $totalDeficitQuery->whereHas('shift', function ($query) use ($warehouseId) {
                $query->where('warehouse_id', $warehouseId);
            });
        }
        if ($userId) {
            $totalDeficitQuery->whereHas('shift', function ($query) use ($userId) {
                $query->where('created_by', $userId);
            });
        }

        $totalDeficit = $totalDeficitQuery->sum('deficit') ?? 0;

        // Count active warehouses (with open shifts)
        $activeWarehousesQuery = Shift::where('is_closed', false)
            ->distinct('warehouse_id');

        if ($warehouseId) {
            $activeWarehousesQuery->where('warehouse_id', $warehouseId);
        }
        if ($userId) {
            $activeWarehousesQuery->where('created_by', $userId);
        }

        $activeWarehouses = $activeWarehousesQuery->count('warehouse_id');

        return response()->json([
            'daily_receipts' => number_format($dailyReceipts, 2),
            'daily_payments' => number_format($dailyPayments, 2),
            'net_cash' => number_format($netCash, 2),
            'deferred_amount' => number_format($posDeferredTotal, 2), // Add deferred amount separately
            'open_shifts' => $openShifts,
            'total_deficit' => number_format($totalDeficit, 2),
            'active_warehouses' => $activeWarehouses
        ]);
    }

    /**
     * Get shifts data for the table
     */
    public function getShiftsData(Request $request)
    {
        $warehouseId = $request->input('warehouse_id');
        $userId = $request->input('user_id');
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->endOfMonth()->format('Y-m-d'));
        $creatorId = Auth::user()->creatorId();

        $query = Shift::with(['financialRecord', 'creator', 'closer'])
            ->join('warehouses', 'shifts.warehouse_id', '=', 'warehouses.id')
            ->select('shifts.*', 'warehouses.name as warehouse_name')
            ->whereBetween('shifts.opened_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']);

        if ($warehouseId) {
            $query->where('shifts.warehouse_id', $warehouseId);
        }
        if ($userId) {
            $query->where('shifts.created_by', $userId);
        }

        $shifts = $query->orderBy('shifts.opened_at', 'desc')->get();

        return response()->json([
            'data' => $shifts->map(function ($shift) {
                return [
                    'id' => $shift->id,
                    'warehouse_name' => $shift->warehouse_name,
                    'creator_name' => $shift->creator->name ?? '',
                    'closer_name' => $shift->closer->name ?? '',
                    'opened_at' => $shift->opened_at ? $shift->opened_at->format('Y-m-d H:i') : '',
                    'closed_at' => $shift->closed_at ? $shift->closed_at->format('Y-m-d H:i') : '',
                    'opening_balance' => number_format($shift->shift_opening_balance, 2),
                    'current_cash' => $shift->financialRecord ? number_format($shift->financialRecord->current_cash, 2) : '0.00',
                    'overnetwork_cash' => $shift->financialRecord ? number_format($shift->financialRecord->overnetwork_cash, 2) : '0.00',
                    'total_cash' => $shift->financialRecord ? number_format($shift->financialRecord->total_cash, 2) : '0.00',
                    'deficit' => $shift->financialRecord ? number_format($shift->financialRecord->deficit, 2) : '0.00',
                    'is_closed' => $shift->is_closed,
                    'status' => $shift->is_closed ? 'مغلق' : 'نشط',
                    'status_class' => $shift->is_closed ? 'badge-closed' : 'badge-active'
                ];
            })
        ]);
    }

    /**
     * Get receipt vouchers data
     */
    public function getReceiptVouchers(Request $request)
    {
        $warehouseId = $request->input('warehouse_id');
        $userId = $request->input('user_id');
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->endOfMonth()->format('Y-m-d'));
        $creatorId = Auth::user()->creatorId();

        $query = ReceiptVoucher::with(['creator', 'receiptFrom', 'warehouse'])
            ->whereBetween('date', [$startDate, $endDate]);

        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }
        if ($userId) {
            $query->where('created_by', $userId);
        }

        $vouchers = $query->orderBy('date', 'desc')->get();

        return response()->json([
            'data' => $vouchers->map(function ($voucher) {
                return [
                    'id' => $voucher->id,
                    'custome_id' => $voucher->custome_id,
                    'date' => Carbon::parse($voucher->date)->format('Y-m-d'),
                    'amount' => number_format($voucher->payment_amount, 2),
                    'payment_method' => $voucher->payment_method == 'cash' ? 'نقد' : 'تحويل بنكي',
                    'purpose' => $voucher->purpose,
                    'received_from' => $voucher->receiptFrom->name ?? '',
                    'creator_name' => $voucher->creator->name ?? '',
                    'warehouse_name' => $voucher->warehouse->name ?? '',
                    'status' => $voucher->status,
                    'status_class' => $voucher->status == 'accepted' ? 'bg-success' : ($voucher->status == 'pending' ? 'bg-warning' : 'bg-danger'),
                    'created_at' => $voucher->created_at->toISOString(), // For real-time detection
                ];
            })
        ]);
    }

    /**
     * Get payment vouchers data
     */
    public function getPaymentVouchers(Request $request)
    {
        $warehouseId = $request->input('warehouse_id');
        $userId = $request->input('user_id');
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->endOfMonth()->format('Y-m-d'));
        $creatorId = Auth::user()->creatorId();

        $query = PaymentVoucher::with(['creator', 'payTo', 'warehouse'])
            ->whereBetween('date', [$startDate, $endDate]);

        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }
        if ($userId) {
            $query->where('created_by', $userId);
        }

        $vouchers = $query->orderBy('date', 'desc')->get();

        return response()->json([
            'data' => $vouchers->map(function ($voucher) {
                return [
                    'id' => $voucher->id,
                    'custome_id' => $voucher->custome_id,
                    'date' => Carbon::parse($voucher->date)->format('Y-m-d'),
                    'amount' => number_format($voucher->payment_amount, 2),
                    'payment_method' => $voucher->payment_method == 'cash' ? 'نقد' : 'تحويل بنكي',
                    'purpose' => $voucher->purpose,
                    'paid_to' => $voucher->payTo->name ?? '',
                    'creator_name' => $voucher->creator->name ?? '',
                    'warehouse_name' => $voucher->warehouse->name ?? '',
                    'status' => $voucher->status,
                    'status_class' => $voucher->status == 'accepted' ? 'bg-success' : ($voucher->status == 'pending' ? 'bg-warning' : 'bg-danger'),
                    'created_at' => $voucher->created_at->toISOString(), // For real-time detection
                ];
            })
        ]);
    }

    /**
     * Get POS sales data with deficit/surplus logic
     */
    public function getPOSSalesData(Request $request)
    {
        try {
            $warehouseId = $request->input('warehouse_id');
            $userId = $request->input('user_id');
            $shiftStatus = $request->input('shift_status');
            $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
            $endDate = $request->input('end_date', Carbon::now()->endOfMonth()->format('Y-m-d'));
            $creatorId = Auth::user()->creatorId();

            // تسجيل معلومات الطلب للتشخيص
            \Log::info('POS Sales Data Request', [
                'warehouse_id' => $warehouseId,
                'user_id' => $userId,
                'shift_status' => $shiftStatus,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'creator_id' => $creatorId
            ]);

            // أولاً: التحقق من وجود بيانات POS في الفترة المحددة
            $posCount = DB::table('pos')
                ->where('created_by', $creatorId)
                ->whereBetween('pos_date', [$startDate, $endDate])
                ->count();

            \Log::info('POS Count in date range', ['count' => $posCount]);

            // ثانياً: التحقق من وجود مدفوعات
            $paymentsCount = DB::table('pos')
                ->join('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
                ->where('pos.created_by', $creatorId)
                ->whereBetween('pos.pos_date', [$startDate, $endDate])
                ->count();

            \Log::info('POS Payments Count in date range', ['count' => $paymentsCount]);

            // استعلام محسن مع معالجة أفضل للحالات المختلفة
            $query = DB::table('pos')
                ->join('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
                ->join('users', 'pos.created_by', '=', 'users.id')
                ->join('warehouses', 'pos.warehouse_id', '=', 'warehouses.id')
                ->leftJoin('shifts', 'pos.shift_id', '=', 'shifts.id')
                ->leftJoin('financial_records', 'shifts.id', '=', 'financial_records.shift_id')
                ->select(
                    DB::raw('DATE(pos.pos_date) as sale_date'),
                    DB::raw('CASE
                        WHEN users.type = "company" THEN CONCAT("شركة: ", users.name)
                        ELSE users.name
                    END as user_name'),
                    'users.type as user_type',
                    'warehouses.name as warehouse_name',
                    DB::raw('COALESCE(shifts.id, 0) as shift_id'),
                    DB::raw('COALESCE(shifts.is_closed, 1) as shift_is_closed'),
                    'shifts.opened_at as shift_opened_at',
                    'shifts.closed_at as shift_closed_at',
                    'pos.created_by as user_id',
                    'pos.warehouse_id',
                    DB::raw('COUNT(DISTINCT pos.id) as invoice_count'),
                    // إجمالي مبيعات اليوم (جميع أنواع الدفع)
                    DB::raw('SUM(pos_payments.amount) as total_sales'),
                    // النقد المحصل فعلياً (current_cash + delivery_cash)
                    DB::raw('MAX(COALESCE(financial_records.current_cash, 0) + COALESCE(financial_records.delivery_cash, 0)) as actual_cash_collected'),
                    // النقد عبر الشبكة
                    DB::raw('MAX(COALESCE(financial_records.overnetwork_cash, 0)) as network_cash')
                )
                ->where('pos.created_by', $creatorId)
                ->whereBetween('pos.pos_date', [$startDate, $endDate]);

            if ($warehouseId) {
                $query->where('pos.warehouse_id', $warehouseId);
            }
            if ($userId) {
                $query->where('pos.created_by', $userId);
            }
            if ($shiftStatus) {
                if ($shiftStatus === 'open') {
                    $query->where('shifts.is_closed', false);
                } elseif ($shiftStatus === 'closed') {
                    $query->where('shifts.is_closed', true);
                }
            }

            // تسجيل الاستعلام للتشخيص
            $sqlQuery = $query->toSql();
            $bindings = $query->getBindings();
            \Log::info('POS Sales Query', ['sql' => $sqlQuery, 'bindings' => $bindings]);

            $salesData = $query->groupBy('sale_date', 'pos.created_by', 'pos.warehouse_id', 'shifts.id')
                ->orderBy('sale_date', 'desc')
                ->get();

            \Log::info('POS Sales Data Result', ['count' => $salesData->count()]);

        } catch (\Exception $e) {
            \Log::error('Error in getPOSSalesData', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'data' => [],
                'error' => 'حدث خطأ في جلب البيانات: ' . $e->getMessage()
            ], 500);
        }

        // إذا لم توجد بيانات، نحاول استعلام بديل بدون شفتات
        if ($salesData->isEmpty()) {
            \Log::info('No data found with shifts, trying alternative query without shifts');

            $alternativeQuery = DB::table('pos')
                ->join('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
                ->join('users', 'pos.created_by', '=', 'users.id')
                ->join('warehouses', 'pos.warehouse_id', '=', 'warehouses.id')
                ->select(
                    DB::raw('DATE(pos.pos_date) as sale_date'),
                    DB::raw('CASE
                        WHEN users.type = "company" THEN CONCAT("شركة: ", users.name)
                        ELSE users.name
                    END as user_name'),
                    'users.type as user_type',
                    'warehouses.name as warehouse_name',
                    DB::raw('0 as shift_id'),
                    DB::raw('1 as shift_is_closed'),
                    DB::raw('NULL as shift_opened_at'),
                    DB::raw('NULL as shift_closed_at'),
                    'pos.created_by as user_id',
                    'pos.warehouse_id',
                    DB::raw('COUNT(DISTINCT pos.id) as invoice_count'),
                    DB::raw('SUM(pos_payments.amount) as total_sales'),
                    DB::raw('0 as actual_cash_collected'),
                    DB::raw('0 as network_cash')
                )
                ->where('pos.created_by', $creatorId)
                ->whereBetween('pos.pos_date', [$startDate, $endDate]);

            if ($warehouseId) {
                $alternativeQuery->where('pos.warehouse_id', $warehouseId);
            }
            if ($userId) {
                $alternativeQuery->where('pos.created_by', $userId);
            }

            $salesData = $alternativeQuery->groupBy('sale_date', 'pos.created_by', 'pos.warehouse_id')
                ->orderBy('sale_date', 'desc')
                ->get();

            \Log::info('Alternative query result', ['count' => $salesData->count()]);
        }

        return response()->json([
            'data' => $salesData->map(function ($sale) {
                $totalSales = floatval($sale->total_sales);
                $actualCashCollected = floatval($sale->actual_cash_collected);
                $networkCash = floatval($sale->network_cash);
                $isShiftClosed = (bool) $sale->shift_is_closed;

                // حساب إجمالي النقد والشبكة المحصل
                $totalCollected = $actualCashCollected + $networkCash;

                // حساب العجز أو الفائض
                $deficitOrSurplus = $totalSales - $totalCollected;

                // تحديد حالة الشفت
                $shiftStatus = $isShiftClosed ? 'مغلق' : 'مفتوح';
                $shiftStatusClass = $isShiftClosed ? 'badge-success' : 'badge-warning';

                // ملاحظة خاصة للشفتات المفتوحة أو عدم وجود شفت
                $note = '';
                if ($sale->shift_id == 0) {
                    $note = 'لا يوجد شفت مرتبط - بيانات تقديرية';
                    $shiftStatus = 'غير محدد';
                    $shiftStatusClass = 'badge-secondary';
                } elseif (!$isShiftClosed) {
                    $note = 'الشفت مفتوح - الأرقام قابلة للتغيير';
                }

                return [
                    'sale_date' => $sale->sale_date,
                    'user_name' => $sale->user_name,
                    'warehouse_name' => $sale->warehouse_name,
                    'invoice_count' => $sale->invoice_count,
                    'total_sales' => number_format($totalSales, 2), // إجمالي المبيعات
                    'total_collected' => number_format($totalCollected, 2), // إجمالي المحصل (نقد + شبكة)
                    'deficit_surplus' => number_format($deficitOrSurplus, 2), // العجز أو الفائض
                    'deficit_surplus_raw' => $deficitOrSurplus, // للاستخدام في التلوين
                    'shift_status' => $shiftStatus, // حالة الشفت
                    'shift_status_class' => $shiftStatusClass, // CSS class للحالة
                    'is_shift_closed' => $isShiftClosed, // للاستخدام في JavaScript
                    'note' => $note, // ملاحظة إضافية
                    'shift_opened_at' => $sale->shift_opened_at,
                    'shift_closed_at' => $sale->shift_closed_at,
                ];
            }),
            'debug_info' => [
                'pos_count' => $posCount ?? 0,
                'payments_count' => $paymentsCount ?? 0,
                'result_count' => $salesData->count(),
                'filters' => [
                    'warehouse_id' => $warehouseId,
                    'user_id' => $userId,
                    'shift_status' => $shiftStatus,
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ]
            ]
        ]);
    }

    /**
     * Get chart data for POS sales
     */
    public function getChartData(Request $request)
    {
        $warehouseId = $request->input('warehouse_id');
        $userId = $request->input('user_id');
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->endOfMonth()->format('Y-m-d'));
        $creatorId = Auth::user()->creatorId();

        $query = DB::table('pos')
            ->join('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
            ->whereBetween('pos.pos_date', [$startDate, $endDate]);

        if ($warehouseId) {
            $query->where('pos.warehouse_id', $warehouseId);
        }
        if ($userId) {
            $query->where('pos.created_by', $userId);
        }

        $totalCash = $query->sum('pos_payments.cash_amount');
        $totalNetwork = $query->sum('pos_payments.network_amount');
        $splitPayments = $query->where('pos_payments.payment_type', 'split')->count();

        return response()->json([
            'labels' => ['نقد', 'بطاقة ائتمان', 'مختلط'],
            'data' => [$totalCash, $totalNetwork, $splitPayments],
            'colors' => ['#28a745', '#17a2b8', '#ffc107']
        ]);
    }

    /**
     * Get alerts for the dashboard
     */
    public function getAlerts(Request $request)
    {
        $creatorId = Auth::user()->creatorId();
        $alerts = [];

        // Check for long open shifts (more than 12 hours)
        $longOpenShifts = Shift::where('is_closed', false)
            ->where('opened_at', '<', Carbon::now()->subHours(12))
            ->with('warehouse')
            ->get();

        foreach ($longOpenShifts as $shift) {
            $alerts[] = [
                'type' => 'warning',
                'message' => 'يوجد شفت مفتوح منذ أكثر من 12 ساعة في ' . ($shift->warehouse->name ?? 'المستودع'),
                'created_at' => $shift->opened_at->diffForHumans()
            ];
        }

        // Check for cash deficit
        $deficitRecords = FinancialRecord::where('deficit', '>', 0)
            ->with('shift.warehouse')
            ->get();

        foreach ($deficitRecords as $record) {
            $alerts[] = [
                'type' => 'danger',
                'message' => 'يوجد عجز في النقد بقيمة ' . number_format($record->deficit, 2) . ' ريال في ' . ($record->shift->warehouse->name ?? 'المستودع'),
                'created_at' => $record->updated_at->diffForHumans()
            ];
        }

        // Check for high cash amounts (more than 10,000)
        $highCashRecords = FinancialRecord::where('current_cash', '>', 10000)
            ->whereHas('shift', function ($query) {
                $query->where('is_closed', false);
            })
            ->with('shift.warehouse')
            ->get();

        foreach ($highCashRecords as $record) {
            $alerts[] = [
                'type' => 'info',
                'message' => 'مبلغ نقدي مرتفع ' . number_format($record->current_cash, 2) . ' ريال في ' . ($record->shift->warehouse->name ?? 'المستودع') . ' - يُنصح بالإيداع',
                'created_at' => $record->updated_at->diffForHumans()
            ];
        }

        return response()->json(['alerts' => $alerts]);
    }
}
