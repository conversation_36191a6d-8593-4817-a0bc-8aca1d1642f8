{"__meta": {"id": "Xe732e6c3d9e01e5dbf19f55b41cef46a", "datetime": "2025-06-30 23:14:00", "utime": **********.694305, "method": "GET", "uri": "/login-with-company/exit", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.307231, "end": **********.694318, "duration": 0.3870871067047119, "duration_str": "387ms", "measures": [{"label": "Booting", "start": **********.307231, "relative_start": 0, "end": **********.650224, "relative_end": **********.650224, "duration": 0.34299302101135254, "duration_str": "343ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.650233, "relative_start": 0.3430020809173584, "end": **********.694319, "relative_end": 9.5367431640625e-07, "duration": 0.04408597946166992, "duration_str": "44.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43760000, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login-with-company/exit", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@ExitCompany", "namespace": null, "prefix": "", "where": [], "as": "exit.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FUserController.php&line=669\" onclick=\"\">app/Http/Controllers/UserController.php:669-673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00198, "accumulated_duration_str": "1.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.679981, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 81.818}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Services/ImpersonateManager.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\lab404\\laravel-impersonate\\src\\Services\\ImpersonateManager.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Services/ImpersonateManager.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\lab404\\laravel-impersonate\\src\\Services\\ImpersonateManager.php", "line": 137}, {"index": 19, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Models/Impersonate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\lab404\\laravel-impersonate\\src\\Models\\Impersonate.php", "line": 64}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\UserController.php", "line": 671}], "start": **********.684947, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 81.818, "width_percent": 18.182}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login-with-company/exit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login-with-company/exit", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2065456446 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2065456446\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325232686%7C31%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InQ3WE9SSVdXdzYxNWxUYktKQVhKS0E9PSIsInZhbHVlIjoiQWdLOGJaWGozSWFPM25JcWdGUmhhM1VJVGJKbzlqSWRyK05TNjEyc21qMFJLNk55cnJQNUcwcGlPOXJKbitLd1dreWZVbFVCNXVKOWZnOFVhVGFjalgwRU5uSXJub2ZqblYvR3BKRWpLRjlzVG9VN3NTQUZEL3FUSnZ3SiswdVVtSzE2RXFPMkhlS3JKVnZ2MkNSTjNhNVVFUm56Y2srQVo4SFI3Y21GTGZ3NWRzc3BJYUpQdW5YUXI1amF3S0h2R2JneW5MODBra25ucUxxc2VYR3FsSlcya0JBTUVlV2N6TmxWTG1SSFlxdnU3KzdCOXVmM3o1VUFDeG9kdHE5WWtNdW8xVmFFUnFtcXMvbWsySTJNeTI5MkVteG4vZWpnNGk2K2ZXckZ5ZnlZRFN5WDdSWnVCT1hHR29kbHc1YlFuRUdpSkpCTXFLQkRGdWZKS0dicW5kS2hoZzlLREJBSzdoaDluT1p0TUpSM05sb0xPM2phZXJ6MWJSZ3R6aXZQVVlpdTVYWDVqcnd1SUlwNEJjWXFMd2lUbVhEc0s4cmw3TW9ISlg0U2NJQi9INmluSGQ0dndhSERFNmxaSEZERU1xcU1MeGltNG9DdFlmQkVaSlVBa3JPOUtyWXd3Mkt5K3hNNE5ISnJFOXJVeDFSWm1ZUUZ3YVlxSit0ZHlIRXoiLCJtYWMiOiI1NDBkODM1ODllNmI3ZjJkODY4Yjg2NDUxMmQ1NGQxZjY1NGZkMGRlODQ2MjI1NjhmMDQxN2UwZjlmNzc2MGY5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ijk1ek5xcDdqK2pzSUpzQ1YwUmhJNHc9PSIsInZhbHVlIjoiN0k1ZGpDQnJ6SjM3V3NsUU80b2dPOFlEVjI3NEVsUjhoREllTDZUTjNBVTJXZ21rMkF3SHdGbHBlbWE1clQyN201aGcxVE5STldFdlpiZmcyNFFEOWhLTDlGc3JCNGQveFBRUnN6S05iYlNnYTVBcDJwVkF6ZFZiRGZpOS9hVlJXb2JneFRxTWhOTzhpUng5cGhqMUxUTXJ2Ujl2eG5rYWlzelFxdFU4QUcxL2t6TytYL2lwQ09Kb2JnY0ppd1k2dTJYNGpRbnZHY3owdk9sUWhJYTBOdUMreHVBVWRLMzdDTWVWRWo1d3FNT2Z6UUt0UEVrTXBadHcweEQ2MUkraUthZU51ZG1NWkhkWDhwcWIwVXlIeVkvUDFoMGhReEkybTZ5Qzl6RmN1S1RqNEhBb3hVYXp6VGdTMUNaMFkrN3ZiNTVuT3RMNHRKUXdVcXhKbEtOYlExbFpGMHo2aXhSTG1RQTQwWE55aHVtalhOTzI1TWRIMlVnOU00WTBRbDEweWFBTEFkRUd2dy9kWUZsem9TSWtXWTZZN2FlelZSTHJKMU9UR2JTTmw1S0FJU0hwQTl1OFZ1M2dBOHlyeWJkT2hiSVFBa1QwcytJazJ6SitMdW4vYTdJRnlGajlOei9aL1duUTl4MnMwZzZ0NFZKZTdtaE00b2ZqK1VVS1ZJTmwiLCJtYWMiOiI2ZTE3YWYyN2U4MjNhYmZiNjIwMGZkNjJhZDIzODc0NWQyM2JhZDNiZTBkNzUxN2M1MzNiZWYyOWM0MWVlN2ZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1441228509 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">H4YK57QUXyJ63T0CnkmwYWGCllGQ9A8V3d5cZCUG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1441228509\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1653266975 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:14:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlY4aURQN0c4K1ViVFhSSG1TbHZXZ3c9PSIsInZhbHVlIjoicEJYZVRCWEFrS0pPamlXSEMyOHlBVHFsbU5IWDJ5V29hRlZwWUxWcHVjVVZWNlVteHB0Vm96U0NSZitGNWE4emw3cTl4dHhNZi9RTDkxcENVdm04bEVxS0NRR2tzQnZCdTdyY0xqaERCcmg5NFZZTDkrcHV5Tk0zOTV2Y0hXZ1pPYmxhREt4eHVBelM2NzdlQ1BrRUVBVnpseEwyOGVweVQ0UlZKaTFBLzdiY1ZZdlVRZHEyeENxVEFVUTVXMFNYT1lyeGM1djJHMEprTDdwOUNsZHNTTFY3d2g2c3prS3I2dSswMVlUVERiWVgzakRqc2UzaDNneGFzM1pPVktCcU9QRWF0NStGVVVoamI0VTl1d0NETlhPMmpzK1FEdlAzT2dGU25Zc2FpUzViQ0tmMVRrY0x0Vm8zNjVMWXpBRGFNRS9TcUJmSG85bEY4Z3gvajRSeWtHbkRsNzJDZGFwcWdyRjhaSVBGaDE4YS9aYnN0OVNPQ2xJN0V2clJ1b3JRT1JoRi9FM0pOZzdmaUppWVlPZXNGUXYvaHVSeU9lTVhXRitzMTVoamZKZGt0T2tiMzBWRGpRN2ZyZEFUbm81aUZGZkx6WjJaYXh1MEphUTYxNGJVR090dHMyTlBLL0E4M2ZqbnBMVGZpbG1FanZ0NFM1djRRcFFCSmNpbU9LZkkiLCJtYWMiOiIxZDRkMjBlNjU4MjJlYmMzMDk5YzY4ZTFlOTE1NTAyYTRmNjIwYWMzY2MwYWRhOGUwZGU3NDJmMTljZWVmNDI3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJFQXppYXRVa01HQzd6cU1BcTRRMnc9PSIsInZhbHVlIjoieDlzWXNMUHorNVlJb2RwdGVIOXFKYzdTcmFueDJMTkJSbzJ4ckpxdkdOS1lHS0kyTXJEMjlnK0c2aVpDTW1LZUUxcTk3TDVzSUZvZkswcTl5ekFibXNFbkV5Z3VEbjB5ZGNRMEZzYU56blNCc3pYUElFRFNMdlhVUzV6eXl2eTJ3WFpmcmwxcXAycTlhRVliMWxpOGZQeXhZKy96UEFobkNGRzE4YlNpQy9MK2huOVJ4bnE4RllNalhYNFB5WXl3aWwrQVFjZVZzaEJTWFVNQ1lqejBTVnExaDZ5dG1yaWhJeHh2cGJSSUttMUF2dDMrV09NZUVXMlN5MkhZL1locWl2eklKVGpLbFlYbzA4Y25JRlBubmFUWmNhYktpcjdZY0VKVGNrbVYzTEtwTldmcmhIV1YwSzlhVS9td1FmOEN0Nkl0dktmc21yN2NnNGhnLzUzM1c1Mm5JODRaOUNRQTBOL052UHdpNGVpbXpoYzRwblJ4d2xvRUhpN1c4SmVuM3hFL0RobXM3b0xtc0NNVU1LWXdQaEMvb0QyclIrWUdDK3puQVQyK1NKdmI3VGExUjY5cnF5amFpSnZuMVpPVmNTMzBSYWZ2NVJHeGxRbUhjUkVjanowaWgzUnRvWnorYkZnZXVhTkk5UnA2NUt6L1pBaWRyamVlbGlQU0lmTW4iLCJtYWMiOiI2NTljZjBiY2U0YjhkYzYyMjM2NjAwMjkxNzgyMmNmYmM2NWRmZjYyYjYwMDNmNjkzZjU1NmY1NzNiMzk2MzdmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlY4aURQN0c4K1ViVFhSSG1TbHZXZ3c9PSIsInZhbHVlIjoicEJYZVRCWEFrS0pPamlXSEMyOHlBVHFsbU5IWDJ5V29hRlZwWUxWcHVjVVZWNlVteHB0Vm96U0NSZitGNWE4emw3cTl4dHhNZi9RTDkxcENVdm04bEVxS0NRR2tzQnZCdTdyY0xqaERCcmg5NFZZTDkrcHV5Tk0zOTV2Y0hXZ1pPYmxhREt4eHVBelM2NzdlQ1BrRUVBVnpseEwyOGVweVQ0UlZKaTFBLzdiY1ZZdlVRZHEyeENxVEFVUTVXMFNYT1lyeGM1djJHMEprTDdwOUNsZHNTTFY3d2g2c3prS3I2dSswMVlUVERiWVgzakRqc2UzaDNneGFzM1pPVktCcU9QRWF0NStGVVVoamI0VTl1d0NETlhPMmpzK1FEdlAzT2dGU25Zc2FpUzViQ0tmMVRrY0x0Vm8zNjVMWXpBRGFNRS9TcUJmSG85bEY4Z3gvajRSeWtHbkRsNzJDZGFwcWdyRjhaSVBGaDE4YS9aYnN0OVNPQ2xJN0V2clJ1b3JRT1JoRi9FM0pOZzdmaUppWVlPZXNGUXYvaHVSeU9lTVhXRitzMTVoamZKZGt0T2tiMzBWRGpRN2ZyZEFUbm81aUZGZkx6WjJaYXh1MEphUTYxNGJVR090dHMyTlBLL0E4M2ZqbnBMVGZpbG1FanZ0NFM1djRRcFFCSmNpbU9LZkkiLCJtYWMiOiIxZDRkMjBlNjU4MjJlYmMzMDk5YzY4ZTFlOTE1NTAyYTRmNjIwYWMzY2MwYWRhOGUwZGU3NDJmMTljZWVmNDI3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJFQXppYXRVa01HQzd6cU1BcTRRMnc9PSIsInZhbHVlIjoieDlzWXNMUHorNVlJb2RwdGVIOXFKYzdTcmFueDJMTkJSbzJ4ckpxdkdOS1lHS0kyTXJEMjlnK0c2aVpDTW1LZUUxcTk3TDVzSUZvZkswcTl5ekFibXNFbkV5Z3VEbjB5ZGNRMEZzYU56blNCc3pYUElFRFNMdlhVUzV6eXl2eTJ3WFpmcmwxcXAycTlhRVliMWxpOGZQeXhZKy96UEFobkNGRzE4YlNpQy9MK2huOVJ4bnE4RllNalhYNFB5WXl3aWwrQVFjZVZzaEJTWFVNQ1lqejBTVnExaDZ5dG1yaWhJeHh2cGJSSUttMUF2dDMrV09NZUVXMlN5MkhZL1locWl2eklKVGpLbFlYbzA4Y25JRlBubmFUWmNhYktpcjdZY0VKVGNrbVYzTEtwTldmcmhIV1YwSzlhVS9td1FmOEN0Nkl0dktmc21yN2NnNGhnLzUzM1c1Mm5JODRaOUNRQTBOL052UHdpNGVpbXpoYzRwblJ4d2xvRUhpN1c4SmVuM3hFL0RobXM3b0xtc0NNVU1LWXdQaEMvb0QyclIrWUdDK3puQVQyK1NKdmI3VGExUjY5cnF5amFpSnZuMVpPVmNTMzBSYWZ2NVJHeGxRbUhjUkVjanowaWgzUnRvWnorYkZnZXVhTkk5UnA2NUt6L1pBaWRyamVlbGlQU0lmTW4iLCJtYWMiOiI2NTljZjBiY2U0YjhkYzYyMjM2NjAwMjkxNzgyMmNmYmM2NWRmZjYyYjYwMDNmNjkzZjU1NmY1NzNiMzk2MzdmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1653266975\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1053577600 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost/login-with-company/exit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1053577600\", {\"maxDepth\":0})</script>\n"}}