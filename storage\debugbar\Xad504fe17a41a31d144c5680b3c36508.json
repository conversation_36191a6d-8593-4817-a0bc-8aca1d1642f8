{"__meta": {"id": "Xad504fe17a41a31d144c5680b3c36508", "datetime": "2025-06-30 22:39:30", "utime": **********.128491, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.598871, "end": **********.128507, "duration": 0.****************, "duration_str": "530ms", "measures": [{"label": "Booting", "start": **********.598871, "relative_start": 0, "end": **********.058427, "relative_end": **********.058427, "duration": 0.*****************, "duration_str": "460ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.058438, "relative_start": 0.****************, "end": **********.128509, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "70.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00403, "accumulated_duration_str": "4.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0960178, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.787}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.111249, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.787, "width_percent": 18.61}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1192548, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 80.397, "width_percent": 19.603}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751323019449%7C9%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpuSVc4dVI4aTZEeTA5RERaUGlSRXc9PSIsInZhbHVlIjoiUE42RjBnTHIrVTJYWURYTWF0a0RPbVRqUXFJWStrSDliNmVHZjNUQUYxeEtpOGw4b2liLzhacGtJaDc3ZFlhbXRuMHRuenJISFB0S2crN3lYM2g2UjdZYVZYak9UenloaFY2VG1LelBVOFFBQ2NQcVlvNGtpV2FCODlrc2RYc1B1a2VlQXZVdnZFdGQvcUpxQlAyVHltVE9QcDI2M2VObXZ0ODJIaWIwV0hJNWtoVEZrQURoSUwwR29WU25LbWtxbUp3MWhLQzlBSk9ETmFPb0p3VzFJNDA0TWhvR1l1YWxuNzV1NVcyR2U3QzVITTRXQ2NIeUtHdnhhVGY2UDU2aWlMZEgxamFUc0N6M1BJbm1IRE5sTThKZHVncHNMZUZ1bXBzbldTdUVkMHFmODZ1YWxqSTlkTHFOZmZ5cjh3b0JFaW9WY1R6UDNMYVVCYmxnbkVicklKK3BMMlZtM3FhSkVXNXNqWUVLZis2OThpYmhUSS9aLzF3S091SmZid1BzRFZXdFJ6TDdLZndMdVorZjhjVzEySHJnUTlrQi9ISm1Ob2h4aWRuckZpR0krKzJ6YTAyNkllRzF0blJ6ZnI1ajc5YUhkbVRwM3VYdzkrT3V5OWRoQ3doUWxQLzNsZmhJa2hIZVVDbFZDbndKci93eDZ0SUo3MjJrZWRQcDVSYkEiLCJtYWMiOiI0NWU5Y2IzYzdlM2Q4NTFjM2Q2ZmNjZmRmZmMwN2Q0ZDMzZTQ0OGE1ODUyMWMyZTk4MzBhYWYzMGY1ZmEzMGJmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkVnMXZnKzlvUnkzSUJmcVVCMk53Q1E9PSIsInZhbHVlIjoidWxreC9IYkNTbnd6bFNrY0pnQ1NWT2NxMXZYTjl2MEt1MEh3cTMybFpRVUxZRHlwTGVtUkt5dmlpUzMzakNvcGQ3Zll1dXh1YWY3RkM0ZXlOc0Fub0FLOTlDYks4bkpkdTBJUVk2VlBFcWlHc3FsSXM5VmpzN1ZtTWt1OUVMUGcrTmhWK3pRZnlFbmZWSHZOTmVqNG05VEFnMW50MnNIaWVFOTQzT2kydmo1dFRSSU5pNnp4cmxHVGlVeEZhMHUwK2hKeWlKSmxrV1l1d3BJbWlJOG1sSEVUNVNqMGV1SEFyQS92TWljb3pkZlp5Ris5b3YyOXdFb1MySHA4aWE3UXR3elJzV1hNc1dHeGFNVTNjaVhEazArTkxJT0oxTzZ2MmZzZjI3SndzQmFmTUJUUXdKNXNjamVxRWw4SUNrSnBEMmY3eGdSOC80dDVicjRZMG0xTCtrdEVHd2xBUnA1eTRET0NIRzdLeTcrNU83NmZ6VFRSTkR1ZFE2U3IyOXk5UjBvV2VXcndna29nUnlsNkROL3VYRWVWQkIxNnhTQ3NzS2xtcklHclZUajVKVjdXYTJtbmlSTC92SThqZmJkMmZraDYwRUN3UUpRRE4xSWdLVTVEVXlJcWF4RWgwMW1xeHR3MlJhSDh4ZGFHVGljNjRzdVlLME1FY3lwQXp1SysiLCJtYWMiOiI5YWU3YWQzODE1OGU4MTQ2ZWYwZjMyZjE5MTgwYTc5ZDViZTNmOTViYzJlNzQxZjBiMjIwYjY5MzU0YjdiYzgxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-660714950 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">K7EIYCLnalanTBUASCKMEOK1yUmooVr7ha2cCSMP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-660714950\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-183315665 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNiYmtuRWRNTGdaeWdKU0VyNnppMVE9PSIsInZhbHVlIjoiZDFvd3lxanc3cnZHMWJMS2dSeHUwZFdZYVZzN2MrTkhzVit6TmFPNndaYnN0NkNYMmxtOW1QSytWODR2RllDdi9qb2xZS0xIck0zR1M1Y0ZlMUFWMisyUGFIR0hMWThZNzUxLytmQ2RHMkFzRXh1Tkw1bTJ3UUhxVG1keXRRRFc3OXdjcHF0MHUzbFFVdnlEaXVEakdHVGR5dGNLS1JPd1pzUlBGU094aVpFM1h2SkF4cnYxaGJaY1U0eXFCcnp3RGUySG54emFWTkpFR2M1V0x5alAvT1JiYkhEUXMzcXQ4MDl1TVlDdFlZS1RjMTh1UVE3b25sL1RaeDVaWmhNNDEyc09hOFI0d2VHSG9GRTF4YVlicERhZ3RrWHB0Q3VOQkV4V1B3YTVKVXdYQ0FqL3lZb1pBbXJkcnFMOTB1NU83eHNlQTBBOGt1NjhkU25rcUFMeG1xTWVGaFJyL3RIbmtMT0YvNnRFU05pUEREaDVnaG5sWVRRRGx4SVVTOE5McmxUR1JJNUpoV1JVYjY3S1BhM1RZNVdoeGR4eCtEWHVQRmY3VmFCaW5tUzM3a1RySEZiQ1FWUXVRR2hZRkliZ2VXWGRtUlA3LzhkRWlaZ0ptM3ZubGJXRUh2MDFQQ0JGMkpseTlVREpiT0M2T25PZlNONEhZOHVIcXRUTmVGSW0iLCJtYWMiOiJhODA0YzRhNWFlYjBmYThhOWI1NDVlN2QxNTBhNjA0ZWZiNDY3NDdhNGRkN2M0YjJmMWZhMDM3OTg3MzRjZmY0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZkMGRtNUhpbHc1bUVCYWFZc1Y5Znc9PSIsInZhbHVlIjoiaVZXRDVYOTJQSmM4RUpvTzhYL2JGa3E1RWkxNVphYjh4Wi9Zck5KV1phYW9aeGJJSGZUWmRIdXJkMjNhazBURGlReitqTUtYNk5ieE5nTFE1elJsbW1UMkVYZWlMa1V3a3JoMlNtT25BQkNnSU5HekhZSzBiUGM5QVQwbTk1OWdXeGtiYVNNZHdjUTVCUUlUZmVodHpLRzNsN0ZpZTFsaGhBaEdnVU5FYkdhR3BwdzZtamI3VUNlb2s4YnpycE50OVJqQzRsRWpFaW1rVVk4RmJ3TzBiN0dLUGlDY1JvTTJyWFhJbjE4Zjg3Tk4vck04M3RaVThqUG9QLzJlckljay9OTXYzZFo0aXJ2b0hPV045Y1NWZnZMU2x1VGZDeE9rK0NGRElmWWhnTTlYL0VBb1Jwc1h2V3NDOExsUVBaeHJVOE9velVCNnROWGVlQ05pS0JTMVU0SElaRVhBaU1aa0hNakZjdk1xYm9QRk1oNDd2S3hQTjlvWU9SY1pqRGJjT0xXOVJlWjV0ZHFyRGU3VUlJakhTN1k0MFRKWlMvbmNhSk53eVB4OENqSEhVelhZK0pwdmxiNjdwSDN3UXQvaVBFUzR0cVlXWlJHYWtjSk1LQUJicjlra3VHUHZTb3FYZXRtdjVtS3RRcjlETG5EUVlEeENPL21uelRIN1RoVXgiLCJtYWMiOiJkYmUzZTVkZDNhNGMzOTg1YjM4MWZmMTVjNTFjN2E0NzEzYTI0ZDE2NmRmM2M2ODJkMDhhZmM1ZjljYjgwZjliIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNiYmtuRWRNTGdaeWdKU0VyNnppMVE9PSIsInZhbHVlIjoiZDFvd3lxanc3cnZHMWJMS2dSeHUwZFdZYVZzN2MrTkhzVit6TmFPNndaYnN0NkNYMmxtOW1QSytWODR2RllDdi9qb2xZS0xIck0zR1M1Y0ZlMUFWMisyUGFIR0hMWThZNzUxLytmQ2RHMkFzRXh1Tkw1bTJ3UUhxVG1keXRRRFc3OXdjcHF0MHUzbFFVdnlEaXVEakdHVGR5dGNLS1JPd1pzUlBGU094aVpFM1h2SkF4cnYxaGJaY1U0eXFCcnp3RGUySG54emFWTkpFR2M1V0x5alAvT1JiYkhEUXMzcXQ4MDl1TVlDdFlZS1RjMTh1UVE3b25sL1RaeDVaWmhNNDEyc09hOFI0d2VHSG9GRTF4YVlicERhZ3RrWHB0Q3VOQkV4V1B3YTVKVXdYQ0FqL3lZb1pBbXJkcnFMOTB1NU83eHNlQTBBOGt1NjhkU25rcUFMeG1xTWVGaFJyL3RIbmtMT0YvNnRFU05pUEREaDVnaG5sWVRRRGx4SVVTOE5McmxUR1JJNUpoV1JVYjY3S1BhM1RZNVdoeGR4eCtEWHVQRmY3VmFCaW5tUzM3a1RySEZiQ1FWUXVRR2hZRkliZ2VXWGRtUlA3LzhkRWlaZ0ptM3ZubGJXRUh2MDFQQ0JGMkpseTlVREpiT0M2T25PZlNONEhZOHVIcXRUTmVGSW0iLCJtYWMiOiJhODA0YzRhNWFlYjBmYThhOWI1NDVlN2QxNTBhNjA0ZWZiNDY3NDdhNGRkN2M0YjJmMWZhMDM3OTg3MzRjZmY0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZkMGRtNUhpbHc1bUVCYWFZc1Y5Znc9PSIsInZhbHVlIjoiaVZXRDVYOTJQSmM4RUpvTzhYL2JGa3E1RWkxNVphYjh4Wi9Zck5KV1phYW9aeGJJSGZUWmRIdXJkMjNhazBURGlReitqTUtYNk5ieE5nTFE1elJsbW1UMkVYZWlMa1V3a3JoMlNtT25BQkNnSU5HekhZSzBiUGM5QVQwbTk1OWdXeGtiYVNNZHdjUTVCUUlUZmVodHpLRzNsN0ZpZTFsaGhBaEdnVU5FYkdhR3BwdzZtamI3VUNlb2s4YnpycE50OVJqQzRsRWpFaW1rVVk4RmJ3TzBiN0dLUGlDY1JvTTJyWFhJbjE4Zjg3Tk4vck04M3RaVThqUG9QLzJlckljay9OTXYzZFo0aXJ2b0hPV045Y1NWZnZMU2x1VGZDeE9rK0NGRElmWWhnTTlYL0VBb1Jwc1h2V3NDOExsUVBaeHJVOE9velVCNnROWGVlQ05pS0JTMVU0SElaRVhBaU1aa0hNakZjdk1xYm9QRk1oNDd2S3hQTjlvWU9SY1pqRGJjT0xXOVJlWjV0ZHFyRGU3VUlJakhTN1k0MFRKWlMvbmNhSk53eVB4OENqSEhVelhZK0pwdmxiNjdwSDN3UXQvaVBFUzR0cVlXWlJHYWtjSk1LQUJicjlra3VHUHZTb3FYZXRtdjVtS3RRcjlETG5EUVlEeENPL21uelRIN1RoVXgiLCJtYWMiOiJkYmUzZTVkZDNhNGMzOTg1YjM4MWZmMTVjNTFjN2E0NzEzYTI0ZDE2NmRmM2M2ODJkMDhhZmM1ZjljYjgwZjliIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-183315665\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-233509629 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-233509629\", {\"maxDepth\":0})</script>\n"}}