<?php

namespace Database\Factories;

use App\Models\Pos;
use App\Models\User;
use App\Models\Shift;
use Illuminate\Database\Eloquent\Factories\Factory;

class PosFactory extends Factory
{
    protected $model = Pos::class;

    public function definition()
    {
        return [
            'pos_id' => $this->faker->unique()->numberBetween(1000, 9999),
            'customer_id' => $this->faker->numberBetween(1, 100),
            'warehouse_id' => $this->faker->numberBetween(1, 10),
            'pos_date' => $this->faker->date(),
            'category_id' => $this->faker->numberBetween(1, 20),
            'status' => $this->faker->numberBetween(0, 2),
            'status_type' => $this->faker->randomElement(['normal', 'returned', 'cancelled']),
            'shipping_display' => 1,
            'created_by' => User::factory(),
            'shift_id' => Shift::factory(),
        ];
    }

    public function normal()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 1,
                'status_type' => 'normal',
            ];
        });
    }

    public function returned()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 2,
                'status_type' => 'returned',
            ];
        });
    }

    public function cancelled()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 0,
                'status_type' => 'cancelled',
            ];
        });
    }
}
