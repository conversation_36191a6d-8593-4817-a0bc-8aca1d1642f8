<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Pos;
use App\Models\PosPayment;
use App\Models\FinancialRecord;
use App\Models\Shift;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class DeferredPaymentTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $shift;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->shift = Shift::factory()->create([
            'created_by' => $this->user->id,
            'is_closed' => false
        ]);
    }

    /** @test */
    public function it_can_create_deferred_payment()
    {
        $this->actingAs($this->user);

        $paymentData = [
            'payment_type' => 'deferred',
            'total_price' => 100.00,
            'deferred_customer_name' => '<PERSON>',
            'deferred_due_date' => now()->addDays(30)->format('Y-m-d'),
            'deferred_notes' => 'Test deferred payment'
        ];

        $pos = Pos::factory()->create([
            'created_by' => $this->user->id,
            'shift_id' => $this->shift->id
        ]);

        $posPayment = PosPayment::create([
            'pos_id' => $pos->id,
            'amount' => $paymentData['total_price'],
            'payment_type' => $paymentData['payment_type'],
            'cash_amount' => 0,
            'network_amount' => 0,
            'transaction_number' => json_encode([
                'customer_name' => $paymentData['deferred_customer_name'],
                'due_date' => $paymentData['deferred_due_date'],
                'notes' => $paymentData['deferred_notes']
            ]),
            'created_by' => $this->user->id
        ]);

        $this->assertDatabaseHas('pos_payments', [
            'id' => $posPayment->id,
            'payment_type' => 'deferred',
            'cash_amount' => 0,
            'network_amount' => 0
        ]);

        // Test deferred details accessor
        $deferredDetails = $posPayment->deferred_details;
        $this->assertEquals('John Doe', $deferredDetails['customer_name']);
        $this->assertEquals($paymentData['deferred_due_date'], $deferredDetails['due_date']);
        $this->assertEquals('Test deferred payment', $deferredDetails['notes']);

        // Test isDeferredPayment method
        $this->assertTrue($posPayment->isDeferredPayment());

        // Test deferred amount attribute
        $this->assertEquals(100.00, $posPayment->deferred_amount);
    }

    /** @test */
    public function it_excludes_deferred_payments_from_cash_calculations()
    {
        $financialRecord = FinancialRecord::factory()->create([
            'opening_balance' => 1000.00,
            'current_cash' => 500.00,
            'overnetwork_cash' => 200.00,
            'delivery_cash' => 300.00, // This now represents deferred amount
            'shift_id' => $this->shift->id
        ]);

        // Test new calculation method (should exclude delivery_cash/deferred amount)
        $totalCash = $financialRecord->calculateTotalCash();
        $this->assertEquals(1500.00, $totalCash); // 1000 + 500 (excluding delivery_cash)

        // Test deferred amount accessor
        $this->assertEquals(300.00, $financialRecord->deferred_amount);
    }

    /** @test */
    public function it_validates_deferred_payment_fields()
    {
        // This would be tested in the actual controller/form request validation
        $requiredFields = [
            'deferred_customer_name',
            'deferred_due_date'
        ];

        $optionalFields = [
            'deferred_notes'
        ];

        // Test that required fields are validated
        foreach ($requiredFields as $field) {
            $this->assertNotEmpty($field, "Field {$field} should be required for deferred payments");
        }

        // Test that optional fields exist
        foreach ($optionalFields as $field) {
            $this->assertNotEmpty($field, "Field {$field} should be available for deferred payments");
        }
    }

    /** @test */
    public function it_handles_different_payment_types_correctly()
    {
        $this->actingAs($this->user);

        $pos = Pos::factory()->create([
            'created_by' => $this->user->id,
            'shift_id' => $this->shift->id
        ]);

        // Test cash payment
        $cashPayment = PosPayment::create([
            'pos_id' => $pos->id,
            'amount' => 100.00,
            'payment_type' => 'cash',
            'cash_amount' => 100.00,
            'network_amount' => 0,
            'created_by' => $this->user->id
        ]);

        $this->assertFalse($cashPayment->isDeferredPayment());
        $this->assertEquals(0, $cashPayment->deferred_amount);

        // Test network payment
        $networkPayment = PosPayment::create([
            'pos_id' => $pos->id,
            'amount' => 100.00,
            'payment_type' => 'network',
            'cash_amount' => 0,
            'network_amount' => 100.00,
            'transaction_number' => 'TXN123456',
            'created_by' => $this->user->id
        ]);

        $this->assertFalse($networkPayment->isDeferredPayment());
        $this->assertEquals(0, $networkPayment->deferred_amount);

        // Test deferred payment
        $deferredPayment = PosPayment::create([
            'pos_id' => $pos->id,
            'amount' => 100.00,
            'payment_type' => 'deferred',
            'cash_amount' => 0,
            'network_amount' => 0,
            'transaction_number' => json_encode([
                'customer_name' => 'Test Customer',
                'due_date' => now()->addDays(30)->format('Y-m-d'),
                'notes' => 'Test notes'
            ]),
            'created_by' => $this->user->id
        ]);

        $this->assertTrue($deferredPayment->isDeferredPayment());
        $this->assertEquals(100.00, $deferredPayment->deferred_amount);
    }
}
