{"__meta": {"id": "X18b28532857014d7f63f54e658fe8bce", "datetime": "2025-06-30 23:14:04", "utime": **********.101707, "method": "POST", "uri": "/logout", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751325243.667224, "end": **********.101723, "duration": 0.43449902534484863, "duration_str": "434ms", "measures": [{"label": "Booting", "start": 1751325243.667224, "relative_start": 0, "end": **********.061335, "relative_end": **********.061335, "duration": 0.39411115646362305, "duration_str": "394ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.061343, "relative_start": 0.3941190242767334, "end": **********.101725, "relative_end": 2.1457672119140625e-06, "duration": 0.04038214683532715, "duration_str": "40.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43606208, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST logout", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@destroy", "namespace": null, "prefix": "", "where": [], "as": "logout", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=243\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:243-252</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00159, "accumulated_duration_str": "1.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.086379, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "6DOf3LRIRxH3sOgteNJGho7yggpQCjM4JOiRU4Kx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/logout", "status_code": "<pre class=sf-dump id=sf-dump-223338576 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-223338576\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-238050171 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-238050171\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-570347490 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-570347490\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2020776576 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325241921%7C32%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imk3K0w2ODlhcEV6UkRDUlZVRFBZL3c9PSIsInZhbHVlIjoiMzFjSTB5emhWTmFKYzhNL05UVk5MNHVhRVN1TjdoOVI3blJpRi9hbzZEcXpNUDN4aXZQM3crOE51c0xrZDNsWmZxZ0NXQzJ4UWkvZHJ4S24rZEhKRmtCYTVFWlM4WXR4NVpFaCtRMTNmTWgvSE9meE84Y1hVUk9kYzhzSVNEbnUxbFNiOGdOdjBuQWpmZVNDZ1lsWC9RRk9hQ1ZCRG4xRkxrMTgxSHo2a2dteFNmYzFyVXIzTHJTbFNPYWdnR0JaZGhtaG5OTXg2d1l1Vkh6cjB5ajhmSjRReEN4eG9WSVdKQzVzTGNSN1l4UCtnazhJb0dtSzRvbW9ycWFuLzBpSmdKaXdzM3RnZlIwOGF5T1JPT0pyRWYza2dnbEJ2ZStOY2pRdFkxbHRUNHpzT3R3ZEl4WVFaai9aSm8yb21YazlXQkxCZFhMQUdhUk4xdE9oYnkxcmlMMDdUN0V3MUJZZ1hnRzdXUHZIVGJsWDFKdkUyaUN2OHRKTzZTWTMrNG1iNG40c2NOU0c5V1A0bmlRdERiQ2h6Z3habjdsTy9zYnVJUWw3WHQ1ZkNSU3VaZit1S3lGTlpvRmFLOXk1R1Nxd0VtcU5YTjFyeDJHcUs4YjZwMTFBc0d5QnY0SGFVNHM2dmdMcWRmM2o4ZzNPUGw3L1VkRW00ZWdGK3dqamxBaDkiLCJtYWMiOiJhNzUwYjJmNDc4Nzg1NjQzZmUxNWQ3NTcwZWQ5N2Q0OTBmYzE3NmI0OTIyYWY1NmI5OWQwYmRlN2FkNDMwZTFkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InRlZ2l4OHllc0U4aGp3dnpjU3RaV0E9PSIsInZhbHVlIjoiSmJYWWtnYXFZTWkvTGppK29PcVNDRzMvNENWaFFReVAzdVNhVUtJOGpYQjVmQkMvTi9aS0NaTVE4SE80UmsyQmxHYjREYnQyRlpUOW0xOGd2c24yTXJhNFVXNG9LM0pRR0I4SXNpUUdBUjQvd2taNnFrZlVNaFR2NjNrMGFDMEZjR0Q2b1ZBazBHYjdaNWliaWNNYk9JRno0Nk9tRUdRZmhCRW13V3JtaDBYK21lc3hjS3I5TjJWWnZna2ZLUDdGUmM2R0ZzcVlOTEZVQmNOdktkdmRFVEVLYktKaEJDeW84Q0FOUTh0SzY1dXB5WnVMVm5NdW9sY2tKUGZkQ2M4MndxUkZvNmd1R2xkdkFaZ3dUMWNoVmV3TEpQTFJDYm1wVzRTNmRsN3FnU2I3OGRJdHV3WFVLSnlGSmF1NGpacUdwY2lvVXlQUDlaSGwrYVVkbC85bHR4L1JmTzhxcFZhcXBJdTdtaXFJUzluKzRwWXVSRWtweFVyUUxFdGlPZ2NpSTVacWRqN3ZGck8vWW1FT0JybVJDbjJldnYyOXArMUU3M1pnMTN1MTNlZHQwUm1OcHZTME4yaXRoQjhlOC9McUZEOG1IV01OYmR2NnpvOGR4KzY1c3BWTUoxMFVCYTN6aHpkN3ZPOUQzSStvcERtT3FHV0MyNGN2YytQM1EvNDAiLCJtYWMiOiJlY2EwYTk3NGJiYzJkMTQ4NmYyMzhmNDdkZThhMjI5ZDI3NWUyMjE3ZmE5MTUyNjZlNmFlYTM5NjdmMWMwYzE3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2020776576\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1060740282 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LzQkrwbVBtmiKyPqbktIsF6wiC8bftN1cyLybXUc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1060740282\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:14:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpmYWtiWUYvS29pb2Q3TDRCZHFTM3c9PSIsInZhbHVlIjoicmhYdzBPb1ZoajF0UDRCWEZaa0JQN2dQRHZWOEdaa3ZJZTNwdEZIc0pDSDJJVDBmN0J4ZmRiek5nNkJpOW0vaGN1N2llbjNUc21wd2tKNlBJY2lCNEU2by9id212ckE5VXlsVDBtQ1FYOG93dVVsYmpkaFVjTlJSZDZFYVMrcmFTcUFRWVNjYnZIb0lSVFBUdUlzUm1rNHNJRzlTZ0ZVVVFYcG1pdm1NOEhjVjFqL0ZoZ1k3VjN4Ym5NNUZYblVyQnBMMU9zTVRsVVc3UU9hSWZYOXJxb0FjRHlubWVHTFZIQTJQSktseHRsQnhNcEpucjJoZmcxNE16UVhXWXdVOFdVcEVZaXpRYXJiWTVvaXBlc3NZVzVjZG0wY0psZm01azhEYUlZbmJGbjdKYk9EQ3pHbFZ1cXlhaXlPY2doREpJKzlrWC9wUlQ5QmZnQStKK0dabkNxQTg2WmVuMFRqY2lGcXlMZ1BqRjhlc21TQ3U3QWFGOFF4MmhMckZFenhmNEp0Nk5Xa25MVFJ5VmZHczBjMUZ1S2lGSC9STFcyQ0IrQzhNNUc0ZDllTnBNN2tYOVFTOFk5N0NjN0JMeDFYSHA1d2FqL0FrUXpmV29lY1J5WUxVYkVpTm9RbEpzT21BRXJZVXBVUyt0UWE2OVYxYWNBbU8zRlpmRThoQzVzZGciLCJtYWMiOiJiZTc1YzY1OGRkODJkODdjNTg1YTM1MTMzZGE4Y2M4N2I0ZTcyZmI0MjAxN2RhNzY0MTUyOTMwODM0YmM1OTU3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlpBcyt2ZHNPbWlMSFRvTjI4dEtEWVE9PSIsInZhbHVlIjoiMExHOUJvUkFKM2ZQT2R0Tm01UTBGU3lJTnFIQW10VFJ1eTUva0pxcFVWMVlYMGRoSVRyMExJRmZxd1hNRkpBblNWcXpYOWo3VmEwb3RtMGZUZEVscTVJVFRNK2dFSUtpWDljZGZyRWFZMUM2VXR5a2J4NENudUlLSVY4Z0cvS1F3Q3pmOUJXUUs0eXg3L1g0NjFpcWxURW1pdFlFYUY0c2FGaENrbzkvaGU4cWYyTSt6bkRNMG1BMzNpS1BoWXc2WFBIc2tYUUFLR1g4TElBdGhRQXloME5ENFVXQU9jT3RUa2QydDA3OVVwVnZrVFdXSDV2RVl4MTNKMUlzWU1XT3NTanNzb2E4T2QydGdNTG9wU0RoZkFRSHQyMnN4b1M4MFBWZEdhczFBNE92M1d2YWxDMGNQVSs3VlkxNUpTMFlvWDhNQlRtY01Xa2xKR2tSejNYVm83QTJON0FKOEpsN0pISzNjUlJ5eVA2VTUyS2lpOUxaR3VuTGxlQ0NHSmFiaFJtQXZwL21xMWM4MnRReVdURWkrMHIvSVducTZSOEpGcXhUelVCUUhZSEFjZ0tvNGdpaHVmbEVTR0tINDlVY3NQVU9hTG1RZWFWRWVma0ljU1ZsNWdyRHJFcEt6K2Yrc1h4NXBHTnphaUsveTF6LzlxaFprRkc0dmVKcUNRK0wiLCJtYWMiOiI3ZjgwZmY2YzExYjgzYzQwN2U1YTNjZjJjZDQ0NTFjMWFhMDA5MWNkNDk3MWJhNTVjNDJmMjM4MmU4Nzc5NzdkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpmYWtiWUYvS29pb2Q3TDRCZHFTM3c9PSIsInZhbHVlIjoicmhYdzBPb1ZoajF0UDRCWEZaa0JQN2dQRHZWOEdaa3ZJZTNwdEZIc0pDSDJJVDBmN0J4ZmRiek5nNkJpOW0vaGN1N2llbjNUc21wd2tKNlBJY2lCNEU2by9id212ckE5VXlsVDBtQ1FYOG93dVVsYmpkaFVjTlJSZDZFYVMrcmFTcUFRWVNjYnZIb0lSVFBUdUlzUm1rNHNJRzlTZ0ZVVVFYcG1pdm1NOEhjVjFqL0ZoZ1k3VjN4Ym5NNUZYblVyQnBMMU9zTVRsVVc3UU9hSWZYOXJxb0FjRHlubWVHTFZIQTJQSktseHRsQnhNcEpucjJoZmcxNE16UVhXWXdVOFdVcEVZaXpRYXJiWTVvaXBlc3NZVzVjZG0wY0psZm01azhEYUlZbmJGbjdKYk9EQ3pHbFZ1cXlhaXlPY2doREpJKzlrWC9wUlQ5QmZnQStKK0dabkNxQTg2WmVuMFRqY2lGcXlMZ1BqRjhlc21TQ3U3QWFGOFF4MmhMckZFenhmNEp0Nk5Xa25MVFJ5VmZHczBjMUZ1S2lGSC9STFcyQ0IrQzhNNUc0ZDllTnBNN2tYOVFTOFk5N0NjN0JMeDFYSHA1d2FqL0FrUXpmV29lY1J5WUxVYkVpTm9RbEpzT21BRXJZVXBVUyt0UWE2OVYxYWNBbU8zRlpmRThoQzVzZGciLCJtYWMiOiJiZTc1YzY1OGRkODJkODdjNTg1YTM1MTMzZGE4Y2M4N2I0ZTcyZmI0MjAxN2RhNzY0MTUyOTMwODM0YmM1OTU3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlpBcyt2ZHNPbWlMSFRvTjI4dEtEWVE9PSIsInZhbHVlIjoiMExHOUJvUkFKM2ZQT2R0Tm01UTBGU3lJTnFIQW10VFJ1eTUva0pxcFVWMVlYMGRoSVRyMExJRmZxd1hNRkpBblNWcXpYOWo3VmEwb3RtMGZUZEVscTVJVFRNK2dFSUtpWDljZGZyRWFZMUM2VXR5a2J4NENudUlLSVY4Z0cvS1F3Q3pmOUJXUUs0eXg3L1g0NjFpcWxURW1pdFlFYUY0c2FGaENrbzkvaGU4cWYyTSt6bkRNMG1BMzNpS1BoWXc2WFBIc2tYUUFLR1g4TElBdGhRQXloME5ENFVXQU9jT3RUa2QydDA3OVVwVnZrVFdXSDV2RVl4MTNKMUlzWU1XT3NTanNzb2E4T2QydGdNTG9wU0RoZkFRSHQyMnN4b1M4MFBWZEdhczFBNE92M1d2YWxDMGNQVSs3VlkxNUpTMFlvWDhNQlRtY01Xa2xKR2tSejNYVm83QTJON0FKOEpsN0pISzNjUlJ5eVA2VTUyS2lpOUxaR3VuTGxlQ0NHSmFiaFJtQXZwL21xMWM4MnRReVdURWkrMHIvSVducTZSOEpGcXhUelVCUUhZSEFjZ0tvNGdpaHVmbEVTR0tINDlVY3NQVU9hTG1RZWFWRWVma0ljU1ZsNWdyRHJFcEt6K2Yrc1h4NXBHTnphaUsveTF6LzlxaFprRkc0dmVKcUNRK0wiLCJtYWMiOiI3ZjgwZmY2YzExYjgzYzQwN2U1YTNjZjJjZDQ0NTFjMWFhMDA5MWNkNDk3MWJhNTVjNDJmMjM4MmU4Nzc5NzdkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-971187142 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6DOf3LRIRxH3sOgteNJGho7yggpQCjM4JOiRU4Kx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-971187142\", {\"maxDepth\":0})</script>\n"}}