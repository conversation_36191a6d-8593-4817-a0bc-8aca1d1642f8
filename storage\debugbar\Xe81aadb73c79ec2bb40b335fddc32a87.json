{"__meta": {"id": "Xe81aadb73c79ec2bb40b335fddc32a87", "datetime": "2025-06-30 23:14:04", "utime": **********.969989, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.543024, "end": **********.970009, "duration": 0.4269850254058838, "duration_str": "427ms", "measures": [{"label": "Booting", "start": **********.543024, "relative_start": 0, "end": **********.888953, "relative_end": **********.888953, "duration": 0.3459289073944092, "duration_str": "346ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.888965, "relative_start": 0.34594082832336426, "end": **********.970011, "relative_end": 1.9073486328125e-06, "duration": 0.08104610443115234, "duration_str": "81.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45941696, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.937671, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.943274, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.95998, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.962583, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.00753, "accumulated_duration_str": "7.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.921197, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 28.552}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9250112, "duration": 0.00257, "duration_str": "2.57ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 28.552, "width_percent": 34.13}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.929672, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 62.683, "width_percent": 2.656}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\erpq22\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.938407, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 65.339, "width_percent": 6.64}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq22\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9439569, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 71.979, "width_percent": 5.046}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq22\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.952463, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "kdmkjkqknb", "start_percent": 77.025, "width_percent": 9.562}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq22\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.955506, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "kdmkjkqknb", "start_percent": 86.587, "width_percent": 4.515}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq22\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9572031, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 91.102, "width_percent": 3.851}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\erpq22\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\erpq22\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.960962, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 94.954, "width_percent": 5.046}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "6DOf3LRIRxH3sOgteNJGho7yggpQCjM4JOiRU4Kx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1435923719 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1435923719\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-140265241 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-140265241\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1444454358 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1444454358\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1738317538 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325241921%7C32%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVvMU5NaHE3L29NQTFJcE5MaEtqbUE9PSIsInZhbHVlIjoibFNRMzRvQlNURlZHdW5SVWY3akJONW1GMDZjUCtIek4zSlVYdCtYYWZzZi90b21rWGZKN3NwU1BwWWVpVjBOY0JKbFRGWXNwTzBDU3lidHY4Tm1lN3hOZVdGcFcyR2lwZDF4Y09SeXNvbFd5NVZpT09EYlUwaHpMbkZHRnVpMzhTV1U3VUxQK0hoQmM3eHR5ZXMxTmdNTHg5aGNvU3Q5QzZ5UXBTM08xcU93ZDY5dVo3VUtJdzJxanhaeG9XREFPaEoxdnhyam9kUmJPYyt0b0J5WXpseVhIaDB6eGFkQlhLS3hESEV0N0pkSzhkRU1Na005WjdMZmI3cnpEM29RRm1yTjFQZ1c1elZ4Q1R4UmxUN012aStVWlJ4b1Bnb3BBUGRSV0tWR3M3QklOZXRxTzhmbVlzM3pJeGM0RGRPNUxIOWpBcmJjRHoyK3NHdnRxVHByRU9JMFJYRTUraHgxWFgrNkliOE04YXVxMGs4QTluRDRHdFMxS3lVcC9VcmszUWF5eWVQTmpRdVR6VVBCOHJNVE96QTFteDl0RUdMK3o3QVNmV1lEUnZLZHRMN3pwcEZQRjJyb3VQTnFvdE4rbVdsR1Nqcnl6YnBZNnIvUkFqQVVFQjhrMGdoaERUVjVsQlZMT3R6dmx6WTlvTlloTU82RFZpS01tUGVGRXRReVciLCJtYWMiOiJjY2NkOWIxOTI5YWQxZWMzOTVlMGViNmZkM2VlMzFhNTU4MzM1MzIzMmZhMmIxODRkNDkzNjQwZmJjNDFjZWE2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlVVR2dsMEJ3Z3lKYnFyak0rQXcyTEE9PSIsInZhbHVlIjoiR3Nic1VpMUluRUVmMVB3b1pydnU3MkJGVnhKTzlYMzB2bzBlektUWmlaUnJWeDRaZjBnemxjT1V0OGtsamVQd1ErNDQzQ08yQURrbW5ubEpFMGZhdXcyVEU4WGlUUFVmMS9LVlpiVGc4NXp1Z1FxNnlpaHduZXA0SE0xYVV2bkpyWFd4NCtOZkljUUZJNnFsNjdBK2s4dWZvMHNKbUxFWDZMRWZxaGxkZnVUYnlSU0NKcXlWU0tTN01qM3hFSGI0QnNhbithNk1QQUdSV2k0T3ZHWUFwMmdvL2ZwYzZkcXowWWduaXZRZmo2WXVvWXR0VXhGK09QN3Y5N0JwR0Jhc2o3RFVNMmI5MjVtVzQrcjZxRmlackVzSHBMc1pkTGZob3RGM2hlM3FkcktkbVZOUHUvQVFPbUdVdEZQZVNCOTZEOTdxYzM2azUrNjFsZExLeCt6TzllenFQeVZiYzVNV2lKeWNjK2dnVVg0UXdlNzhVaWZReFZ4Z0hUWWRhQTk2K2wwTFkzTWFFWi94cDlvaFdpOWc0QkFRSG1iSENabnltYmxNY3VEYUhYKyszS2Vjdk40bmJuVVlPaWI3ZlQ1bmxRamV5VFFQQ25NNG1RZlFoUmcza0dvQlNmbXFFelVxNVBDdGRXekxmM0hBamx1d1pVYmdWR2U0VEx6MzlIa2kiLCJtYWMiOiJhNjY1N2I1YjJjNjNhOTdmODZlNmU3ZjBmZmI2ODllYTQ0NDYzMDRmY2M0YWVlY2JlNTliYmEwYWQ5ZDBlOTZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1738317538\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-105546031 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6DOf3LRIRxH3sOgteNJGho7yggpQCjM4JOiRU4Kx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sMb5jzTWJNq3hLmJyShrQXVAdtXYkO6Vq50wL9dH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105546031\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1394272778 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:14:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhqWmx5WGJRaFdLZjJXOFJHVmJ1Mnc9PSIsInZhbHVlIjoiS0RUeEI3b3RKS0dnaTI1eG5oa1hoZmZQczJkZDhLUmduOEF4UElEYW5TeDQzcjZJQWxwQ2Y0LzU5dXRXMFdheTlqYktDOHA2QkZSNjZBNDM1VSttTkVsVy9ocE4yb2s3S1k2R3doM1Z2LzRKVmJOZzhSdmFURVpJRzVaWUdJZ0JVUzNyL2x3QWx4SUhVajZnWHU0NzNaR3RFbXdtVWN3V2tJSktnd1lUejNJeG9FbHJLQzNnRU5GZ1dkU2dPcENGblVVb29SN0krSy9kSGljZFVmU3FHTHd0TXpnRnU5bzByUDdqQUNDRjZiaWo2OGN2Q2hRMmVEam0yZzQ0ZExFR3lwOTltN3Y3Q056MTFwNThkcGZnOE4vVnRNR1M0NnBUOUZsZWhTY0VqeitRcFFoZGh5WXM1eEEwT3NsZjlZMTYzVElHQnplZWo1R2diMkNGOEp3WFNmdFBNWTdNUWV0ZkZLUHJoa0VsNjRiTWU2OGJCZDRTejE1bmh3TjlpbXZtSFVIbldZeUh6NGMrSjRvN0x0SzR1ZmYycmJlM2t2R2JxUFNmWjdidUFBRENPVU9LNFRKNGh1RWg5aWZIRUpscGtwNVYzNFVPTGFwdFIyMFVWRUR6RXlCMWdjUjg4eUxEYXREa0o0WjhaT2p4dG10bi9OeEpZay9mdm82T1JSNE4iLCJtYWMiOiI0MDkyNGUxMmJjM2I2MTRjODQwYmRhMmU5YTA1NTJiMTY5OTk2NDEwNTRlNWMwMWI3OWIzYzlmZjY4ZDdjNzZhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ind6YlhaNk1pUWdzNm9Ub1haMk1KeVE9PSIsInZhbHVlIjoiQjVndytvV1NFbFREUnJCRzFlU0x3ZU8wK2czUFpTT1ZtQjVoQy8wK0lUS0k5YTFYTkpoRW5VT2RncVIxRGFKNTFsM1BETjBzWjgrYmwxalUrN3lqWTFrMWxtZHNMRFBVZFF6c2RzRFZyNzdBRzNoZTlsQ2FjdHZpSGRDaGZlZTRsS2tHdTJBbGhZeE5KVWxiUGpKbHhNQ1VEbWpYNzEwbmV1Mkh4YmRrOEJJQTl3VkZqcmxpTzlnOXRsbGtmSEpLd0VidHE5SWxmenBYRldRZUhuakxTdFcxSmNOVHN1U3krY0FscnVZS1BHa21MUFlRMVN1TXFKVlNmaWZhdGpHekNqdC8yV0tKelNHL1ZRcndLclpibVk0SjRHa0FwRSt4cytobTk3ZVhPNk9oRk5EWi9ScW5WRmJ5Y3V3QzVjQ0N4bDQ4dU5jOTNHaWliUlU2S1lUa0tDTGQ5UUhZK1p6cG5CU2E4VkxtY1hMZTZlVHdJei90UlJQS2RuSVZvVDMyMHgxbVQ0d0UwNFR6NnlXc3cxVnVCSEFzOXN5NFRqOUNrOGtwWTBRZThEY05xOGJmTHBhZ3Q4TWI4MEpva2VGTHlxWWhxWkhkdFYzN3RNYzhpYmk4KzVTTTJ4TzZ0Qy95d1hxZWo5RmIwb0RMUjFIcWZUZ05wTzZyTkpVaG52Y0YiLCJtYWMiOiI5YzY5NDUyNzgxMjFkY2QwOGUzNjJkYmQ3ZWY2ODk1ZGEwOTY1OGQ4ZTFkZTdhNzBjNGRkMDU5ZDdkMWM2ZmQ4IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhqWmx5WGJRaFdLZjJXOFJHVmJ1Mnc9PSIsInZhbHVlIjoiS0RUeEI3b3RKS0dnaTI1eG5oa1hoZmZQczJkZDhLUmduOEF4UElEYW5TeDQzcjZJQWxwQ2Y0LzU5dXRXMFdheTlqYktDOHA2QkZSNjZBNDM1VSttTkVsVy9ocE4yb2s3S1k2R3doM1Z2LzRKVmJOZzhSdmFURVpJRzVaWUdJZ0JVUzNyL2x3QWx4SUhVajZnWHU0NzNaR3RFbXdtVWN3V2tJSktnd1lUejNJeG9FbHJLQzNnRU5GZ1dkU2dPcENGblVVb29SN0krSy9kSGljZFVmU3FHTHd0TXpnRnU5bzByUDdqQUNDRjZiaWo2OGN2Q2hRMmVEam0yZzQ0ZExFR3lwOTltN3Y3Q056MTFwNThkcGZnOE4vVnRNR1M0NnBUOUZsZWhTY0VqeitRcFFoZGh5WXM1eEEwT3NsZjlZMTYzVElHQnplZWo1R2diMkNGOEp3WFNmdFBNWTdNUWV0ZkZLUHJoa0VsNjRiTWU2OGJCZDRTejE1bmh3TjlpbXZtSFVIbldZeUh6NGMrSjRvN0x0SzR1ZmYycmJlM2t2R2JxUFNmWjdidUFBRENPVU9LNFRKNGh1RWg5aWZIRUpscGtwNVYzNFVPTGFwdFIyMFVWRUR6RXlCMWdjUjg4eUxEYXREa0o0WjhaT2p4dG10bi9OeEpZay9mdm82T1JSNE4iLCJtYWMiOiI0MDkyNGUxMmJjM2I2MTRjODQwYmRhMmU5YTA1NTJiMTY5OTk2NDEwNTRlNWMwMWI3OWIzYzlmZjY4ZDdjNzZhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ind6YlhaNk1pUWdzNm9Ub1haMk1KeVE9PSIsInZhbHVlIjoiQjVndytvV1NFbFREUnJCRzFlU0x3ZU8wK2czUFpTT1ZtQjVoQy8wK0lUS0k5YTFYTkpoRW5VT2RncVIxRGFKNTFsM1BETjBzWjgrYmwxalUrN3lqWTFrMWxtZHNMRFBVZFF6c2RzRFZyNzdBRzNoZTlsQ2FjdHZpSGRDaGZlZTRsS2tHdTJBbGhZeE5KVWxiUGpKbHhNQ1VEbWpYNzEwbmV1Mkh4YmRrOEJJQTl3VkZqcmxpTzlnOXRsbGtmSEpLd0VidHE5SWxmenBYRldRZUhuakxTdFcxSmNOVHN1U3krY0FscnVZS1BHa21MUFlRMVN1TXFKVlNmaWZhdGpHekNqdC8yV0tKelNHL1ZRcndLclpibVk0SjRHa0FwRSt4cytobTk3ZVhPNk9oRk5EWi9ScW5WRmJ5Y3V3QzVjQ0N4bDQ4dU5jOTNHaWliUlU2S1lUa0tDTGQ5UUhZK1p6cG5CU2E4VkxtY1hMZTZlVHdJei90UlJQS2RuSVZvVDMyMHgxbVQ0d0UwNFR6NnlXc3cxVnVCSEFzOXN5NFRqOUNrOGtwWTBRZThEY05xOGJmTHBhZ3Q4TWI4MEpva2VGTHlxWWhxWkhkdFYzN3RNYzhpYmk4KzVTTTJ4TzZ0Qy95d1hxZWo5RmIwb0RMUjFIcWZUZ05wTzZyTkpVaG52Y0YiLCJtYWMiOiI5YzY5NDUyNzgxMjFkY2QwOGUzNjJkYmQ3ZWY2ODk1ZGEwOTY1OGQ4ZTFkZTdhNzBjNGRkMDU5ZDdkMWM2ZmQ4IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1394272778\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-117824475 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6DOf3LRIRxH3sOgteNJGho7yggpQCjM4JOiRU4Kx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-117824475\", {\"maxDepth\":0})</script>\n"}}