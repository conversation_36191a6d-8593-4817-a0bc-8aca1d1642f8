{"__meta": {"id": "Xa7c5f2999e28c44162d2e65ddc9fd0a7", "datetime": "2025-06-30 23:13:00", "utime": **********.331607, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751325179.846818, "end": **********.331619, "duration": 0.4848010540008545, "duration_str": "485ms", "measures": [{"label": "Booting", "start": 1751325179.846818, "relative_start": 0, "end": **********.257564, "relative_end": **********.257564, "duration": 0.41074609756469727, "duration_str": "411ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.257573, "relative_start": 0.410754919052124, "end": **********.331621, "relative_end": 1.9073486328125e-06, "duration": 0.07404804229736328, "duration_str": "74.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45353168, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01884, "accumulated_duration_str": "18.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.295675, "duration": 0.01773, "duration_str": "17.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.108}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.321892, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.108, "width_percent": 2.919}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.325268, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 97.028, "width_percent": 2.972}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-473039934 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2818 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751325027058%7C14%7C1%7Co.clarity.ms%2Fcollect; laravel_session=eyJpdiI6ImpxTklOWXhTYU1XWEJkQTBqVTRXZlE9PSIsInZhbHVlIjoiTXFGZEpQRXlGa212dDlvcjMyZUgzOTJTdTZUUld1Y0RIbVM4elBEck42T2RTa0tjV2NoaTJNekRxM3Vkc3lrR1dOeGt5blA0Ym55U2pNZWY5am1QNC9lMm5SZE1VcGh6OVBneXMxN2Y4Qmx1TWhKckg0OXNOdzJBV1ZmTzVubEw3cmlyUDRwVnhxYzd4NmhqaEJiZThRcHlESndxYTY0WWNKZ1pqeVdFSTZuZnRmZ1BDZy91c3ZCbWloYytBQnpaczBrTmh2S3Z1dllBcHJYMkhWOXM3alFFWENqVkhpc2Rnb1lHSlpwM2dNb0s5cUxpakE5OVNEbEZRZ0VUVDJBWFZKWmZyYjZRM2ZNL1BuR0l5L3BsYXhVbzEwQ1ZlaXYySVUrQnk5a0dvY1BKK2dwQnEzM3ppZnFlaW9XTW9Ga1pSQ29mZktuT3BjSnc5ampzYU1SdTIxNzd4Wms4eUJ6eWdrajg1UUhYS2puSkhMNDVRbEh3WENJazQ2Qzk0Vlk5cE5NYW1qQ0xCbWJBVHZ5ZUlzUzBGRTJCWGliL1ovUHlOejhGSHJzRS9UOC8wK0QvTTQ4WHdveVBkTE1CdHlWcnFrQWdDcU1jR0hscFd0SUgzYWRtbFRaT0pzTGUzSkVDS2k4YlhiU0s3b29aMU5tbWtnL1NUaWZ1VmhxYnZ3ZFoiLCJtYWMiOiJjNTgzZDkxNDlmYmY2OTAwMmNmYTUwNTg0OWMwNDQ3MTVmZWQ0YWFiNjk4ZTJkZDM4YTkzNjhiMzE3YzIyNjlmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlpCSklubjBqc0N4cFFNNnIrZXZYWEE9PSIsInZhbHVlIjoiYUVDYXJXMHFpUnlVc0JhRWxuQUdGMFh5WkdZeHBUZG1PM09hRnFYaDNOV293SnFEaDZrRzR5ZWdGRkhKYURwTkVLck5YbE4xWkxIN2hkdkdvMnNyQ2xQVnVhT0RBZjNpRlovdGdJN2QwSzJ2TWxNVmdIYWVLbFQyZFM4UmVGZU5jdy9VY1V6c3VKY2lZWjQyM0d0LzBHNFFCNSs3WVl3L1hUMFp1K1Z3UHlPVEFSTmpkS2RQMmV2aUIwejBRNzM5Und1NVZ1QWcyakswN2V5aGdTbzFJTEhyTG1WZndQUDJieTRNc3JhQnlTOXBvVTVGcFZ2eHdlUFlkcUt6TVdxa1hvNVYycHBaWGpBY2UxeTA4TTBsSHZIemZVVzhoOVNDS3FhbEFCSVU0M0hWeXJZZlFBYXJKYmxMUTZDTFlXekhpbnNndGh6MzdTZVFwU2d0OFd4R2FnVXFMRUFUVXc5ZUgwQjMzMXBGaDVwZW8wTlNveWQ4bTB0Wm5MU1RuNExTdWphUTd4RHQxbmVFdzNieGZUOVBXbFJPT21HbnZDSW1MdFFWekpoNDJSckZ5aW5pUUdtVTZjYzI5SDZyeW82eWRQamg2NlpYaCtnZFFmWU01ZkpWWGZ0Y2RkQkt3VXUyWk9sNGxjQi9OVGpkV1ZHK01oYlNLS3pKWkVnam5URVAiLCJtYWMiOiI2OTM2OWQzZGVhNTVhYWVmMGI2MmVmNjkzYjg3N2FhN2I5OGFjYmEyYzFhNWExZmVjMTY3ZDdlNWNlOGIwZmZmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlpSNlp3N0t3SXpROVAzWWh3R0VKQ2c9PSIsInZhbHVlIjoiZEVmTy9HTlVLYStZNlFjc25LRkhIV0Rtd2pKa3c2TjhrMHp2M3NDdi8wVEg3WWg0d3grTnhqK0xsdm1ieVBmWXE1cnBUVHEyQjlaTzhCK25qLzRrSXRzcTRjM1hZZ0NIS0p2L2c1MTBUVmw0TTc0QWxGay9XWm5COHB6Sml3Nng3TTR1VEtMWUVrK0NXRVdXeDkzYUtqNmJ5dWZPbThTc3NrZU9ENXB6cnlVY3BNei9HZmtFMXBpTUVWaU5xVFV0aHhxZEQ2VTJZMkgyUlNjUmZ0MmkvcTZIMUhNcWJtUmR6SXl1YXZZVEdxK3hkOGxqM1lrdFRMaWNtUjFPcE5rMkl3U29ZSVhubVF1NXVMWjFuQXNyamh2REpNeHNvekszS1czY0lhS20xUWhWQmFnVE8wMTMvYzBwMFo4dXZKcCtXeE4xVnJnVnpqcmE0eS9RR1VzNmJtSEVmOG4raWpGRk5zL1V6MXcyenFUMmVkYzZ5dEx6WmVZeCtaVXNvYlF4d1pUczZ3RURTOTJhcmFUWUh5ZVVmWWdZRWZNeE9wZ1ZnMEYvTEVEdHdjaFdSS3gyNjAwcTZHZ082U1drRjlUTGsrb1ljNG43WmZLUCtpckJ5RmMvdEJJSzdPQ0Iwc29Uc2Iwa3ozVlhhdUJEdm5LTEVaZ3FlR0JKUXpvbFhZekgiLCJtYWMiOiJkZDMwZGY2ODRmNjIxY2U2Y2NmMjZhNzdmMjhhNDMyOGQyOTIxN2U1ZTJjNjIyZWNmNTIxNzNhZGU4ZThkOGI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-473039934\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-363347588 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nMXQGGGSGQFIfGwlrnEYCl0LIB0DtCvcZqQRlbGj</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-363347588\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-166866728 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:13:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktVU1RDY21PcWVNZVdJMTZ3eXpRK0E9PSIsInZhbHVlIjoiUG11dzlXMC9oeldwanFETERvZWtwSFZqZ1Y1ZmlFVDNyUXJWaFlmT2Fya1pPSXFuUFdlaTcvcnpuNTNmbUZjRmU3MU81Vm5WTmhpOWVMdittek85Mks5TXZLRXRCUEpONENibmZvRElsVGhvNUF1ZVkwZmxwR0p1VDJrL09WTnFONDdMbGwzTktsRVFGN0hoWERrTDFMSUtsMVdDZXdwTlZtL1NvM0dxNXJydVVwZ0VOMHdIdzlySGZQak9GNG8ybE15OTdydGt6YzVQb2ZLbWtMd0YzS2RTeVhEUngzOEtKUStFajFEWWJHSjNvaHp1Z2JldWkxQ0U3MnoyL0FRdGZtNHM0T3VFbzdXZEVpTjJ3SUo1aXpyTVFBbEVHNXdEL1JaMG5CdElndFYrUEQ3YnNLZE9wTFY1NFpFaTFoTlllSmd3djBBK1RXWWlHdjl3aFZXSmFJc1lHNDkvbkNPeXBSMTFSMjFxb25qTmRpbDVRajJXVmN2b2ErOFNWdGtrSTU4OFFiRHNjUEg4QXRFSlZlYWtQK2dkLzlRb01oVjNjRy83em5MeWZIVTM0TUk5MWUwUndNV3dnaE4vQUV0cVJ3czVWTHVnUEI4aWpmYWtvaDh2aG5FQXFtVWl2Z1VncTlZSnJoajZBTVV6UlF3NmtKMWQyc1hkVG01aUhZK2giLCJtYWMiOiJhNmNiMmNhZTdiOTUwNTRmODgwMTA1NzI0OWQ0MzZjNDEzNDM0Y2IxNDc2MGQ1ZWUzODRiZmFjZGQyMDI2ZmVjIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Iit3LzFQUVVHYnJDamRKVDVDTUF1d3c9PSIsInZhbHVlIjoicDU5ZUY3L0ZwZjZUdnVhekdvZnhJSEUzQ3lVVko4YThtaklwOEZ1Vm54SnFMZ3Bvb3VHZnVoZHcrSUN2U3RFSDR4c0lVRHFXalp2MkVFTnh3cjdacXcwYmpXOHpTTUJJZm9FR0oyQ1JoOElLRXQ4UnlQdFY2UlgybTJEMDFJS2NXZ1NCY0VwMkM3U1lPYWo4TmJLS0pjRitvVEVhdzNUYmdrZzVvV2Uzei9YM0lGZTcra05HUENCdCtXR2hpc0lPZW9uczBRanZMZjQ5OXpxdVhtUTMyM20rNGl1TFVIbWMrT2NzN1JMLzAwQ0dVK0sydmtrcytNb1pMbm5jVVFxdithWVJxbDNWQ0d6ejljZHVjQzExS3lOaEk4enlIQ3Y2cWp5Q0tiMUgrcEZ4ZDZCV21ZMUVnc1duSXg2Nlp5Y2lpSUtHYzlQeWZRbVJlWndMR1BhQVlqN2p1U1dGVHZDaXhJZlozUDR5WGp4cDY3SW14SDFCYnJ3ODRxYi91dXg4QUJ2Si91YlQyT1pjQUhxVjFWbGZYTFJObWh4WkduTjQ2Y3l0TnpaVUlEY0YvR295UGJJRnN5dlRGOGVmNjVZNnZOMkZ2SVI0bU0vUnBkNUEvNkZ5eW9KdHEvUmhGMmxSWlBrRzFnbjZBT0JmalBVb2xiaWZlTk9sM3F0eHJhWWsiLCJtYWMiOiJkOGJjZTQzMjViODZjZWM2ZDA0ZDIyOTI4NzA5MmEwM2ZkYmZkMmYxYjU1ZGEyMGI1YzI2MTM3NzUyOTI1NGEzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktVU1RDY21PcWVNZVdJMTZ3eXpRK0E9PSIsInZhbHVlIjoiUG11dzlXMC9oeldwanFETERvZWtwSFZqZ1Y1ZmlFVDNyUXJWaFlmT2Fya1pPSXFuUFdlaTcvcnpuNTNmbUZjRmU3MU81Vm5WTmhpOWVMdittek85Mks5TXZLRXRCUEpONENibmZvRElsVGhvNUF1ZVkwZmxwR0p1VDJrL09WTnFONDdMbGwzTktsRVFGN0hoWERrTDFMSUtsMVdDZXdwTlZtL1NvM0dxNXJydVVwZ0VOMHdIdzlySGZQak9GNG8ybE15OTdydGt6YzVQb2ZLbWtMd0YzS2RTeVhEUngzOEtKUStFajFEWWJHSjNvaHp1Z2JldWkxQ0U3MnoyL0FRdGZtNHM0T3VFbzdXZEVpTjJ3SUo1aXpyTVFBbEVHNXdEL1JaMG5CdElndFYrUEQ3YnNLZE9wTFY1NFpFaTFoTlllSmd3djBBK1RXWWlHdjl3aFZXSmFJc1lHNDkvbkNPeXBSMTFSMjFxb25qTmRpbDVRajJXVmN2b2ErOFNWdGtrSTU4OFFiRHNjUEg4QXRFSlZlYWtQK2dkLzlRb01oVjNjRy83em5MeWZIVTM0TUk5MWUwUndNV3dnaE4vQUV0cVJ3czVWTHVnUEI4aWpmYWtvaDh2aG5FQXFtVWl2Z1VncTlZSnJoajZBTVV6UlF3NmtKMWQyc1hkVG01aUhZK2giLCJtYWMiOiJhNmNiMmNhZTdiOTUwNTRmODgwMTA1NzI0OWQ0MzZjNDEzNDM0Y2IxNDc2MGQ1ZWUzODRiZmFjZGQyMDI2ZmVjIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Iit3LzFQUVVHYnJDamRKVDVDTUF1d3c9PSIsInZhbHVlIjoicDU5ZUY3L0ZwZjZUdnVhekdvZnhJSEUzQ3lVVko4YThtaklwOEZ1Vm54SnFMZ3Bvb3VHZnVoZHcrSUN2U3RFSDR4c0lVRHFXalp2MkVFTnh3cjdacXcwYmpXOHpTTUJJZm9FR0oyQ1JoOElLRXQ4UnlQdFY2UlgybTJEMDFJS2NXZ1NCY0VwMkM3U1lPYWo4TmJLS0pjRitvVEVhdzNUYmdrZzVvV2Uzei9YM0lGZTcra05HUENCdCtXR2hpc0lPZW9uczBRanZMZjQ5OXpxdVhtUTMyM20rNGl1TFVIbWMrT2NzN1JMLzAwQ0dVK0sydmtrcytNb1pMbm5jVVFxdithWVJxbDNWQ0d6ejljZHVjQzExS3lOaEk4enlIQ3Y2cWp5Q0tiMUgrcEZ4ZDZCV21ZMUVnc1duSXg2Nlp5Y2lpSUtHYzlQeWZRbVJlWndMR1BhQVlqN2p1U1dGVHZDaXhJZlozUDR5WGp4cDY3SW14SDFCYnJ3ODRxYi91dXg4QUJ2Si91YlQyT1pjQUhxVjFWbGZYTFJObWh4WkduTjQ2Y3l0TnpaVUlEY0YvR295UGJJRnN5dlRGOGVmNjVZNnZOMkZ2SVI0bU0vUnBkNUEvNkZ5eW9KdHEvUmhGMmxSWlBrRzFnbjZBT0JmalBVb2xiaWZlTk9sM3F0eHJhWWsiLCJtYWMiOiJkOGJjZTQzMjViODZjZWM2ZDA0ZDIyOTI4NzA5MmEwM2ZkYmZkMmYxYjU1ZGEyMGI1YzI2MTM3NzUyOTI1NGEzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-166866728\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}