{"__meta": {"id": "X7ce358f93bae1b59f9b0c23cd0599d78", "datetime": "2025-06-30 22:36:53", "utime": **********.207579, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323012.719703, "end": **********.207599, "duration": 0.4878959655761719, "duration_str": "488ms", "measures": [{"label": "Booting", "start": 1751323012.719703, "relative_start": 0, "end": **********.126267, "relative_end": **********.126267, "duration": 0.40656399726867676, "duration_str": "407ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.126277, "relative_start": 0.406574010848999, "end": **********.207601, "relative_end": 2.1457672119140625e-06, "duration": 0.08132410049438477, "duration_str": "81.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45709072, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01349, "accumulated_duration_str": "13.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.157334, "duration": 0.01238, "duration_str": "12.38ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 91.772}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.183688, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 91.772, "width_percent": 3.706}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.191362, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.478, "width_percent": 4.522}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-255145245 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-255145245\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-5641783 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-5641783\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-594348030 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-594348030\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=6nrx0y%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1aj6s00%7C1751323011454%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpSazVIR29GTlBHRTRVeUVuVGQvVVE9PSIsInZhbHVlIjoiUzNZVk1DUkZ1eGxIOE8wdytpeWVQeUNDSjZvbmEwemVJdVpxSDZ3enJBUHRIVkIxR1FRekxGcFllOE5xMVpCay8wT1ZhNHdHTEExYUtZUlRLTVJzMmhYMFdCaDdCQWRrRS9nYTB6c2NQRVZmZE5rRWhvK1RhQ0VSaDZscTBVRWNHVU1uajBQeEdUeWx5TjlCQnJwYUxHYjVrTUVXeVFYVW9kaW01UEVXaHRvbEJ1bnVIYy8wcTlUNXBKNGRkVHc1UU9ZdXdleGRLMmtWNnVKemlrRHF3MWppdWxDRG5ydGhkZ09oQ0dpUW93RTBGZUZzZm9lcDRnVEM0dFZTdXMzdVBxVnZ6RmRmR3lyd0NOcHZvN2c1dnBrSHNTL1J0eTFvUHJGQXR0OHQ2TEs4K3djWTFhbkZsaWJ2MXRjZy9oOVNSbkZnaHNZYjNGOFVTUjZmS1RBaWE5NHVwa1VPZ08yTGV5dERZTjlGN05yUnc2MGxobWJKUHhzeVRMSEhUTXVrblp2Smd5VStaZm1uUllLWllLMUpyLzI3aENwRmJhc1dMUE94WkR1VVR2NUhsNGFITEsyY3RFOCtjaUpMZmJoc0FPamE0WnhmeFdQTVY3RGFmL0ZqSDNMblJHOVgvQlNIUXlvWFM3a0RyU2N1UXowRkxWMkw1bUROalB5NkFVeHYiLCJtYWMiOiIzYTlhNTdmZjhkOWVkNzdkZGE3YmEwODA1ZmE0YmE0Y2VkMTZlNjkxY2YxODg4ZDkzOTFiMzA1NmVhODY1YmUzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlpmRUpqVWlUWlArVGtjbXBqdEIrUWc9PSIsInZhbHVlIjoiNEcvbnpHNXc3WC90QU10UStJMVFQQThac0grYVZkUjduZm5xWlg5QjNDbktKZDJ1aCtXNjRDaENOZWw3cWhCTlo5ajV3cHdId0daV3V6dGF4ZUpVekFmb1FjYUdhWnl5L3dRdXRHUmxPc3ZSTGZHSndwTUlSRWpPUEJzYmVydU91MWFHSGFDamN0aEJyK0Q1dUs2RWViVmREelY5YmFtNG0rc1BkREhqU1M2YTNjVnRhWDVmbjhkRHJNMDg1cXc1MUpOcTFrQTBZSW44MkxLNEE1SHVuK1ZMSEtqeklSR09TMnBGTGFCMEt2UjJrSDBmTVFRTk5mNnZFM0QvSEY4TWt6WDRvMVc3dU5VRGJsUFNpZHlIdlNyZ1hxVWhQSWh3OGpEOThNTk1CRmdjbTlkQ0I0a2xBaUt4ckF2YzU1WjdQYmprV3NNUHp4Yy9QV0QvdmJCRDZtbk5xTDRlbEJLUG1ueVRMTS9TVzhYdHRkR2JraUxHdkZPQUd4Z2d1MzlhT3pzNjVEdnRlc2FhajNXL3FCdlJoUzBuUU9XWDc3SWFlZHp6ekJUVzQzV2pHdWFFS3lDTW5mUWpLS1JJeC9vNFVxZ2Z3RTBqYmdhdTM0U2RIODByaks0d2lNOFpzZmZEZzlMVXdZaWQ1YnVBaHZYT3lmTnpiUkNQUytKc0t0VS8iLCJtYWMiOiI4YTE4YjZhY2FjOTJkNmQ0YjA0ODBmNDcxZDY5MTE2NzRjODk2MGY4ZGQwMzk4ZjQyMTgwNjNiMTcyZjA1ZDkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A9rdyCRm7SKpfljuMnVO7IpcgTBMITMjowAdsmJo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1981028142 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:36:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBaSGs0YnZKZU95OFdUaUZIUnJvRWc9PSIsInZhbHVlIjoiMjN0amdNbUp5TVEreERGSFAzcEYzTWlUSlltaUFHVG16RVllZ3NXSkdiQTJ6NTgwSjZ0VlhBSlVvTlMzN3Bocy9kWnBkZjBPaXlXMUtseVZ1aHJBd2JkM1pxZ1JTQS9nY2F6MEVjQ1YxQTI3T21DbUZGUnNMVDdkTzVFcUNnVXVIY0lnaEpsb1hrRHVFdUxyNkZqRFJFeXlnaWRpdWtlcXMxSFhCaS91SCtabjN4TjVZemJoQXd1dzBCWk0yR1hrMXdLOTlBL1M5d0xVRlprcjB5Rlova2M2LzJtUWZVaGlTODN3N1VRUUxjbnd2MGZRRExteDRsOGRMeXU0bmlQeE5ZdVBoMUpDbjh1UmFuS3lKcmZTekFJOXBzYWdHa0dzNTVlN05RM3gxaS9Cakc3dENBU25FTnBXcTlVcXY2ZUFSaXpwZ21hL0MrWkxhdmtVSm1DSWQrQi9YRkpmMzNrOWc1eFlEK3k0YW1OcE9iT3d6N0RINHh2WjB4YTgzbytET1cvWEZhUEltS2JEQ3UyYm1sNUJhNHZsaEJkS2ZLTjl5M2V2dXFseTZ0TWprdmVoeHk3WlFJRzh5L0xBUjFaK1NsUWpuVXcwU0crR292QXZ6Ym9hMkgxRVpjYU9BOFFldGlBNXZvMVc2TmpYdlJEVmNxdlpQeXlhTjUrNzFRTk0iLCJtYWMiOiI3N2JlMjFhNzljNmIwYTA5M2JlMDc1NzBmMGZiMmE5OWFhZWEzNzNkYThkOTc4MzRiNTRkYjI2YWNmMGI5OWM4IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImpHcVFpOHNMUTZ1aVEzVDBUSW0ybHc9PSIsInZhbHVlIjoiV3hmejAzU2k3Nkc1anBuVmNjZjc2R2tHNSsxZjZxam5oOE9LVzlhOXZFZXhSOXRzVTI2dndOdEtaVFVaWVIxSHNNcmR6Ky9KT3lIZHIwYWZ2cDNrbG12eGFNaW9pNkdzVzlUdmRkMmRZODF3RlgzQ0dpTlcrMloyK0Y2QWZMdEZCbTdLcnlHdUVRc29JNUdTWUJPMHVXMEZTcjNONnh5b0dLTmVYVmp3UmdQUWtBdTNXNG5vdkhvcWRRVUM5SGgzYXJROVk4RHNCSFhIbkNwVTBqZjQ0ajFWS3AxeisvcUxNdm00TmQ3a01kaTVlZ3lLa3NUZy9kUnZoYy9USmhEamxiUi8vajNHTXpxT2E0MkRBRzJVaXRUcmx6ZlMraEtqMUtLVGlUNU4rYzdzUDlOclpwU1JxQjBJNkUvenpMVVlQVlNjMTZGc01FNlJ3b1dycUl4U25PbUh2bXhiTXlVTlBuSXBrUVQya2lON0JKdGlPR2s2VUlDb1pMMVVPaWQ2WEQvRGt6UldsNjQrVm5TSmxPQkl3cmduTG5ISHk3VVFXYmRndHpiOFB2WGtFVGZEMllDY1N3NnBsdnJaRzNpS0NTaEttbmhCZ0t2b2tqSWU1d0QyOTRtcVBpVkJFN1Y1cnRKN29Xa3FnS1hneUN2S3FreHZONnlsSXJRNlRKd24iLCJtYWMiOiJkZjkyYTFlODhkMWE2MzVmNmFmZGVmZDI0YmE4NjNmYzQ3OTJlMTkwYjM1ZWJjY2ZhM2Y3Y2RiNGEzYjVmMDQ4IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBaSGs0YnZKZU95OFdUaUZIUnJvRWc9PSIsInZhbHVlIjoiMjN0amdNbUp5TVEreERGSFAzcEYzTWlUSlltaUFHVG16RVllZ3NXSkdiQTJ6NTgwSjZ0VlhBSlVvTlMzN3Bocy9kWnBkZjBPaXlXMUtseVZ1aHJBd2JkM1pxZ1JTQS9nY2F6MEVjQ1YxQTI3T21DbUZGUnNMVDdkTzVFcUNnVXVIY0lnaEpsb1hrRHVFdUxyNkZqRFJFeXlnaWRpdWtlcXMxSFhCaS91SCtabjN4TjVZemJoQXd1dzBCWk0yR1hrMXdLOTlBL1M5d0xVRlprcjB5Rlova2M2LzJtUWZVaGlTODN3N1VRUUxjbnd2MGZRRExteDRsOGRMeXU0bmlQeE5ZdVBoMUpDbjh1UmFuS3lKcmZTekFJOXBzYWdHa0dzNTVlN05RM3gxaS9Cakc3dENBU25FTnBXcTlVcXY2ZUFSaXpwZ21hL0MrWkxhdmtVSm1DSWQrQi9YRkpmMzNrOWc1eFlEK3k0YW1OcE9iT3d6N0RINHh2WjB4YTgzbytET1cvWEZhUEltS2JEQ3UyYm1sNUJhNHZsaEJkS2ZLTjl5M2V2dXFseTZ0TWprdmVoeHk3WlFJRzh5L0xBUjFaK1NsUWpuVXcwU0crR292QXZ6Ym9hMkgxRVpjYU9BOFFldGlBNXZvMVc2TmpYdlJEVmNxdlpQeXlhTjUrNzFRTk0iLCJtYWMiOiI3N2JlMjFhNzljNmIwYTA5M2JlMDc1NzBmMGZiMmE5OWFhZWEzNzNkYThkOTc4MzRiNTRkYjI2YWNmMGI5OWM4IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImpHcVFpOHNMUTZ1aVEzVDBUSW0ybHc9PSIsInZhbHVlIjoiV3hmejAzU2k3Nkc1anBuVmNjZjc2R2tHNSsxZjZxam5oOE9LVzlhOXZFZXhSOXRzVTI2dndOdEtaVFVaWVIxSHNNcmR6Ky9KT3lIZHIwYWZ2cDNrbG12eGFNaW9pNkdzVzlUdmRkMmRZODF3RlgzQ0dpTlcrMloyK0Y2QWZMdEZCbTdLcnlHdUVRc29JNUdTWUJPMHVXMEZTcjNONnh5b0dLTmVYVmp3UmdQUWtBdTNXNG5vdkhvcWRRVUM5SGgzYXJROVk4RHNCSFhIbkNwVTBqZjQ0ajFWS3AxeisvcUxNdm00TmQ3a01kaTVlZ3lLa3NUZy9kUnZoYy9USmhEamxiUi8vajNHTXpxT2E0MkRBRzJVaXRUcmx6ZlMraEtqMUtLVGlUNU4rYzdzUDlOclpwU1JxQjBJNkUvenpMVVlQVlNjMTZGc01FNlJ3b1dycUl4U25PbUh2bXhiTXlVTlBuSXBrUVQya2lON0JKdGlPR2s2VUlDb1pMMVVPaWQ2WEQvRGt6UldsNjQrVm5TSmxPQkl3cmduTG5ISHk3VVFXYmRndHpiOFB2WGtFVGZEMllDY1N3NnBsdnJaRzNpS0NTaEttbmhCZ0t2b2tqSWU1d0QyOTRtcVBpVkJFN1Y1cnRKN29Xa3FnS1hneUN2S3FreHZONnlsSXJRNlRKd24iLCJtYWMiOiJkZjkyYTFlODhkMWE2MzVmNmFmZGVmZDI0YmE4NjNmYzQ3OTJlMTkwYjM1ZWJjY2ZhM2Y3Y2RiNGEzYjVmMDQ4IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1981028142\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1695708243 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1695708243\", {\"maxDepth\":0})</script>\n"}}