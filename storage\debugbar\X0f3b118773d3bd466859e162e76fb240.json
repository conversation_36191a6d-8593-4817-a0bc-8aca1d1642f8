{"__meta": {"id": "X0f3b118773d3bd466859e162e76fb240", "datetime": "2025-06-30 23:13:29", "utime": **********.178841, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751325208.726932, "end": **********.178856, "duration": 0.45192384719848633, "duration_str": "452ms", "measures": [{"label": "Booting", "start": 1751325208.726932, "relative_start": 0, "end": **********.100927, "relative_end": **********.100927, "duration": 0.3739950656890869, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.100937, "relative_start": 0.3740048408508301, "end": **********.178858, "relative_end": 2.1457672119140625e-06, "duration": 0.07792115211486816, "duration_str": "77.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45047648, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.018090000000000002, "accumulated_duration_str": "18.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.130746, "duration": 0.0162, "duration_str": "16.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 89.552}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.155786, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 89.552, "width_percent": 2.819}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.163727, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 92.371, "width_percent": 4.478}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.17131, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.849, "width_percent": 3.151}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-544017172 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-544017172\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1549993638 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1549993638\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1305454470 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1305454470\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1092466005 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325205976%7C26%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFXcnppR0dicHMyeTUvbk50Y3gxT0E9PSIsInZhbHVlIjoiR0tHa28yQnp6aFFON3NSckQ4bUdiTTlyWVlGcWFHb0FscmdvUkFPaC8yNkxvb2IvMDJOQkNQM2tMZW5VaTB6eW12akRQY1lucnpXU3QvQ2tnVkEwblZHbEVCQmRkSDdjWUZ3bEJESVh2TXdjeEV2VkZ4Yjc5WG5Qc3VEMTN3QlN1Mmt5bkpjVk1tSGZmVy9YeC9wb3A4SjlFbXJ6eFI0dExRTGJBb3FxK1UrdFZ2ZVRmN1RzQXIxdThSSTVmKzBaaEIrd1dWRVo2Mk1zd2tXQzBhRGhWR0RSdzlZVWNJUDZwcjlkb0UrVklObmRGMEs2OG5meVRCTER4UGcrS1psYlBBdC9kV1AxTk9ZUzVEcUR0d1h5VWIvc3Rib0t3UTdKaU9XR2R3RkJsdDB5ZU03ek5tNE45RDFmTnJZbGZsYmxVeVdLb2duNVhROWdhZnVNV1h5VzI2T2FSUlZFQlhNVXI1QWhUenI4TDhIRm1JRk9WazRRdmxhMzlydzBwMkFvTHZkMXJKZTVWK1prMTZ4RVVtakxhRWNWTHhKcnBpTTJlM0RyOGhua1ByNzBkWUJlTFNMMFVaTlhkd2lPMFMrcFUrTWtzSFhkbEwwT3pwV01VUHRBZzFnRERKSWhCaVYyMHcrSVY0NmJVbzZlenJrS0h3ZTljejJCcmdkOTFOWHUiLCJtYWMiOiI5ZjA1NjM3OGFiMmRiZmE1MDkyYzUwMTFiOWNmM2ZmYzRhZTZlMmNmNWU2ODQxNDRhNzc4YTQ0YzgxOGNhNmE2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkpqNUg1WExCQ0lqQlhMVjAvV1lxZEE9PSIsInZhbHVlIjoiMlVRQS9sS25wcjVXNkkycU5DV2NOellFMWE4M2NwZWd2dXJNV1pqaWRISHJnMjg4a2xnR0d1eFF2QnFUdDl1T3NYZ0lmWWd1NlpwLzZnVFA2aC9PcWF6WTlUSTA1SXRuSDBCVitqNHJNVmxJdXBObWNGOURLRVN1M2s2Z2YrUzlLYmRNK2ZoMHFqMXQ5RjlaZ0I2cHVJdGRrdzJKOHVCOUcrRktUSkpRYkFMMmtoNWxvR2MzVnMwMG03KzExZEp0Z1huaVZkRW9IWHNxM3kyaVduMDMzOXhBNndNQkZybTljb0lnOWFGT3ZLU1dIaFoxRFg2SkVXc0xVWVZDclpBY3BMeVlPdDMxRmt0SU5aT2lCZ1QvY1graEQ4WmtsZDI2VmpEQmZCMm4xU0VrK3ovZXpqSzdGM3AxZUhPZGFpQWZ2SVptQ3ZMNE5FQXlWc2xobXIvSVl2WlRleGtLenZzYW00NENjNFJWTEFFS0JoNElHSDg0V1ZPeGdEd2FQUjBWMVQvZTUxL25DcFBBWmFabC85MCs2QisrWkNYVEYySXo4eFlNT3lzTHFPSVZQQU1wRVh4L2VsNjZybHN5ZHJmaklWMVhEeFVKSFdwY3ljS2NsNUpacUxUTHhlL2ZJdTRnd2FSVHhwVkpZa09PTkVnMTF3TkUzZEl6TER6eXRWMmYiLCJtYWMiOiIzNzUxYzU5YTExMDk0ZWI4ZDZiMjQwMDY2ZGI4NmQ4NDU5MGRmOWRjMjgzOWQyNTFlYTNkOGM1NGI4MjE3MTZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1092466005\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-690616827 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0aUeGT9vcZQoKvixhqnXLFDjX39UXyfabqLnLub</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-690616827\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1190754479 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:13:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IncvZUpONWZCQlUydzNiby9Ma2pZS0E9PSIsInZhbHVlIjoiNE5CbW5sRTVhaklqeTR4Tm9iWmgraHVlaHVBR2xZTi9TZHgrQ0dDR21nRTIzS0YyK1VMa2svU3dmbE5nRGxDNHdmYzMwVGpxUkczUXRXMU9iRjZJMEZVQzhackR0bTF3Qi9iUEJkZ1VHNkJvWS9TZFQyUjlxSlVjOTc5VHJ2V1FDVU1xbjM3ZFVJOGhLdm1DQXR4cHQ3QTd6VUtJbDFMWVVmOGlXSUZER2FwNTNDU3REblVGN1hCUDRTbERxTUoycFBqaE8wbnE3L1puaW5tWkdJUWJMbzUrVlN0bTRUTWlhWVJpVEI4MEQ3dStydCtkb1RqelFzaTAvd1JXUzcybVl5SVZxam45SmtnQlFGMW4rOEE0N1NKQUlRa0E3SVJSTmd6QWR0NzRDNnhoNUtXWGt4VnFsOFR0d3V3R3BwelhMdWFzdHY5b0twQVc2ejY4SFRkOUp6YzIxQ3RuR3kyRld3Q0EwL0JGWThsVDVjaXhWS3JZRzY1WmJzNFBYazIwRzF6UDR5Umcrc3J3VG9lN2RLSEtIYnBVeWNOOTd5NnRzOEhVYUZOV0ttYkJuMGJzYW50QXFTdnQrNTIrbDBrai90VlFrVU16YXNlRFdIS25Ca0VoeG85V0FVeVZobXB3eGdyMHFSREk3RFdzdllLWnVrVmVWZGJKd01nVnFudXQiLCJtYWMiOiI4ZDEyMThkNGMwM2ZkYjI4OGIzZDgxMGEzYmU3MjIxOTgzNmZkMGEwMDFhM2YzODZhZDQxM2VkY2M3NTM0NWQxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikc2b25TNzNkLzl3cU9HNE9kRzZxL2c9PSIsInZhbHVlIjoiS1BUSkZrM2s0UTFNWDAvY2Q3SVRpSnduaGE1SzE0ZTRFVkR3aHhzL0FNUUVoVmxKR2xRSEwvMXNGeHZkaU1ZYkJ1RlpVUGVwNDgyL2E5YVNZMmVDV2JLaWgyNUNoLzZPYkVHem1pdHc0TElJNkZBTWs0TVdUQjV0Q2hoUk9NdU1BWTVZQVhlYmpBOWI4a1RRWUlLWWE3K09TZkNWYjd3QW9BM2c3RSttOWhQbzRJQm1jSWliL24yWUJrZWF0L1V6VmpoZDFaNzltYkhIQlg0WjJ2T2MrVHkwYXpGNG5iUGdBM0sxSU1kZjJjZmk0M042TlFBeEhrL25ZZlVpVGQzbjlOUjV5TXU3WXBSV0pjRGQ2NkJ4WkVBeWtaS2l2aERITFpZOFFPci9FeXFaZStYQXdwd3lSRUFHYUFNTlFuZ1ZodGMvVFdFRUphTE9vSzA2YzI5L1lzWFhjZk1mSy9lSDUxSnc1bi9HV3oySm9aWUdLWlZ0dUZnWjkrNk9KUlJWWWFhUW1zUHBVSWFGdTJNK1VGYVhUVVVad3FPL09lQUI2bXl6MWdWV2ZrYnBNQXk1VW1nVDdCTm1rYlpaQTNqRXRUM1pSR3FIUEZuZWEra20xZGV2eWI2ajA5WGlRcUxMT1pZMTY5VElsWVZTK1JQMldSeG53YnY2N2RCMVpDWmQiLCJtYWMiOiJiYTQ1NThhNGQzNzhmY2M3MWEzZDQ2YTY1MDJhZTcwMzY2MjE5NzU3YjI4YzgwYzM2YzFlZjlmYmMxNWVhZTY3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IncvZUpONWZCQlUydzNiby9Ma2pZS0E9PSIsInZhbHVlIjoiNE5CbW5sRTVhaklqeTR4Tm9iWmgraHVlaHVBR2xZTi9TZHgrQ0dDR21nRTIzS0YyK1VMa2svU3dmbE5nRGxDNHdmYzMwVGpxUkczUXRXMU9iRjZJMEZVQzhackR0bTF3Qi9iUEJkZ1VHNkJvWS9TZFQyUjlxSlVjOTc5VHJ2V1FDVU1xbjM3ZFVJOGhLdm1DQXR4cHQ3QTd6VUtJbDFMWVVmOGlXSUZER2FwNTNDU3REblVGN1hCUDRTbERxTUoycFBqaE8wbnE3L1puaW5tWkdJUWJMbzUrVlN0bTRUTWlhWVJpVEI4MEQ3dStydCtkb1RqelFzaTAvd1JXUzcybVl5SVZxam45SmtnQlFGMW4rOEE0N1NKQUlRa0E3SVJSTmd6QWR0NzRDNnhoNUtXWGt4VnFsOFR0d3V3R3BwelhMdWFzdHY5b0twQVc2ejY4SFRkOUp6YzIxQ3RuR3kyRld3Q0EwL0JGWThsVDVjaXhWS3JZRzY1WmJzNFBYazIwRzF6UDR5Umcrc3J3VG9lN2RLSEtIYnBVeWNOOTd5NnRzOEhVYUZOV0ttYkJuMGJzYW50QXFTdnQrNTIrbDBrai90VlFrVU16YXNlRFdIS25Ca0VoeG85V0FVeVZobXB3eGdyMHFSREk3RFdzdllLWnVrVmVWZGJKd01nVnFudXQiLCJtYWMiOiI4ZDEyMThkNGMwM2ZkYjI4OGIzZDgxMGEzYmU3MjIxOTgzNmZkMGEwMDFhM2YzODZhZDQxM2VkY2M3NTM0NWQxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikc2b25TNzNkLzl3cU9HNE9kRzZxL2c9PSIsInZhbHVlIjoiS1BUSkZrM2s0UTFNWDAvY2Q3SVRpSnduaGE1SzE0ZTRFVkR3aHhzL0FNUUVoVmxKR2xRSEwvMXNGeHZkaU1ZYkJ1RlpVUGVwNDgyL2E5YVNZMmVDV2JLaWgyNUNoLzZPYkVHem1pdHc0TElJNkZBTWs0TVdUQjV0Q2hoUk9NdU1BWTVZQVhlYmpBOWI4a1RRWUlLWWE3K09TZkNWYjd3QW9BM2c3RSttOWhQbzRJQm1jSWliL24yWUJrZWF0L1V6VmpoZDFaNzltYkhIQlg0WjJ2T2MrVHkwYXpGNG5iUGdBM0sxSU1kZjJjZmk0M042TlFBeEhrL25ZZlVpVGQzbjlOUjV5TXU3WXBSV0pjRGQ2NkJ4WkVBeWtaS2l2aERITFpZOFFPci9FeXFaZStYQXdwd3lSRUFHYUFNTlFuZ1ZodGMvVFdFRUphTE9vSzA2YzI5L1lzWFhjZk1mSy9lSDUxSnc1bi9HV3oySm9aWUdLWlZ0dUZnWjkrNk9KUlJWWWFhUW1zUHBVSWFGdTJNK1VGYVhUVVVad3FPL09lQUI2bXl6MWdWV2ZrYnBNQXk1VW1nVDdCTm1rYlpaQTNqRXRUM1pSR3FIUEZuZWEra20xZGV2eWI2ajA5WGlRcUxMT1pZMTY5VElsWVZTK1JQMldSeG53YnY2N2RCMVpDWmQiLCJtYWMiOiJiYTQ1NThhNGQzNzhmY2M3MWEzZDQ2YTY1MDJhZTcwMzY2MjE5NzU3YjI4YzgwYzM2YzFlZjlmYmMxNWVhZTY3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1190754479\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1396956768 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1396956768\", {\"maxDepth\":0})</script>\n"}}