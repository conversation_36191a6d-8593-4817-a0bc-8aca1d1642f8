{"__meta": {"id": "Xb8f4c257311c843ff01a27a68534d1e3", "datetime": "2025-06-30 22:42:28", "utime": **********.050639, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323347.63738, "end": **********.050656, "duration": 0.41327619552612305, "duration_str": "413ms", "measures": [{"label": "Booting", "start": 1751323347.63738, "relative_start": 0, "end": 1751323347.986777, "relative_end": 1751323347.986777, "duration": 0.3493971824645996, "duration_str": "349ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751323347.986787, "relative_start": 0.3494071960449219, "end": **********.050657, "relative_end": 9.5367431640625e-07, "duration": 0.06386995315551758, "duration_str": "63.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45140816, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.017800000000000003, "accumulated_duration_str": "17.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0148182, "duration": 0.016800000000000002, "duration_str": "16.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.382}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.040518, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.382, "width_percent": 2.809}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.043468, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 97.191, "width_percent": 2.809}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:3 [\n  2353 => array:9 [\n    \"name\" => \"مفتاح علبه\"\n    \"quantity\" => \"1\"\n    \"price\" => \"6.00\"\n    \"id\" => \"2353\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2354 => array:8 [\n    \"name\" => \"العاب شكل بطه\"\n    \"quantity\" => \"1\"\n    \"price\" => \"6.50\"\n    \"tax\" => 0\n    \"subtotal\" => 6.5\n    \"id\" => \"2354\"\n    \"originalquantity\" => 10\n    \"product_tax\" => \"-\"\n  ]\n  2355 => array:8 [\n    \"name\" => \"العاب اطفال\"\n    \"quantity\" => 1\n    \"price\" => \"6.50\"\n    \"tax\" => 0\n    \"subtotal\" => 6.5\n    \"id\" => \"2355\"\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-8853805 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-8853805\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1462496639 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1462496639\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1830534322 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1830534322\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1344128800 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323255132%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Iko2WkM3WklETmhML1NqUWduRStMMmc9PSIsInZhbHVlIjoiaHFZcjNCWE1kV084QmxXSDVmRFZEMFU1QjZUVlh5N01aQzRQLzY3OUxxL053ME9ROWd0VjNRaExiUlNhWk4xUEc3QmZmYjVMNko5YU1XSXFGdFQxek9xM21TcFEzMHlpd1g2Q1BWckgranhia216Zm9ZcXN1YzM3NXRXTkRWaHZnRW0wS2dsZlVRRDBXZncxM2lTUisvMlJ1WFFuN3lVQ2dZWlc3R0QvRlUvRXUxRk9UVjZBWGpiSHJhcUhLODF5alhxZ0d0WXZnWFJkdzVZcHlhOVYzMmJENW5aTXBDbXNHdmZTTGtEU2paNm9GUmc5MmZmYXFFbVRyRUdMWnJFNUNXUUFTQnJEOVNZeU51RXUwUEl0TGtYT2tBSHhZd3M2eFloOXB1bS9FbHdlODNQMHlqalV0ZVJHQnY4V2lRc1lTY1VQTUJJWWhMSTVkWXdwVGtFT2JqQ0czU2lUdGVLR2JlWERqTE5sZUxKWkJUb3FTUDR4NXlzelFsTnRYdW1DaVBKQWRuU3BTeW44eDJCV1hyYmdPbjg1cG9Uc2ZqUHc1UGNUMVdnTk5Rc3Zxd1JUZGVWbG5ZYkJJWXNGY2JuTnU3UklackJzMzQzNE5ISXBjOFVzRXk2a2JOMXc4WFNaU0hQNUxBUGNtcU1qVVM2WkIzb3dEb0FwcWxtV043YmciLCJtYWMiOiJkMjg2NmFkZWRjYTYyNDkxYWZmNGZlNDc1MDJlN2U5Njk2OGRiOWIzMGUwZjExNjdlNGVkZTBkYzdkOGZmYWU3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InhLRm5BY2FIMnVLSzd3UTU0SlVNR1E9PSIsInZhbHVlIjoiMGt3UU50UVVORXBVSUpMdTFGc1FvcXF1RVc0QUl5RGFkUUZWaGlnQjQzc01NZzF4MDVRVHhpSG41ZWxkbkpEbTJEclI3UFUvQ2JWMHNGd0E1WlV6VzZESE92OHVUMTBaS2tteEtaNGtONU0waHZNL1prN1JBOHc5c3NVZHVZVWprSnN5SlF4ZC9HeUk2YXNld3htQWhhTW0zSUJNQXRIMmphbjV2emdRQmNwOE4rbG0zMlVaSnBzWDNkbWZrUTUyTCtZeVg3SHYwYlZsZ3dpSUVkWFgxTTRERnZYbXBBU2llR25PZEdZQjRBTHFzelMzbEhCMHRLTzRZUjBDZzRrZUpEL2ZVRXlsdnZ6NUtHb21sejhUeFV6eTNZWFhpV0RzVHpsT1lOQTNmd3hqcWgvR3JnTU50dzhIM2paSzI3YWVRUm5vaTRJUEZWaHdoTmV3TnFNa1dRSXRZdUlUeW9jczl1dURDQWZJQ0NlV010Z2tibURyRDhnU0hnVW55ckJBNzF5Q0pmOGZQR01IcERTSnpPdjAxRkFuYzEvT2cyRDBNSHo0WUE4QWVVQ09hY0VGTVZqMmdPdmIzeHNVQ3hLbHdBZElSalRVVjJ2c1IwTTIvdkQzcXJBNTY0VGJnVXRIWFVnYWUyQzd1dG5WNjJvcTVJMHY3UlVsUmZWMzUrY3oiLCJtYWMiOiIyM2YwMWNlMTQzNzE3MDk3OGMxZmE2MDI4Y2QyOGY3YzkyYmYyODkxYmNiYzU3NGY5NjI0YjBlYWQwOTlhNDE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1344128800\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1471905950 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1471905950\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-222092573 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:42:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpNY3BGVkk3NWkrV3U3TXc5RHdDdUE9PSIsInZhbHVlIjoiaUpQeFI0WmNobEtMczEza0RNTjBCemdKV3hheEZMeEhRZzZNcHhhWmZnNFBubFZpa0lLSXk3R09XVnZoTEdsOG9WYVZHaVIzZHJTajYwTTNRMkRHamdLZk1JQUR5cFpYSDN6TlFXU2dwT2FhUFgyL2haeXlhYVlOTVIvbGRsNzdjWkE3MnVLa1hRaWtKalRsWFM4KzNzV1AzM01TNTBpenQ5WDJoSVVxa3JLNmRJVWkxVGJQNldOWklsYUZzYitEdHRPV052Ylg3QUIxbEVUdTA3U1Y1YTBUbzhaU01DRDR2ZlNDdzhSRjBuUUtZVlhnQUtuVXo4cStET09YZzd3SjhGMVF0Si94Z2FYM1hVQThiUC9xVG5mcWdFdUJHL095cnZGWS9wSDBwaWdHclhNdHVVTmdONG5TZ3FkUXk4c09rWllVZ0ZqWE5wNXZTT2wwMmxnYXAzdlFMMlUxTzk3MGZMR3JsL3JPNkhuTVN6czUyZ1VMTlNpdzl4Z1hoSXFxRXVjMlp3V0lWViszRjRPRTFpU2xmOHNJdEpMdkVQcHdoK3ZJTmRyT2tRSy81YU1aM1FjYyt4dS9QT0RMU0FNT3hEU2RnUHU0RmhQMG5GMzFuQUdCMnErL05aZDQwbEY2STBCMmJua3l2dHN2cGcwSXFodUtuMVU5TndqR1FHVFMiLCJtYWMiOiJmMmUyNTRkYmI4NTU1OTI0NDQxNWVlN2NjYmIyODA2M2QzYTM0YWQ0MzI2ODZhNjJiODJkMGU0NWM0MTY0NjM4IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:42:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im12aG1CTzVTV3NPTzVUS0MreitUTnc9PSIsInZhbHVlIjoibjJhV3JrQ0drMnExZkUyUE41VllUNnI1ZjdSYlE2akVKTERPM25uelVXZWFhMzJhdTlGZkNKRFdmVk4vOFhTR2l2dCt3MFNEczV3TXhVNHBZVy93Wmp1UzJVUEMrbmpsY0dlNElDWEJRYnJONHNjZTdtdGxPODMzRG1MaHM5emJwcFJXZjBsZ0Fka3JMK3ZOa1prUHE3U0NlQ0tYd0xHeDdFa1ZsWHNoMXRBVENXR2lobWhqL0JLb0N4NDFsZXk3UjNGeHMrT2g5WTg1T0drTk9xYytZT2R0T3ozVlJ1U0wwaG1zNnc0UXZyS2Zwb3hPOXY0eEJwK2ZPdlphSjdqMEYwcCsySkFneHM5RUIvNCsyRFZHWE5wVTNnQnMvdmRWWldmOWlFZW5rbmkvWlRLT3pkRDZRYS9INE9ISkNLM0J5VUV3NVdBWkY0WGhVaUpFcGZ0eGZLak1iUk5sRnJTZ0VuVmp0VFhCeUFxV0xsNXlreHNNK29SeEdveUhOdUsxR2JIU0VwWDV6UW4xdEUzRUY2ZnNVTmE5Q3lvdHpFN2hGblA0S2lqZTFWME02cS95eGVIUHNYTVlRamZmTjZQUnlXM0R0dUtIeUh1aytnTDV6TlRBUGhTd01LYVJWN1NwUzMvajV3Q3Y0VS8zbmUzWnQ1YU5zcmd3d2dQQUJWV1YiLCJtYWMiOiI5YTdmYTdiNTcwZmY4OTRlZTlhMGE0YmYxNzJmMWMxOTNjNGMxMzI5OGZiYWMyNGM5MDhjZWZlYTEzMTc2ZTBjIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:42:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpNY3BGVkk3NWkrV3U3TXc5RHdDdUE9PSIsInZhbHVlIjoiaUpQeFI0WmNobEtMczEza0RNTjBCemdKV3hheEZMeEhRZzZNcHhhWmZnNFBubFZpa0lLSXk3R09XVnZoTEdsOG9WYVZHaVIzZHJTajYwTTNRMkRHamdLZk1JQUR5cFpYSDN6TlFXU2dwT2FhUFgyL2haeXlhYVlOTVIvbGRsNzdjWkE3MnVLa1hRaWtKalRsWFM4KzNzV1AzM01TNTBpenQ5WDJoSVVxa3JLNmRJVWkxVGJQNldOWklsYUZzYitEdHRPV052Ylg3QUIxbEVUdTA3U1Y1YTBUbzhaU01DRDR2ZlNDdzhSRjBuUUtZVlhnQUtuVXo4cStET09YZzd3SjhGMVF0Si94Z2FYM1hVQThiUC9xVG5mcWdFdUJHL095cnZGWS9wSDBwaWdHclhNdHVVTmdONG5TZ3FkUXk4c09rWllVZ0ZqWE5wNXZTT2wwMmxnYXAzdlFMMlUxTzk3MGZMR3JsL3JPNkhuTVN6czUyZ1VMTlNpdzl4Z1hoSXFxRXVjMlp3V0lWViszRjRPRTFpU2xmOHNJdEpMdkVQcHdoK3ZJTmRyT2tRSy81YU1aM1FjYyt4dS9QT0RMU0FNT3hEU2RnUHU0RmhQMG5GMzFuQUdCMnErL05aZDQwbEY2STBCMmJua3l2dHN2cGcwSXFodUtuMVU5TndqR1FHVFMiLCJtYWMiOiJmMmUyNTRkYmI4NTU1OTI0NDQxNWVlN2NjYmIyODA2M2QzYTM0YWQ0MzI2ODZhNjJiODJkMGU0NWM0MTY0NjM4IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:42:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im12aG1CTzVTV3NPTzVUS0MreitUTnc9PSIsInZhbHVlIjoibjJhV3JrQ0drMnExZkUyUE41VllUNnI1ZjdSYlE2akVKTERPM25uelVXZWFhMzJhdTlGZkNKRFdmVk4vOFhTR2l2dCt3MFNEczV3TXhVNHBZVy93Wmp1UzJVUEMrbmpsY0dlNElDWEJRYnJONHNjZTdtdGxPODMzRG1MaHM5emJwcFJXZjBsZ0Fka3JMK3ZOa1prUHE3U0NlQ0tYd0xHeDdFa1ZsWHNoMXRBVENXR2lobWhqL0JLb0N4NDFsZXk3UjNGeHMrT2g5WTg1T0drTk9xYytZT2R0T3ozVlJ1U0wwaG1zNnc0UXZyS2Zwb3hPOXY0eEJwK2ZPdlphSjdqMEYwcCsySkFneHM5RUIvNCsyRFZHWE5wVTNnQnMvdmRWWldmOWlFZW5rbmkvWlRLT3pkRDZRYS9INE9ISkNLM0J5VUV3NVdBWkY0WGhVaUpFcGZ0eGZLak1iUk5sRnJTZ0VuVmp0VFhCeUFxV0xsNXlreHNNK29SeEdveUhOdUsxR2JIU0VwWDV6UW4xdEUzRUY2ZnNVTmE5Q3lvdHpFN2hGblA0S2lqZTFWME02cS95eGVIUHNYTVlRamZmTjZQUnlXM0R0dUtIeUh1aytnTDV6TlRBUGhTd01LYVJWN1NwUzMvajV3Q3Y0VS8zbmUzWnQ1YU5zcmd3d2dQQUJWV1YiLCJtYWMiOiI5YTdmYTdiNTcwZmY4OTRlZTlhMGE0YmYxNzJmMWMxOTNjNGMxMzI5OGZiYWMyNGM5MDhjZWZlYTEzMTc2ZTBjIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:42:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-222092573\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-14564428 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2353</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1605;&#1601;&#1578;&#1575;&#1581; &#1593;&#1604;&#1576;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2353</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2354</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1588;&#1603;&#1604; &#1576;&#1591;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2354</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2355</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1575;&#1591;&#1601;&#1575;&#1604;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2355</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14564428\", {\"maxDepth\":0})</script>\n"}}