@extends('layouts.admin')

@section('page-title')
    {{ __('إدارة النقد المتقدمة') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="#">{{ __('العمليات المالية') }}</a></li>
    <li class="breadcrumb-item active">{{ __('إدارة النقد المتقدمة') }}</li>
@endsection

@push('css-page')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
    <link rel="stylesheet" href="{{ asset('css/advanced-cash-management.css') }}">
    <style>
        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            border-radius: 10px;
        }
        .filter-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 25px;
        }
        .stats-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        .stats-card.success { border-left: 5px solid #28a745; }
        .stats-card.danger { border-left: 5px solid #dc3545; }
        .stats-card.warning { border-left: 5px solid #ffc107; }
        .stats-card.info { border-left: 5px solid #17a2b8; }
        .table-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 25px;
        }
        .badge-active { background-color: #28a745; }
        .badge-closed { background-color: #6c757d; }
        .chart-container { height: 300px; }
        .alert-custom { border-radius: 10px; border: none; }
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 20px;
        }

        /* تحسينات لجدول مبيعات POS */
        .pos-sales-table .deficit-cell {
            background-color: rgba(220, 53, 69, 0.1);
        }
        .pos-sales-table .surplus-cell {
            background-color: rgba(255, 193, 7, 0.1);
        }
        .pos-sales-table .balanced-cell {
            background-color: rgba(40, 167, 69, 0.1);
        }
        .pos-sales-table .open-shift-row {
            border-left: 4px solid #ffc107;
        }

        /* تحسينات للملاحظات */
        .pos-sales-table .fa-info-circle {
            cursor: help;
        }

        /* تحسينات للتنبيهات */
        .voucher-update-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            z-index: 9999;
            display: none;
        }

        /* تحسينات للبيانات المباشرة */
        .real-time-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #28a745;
            border-radius: 50%;
            margin-left: 5px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* تحسينات للجداول */
        .live-data-table {
            position: relative;
        }

        .live-data-table::before {
            content: "بيانات مباشرة";
            position: absolute;
            top: -10px;
            right: 10px;
            background: #28a745;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 10px;
            z-index: 1;
        }
    </style>
@endpush

@section('content')
    <div class="container-fluid">
        <!-- Header Section -->
        <div class="header-section">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2><i class="fas fa-cash-register me-3"></i>{{ __('إدارة النقد المتقدمة') }}</h2>
                    <p class="mb-0">{{ __('مراقبة شاملة للشفتات وحركة النقد ومبيعات نقاط البيع') }}</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-lg" id="refreshBtn">
                        <i class="fas fa-sync-alt me-2"></i>{{ __('تحديث البيانات') }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filter-card">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label"><i class="fas fa-warehouse me-2"></i>{{ __('المستودع') }}</label>
                    <select class="form-select" id="warehouseFilter">
                        <option value="">{{ __('جميع المستودعات') }}</option>
                        @foreach($warehouses as $warehouse)
                            <option value="{{ $warehouse->id }}" {{ $warehouseId == $warehouse->id ? 'selected' : '' }}>
                                {{ $warehouse->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label"><i class="fas fa-user me-2"></i>{{ __('المستخدم') }}</label>
                    <select class="form-select" id="userFilter">
                        <option value="">{{ __('جميع المستخدمين') }}</option>
                        @foreach($users as $user)
                            <option value="{{ $user->id }}" {{ $userId == $user->id ? 'selected' : '' }}>
                                {{ $user->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label"><i class="fas fa-toggle-on me-2"></i>{{ __('حالة الشفت') }}</label>
                    <select class="form-select" id="shiftStatusFilter">
                        <option value="">{{ __('جميع الشفتات') }}</option>
                        <option value="open">{{ __('مفتوحة') }}</option>
                        <option value="closed">{{ __('مغلقة') }}</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label"><i class="fas fa-calendar me-2"></i>{{ __('فترة التاريخ') }}</label>
                    <input type="text" class="form-control" id="daterange" value="{{ $startDate }} - {{ $endDate }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button class="btn btn-primary w-100" id="applyFilters">
                        <i class="fas fa-search me-2"></i>{{ __('بحث') }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row" id="quickStats">
            <div class="col-md-2">
                <div class="stats-card success">
                    <h3 class="text-success" id="dailyReceipts">0.00</h3>
                    <p class="mb-0">{{ __('إجمالي المقبوضات') }}</p>
                    <small class="text-muted">{{ __('ريال سعودي') }}</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card danger">
                    <h3 class="text-danger" id="dailyPayments">0.00</h3>
                    <p class="mb-0">{{ __('إجمالي المصروفات') }}</p>
                    <small class="text-muted">{{ __('ريال سعودي') }}</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card info">
                    <h3 class="text-info" id="netCash">0.00</h3>
                    <p class="mb-0">{{ __('صافي النقد') }}</p>
                    <small class="text-muted">{{ __('ريال سعودي') }}</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card warning">
                    <h3 class="text-warning" id="openShifts">0</h3>
                    <p class="mb-0">{{ __('شفتات مفتوحة') }}</p>
                    <small class="text-muted">{{ __('تحتاج مراجعة') }}</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card warning">
                    <h3 class="text-warning" id="deferredAmount">0.00</h3>
                    <p class="mb-0">{{ __('المبالغ الآجلة') }}</p>
                    <small class="text-muted">{{ __('دفعات مؤجلة') }}</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card info">
                    <h3 class="text-info" id="activeWarehouses">0</h3>
                    <p class="mb-0">{{ __('المستودعات النشطة') }}</p>
                    <small class="text-muted">{{ __('لها شفتات مفتوحة') }}</small>
                </div>
            </div>
        </div>

        <!-- Alerts Section -->
        <div id="alertsContainer"></div>

        <!-- Main Content Tabs -->
        <div class="table-card">
            <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="shifts-tab" data-bs-toggle="tab" data-bs-target="#shifts" type="button">
                        <i class="fas fa-clock me-2"></i>{{ __('الشفتات') }}
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="receipts-tab" data-bs-toggle="tab" data-bs-target="#receipts" type="button">
                        <i class="fas fa-arrow-down me-2"></i>{{ __('سندات القبض') }}
                        <span class="real-time-indicator" title="بيانات مباشرة - تحديث كل 30 ثانية"></span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="payments-tab" data-bs-toggle="tab" data-bs-target="#payments" type="button">
                        <i class="fas fa-arrow-up me-2"></i>{{ __('سندات الصرف') }}
                        <span class="real-time-indicator" title="بيانات مباشرة - تحديث كل 30 ثانية"></span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="pos-tab" data-bs-toggle="tab" data-bs-target="#pos" type="button">
                        <i class="fas fa-cash-register me-2"></i>{{ __('مبيعات POS') }}
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="mainTabsContent">
                <!-- Shifts Tab -->
                <div class="tab-pane fade show active" id="shifts" role="tabpanel">
                    <div class="loading-spinner" id="shiftsLoading">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p>{{ __('جاري تحميل البيانات...') }}</p>
                    </div>
                    <div class="table-responsive mt-3" id="shiftsTable" style="display: none;">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ __('المستودع') }}</th>
                                    <th>{{ __('المستخدم') }}</th>
                                    <th>{{ __('وقت البدء') }}</th>
                                    <th>{{ __('وقت الانتهاء') }}</th>
                                    <th>{{ __('الرصيد الافتتاحي') }}</th>
                                    <th>{{ __('النقد الحالي') }}</th>
                                    <th>{{ __('النقد عبر الشبكة') }}</th>
                                    <th>{{ __('إجمالي النقد') }}</th>
                                    <th>{{ __('العجز') }}</th>
                                    <th>{{ __('الحالة') }}</th>
                                    <th>{{ __('الإجراءات') }}</th>
                                </tr>
                            </thead>
                            <tbody id="shiftsTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Receipt Vouchers Tab -->
                <div class="tab-pane fade" id="receipts" role="tabpanel">
                    <div class="loading-spinner" id="receiptsLoading">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p>{{ __('جاري تحميل البيانات...') }}</p>
                    </div>
                    <div class="table-responsive mt-3 live-data-table" id="receiptsTable" style="display: none;">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ __('رقم السند') }}</th>
                                    <th>{{ __('التاريخ') }}</th>
                                    <th>{{ __('المستلم من') }}</th>
                                    <th>{{ __('القيمة') }}</th>
                                    <th>{{ __('النوع') }}</th>
                                    <th>{{ __('الغرض') }}</th>
                                    <th>{{ __('المستودع') }}</th>
                                    <th>{{ __('الحالة') }}</th>
                                    <th>{{ __('الإجراءات') }}</th>
                                </tr>
                            </thead>
                            <tbody id="receiptsTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Payment Vouchers Tab -->
                <div class="tab-pane fade" id="payments" role="tabpanel">
                    <div class="loading-spinner" id="paymentsLoading">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p>{{ __('جاري تحميل البيانات...') }}</p>
                    </div>
                    <div class="table-responsive mt-3 live-data-table" id="paymentsTable" style="display: none;">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ __('رقم السند') }}</th>
                                    <th>{{ __('التاريخ') }}</th>
                                    <th>{{ __('المدفوع إلى') }}</th>
                                    <th>{{ __('القيمة') }}</th>
                                    <th>{{ __('النوع') }}</th>
                                    <th>{{ __('الغرض') }}</th>
                                    <th>{{ __('المستودع') }}</th>
                                    <th>{{ __('الحالة') }}</th>
                                    <th>{{ __('الإجراءات') }}</th>
                                </tr>
                            </thead>
                            <tbody id="paymentsTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- POS Sales Tab -->
                <div class="tab-pane fade" id="pos" role="tabpanel">
                    <div class="row mt-3">
                        <div class="col-md-8">
                            <div class="loading-spinner" id="posLoading">
                                <i class="fas fa-spinner fa-spin fa-2x"></i>
                                <p>{{ __('جاري تحميل البيانات...') }}</p>
                            </div>
                            <div class="table-responsive" id="posTable" style="display: none;">
                                <table class="table table-hover pos-sales-table">
                                    <thead>
                                        <tr>
                                            <th>{{ __('التاريخ') }}</th>
                                            <th>{{ __('المستخدم') }}</th>
                                            <th>{{ __('المستودع') }}</th>
                                            <th>{{ __('عدد الفواتير') }}</th>
                                            <th>{{ __('إجمالي المبيعات') }}</th>
                                            <th>{{ __('إجمالي المحصل') }}</th>
                                            <th>{{ __('العجز/الفائض') }}</th>
                                            <th>{{ __('حالة الشفت') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody id="posTableBody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="chart-container">
                                <canvas id="salesChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script src="{{ asset('js/advanced-cash-management.js') }}"></script>
@endpush
