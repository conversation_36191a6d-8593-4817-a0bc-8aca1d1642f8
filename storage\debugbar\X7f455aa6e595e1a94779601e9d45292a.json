{"__meta": {"id": "X7f455aa6e595e1a94779601e9d45292a", "datetime": "2025-06-30 23:08:14", "utime": **********.595717, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.167569, "end": **********.595737, "duration": 0.42816805839538574, "duration_str": "428ms", "measures": [{"label": "Booting", "start": **********.167569, "relative_start": 0, "end": **********.529058, "relative_end": **********.529058, "duration": 0.36148905754089355, "duration_str": "361ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.529067, "relative_start": 0.3614981174468994, "end": **********.595739, "relative_end": 1.9073486328125e-06, "duration": 0.06667184829711914, "duration_str": "66.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45706936, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00297, "accumulated_duration_str": "2.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.557578, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.38}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.568931, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.38, "width_percent": 15.152}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.575698, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.532, "width_percent": 13.468}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1769269459 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1769269459\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-279303289 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-279303289\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-886818857 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-886818857\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-689756178 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751324892483%7C7%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNLZU11cy94K2xGdURUZlQ2bmNITkE9PSIsInZhbHVlIjoiUExPVXptR2JJM2U0YWFpMk50MjVNSFFwZ0x3bjVKT20zREVMNGRJRHFXQlBGaHN4M2VRY3FrcURVa25UdFNPSFJPRlI2UGJGZmJZVU51bFNBbEtFSDJyR2RDamZ1NE1qN0ZaQkkxUXFJT2x5M2ZNQXZxMHY2R2g4NGZMUGtucGQyeXd0Si9HQjZNR05ZenBYc1lneEtXK3lNM2hHdkc4NGt6Y3JCV1o1NWpRWlpVRUJ2cHhJQW1vQnNsOEtVYnVBcHFIL3UreCswQ3owYlczREFqdE5WdDJtMDZWb3N4R0t2TXlpODJxbjJPK3NQSzlzWDF1RFlCM20vZHB2T3o0MURYaGFKTFh1QW1RV3hoUkdDNkZpTzQrSzRENjdJSmhhL0tFdE5OZ1ltaGR3dmpMdldWdng4bjF3RFV2NUJlbDRDMDlQcU5NV0xVZHdvQXlKZVY0K1kvZUQycWtJWklUdUptYVYraTlRZzFjT2ZXUVB0Y1N6Smt1QXhQVTF3WFdJR1B4NTJucmk0RlVkVExsdUpMOGJRMFkvN0psRXk2elhsd2t0WE5ZaGprMmNnbGc1R1d2OEYzVVIzdnBaQnNGeGZEZ0M1Zm5KUWZRZ0NmK0dwelhPU3E0aDV5cStSd3d1M2ZMVGZ1VHRwaWZENUJmVGliTGdUMmtrQ0M3d3NxZk8iLCJtYWMiOiJlNWFmOGZkNGVjNTE2YjA1NWFmYjJlM2UxNWViODE1YTI4MjI0NzZiMzA2ZmZjNDA3NGU1Nzk2YzYwYjhjMmZkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjNXNDFXaUsrSHBSeHBvclFiTnlaMmc9PSIsInZhbHVlIjoiV21JZjdMUGd1UnFHTkVhL2t3Rm1xTnpEb3FKNENJZHJ3WTJNcFB0di9mendockFpUS91VS82RXRMZkhaRENsRHpCVzM1NDZBVFluR05LOURQd1FuVHJxMUMvMU5ROWlYZWVWVlJtTUJUU0Z6UkE5YWRDWEFpNkhraEVOeTB3WWV1UHBQMVpnYjQ0TndGN29IOFJCTHhUZ1oyb2o0THFSeVNVbm5OK3lkKzJYbHZNMjFrYVBpWXd6OWNtdEtBV1ErczBEVHhCOUFrM3JucWdtdFJ2OTYxUUlhN3I2elJPUkdabDlqam1FMkt5V3pSUExPanFDQlQ5dk5sV2VMMWNnWXJTaE9aT3dDVkpIUldoNkJtQWtGOWk0Y0FTYTFUbGp1Q0Y5YXozQkUrYWJ1R1RhUGF5K0UwNXMwZVJBSXh1Ni92anBNZ1V1ejYzbjZQNnFwZjRnb0JHaVRiYXpvN2JFQXVOS2Rzb2FBRktqT3EwbXMrOEtkRU9JTTBXRUdteWdWaXBsU1oyZFI4RDRZa1VkZExPTGxYOU1MOGk5TTJpd3o2TnVTTllVQVRVWjN6SnBvR0dMRzNVVVFSUE5IQ25JODBmL0FkNEpyckdWTUxPNkNCdkZpdlhLQ0twN0tWOHlFV09uR09GUWFvM3BKTUZxSkZPYWNRcElrV1NxOTJRTTMiLCJtYWMiOiI0Y2FhYjE0MjkwOTRmY2MyOGY1MTYxZjE3MTQxNTdiN2YwZjZhNjI4ODliODg4MDNjMGI4NmZjNDIyOGRkNDhkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-689756178\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2080379904 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2080379904\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1993441716 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:08:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imd1RTNpRkxBaVAySmpidzR1OUF5aUE9PSIsInZhbHVlIjoialdTTC85RFhNMmdpVEhLWDNqdHBVeVpjTUlQODY1RUhrMC8zQnhRZC83S1M0eGh3a3B2Tk9TR1IrR256SG8xM1IxY2ZjUUpTWkMvQUZzdzRDQkRzaWs4S2VqYWR0emxrK3hBT0ZEcUxmRW9HMWlyMjRIK2dMaDRkNkh2M1ZtQll6N3EzZTQ2ZG9KNzkxTFlWUWMwSTZEeGZ0bTNnUFB5R1N4TkorUXZDcU4xZU13NjB6ZGNWYVdKRVg5c3NmbktLcmtoZ29FbnV1WXlaRll2d0RiREhpMG5VR0RBVDBQZkpQazU0OEZoazRLUnVUSy9qWld1eTBPaVd4cTFCZEM0dC9DK1QrZGRNaThYV2k2T2U5SWdmd0xrMkl6N1JEMVlIcWlVN09IQVRINXRiN1VOc2U0SnFEQ3VEOC9jRzY3ck5MdkxpZ014aVJ0TVlqUXkrY2RVOE1JVHBBc0ZDcG15bjlMenFwSGVtSEM1b2tubVVaNTR3M0owOVh5MDhsZUlQZ3hpV25qbFRoSWcxdVcxQUU0bHlmNzRXUHhpYUg5Uk0yL0pld2JHKzkwZ0V4RzVlVTNBb2lEUUtOVWtqK2M1emdIUEtJcUVyc2lVWnA1TG53ZU1vT0d1Q0tHSE1iYmc4RlVHNVBYam1KdmFqdkVtTC94dFY5YlJwaEV0cFFSWHkiLCJtYWMiOiJjZTgxODJlZjYxY2VmYmE2NWE2NDhkNzViODg4NjU5Y2FmMTQ5MGRhZWViNTUwNjdiN2NhYjUyYmQyZjNhNjU5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:08:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ilo2bVZtcG14WDZSS29kdG02NVNLQnc9PSIsInZhbHVlIjoiMkc4MnQ2K3dJdlR0d2E5Z3dYT2J6L2I4MkMxRkxWdE4vTE14Q0M4U2tjWWNqeHZFeE9Sc29Xak41TjBhaFRYVzFMZ3VwN3N1SkpqNklKVWhRcGM4U0dIOVFvT2VBVzduSEZ6Z255Q3ZnSzRWUUxKY0ZnUzdiZW9mRlJHcm1HcU9tZWNwbUlYWGNlZm9wenFGT1o4cDZqSEdGbHBMeHBoVHMvOWhQKzhQQ1F6WFIzc3h0TkNBWjlacU5oMEtodWo4NFBYb0RmaVNkYUJHbDJ4cG5Td2xCRFM0VUtBdkkrUms4UW5LUDdnbkdFUmMvbFd1bkJRYnVzVEVKY1EwREhkV2M5QVRRaEZsSS9seDlMM1RTeTMyTTFLN29DcGpEMW9tYW90U3AxSGkvZ0o3RGhrLzhOMnVkSCsrVGVSc3lOYjE4aksvdFZ2RlZZdE94VC9OU3RXVG1NcFNKNTN4NTc0empMZUdXYU5JY2hnWmJTNGNBMkZNZ0d5MGFKUVc4YTdBem9QYU1xc1pDWjRMbHRtRnZxM1c4Q0c5VGZDcXpJWUx3b3hjUmordjJQT2ZoNWtMYkxNODBOUE1DeVFmS2QycURSWFJGOFNmM1FnMzQ2cVpYNUlNUWM1ZnZ5RW5DazFiZWNBOE1pK051YXNsUlljV3F0TDcySzBGQVlsejBzT1ciLCJtYWMiOiIyNDAwZmEyZGU4YjdjODgwYjNkNjEzNTdhZjJiMjA0NTUwZTNjNmFlY2I1OGRlNzJmZTkwNzhiMDY0MDQwYzg4IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:08:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imd1RTNpRkxBaVAySmpidzR1OUF5aUE9PSIsInZhbHVlIjoialdTTC85RFhNMmdpVEhLWDNqdHBVeVpjTUlQODY1RUhrMC8zQnhRZC83S1M0eGh3a3B2Tk9TR1IrR256SG8xM1IxY2ZjUUpTWkMvQUZzdzRDQkRzaWs4S2VqYWR0emxrK3hBT0ZEcUxmRW9HMWlyMjRIK2dMaDRkNkh2M1ZtQll6N3EzZTQ2ZG9KNzkxTFlWUWMwSTZEeGZ0bTNnUFB5R1N4TkorUXZDcU4xZU13NjB6ZGNWYVdKRVg5c3NmbktLcmtoZ29FbnV1WXlaRll2d0RiREhpMG5VR0RBVDBQZkpQazU0OEZoazRLUnVUSy9qWld1eTBPaVd4cTFCZEM0dC9DK1QrZGRNaThYV2k2T2U5SWdmd0xrMkl6N1JEMVlIcWlVN09IQVRINXRiN1VOc2U0SnFEQ3VEOC9jRzY3ck5MdkxpZ014aVJ0TVlqUXkrY2RVOE1JVHBBc0ZDcG15bjlMenFwSGVtSEM1b2tubVVaNTR3M0owOVh5MDhsZUlQZ3hpV25qbFRoSWcxdVcxQUU0bHlmNzRXUHhpYUg5Uk0yL0pld2JHKzkwZ0V4RzVlVTNBb2lEUUtOVWtqK2M1emdIUEtJcUVyc2lVWnA1TG53ZU1vT0d1Q0tHSE1iYmc4RlVHNVBYam1KdmFqdkVtTC94dFY5YlJwaEV0cFFSWHkiLCJtYWMiOiJjZTgxODJlZjYxY2VmYmE2NWE2NDhkNzViODg4NjU5Y2FmMTQ5MGRhZWViNTUwNjdiN2NhYjUyYmQyZjNhNjU5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:08:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ilo2bVZtcG14WDZSS29kdG02NVNLQnc9PSIsInZhbHVlIjoiMkc4MnQ2K3dJdlR0d2E5Z3dYT2J6L2I4MkMxRkxWdE4vTE14Q0M4U2tjWWNqeHZFeE9Sc29Xak41TjBhaFRYVzFMZ3VwN3N1SkpqNklKVWhRcGM4U0dIOVFvT2VBVzduSEZ6Z255Q3ZnSzRWUUxKY0ZnUzdiZW9mRlJHcm1HcU9tZWNwbUlYWGNlZm9wenFGT1o4cDZqSEdGbHBMeHBoVHMvOWhQKzhQQ1F6WFIzc3h0TkNBWjlacU5oMEtodWo4NFBYb0RmaVNkYUJHbDJ4cG5Td2xCRFM0VUtBdkkrUms4UW5LUDdnbkdFUmMvbFd1bkJRYnVzVEVKY1EwREhkV2M5QVRRaEZsSS9seDlMM1RTeTMyTTFLN29DcGpEMW9tYW90U3AxSGkvZ0o3RGhrLzhOMnVkSCsrVGVSc3lOYjE4aksvdFZ2RlZZdE94VC9OU3RXVG1NcFNKNTN4NTc0empMZUdXYU5JY2hnWmJTNGNBMkZNZ0d5MGFKUVc4YTdBem9QYU1xc1pDWjRMbHRtRnZxM1c4Q0c5VGZDcXpJWUx3b3hjUmordjJQT2ZoNWtMYkxNODBOUE1DeVFmS2QycURSWFJGOFNmM1FnMzQ2cVpYNUlNUWM1ZnZ5RW5DazFiZWNBOE1pK051YXNsUlljV3F0TDcySzBGQVlsejBzT1ciLCJtYWMiOiIyNDAwZmEyZGU4YjdjODgwYjNkNjEzNTdhZjJiMjA0NTUwZTNjNmFlY2I1OGRlNzJmZTkwNzhiMDY0MDQwYzg4IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:08:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1993441716\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1422931310 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1422931310\", {\"maxDepth\":0})</script>\n"}}