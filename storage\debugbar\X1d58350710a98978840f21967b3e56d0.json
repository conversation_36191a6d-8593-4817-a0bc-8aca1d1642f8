{"__meta": {"id": "X1d58350710a98978840f21967b3e56d0", "datetime": "2025-06-30 23:11:42", "utime": **********.906793, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.459488, "end": **********.906812, "duration": 0.4473240375518799, "duration_str": "447ms", "measures": [{"label": "Booting", "start": **********.459488, "relative_start": 0, "end": **********.854545, "relative_end": **********.854545, "duration": 0.39505720138549805, "duration_str": "395ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.854558, "relative_start": 0.39507007598876953, "end": **********.906814, "relative_end": 2.1457672119140625e-06, "duration": 0.052256107330322266, "duration_str": "52.26ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45028616, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00314, "accumulated_duration_str": "3.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.882718, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 53.822}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.892868, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 53.822, "width_percent": 25.478}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.89924, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.299, "width_percent": 20.701}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1629709594 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1629709594\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1249654346 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1249654346\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1375571259 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1375571259\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1745300718 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325098367%7C18%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlB4S1dWRkx0WmVrUjFDNWl6RDdiK0E9PSIsInZhbHVlIjoia3I3eU9kNVlQQ3owb2NNa2huVzk0ZzRHMnIvdWpOUSs2RW5BZyt4NEY1bFp0RUJHeTBhdFRjVUVjbG1ZUWMrVC9WNFpWSC9mUGZIbm44cjRuN1hMSDUxNSsycklKOGM3TDZiaC9NZzRMU3hQMkxEc1d6VDF2SWhSZjBuUGVodi9GNjhaQ1NpNFJyaEFEME54Y295ZkdsL3dPTXdFUjVkekNSK3R1UnBvMFhHZ1ozWjhWa3dKWEE4aTVmdWJDWnZ1c3NnbkJZVDZDY1picHBROS9YWFdlZE9weVlwcVNXc2dmM0dFU2tUeG1ubHJtL2RnVkp1Y1VwUmIvQTB0Q1RFazhpaXgzL25PREx2eHhuQTQvR3lwdHBtaUlIQkoxelJpcW9iN1BOZUU3MzlQU0FmYURyVmpnMjZsSjc0cE9FZ1J2ZWNYeXB1WVdPQWNTeEtpWUQ1VjVwdmFpL2ZRRklFYWVkeTMxSTduZEJXVTNuYzZKK2lZNXZvalFlc2J2ZURpQUlPeC9LWTZKbWRQMlZ3akptQ2pBa20yS1ZqSkMzZDhFdTVrU2V4V3dNS2RoQ09aZ2RIMUhDcTNxS2NKQVRRS0NTWEMzQjFKd2lpN05WR05JcDczNmpMUGhTN2tXMXBJY3Y3anpMMTB1c3ZlOHBBNW9vOEw4UTlBczMwQmpYZmsiLCJtYWMiOiJjYjZmN2YxNWFjNTQ1YmViYjNkMzgxOTY2NjU0N2NjYjcyNDMwMGNlNDNkZjY2YjZhZGVmZWNhMTJmY2MzMDMzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjdDUWhBb1ZidzVkVS8yYlFKN1N5Znc9PSIsInZhbHVlIjoiNGJmU0MrRUtyNGZiUms1RnE2bGZKdWNmSzhwd3hseHBkYkVDTFIvZXloaGtVelFJa04rVitTWXExVGVlQ3dVeEdCVkJ0cHM5L2JWN0ZqVW9DdG9CNFF2M2NUWVc2ckY0WHRDaEk0Y2daa2hhM2llbUdlcWI2Yi9rL1NzYW5uRk0wbjlzSXNwU0I0NkNxVHdxNlJPY3NGcDRHS1hsTzRISzBXVUVCTjdDTUJkVDY4U0pFcmYvOTdsSXp4UUlTOTVYTVl5Z1gzUTZZalNwSC9VbXBEaGs3cXRiand3OW9tUVRjNE9jSmVqSHVFbGo1d1loNWNTVDNqSk9mSkMrMjhuMGhnSzh2YnArU0ZmUXNMZE0yTmhkYng0Sm1PYm1VNXdaVUUzN1BlUWRndDB1dlN4U2taMWtnVVlzdW9BWHlMUU9RMGJMcXNNV2lhQkZXV1RsTkpBM1NqUk5HVjhsbEFYZEVxanhWZnErRGF3VEVyNzMwVFlRM2k4UDUvSWN5OXFvcWxtZTR0YW9Kb0ozaFdmWW1WdVlQWWVldkFnQUtaVWdFcG9MeFl4eTU2YkV2ckdiREYwT3h2eFczRmlSMjAxVnJ2ZEVHb1dTMVhON3B4elVUL2pmR3BiNTJPQTNDMmJ5SlhmemhVMEZ4KzNUbmhSOGZZK1N4TVlYWXljS1pSak4iLCJtYWMiOiJlZWRkOTlmMzc0OTRhZDgzMDY3YzI5MzE0NWJhZDJjNjkwY2Q3MDU1MjMxN2VlMDA2MDVjMjdhNGViNzYwNDM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1745300718\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1938645639 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0M1K0sdNadPD2LVrnt8LmBw227nA9F1U5e3ROaJK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1938645639\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2080868097 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:11:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijd5VnI0NUUzSmFQdWdMdDhjejZMNmc9PSIsInZhbHVlIjoiemswZDF2R3IyYU9hVTRhYkJhNURmb1RmOW9sd0xsUXhpT0FnRUZLUWVYaTAwNi92NC9RaitXd3dnOGgzN0M5ZHpmZjNSMXRBTnk5U1ZIOUplNU1YcS9SaE41SXRhYkk2bmtuNERsWENQdVRBUUszelhFdVFEdmhDYjdrbklvbEtIQU5vMENBWSt0R052MHo1UWU5OUZmTU00MDBBcVoyOVdEYnptdzRmRXJVS3NvQmRqRjI3OTl5MlR4MnliQWxCMXRzMWttcHZ0RGIzdUtGV1A2S3BxcXFEMEE3MGtvcDJTMHk0MHYwYm91VEZneEtLWnU5VVVMeEtVZGhENDBFNUc3MjM3RFM0M1hPTloyME8wZ2RrUHlIQS9BRGo3dVNKMnZhMWtjcGVlZTltNkhTaERxZWQ2TVpGMmdxUG4wSXoxUStwTzd3N215Nll4aUg5ZEc4TnB1bnIxemFNQlRTa1IvUGRNaVFidEw0cXJNejc2SThlKzdmU0Irc2Rzdzc5R1h5emo1RERFbENQUjFOc216YW15dzhjZ3UrQUNLK21DTzU5OFZUOU9SRzdiNkFoQWozZ21LN2FZbVhCNkJNTGNZMEs4U0o5MXRwRjBzbnh0Zk1xQ2ZsOElTWnZTeXpmTXhQREhWK0g4QmQ4MFg0bTQvOGpUMFNDTzNmaFk4eUsiLCJtYWMiOiI3OTEwM2ZmYzYyNzUxY2YxYmVmNzRjNzI2MzM1MWI0YTkyOWUyYzhhZWVmMTdlNTRiYTQyNWZkMGFlNDRiNzcwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:11:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkV4MDlMOGF5WFRnU2Z5YVBRQnRzK2c9PSIsInZhbHVlIjoiMGNKampZS1VCVndBMk9Ua1crc3dWcHhlb1U2bFhsb1F4LzJEb08wM05qc3IwRWZsblhyRk5yQUNtdFZ0NTB6V09HczM2T1drUFFjcTk1MDNqbXFWVTBXQjJCaXB4K3JFNFMzQWFTQzNVRXEvaFdHMUpSZEwrS2lKVWFZc2VtZEQ4TXhZTHFJamhENG05UzNsRjlIdTNMbFIyNElZNmVvdExDWkQwOUhqRC9wczd4ODNsa24rdEZRKzhQMkFBeHJINU9ZM1VENjN6QlBSRFVEVjBQWm5tRVpoOUN2ZERmaGUzUXVydFNoV2U2a09QbjFBMEh3cndwU2ZhQmdaODlHdUE5bjRuRHVteWgwTlVjN0NGV1JPaVNyaEFpUEg4V0FjUVlBa1REWE80Z2Vza1NVNGlkR1BTOUVpdjFvc1FRZ3Z6M2ZURVBNNUJ5c2J4ekJXdDVqV0Q3N3dSK3BPUjZWelEycE51Zi9yL0tMM09qZDRJdlV4eU9pc0NrNk1POUlHVTF0MmZlYWxFWDBlb3NhT3IzQXRDOHJUTEgycUtxNE94R2FxbXNpeG9Rd3h4UUhWUmVtMS83VHV6b045ZWd6OHhDK0RYQ29DSzNGMEZsc0JLdUg0OEowZ0gvMmpyV0REdEYwUTMxeDZNeFltV1g0a2lveEUxeTkvSjJnWjZlZXkiLCJtYWMiOiJkYzA3ZmI2MzBhMDJlOTU1MGZkMjBlZTA1ZDQwYTk4YTM2ZGE0NThmYTdkOWY3ZGMwMTNhNjQ4NjVkOTRmMzRjIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:11:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijd5VnI0NUUzSmFQdWdMdDhjejZMNmc9PSIsInZhbHVlIjoiemswZDF2R3IyYU9hVTRhYkJhNURmb1RmOW9sd0xsUXhpT0FnRUZLUWVYaTAwNi92NC9RaitXd3dnOGgzN0M5ZHpmZjNSMXRBTnk5U1ZIOUplNU1YcS9SaE41SXRhYkk2bmtuNERsWENQdVRBUUszelhFdVFEdmhDYjdrbklvbEtIQU5vMENBWSt0R052MHo1UWU5OUZmTU00MDBBcVoyOVdEYnptdzRmRXJVS3NvQmRqRjI3OTl5MlR4MnliQWxCMXRzMWttcHZ0RGIzdUtGV1A2S3BxcXFEMEE3MGtvcDJTMHk0MHYwYm91VEZneEtLWnU5VVVMeEtVZGhENDBFNUc3MjM3RFM0M1hPTloyME8wZ2RrUHlIQS9BRGo3dVNKMnZhMWtjcGVlZTltNkhTaERxZWQ2TVpGMmdxUG4wSXoxUStwTzd3N215Nll4aUg5ZEc4TnB1bnIxemFNQlRTa1IvUGRNaVFidEw0cXJNejc2SThlKzdmU0Irc2Rzdzc5R1h5emo1RERFbENQUjFOc216YW15dzhjZ3UrQUNLK21DTzU5OFZUOU9SRzdiNkFoQWozZ21LN2FZbVhCNkJNTGNZMEs4U0o5MXRwRjBzbnh0Zk1xQ2ZsOElTWnZTeXpmTXhQREhWK0g4QmQ4MFg0bTQvOGpUMFNDTzNmaFk4eUsiLCJtYWMiOiI3OTEwM2ZmYzYyNzUxY2YxYmVmNzRjNzI2MzM1MWI0YTkyOWUyYzhhZWVmMTdlNTRiYTQyNWZkMGFlNDRiNzcwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:11:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkV4MDlMOGF5WFRnU2Z5YVBRQnRzK2c9PSIsInZhbHVlIjoiMGNKampZS1VCVndBMk9Ua1crc3dWcHhlb1U2bFhsb1F4LzJEb08wM05qc3IwRWZsblhyRk5yQUNtdFZ0NTB6V09HczM2T1drUFFjcTk1MDNqbXFWVTBXQjJCaXB4K3JFNFMzQWFTQzNVRXEvaFdHMUpSZEwrS2lKVWFZc2VtZEQ4TXhZTHFJamhENG05UzNsRjlIdTNMbFIyNElZNmVvdExDWkQwOUhqRC9wczd4ODNsa24rdEZRKzhQMkFBeHJINU9ZM1VENjN6QlBSRFVEVjBQWm5tRVpoOUN2ZERmaGUzUXVydFNoV2U2a09QbjFBMEh3cndwU2ZhQmdaODlHdUE5bjRuRHVteWgwTlVjN0NGV1JPaVNyaEFpUEg4V0FjUVlBa1REWE80Z2Vza1NVNGlkR1BTOUVpdjFvc1FRZ3Z6M2ZURVBNNUJ5c2J4ekJXdDVqV0Q3N3dSK3BPUjZWelEycE51Zi9yL0tMM09qZDRJdlV4eU9pc0NrNk1POUlHVTF0MmZlYWxFWDBlb3NhT3IzQXRDOHJUTEgycUtxNE94R2FxbXNpeG9Rd3h4UUhWUmVtMS83VHV6b045ZWd6OHhDK0RYQ29DSzNGMEZsc0JLdUg0OEowZ0gvMmpyV0REdEYwUTMxeDZNeFltV1g0a2lveEUxeTkvSjJnWjZlZXkiLCJtYWMiOiJkYzA3ZmI2MzBhMDJlOTU1MGZkMjBlZTA1ZDQwYTk4YTM2ZGE0NThmYTdkOWY3ZGMwMTNhNjQ4NjVkOTRmMzRjIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:11:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2080868097\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1035246151 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1035246151\", {\"maxDepth\":0})</script>\n"}}