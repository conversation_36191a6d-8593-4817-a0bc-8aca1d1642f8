<?php
// This file was auto-generated from sdk-root/src/data/datasync/2018-11-09/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-11-09', 'endpointPrefix' => 'datasync', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceAbbreviation' => 'DataSync', 'serviceFullName' => 'AWS DataSync', 'serviceId' => 'DataSync', 'signatureVersion' => 'v4', 'signingName' => 'datasync', 'targetPrefix' => 'FmrsService', 'uid' => 'datasync-2018-11-09', ], 'operations' => [ 'AddStorageSystem' => [ 'name' => 'AddStorageSystem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddStorageSystemRequest', ], 'output' => [ 'shape' => 'AddStorageSystemResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], 'endpoint' => [ 'hostPrefix' => 'discovery-', ], ], 'CancelTaskExecution' => [ 'name' => 'CancelTaskExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelTaskExecutionRequest', ], 'output' => [ 'shape' => 'CancelTaskExecutionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'CreateAgent' => [ 'name' => 'CreateAgent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAgentRequest', ], 'output' => [ 'shape' => 'CreateAgentResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'CreateLocationAzureBlob' => [ 'name' => 'CreateLocationAzureBlob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLocationAzureBlobRequest', ], 'output' => [ 'shape' => 'CreateLocationAzureBlobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'CreateLocationEfs' => [ 'name' => 'CreateLocationEfs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLocationEfsRequest', ], 'output' => [ 'shape' => 'CreateLocationEfsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'CreateLocationFsxLustre' => [ 'name' => 'CreateLocationFsxLustre', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLocationFsxLustreRequest', ], 'output' => [ 'shape' => 'CreateLocationFsxLustreResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'CreateLocationFsxOntap' => [ 'name' => 'CreateLocationFsxOntap', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLocationFsxOntapRequest', ], 'output' => [ 'shape' => 'CreateLocationFsxOntapResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'CreateLocationFsxOpenZfs' => [ 'name' => 'CreateLocationFsxOpenZfs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLocationFsxOpenZfsRequest', ], 'output' => [ 'shape' => 'CreateLocationFsxOpenZfsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'CreateLocationFsxWindows' => [ 'name' => 'CreateLocationFsxWindows', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLocationFsxWindowsRequest', ], 'output' => [ 'shape' => 'CreateLocationFsxWindowsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'CreateLocationHdfs' => [ 'name' => 'CreateLocationHdfs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLocationHdfsRequest', ], 'output' => [ 'shape' => 'CreateLocationHdfsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'CreateLocationNfs' => [ 'name' => 'CreateLocationNfs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLocationNfsRequest', ], 'output' => [ 'shape' => 'CreateLocationNfsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'CreateLocationObjectStorage' => [ 'name' => 'CreateLocationObjectStorage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLocationObjectStorageRequest', ], 'output' => [ 'shape' => 'CreateLocationObjectStorageResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'CreateLocationS3' => [ 'name' => 'CreateLocationS3', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLocationS3Request', ], 'output' => [ 'shape' => 'CreateLocationS3Response', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'CreateLocationSmb' => [ 'name' => 'CreateLocationSmb', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLocationSmbRequest', ], 'output' => [ 'shape' => 'CreateLocationSmbResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'CreateTask' => [ 'name' => 'CreateTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTaskRequest', ], 'output' => [ 'shape' => 'CreateTaskResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'DeleteAgent' => [ 'name' => 'DeleteAgent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAgentRequest', ], 'output' => [ 'shape' => 'DeleteAgentResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'DeleteLocation' => [ 'name' => 'DeleteLocation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLocationRequest', ], 'output' => [ 'shape' => 'DeleteLocationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'DeleteTask' => [ 'name' => 'DeleteTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTaskRequest', ], 'output' => [ 'shape' => 'DeleteTaskResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeAgent' => [ 'name' => 'DescribeAgent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAgentRequest', ], 'output' => [ 'shape' => 'DescribeAgentResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeDiscoveryJob' => [ 'name' => 'DescribeDiscoveryJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDiscoveryJobRequest', ], 'output' => [ 'shape' => 'DescribeDiscoveryJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], 'endpoint' => [ 'hostPrefix' => 'discovery-', ], ], 'DescribeLocationAzureBlob' => [ 'name' => 'DescribeLocationAzureBlob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLocationAzureBlobRequest', ], 'output' => [ 'shape' => 'DescribeLocationAzureBlobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeLocationEfs' => [ 'name' => 'DescribeLocationEfs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLocationEfsRequest', ], 'output' => [ 'shape' => 'DescribeLocationEfsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeLocationFsxLustre' => [ 'name' => 'DescribeLocationFsxLustre', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLocationFsxLustreRequest', ], 'output' => [ 'shape' => 'DescribeLocationFsxLustreResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeLocationFsxOntap' => [ 'name' => 'DescribeLocationFsxOntap', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLocationFsxOntapRequest', ], 'output' => [ 'shape' => 'DescribeLocationFsxOntapResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeLocationFsxOpenZfs' => [ 'name' => 'DescribeLocationFsxOpenZfs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLocationFsxOpenZfsRequest', ], 'output' => [ 'shape' => 'DescribeLocationFsxOpenZfsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeLocationFsxWindows' => [ 'name' => 'DescribeLocationFsxWindows', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLocationFsxWindowsRequest', ], 'output' => [ 'shape' => 'DescribeLocationFsxWindowsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeLocationHdfs' => [ 'name' => 'DescribeLocationHdfs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLocationHdfsRequest', ], 'output' => [ 'shape' => 'DescribeLocationHdfsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeLocationNfs' => [ 'name' => 'DescribeLocationNfs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLocationNfsRequest', ], 'output' => [ 'shape' => 'DescribeLocationNfsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeLocationObjectStorage' => [ 'name' => 'DescribeLocationObjectStorage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLocationObjectStorageRequest', ], 'output' => [ 'shape' => 'DescribeLocationObjectStorageResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeLocationS3' => [ 'name' => 'DescribeLocationS3', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLocationS3Request', ], 'output' => [ 'shape' => 'DescribeLocationS3Response', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeLocationSmb' => [ 'name' => 'DescribeLocationSmb', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLocationSmbRequest', ], 'output' => [ 'shape' => 'DescribeLocationSmbResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeStorageSystem' => [ 'name' => 'DescribeStorageSystem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStorageSystemRequest', ], 'output' => [ 'shape' => 'DescribeStorageSystemResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], 'endpoint' => [ 'hostPrefix' => 'discovery-', ], ], 'DescribeStorageSystemResourceMetrics' => [ 'name' => 'DescribeStorageSystemResourceMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStorageSystemResourceMetricsRequest', ], 'output' => [ 'shape' => 'DescribeStorageSystemResourceMetricsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], 'endpoint' => [ 'hostPrefix' => 'discovery-', ], ], 'DescribeStorageSystemResources' => [ 'name' => 'DescribeStorageSystemResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStorageSystemResourcesRequest', ], 'output' => [ 'shape' => 'DescribeStorageSystemResourcesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], 'endpoint' => [ 'hostPrefix' => 'discovery-', ], ], 'DescribeTask' => [ 'name' => 'DescribeTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTaskRequest', ], 'output' => [ 'shape' => 'DescribeTaskResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeTaskExecution' => [ 'name' => 'DescribeTaskExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTaskExecutionRequest', ], 'output' => [ 'shape' => 'DescribeTaskExecutionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'GenerateRecommendations' => [ 'name' => 'GenerateRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GenerateRecommendationsRequest', ], 'output' => [ 'shape' => 'GenerateRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], 'endpoint' => [ 'hostPrefix' => 'discovery-', ], ], 'ListAgents' => [ 'name' => 'ListAgents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAgentsRequest', ], 'output' => [ 'shape' => 'ListAgentsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'ListDiscoveryJobs' => [ 'name' => 'ListDiscoveryJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDiscoveryJobsRequest', ], 'output' => [ 'shape' => 'ListDiscoveryJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], 'endpoint' => [ 'hostPrefix' => 'discovery-', ], ], 'ListLocations' => [ 'name' => 'ListLocations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListLocationsRequest', ], 'output' => [ 'shape' => 'ListLocationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'ListStorageSystems' => [ 'name' => 'ListStorageSystems', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStorageSystemsRequest', ], 'output' => [ 'shape' => 'ListStorageSystemsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], 'endpoint' => [ 'hostPrefix' => 'discovery-', ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'ListTaskExecutions' => [ 'name' => 'ListTaskExecutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTaskExecutionsRequest', ], 'output' => [ 'shape' => 'ListTaskExecutionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'ListTasks' => [ 'name' => 'ListTasks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTasksRequest', ], 'output' => [ 'shape' => 'ListTasksResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'RemoveStorageSystem' => [ 'name' => 'RemoveStorageSystem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveStorageSystemRequest', ], 'output' => [ 'shape' => 'RemoveStorageSystemResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], 'endpoint' => [ 'hostPrefix' => 'discovery-', ], ], 'StartDiscoveryJob' => [ 'name' => 'StartDiscoveryJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartDiscoveryJobRequest', ], 'output' => [ 'shape' => 'StartDiscoveryJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], 'endpoint' => [ 'hostPrefix' => 'discovery-', ], ], 'StartTaskExecution' => [ 'name' => 'StartTaskExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartTaskExecutionRequest', ], 'output' => [ 'shape' => 'StartTaskExecutionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'StopDiscoveryJob' => [ 'name' => 'StopDiscoveryJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopDiscoveryJobRequest', ], 'output' => [ 'shape' => 'StopDiscoveryJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], 'endpoint' => [ 'hostPrefix' => 'discovery-', ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'UpdateAgent' => [ 'name' => 'UpdateAgent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAgentRequest', ], 'output' => [ 'shape' => 'UpdateAgentResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'UpdateDiscoveryJob' => [ 'name' => 'UpdateDiscoveryJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDiscoveryJobRequest', ], 'output' => [ 'shape' => 'UpdateDiscoveryJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], 'endpoint' => [ 'hostPrefix' => 'discovery-', ], ], 'UpdateLocationAzureBlob' => [ 'name' => 'UpdateLocationAzureBlob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateLocationAzureBlobRequest', ], 'output' => [ 'shape' => 'UpdateLocationAzureBlobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'UpdateLocationHdfs' => [ 'name' => 'UpdateLocationHdfs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateLocationHdfsRequest', ], 'output' => [ 'shape' => 'UpdateLocationHdfsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'UpdateLocationNfs' => [ 'name' => 'UpdateLocationNfs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateLocationNfsRequest', ], 'output' => [ 'shape' => 'UpdateLocationNfsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'UpdateLocationObjectStorage' => [ 'name' => 'UpdateLocationObjectStorage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateLocationObjectStorageRequest', ], 'output' => [ 'shape' => 'UpdateLocationObjectStorageResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'UpdateLocationSmb' => [ 'name' => 'UpdateLocationSmb', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateLocationSmbRequest', ], 'output' => [ 'shape' => 'UpdateLocationSmbResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'UpdateStorageSystem' => [ 'name' => 'UpdateStorageSystem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateStorageSystemRequest', ], 'output' => [ 'shape' => 'UpdateStorageSystemResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], 'endpoint' => [ 'hostPrefix' => 'discovery-', ], ], 'UpdateTask' => [ 'name' => 'UpdateTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTaskRequest', ], 'output' => [ 'shape' => 'UpdateTaskResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], 'UpdateTaskExecution' => [ 'name' => 'UpdateTaskExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTaskExecutionRequest', ], 'output' => [ 'shape' => 'UpdateTaskExecutionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalException', ], ], ], ], 'shapes' => [ 'ActivationKey' => [ 'type' => 'string', 'max' => 29, 'pattern' => '[A-Z0-9]{5}(-[A-Z0-9]{5}){4}', ], 'AddStorageSystemRequest' => [ 'type' => 'structure', 'required' => [ 'ServerConfiguration', 'SystemType', 'AgentArns', 'ClientToken', 'Credentials', ], 'members' => [ 'ServerConfiguration' => [ 'shape' => 'DiscoveryServerConfiguration', ], 'SystemType' => [ 'shape' => 'DiscoverySystemType', ], 'AgentArns' => [ 'shape' => 'DiscoveryAgentArnList', ], 'CloudWatchLogGroupArn' => [ 'shape' => 'LogGroupArn', ], 'Tags' => [ 'shape' => 'InputTagList', ], 'Name' => [ 'shape' => 'Name', ], 'ClientToken' => [ 'shape' => 'PtolemyUUID', 'idempotencyToken' => true, ], 'Credentials' => [ 'shape' => 'Credentials', ], ], ], 'AddStorageSystemResponse' => [ 'type' => 'structure', 'required' => [ 'StorageSystemArn', ], 'members' => [ 'StorageSystemArn' => [ 'shape' => 'StorageSystemArn', ], ], ], 'AgentArn' => [ 'type' => 'string', 'max' => 128, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):datasync:[a-z\\-0-9]+:[0-9]{12}:agent/agent-[0-9a-z]{17}$', ], 'AgentArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentArn', ], 'max' => 4, 'min' => 1, ], 'AgentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentListEntry', ], ], 'AgentListEntry' => [ 'type' => 'structure', 'members' => [ 'AgentArn' => [ 'shape' => 'AgentArn', ], 'Name' => [ 'shape' => 'TagValue', ], 'Status' => [ 'shape' => 'AgentStatus', ], 'Platform' => [ 'shape' => 'Platform', ], ], ], 'AgentStatus' => [ 'type' => 'string', 'enum' => [ 'ONLINE', 'OFFLINE', ], ], 'AgentVersion' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\s+=._:@/-]+$', ], 'Atime' => [ 'type' => 'string', 'enum' => [ 'NONE', 'BEST_EFFORT', ], ], 'AzureAccessTier' => [ 'type' => 'string', 'enum' => [ 'HOT', 'COOL', 'ARCHIVE', ], ], 'AzureBlobAuthenticationType' => [ 'type' => 'string', 'enum' => [ 'SAS', ], ], 'AzureBlobContainerUrl' => [ 'type' => 'string', 'max' => 325, 'pattern' => '^https:\\/\\/[A-Za-z0-9]((\\.|-+)?[A-Za-z0-9]){0,252}\\/[a-z0-9](-?[a-z0-9]){2,62}$', ], 'AzureBlobSasConfiguration' => [ 'type' => 'structure', 'required' => [ 'Token', ], 'members' => [ 'Token' => [ 'shape' => 'AzureBlobSasToken', ], ], ], 'AzureBlobSasToken' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', 'sensitive' => true, ], 'AzureBlobSubdirectory' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '^[\\p{L}\\p{M}\\p{Z}\\p{S}\\p{N}\\p{P}\\p{C}]*$', ], 'AzureBlobType' => [ 'type' => 'string', 'enum' => [ 'BLOCK', ], ], 'BytesPerSecond' => [ 'type' => 'long', 'min' => -1, ], 'CancelTaskExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'TaskExecutionArn', ], 'members' => [ 'TaskExecutionArn' => [ 'shape' => 'TaskExecutionArn', ], ], ], 'CancelTaskExecutionResponse' => [ 'type' => 'structure', 'members' => [], ], 'Capacity' => [ 'type' => 'structure', 'members' => [ 'Used' => [ 'shape' => 'NonNegativeLong', ], 'Provisioned' => [ 'shape' => 'NonNegativeLong', ], 'LogicalUsed' => [ 'shape' => 'NonNegativeLong', ], 'ClusterCloudStorageUsed' => [ 'shape' => 'NonNegativeLong', ], ], ], 'CollectionDurationMinutes' => [ 'type' => 'integer', 'max' => 44640, 'min' => 60, ], 'CreateAgentRequest' => [ 'type' => 'structure', 'required' => [ 'ActivationKey', ], 'members' => [ 'ActivationKey' => [ 'shape' => 'ActivationKey', ], 'AgentName' => [ 'shape' => 'TagValue', ], 'Tags' => [ 'shape' => 'InputTagList', ], 'VpcEndpointId' => [ 'shape' => 'VpcEndpointId', ], 'SubnetArns' => [ 'shape' => 'PLSubnetArnList', ], 'SecurityGroupArns' => [ 'shape' => 'PLSecurityGroupArnList', ], ], ], 'CreateAgentResponse' => [ 'type' => 'structure', 'members' => [ 'AgentArn' => [ 'shape' => 'AgentArn', ], ], ], 'CreateLocationAzureBlobRequest' => [ 'type' => 'structure', 'required' => [ 'ContainerUrl', 'AuthenticationType', 'AgentArns', ], 'members' => [ 'ContainerUrl' => [ 'shape' => 'AzureBlobContainerUrl', ], 'AuthenticationType' => [ 'shape' => 'AzureBlobAuthenticationType', ], 'SasConfiguration' => [ 'shape' => 'AzureBlobSasConfiguration', ], 'BlobType' => [ 'shape' => 'AzureBlobType', ], 'AccessTier' => [ 'shape' => 'AzureAccessTier', ], 'Subdirectory' => [ 'shape' => 'AzureBlobSubdirectory', ], 'AgentArns' => [ 'shape' => 'AgentArnList', ], 'Tags' => [ 'shape' => 'InputTagList', ], ], ], 'CreateLocationAzureBlobResponse' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'CreateLocationEfsRequest' => [ 'type' => 'structure', 'required' => [ 'EfsFilesystemArn', 'Ec2Config', ], 'members' => [ 'Subdirectory' => [ 'shape' => 'EfsSubdirectory', ], 'EfsFilesystemArn' => [ 'shape' => 'EfsFilesystemArn', ], 'Ec2Config' => [ 'shape' => 'Ec2Config', ], 'Tags' => [ 'shape' => 'InputTagList', ], 'AccessPointArn' => [ 'shape' => 'EfsAccessPointArn', ], 'FileSystemAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'InTransitEncryption' => [ 'shape' => 'EfsInTransitEncryption', ], ], ], 'CreateLocationEfsResponse' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'CreateLocationFsxLustreRequest' => [ 'type' => 'structure', 'required' => [ 'FsxFilesystemArn', 'SecurityGroupArns', ], 'members' => [ 'FsxFilesystemArn' => [ 'shape' => 'FsxFilesystemArn', ], 'SecurityGroupArns' => [ 'shape' => 'Ec2SecurityGroupArnList', ], 'Subdirectory' => [ 'shape' => 'FsxLustreSubdirectory', ], 'Tags' => [ 'shape' => 'InputTagList', ], ], ], 'CreateLocationFsxLustreResponse' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'CreateLocationFsxOntapRequest' => [ 'type' => 'structure', 'required' => [ 'Protocol', 'SecurityGroupArns', 'StorageVirtualMachineArn', ], 'members' => [ 'Protocol' => [ 'shape' => 'FsxProtocol', ], 'SecurityGroupArns' => [ 'shape' => 'Ec2SecurityGroupArnList', ], 'StorageVirtualMachineArn' => [ 'shape' => 'StorageVirtualMachineArn', ], 'Subdirectory' => [ 'shape' => 'FsxOntapSubdirectory', ], 'Tags' => [ 'shape' => 'InputTagList', ], ], ], 'CreateLocationFsxOntapResponse' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'CreateLocationFsxOpenZfsRequest' => [ 'type' => 'structure', 'required' => [ 'FsxFilesystemArn', 'Protocol', 'SecurityGroupArns', ], 'members' => [ 'FsxFilesystemArn' => [ 'shape' => 'FsxFilesystemArn', ], 'Protocol' => [ 'shape' => 'FsxProtocol', ], 'SecurityGroupArns' => [ 'shape' => 'Ec2SecurityGroupArnList', ], 'Subdirectory' => [ 'shape' => 'FsxOpenZfsSubdirectory', ], 'Tags' => [ 'shape' => 'InputTagList', ], ], ], 'CreateLocationFsxOpenZfsResponse' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'CreateLocationFsxWindowsRequest' => [ 'type' => 'structure', 'required' => [ 'FsxFilesystemArn', 'SecurityGroupArns', 'User', 'Password', ], 'members' => [ 'Subdirectory' => [ 'shape' => 'FsxWindowsSubdirectory', ], 'FsxFilesystemArn' => [ 'shape' => 'FsxFilesystemArn', ], 'SecurityGroupArns' => [ 'shape' => 'Ec2SecurityGroupArnList', ], 'Tags' => [ 'shape' => 'InputTagList', ], 'User' => [ 'shape' => 'SmbUser', ], 'Domain' => [ 'shape' => 'SmbDomain', ], 'Password' => [ 'shape' => 'SmbPassword', ], ], ], 'CreateLocationFsxWindowsResponse' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'CreateLocationHdfsRequest' => [ 'type' => 'structure', 'required' => [ 'NameNodes', 'AuthenticationType', 'AgentArns', ], 'members' => [ 'Subdirectory' => [ 'shape' => 'HdfsSubdirectory', ], 'NameNodes' => [ 'shape' => 'HdfsNameNodeList', ], 'BlockSize' => [ 'shape' => 'HdfsBlockSize', ], 'ReplicationFactor' => [ 'shape' => 'HdfsReplicationFactor', ], 'KmsKeyProviderUri' => [ 'shape' => 'KmsKeyProviderUri', ], 'QopConfiguration' => [ 'shape' => 'QopConfiguration', ], 'AuthenticationType' => [ 'shape' => 'HdfsAuthenticationType', ], 'SimpleUser' => [ 'shape' => 'HdfsUser', ], 'KerberosPrincipal' => [ 'shape' => 'KerberosPrincipal', ], 'KerberosKeytab' => [ 'shape' => 'KerberosKeytabFile', ], 'KerberosKrb5Conf' => [ 'shape' => 'KerberosKrb5ConfFile', ], 'AgentArns' => [ 'shape' => 'AgentArnList', ], 'Tags' => [ 'shape' => 'InputTagList', ], ], ], 'CreateLocationHdfsResponse' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'CreateLocationNfsRequest' => [ 'type' => 'structure', 'required' => [ 'Subdirectory', 'ServerHostname', 'OnPremConfig', ], 'members' => [ 'Subdirectory' => [ 'shape' => 'NfsSubdirectory', ], 'ServerHostname' => [ 'shape' => 'ServerHostname', ], 'OnPremConfig' => [ 'shape' => 'OnPremConfig', ], 'MountOptions' => [ 'shape' => 'NfsMountOptions', ], 'Tags' => [ 'shape' => 'InputTagList', ], ], ], 'CreateLocationNfsResponse' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'CreateLocationObjectStorageRequest' => [ 'type' => 'structure', 'required' => [ 'ServerHostname', 'BucketName', 'AgentArns', ], 'members' => [ 'ServerHostname' => [ 'shape' => 'ServerHostname', ], 'ServerPort' => [ 'shape' => 'ObjectStorageServerPort', ], 'ServerProtocol' => [ 'shape' => 'ObjectStorageServerProtocol', ], 'Subdirectory' => [ 'shape' => 'S3Subdirectory', ], 'BucketName' => [ 'shape' => 'ObjectStorageBucketName', ], 'AccessKey' => [ 'shape' => 'ObjectStorageAccessKey', ], 'SecretKey' => [ 'shape' => 'ObjectStorageSecretKey', ], 'AgentArns' => [ 'shape' => 'AgentArnList', ], 'Tags' => [ 'shape' => 'InputTagList', ], 'ServerCertificate' => [ 'shape' => 'ObjectStorageCertificate', ], ], ], 'CreateLocationObjectStorageResponse' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'CreateLocationS3Request' => [ 'type' => 'structure', 'required' => [ 'S3BucketArn', 'S3Config', ], 'members' => [ 'Subdirectory' => [ 'shape' => 'S3Subdirectory', ], 'S3BucketArn' => [ 'shape' => 'S3BucketArn', ], 'S3StorageClass' => [ 'shape' => 'S3StorageClass', ], 'S3Config' => [ 'shape' => 'S3Config', ], 'AgentArns' => [ 'shape' => 'AgentArnList', ], 'Tags' => [ 'shape' => 'InputTagList', ], ], ], 'CreateLocationS3Response' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'CreateLocationSmbRequest' => [ 'type' => 'structure', 'required' => [ 'Subdirectory', 'ServerHostname', 'User', 'Password', 'AgentArns', ], 'members' => [ 'Subdirectory' => [ 'shape' => 'SmbSubdirectory', ], 'ServerHostname' => [ 'shape' => 'ServerHostname', ], 'User' => [ 'shape' => 'SmbUser', ], 'Domain' => [ 'shape' => 'SmbDomain', ], 'Password' => [ 'shape' => 'SmbPassword', ], 'AgentArns' => [ 'shape' => 'AgentArnList', ], 'MountOptions' => [ 'shape' => 'SmbMountOptions', ], 'Tags' => [ 'shape' => 'InputTagList', ], ], ], 'CreateLocationSmbResponse' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'CreateTaskRequest' => [ 'type' => 'structure', 'required' => [ 'SourceLocationArn', 'DestinationLocationArn', ], 'members' => [ 'SourceLocationArn' => [ 'shape' => 'LocationArn', ], 'DestinationLocationArn' => [ 'shape' => 'LocationArn', ], 'CloudWatchLogGroupArn' => [ 'shape' => 'LogGroupArn', ], 'Name' => [ 'shape' => 'TagValue', ], 'Options' => [ 'shape' => 'Options', ], 'Excludes' => [ 'shape' => 'FilterList', ], 'Schedule' => [ 'shape' => 'TaskSchedule', ], 'Tags' => [ 'shape' => 'InputTagList', ], 'Includes' => [ 'shape' => 'FilterList', ], 'ManifestConfig' => [ 'shape' => 'ManifestConfig', ], 'TaskReportConfig' => [ 'shape' => 'TaskReportConfig', ], ], ], 'CreateTaskResponse' => [ 'type' => 'structure', 'members' => [ 'TaskArn' => [ 'shape' => 'TaskArn', ], ], ], 'Credentials' => [ 'type' => 'structure', 'required' => [ 'Username', 'Password', ], 'members' => [ 'Username' => [ 'shape' => 'PtolemyUsername', ], 'Password' => [ 'shape' => 'PtolemyPassword', ], ], ], 'DeleteAgentRequest' => [ 'type' => 'structure', 'required' => [ 'AgentArn', ], 'members' => [ 'AgentArn' => [ 'shape' => 'AgentArn', ], ], ], 'DeleteAgentResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLocationRequest' => [ 'type' => 'structure', 'required' => [ 'LocationArn', ], 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'DeleteLocationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTaskRequest' => [ 'type' => 'structure', 'required' => [ 'TaskArn', ], 'members' => [ 'TaskArn' => [ 'shape' => 'TaskArn', ], ], ], 'DeleteTaskResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeAgentRequest' => [ 'type' => 'structure', 'required' => [ 'AgentArn', ], 'members' => [ 'AgentArn' => [ 'shape' => 'AgentArn', ], ], ], 'DescribeAgentResponse' => [ 'type' => 'structure', 'members' => [ 'AgentArn' => [ 'shape' => 'AgentArn', ], 'Name' => [ 'shape' => 'TagValue', ], 'Status' => [ 'shape' => 'AgentStatus', ], 'LastConnectionTime' => [ 'shape' => 'Time', ], 'CreationTime' => [ 'shape' => 'Time', ], 'EndpointType' => [ 'shape' => 'EndpointType', ], 'PrivateLinkConfig' => [ 'shape' => 'PrivateLinkConfig', ], 'Platform' => [ 'shape' => 'Platform', ], ], ], 'DescribeDiscoveryJobRequest' => [ 'type' => 'structure', 'required' => [ 'DiscoveryJobArn', ], 'members' => [ 'DiscoveryJobArn' => [ 'shape' => 'DiscoveryJobArn', ], ], ], 'DescribeDiscoveryJobResponse' => [ 'type' => 'structure', 'members' => [ 'StorageSystemArn' => [ 'shape' => 'StorageSystemArn', ], 'DiscoveryJobArn' => [ 'shape' => 'DiscoveryJobArn', ], 'CollectionDurationMinutes' => [ 'shape' => 'CollectionDurationMinutes', ], 'Status' => [ 'shape' => 'DiscoveryJobStatus', ], 'JobStartTime' => [ 'shape' => 'DiscoveryTime', ], 'JobEndTime' => [ 'shape' => 'DiscoveryTime', ], ], ], 'DescribeLocationAzureBlobRequest' => [ 'type' => 'structure', 'required' => [ 'LocationArn', ], 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'DescribeLocationAzureBlobResponse' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], 'LocationUri' => [ 'shape' => 'LocationUri', ], 'AuthenticationType' => [ 'shape' => 'AzureBlobAuthenticationType', ], 'BlobType' => [ 'shape' => 'AzureBlobType', ], 'AccessTier' => [ 'shape' => 'AzureAccessTier', ], 'AgentArns' => [ 'shape' => 'AgentArnList', ], 'CreationTime' => [ 'shape' => 'Time', ], ], ], 'DescribeLocationEfsRequest' => [ 'type' => 'structure', 'required' => [ 'LocationArn', ], 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'DescribeLocationEfsResponse' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], 'LocationUri' => [ 'shape' => 'LocationUri', ], 'Ec2Config' => [ 'shape' => 'Ec2Config', ], 'CreationTime' => [ 'shape' => 'Time', ], 'AccessPointArn' => [ 'shape' => 'EfsAccessPointArn', ], 'FileSystemAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'InTransitEncryption' => [ 'shape' => 'EfsInTransitEncryption', ], ], ], 'DescribeLocationFsxLustreRequest' => [ 'type' => 'structure', 'required' => [ 'LocationArn', ], 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'DescribeLocationFsxLustreResponse' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], 'LocationUri' => [ 'shape' => 'LocationUri', ], 'SecurityGroupArns' => [ 'shape' => 'Ec2SecurityGroupArnList', ], 'CreationTime' => [ 'shape' => 'Time', ], ], ], 'DescribeLocationFsxOntapRequest' => [ 'type' => 'structure', 'required' => [ 'LocationArn', ], 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'DescribeLocationFsxOntapResponse' => [ 'type' => 'structure', 'members' => [ 'CreationTime' => [ 'shape' => 'Time', ], 'LocationArn' => [ 'shape' => 'LocationArn', ], 'LocationUri' => [ 'shape' => 'LocationUri', ], 'Protocol' => [ 'shape' => 'FsxProtocol', ], 'SecurityGroupArns' => [ 'shape' => 'Ec2SecurityGroupArnList', ], 'StorageVirtualMachineArn' => [ 'shape' => 'StorageVirtualMachineArn', ], 'FsxFilesystemArn' => [ 'shape' => 'FsxFilesystemArn', ], ], ], 'DescribeLocationFsxOpenZfsRequest' => [ 'type' => 'structure', 'required' => [ 'LocationArn', ], 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'DescribeLocationFsxOpenZfsResponse' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], 'LocationUri' => [ 'shape' => 'LocationUri', ], 'SecurityGroupArns' => [ 'shape' => 'Ec2SecurityGroupArnList', ], 'Protocol' => [ 'shape' => 'FsxProtocol', ], 'CreationTime' => [ 'shape' => 'Time', ], ], ], 'DescribeLocationFsxWindowsRequest' => [ 'type' => 'structure', 'required' => [ 'LocationArn', ], 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'DescribeLocationFsxWindowsResponse' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], 'LocationUri' => [ 'shape' => 'LocationUri', ], 'SecurityGroupArns' => [ 'shape' => 'Ec2SecurityGroupArnList', ], 'CreationTime' => [ 'shape' => 'Time', ], 'User' => [ 'shape' => 'SmbUser', ], 'Domain' => [ 'shape' => 'SmbDomain', ], ], ], 'DescribeLocationHdfsRequest' => [ 'type' => 'structure', 'required' => [ 'LocationArn', ], 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'DescribeLocationHdfsResponse' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], 'LocationUri' => [ 'shape' => 'LocationUri', ], 'NameNodes' => [ 'shape' => 'HdfsNameNodeList', ], 'BlockSize' => [ 'shape' => 'HdfsBlockSize', ], 'ReplicationFactor' => [ 'shape' => 'HdfsReplicationFactor', ], 'KmsKeyProviderUri' => [ 'shape' => 'KmsKeyProviderUri', ], 'QopConfiguration' => [ 'shape' => 'QopConfiguration', ], 'AuthenticationType' => [ 'shape' => 'HdfsAuthenticationType', ], 'SimpleUser' => [ 'shape' => 'HdfsUser', ], 'KerberosPrincipal' => [ 'shape' => 'KerberosPrincipal', ], 'AgentArns' => [ 'shape' => 'AgentArnList', ], 'CreationTime' => [ 'shape' => 'Time', ], ], ], 'DescribeLocationNfsRequest' => [ 'type' => 'structure', 'required' => [ 'LocationArn', ], 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'DescribeLocationNfsResponse' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], 'LocationUri' => [ 'shape' => 'LocationUri', ], 'OnPremConfig' => [ 'shape' => 'OnPremConfig', ], 'MountOptions' => [ 'shape' => 'NfsMountOptions', ], 'CreationTime' => [ 'shape' => 'Time', ], ], ], 'DescribeLocationObjectStorageRequest' => [ 'type' => 'structure', 'required' => [ 'LocationArn', ], 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'DescribeLocationObjectStorageResponse' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], 'LocationUri' => [ 'shape' => 'LocationUri', ], 'AccessKey' => [ 'shape' => 'ObjectStorageAccessKey', ], 'ServerPort' => [ 'shape' => 'ObjectStorageServerPort', ], 'ServerProtocol' => [ 'shape' => 'ObjectStorageServerProtocol', ], 'AgentArns' => [ 'shape' => 'AgentArnList', ], 'CreationTime' => [ 'shape' => 'Time', ], 'ServerCertificate' => [ 'shape' => 'ObjectStorageCertificate', ], ], ], 'DescribeLocationS3Request' => [ 'type' => 'structure', 'required' => [ 'LocationArn', ], 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'DescribeLocationS3Response' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], 'LocationUri' => [ 'shape' => 'LocationUri', ], 'S3StorageClass' => [ 'shape' => 'S3StorageClass', ], 'S3Config' => [ 'shape' => 'S3Config', ], 'AgentArns' => [ 'shape' => 'AgentArnList', ], 'CreationTime' => [ 'shape' => 'Time', ], ], ], 'DescribeLocationSmbRequest' => [ 'type' => 'structure', 'required' => [ 'LocationArn', ], 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], ], ], 'DescribeLocationSmbResponse' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], 'LocationUri' => [ 'shape' => 'LocationUri', ], 'AgentArns' => [ 'shape' => 'AgentArnList', ], 'User' => [ 'shape' => 'SmbUser', ], 'Domain' => [ 'shape' => 'SmbDomain', ], 'MountOptions' => [ 'shape' => 'SmbMountOptions', ], 'CreationTime' => [ 'shape' => 'Time', ], ], ], 'DescribeStorageSystemRequest' => [ 'type' => 'structure', 'required' => [ 'StorageSystemArn', ], 'members' => [ 'StorageSystemArn' => [ 'shape' => 'StorageSystemArn', ], ], ], 'DescribeStorageSystemResourceMetricsRequest' => [ 'type' => 'structure', 'required' => [ 'DiscoveryJobArn', 'ResourceType', 'ResourceId', ], 'members' => [ 'DiscoveryJobArn' => [ 'shape' => 'DiscoveryJobArn', ], 'ResourceType' => [ 'shape' => 'DiscoveryResourceType', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'StartTime' => [ 'shape' => 'DiscoveryTime', ], 'EndTime' => [ 'shape' => 'DiscoveryTime', ], 'MaxResults' => [ 'shape' => 'DiscoveryMaxResults', ], 'NextToken' => [ 'shape' => 'DiscoveryNextToken', ], ], ], 'DescribeStorageSystemResourceMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'Metrics' => [ 'shape' => 'Metrics', ], 'NextToken' => [ 'shape' => 'DiscoveryNextToken', ], ], ], 'DescribeStorageSystemResourcesRequest' => [ 'type' => 'structure', 'required' => [ 'DiscoveryJobArn', 'ResourceType', ], 'members' => [ 'DiscoveryJobArn' => [ 'shape' => 'DiscoveryJobArn', ], 'ResourceType' => [ 'shape' => 'DiscoveryResourceType', ], 'ResourceIds' => [ 'shape' => 'ResourceIds', ], 'Filter' => [ 'shape' => 'ResourceFilters', ], 'MaxResults' => [ 'shape' => 'DiscoveryMaxResults', ], 'NextToken' => [ 'shape' => 'DiscoveryNextToken', ], ], ], 'DescribeStorageSystemResourcesResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceDetails' => [ 'shape' => 'ResourceDetails', ], 'NextToken' => [ 'shape' => 'DiscoveryNextToken', ], ], ], 'DescribeStorageSystemResponse' => [ 'type' => 'structure', 'members' => [ 'StorageSystemArn' => [ 'shape' => 'StorageSystemArn', ], 'ServerConfiguration' => [ 'shape' => 'DiscoveryServerConfiguration', ], 'SystemType' => [ 'shape' => 'DiscoverySystemType', ], 'AgentArns' => [ 'shape' => 'DiscoveryAgentArnList', ], 'Name' => [ 'shape' => 'Name', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], 'ConnectivityStatus' => [ 'shape' => 'StorageSystemConnectivityStatus', ], 'CloudWatchLogGroupArn' => [ 'shape' => 'LogGroupArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'SecretsManagerArn' => [ 'shape' => 'SecretsManagerArn', ], ], ], 'DescribeTaskExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'TaskExecutionArn', ], 'members' => [ 'TaskExecutionArn' => [ 'shape' => 'TaskExecutionArn', ], ], ], 'DescribeTaskExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'TaskExecutionArn' => [ 'shape' => 'TaskExecutionArn', ], 'Status' => [ 'shape' => 'TaskExecutionStatus', ], 'Options' => [ 'shape' => 'Options', ], 'Excludes' => [ 'shape' => 'FilterList', ], 'Includes' => [ 'shape' => 'FilterList', ], 'ManifestConfig' => [ 'shape' => 'ManifestConfig', ], 'StartTime' => [ 'shape' => 'Time', ], 'EstimatedFilesToTransfer' => [ 'shape' => 'long', ], 'EstimatedBytesToTransfer' => [ 'shape' => 'long', ], 'FilesTransferred' => [ 'shape' => 'long', ], 'BytesWritten' => [ 'shape' => 'long', ], 'BytesTransferred' => [ 'shape' => 'long', ], 'BytesCompressed' => [ 'shape' => 'long', ], 'Result' => [ 'shape' => 'TaskExecutionResultDetail', ], 'TaskReportConfig' => [ 'shape' => 'TaskReportConfig', ], 'FilesDeleted' => [ 'shape' => 'long', ], 'FilesSkipped' => [ 'shape' => 'long', ], 'FilesVerified' => [ 'shape' => 'long', ], 'ReportResult' => [ 'shape' => 'ReportResult', ], 'EstimatedFilesToDelete' => [ 'shape' => 'long', ], ], ], 'DescribeTaskRequest' => [ 'type' => 'structure', 'required' => [ 'TaskArn', ], 'members' => [ 'TaskArn' => [ 'shape' => 'TaskArn', ], ], ], 'DescribeTaskResponse' => [ 'type' => 'structure', 'members' => [ 'TaskArn' => [ 'shape' => 'TaskArn', ], 'Status' => [ 'shape' => 'TaskStatus', ], 'Name' => [ 'shape' => 'TagValue', ], 'CurrentTaskExecutionArn' => [ 'shape' => 'TaskExecutionArn', ], 'SourceLocationArn' => [ 'shape' => 'LocationArn', ], 'DestinationLocationArn' => [ 'shape' => 'LocationArn', ], 'CloudWatchLogGroupArn' => [ 'shape' => 'LogGroupArn', ], 'SourceNetworkInterfaceArns' => [ 'shape' => 'SourceNetworkInterfaceArns', ], 'DestinationNetworkInterfaceArns' => [ 'shape' => 'DestinationNetworkInterfaceArns', ], 'Options' => [ 'shape' => 'Options', ], 'Excludes' => [ 'shape' => 'FilterList', ], 'Schedule' => [ 'shape' => 'TaskSchedule', ], 'ErrorCode' => [ 'shape' => 'string', ], 'ErrorDetail' => [ 'shape' => 'string', ], 'CreationTime' => [ 'shape' => 'Time', ], 'Includes' => [ 'shape' => 'FilterList', ], 'ManifestConfig' => [ 'shape' => 'ManifestConfig', ], 'TaskReportConfig' => [ 'shape' => 'TaskReportConfig', ], 'ScheduleDetails' => [ 'shape' => 'TaskScheduleDetails', ], ], ], 'DestinationNetworkInterfaceArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkInterfaceArn', ], ], 'DiscoveryAgentArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentArn', ], 'max' => 1, 'min' => 1, ], 'DiscoveryJobArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):datasync:[a-z\\-0-9]+:[0-9]{12}:system/storage-system-[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}/job/discovery-job-[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'DiscoveryJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DiscoveryJobListEntry', ], ], 'DiscoveryJobListEntry' => [ 'type' => 'structure', 'members' => [ 'DiscoveryJobArn' => [ 'shape' => 'DiscoveryJobArn', ], 'Status' => [ 'shape' => 'DiscoveryJobStatus', ], ], ], 'DiscoveryJobStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'WARNING', 'TERMINATED', 'FAILED', 'STOPPED', 'COMPLETED', 'COMPLETED_WITH_ISSUES', ], ], 'DiscoveryMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'DiscoveryNextToken' => [ 'type' => 'string', 'max' => 65535, 'pattern' => '[a-zA-Z0-9=_-]+', ], 'DiscoveryResourceFilter' => [ 'type' => 'string', 'enum' => [ 'SVM', ], ], 'DiscoveryResourceType' => [ 'type' => 'string', 'enum' => [ 'SVM', 'VOLUME', 'CLUSTER', ], ], 'DiscoveryServerConfiguration' => [ 'type' => 'structure', 'required' => [ 'ServerHostname', ], 'members' => [ 'ServerHostname' => [ 'shape' => 'DiscoveryServerHostname', ], 'ServerPort' => [ 'shape' => 'DiscoveryServerPort', ], ], ], 'DiscoveryServerHostname' => [ 'type' => 'string', 'max' => 255, 'pattern' => '^(([a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)*([A-Za-z0-9\\-]*[A-Za-z0-9])$', ], 'DiscoveryServerPort' => [ 'type' => 'integer', 'box' => true, 'max' => 65535, 'min' => 1, ], 'DiscoverySystemType' => [ 'type' => 'string', 'enum' => [ 'NetAppONTAP', ], ], 'DiscoveryTime' => [ 'type' => 'timestamp', ], 'Duration' => [ 'type' => 'long', 'min' => 0, ], 'Ec2Config' => [ 'type' => 'structure', 'required' => [ 'SubnetArn', 'SecurityGroupArns', ], 'members' => [ 'SubnetArn' => [ 'shape' => 'Ec2SubnetArn', ], 'SecurityGroupArns' => [ 'shape' => 'Ec2SecurityGroupArnList', ], ], ], 'Ec2SecurityGroupArn' => [ 'type' => 'string', 'max' => 128, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):ec2:[a-z\\-0-9]*:[0-9]{12}:security-group/sg-[a-f0-9]+$', ], 'Ec2SecurityGroupArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Ec2SecurityGroupArn', ], 'max' => 5, 'min' => 1, ], 'Ec2SubnetArn' => [ 'type' => 'string', 'max' => 128, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):ec2:[a-z\\-0-9]*:[0-9]{12}:subnet/.*$', ], 'EfsAccessPointArn' => [ 'type' => 'string', 'max' => 128, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):elasticfilesystem:[a-z\\-0-9]+:[0-9]{12}:access-point/fsap-[0-9a-f]{8,40}$', ], 'EfsFilesystemArn' => [ 'type' => 'string', 'max' => 128, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):elasticfilesystem:[a-z\\-0-9]*:[0-9]{12}:file-system/fs-.*$', ], 'EfsInTransitEncryption' => [ 'type' => 'string', 'enum' => [ 'NONE', 'TLS1_2', ], ], 'EfsSubdirectory' => [ 'type' => 'string', 'max' => 4096, 'pattern' => '^[a-zA-Z0-9_\\-\\+\\./\\(\\)\\p{Zs}]*$', ], 'EnabledProtocols' => [ 'type' => 'list', 'member' => [ 'shape' => 'PtolemyString', ], ], 'Endpoint' => [ 'type' => 'string', 'max' => 15, 'min' => 7, 'pattern' => '\\A(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}\\z', ], 'EndpointType' => [ 'type' => 'string', 'enum' => [ 'PUBLIC', 'PRIVATE_LINK', 'FIPS', ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 128, 'pattern' => '.*', ], 'FilterAttributeValue' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[0-9a-zA-Z_\\ \\-\\:\\*\\.\\\\/\\?-]*$', ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterRule', ], 'max' => 1, 'min' => 0, ], 'FilterMembers' => [ 'type' => 'list', 'member' => [ 'shape' => 'PtolemyString', ], ], 'FilterRule' => [ 'type' => 'structure', 'members' => [ 'FilterType' => [ 'shape' => 'FilterType', ], 'Value' => [ 'shape' => 'FilterValue', ], ], ], 'FilterType' => [ 'type' => 'string', 'enum' => [ 'SIMPLE_PATTERN', ], 'max' => 128, 'pattern' => '^[A-Z0-9_]+$', ], 'FilterValue' => [ 'type' => 'string', 'max' => 102400, 'pattern' => '^[^\\x00]+$', ], 'FilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterAttributeValue', ], ], 'FsxFilesystemArn' => [ 'type' => 'string', 'max' => 128, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):fsx:[a-z\\-0-9]*:[0-9]{12}:file-system/fs-.*$', ], 'FsxLustreSubdirectory' => [ 'type' => 'string', 'max' => 4096, 'pattern' => '^[a-zA-Z0-9_\\-\\+\\./\\(\\)\\$\\p{Zs}]+$', ], 'FsxOntapSubdirectory' => [ 'type' => 'string', 'max' => 255, 'pattern' => '^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,255}$', ], 'FsxOpenZfsSubdirectory' => [ 'type' => 'string', 'max' => 4096, 'pattern' => '^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,4096}$', ], 'FsxProtocol' => [ 'type' => 'structure', 'members' => [ 'NFS' => [ 'shape' => 'FsxProtocolNfs', ], 'SMB' => [ 'shape' => 'FsxProtocolSmb', ], ], ], 'FsxProtocolNfs' => [ 'type' => 'structure', 'members' => [ 'MountOptions' => [ 'shape' => 'NfsMountOptions', ], ], ], 'FsxProtocolSmb' => [ 'type' => 'structure', 'required' => [ 'Password', 'User', ], 'members' => [ 'Domain' => [ 'shape' => 'SmbDomain', ], 'MountOptions' => [ 'shape' => 'SmbMountOptions', ], 'Password' => [ 'shape' => 'SmbPassword', ], 'User' => [ 'shape' => 'SmbUser', ], ], ], 'FsxWindowsSubdirectory' => [ 'type' => 'string', 'max' => 4096, 'pattern' => '^[a-zA-Z0-9_\\-\\+\\./\\(\\)\\$\\p{Zs}]+$', ], 'GenerateRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 'DiscoveryJobArn', 'ResourceIds', 'ResourceType', ], 'members' => [ 'DiscoveryJobArn' => [ 'shape' => 'DiscoveryJobArn', ], 'ResourceIds' => [ 'shape' => 'ResourceIds', ], 'ResourceType' => [ 'shape' => 'DiscoveryResourceType', ], ], ], 'GenerateRecommendationsResponse' => [ 'type' => 'structure', 'members' => [], ], 'Gid' => [ 'type' => 'string', 'enum' => [ 'NONE', 'INT_VALUE', 'NAME', 'BOTH', ], ], 'HdfsAuthenticationType' => [ 'type' => 'string', 'enum' => [ 'SIMPLE', 'KERBEROS', ], ], 'HdfsBlockSize' => [ 'type' => 'integer', 'box' => true, 'max' => 1073741824, 'min' => 1048576, ], 'HdfsDataTransferProtection' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'AUTHENTICATION', 'INTEGRITY', 'PRIVACY', ], ], 'HdfsNameNode' => [ 'type' => 'structure', 'required' => [ 'Hostname', 'Port', ], 'members' => [ 'Hostname' => [ 'shape' => 'HdfsServerHostname', ], 'Port' => [ 'shape' => 'HdfsServerPort', ], ], ], 'HdfsNameNodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HdfsNameNode', ], 'min' => 1, ], 'HdfsReplicationFactor' => [ 'type' => 'integer', 'box' => true, 'max' => 512, 'min' => 1, ], 'HdfsRpcProtection' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'AUTHENTICATION', 'INTEGRITY', 'PRIVACY', ], ], 'HdfsServerHostname' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(([a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)*([A-Za-z0-9\\-]*[A-Za-z0-9])$', ], 'HdfsServerPort' => [ 'type' => 'integer', 'box' => true, 'max' => 65536, 'min' => 1, ], 'HdfsSubdirectory' => [ 'type' => 'string', 'max' => 4096, 'pattern' => '^[a-zA-Z0-9_\\-\\+\\./\\(\\)\\$\\p{Zs}]+$', ], 'HdfsUser' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[_.A-Za-z0-9][-_.A-Za-z0-9]*$', ], 'IOPS' => [ 'type' => 'structure', 'members' => [ 'Read' => [ 'shape' => 'NonNegativeDouble', ], 'Write' => [ 'shape' => 'NonNegativeDouble', ], 'Other' => [ 'shape' => 'NonNegativeDouble', ], 'Total' => [ 'shape' => 'NonNegativeDouble', ], ], ], 'IamRoleArn' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):iam::[0-9]{12}:role/.*$', ], 'InputTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagListEntry', ], 'max' => 50, 'min' => 0, ], 'InternalException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], 'errorCode' => [ 'shape' => 'string', ], ], 'exception' => true, 'fault' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], 'errorCode' => [ 'shape' => 'string', ], 'datasyncErrorCode' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'KerberosKeytabFile' => [ 'type' => 'blob', 'max' => 65536, ], 'KerberosKrb5ConfFile' => [ 'type' => 'blob', 'max' => 131072, ], 'KerberosPrincipal' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^.+$', ], 'KmsKeyProviderUri' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^kms:\\/\\/http[s]?@(([a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)*([A-Za-z0-9\\-]*[A-Za-z0-9])(;(([a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)*([A-Za-z0-9\\-]*[A-Za-z0-9]))*:[0-9]{1,5}\\/kms$', ], 'Latency' => [ 'type' => 'structure', 'members' => [ 'Read' => [ 'shape' => 'NonNegativeDouble', ], 'Write' => [ 'shape' => 'NonNegativeDouble', ], 'Other' => [ 'shape' => 'NonNegativeDouble', ], ], ], 'ListAgentsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentsResponse' => [ 'type' => 'structure', 'members' => [ 'Agents' => [ 'shape' => 'AgentList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDiscoveryJobsRequest' => [ 'type' => 'structure', 'members' => [ 'StorageSystemArn' => [ 'shape' => 'StorageSystemArn', ], 'MaxResults' => [ 'shape' => 'DiscoveryMaxResults', ], 'NextToken' => [ 'shape' => 'DiscoveryNextToken', ], ], ], 'ListDiscoveryJobsResponse' => [ 'type' => 'structure', 'members' => [ 'DiscoveryJobs' => [ 'shape' => 'DiscoveryJobList', ], 'NextToken' => [ 'shape' => 'DiscoveryNextToken', ], ], ], 'ListLocationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Filters' => [ 'shape' => 'LocationFilters', ], ], ], 'ListLocationsResponse' => [ 'type' => 'structure', 'members' => [ 'Locations' => [ 'shape' => 'LocationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStorageSystemsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'DiscoveryMaxResults', ], 'NextToken' => [ 'shape' => 'DiscoveryNextToken', ], ], ], 'ListStorageSystemsResponse' => [ 'type' => 'structure', 'members' => [ 'StorageSystems' => [ 'shape' => 'StorageSystemList', ], 'NextToken' => [ 'shape' => 'DiscoveryNextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'TaggableResourceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'OutputTagList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTaskExecutionsRequest' => [ 'type' => 'structure', 'members' => [ 'TaskArn' => [ 'shape' => 'TaskArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTaskExecutionsResponse' => [ 'type' => 'structure', 'members' => [ 'TaskExecutions' => [ 'shape' => 'TaskExecutionList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTasksRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Filters' => [ 'shape' => 'TaskFilters', ], ], ], 'ListTasksResponse' => [ 'type' => 'structure', 'members' => [ 'Tasks' => [ 'shape' => 'TaskList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'LocationArn' => [ 'type' => 'string', 'max' => 128, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):datasync:[a-z\\-0-9]+:[0-9]{12}:location/loc-[0-9a-z]{17}$', ], 'LocationFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', 'Operator', ], 'members' => [ 'Name' => [ 'shape' => 'LocationFilterName', ], 'Values' => [ 'shape' => 'FilterValues', ], 'Operator' => [ 'shape' => 'Operator', ], ], ], 'LocationFilterName' => [ 'type' => 'string', 'enum' => [ 'LocationUri', 'LocationType', 'CreationTime', ], ], 'LocationFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'LocationFilter', ], ], 'LocationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LocationListEntry', ], ], 'LocationListEntry' => [ 'type' => 'structure', 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], 'LocationUri' => [ 'shape' => 'LocationUri', ], ], ], 'LocationUri' => [ 'type' => 'string', 'max' => 4360, 'pattern' => '^(efs|nfs|s3|smb|hdfs|fsx[a-z0-9-]+)://[a-zA-Z0-9.:/\\-]+$', ], 'LogGroupArn' => [ 'type' => 'string', 'max' => 562, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):logs:[a-z\\-0-9]+:[0-9]{12}:log-group:([^:\\*]*)(:\\*)?$', ], 'LogLevel' => [ 'type' => 'string', 'enum' => [ 'OFF', 'BASIC', 'TRANSFER', ], ], 'ManifestAction' => [ 'type' => 'string', 'enum' => [ 'TRANSFER', ], ], 'ManifestConfig' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'ManifestAction', ], 'Format' => [ 'shape' => 'ManifestFormat', ], 'Source' => [ 'shape' => 'SourceManifestConfig', ], ], ], 'ManifestFormat' => [ 'type' => 'string', 'enum' => [ 'CSV', ], ], 'MaxP95Performance' => [ 'type' => 'structure', 'members' => [ 'IopsRead' => [ 'shape' => 'NonNegativeDouble', ], 'IopsWrite' => [ 'shape' => 'NonNegativeDouble', ], 'IopsOther' => [ 'shape' => 'NonNegativeDouble', ], 'IopsTotal' => [ 'shape' => 'NonNegativeDouble', ], 'ThroughputRead' => [ 'shape' => 'NonNegativeDouble', ], 'ThroughputWrite' => [ 'shape' => 'NonNegativeDouble', ], 'ThroughputOther' => [ 'shape' => 'NonNegativeDouble', ], 'ThroughputTotal' => [ 'shape' => 'NonNegativeDouble', ], 'LatencyRead' => [ 'shape' => 'NonNegativeDouble', ], 'LatencyWrite' => [ 'shape' => 'NonNegativeDouble', ], 'LatencyOther' => [ 'shape' => 'NonNegativeDouble', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'Metrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceMetrics', ], ], 'Mtime' => [ 'type' => 'string', 'enum' => [ 'NONE', 'PRESERVE', ], ], 'Name' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[\\p{L}\\p{M}\\p{N}\\s+=._:@\\/-]+$', ], 'NetAppONTAPCluster' => [ 'type' => 'structure', 'members' => [ 'CifsShareCount' => [ 'shape' => 'NonNegativeLong', ], 'NfsExportedVolumes' => [ 'shape' => 'NonNegativeLong', ], 'ResourceId' => [ 'shape' => 'PtolemyUUID', ], 'ClusterName' => [ 'shape' => 'PtolemyString', ], 'MaxP95Performance' => [ 'shape' => 'MaxP95Performance', ], 'ClusterBlockStorageSize' => [ 'shape' => 'NonNegativeLong', ], 'ClusterBlockStorageUsed' => [ 'shape' => 'NonNegativeLong', ], 'ClusterBlockStorageLogicalUsed' => [ 'shape' => 'NonNegativeLong', ], 'Recommendations' => [ 'shape' => 'Recommendations', ], 'RecommendationStatus' => [ 'shape' => 'RecommendationStatus', ], 'LunCount' => [ 'shape' => 'NonNegativeLong', ], 'ClusterCloudStorageUsed' => [ 'shape' => 'NonNegativeLong', ], ], ], 'NetAppONTAPClusters' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetAppONTAPCluster', ], ], 'NetAppONTAPSVM' => [ 'type' => 'structure', 'members' => [ 'ClusterUuid' => [ 'shape' => 'PtolemyUUID', ], 'ResourceId' => [ 'shape' => 'PtolemyUUID', ], 'SvmName' => [ 'shape' => 'PtolemyString', ], 'CifsShareCount' => [ 'shape' => 'NonNegativeLong', ], 'EnabledProtocols' => [ 'shape' => 'EnabledProtocols', ], 'TotalCapacityUsed' => [ 'shape' => 'NonNegativeLong', ], 'TotalCapacityProvisioned' => [ 'shape' => 'NonNegativeLong', ], 'TotalLogicalCapacityUsed' => [ 'shape' => 'NonNegativeLong', ], 'MaxP95Performance' => [ 'shape' => 'MaxP95Performance', ], 'Recommendations' => [ 'shape' => 'Recommendations', ], 'NfsExportedVolumes' => [ 'shape' => 'NonNegativeLong', ], 'RecommendationStatus' => [ 'shape' => 'RecommendationStatus', ], 'TotalSnapshotCapacityUsed' => [ 'shape' => 'NonNegativeLong', ], 'LunCount' => [ 'shape' => 'NonNegativeLong', ], ], ], 'NetAppONTAPSVMs' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetAppONTAPSVM', ], ], 'NetAppONTAPVolume' => [ 'type' => 'structure', 'members' => [ 'VolumeName' => [ 'shape' => 'PtolemyString', ], 'ResourceId' => [ 'shape' => 'PtolemyUUID', ], 'CifsShareCount' => [ 'shape' => 'NonNegativeLong', ], 'SecurityStyle' => [ 'shape' => 'PtolemyString', ], 'SvmUuid' => [ 'shape' => 'PtolemyUUID', ], 'SvmName' => [ 'shape' => 'PtolemyString', ], 'CapacityUsed' => [ 'shape' => 'NonNegativeLong', ], 'CapacityProvisioned' => [ 'shape' => 'NonNegativeLong', ], 'LogicalCapacityUsed' => [ 'shape' => 'NonNegativeLong', ], 'NfsExported' => [ 'shape' => 'PtolemyBoolean', ], 'SnapshotCapacityUsed' => [ 'shape' => 'NonNegativeLong', ], 'MaxP95Performance' => [ 'shape' => 'MaxP95Performance', ], 'Recommendations' => [ 'shape' => 'Recommendations', ], 'RecommendationStatus' => [ 'shape' => 'RecommendationStatus', ], 'LunCount' => [ 'shape' => 'NonNegativeLong', ], ], ], 'NetAppONTAPVolumes' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetAppONTAPVolume', ], ], 'NetworkInterfaceArn' => [ 'type' => 'string', 'max' => 128, 'pattern' => '^arn:aws[\\-a-z]{0,}:ec2:[a-z\\-0-9]*:[0-9]{12}:network-interface/eni-[0-9a-f]+$', ], 'NextToken' => [ 'type' => 'string', 'max' => 65535, 'pattern' => '[a-zA-Z0-9=_-]+', ], 'NfsMountOptions' => [ 'type' => 'structure', 'members' => [ 'Version' => [ 'shape' => 'NfsVersion', ], ], ], 'NfsSubdirectory' => [ 'type' => 'string', 'max' => 4096, 'pattern' => '^[a-zA-Z0-9_\\-\\+\\./\\(\\)\\p{Zs}]+$', ], 'NfsVersion' => [ 'type' => 'string', 'enum' => [ 'AUTOMATIC', 'NFS3', 'NFS4_0', 'NFS4_1', ], ], 'NonNegativeDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'NonNegativeLong' => [ 'type' => 'long', 'box' => true, 'min' => 0, ], 'ObjectStorageAccessKey' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'pattern' => '^.*$', ], 'ObjectStorageBucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-zA-Z0-9_\\-\\+\\./\\(\\)\\$\\p{Zs}]+$', ], 'ObjectStorageCertificate' => [ 'type' => 'blob', 'max' => 32768, ], 'ObjectStorageSecretKey' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'pattern' => '^.*$', 'sensitive' => true, ], 'ObjectStorageServerPort' => [ 'type' => 'integer', 'box' => true, 'max' => 65536, 'min' => 1, ], 'ObjectStorageServerProtocol' => [ 'type' => 'string', 'enum' => [ 'HTTPS', 'HTTP', ], ], 'ObjectTags' => [ 'type' => 'string', 'enum' => [ 'PRESERVE', 'NONE', ], ], 'ObjectVersionIds' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'NONE', ], ], 'OnPremConfig' => [ 'type' => 'structure', 'required' => [ 'AgentArns', ], 'members' => [ 'AgentArns' => [ 'shape' => 'AgentArnList', ], ], ], 'Operator' => [ 'type' => 'string', 'enum' => [ 'Equals', 'NotEquals', 'In', 'LessThanOrEqual', 'LessThan', 'GreaterThanOrEqual', 'GreaterThan', 'Contains', 'NotContains', 'BeginsWith', ], ], 'Options' => [ 'type' => 'structure', 'members' => [ 'VerifyMode' => [ 'shape' => 'VerifyMode', ], 'OverwriteMode' => [ 'shape' => 'OverwriteMode', ], 'Atime' => [ 'shape' => 'Atime', ], 'Mtime' => [ 'shape' => 'Mtime', ], 'Uid' => [ 'shape' => 'Uid', ], 'Gid' => [ 'shape' => 'Gid', ], 'PreserveDeletedFiles' => [ 'shape' => 'PreserveDeletedFiles', ], 'PreserveDevices' => [ 'shape' => 'PreserveDevices', ], 'PosixPermissions' => [ 'shape' => 'PosixPermissions', ], 'BytesPerSecond' => [ 'shape' => 'BytesPerSecond', ], 'TaskQueueing' => [ 'shape' => 'TaskQueueing', ], 'LogLevel' => [ 'shape' => 'LogLevel', ], 'TransferMode' => [ 'shape' => 'TransferMode', ], 'SecurityDescriptorCopyFlags' => [ 'shape' => 'SmbSecurityDescriptorCopyFlags', ], 'ObjectTags' => [ 'shape' => 'ObjectTags', ], ], ], 'OutputTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagListEntry', ], 'max' => 55, 'min' => 0, ], 'OverwriteMode' => [ 'type' => 'string', 'enum' => [ 'ALWAYS', 'NEVER', ], ], 'P95Metrics' => [ 'type' => 'structure', 'members' => [ 'IOPS' => [ 'shape' => 'IOPS', ], 'Throughput' => [ 'shape' => 'Throughput', ], 'Latency' => [ 'shape' => 'Latency', ], ], ], 'PLSecurityGroupArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Ec2SecurityGroupArn', ], 'max' => 1, 'min' => 1, ], 'PLSubnetArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Ec2SubnetArn', ], 'max' => 1, 'min' => 1, ], 'PhaseStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'SUCCESS', 'ERROR', ], ], 'Platform' => [ 'type' => 'structure', 'members' => [ 'Version' => [ 'shape' => 'AgentVersion', ], ], ], 'PosixPermissions' => [ 'type' => 'string', 'enum' => [ 'NONE', 'PRESERVE', ], ], 'PreserveDeletedFiles' => [ 'type' => 'string', 'enum' => [ 'PRESERVE', 'REMOVE', ], ], 'PreserveDevices' => [ 'type' => 'string', 'enum' => [ 'NONE', 'PRESERVE', ], ], 'PrivateLinkConfig' => [ 'type' => 'structure', 'members' => [ 'VpcEndpointId' => [ 'shape' => 'VpcEndpointId', ], 'PrivateLinkEndpoint' => [ 'shape' => 'Endpoint', ], 'SubnetArns' => [ 'shape' => 'PLSubnetArnList', ], 'SecurityGroupArns' => [ 'shape' => 'PLSecurityGroupArnList', ], ], ], 'PtolemyBoolean' => [ 'type' => 'boolean', ], 'PtolemyPassword' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '^(?!.*[:\\"][^:"]*$).+$', 'sensitive' => true, ], 'PtolemyString' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '^.{0,1024}$', ], 'PtolemyUUID' => [ 'type' => 'string', 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'PtolemyUsername' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '^(?!.*[:\\"][^:"]*$).+$', 'sensitive' => true, ], 'QopConfiguration' => [ 'type' => 'structure', 'members' => [ 'RpcProtection' => [ 'shape' => 'HdfsRpcProtection', ], 'DataTransferProtection' => [ 'shape' => 'HdfsDataTransferProtection', ], ], ], 'Recommendation' => [ 'type' => 'structure', 'members' => [ 'StorageType' => [ 'shape' => 'PtolemyString', ], 'StorageConfiguration' => [ 'shape' => 'RecommendationsConfigMap', ], 'EstimatedMonthlyStorageCost' => [ 'shape' => 'PtolemyString', ], ], ], 'RecommendationStatus' => [ 'type' => 'string', 'enum' => [ 'NONE', 'IN_PROGRESS', 'COMPLETED', 'FAILED', ], ], 'Recommendations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Recommendation', ], ], 'RecommendationsConfigMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'PtolemyString', ], 'value' => [ 'shape' => 'PtolemyString', ], ], 'RemoveStorageSystemRequest' => [ 'type' => 'structure', 'required' => [ 'StorageSystemArn', ], 'members' => [ 'StorageSystemArn' => [ 'shape' => 'StorageSystemArn', ], ], ], 'RemoveStorageSystemResponse' => [ 'type' => 'structure', 'members' => [], ], 'ReportDestination' => [ 'type' => 'structure', 'members' => [ 'S3' => [ 'shape' => 'ReportDestinationS3', ], ], ], 'ReportDestinationS3' => [ 'type' => 'structure', 'required' => [ 'S3BucketArn', 'BucketAccessRoleArn', ], 'members' => [ 'Subdirectory' => [ 'shape' => 'S3Subdirectory', ], 'S3BucketArn' => [ 'shape' => 'S3BucketArn', ], 'BucketAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'ReportLevel' => [ 'type' => 'string', 'enum' => [ 'ERRORS_ONLY', 'SUCCESSES_AND_ERRORS', ], ], 'ReportOutputType' => [ 'type' => 'string', 'enum' => [ 'SUMMARY_ONLY', 'STANDARD', ], ], 'ReportOverride' => [ 'type' => 'structure', 'members' => [ 'ReportLevel' => [ 'shape' => 'ReportLevel', ], ], ], 'ReportOverrides' => [ 'type' => 'structure', 'members' => [ 'Transferred' => [ 'shape' => 'ReportOverride', ], 'Verified' => [ 'shape' => 'ReportOverride', ], 'Deleted' => [ 'shape' => 'ReportOverride', ], 'Skipped' => [ 'shape' => 'ReportOverride', ], ], ], 'ReportResult' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'PhaseStatus', ], 'ErrorCode' => [ 'shape' => 'string', ], 'ErrorDetail' => [ 'shape' => 'string', ], ], ], 'ResourceDetails' => [ 'type' => 'structure', 'members' => [ 'NetAppONTAPSVMs' => [ 'shape' => 'NetAppONTAPSVMs', ], 'NetAppONTAPVolumes' => [ 'shape' => 'NetAppONTAPVolumes', ], 'NetAppONTAPClusters' => [ 'shape' => 'NetAppONTAPClusters', ], ], ], 'ResourceFilters' => [ 'type' => 'map', 'key' => [ 'shape' => 'DiscoveryResourceFilter', ], 'value' => [ 'shape' => 'FilterMembers', ], ], 'ResourceId' => [ 'type' => 'string', 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'ResourceIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceId', ], 'max' => 100, 'min' => 1, ], 'ResourceMetrics' => [ 'type' => 'structure', 'members' => [ 'Timestamp' => [ 'shape' => 'Timestamp', ], 'P95Metrics' => [ 'shape' => 'P95Metrics', ], 'Capacity' => [ 'shape' => 'Capacity', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'ResourceType' => [ 'shape' => 'DiscoveryResourceType', ], ], ], 'S3BucketArn' => [ 'type' => 'string', 'max' => 156, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):(s3|s3-outposts):[a-z\\-0-9]*:[0-9]*:.*$', ], 'S3Config' => [ 'type' => 'structure', 'required' => [ 'BucketAccessRoleArn', ], 'members' => [ 'BucketAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'S3ManifestConfig' => [ 'type' => 'structure', 'required' => [ 'ManifestObjectPath', 'BucketAccessRoleArn', 'S3BucketArn', ], 'members' => [ 'ManifestObjectPath' => [ 'shape' => 'S3Subdirectory', ], 'BucketAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'S3BucketArn' => [ 'shape' => 'S3BucketArn', ], 'ManifestObjectVersionId' => [ 'shape' => 'S3ObjectVersionId', ], ], ], 'S3ObjectVersionId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^.+$', ], 'S3StorageClass' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'STANDARD_IA', 'ONEZONE_IA', 'INTELLIGENT_TIERING', 'GLACIER', 'DEEP_ARCHIVE', 'OUTPOSTS', 'GLACIER_INSTANT_RETRIEVAL', ], ], 'S3Subdirectory' => [ 'type' => 'string', 'max' => 4096, 'pattern' => '^[a-zA-Z0-9_\\-\\+\\./\\(\\)\\p{Zs}]*$', ], 'ScheduleDisabledBy' => [ 'type' => 'string', 'enum' => [ 'USER', 'SERVICE', ], ], 'ScheduleDisabledReason' => [ 'type' => 'string', 'max' => 8192, 'pattern' => '^[\\w\\s.,\'?!:;\\/=|<>()-]*$', ], 'ScheduleExpressionCron' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^[a-zA-Z0-9\\ \\_\\*\\?\\,\\|\\^\\-\\/\\#\\s\\(\\)\\+]*$', ], 'ScheduleStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'SecretsManagerArn' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):secretsmanager:[a-z\\-0-9]+:[0-9]{12}:secret:.*', ], 'ServerHostname' => [ 'type' => 'string', 'max' => 255, 'pattern' => '^(([a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)*([A-Za-z0-9\\-]*[A-Za-z0-9])$', ], 'SmbDomain' => [ 'type' => 'string', 'max' => 253, 'pattern' => '^[A-Za-z0-9]((\\.|-+)?[A-Za-z0-9]){0,252}$', ], 'SmbMountOptions' => [ 'type' => 'structure', 'members' => [ 'Version' => [ 'shape' => 'SmbVersion', ], ], ], 'SmbPassword' => [ 'type' => 'string', 'max' => 104, 'pattern' => '^.{0,104}$', 'sensitive' => true, ], 'SmbSecurityDescriptorCopyFlags' => [ 'type' => 'string', 'enum' => [ 'NONE', 'OWNER_DACL', 'OWNER_DACL_SACL', ], ], 'SmbSubdirectory' => [ 'type' => 'string', 'max' => 4096, 'pattern' => '^[a-zA-Z0-9_\\-\\+\\./\\(\\)\\$\\p{Zs}]+$', ], 'SmbUser' => [ 'type' => 'string', 'max' => 104, 'pattern' => '^[^\\x5B\\x5D\\\\/:;|=,+*?]{1,104}$', ], 'SmbVersion' => [ 'type' => 'string', 'enum' => [ 'AUTOMATIC', 'SMB2', 'SMB3', 'SMB1', 'SMB2_0', ], ], 'SourceManifestConfig' => [ 'type' => 'structure', 'required' => [ 'S3', ], 'members' => [ 'S3' => [ 'shape' => 'S3ManifestConfig', ], ], ], 'SourceNetworkInterfaceArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkInterfaceArn', ], ], 'StartDiscoveryJobRequest' => [ 'type' => 'structure', 'required' => [ 'StorageSystemArn', 'CollectionDurationMinutes', 'ClientToken', ], 'members' => [ 'StorageSystemArn' => [ 'shape' => 'StorageSystemArn', ], 'CollectionDurationMinutes' => [ 'shape' => 'CollectionDurationMinutes', ], 'ClientToken' => [ 'shape' => 'PtolemyUUID', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'InputTagList', ], ], ], 'StartDiscoveryJobResponse' => [ 'type' => 'structure', 'members' => [ 'DiscoveryJobArn' => [ 'shape' => 'DiscoveryJobArn', ], ], ], 'StartTaskExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'TaskArn', ], 'members' => [ 'TaskArn' => [ 'shape' => 'TaskArn', ], 'OverrideOptions' => [ 'shape' => 'Options', ], 'Includes' => [ 'shape' => 'FilterList', ], 'Excludes' => [ 'shape' => 'FilterList', ], 'ManifestConfig' => [ 'shape' => 'ManifestConfig', ], 'TaskReportConfig' => [ 'shape' => 'TaskReportConfig', ], 'Tags' => [ 'shape' => 'InputTagList', ], ], ], 'StartTaskExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'TaskExecutionArn' => [ 'shape' => 'TaskExecutionArn', ], ], ], 'StopDiscoveryJobRequest' => [ 'type' => 'structure', 'required' => [ 'DiscoveryJobArn', ], 'members' => [ 'DiscoveryJobArn' => [ 'shape' => 'DiscoveryJobArn', ], ], ], 'StopDiscoveryJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'StorageSystemArn' => [ 'type' => 'string', 'max' => 128, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):datasync:[a-z\\-0-9]+:[0-9]{12}:system/storage-system-[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'StorageSystemConnectivityStatus' => [ 'type' => 'string', 'enum' => [ 'PASS', 'FAIL', 'UNKNOWN', ], ], 'StorageSystemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageSystemListEntry', ], ], 'StorageSystemListEntry' => [ 'type' => 'structure', 'members' => [ 'StorageSystemArn' => [ 'shape' => 'StorageSystemArn', ], 'Name' => [ 'shape' => 'Name', ], ], ], 'StorageVirtualMachineArn' => [ 'type' => 'string', 'max' => 162, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):fsx:[a-z\\-0-9]+:[0-9]{12}:storage-virtual-machine/fs-[0-9a-f]+/svm-[0-9a-f]{17,}$', ], 'TagKey' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\s+=._:/-]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagListEntry' => [ 'type' => 'structure', 'required' => [ 'Key', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'TaggableResourceArn', ], 'Tags' => [ 'shape' => 'InputTagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[a-zA-Z0-9\\s+=._:@/-]+$', ], 'TaggableResourceArn' => [ 'type' => 'string', 'max' => 128, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):datasync:[a-z\\-0-9]+:[0-9]{12}:(agent|task|location|system)/((agent|task|loc)-[a-f0-9]{17}|storage-system-[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})(/execution/exec-[a-f0-9]{17})?$', ], 'TaskArn' => [ 'type' => 'string', 'max' => 128, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):datasync:[a-z\\-0-9]*:[0-9]{12}:task/task-[0-9a-f]{17}$', ], 'TaskExecutionArn' => [ 'type' => 'string', 'max' => 128, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):datasync:[a-z\\-0-9]*:[0-9]{12}:task/task-[0-9a-f]{17}/execution/exec-[0-9a-f]{17}$', ], 'TaskExecutionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaskExecutionListEntry', ], ], 'TaskExecutionListEntry' => [ 'type' => 'structure', 'members' => [ 'TaskExecutionArn' => [ 'shape' => 'TaskExecutionArn', ], 'Status' => [ 'shape' => 'TaskExecutionStatus', ], ], ], 'TaskExecutionResultDetail' => [ 'type' => 'structure', 'members' => [ 'PrepareDuration' => [ 'shape' => 'Duration', ], 'PrepareStatus' => [ 'shape' => 'PhaseStatus', ], 'TotalDuration' => [ 'shape' => 'Duration', ], 'TransferDuration' => [ 'shape' => 'Duration', ], 'TransferStatus' => [ 'shape' => 'PhaseStatus', ], 'VerifyDuration' => [ 'shape' => 'Duration', ], 'VerifyStatus' => [ 'shape' => 'PhaseStatus', ], 'ErrorCode' => [ 'shape' => 'string', ], 'ErrorDetail' => [ 'shape' => 'string', ], ], ], 'TaskExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'QUEUED', 'CANCELLING', 'LAUNCHING', 'PREPARING', 'TRANSFERRING', 'VERIFYING', 'SUCCESS', 'ERROR', ], ], 'TaskFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', 'Operator', ], 'members' => [ 'Name' => [ 'shape' => 'TaskFilterName', ], 'Values' => [ 'shape' => 'FilterValues', ], 'Operator' => [ 'shape' => 'Operator', ], ], ], 'TaskFilterName' => [ 'type' => 'string', 'enum' => [ 'LocationId', 'CreationTime', ], ], 'TaskFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaskFilter', ], ], 'TaskList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaskListEntry', ], ], 'TaskListEntry' => [ 'type' => 'structure', 'members' => [ 'TaskArn' => [ 'shape' => 'TaskArn', ], 'Status' => [ 'shape' => 'TaskStatus', ], 'Name' => [ 'shape' => 'TagValue', ], ], ], 'TaskQueueing' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'TaskReportConfig' => [ 'type' => 'structure', 'members' => [ 'Destination' => [ 'shape' => 'ReportDestination', ], 'OutputType' => [ 'shape' => 'ReportOutputType', ], 'ReportLevel' => [ 'shape' => 'ReportLevel', ], 'ObjectVersionIds' => [ 'shape' => 'ObjectVersionIds', ], 'Overrides' => [ 'shape' => 'ReportOverrides', ], ], ], 'TaskSchedule' => [ 'type' => 'structure', 'required' => [ 'ScheduleExpression', ], 'members' => [ 'ScheduleExpression' => [ 'shape' => 'ScheduleExpressionCron', ], 'Status' => [ 'shape' => 'ScheduleStatus', ], ], ], 'TaskScheduleDetails' => [ 'type' => 'structure', 'members' => [ 'StatusUpdateTime' => [ 'shape' => 'Time', ], 'DisabledReason' => [ 'shape' => 'ScheduleDisabledReason', ], 'DisabledBy' => [ 'shape' => 'ScheduleDisabledBy', ], ], ], 'TaskStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'CREATING', 'QUEUED', 'RUNNING', 'UNAVAILABLE', ], ], 'Throughput' => [ 'type' => 'structure', 'members' => [ 'Read' => [ 'shape' => 'NonNegativeDouble', ], 'Write' => [ 'shape' => 'NonNegativeDouble', ], 'Other' => [ 'shape' => 'NonNegativeDouble', ], 'Total' => [ 'shape' => 'NonNegativeDouble', ], ], ], 'Time' => [ 'type' => 'timestamp', ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TransferMode' => [ 'type' => 'string', 'enum' => [ 'CHANGED', 'ALL', ], ], 'Uid' => [ 'type' => 'string', 'enum' => [ 'NONE', 'INT_VALUE', 'NAME', 'BOTH', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Keys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'TaggableResourceArn', ], 'Keys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAgentRequest' => [ 'type' => 'structure', 'required' => [ 'AgentArn', ], 'members' => [ 'AgentArn' => [ 'shape' => 'AgentArn', ], 'Name' => [ 'shape' => 'TagValue', ], ], ], 'UpdateAgentResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDiscoveryJobRequest' => [ 'type' => 'structure', 'required' => [ 'DiscoveryJobArn', 'CollectionDurationMinutes', ], 'members' => [ 'DiscoveryJobArn' => [ 'shape' => 'DiscoveryJobArn', ], 'CollectionDurationMinutes' => [ 'shape' => 'CollectionDurationMinutes', ], ], ], 'UpdateDiscoveryJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateLocationAzureBlobRequest' => [ 'type' => 'structure', 'required' => [ 'LocationArn', ], 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], 'Subdirectory' => [ 'shape' => 'AzureBlobSubdirectory', ], 'AuthenticationType' => [ 'shape' => 'AzureBlobAuthenticationType', ], 'SasConfiguration' => [ 'shape' => 'AzureBlobSasConfiguration', ], 'BlobType' => [ 'shape' => 'AzureBlobType', ], 'AccessTier' => [ 'shape' => 'AzureAccessTier', ], 'AgentArns' => [ 'shape' => 'AgentArnList', ], ], ], 'UpdateLocationAzureBlobResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateLocationHdfsRequest' => [ 'type' => 'structure', 'required' => [ 'LocationArn', ], 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], 'Subdirectory' => [ 'shape' => 'HdfsSubdirectory', ], 'NameNodes' => [ 'shape' => 'HdfsNameNodeList', ], 'BlockSize' => [ 'shape' => 'HdfsBlockSize', ], 'ReplicationFactor' => [ 'shape' => 'HdfsReplicationFactor', ], 'KmsKeyProviderUri' => [ 'shape' => 'KmsKeyProviderUri', ], 'QopConfiguration' => [ 'shape' => 'QopConfiguration', ], 'AuthenticationType' => [ 'shape' => 'HdfsAuthenticationType', ], 'SimpleUser' => [ 'shape' => 'HdfsUser', ], 'KerberosPrincipal' => [ 'shape' => 'KerberosPrincipal', ], 'KerberosKeytab' => [ 'shape' => 'KerberosKeytabFile', ], 'KerberosKrb5Conf' => [ 'shape' => 'KerberosKrb5ConfFile', ], 'AgentArns' => [ 'shape' => 'AgentArnList', ], ], ], 'UpdateLocationHdfsResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateLocationNfsRequest' => [ 'type' => 'structure', 'required' => [ 'LocationArn', ], 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], 'Subdirectory' => [ 'shape' => 'NfsSubdirectory', ], 'OnPremConfig' => [ 'shape' => 'OnPremConfig', ], 'MountOptions' => [ 'shape' => 'NfsMountOptions', ], ], ], 'UpdateLocationNfsResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateLocationObjectStorageRequest' => [ 'type' => 'structure', 'required' => [ 'LocationArn', ], 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], 'ServerPort' => [ 'shape' => 'ObjectStorageServerPort', ], 'ServerProtocol' => [ 'shape' => 'ObjectStorageServerProtocol', ], 'Subdirectory' => [ 'shape' => 'S3Subdirectory', ], 'AccessKey' => [ 'shape' => 'ObjectStorageAccessKey', ], 'SecretKey' => [ 'shape' => 'ObjectStorageSecretKey', ], 'AgentArns' => [ 'shape' => 'AgentArnList', ], 'ServerCertificate' => [ 'shape' => 'ObjectStorageCertificate', ], ], ], 'UpdateLocationObjectStorageResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateLocationSmbRequest' => [ 'type' => 'structure', 'required' => [ 'LocationArn', ], 'members' => [ 'LocationArn' => [ 'shape' => 'LocationArn', ], 'Subdirectory' => [ 'shape' => 'SmbSubdirectory', ], 'User' => [ 'shape' => 'SmbUser', ], 'Domain' => [ 'shape' => 'SmbDomain', ], 'Password' => [ 'shape' => 'SmbPassword', ], 'AgentArns' => [ 'shape' => 'AgentArnList', ], 'MountOptions' => [ 'shape' => 'SmbMountOptions', ], ], ], 'UpdateLocationSmbResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateStorageSystemRequest' => [ 'type' => 'structure', 'required' => [ 'StorageSystemArn', ], 'members' => [ 'StorageSystemArn' => [ 'shape' => 'StorageSystemArn', ], 'ServerConfiguration' => [ 'shape' => 'DiscoveryServerConfiguration', ], 'AgentArns' => [ 'shape' => 'DiscoveryAgentArnList', ], 'Name' => [ 'shape' => 'Name', ], 'CloudWatchLogGroupArn' => [ 'shape' => 'LogGroupArn', ], 'Credentials' => [ 'shape' => 'Credentials', ], ], ], 'UpdateStorageSystemResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTaskExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'TaskExecutionArn', 'Options', ], 'members' => [ 'TaskExecutionArn' => [ 'shape' => 'TaskExecutionArn', ], 'Options' => [ 'shape' => 'Options', ], ], ], 'UpdateTaskExecutionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTaskRequest' => [ 'type' => 'structure', 'required' => [ 'TaskArn', ], 'members' => [ 'TaskArn' => [ 'shape' => 'TaskArn', ], 'Options' => [ 'shape' => 'Options', ], 'Excludes' => [ 'shape' => 'FilterList', ], 'Schedule' => [ 'shape' => 'TaskSchedule', ], 'Name' => [ 'shape' => 'TagValue', ], 'CloudWatchLogGroupArn' => [ 'shape' => 'LogGroupArn', ], 'Includes' => [ 'shape' => 'FilterList', ], 'ManifestConfig' => [ 'shape' => 'ManifestConfig', ], 'TaskReportConfig' => [ 'shape' => 'TaskReportConfig', ], ], ], 'UpdateTaskResponse' => [ 'type' => 'structure', 'members' => [], ], 'VerifyMode' => [ 'type' => 'string', 'enum' => [ 'POINT_IN_TIME_CONSISTENT', 'ONLY_FILES_TRANSFERRED', 'NONE', ], ], 'VpcEndpointId' => [ 'type' => 'string', 'pattern' => '^vpce-[0-9a-f]{17}$', ], 'long' => [ 'type' => 'long', ], 'string' => [ 'type' => 'string', ], ],];
