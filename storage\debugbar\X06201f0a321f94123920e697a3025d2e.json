{"__meta": {"id": "X06201f0a321f94123920e697a3025d2e", "datetime": "2025-06-30 23:13:00", "utime": **********.271521, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751325179.847831, "end": **********.271536, "duration": 0.4237051010131836, "duration_str": "424ms", "measures": [{"label": "Booting", "start": 1751325179.847831, "relative_start": 0, "end": **********.217439, "relative_end": **********.217439, "duration": 0.36960792541503906, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.217448, "relative_start": 0.3696169853210449, "end": **********.271538, "relative_end": 1.9073486328125e-06, "duration": 0.054090023040771484, "duration_str": "54.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45728024, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0026699999999999996, "accumulated_duration_str": "2.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.245001, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.547}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.256921, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.547, "width_percent": 20.225}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.262517, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.772, "width_percent": 17.228}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1893071852 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2818 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751325027058%7C14%7C1%7Co.clarity.ms%2Fcollect; laravel_session=eyJpdiI6ImpxTklOWXhTYU1XWEJkQTBqVTRXZlE9PSIsInZhbHVlIjoiTXFGZEpQRXlGa212dDlvcjMyZUgzOTJTdTZUUld1Y0RIbVM4elBEck42T2RTa0tjV2NoaTJNekRxM3Vkc3lrR1dOeGt5blA0Ym55U2pNZWY5am1QNC9lMm5SZE1VcGh6OVBneXMxN2Y4Qmx1TWhKckg0OXNOdzJBV1ZmTzVubEw3cmlyUDRwVnhxYzd4NmhqaEJiZThRcHlESndxYTY0WWNKZ1pqeVdFSTZuZnRmZ1BDZy91c3ZCbWloYytBQnpaczBrTmh2S3Z1dllBcHJYMkhWOXM3alFFWENqVkhpc2Rnb1lHSlpwM2dNb0s5cUxpakE5OVNEbEZRZ0VUVDJBWFZKWmZyYjZRM2ZNL1BuR0l5L3BsYXhVbzEwQ1ZlaXYySVUrQnk5a0dvY1BKK2dwQnEzM3ppZnFlaW9XTW9Ga1pSQ29mZktuT3BjSnc5ampzYU1SdTIxNzd4Wms4eUJ6eWdrajg1UUhYS2puSkhMNDVRbEh3WENJazQ2Qzk0Vlk5cE5NYW1qQ0xCbWJBVHZ5ZUlzUzBGRTJCWGliL1ovUHlOejhGSHJzRS9UOC8wK0QvTTQ4WHdveVBkTE1CdHlWcnFrQWdDcU1jR0hscFd0SUgzYWRtbFRaT0pzTGUzSkVDS2k4YlhiU0s3b29aMU5tbWtnL1NUaWZ1VmhxYnZ3ZFoiLCJtYWMiOiJjNTgzZDkxNDlmYmY2OTAwMmNmYTUwNTg0OWMwNDQ3MTVmZWQ0YWFiNjk4ZTJkZDM4YTkzNjhiMzE3YzIyNjlmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlpCSklubjBqc0N4cFFNNnIrZXZYWEE9PSIsInZhbHVlIjoiYUVDYXJXMHFpUnlVc0JhRWxuQUdGMFh5WkdZeHBUZG1PM09hRnFYaDNOV293SnFEaDZrRzR5ZWdGRkhKYURwTkVLck5YbE4xWkxIN2hkdkdvMnNyQ2xQVnVhT0RBZjNpRlovdGdJN2QwSzJ2TWxNVmdIYWVLbFQyZFM4UmVGZU5jdy9VY1V6c3VKY2lZWjQyM0d0LzBHNFFCNSs3WVl3L1hUMFp1K1Z3UHlPVEFSTmpkS2RQMmV2aUIwejBRNzM5Und1NVZ1QWcyakswN2V5aGdTbzFJTEhyTG1WZndQUDJieTRNc3JhQnlTOXBvVTVGcFZ2eHdlUFlkcUt6TVdxa1hvNVYycHBaWGpBY2UxeTA4TTBsSHZIemZVVzhoOVNDS3FhbEFCSVU0M0hWeXJZZlFBYXJKYmxMUTZDTFlXekhpbnNndGh6MzdTZVFwU2d0OFd4R2FnVXFMRUFUVXc5ZUgwQjMzMXBGaDVwZW8wTlNveWQ4bTB0Wm5MU1RuNExTdWphUTd4RHQxbmVFdzNieGZUOVBXbFJPT21HbnZDSW1MdFFWekpoNDJSckZ5aW5pUUdtVTZjYzI5SDZyeW82eWRQamg2NlpYaCtnZFFmWU01ZkpWWGZ0Y2RkQkt3VXUyWk9sNGxjQi9OVGpkV1ZHK01oYlNLS3pKWkVnam5URVAiLCJtYWMiOiI2OTM2OWQzZGVhNTVhYWVmMGI2MmVmNjkzYjg3N2FhN2I5OGFjYmEyYzFhNWExZmVjMTY3ZDdlNWNlOGIwZmZmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlpSNlp3N0t3SXpROVAzWWh3R0VKQ2c9PSIsInZhbHVlIjoiZEVmTy9HTlVLYStZNlFjc25LRkhIV0Rtd2pKa3c2TjhrMHp2M3NDdi8wVEg3WWg0d3grTnhqK0xsdm1ieVBmWXE1cnBUVHEyQjlaTzhCK25qLzRrSXRzcTRjM1hZZ0NIS0p2L2c1MTBUVmw0TTc0QWxGay9XWm5COHB6Sml3Nng3TTR1VEtMWUVrK0NXRVdXeDkzYUtqNmJ5dWZPbThTc3NrZU9ENXB6cnlVY3BNei9HZmtFMXBpTUVWaU5xVFV0aHhxZEQ2VTJZMkgyUlNjUmZ0MmkvcTZIMUhNcWJtUmR6SXl1YXZZVEdxK3hkOGxqM1lrdFRMaWNtUjFPcE5rMkl3U29ZSVhubVF1NXVMWjFuQXNyamh2REpNeHNvekszS1czY0lhS20xUWhWQmFnVE8wMTMvYzBwMFo4dXZKcCtXeE4xVnJnVnpqcmE0eS9RR1VzNmJtSEVmOG4raWpGRk5zL1V6MXcyenFUMmVkYzZ5dEx6WmVZeCtaVXNvYlF4d1pUczZ3RURTOTJhcmFUWUh5ZVVmWWdZRWZNeE9wZ1ZnMEYvTEVEdHdjaFdSS3gyNjAwcTZHZ082U1drRjlUTGsrb1ljNG43WmZLUCtpckJ5RmMvdEJJSzdPQ0Iwc29Uc2Iwa3ozVlhhdUJEdm5LTEVaZ3FlR0JKUXpvbFhZekgiLCJtYWMiOiJkZDMwZGY2ODRmNjIxY2U2Y2NmMjZhNzdmMjhhNDMyOGQyOTIxN2U1ZTJjNjIyZWNmNTIxNzNhZGU4ZThkOGI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893071852\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1236275725 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nMXQGGGSGQFIfGwlrnEYCl0LIB0DtCvcZqQRlbGj</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1236275725\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:13:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVyMFZTK3B5SlBzbm13elN3a0pxeUE9PSIsInZhbHVlIjoiQzBaczMvQ09DZjlSTk9GRi9BMmlDQ0ZUU0hya3U0UTRBVm1CT2lUajBGRkRRTUl2WlhZaWtjbUNQOHMwbVYzTm8vQk9nMC92aUJ6ZDhsUVowSXhYOTZOdkFzMGdzR0MyeWU4LzMrZ1NzeWpnaDBRSURZQ3NHQWtxTG1sLzBjT1Jybk8vNm5aWkVLL2dnTHRYR3BleHRQT2dWcGtLZFVmRGphaXFNQk5GZ0ZzdnFrZ2M4TGwrM1Nqd1BoS1VFSzM5UW5IRkw0VlJsUDI5SDFvOXZyN0pQQ056L0NMNXJ1ZTIvVk9jb2ljb2NLVFVheEkxdlBzQUxpWFJzK3FEbk5PNU9ZZ2wzcis3eklBYk5tQ0xJYXZWbml0MVU4ckRLU2hybWxtbklXVkFGa1ByeHdnRlhwV3hBT1EvUVFmRk1mZjgyMGVMWDhja3lJVlFXY2EwNkRzdEJ4Z1phK3lseXl2L3o2UW9YVm80WVpvMXY4dWpyTXhIWEVadTQ3Y3pHYlF6M3FVd3hEbStYeXJldHcyTzhOM2ZReExYNElLYXIyMDRrREVRODlFbUk0a3llNk9SaTc1WDFLVTAwNFRzQ3Byei9uN2RySFNGSUpFc0pOUlhqY0pnQkJzblhhdndFeDBaYzJqYUhickJzTE1HZEEwQUE4Z3pPT3BIQzRSbDROeVgiLCJtYWMiOiI0ZTg0M2MyMzVhYzgwYTNkMjQ0NDNjNzI5ZjE4YWI5ZDU4NDk3ZGMzMDQ3OWNmYTcyNDE3NmI1ZTg0OGJlYjZkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImVVY1kzTHhqYzdFUSt6ZE9QbTZPa3c9PSIsInZhbHVlIjoiWjNMb01GVGNZd2N4UGtLT0pxZEVibGFRanRXcFVhZGVTUGw2MDlNbmZjNmJIaUpkWTVreUc2b0hEUERhZ3pqQkc5cTdQeXB3ZGRLcTN2TG9aVTByRFo4VGNYWE00T0pYK1JJb3B2MWxLamVVdEZub2VlOE9Yc2tiR1ZXOWV1eDBSbHNMdTNrME9YUWwyLzFwSU8zRStac2lRY2pBd1IyYjBrdkE0bFZPKzh3SkJYOXB4eStoaDlNdXFRN2JwUFhvSFR4V1UxaXlsKy9ncEFEa1BkNE1jeFJYQXpINWNGS0tkeDE5QlRHUmpRaW00V1VDQVZZWWZYMGpQVjVXeEVUYklBZGlVWGkybmhMUWl6a3JmTDBEa01QWFRjbW5BM1F6WkdMN3g0czlLRDR3akZ1OWJiTEVJaENjVFFacnc0cnB1R2hFVHVvTjFVZ1NFVUU1eHFUd1I3V01ucnk1aFdsZnZXazhtMDdJUDhUZ0oySXNDTERSS2c0RGgxeFl3Zld2bUJ3eVAzUlpMYkxaWXErd0lQTHovR2xtQUYxTFBNVU8zMGxOdjVFbGc4S2ZESVNVdDNnSm4vYVRtdTI2RUx4WlhPaVN5ZStSM05GeEtGYjlxd2RJamFUN25kM1ZGdVozZVEzRVBXdFdtbTFHZ3Z2MzFTYjJrT3RjTDYwNHViQXQiLCJtYWMiOiI1MTJlNTgxZTBkNjJmM2IyMGU4ZmFlMTlmMzQzYTlmODU4MzlhNzkwNDE3MTBmMzlhZGI4N2FjYzYwNWQ4ZDI0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVyMFZTK3B5SlBzbm13elN3a0pxeUE9PSIsInZhbHVlIjoiQzBaczMvQ09DZjlSTk9GRi9BMmlDQ0ZUU0hya3U0UTRBVm1CT2lUajBGRkRRTUl2WlhZaWtjbUNQOHMwbVYzTm8vQk9nMC92aUJ6ZDhsUVowSXhYOTZOdkFzMGdzR0MyeWU4LzMrZ1NzeWpnaDBRSURZQ3NHQWtxTG1sLzBjT1Jybk8vNm5aWkVLL2dnTHRYR3BleHRQT2dWcGtLZFVmRGphaXFNQk5GZ0ZzdnFrZ2M4TGwrM1Nqd1BoS1VFSzM5UW5IRkw0VlJsUDI5SDFvOXZyN0pQQ056L0NMNXJ1ZTIvVk9jb2ljb2NLVFVheEkxdlBzQUxpWFJzK3FEbk5PNU9ZZ2wzcis3eklBYk5tQ0xJYXZWbml0MVU4ckRLU2hybWxtbklXVkFGa1ByeHdnRlhwV3hBT1EvUVFmRk1mZjgyMGVMWDhja3lJVlFXY2EwNkRzdEJ4Z1phK3lseXl2L3o2UW9YVm80WVpvMXY4dWpyTXhIWEVadTQ3Y3pHYlF6M3FVd3hEbStYeXJldHcyTzhOM2ZReExYNElLYXIyMDRrREVRODlFbUk0a3llNk9SaTc1WDFLVTAwNFRzQ3Byei9uN2RySFNGSUpFc0pOUlhqY0pnQkJzblhhdndFeDBaYzJqYUhickJzTE1HZEEwQUE4Z3pPT3BIQzRSbDROeVgiLCJtYWMiOiI0ZTg0M2MyMzVhYzgwYTNkMjQ0NDNjNzI5ZjE4YWI5ZDU4NDk3ZGMzMDQ3OWNmYTcyNDE3NmI1ZTg0OGJlYjZkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImVVY1kzTHhqYzdFUSt6ZE9QbTZPa3c9PSIsInZhbHVlIjoiWjNMb01GVGNZd2N4UGtLT0pxZEVibGFRanRXcFVhZGVTUGw2MDlNbmZjNmJIaUpkWTVreUc2b0hEUERhZ3pqQkc5cTdQeXB3ZGRLcTN2TG9aVTByRFo4VGNYWE00T0pYK1JJb3B2MWxLamVVdEZub2VlOE9Yc2tiR1ZXOWV1eDBSbHNMdTNrME9YUWwyLzFwSU8zRStac2lRY2pBd1IyYjBrdkE0bFZPKzh3SkJYOXB4eStoaDlNdXFRN2JwUFhvSFR4V1UxaXlsKy9ncEFEa1BkNE1jeFJYQXpINWNGS0tkeDE5QlRHUmpRaW00V1VDQVZZWWZYMGpQVjVXeEVUYklBZGlVWGkybmhMUWl6a3JmTDBEa01QWFRjbW5BM1F6WkdMN3g0czlLRDR3akZ1OWJiTEVJaENjVFFacnc0cnB1R2hFVHVvTjFVZ1NFVUU1eHFUd1I3V01ucnk1aFdsZnZXazhtMDdJUDhUZ0oySXNDTERSS2c0RGgxeFl3Zld2bUJ3eVAzUlpMYkxaWXErd0lQTHovR2xtQUYxTFBNVU8zMGxOdjVFbGc4S2ZESVNVdDNnSm4vYVRtdTI2RUx4WlhPaVN5ZStSM05GeEtGYjlxd2RJamFUN25kM1ZGdVozZVEzRVBXdFdtbTFHZ3Z2MzFTYjJrT3RjTDYwNHViQXQiLCJtYWMiOiI1MTJlNTgxZTBkNjJmM2IyMGU4ZmFlMTlmMzQzYTlmODU4MzlhNzkwNDE3MTBmMzlhZGI4N2FjYzYwNWQ4ZDI0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}