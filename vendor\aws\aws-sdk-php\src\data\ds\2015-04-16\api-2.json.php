<?php
// This file was auto-generated from sdk-root/src/data/ds/2015-04-16/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-04-16', 'endpointPrefix' => 'ds', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceAbbreviation' => 'Directory Service', 'serviceFullName' => 'AWS Directory Service', 'serviceId' => 'Directory Service', 'signatureVersion' => 'v4', 'targetPrefix' => 'DirectoryService_20150416', 'uid' => 'ds-2015-04-16', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AcceptSharedDirectory' => [ 'name' => 'AcceptSharedDirectory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AcceptSharedDirectoryRequest', ], 'output' => [ 'shape' => 'AcceptSharedDirectoryResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'DirectoryAlreadySharedException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'AddIpRoutes' => [ 'name' => 'AddIpRoutes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddIpRoutesRequest', ], 'output' => [ 'shape' => 'AddIpRoutesResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'IpRouteLimitExceededException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'AddRegion' => [ 'name' => 'AddRegion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddRegionRequest', ], 'output' => [ 'shape' => 'AddRegionResult', ], 'errors' => [ [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'DirectoryAlreadyInRegionException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'DirectoryDoesNotExistException', ], [ 'shape' => 'RegionLimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'AddTagsToResource' => [ 'name' => 'AddTagsToResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddTagsToResourceRequest', ], 'output' => [ 'shape' => 'AddTagsToResourceResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TagLimitExceededException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CancelSchemaExtension' => [ 'name' => 'CancelSchemaExtension', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelSchemaExtensionRequest', ], 'output' => [ 'shape' => 'CancelSchemaExtensionResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'ConnectDirectory' => [ 'name' => 'ConnectDirectory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ConnectDirectoryRequest', ], 'output' => [ 'shape' => 'ConnectDirectoryResult', ], 'errors' => [ [ 'shape' => 'DirectoryLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CreateAlias' => [ 'name' => 'CreateAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAliasRequest', ], 'output' => [ 'shape' => 'CreateAliasResult', ], 'errors' => [ [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CreateComputer' => [ 'name' => 'CreateComputer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateComputerRequest', ], 'output' => [ 'shape' => 'CreateComputerResult', ], 'errors' => [ [ 'shape' => 'AuthenticationFailedException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CreateConditionalForwarder' => [ 'name' => 'CreateConditionalForwarder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateConditionalForwarderRequest', ], 'output' => [ 'shape' => 'CreateConditionalForwarderResult', ], 'errors' => [ [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CreateDirectory' => [ 'name' => 'CreateDirectory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDirectoryRequest', ], 'output' => [ 'shape' => 'CreateDirectoryResult', ], 'errors' => [ [ 'shape' => 'DirectoryLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CreateLogSubscription' => [ 'name' => 'CreateLogSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLogSubscriptionRequest', ], 'output' => [ 'shape' => 'CreateLogSubscriptionResult', ], 'errors' => [ [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InsufficientPermissionsException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CreateMicrosoftAD' => [ 'name' => 'CreateMicrosoftAD', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateMicrosoftADRequest', ], 'output' => [ 'shape' => 'CreateMicrosoftADResult', ], 'errors' => [ [ 'shape' => 'DirectoryLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'CreateSnapshot' => [ 'name' => 'CreateSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSnapshotRequest', ], 'output' => [ 'shape' => 'CreateSnapshotResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'SnapshotLimitExceededException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CreateTrust' => [ 'name' => 'CreateTrust', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTrustRequest', ], 'output' => [ 'shape' => 'CreateTrustResult', ], 'errors' => [ [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'DeleteConditionalForwarder' => [ 'name' => 'DeleteConditionalForwarder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteConditionalForwarderRequest', ], 'output' => [ 'shape' => 'DeleteConditionalForwarderResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DeleteDirectory' => [ 'name' => 'DeleteDirectory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDirectoryRequest', ], 'output' => [ 'shape' => 'DeleteDirectoryResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DeleteLogSubscription' => [ 'name' => 'DeleteLogSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLogSubscriptionRequest', ], 'output' => [ 'shape' => 'DeleteLogSubscriptionResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DeleteSnapshot' => [ 'name' => 'DeleteSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSnapshotRequest', ], 'output' => [ 'shape' => 'DeleteSnapshotResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DeleteTrust' => [ 'name' => 'DeleteTrust', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTrustRequest', ], 'output' => [ 'shape' => 'DeleteTrustResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'DeregisterCertificate' => [ 'name' => 'DeregisterCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterCertificateRequest', ], 'output' => [ 'shape' => 'DeregisterCertificateResult', ], 'errors' => [ [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'DirectoryDoesNotExistException', ], [ 'shape' => 'CertificateDoesNotExistException', ], [ 'shape' => 'CertificateInUseException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DeregisterEventTopic' => [ 'name' => 'DeregisterEventTopic', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterEventTopicRequest', ], 'output' => [ 'shape' => 'DeregisterEventTopicResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeCertificate' => [ 'name' => 'DescribeCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCertificateRequest', ], 'output' => [ 'shape' => 'DescribeCertificateResult', ], 'errors' => [ [ 'shape' => 'DirectoryDoesNotExistException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'CertificateDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeClientAuthenticationSettings' => [ 'name' => 'DescribeClientAuthenticationSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeClientAuthenticationSettingsRequest', ], 'output' => [ 'shape' => 'DescribeClientAuthenticationSettingsResult', ], 'errors' => [ [ 'shape' => 'DirectoryDoesNotExistException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeConditionalForwarders' => [ 'name' => 'DescribeConditionalForwarders', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConditionalForwardersRequest', ], 'output' => [ 'shape' => 'DescribeConditionalForwardersResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeDirectories' => [ 'name' => 'DescribeDirectories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDirectoriesRequest', ], 'output' => [ 'shape' => 'DescribeDirectoriesResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeDomainControllers' => [ 'name' => 'DescribeDomainControllers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDomainControllersRequest', ], 'output' => [ 'shape' => 'DescribeDomainControllersResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'DescribeEventTopics' => [ 'name' => 'DescribeEventTopics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventTopicsRequest', ], 'output' => [ 'shape' => 'DescribeEventTopicsResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeLDAPSSettings' => [ 'name' => 'DescribeLDAPSSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLDAPSSettingsRequest', ], 'output' => [ 'shape' => 'DescribeLDAPSSettingsResult', ], 'errors' => [ [ 'shape' => 'DirectoryDoesNotExistException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeRegions' => [ 'name' => 'DescribeRegions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRegionsRequest', ], 'output' => [ 'shape' => 'DescribeRegionsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DirectoryDoesNotExistException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeSettings' => [ 'name' => 'DescribeSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSettingsRequest', ], 'output' => [ 'shape' => 'DescribeSettingsResult', ], 'errors' => [ [ 'shape' => 'DirectoryDoesNotExistException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeSharedDirectories' => [ 'name' => 'DescribeSharedDirectories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSharedDirectoriesRequest', ], 'output' => [ 'shape' => 'DescribeSharedDirectoriesResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeSnapshots' => [ 'name' => 'DescribeSnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSnapshotsRequest', ], 'output' => [ 'shape' => 'DescribeSnapshotsResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeTrusts' => [ 'name' => 'DescribeTrusts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTrustsRequest', ], 'output' => [ 'shape' => 'DescribeTrustsResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'DescribeUpdateDirectory' => [ 'name' => 'DescribeUpdateDirectory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeUpdateDirectoryRequest', ], 'output' => [ 'shape' => 'DescribeUpdateDirectoryResult', ], 'errors' => [ [ 'shape' => 'DirectoryDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'DisableClientAuthentication' => [ 'name' => 'DisableClientAuthentication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisableClientAuthenticationRequest', ], 'output' => [ 'shape' => 'DisableClientAuthenticationResult', ], 'errors' => [ [ 'shape' => 'DirectoryDoesNotExistException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InvalidClientAuthStatusException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DisableLDAPS' => [ 'name' => 'DisableLDAPS', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisableLDAPSRequest', ], 'output' => [ 'shape' => 'DisableLDAPSResult', ], 'errors' => [ [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'DirectoryDoesNotExistException', ], [ 'shape' => 'InvalidLDAPSStatusException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DisableRadius' => [ 'name' => 'DisableRadius', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisableRadiusRequest', ], 'output' => [ 'shape' => 'DisableRadiusResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DisableSso' => [ 'name' => 'DisableSso', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisableSsoRequest', ], 'output' => [ 'shape' => 'DisableSsoResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InsufficientPermissionsException', ], [ 'shape' => 'AuthenticationFailedException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'EnableClientAuthentication' => [ 'name' => 'EnableClientAuthentication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnableClientAuthenticationRequest', ], 'output' => [ 'shape' => 'EnableClientAuthenticationResult', ], 'errors' => [ [ 'shape' => 'DirectoryDoesNotExistException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InvalidClientAuthStatusException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoAvailableCertificateException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'EnableLDAPS' => [ 'name' => 'EnableLDAPS', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnableLDAPSRequest', ], 'output' => [ 'shape' => 'EnableLDAPSResult', ], 'errors' => [ [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'DirectoryDoesNotExistException', ], [ 'shape' => 'NoAvailableCertificateException', ], [ 'shape' => 'InvalidLDAPSStatusException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'EnableRadius' => [ 'name' => 'EnableRadius', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnableRadiusRequest', ], 'output' => [ 'shape' => 'EnableRadiusResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'EnableSso' => [ 'name' => 'EnableSso', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnableSsoRequest', ], 'output' => [ 'shape' => 'EnableSsoResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InsufficientPermissionsException', ], [ 'shape' => 'AuthenticationFailedException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'GetDirectoryLimits' => [ 'name' => 'GetDirectoryLimits', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDirectoryLimitsRequest', ], 'output' => [ 'shape' => 'GetDirectoryLimitsResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'GetSnapshotLimits' => [ 'name' => 'GetSnapshotLimits', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSnapshotLimitsRequest', ], 'output' => [ 'shape' => 'GetSnapshotLimitsResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'ListCertificates' => [ 'name' => 'ListCertificates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCertificatesRequest', ], 'output' => [ 'shape' => 'ListCertificatesResult', ], 'errors' => [ [ 'shape' => 'DirectoryDoesNotExistException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'ListIpRoutes' => [ 'name' => 'ListIpRoutes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListIpRoutesRequest', ], 'output' => [ 'shape' => 'ListIpRoutesResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'ListLogSubscriptions' => [ 'name' => 'ListLogSubscriptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListLogSubscriptionsRequest', ], 'output' => [ 'shape' => 'ListLogSubscriptionsResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'ListSchemaExtensions' => [ 'name' => 'ListSchemaExtensions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSchemaExtensionsRequest', ], 'output' => [ 'shape' => 'ListSchemaExtensionsResult', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'RegisterCertificate' => [ 'name' => 'RegisterCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterCertificateRequest', ], 'output' => [ 'shape' => 'RegisterCertificateResult', ], 'errors' => [ [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'DirectoryDoesNotExistException', ], [ 'shape' => 'InvalidCertificateException', ], [ 'shape' => 'CertificateLimitExceededException', ], [ 'shape' => 'CertificateAlreadyExistsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'RegisterEventTopic' => [ 'name' => 'RegisterEventTopic', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterEventTopicRequest', ], 'output' => [ 'shape' => 'RegisterEventTopicResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'RejectSharedDirectory' => [ 'name' => 'RejectSharedDirectory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RejectSharedDirectoryRequest', ], 'output' => [ 'shape' => 'RejectSharedDirectoryResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'DirectoryAlreadySharedException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'RemoveIpRoutes' => [ 'name' => 'RemoveIpRoutes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveIpRoutesRequest', ], 'output' => [ 'shape' => 'RemoveIpRoutesResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'RemoveRegion' => [ 'name' => 'RemoveRegion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveRegionRequest', ], 'output' => [ 'shape' => 'RemoveRegionResult', ], 'errors' => [ [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'DirectoryDoesNotExistException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'RemoveTagsFromResource' => [ 'name' => 'RemoveTagsFromResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveTagsFromResourceRequest', ], 'output' => [ 'shape' => 'RemoveTagsFromResourceResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'ResetUserPassword' => [ 'name' => 'ResetUserPassword', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResetUserPasswordRequest', ], 'output' => [ 'shape' => 'ResetUserPasswordResult', ], 'errors' => [ [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'UserDoesNotExistException', ], [ 'shape' => 'InvalidPasswordException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'RestoreFromSnapshot' => [ 'name' => 'RestoreFromSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreFromSnapshotRequest', ], 'output' => [ 'shape' => 'RestoreFromSnapshotResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'ShareDirectory' => [ 'name' => 'ShareDirectory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ShareDirectoryRequest', ], 'output' => [ 'shape' => 'ShareDirectoryResult', ], 'errors' => [ [ 'shape' => 'DirectoryAlreadySharedException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidTargetException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ShareLimitExceededException', ], [ 'shape' => 'OrganizationsException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ServiceException', ], ], ], 'StartSchemaExtension' => [ 'name' => 'StartSchemaExtension', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartSchemaExtensionRequest', ], 'output' => [ 'shape' => 'StartSchemaExtensionResult', ], 'errors' => [ [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'SnapshotLimitExceededException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'UnshareDirectory' => [ 'name' => 'UnshareDirectory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UnshareDirectoryRequest', ], 'output' => [ 'shape' => 'UnshareDirectoryResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidTargetException', ], [ 'shape' => 'DirectoryNotSharedException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'UpdateConditionalForwarder' => [ 'name' => 'UpdateConditionalForwarder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateConditionalForwarderRequest', ], 'output' => [ 'shape' => 'UpdateConditionalForwarderResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'UpdateDirectorySetup' => [ 'name' => 'UpdateDirectorySetup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDirectorySetupRequest', ], 'output' => [ 'shape' => 'UpdateDirectorySetupResult', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'DirectoryInDesiredStateException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'SnapshotLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DirectoryDoesNotExistException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'UpdateNumberOfDomainControllers' => [ 'name' => 'UpdateNumberOfDomainControllers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateNumberOfDomainControllersRequest', ], 'output' => [ 'shape' => 'UpdateNumberOfDomainControllersResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'DomainControllerLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'UpdateRadius' => [ 'name' => 'UpdateRadius', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRadiusRequest', ], 'output' => [ 'shape' => 'UpdateRadiusResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'UpdateSettings' => [ 'name' => 'UpdateSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSettingsRequest', ], 'output' => [ 'shape' => 'UpdateSettingsResult', ], 'errors' => [ [ 'shape' => 'DirectoryDoesNotExistException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'IncompatibleSettingsException', ], [ 'shape' => 'UnsupportedSettingsException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'UpdateTrust' => [ 'name' => 'UpdateTrust', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTrustRequest', ], 'output' => [ 'shape' => 'UpdateTrustResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'VerifyTrust' => [ 'name' => 'VerifyTrust', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'VerifyTrustRequest', ], 'output' => [ 'shape' => 'VerifyTrustResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], ], 'shapes' => [ 'AcceptSharedDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'SharedDirectoryId', ], 'members' => [ 'SharedDirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'AcceptSharedDirectoryResult' => [ 'type' => 'structure', 'members' => [ 'SharedDirectory' => [ 'shape' => 'SharedDirectory', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'AccessUrl' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'AddIpRoutesRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'IpRoutes', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'IpRoutes' => [ 'shape' => 'IpRoutes', ], 'UpdateSecurityGroupForDirectoryControllers' => [ 'shape' => 'UpdateSecurityGroupForDirectoryControllers', ], ], ], 'AddIpRoutesResult' => [ 'type' => 'structure', 'members' => [], ], 'AddRegionRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'RegionName', 'VPCSettings', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RegionName' => [ 'shape' => 'RegionName', ], 'VPCSettings' => [ 'shape' => 'DirectoryVpcSettings', ], ], ], 'AddRegionResult' => [ 'type' => 'structure', 'members' => [], ], 'AddTagsToResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'Tags', ], 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceId', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'AddTagsToResourceResult' => [ 'type' => 'structure', 'members' => [], ], 'AddedDateTime' => [ 'type' => 'timestamp', ], 'AdditionalRegions' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegionName', ], ], 'AliasName' => [ 'type' => 'string', 'max' => 62, 'min' => 1, 'pattern' => '^(?!D-|d-)([\\da-zA-Z]+)([-]*[\\da-zA-Z])*', ], 'Attribute' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'AttributeName', ], 'Value' => [ 'shape' => 'AttributeValue', ], ], ], 'AttributeName' => [ 'type' => 'string', 'min' => 1, ], 'AttributeValue' => [ 'type' => 'string', ], 'Attributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Attribute', ], ], 'AuthenticationFailedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'AvailabilityZone' => [ 'type' => 'string', ], 'AvailabilityZones' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailabilityZone', ], ], 'CancelSchemaExtensionRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'SchemaExtensionId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'SchemaExtensionId' => [ 'shape' => 'SchemaExtensionId', ], ], ], 'CancelSchemaExtensionResult' => [ 'type' => 'structure', 'members' => [], ], 'Certificate' => [ 'type' => 'structure', 'members' => [ 'CertificateId' => [ 'shape' => 'CertificateId', ], 'State' => [ 'shape' => 'CertificateState', ], 'StateReason' => [ 'shape' => 'CertificateStateReason', ], 'CommonName' => [ 'shape' => 'CertificateCN', ], 'RegisteredDateTime' => [ 'shape' => 'CertificateRegisteredDateTime', ], 'ExpiryDateTime' => [ 'shape' => 'CertificateExpiryDateTime', ], 'Type' => [ 'shape' => 'CertificateType', ], 'ClientCertAuthSettings' => [ 'shape' => 'ClientCertAuthSettings', ], ], ], 'CertificateAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'CertificateCN' => [ 'type' => 'string', ], 'CertificateData' => [ 'type' => 'string', 'max' => 8192, 'min' => 1, ], 'CertificateDoesNotExistException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'CertificateExpiryDateTime' => [ 'type' => 'timestamp', ], 'CertificateId' => [ 'type' => 'string', 'pattern' => '^c-[0-9a-f]{10}$', ], 'CertificateInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'CertificateInfo' => [ 'type' => 'structure', 'members' => [ 'CertificateId' => [ 'shape' => 'CertificateId', ], 'CommonName' => [ 'shape' => 'CertificateCN', ], 'State' => [ 'shape' => 'CertificateState', ], 'ExpiryDateTime' => [ 'shape' => 'CertificateExpiryDateTime', ], 'Type' => [ 'shape' => 'CertificateType', ], ], ], 'CertificateLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'CertificateRegisteredDateTime' => [ 'type' => 'timestamp', ], 'CertificateState' => [ 'type' => 'string', 'enum' => [ 'Registering', 'Registered', 'RegisterFailed', 'Deregistering', 'Deregistered', 'DeregisterFailed', ], ], 'CertificateStateReason' => [ 'type' => 'string', ], 'CertificateType' => [ 'type' => 'string', 'enum' => [ 'ClientCertAuth', 'ClientLDAPS', ], ], 'CertificatesInfo' => [ 'type' => 'list', 'member' => [ 'shape' => 'CertificateInfo', ], ], 'CidrIp' => [ 'type' => 'string', 'pattern' => '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\\/([1-9]|[1-2][0-9]|3[0-2]))$', ], 'CidrIps' => [ 'type' => 'list', 'member' => [ 'shape' => 'CidrIp', ], ], 'ClientAuthenticationSettingInfo' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ClientAuthenticationType', ], 'Status' => [ 'shape' => 'ClientAuthenticationStatus', ], 'LastUpdatedDateTime' => [ 'shape' => 'LastUpdatedDateTime', ], ], ], 'ClientAuthenticationSettingsInfo' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClientAuthenticationSettingInfo', ], ], 'ClientAuthenticationStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'ClientAuthenticationType' => [ 'type' => 'string', 'enum' => [ 'SmartCard', 'SmartCardOrPassword', ], ], 'ClientCertAuthSettings' => [ 'type' => 'structure', 'members' => [ 'OCSPUrl' => [ 'shape' => 'OCSPUrl', ], ], ], 'ClientException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'CloudOnlyDirectoriesLimitReached' => [ 'type' => 'boolean', ], 'Computer' => [ 'type' => 'structure', 'members' => [ 'ComputerId' => [ 'shape' => 'SID', ], 'ComputerName' => [ 'shape' => 'ComputerName', ], 'ComputerAttributes' => [ 'shape' => 'Attributes', ], ], ], 'ComputerName' => [ 'type' => 'string', 'max' => 15, 'min' => 1, ], 'ComputerPassword' => [ 'type' => 'string', 'max' => 64, 'min' => 8, 'pattern' => '[\\u0020-\\u00FF]+', 'sensitive' => true, ], 'ConditionalForwarder' => [ 'type' => 'structure', 'members' => [ 'RemoteDomainName' => [ 'shape' => 'RemoteDomainName', ], 'DnsIpAddrs' => [ 'shape' => 'DnsIpAddrs', ], 'ReplicationScope' => [ 'shape' => 'ReplicationScope', ], ], ], 'ConditionalForwarders' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConditionalForwarder', ], ], 'ConnectDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Password', 'Size', 'ConnectSettings', ], 'members' => [ 'Name' => [ 'shape' => 'DirectoryName', ], 'ShortName' => [ 'shape' => 'DirectoryShortName', ], 'Password' => [ 'shape' => 'ConnectPassword', ], 'Description' => [ 'shape' => 'Description', ], 'Size' => [ 'shape' => 'DirectorySize', ], 'ConnectSettings' => [ 'shape' => 'DirectoryConnectSettings', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'ConnectDirectoryResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'ConnectPassword' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'sensitive' => true, ], 'ConnectedDirectoriesLimitReached' => [ 'type' => 'boolean', ], 'CreateAliasRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'Alias', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Alias' => [ 'shape' => 'AliasName', ], ], ], 'CreateAliasResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Alias' => [ 'shape' => 'AliasName', ], ], ], 'CreateComputerRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'ComputerName', 'Password', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'ComputerName' => [ 'shape' => 'ComputerName', ], 'Password' => [ 'shape' => 'ComputerPassword', ], 'OrganizationalUnitDistinguishedName' => [ 'shape' => 'OrganizationalUnitDN', ], 'ComputerAttributes' => [ 'shape' => 'Attributes', ], ], ], 'CreateComputerResult' => [ 'type' => 'structure', 'members' => [ 'Computer' => [ 'shape' => 'Computer', ], ], ], 'CreateConditionalForwarderRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'RemoteDomainName', 'DnsIpAddrs', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RemoteDomainName' => [ 'shape' => 'RemoteDomainName', ], 'DnsIpAddrs' => [ 'shape' => 'DnsIpAddrs', ], ], ], 'CreateConditionalForwarderResult' => [ 'type' => 'structure', 'members' => [], ], 'CreateDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Password', 'Size', ], 'members' => [ 'Name' => [ 'shape' => 'DirectoryName', ], 'ShortName' => [ 'shape' => 'DirectoryShortName', ], 'Password' => [ 'shape' => 'Password', ], 'Description' => [ 'shape' => 'Description', ], 'Size' => [ 'shape' => 'DirectorySize', ], 'VpcSettings' => [ 'shape' => 'DirectoryVpcSettings', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateDirectoryResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'CreateLogSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'LogGroupName', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'LogGroupName' => [ 'shape' => 'LogGroupName', ], ], ], 'CreateLogSubscriptionResult' => [ 'type' => 'structure', 'members' => [], ], 'CreateMicrosoftADRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Password', 'VpcSettings', ], 'members' => [ 'Name' => [ 'shape' => 'DirectoryName', ], 'ShortName' => [ 'shape' => 'DirectoryShortName', ], 'Password' => [ 'shape' => 'Password', ], 'Description' => [ 'shape' => 'Description', ], 'VpcSettings' => [ 'shape' => 'DirectoryVpcSettings', ], 'Edition' => [ 'shape' => 'DirectoryEdition', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateMicrosoftADResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'CreateSnapshotBeforeSchemaExtension' => [ 'type' => 'boolean', ], 'CreateSnapshotBeforeUpdate' => [ 'type' => 'boolean', ], 'CreateSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Name' => [ 'shape' => 'SnapshotName', ], ], ], 'CreateSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'SnapshotId' => [ 'shape' => 'SnapshotId', ], ], ], 'CreateTrustRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'RemoteDomainName', 'TrustPassword', 'TrustDirection', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RemoteDomainName' => [ 'shape' => 'RemoteDomainName', ], 'TrustPassword' => [ 'shape' => 'TrustPassword', ], 'TrustDirection' => [ 'shape' => 'TrustDirection', ], 'TrustType' => [ 'shape' => 'TrustType', ], 'ConditionalForwarderIpAddrs' => [ 'shape' => 'DnsIpAddrs', ], 'SelectiveAuth' => [ 'shape' => 'SelectiveAuth', ], ], ], 'CreateTrustResult' => [ 'type' => 'structure', 'members' => [ 'TrustId' => [ 'shape' => 'TrustId', ], ], ], 'CreatedDateTime' => [ 'type' => 'timestamp', ], 'CustomerId' => [ 'type' => 'string', 'pattern' => '^(\\d{12})$', ], 'CustomerUserName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^(?!.*\\\\|.*"|.*\\/|.*\\[|.*\\]|.*:|.*;|.*\\||.*=|.*,|.*\\+|.*\\*|.*\\?|.*<|.*>|.*@).*$', ], 'DeleteAssociatedConditionalForwarder' => [ 'type' => 'boolean', ], 'DeleteConditionalForwarderRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'RemoteDomainName', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RemoteDomainName' => [ 'shape' => 'RemoteDomainName', ], ], ], 'DeleteConditionalForwarderResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'DeleteDirectoryResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'DeleteLogSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'DeleteLogSubscriptionResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'SnapshotId', ], 'members' => [ 'SnapshotId' => [ 'shape' => 'SnapshotId', ], ], ], 'DeleteSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'SnapshotId' => [ 'shape' => 'SnapshotId', ], ], ], 'DeleteTrustRequest' => [ 'type' => 'structure', 'required' => [ 'TrustId', ], 'members' => [ 'TrustId' => [ 'shape' => 'TrustId', ], 'DeleteAssociatedConditionalForwarder' => [ 'shape' => 'DeleteAssociatedConditionalForwarder', ], ], ], 'DeleteTrustResult' => [ 'type' => 'structure', 'members' => [ 'TrustId' => [ 'shape' => 'TrustId', ], ], ], 'DeregisterCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'CertificateId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'CertificateId' => [ 'shape' => 'CertificateId', ], ], ], 'DeregisterCertificateResult' => [ 'type' => 'structure', 'members' => [], ], 'DeregisterEventTopicRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'TopicName', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'TopicName' => [ 'shape' => 'TopicName', ], ], ], 'DeregisterEventTopicResult' => [ 'type' => 'structure', 'members' => [], ], 'DescribeCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'CertificateId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'CertificateId' => [ 'shape' => 'CertificateId', ], ], ], 'DescribeCertificateResult' => [ 'type' => 'structure', 'members' => [ 'Certificate' => [ 'shape' => 'Certificate', ], ], ], 'DescribeClientAuthenticationSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Type' => [ 'shape' => 'ClientAuthenticationType', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'PageLimit', ], ], ], 'DescribeClientAuthenticationSettingsResult' => [ 'type' => 'structure', 'members' => [ 'ClientAuthenticationSettingsInfo' => [ 'shape' => 'ClientAuthenticationSettingsInfo', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeConditionalForwardersRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RemoteDomainNames' => [ 'shape' => 'RemoteDomainNames', ], ], ], 'DescribeConditionalForwardersResult' => [ 'type' => 'structure', 'members' => [ 'ConditionalForwarders' => [ 'shape' => 'ConditionalForwarders', ], ], ], 'DescribeDirectoriesRequest' => [ 'type' => 'structure', 'members' => [ 'DirectoryIds' => [ 'shape' => 'DirectoryIds', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'DescribeDirectoriesResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryDescriptions' => [ 'shape' => 'DirectoryDescriptions', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeDomainControllersRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'DomainControllerIds' => [ 'shape' => 'DomainControllerIds', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'DescribeDomainControllersResult' => [ 'type' => 'structure', 'members' => [ 'DomainControllers' => [ 'shape' => 'DomainControllers', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeEventTopicsRequest' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'TopicNames' => [ 'shape' => 'TopicNames', ], ], ], 'DescribeEventTopicsResult' => [ 'type' => 'structure', 'members' => [ 'EventTopics' => [ 'shape' => 'EventTopics', ], ], ], 'DescribeLDAPSSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Type' => [ 'shape' => 'LDAPSType', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'PageLimit', ], ], ], 'DescribeLDAPSSettingsResult' => [ 'type' => 'structure', 'members' => [ 'LDAPSSettingsInfo' => [ 'shape' => 'LDAPSSettingsInfo', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeRegionsRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RegionName' => [ 'shape' => 'RegionName', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeRegionsResult' => [ 'type' => 'structure', 'members' => [ 'RegionsDescription' => [ 'shape' => 'RegionsDescription', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Status' => [ 'shape' => 'DirectoryConfigurationStatus', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeSettingsResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'SettingEntries' => [ 'shape' => 'SettingEntries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeSharedDirectoriesRequest' => [ 'type' => 'structure', 'required' => [ 'OwnerDirectoryId', ], 'members' => [ 'OwnerDirectoryId' => [ 'shape' => 'DirectoryId', ], 'SharedDirectoryIds' => [ 'shape' => 'DirectoryIds', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'DescribeSharedDirectoriesResult' => [ 'type' => 'structure', 'members' => [ 'SharedDirectories' => [ 'shape' => 'SharedDirectories', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeSnapshotsRequest' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'SnapshotIds' => [ 'shape' => 'SnapshotIds', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'DescribeSnapshotsResult' => [ 'type' => 'structure', 'members' => [ 'Snapshots' => [ 'shape' => 'Snapshots', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeTrustsRequest' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'TrustIds' => [ 'shape' => 'TrustIds', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'DescribeTrustsResult' => [ 'type' => 'structure', 'members' => [ 'Trusts' => [ 'shape' => 'Trusts', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeUpdateDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'UpdateType', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'UpdateType' => [ 'shape' => 'UpdateType', ], 'RegionName' => [ 'shape' => 'RegionName', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeUpdateDirectoryResult' => [ 'type' => 'structure', 'members' => [ 'UpdateActivities' => [ 'shape' => 'UpdateActivities', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^([a-zA-Z0-9_])[\\\\a-zA-Z0-9_@#%*+=:?./!\\s-]*$', ], 'DesiredNumberOfDomainControllers' => [ 'type' => 'integer', 'min' => 2, ], 'DirectoryAlreadyInRegionException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'DirectoryAlreadySharedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'DirectoryConfigurationSettingAllowedValues' => [ 'type' => 'string', ], 'DirectoryConfigurationSettingDataType' => [ 'type' => 'string', ], 'DirectoryConfigurationSettingLastRequestedDateTime' => [ 'type' => 'timestamp', ], 'DirectoryConfigurationSettingLastUpdatedDateTime' => [ 'type' => 'timestamp', ], 'DirectoryConfigurationSettingName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-/. _]*$', ], 'DirectoryConfigurationSettingRequestDetailedStatus' => [ 'type' => 'map', 'key' => [ 'shape' => 'RegionName', ], 'value' => [ 'shape' => 'DirectoryConfigurationStatus', ], ], 'DirectoryConfigurationSettingRequestStatusMessage' => [ 'type' => 'string', ], 'DirectoryConfigurationSettingType' => [ 'type' => 'string', ], 'DirectoryConfigurationSettingValue' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_]*$', ], 'DirectoryConfigurationStatus' => [ 'type' => 'string', 'enum' => [ 'Requested', 'Updating', 'Updated', 'Failed', 'Default', ], ], 'DirectoryConnectSettings' => [ 'type' => 'structure', 'required' => [ 'VpcId', 'SubnetIds', 'CustomerDnsIps', 'CustomerUserName', ], 'members' => [ 'VpcId' => [ 'shape' => 'VpcId', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'CustomerDnsIps' => [ 'shape' => 'DnsIpAddrs', ], 'CustomerUserName' => [ 'shape' => 'UserName', ], ], ], 'DirectoryConnectSettingsDescription' => [ 'type' => 'structure', 'members' => [ 'VpcId' => [ 'shape' => 'VpcId', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'CustomerUserName' => [ 'shape' => 'UserName', ], 'SecurityGroupId' => [ 'shape' => 'SecurityGroupId', ], 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'ConnectIps' => [ 'shape' => 'IpAddrs', ], ], ], 'DirectoryDescription' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Name' => [ 'shape' => 'DirectoryName', ], 'ShortName' => [ 'shape' => 'DirectoryShortName', ], 'Size' => [ 'shape' => 'DirectorySize', ], 'Edition' => [ 'shape' => 'DirectoryEdition', ], 'Alias' => [ 'shape' => 'AliasName', ], 'AccessUrl' => [ 'shape' => 'AccessUrl', ], 'Description' => [ 'shape' => 'Description', ], 'DnsIpAddrs' => [ 'shape' => 'DnsIpAddrs', ], 'Stage' => [ 'shape' => 'DirectoryStage', ], 'ShareStatus' => [ 'shape' => 'ShareStatus', ], 'ShareMethod' => [ 'shape' => 'ShareMethod', ], 'ShareNotes' => [ 'shape' => 'Notes', ], 'LaunchTime' => [ 'shape' => 'LaunchTime', ], 'StageLastUpdatedDateTime' => [ 'shape' => 'LastUpdatedDateTime', ], 'Type' => [ 'shape' => 'DirectoryType', ], 'VpcSettings' => [ 'shape' => 'DirectoryVpcSettingsDescription', ], 'ConnectSettings' => [ 'shape' => 'DirectoryConnectSettingsDescription', ], 'RadiusSettings' => [ 'shape' => 'RadiusSettings', ], 'RadiusStatus' => [ 'shape' => 'RadiusStatus', ], 'StageReason' => [ 'shape' => 'StageReason', ], 'SsoEnabled' => [ 'shape' => 'SsoEnabled', ], 'DesiredNumberOfDomainControllers' => [ 'shape' => 'DesiredNumberOfDomainControllers', ], 'OwnerDirectoryDescription' => [ 'shape' => 'OwnerDirectoryDescription', ], 'RegionsInfo' => [ 'shape' => 'RegionsInfo', ], 'OsVersion' => [ 'shape' => 'OSVersion', ], ], ], 'DirectoryDescriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'DirectoryDescription', ], ], 'DirectoryDoesNotExistException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'DirectoryEdition' => [ 'type' => 'string', 'enum' => [ 'Enterprise', 'Standard', ], ], 'DirectoryId' => [ 'type' => 'string', 'pattern' => '^d-[0-9a-f]{10}$', ], 'DirectoryIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'DirectoryId', ], ], 'DirectoryInDesiredStateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'DirectoryLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'DirectoryLimits' => [ 'type' => 'structure', 'members' => [ 'CloudOnlyDirectoriesLimit' => [ 'shape' => 'Limit', ], 'CloudOnlyDirectoriesCurrentCount' => [ 'shape' => 'Limit', ], 'CloudOnlyDirectoriesLimitReached' => [ 'shape' => 'CloudOnlyDirectoriesLimitReached', ], 'CloudOnlyMicrosoftADLimit' => [ 'shape' => 'Limit', ], 'CloudOnlyMicrosoftADCurrentCount' => [ 'shape' => 'Limit', ], 'CloudOnlyMicrosoftADLimitReached' => [ 'shape' => 'CloudOnlyDirectoriesLimitReached', ], 'ConnectedDirectoriesLimit' => [ 'shape' => 'Limit', ], 'ConnectedDirectoriesCurrentCount' => [ 'shape' => 'Limit', ], 'ConnectedDirectoriesLimitReached' => [ 'shape' => 'ConnectedDirectoriesLimitReached', ], ], ], 'DirectoryName' => [ 'type' => 'string', 'pattern' => '^([a-zA-Z0-9]+[\\\\.-])+([a-zA-Z0-9])+$', ], 'DirectoryNotSharedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'DirectoryShortName' => [ 'type' => 'string', 'pattern' => '^[^\\\\/:*?"<>|.]+[^\\\\/:*?"<>|]*$', ], 'DirectorySize' => [ 'type' => 'string', 'enum' => [ 'Small', 'Large', ], ], 'DirectoryStage' => [ 'type' => 'string', 'enum' => [ 'Requested', 'Creating', 'Created', 'Active', 'Inoperable', 'Impaired', 'Restoring', 'RestoreFailed', 'Deleting', 'Deleted', 'Failed', ], ], 'DirectoryType' => [ 'type' => 'string', 'enum' => [ 'SimpleAD', 'ADConnector', 'MicrosoftAD', 'SharedMicrosoftAD', ], ], 'DirectoryUnavailableException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'DirectoryVpcSettings' => [ 'type' => 'structure', 'required' => [ 'VpcId', 'SubnetIds', ], 'members' => [ 'VpcId' => [ 'shape' => 'VpcId', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], ], ], 'DirectoryVpcSettingsDescription' => [ 'type' => 'structure', 'members' => [ 'VpcId' => [ 'shape' => 'VpcId', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'SecurityGroupId' => [ 'shape' => 'SecurityGroupId', ], 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], ], ], 'DisableClientAuthenticationRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'Type', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Type' => [ 'shape' => 'ClientAuthenticationType', ], ], ], 'DisableClientAuthenticationResult' => [ 'type' => 'structure', 'members' => [], ], 'DisableLDAPSRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'Type', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Type' => [ 'shape' => 'LDAPSType', ], ], ], 'DisableLDAPSResult' => [ 'type' => 'structure', 'members' => [], ], 'DisableRadiusRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'DisableRadiusResult' => [ 'type' => 'structure', 'members' => [], ], 'DisableSsoRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'UserName' => [ 'shape' => 'UserName', ], 'Password' => [ 'shape' => 'ConnectPassword', ], ], ], 'DisableSsoResult' => [ 'type' => 'structure', 'members' => [], ], 'DnsIpAddrs' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpAddr', ], ], 'DomainController' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'DomainControllerId' => [ 'shape' => 'DomainControllerId', ], 'DnsIpAddr' => [ 'shape' => 'IpAddr', ], 'VpcId' => [ 'shape' => 'VpcId', ], 'SubnetId' => [ 'shape' => 'SubnetId', ], 'AvailabilityZone' => [ 'shape' => 'AvailabilityZone', ], 'Status' => [ 'shape' => 'DomainControllerStatus', ], 'StatusReason' => [ 'shape' => 'DomainControllerStatusReason', ], 'LaunchTime' => [ 'shape' => 'LaunchTime', ], 'StatusLastUpdatedDateTime' => [ 'shape' => 'LastUpdatedDateTime', ], ], ], 'DomainControllerId' => [ 'type' => 'string', 'pattern' => '^dc-[0-9a-f]{10}$', ], 'DomainControllerIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainControllerId', ], ], 'DomainControllerLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'DomainControllerStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Active', 'Impaired', 'Restoring', 'Deleting', 'Deleted', 'Failed', ], ], 'DomainControllerStatusReason' => [ 'type' => 'string', ], 'DomainControllers' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainController', ], ], 'EnableClientAuthenticationRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'Type', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Type' => [ 'shape' => 'ClientAuthenticationType', ], ], ], 'EnableClientAuthenticationResult' => [ 'type' => 'structure', 'members' => [], ], 'EnableLDAPSRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'Type', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Type' => [ 'shape' => 'LDAPSType', ], ], ], 'EnableLDAPSResult' => [ 'type' => 'structure', 'members' => [], ], 'EnableRadiusRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'RadiusSettings', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RadiusSettings' => [ 'shape' => 'RadiusSettings', ], ], ], 'EnableRadiusResult' => [ 'type' => 'structure', 'members' => [], ], 'EnableSsoRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'UserName' => [ 'shape' => 'UserName', ], 'Password' => [ 'shape' => 'ConnectPassword', ], ], ], 'EnableSsoResult' => [ 'type' => 'structure', 'members' => [], ], 'EndDateTime' => [ 'type' => 'timestamp', ], 'EntityAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'EntityDoesNotExistException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'EventTopic' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'TopicName' => [ 'shape' => 'TopicName', ], 'TopicArn' => [ 'shape' => 'TopicArn', ], 'CreatedDateTime' => [ 'shape' => 'CreatedDateTime', ], 'Status' => [ 'shape' => 'TopicStatus', ], ], ], 'EventTopics' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventTopic', ], ], 'ExceptionMessage' => [ 'type' => 'string', ], 'GetDirectoryLimitsRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetDirectoryLimitsResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryLimits' => [ 'shape' => 'DirectoryLimits', ], ], ], 'GetSnapshotLimitsRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'GetSnapshotLimitsResult' => [ 'type' => 'structure', 'members' => [ 'SnapshotLimits' => [ 'shape' => 'SnapshotLimits', ], ], ], 'IncompatibleSettingsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'InitiatedBy' => [ 'type' => 'string', ], 'InsufficientPermissionsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'InvalidCertificateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'InvalidClientAuthStatusException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'InvalidLDAPSStatusException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'InvalidNextTokenException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'InvalidPasswordException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'InvalidTargetException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'IpAddr' => [ 'type' => 'string', 'pattern' => '^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$', ], 'IpAddrs' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpAddr', ], ], 'IpRoute' => [ 'type' => 'structure', 'members' => [ 'CidrIp' => [ 'shape' => 'CidrIp', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'IpRouteInfo' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'CidrIp' => [ 'shape' => 'CidrIp', ], 'IpRouteStatusMsg' => [ 'shape' => 'IpRouteStatusMsg', ], 'AddedDateTime' => [ 'shape' => 'AddedDateTime', ], 'IpRouteStatusReason' => [ 'shape' => 'IpRouteStatusReason', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'IpRouteLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'IpRouteStatusMsg' => [ 'type' => 'string', 'enum' => [ 'Adding', 'Added', 'Removing', 'Removed', 'AddFailed', 'RemoveFailed', ], ], 'IpRouteStatusReason' => [ 'type' => 'string', ], 'IpRoutes' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpRoute', ], ], 'IpRoutesInfo' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpRouteInfo', ], ], 'LDAPSSettingInfo' => [ 'type' => 'structure', 'members' => [ 'LDAPSStatus' => [ 'shape' => 'LDAPSStatus', ], 'LDAPSStatusReason' => [ 'shape' => 'LDAPSStatusReason', ], 'LastUpdatedDateTime' => [ 'shape' => 'LastUpdatedDateTime', ], ], ], 'LDAPSSettingsInfo' => [ 'type' => 'list', 'member' => [ 'shape' => 'LDAPSSettingInfo', ], ], 'LDAPSStatus' => [ 'type' => 'string', 'enum' => [ 'Enabling', 'Enabled', 'EnableFailed', 'Disabled', ], ], 'LDAPSStatusReason' => [ 'type' => 'string', ], 'LDAPSType' => [ 'type' => 'string', 'enum' => [ 'Client', ], ], 'LastUpdatedDateTime' => [ 'type' => 'timestamp', ], 'LaunchTime' => [ 'type' => 'timestamp', ], 'LdifContent' => [ 'type' => 'string', 'max' => 500000, 'min' => 1, ], 'Limit' => [ 'type' => 'integer', 'min' => 0, ], 'ListCertificatesRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'PageLimit', ], ], ], 'ListCertificatesResult' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'CertificatesInfo' => [ 'shape' => 'CertificatesInfo', ], ], ], 'ListIpRoutesRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'ListIpRoutesResult' => [ 'type' => 'structure', 'members' => [ 'IpRoutesInfo' => [ 'shape' => 'IpRoutesInfo', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListLogSubscriptionsRequest' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'ListLogSubscriptionsResult' => [ 'type' => 'structure', 'members' => [ 'LogSubscriptions' => [ 'shape' => 'LogSubscriptions', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSchemaExtensionsRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'ListSchemaExtensionsResult' => [ 'type' => 'structure', 'members' => [ 'SchemaExtensionsInfo' => [ 'shape' => 'SchemaExtensionsInfo', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', ], 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'ListTagsForResourceResult' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'Tags', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'LogGroupName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[-._/#A-Za-z0-9]+', ], 'LogSubscription' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'LogGroupName' => [ 'shape' => 'LogGroupName', ], 'SubscriptionCreatedDateTime' => [ 'shape' => 'SubscriptionCreatedDateTime', ], ], ], 'LogSubscriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogSubscription', ], ], 'ManualSnapshotsLimitReached' => [ 'type' => 'boolean', ], 'NextToken' => [ 'type' => 'string', ], 'NoAvailableCertificateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'Notes' => [ 'type' => 'string', 'max' => 1024, 'sensitive' => true, ], 'OCSPUrl' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^(https?|ftp|file|ldaps?)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;()]*[-a-zA-Z0-9+&@#/%=~_|()]', ], 'OSUpdateSettings' => [ 'type' => 'structure', 'members' => [ 'OSVersion' => [ 'shape' => 'OSVersion', ], ], ], 'OSVersion' => [ 'type' => 'string', 'enum' => [ 'SERVER_2012', 'SERVER_2019', ], ], 'OrganizationalUnitDN' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'OrganizationsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'OwnerDirectoryDescription' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'AccountId' => [ 'shape' => 'CustomerId', ], 'DnsIpAddrs' => [ 'shape' => 'DnsIpAddrs', ], 'VpcSettings' => [ 'shape' => 'DirectoryVpcSettingsDescription', ], 'RadiusSettings' => [ 'shape' => 'RadiusSettings', ], 'RadiusStatus' => [ 'shape' => 'RadiusStatus', ], ], ], 'PageLimit' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'Password' => [ 'type' => 'string', 'pattern' => '(?=^.{8,64}$)((?=.*\\d)(?=.*[A-Z])(?=.*[a-z])|(?=.*\\d)(?=.*[^A-Za-z0-9\\s])(?=.*[a-z])|(?=.*[^A-Za-z0-9\\s])(?=.*[A-Z])(?=.*[a-z])|(?=.*\\d)(?=.*[A-Z])(?=.*[^A-Za-z0-9\\s]))^.*', 'sensitive' => true, ], 'PortNumber' => [ 'type' => 'integer', 'max' => 65535, 'min' => 1025, ], 'RadiusAuthenticationProtocol' => [ 'type' => 'string', 'enum' => [ 'PAP', 'CHAP', 'MS-CHAPv1', 'MS-CHAPv2', ], ], 'RadiusDisplayLabel' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'RadiusRetries' => [ 'type' => 'integer', 'max' => 10, 'min' => 0, ], 'RadiusSettings' => [ 'type' => 'structure', 'members' => [ 'RadiusServers' => [ 'shape' => 'Servers', ], 'RadiusPort' => [ 'shape' => 'PortNumber', ], 'RadiusTimeout' => [ 'shape' => 'RadiusTimeout', ], 'RadiusRetries' => [ 'shape' => 'RadiusRetries', ], 'SharedSecret' => [ 'shape' => 'RadiusSharedSecret', ], 'AuthenticationProtocol' => [ 'shape' => 'RadiusAuthenticationProtocol', ], 'DisplayLabel' => [ 'shape' => 'RadiusDisplayLabel', ], 'UseSameUsername' => [ 'shape' => 'UseSameUsername', ], ], ], 'RadiusSharedSecret' => [ 'type' => 'string', 'max' => 512, 'min' => 8, 'pattern' => '^(\\p{LD}|\\p{Punct}| )+$', 'sensitive' => true, ], 'RadiusStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Completed', 'Failed', ], ], 'RadiusTimeout' => [ 'type' => 'integer', 'max' => 20, 'min' => 1, ], 'RegionDescription' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RegionName' => [ 'shape' => 'RegionName', ], 'RegionType' => [ 'shape' => 'RegionType', ], 'Status' => [ 'shape' => 'DirectoryStage', ], 'VpcSettings' => [ 'shape' => 'DirectoryVpcSettings', ], 'DesiredNumberOfDomainControllers' => [ 'shape' => 'DesiredNumberOfDomainControllers', ], 'LaunchTime' => [ 'shape' => 'LaunchTime', ], 'StatusLastUpdatedDateTime' => [ 'shape' => 'StateLastUpdatedDateTime', ], 'LastUpdatedDateTime' => [ 'shape' => 'LastUpdatedDateTime', ], ], ], 'RegionLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'RegionName' => [ 'type' => 'string', 'max' => 32, 'min' => 8, ], 'RegionType' => [ 'type' => 'string', 'enum' => [ 'Primary', 'Additional', ], ], 'RegionsDescription' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegionDescription', ], ], 'RegionsInfo' => [ 'type' => 'structure', 'members' => [ 'PrimaryRegion' => [ 'shape' => 'RegionName', ], 'AdditionalRegions' => [ 'shape' => 'AdditionalRegions', ], ], ], 'RegisterCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'CertificateData', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'CertificateData' => [ 'shape' => 'CertificateData', ], 'Type' => [ 'shape' => 'CertificateType', ], 'ClientCertAuthSettings' => [ 'shape' => 'ClientCertAuthSettings', ], ], ], 'RegisterCertificateResult' => [ 'type' => 'structure', 'members' => [ 'CertificateId' => [ 'shape' => 'CertificateId', ], ], ], 'RegisterEventTopicRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'TopicName', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'TopicName' => [ 'shape' => 'TopicName', ], ], ], 'RegisterEventTopicResult' => [ 'type' => 'structure', 'members' => [], ], 'RejectSharedDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'SharedDirectoryId', ], 'members' => [ 'SharedDirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'RejectSharedDirectoryResult' => [ 'type' => 'structure', 'members' => [ 'SharedDirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'RemoteDomainName' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '^([a-zA-Z0-9]+[\\\\.-])+([a-zA-Z0-9])+[.]?$', ], 'RemoteDomainNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'RemoteDomainName', ], ], 'RemoveIpRoutesRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'CidrIps', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'CidrIps' => [ 'shape' => 'CidrIps', ], ], ], 'RemoveIpRoutesResult' => [ 'type' => 'structure', 'members' => [], ], 'RemoveRegionRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'RemoveRegionResult' => [ 'type' => 'structure', 'members' => [], ], 'RemoveTagsFromResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'TagKeys', ], 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceId', ], 'TagKeys' => [ 'shape' => 'TagKeys', ], ], ], 'RemoveTagsFromResourceResult' => [ 'type' => 'structure', 'members' => [], ], 'ReplicationScope' => [ 'type' => 'string', 'enum' => [ 'Domain', ], ], 'RequestId' => [ 'type' => 'string', 'pattern' => '^([A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12})$', ], 'ResetUserPasswordRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'UserName', 'NewPassword', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'UserName' => [ 'shape' => 'CustomerUserName', ], 'NewPassword' => [ 'shape' => 'UserPassword', ], ], ], 'ResetUserPasswordResult' => [ 'type' => 'structure', 'members' => [], ], 'ResourceId' => [ 'type' => 'string', 'pattern' => '^[d]-[0-9a-f]{10}$', ], 'RestoreFromSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'SnapshotId', ], 'members' => [ 'SnapshotId' => [ 'shape' => 'SnapshotId', ], ], ], 'RestoreFromSnapshotResult' => [ 'type' => 'structure', 'members' => [], ], 'SID' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[&\\w+-.@]+', ], 'SchemaExtensionId' => [ 'type' => 'string', 'pattern' => '^e-[0-9a-f]{10}$', ], 'SchemaExtensionInfo' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'SchemaExtensionId' => [ 'shape' => 'SchemaExtensionId', ], 'Description' => [ 'shape' => 'Description', ], 'SchemaExtensionStatus' => [ 'shape' => 'SchemaExtensionStatus', ], 'SchemaExtensionStatusReason' => [ 'shape' => 'SchemaExtensionStatusReason', ], 'StartDateTime' => [ 'shape' => 'StartDateTime', ], 'EndDateTime' => [ 'shape' => 'EndDateTime', ], ], ], 'SchemaExtensionStatus' => [ 'type' => 'string', 'enum' => [ 'Initializing', 'CreatingSnapshot', 'UpdatingSchema', 'Replicating', 'CancelInProgress', 'RollbackInProgress', 'Cancelled', 'Failed', 'Completed', ], ], 'SchemaExtensionStatusReason' => [ 'type' => 'string', ], 'SchemaExtensionsInfo' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaExtensionInfo', ], ], 'SecurityGroupId' => [ 'type' => 'string', 'pattern' => '^(sg-[0-9a-f]{8}|sg-[0-9a-f]{17})$', ], 'SelectiveAuth' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'Server' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Servers' => [ 'type' => 'list', 'member' => [ 'shape' => 'Server', ], ], 'ServiceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, 'fault' => true, ], 'Setting' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'DirectoryConfigurationSettingName', ], 'Value' => [ 'shape' => 'DirectoryConfigurationSettingValue', ], ], ], 'SettingEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SettingEntry', ], ], 'SettingEntry' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'DirectoryConfigurationSettingType', ], 'Name' => [ 'shape' => 'DirectoryConfigurationSettingName', ], 'AllowedValues' => [ 'shape' => 'DirectoryConfigurationSettingAllowedValues', ], 'AppliedValue' => [ 'shape' => 'DirectoryConfigurationSettingValue', ], 'RequestedValue' => [ 'shape' => 'DirectoryConfigurationSettingValue', ], 'RequestStatus' => [ 'shape' => 'DirectoryConfigurationStatus', ], 'RequestDetailedStatus' => [ 'shape' => 'DirectoryConfigurationSettingRequestDetailedStatus', ], 'RequestStatusMessage' => [ 'shape' => 'DirectoryConfigurationSettingRequestStatusMessage', ], 'LastUpdatedDateTime' => [ 'shape' => 'DirectoryConfigurationSettingLastUpdatedDateTime', ], 'LastRequestedDateTime' => [ 'shape' => 'DirectoryConfigurationSettingLastRequestedDateTime', ], 'DataType' => [ 'shape' => 'DirectoryConfigurationSettingDataType', ], ], ], 'Settings' => [ 'type' => 'list', 'member' => [ 'shape' => 'Setting', ], ], 'ShareDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'ShareTarget', 'ShareMethod', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'ShareNotes' => [ 'shape' => 'Notes', ], 'ShareTarget' => [ 'shape' => 'ShareTarget', ], 'ShareMethod' => [ 'shape' => 'ShareMethod', ], ], ], 'ShareDirectoryResult' => [ 'type' => 'structure', 'members' => [ 'SharedDirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'ShareLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'ShareMethod' => [ 'type' => 'string', 'enum' => [ 'ORGANIZATIONS', 'HANDSHAKE', ], ], 'ShareStatus' => [ 'type' => 'string', 'enum' => [ 'Shared', 'PendingAcceptance', 'Rejected', 'Rejecting', 'RejectFailed', 'Sharing', 'ShareFailed', 'Deleted', 'Deleting', ], ], 'ShareTarget' => [ 'type' => 'structure', 'required' => [ 'Id', 'Type', ], 'members' => [ 'Id' => [ 'shape' => 'TargetId', ], 'Type' => [ 'shape' => 'TargetType', ], ], ], 'SharedDirectories' => [ 'type' => 'list', 'member' => [ 'shape' => 'SharedDirectory', ], ], 'SharedDirectory' => [ 'type' => 'structure', 'members' => [ 'OwnerAccountId' => [ 'shape' => 'CustomerId', ], 'OwnerDirectoryId' => [ 'shape' => 'DirectoryId', ], 'ShareMethod' => [ 'shape' => 'ShareMethod', ], 'SharedAccountId' => [ 'shape' => 'CustomerId', ], 'SharedDirectoryId' => [ 'shape' => 'DirectoryId', ], 'ShareStatus' => [ 'shape' => 'ShareStatus', ], 'ShareNotes' => [ 'shape' => 'Notes', ], 'CreatedDateTime' => [ 'shape' => 'CreatedDateTime', ], 'LastUpdatedDateTime' => [ 'shape' => 'LastUpdatedDateTime', ], ], ], 'Snapshot' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'SnapshotId' => [ 'shape' => 'SnapshotId', ], 'Type' => [ 'shape' => 'SnapshotType', ], 'Name' => [ 'shape' => 'SnapshotName', ], 'Status' => [ 'shape' => 'SnapshotStatus', ], 'StartTime' => [ 'shape' => 'StartTime', ], ], ], 'SnapshotId' => [ 'type' => 'string', 'pattern' => '^s-[0-9a-f]{10}$', ], 'SnapshotIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SnapshotId', ], ], 'SnapshotLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'SnapshotLimits' => [ 'type' => 'structure', 'members' => [ 'ManualSnapshotsLimit' => [ 'shape' => 'Limit', ], 'ManualSnapshotsCurrentCount' => [ 'shape' => 'Limit', ], 'ManualSnapshotsLimitReached' => [ 'shape' => 'ManualSnapshotsLimitReached', ], ], ], 'SnapshotName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^([a-zA-Z0-9_])[\\\\a-zA-Z0-9_@#%*+=:?./!\\s-]*$', ], 'SnapshotStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Completed', 'Failed', ], ], 'SnapshotType' => [ 'type' => 'string', 'enum' => [ 'Auto', 'Manual', ], ], 'Snapshots' => [ 'type' => 'list', 'member' => [ 'shape' => 'Snapshot', ], ], 'SsoEnabled' => [ 'type' => 'boolean', ], 'StageReason' => [ 'type' => 'string', ], 'StartDateTime' => [ 'type' => 'timestamp', ], 'StartSchemaExtensionRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'CreateSnapshotBeforeSchemaExtension', 'LdifContent', 'Description', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'CreateSnapshotBeforeSchemaExtension' => [ 'shape' => 'CreateSnapshotBeforeSchemaExtension', ], 'LdifContent' => [ 'shape' => 'LdifContent', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'StartSchemaExtensionResult' => [ 'type' => 'structure', 'members' => [ 'SchemaExtensionId' => [ 'shape' => 'SchemaExtensionId', ], ], ], 'StartTime' => [ 'type' => 'timestamp', ], 'StateLastUpdatedDateTime' => [ 'type' => 'timestamp', ], 'SubnetId' => [ 'type' => 'string', 'pattern' => '^(subnet-[0-9a-f]{8}|subnet-[0-9a-f]{17})$', ], 'SubnetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], ], 'SubscriptionCreatedDateTime' => [ 'type' => 'timestamp', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TargetId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'TargetType' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT', ], ], 'TopicArn' => [ 'type' => 'string', ], 'TopicName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'TopicNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'TopicName', ], ], 'TopicStatus' => [ 'type' => 'string', 'enum' => [ 'Registered', 'Topic not found', 'Failed', 'Deleted', ], ], 'Trust' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'TrustId' => [ 'shape' => 'TrustId', ], 'RemoteDomainName' => [ 'shape' => 'RemoteDomainName', ], 'TrustType' => [ 'shape' => 'TrustType', ], 'TrustDirection' => [ 'shape' => 'TrustDirection', ], 'TrustState' => [ 'shape' => 'TrustState', ], 'CreatedDateTime' => [ 'shape' => 'CreatedDateTime', ], 'LastUpdatedDateTime' => [ 'shape' => 'LastUpdatedDateTime', ], 'StateLastUpdatedDateTime' => [ 'shape' => 'StateLastUpdatedDateTime', ], 'TrustStateReason' => [ 'shape' => 'TrustStateReason', ], 'SelectiveAuth' => [ 'shape' => 'SelectiveAuth', ], ], ], 'TrustDirection' => [ 'type' => 'string', 'enum' => [ 'One-Way: Outgoing', 'One-Way: Incoming', 'Two-Way', ], ], 'TrustId' => [ 'type' => 'string', 'pattern' => '^t-[0-9a-f]{10}$', ], 'TrustIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrustId', ], ], 'TrustPassword' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(\\p{LD}|\\p{Punct}| )+$', 'sensitive' => true, ], 'TrustState' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Created', 'Verifying', 'VerifyFailed', 'Verified', 'Updating', 'UpdateFailed', 'Updated', 'Deleting', 'Deleted', 'Failed', ], ], 'TrustStateReason' => [ 'type' => 'string', ], 'TrustType' => [ 'type' => 'string', 'enum' => [ 'Forest', 'External', ], ], 'Trusts' => [ 'type' => 'list', 'member' => [ 'shape' => 'Trust', ], ], 'UnshareDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'UnshareTarget', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'UnshareTarget' => [ 'shape' => 'UnshareTarget', ], ], ], 'UnshareDirectoryResult' => [ 'type' => 'structure', 'members' => [ 'SharedDirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'UnshareTarget' => [ 'type' => 'structure', 'required' => [ 'Id', 'Type', ], 'members' => [ 'Id' => [ 'shape' => 'TargetId', ], 'Type' => [ 'shape' => 'TargetType', ], ], ], 'UnsupportedOperationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'UnsupportedSettingsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'UpdateActivities' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateInfoEntry', ], ], 'UpdateConditionalForwarderRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'RemoteDomainName', 'DnsIpAddrs', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RemoteDomainName' => [ 'shape' => 'RemoteDomainName', ], 'DnsIpAddrs' => [ 'shape' => 'DnsIpAddrs', ], ], ], 'UpdateConditionalForwarderResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDirectorySetupRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'UpdateType', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'UpdateType' => [ 'shape' => 'UpdateType', ], 'OSUpdateSettings' => [ 'shape' => 'OSUpdateSettings', ], 'CreateSnapshotBeforeUpdate' => [ 'shape' => 'CreateSnapshotBeforeUpdate', 'box' => true, ], ], ], 'UpdateDirectorySetupResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateInfoEntry' => [ 'type' => 'structure', 'members' => [ 'Region' => [ 'shape' => 'RegionName', ], 'Status' => [ 'shape' => 'UpdateStatus', ], 'StatusReason' => [ 'shape' => 'UpdateStatusReason', ], 'InitiatedBy' => [ 'shape' => 'InitiatedBy', ], 'NewValue' => [ 'shape' => 'UpdateValue', ], 'PreviousValue' => [ 'shape' => 'UpdateValue', ], 'StartTime' => [ 'shape' => 'StartDateTime', ], 'LastUpdatedDateTime' => [ 'shape' => 'LastUpdatedDateTime', ], ], ], 'UpdateNumberOfDomainControllersRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'DesiredNumber', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'DesiredNumber' => [ 'shape' => 'DesiredNumberOfDomainControllers', ], ], ], 'UpdateNumberOfDomainControllersResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateRadiusRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'RadiusSettings', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RadiusSettings' => [ 'shape' => 'RadiusSettings', ], ], ], 'UpdateRadiusResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateSecurityGroupForDirectoryControllers' => [ 'type' => 'boolean', ], 'UpdateSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'Settings', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Settings' => [ 'shape' => 'Settings', ], ], ], 'UpdateSettingsResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'UpdateStatus' => [ 'type' => 'string', 'enum' => [ 'Updated', 'Updating', 'UpdateFailed', ], ], 'UpdateStatusReason' => [ 'type' => 'string', ], 'UpdateTrustRequest' => [ 'type' => 'structure', 'required' => [ 'TrustId', ], 'members' => [ 'TrustId' => [ 'shape' => 'TrustId', ], 'SelectiveAuth' => [ 'shape' => 'SelectiveAuth', ], ], ], 'UpdateTrustResult' => [ 'type' => 'structure', 'members' => [ 'RequestId' => [ 'shape' => 'RequestId', ], 'TrustId' => [ 'shape' => 'TrustId', ], ], ], 'UpdateType' => [ 'type' => 'string', 'enum' => [ 'OS', ], ], 'UpdateValue' => [ 'type' => 'structure', 'members' => [ 'OSUpdateSettings' => [ 'shape' => 'OSUpdateSettings', ], ], ], 'UseSameUsername' => [ 'type' => 'boolean', ], 'UserDoesNotExistException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'UserName' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[a-zA-Z0-9._-]+', ], 'UserPassword' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'sensitive' => true, ], 'VerifyTrustRequest' => [ 'type' => 'structure', 'required' => [ 'TrustId', ], 'members' => [ 'TrustId' => [ 'shape' => 'TrustId', ], ], ], 'VerifyTrustResult' => [ 'type' => 'structure', 'members' => [ 'TrustId' => [ 'shape' => 'TrustId', ], ], ], 'VpcId' => [ 'type' => 'string', 'pattern' => '^(vpc-[0-9a-f]{8}|vpc-[0-9a-f]{17})$', ], ],];
