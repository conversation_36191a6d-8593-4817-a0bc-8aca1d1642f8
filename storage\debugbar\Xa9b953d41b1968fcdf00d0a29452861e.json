{"__meta": {"id": "Xa9b953d41b1968fcdf00d0a29452861e", "datetime": "2025-06-30 23:13:25", "utime": **********.48693, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.030659, "end": **********.486947, "duration": 0.45628809928894043, "duration_str": "456ms", "measures": [{"label": "Booting", "start": **********.030659, "relative_start": 0, "end": **********.425863, "relative_end": **********.425863, "duration": 0.3952040672302246, "duration_str": "395ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.425876, "relative_start": 0.3952169418334961, "end": **********.486949, "relative_end": 1.9073486328125e-06, "duration": 0.06107306480407715, "duration_str": "61.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45045512, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00419, "accumulated_duration_str": "4.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.454713, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.098}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4655209, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.098, "width_percent": 9.547}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.473836, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 70.644, "width_percent": 20.048}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.480008, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 90.692, "width_percent": 9.308}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1721329767 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1721329767\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1912299547 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1912299547\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-168373919 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-168373919\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1883839013 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325166735%7C25%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVmU0xQZUREZDVwRElhM09oV0ExYnc9PSIsInZhbHVlIjoibll4dzdJaTlpWk1IRkVrNXlPV0xMYmgzR3pXSnpvYmgwMXp3OXlwVG5KcTFCS0EwWklMN2pmYmJTdDdISjlmRWk3Rzd6UTRNbTZTeC9aMW1Zbno0M2NWbzBRbUZYNXNCTEVISFdMYjExRGdocGREUHR1QnczWFpWNms1SFM5dzdXU3VJeGEwaFhvRHVmWjhvNDZyTXdla1RFRUhnRC9SUXZuZnd6VzhhM0M3Ty84ME1vdVArNjBxeFdhMFpGTm5oZTZkTWg5dFc1VGp4bGJ4MVFJajE5YXFJUXFUMTlEd0hTL0F6ZnJ2TURyajEzbjNwbElPMzhrdVJLTUNiRkhPOFFQZWQ4UVZnN0R2eWlwMjkxeW9LaFc4YkZmcnRpV0NiVS9hZC9XVkhkbjhoOC8ycEJyTGk2bHUwRzRBZVM0Um8yTGRMNGJuclk4WVladTl1b3dGaFRLSTB3dG03Y0hwbGk5NzROZWJCdHlueVBOUldmZHFvSDRFaUU0d2dUdGxrUzliYTluZEdrejlsS0tnZDM4SWJSNkVjWGpSZjZRWk16enVpc2dRU0MwUVcxOXJ5bGRFZHY3K09ZejJibVZJT3VBZThyei9nODZkWmV5VFVrcndKaUpBLzV5TWorS01vR3VqVzB4b2lyVHd2YkZmTnZPMzRSVlMyQ3pQZjlwRWMiLCJtYWMiOiJlMmNiNTBiMjEyYzM1Mjc5ODRkNDA0ZmIxZjdlYThjNGIwZGM2YjRiMmI5MWZiNTcwM2JlZWY5YmEyODBmODA4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjEyNFplb3FjemtyMU1ZNlVPUmN4L0E9PSIsInZhbHVlIjoiUnhLTTRGTWs1dDdaRkpkR2YrV295VWhCRCs4b3B0akU3L242dkNHMFY1bTdTWTFLNEdvUWo5ZTBXWHZMR3NRSDN0NkNyanpPbFlQc1pJUXBPZmxzdGttUklDaE5qL1kvVFFjWGJhRjdOamJZK3h2aURzY1d1VGZuRkpWdDJpL3A3K1RnajFTU2xnekFmZ2JBU2Z2Q0hZV3BKVndSMHpIM2F4a1REbDcySDNROTRtTEg5Szl4VkNEMmJuVHZLZUZkbWE3MXRoaXh0K3ZkZVVLQmV6WllCdDNUWHplRkhIb0E0SDdqWk9wOGVIcEdIV1oyY25oaWlrU1dPOXJydXQ0bmRIWWtvWXlkN2ptMzhqTDhHeTF6NjR6RjVJamFtOTREZWwyek9EOU42MmppampmeGtIYkE4M052Q3dXRmpoNHVPT210dG9XMlZhREh4SXE3RFZLMlNJVTlta3VqTkRqVWw2dmRSY3pzRmU4NmxRNmtzbU9PV1BqakcwbkR5UjJIdE4zV0lnN0F1VU1tSW1yZ21NUkxmQUJMOGRQUVAxcVdpYTMrSTN5Qk41V2hGR1JwcjJyaDZ3REE2dlZpMEliN1BjSTZBQjVHeCtFTC95d0JPUkl2cjVZTFBkQlg2bGttWHV2a2p4VVROYnkyMHYvTlVzd3JUdTdEWWRZd3F3NVkiLCJtYWMiOiIzNmNiMjM0N2ZhOTBiMjBmZTQ0MWVmMDY4ZWM0YWNlMjI5MmEyMTcwYTkxNDViOTg0ZWQ5ZGJkNDk0MjRhMDQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1883839013\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1677197935 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0aUeGT9vcZQoKvixhqnXLFDjX39UXyfabqLnLub</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1677197935\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2012360410 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:13:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5maUtsY0VnbTJFSnFNZmpSUE9CQ0E9PSIsInZhbHVlIjoiNUxuNkorRWsvR0w4SWkxK3MrWldRMC82Q3JjUmJDY1R5cTU5bjF2czlFZkVOSFpMTUN6c0dpNzZMMWtkVVdObzQ2M3poNEpCSVFLWndPdXYwcWRKemFlaTk4UzFBaG5xdG0vRlA4ZllvWGsrOHFURXcvUm5RbUZ3ZXBmYk1TZnh6MWpNTlB3ODV4dmIwYy81RWJ6WThjbW1xSTF4TUlGSmRIazJUdFRRdzQ5bkZ0MWlKWVR6cDlRaDRzd2s1dkN6RnR0SlNUV3gzenpUbXFoMW8yZVZUWnVQamRSYUhQSVlqUjA2Tkd1aHY2aGk4V0E4ZFRobjNKQ2l3OVVnYjdrQ2hSNFJRSll5ajNCMzVyU2dGMzZHeUR1WTJhdDVzV2JnNldSM3p6T3VLa0dTTGZlNnErZmJaMXdCemhPWVVsOFIwQ2NmWWFKNDdtcTNiQmVtN2pac3EyNjAxdFUrQlNwTkRTLzlJSWZsQzdhdDFtdS9BRSt1MGRDbFRHRElOVVIwTnJNODIwNzFJeDFZN0o2M2FxSGx5NGNxcVVmNzc2dUpmMlNORlMza043RDZHc2ExOVlFeTZWd3RwMitObjJSeHhVRGxKVTVvNEtScjBkV2tUanFpd2tRK0xyQ2l0aFJiNzNrcGdPOS84TkRyM3B3YXFBQVBKYUFwVTl6ZjJDNGsiLCJtYWMiOiI4MWE4NThhMzdjOWVmZTMzYzA5MDllNjFmYmNhMDUzZmNkMmU2MDI0YjNhZmM5NDY0ODU5NWEyOWJhYzhhNTVmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im5EMHRURXo0QW5GQzd6QjRWS3VBY2c9PSIsInZhbHVlIjoibk44RUxKeENBbXV6cCt5WmNnNWlLSEdjbTVBWFhFOCtsejU1TUJzODZZMWRyeCsxNHJwR1R3R2NLSmFtbHMrNWNzaHFuM09iZEJGSkR6QnRacU1aTXl3cW42cTdTRlhTMktWTldFaXVQNUNCKzVQR05oODQydEEyRWdFcEtaOGw1N2ovQURQZU43VnREQStHZlE1WUdUSHJIUytHL2dMUytPWlZkd1JPWXlXNzlnY2VMclpQeHRTYk5OdTh5TDh5V3lWMy9JWmFxTHd3NXE4citVRXVQQUlITGwxaXhVZFVwOFVwdVYrZDRvU2MvWGlUem90a0RZSUdKYzNFRDl1QkNsWGNqUFZwaWFjU0JQWk1QTVZZR3ozR0ZydExwcEdFNzlmaU1Sa2hES0p1RDZZaVo0SnFvNVA3eEtsMjdIbUd0RktuODVvU0p0SE81Wlk1cERnWmJnZStEUEo1b1RWN0RLcDZxQzJjelRyVXhIWHBUc3hFdHlHT2QvdkpacWJTdXBjT3d6MTVNOWxtNlR5bE1jTFI5WFg4U29RZ3d2SjArU3VtMUJ2WXhLeGdMWG5zejluVXM5c1VXck01aGh2VTA1aFMzeWJGNmJIWjVzYU4yTS94R2hnSVd4ZXhtTmhDUDBIOUFmVkpCS1ZYNmIxK1NURDBTemhjd0IrNCtxUDAiLCJtYWMiOiJkODFiNDEwNGExN2U1N2M1ZWZhNjlmMGM2ZDFhZDA3N2MwOGJkYmEyNmNkZTk0MGI5MDJhZDdiZmIwZjlkMGU0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5maUtsY0VnbTJFSnFNZmpSUE9CQ0E9PSIsInZhbHVlIjoiNUxuNkorRWsvR0w4SWkxK3MrWldRMC82Q3JjUmJDY1R5cTU5bjF2czlFZkVOSFpMTUN6c0dpNzZMMWtkVVdObzQ2M3poNEpCSVFLWndPdXYwcWRKemFlaTk4UzFBaG5xdG0vRlA4ZllvWGsrOHFURXcvUm5RbUZ3ZXBmYk1TZnh6MWpNTlB3ODV4dmIwYy81RWJ6WThjbW1xSTF4TUlGSmRIazJUdFRRdzQ5bkZ0MWlKWVR6cDlRaDRzd2s1dkN6RnR0SlNUV3gzenpUbXFoMW8yZVZUWnVQamRSYUhQSVlqUjA2Tkd1aHY2aGk4V0E4ZFRobjNKQ2l3OVVnYjdrQ2hSNFJRSll5ajNCMzVyU2dGMzZHeUR1WTJhdDVzV2JnNldSM3p6T3VLa0dTTGZlNnErZmJaMXdCemhPWVVsOFIwQ2NmWWFKNDdtcTNiQmVtN2pac3EyNjAxdFUrQlNwTkRTLzlJSWZsQzdhdDFtdS9BRSt1MGRDbFRHRElOVVIwTnJNODIwNzFJeDFZN0o2M2FxSGx5NGNxcVVmNzc2dUpmMlNORlMza043RDZHc2ExOVlFeTZWd3RwMitObjJSeHhVRGxKVTVvNEtScjBkV2tUanFpd2tRK0xyQ2l0aFJiNzNrcGdPOS84TkRyM3B3YXFBQVBKYUFwVTl6ZjJDNGsiLCJtYWMiOiI4MWE4NThhMzdjOWVmZTMzYzA5MDllNjFmYmNhMDUzZmNkMmU2MDI0YjNhZmM5NDY0ODU5NWEyOWJhYzhhNTVmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im5EMHRURXo0QW5GQzd6QjRWS3VBY2c9PSIsInZhbHVlIjoibk44RUxKeENBbXV6cCt5WmNnNWlLSEdjbTVBWFhFOCtsejU1TUJzODZZMWRyeCsxNHJwR1R3R2NLSmFtbHMrNWNzaHFuM09iZEJGSkR6QnRacU1aTXl3cW42cTdTRlhTMktWTldFaXVQNUNCKzVQR05oODQydEEyRWdFcEtaOGw1N2ovQURQZU43VnREQStHZlE1WUdUSHJIUytHL2dMUytPWlZkd1JPWXlXNzlnY2VMclpQeHRTYk5OdTh5TDh5V3lWMy9JWmFxTHd3NXE4citVRXVQQUlITGwxaXhVZFVwOFVwdVYrZDRvU2MvWGlUem90a0RZSUdKYzNFRDl1QkNsWGNqUFZwaWFjU0JQWk1QTVZZR3ozR0ZydExwcEdFNzlmaU1Sa2hES0p1RDZZaVo0SnFvNVA3eEtsMjdIbUd0RktuODVvU0p0SE81Wlk1cERnWmJnZStEUEo1b1RWN0RLcDZxQzJjelRyVXhIWHBUc3hFdHlHT2QvdkpacWJTdXBjT3d6MTVNOWxtNlR5bE1jTFI5WFg4U29RZ3d2SjArU3VtMUJ2WXhLeGdMWG5zejluVXM5c1VXck01aGh2VTA1aFMzeWJGNmJIWjVzYU4yTS94R2hnSVd4ZXhtTmhDUDBIOUFmVkpCS1ZYNmIxK1NURDBTemhjd0IrNCtxUDAiLCJtYWMiOiJkODFiNDEwNGExN2U1N2M1ZWZhNjlmMGM2ZDFhZDA3N2MwOGJkYmEyNmNkZTk0MGI5MDJhZDdiZmIwZjlkMGU0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2012360410\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-297268468 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-297268468\", {\"maxDepth\":0})</script>\n"}}