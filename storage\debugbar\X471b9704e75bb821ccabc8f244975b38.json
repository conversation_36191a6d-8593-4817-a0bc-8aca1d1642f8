{"__meta": {"id": "X471b9704e75bb821ccabc8f244975b38", "datetime": "2025-06-30 23:08:30", "utime": **********.364855, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751324909.878074, "end": **********.364869, "duration": 0.48679518699645996, "duration_str": "487ms", "measures": [{"label": "Booting", "start": 1751324909.878074, "relative_start": 0, "end": **********.271042, "relative_end": **********.271042, "duration": 0.39296817779541016, "duration_str": "393ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.271055, "relative_start": 0.39298105239868164, "end": **********.36487, "relative_end": 9.5367431640625e-07, "duration": 0.09381508827209473, "duration_str": "93.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48170176, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02318, "accumulated_duration_str": "23.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.304426, "duration": 0.01786, "duration_str": "17.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 77.049}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.330086, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 77.049, "width_percent": 1.941}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.3441079, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 78.991, "width_percent": 2.373}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.346062, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.363, "width_percent": 1.855}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.35064, "duration": 0.00248, "duration_str": "2.48ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 83.218, "width_percent": 10.699}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.355973, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 93.917, "width_percent": 6.083}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1032477032 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1032477032\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.349636, "xdebug_link": null}]}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-464722771 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-464722771\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-810495253 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-810495253\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-997172255 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-997172255\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751324907481%7C9%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9XWmQ0VmhWQjNnb1p0cUxrc1JDOVE9PSIsInZhbHVlIjoiZkcwTjFsZ3poOVl1OHFXWkp5Qm9zQVBSMTRORHVoKzUxenkwZkJMaTA1RWRBYnAwSGNWZkJ3anlmY3U0bTlRY3BQL1g4U0x1RWs2ZEtUeEp2eUhMSjd3NlJhajJVendjdmhuK3daQStwdS8yWCsxSWYyWWlkZ1MvREZ4ZjR6S1F3by9UNFBkU2puZ0hDUlIrbWFPMU0vZ29pSEdQbnJnWEM5eGhFVk54bldvOW9zYlVNTlRQRENJWDBraGZSRDNRVVhlN0svN3hoRGxRdzMvSnpraGk1MzYrUjhLd09PWTdRM0FGSXl6S2l0Yk5Pb05rRXU4WUlRNklnaVRmTHJwNEMwSFJGVk9pdTZoL1RtclJ5OE00My9CL1pEUmNWUGdPL0gyTHR2VnJUSDdzNFBwdWM2bEU5dlYrMG02UkZyTjJ6REpraXo0ME8zK0ZpcDVZNk53YStTK0JGQmR5NVlXM2FCNEp3VlNMVDdKNjdZd2t6T0RKRVE0OG55Z081R3VQRHpQSTJLVXdOU0xMNDFJTmVmMTBiODI5MExIYU96cS9QM0xTSU02NEFrL2hwV0t6RFJhQlJYU1o5SW82R2NiRFdMK1UycDJyUjd3bFcyKzNwK3Z0dlR2bkFyd3VBUWVOUXFnMjU1UC9BWGcza1ZCdHVmajR2VTFNMDV5OVZPZDIiLCJtYWMiOiJmYzY2ZGQzYzBjZTIwYzRmM2VkNmM2NTY3OGY3NGFhM2Q3MmRiMjE5MDRmZmFhZDA3OTAyMzU1MTFiZDI1ODQ1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InpITXhsRXpRaHNPeUh0ZzRlRTVDUmc9PSIsInZhbHVlIjoidHgreG53MHZPMmhpZWpIWndISnlTQUIxODRTNG5jWUdIL0RTWkhRRmVZT2hRVjZTY1A2MDFtNHl0SWwzTzlaK2x4Nk9kMHFwVy8xK3B2N242MXkzWXp1NTRxdGp4Z2NqSmJLbXl2WHpQMXdmeElXYWlPWUM5WVNHNkNxckRDcC9NN2VRT1p4ZTc5S2lycXVqVjNSaWh5NXlKMk5xVVo5RU1yeWRLL0M0VUUreEszTy9qVVlHd3paZWdVN1hpUEtwQlU5RzdrYTRBZXFvbGFGQXBrNXY0U1hCU21zYW10dS9QejNxWEc3UXZZTzRCcTFvMEp3Mi9xTUFpUUFMZXJRK0pqUHkwMnJkVEkrM0NIc3Jlb0ZFOExYY3lGQkdqWnRiOTZOdmJMMWFBQmEvNmRzVkROM1dVczBKaGNoeCtkNnQwMGFJN3k4NXZyTHlBSnhlSXE0My9sbkxTQWUrT2M3bFJzK2FCdWt3TlBqdE8yNTQwVE9VNGdzRWVMUVRRY0pSSGpDSENDWlhFY3RaV0Nvek9QdERWc3ZoMW13WldiVnc0TUMrM2UxeHdkNnFJSXBXS1lrUWl0Q1R3RnNJUkZqb0lXNTQxNlJiSjA2MXF6UzlTbEVoRGlia25qcGkwYUw2b3pzOW9rbmNXV0FyYVQ4ckZMT0ZxSjVMYzBnU0NGNmEiLCJtYWMiOiIzYjBkM2QyNGQwNTg3OThlMGI0ZmM1MDExNTI3YTZlNTRiNGI3MTc4MDc1ZGFhNWI4ZDU3ZTAyZmQ4MmQ4MzU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1299925657 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1299925657\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1354492349 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:08:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFUbzI1QzhiRzJXa2JKbFJjTWZuMUE9PSIsInZhbHVlIjoiMWtLdFYwM0EyQXI3SDJPK2NzWWYrUEJnN2dEdHJtb0dQQkpiS0VtcDBwYXVEMmpHOFNvRTlzUHBkK1EvUUcwL1lrcXFWRitFRm9NZVBPdHdpbDk4SWlxUkFCa0toNW94UDdVb3V6VXl2TzRQN2pPaStWeDQwQlIxTmUxcTRnT0ZrdUFySmptSmRadWtITXU4MGtHVVlQUDNwTnlKT1djMm9icUo2RjcvbzBKa2t5Y1RLdE5EMlo4U3RmSkFxY3FQWkphRkIwNUFPaFA1Q3FlRjNaUGc4Y1AxL1RXZlBVcGlpVHJHYzdpOFAxeDJUVk93YlJsUzNOdzFYU1R4ZEtQS2FKdWgydEs1NnNiWVB1M3pGZlFMc0xQNmdVb2pxcWRNYVgxZktmbFdCQ0IwaS9NNW8xQnAxcTRyeVpjU01GTFJKZkgvVmt0YnpHeWF5bEIwNzZsemVEeXNxbXRQRlgrbXU5eXpaUktHL3EvNld2TlN1djhERVVPakVoMThqRTVoRnRhL1BoWVNFdXlFdUFzdFBFS0ZxbHJhS2JnQlNTWXpYcm45bnM0emFLQ0tTdUNLQlR0TTVRaE5ac1FBVEk5MXB6NFh4R1F3OWcvK0h3NWNqb3poakZkMGNhKzZwY3lOd0N5TkVidjZORHVJemlmY3lVNTBzWEowZkwrc1QyeFQiLCJtYWMiOiJlMjg4NjRjNzdkMzdhMjE5MWNlNDBmYWFlZGUwNGZkZmYwMjdiZWNmNDcxNDc5NTQyZGUzZDI0OTI0MzgzODQzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:08:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJmbW4yeEo5VExJTmhsYmM0WGlwT1E9PSIsInZhbHVlIjoiV053WjdBTVovWDlFeEZkQUNSZXBqTXNpYVRuamNlSThtZElBQzZwaVJPSHF0aHhVQnpCUkxIVTltaWhTQjlWc0ZlMWxnSGlwZkduVkJEVTZzTTFUN01tL3ZxalUwWEV5QkdtZ1JnN3U4cGdIS1ByOFU5YXRvcmgzbVFQM3Y3aWcwUDNIZmxLRmV5Q1JMRDVsc0hxbFVZdjdTMnZTenQ1dFJBRzFNL0Fna0dmRml0bmRMa1lXTTdmNkdhQk4zNGFZeE96RFd0OHhzMmppNENoRE5kZXBDZTlFS1oyZ3VqNUNWSnBNNDFBYjR2emt2TmhjNndHRUkzb2Q4aE9VUHV4NC9uWXd5VDlucWRqdUNRRHhXSm84Nll6L3Rra1BncytPMkJoSXpxQnFmK1F2d3BWWVZSK2xhOWVpQjRZUnhvamVNM2t6dDFSWEszeHVOM2x3b1RsakZuTjZlMUhQcHh6RGc0Qk5SZHdQY0M4YVFEaFh5VVM0M291WDhJT2RXYVpaYjJvUFdxL3RLS1lnTDF4ZUlFNldTc2hRMXlleVFRVWZ0cnNONGVaTEJoVkxUSEVBZUR4MmxNWjRDTDhrUDZSYW02NzBtTjRUb2FvQ2NxaFVveWVkN1FIYlJ5bUwrYlJBbXlUTUlhTUNkWHVFcThDTnZiUm5kT0EvcGNubnNSbzkiLCJtYWMiOiJiNjgyMzUwMDI4NTU2NGI1MzI5MzZjYmZiYjNjMjZmM2Q5YTk3OTNiOTU3NWQ0NmY2ZjgzOTE0NzIzYjE4ZGQ0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:08:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFUbzI1QzhiRzJXa2JKbFJjTWZuMUE9PSIsInZhbHVlIjoiMWtLdFYwM0EyQXI3SDJPK2NzWWYrUEJnN2dEdHJtb0dQQkpiS0VtcDBwYXVEMmpHOFNvRTlzUHBkK1EvUUcwL1lrcXFWRitFRm9NZVBPdHdpbDk4SWlxUkFCa0toNW94UDdVb3V6VXl2TzRQN2pPaStWeDQwQlIxTmUxcTRnT0ZrdUFySmptSmRadWtITXU4MGtHVVlQUDNwTnlKT1djMm9icUo2RjcvbzBKa2t5Y1RLdE5EMlo4U3RmSkFxY3FQWkphRkIwNUFPaFA1Q3FlRjNaUGc4Y1AxL1RXZlBVcGlpVHJHYzdpOFAxeDJUVk93YlJsUzNOdzFYU1R4ZEtQS2FKdWgydEs1NnNiWVB1M3pGZlFMc0xQNmdVb2pxcWRNYVgxZktmbFdCQ0IwaS9NNW8xQnAxcTRyeVpjU01GTFJKZkgvVmt0YnpHeWF5bEIwNzZsemVEeXNxbXRQRlgrbXU5eXpaUktHL3EvNld2TlN1djhERVVPakVoMThqRTVoRnRhL1BoWVNFdXlFdUFzdFBFS0ZxbHJhS2JnQlNTWXpYcm45bnM0emFLQ0tTdUNLQlR0TTVRaE5ac1FBVEk5MXB6NFh4R1F3OWcvK0h3NWNqb3poakZkMGNhKzZwY3lOd0N5TkVidjZORHVJemlmY3lVNTBzWEowZkwrc1QyeFQiLCJtYWMiOiJlMjg4NjRjNzdkMzdhMjE5MWNlNDBmYWFlZGUwNGZkZmYwMjdiZWNmNDcxNDc5NTQyZGUzZDI0OTI0MzgzODQzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:08:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJmbW4yeEo5VExJTmhsYmM0WGlwT1E9PSIsInZhbHVlIjoiV053WjdBTVovWDlFeEZkQUNSZXBqTXNpYVRuamNlSThtZElBQzZwaVJPSHF0aHhVQnpCUkxIVTltaWhTQjlWc0ZlMWxnSGlwZkduVkJEVTZzTTFUN01tL3ZxalUwWEV5QkdtZ1JnN3U4cGdIS1ByOFU5YXRvcmgzbVFQM3Y3aWcwUDNIZmxLRmV5Q1JMRDVsc0hxbFVZdjdTMnZTenQ1dFJBRzFNL0Fna0dmRml0bmRMa1lXTTdmNkdhQk4zNGFZeE96RFd0OHhzMmppNENoRE5kZXBDZTlFS1oyZ3VqNUNWSnBNNDFBYjR2emt2TmhjNndHRUkzb2Q4aE9VUHV4NC9uWXd5VDlucWRqdUNRRHhXSm84Nll6L3Rra1BncytPMkJoSXpxQnFmK1F2d3BWWVZSK2xhOWVpQjRZUnhvamVNM2t6dDFSWEszeHVOM2x3b1RsakZuTjZlMUhQcHh6RGc0Qk5SZHdQY0M4YVFEaFh5VVM0M291WDhJT2RXYVpaYjJvUFdxL3RLS1lnTDF4ZUlFNldTc2hRMXlleVFRVWZ0cnNONGVaTEJoVkxUSEVBZUR4MmxNWjRDTDhrUDZSYW02NzBtTjRUb2FvQ2NxaFVveWVkN1FIYlJ5bUwrYlJBbXlUTUlhTUNkWHVFcThDTnZiUm5kT0EvcGNubnNSbzkiLCJtYWMiOiJiNjgyMzUwMDI4NTU2NGI1MzI5MzZjYmZiYjNjMjZmM2Q5YTk3OTNiOTU3NWQ0NmY2ZjgzOTE0NzIzYjE4ZGQ0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:08:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1354492349\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1132212351 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1132212351\", {\"maxDepth\":0})</script>\n"}}