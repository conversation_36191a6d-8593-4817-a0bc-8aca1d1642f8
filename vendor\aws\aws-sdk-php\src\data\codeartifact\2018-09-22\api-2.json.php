<?php
// This file was auto-generated from sdk-root/src/data/codeartifact/2018-09-22/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-09-22', 'endpointPrefix' => 'codeartifact', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'CodeArtifact', 'serviceId' => 'codeartifact', 'signatureVersion' => 'v4', 'signingName' => 'codeartifact', 'uid' => 'codeartifact-2018-09-22', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AssociateExternalConnection' => [ 'name' => 'AssociateExternalConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/repository/external-connection', ], 'input' => [ 'shape' => 'AssociateExternalConnectionRequest', ], 'output' => [ 'shape' => 'AssociateExternalConnectionResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'CopyPackageVersions' => [ 'name' => 'CopyPackageVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/package/versions/copy', ], 'input' => [ 'shape' => 'CopyPackageVersionsRequest', ], 'output' => [ 'shape' => 'CopyPackageVersionsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateDomain' => [ 'name' => 'CreateDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/domain', ], 'input' => [ 'shape' => 'CreateDomainRequest', ], 'output' => [ 'shape' => 'CreateDomainResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreatePackageGroup' => [ 'name' => 'CreatePackageGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/package-group', ], 'input' => [ 'shape' => 'CreatePackageGroupRequest', ], 'output' => [ 'shape' => 'CreatePackageGroupResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateRepository' => [ 'name' => 'CreateRepository', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/repository', ], 'input' => [ 'shape' => 'CreateRepositoryRequest', ], 'output' => [ 'shape' => 'CreateRepositoryResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteDomain' => [ 'name' => 'DeleteDomain', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/domain', ], 'input' => [ 'shape' => 'DeleteDomainRequest', ], 'output' => [ 'shape' => 'DeleteDomainResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteDomainPermissionsPolicy' => [ 'name' => 'DeleteDomainPermissionsPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/domain/permissions/policy', ], 'input' => [ 'shape' => 'DeleteDomainPermissionsPolicyRequest', ], 'output' => [ 'shape' => 'DeleteDomainPermissionsPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeletePackage' => [ 'name' => 'DeletePackage', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/package', ], 'input' => [ 'shape' => 'DeletePackageRequest', ], 'output' => [ 'shape' => 'DeletePackageResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeletePackageGroup' => [ 'name' => 'DeletePackageGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/package-group', ], 'input' => [ 'shape' => 'DeletePackageGroupRequest', ], 'output' => [ 'shape' => 'DeletePackageGroupResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeletePackageVersions' => [ 'name' => 'DeletePackageVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/package/versions/delete', ], 'input' => [ 'shape' => 'DeletePackageVersionsRequest', ], 'output' => [ 'shape' => 'DeletePackageVersionsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteRepository' => [ 'name' => 'DeleteRepository', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/repository', ], 'input' => [ 'shape' => 'DeleteRepositoryRequest', ], 'output' => [ 'shape' => 'DeleteRepositoryResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteRepositoryPermissionsPolicy' => [ 'name' => 'DeleteRepositoryPermissionsPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/repository/permissions/policies', ], 'input' => [ 'shape' => 'DeleteRepositoryPermissionsPolicyRequest', ], 'output' => [ 'shape' => 'DeleteRepositoryPermissionsPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeDomain' => [ 'name' => 'DescribeDomain', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/domain', ], 'input' => [ 'shape' => 'DescribeDomainRequest', ], 'output' => [ 'shape' => 'DescribeDomainResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribePackage' => [ 'name' => 'DescribePackage', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/package', ], 'input' => [ 'shape' => 'DescribePackageRequest', ], 'output' => [ 'shape' => 'DescribePackageResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribePackageGroup' => [ 'name' => 'DescribePackageGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/package-group', ], 'input' => [ 'shape' => 'DescribePackageGroupRequest', ], 'output' => [ 'shape' => 'DescribePackageGroupResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribePackageVersion' => [ 'name' => 'DescribePackageVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/package/version', ], 'input' => [ 'shape' => 'DescribePackageVersionRequest', ], 'output' => [ 'shape' => 'DescribePackageVersionResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeRepository' => [ 'name' => 'DescribeRepository', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/repository', ], 'input' => [ 'shape' => 'DescribeRepositoryRequest', ], 'output' => [ 'shape' => 'DescribeRepositoryResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DisassociateExternalConnection' => [ 'name' => 'DisassociateExternalConnection', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/repository/external-connection', ], 'input' => [ 'shape' => 'DisassociateExternalConnectionRequest', ], 'output' => [ 'shape' => 'DisassociateExternalConnectionResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DisposePackageVersions' => [ 'name' => 'DisposePackageVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/package/versions/dispose', ], 'input' => [ 'shape' => 'DisposePackageVersionsRequest', ], 'output' => [ 'shape' => 'DisposePackageVersionsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetAssociatedPackageGroup' => [ 'name' => 'GetAssociatedPackageGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/get-associated-package-group', ], 'input' => [ 'shape' => 'GetAssociatedPackageGroupRequest', ], 'output' => [ 'shape' => 'GetAssociatedPackageGroupResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAuthorizationToken' => [ 'name' => 'GetAuthorizationToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/authorization-token', ], 'input' => [ 'shape' => 'GetAuthorizationTokenRequest', ], 'output' => [ 'shape' => 'GetAuthorizationTokenResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetDomainPermissionsPolicy' => [ 'name' => 'GetDomainPermissionsPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/domain/permissions/policy', ], 'input' => [ 'shape' => 'GetDomainPermissionsPolicyRequest', ], 'output' => [ 'shape' => 'GetDomainPermissionsPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetPackageVersionAsset' => [ 'name' => 'GetPackageVersionAsset', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/package/version/asset', ], 'input' => [ 'shape' => 'GetPackageVersionAssetRequest', ], 'output' => [ 'shape' => 'GetPackageVersionAssetResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetPackageVersionReadme' => [ 'name' => 'GetPackageVersionReadme', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/package/version/readme', ], 'input' => [ 'shape' => 'GetPackageVersionReadmeRequest', ], 'output' => [ 'shape' => 'GetPackageVersionReadmeResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetRepositoryEndpoint' => [ 'name' => 'GetRepositoryEndpoint', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/repository/endpoint', ], 'input' => [ 'shape' => 'GetRepositoryEndpointRequest', ], 'output' => [ 'shape' => 'GetRepositoryEndpointResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetRepositoryPermissionsPolicy' => [ 'name' => 'GetRepositoryPermissionsPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/repository/permissions/policy', ], 'input' => [ 'shape' => 'GetRepositoryPermissionsPolicyRequest', ], 'output' => [ 'shape' => 'GetRepositoryPermissionsPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListAllowedRepositoriesForGroup' => [ 'name' => 'ListAllowedRepositoriesForGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/package-group-allowed-repositories', ], 'input' => [ 'shape' => 'ListAllowedRepositoriesForGroupRequest', ], 'output' => [ 'shape' => 'ListAllowedRepositoriesForGroupResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAssociatedPackages' => [ 'name' => 'ListAssociatedPackages', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/list-associated-packages', ], 'input' => [ 'shape' => 'ListAssociatedPackagesRequest', ], 'output' => [ 'shape' => 'ListAssociatedPackagesResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListDomains' => [ 'name' => 'ListDomains', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/domains', ], 'input' => [ 'shape' => 'ListDomainsRequest', ], 'output' => [ 'shape' => 'ListDomainsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListPackageGroups' => [ 'name' => 'ListPackageGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/package-groups', ], 'input' => [ 'shape' => 'ListPackageGroupsRequest', ], 'output' => [ 'shape' => 'ListPackageGroupsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListPackageVersionAssets' => [ 'name' => 'ListPackageVersionAssets', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/package/version/assets', ], 'input' => [ 'shape' => 'ListPackageVersionAssetsRequest', ], 'output' => [ 'shape' => 'ListPackageVersionAssetsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListPackageVersionDependencies' => [ 'name' => 'ListPackageVersionDependencies', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/package/version/dependencies', ], 'input' => [ 'shape' => 'ListPackageVersionDependenciesRequest', ], 'output' => [ 'shape' => 'ListPackageVersionDependenciesResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListPackageVersions' => [ 'name' => 'ListPackageVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/package/versions', ], 'input' => [ 'shape' => 'ListPackageVersionsRequest', ], 'output' => [ 'shape' => 'ListPackageVersionsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListPackages' => [ 'name' => 'ListPackages', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/packages', ], 'input' => [ 'shape' => 'ListPackagesRequest', ], 'output' => [ 'shape' => 'ListPackagesResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListRepositories' => [ 'name' => 'ListRepositories', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/repositories', ], 'input' => [ 'shape' => 'ListRepositoriesRequest', ], 'output' => [ 'shape' => 'ListRepositoriesResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListRepositoriesInDomain' => [ 'name' => 'ListRepositoriesInDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/domain/repositories', ], 'input' => [ 'shape' => 'ListRepositoriesInDomainRequest', ], 'output' => [ 'shape' => 'ListRepositoriesInDomainResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListSubPackageGroups' => [ 'name' => 'ListSubPackageGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/package-groups/sub-groups', ], 'input' => [ 'shape' => 'ListSubPackageGroupsRequest', ], 'output' => [ 'shape' => 'ListSubPackageGroupsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/tags', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'PublishPackageVersion' => [ 'name' => 'PublishPackageVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/package/version/publish', ], 'input' => [ 'shape' => 'PublishPackageVersionRequest', ], 'output' => [ 'shape' => 'PublishPackageVersionResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'PutDomainPermissionsPolicy' => [ 'name' => 'PutDomainPermissionsPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/domain/permissions/policy', ], 'input' => [ 'shape' => 'PutDomainPermissionsPolicyRequest', ], 'output' => [ 'shape' => 'PutDomainPermissionsPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'PutPackageOriginConfiguration' => [ 'name' => 'PutPackageOriginConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/package', ], 'input' => [ 'shape' => 'PutPackageOriginConfigurationRequest', ], 'output' => [ 'shape' => 'PutPackageOriginConfigurationResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'PutRepositoryPermissionsPolicy' => [ 'name' => 'PutRepositoryPermissionsPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/repository/permissions/policy', ], 'input' => [ 'shape' => 'PutRepositoryPermissionsPolicyRequest', ], 'output' => [ 'shape' => 'PutRepositoryPermissionsPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/tag', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/untag', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdatePackageGroup' => [ 'name' => 'UpdatePackageGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/package-group', ], 'input' => [ 'shape' => 'UpdatePackageGroupRequest', ], 'output' => [ 'shape' => 'UpdatePackageGroupResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdatePackageGroupOriginConfiguration' => [ 'name' => 'UpdatePackageGroupOriginConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/package-group-origin-configuration', ], 'input' => [ 'shape' => 'UpdatePackageGroupOriginConfigurationRequest', ], 'output' => [ 'shape' => 'UpdatePackageGroupOriginConfigurationResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdatePackageVersionsStatus' => [ 'name' => 'UpdatePackageVersionsStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/package/versions/update_status', ], 'input' => [ 'shape' => 'UpdatePackageVersionsStatusRequest', ], 'output' => [ 'shape' => 'UpdatePackageVersionsStatusResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateRepository' => [ 'name' => 'UpdateRepository', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/repository', ], 'input' => [ 'shape' => 'UpdateRepositoryRequest', ], 'output' => [ 'shape' => 'UpdateRepositoryResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '[0-9]{12}', ], 'AllowPublish' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'BLOCK', ], ], 'AllowUpstream' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'BLOCK', ], ], 'Arn' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => '\\S+', ], 'Asset' => [ 'type' => 'blob', 'streaming' => true, ], 'AssetHashes' => [ 'type' => 'map', 'key' => [ 'shape' => 'HashAlgorithm', ], 'value' => [ 'shape' => 'HashValue', ], ], 'AssetName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '\\P{C}+', ], 'AssetSummary' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'AssetName', ], 'size' => [ 'shape' => 'LongOptional', ], 'hashes' => [ 'shape' => 'AssetHashes', ], ], ], 'AssetSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetSummary', ], ], 'AssociateExternalConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', 'externalConnection', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'externalConnection' => [ 'shape' => 'ExternalConnectionName', 'location' => 'querystring', 'locationName' => 'external-connection', ], ], ], 'AssociateExternalConnectionResult' => [ 'type' => 'structure', 'members' => [ 'repository' => [ 'shape' => 'RepositoryDescription', ], ], ], 'AssociatedPackage' => [ 'type' => 'structure', 'members' => [ 'format' => [ 'shape' => 'PackageFormat', ], 'namespace' => [ 'shape' => 'PackageNamespace', ], 'package' => [ 'shape' => 'PackageName', ], 'associationType' => [ 'shape' => 'PackageGroupAssociationType', ], ], ], 'AssociatedPackageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociatedPackage', ], ], 'AuthorizationTokenDurationSeconds' => [ 'type' => 'long', 'max' => 43200, 'min' => 0, ], 'BooleanOptional' => [ 'type' => 'boolean', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CopyPackageVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'sourceRepository', 'destinationRepository', 'format', 'package', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'sourceRepository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'source-repository', ], 'destinationRepository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'destination-repository', ], 'format' => [ 'shape' => 'PackageFormat', 'location' => 'querystring', 'locationName' => 'format', ], 'namespace' => [ 'shape' => 'PackageNamespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'package' => [ 'shape' => 'PackageName', 'location' => 'querystring', 'locationName' => 'package', ], 'versions' => [ 'shape' => 'PackageVersionList', ], 'versionRevisions' => [ 'shape' => 'PackageVersionRevisionMap', ], 'allowOverwrite' => [ 'shape' => 'BooleanOptional', ], 'includeFromUpstream' => [ 'shape' => 'BooleanOptional', ], ], ], 'CopyPackageVersionsResult' => [ 'type' => 'structure', 'members' => [ 'successfulVersions' => [ 'shape' => 'SuccessfulPackageVersionInfoMap', ], 'failedVersions' => [ 'shape' => 'PackageVersionErrorMap', ], ], ], 'CreateDomainRequest' => [ 'type' => 'structure', 'required' => [ 'domain', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'encryptionKey' => [ 'shape' => 'Arn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDomainResult' => [ 'type' => 'structure', 'members' => [ 'domain' => [ 'shape' => 'DomainDescription', ], ], ], 'CreatePackageGroupRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'packageGroup', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'packageGroup' => [ 'shape' => 'PackageGroupPattern', ], 'contactInfo' => [ 'shape' => 'PackageGroupContactInfo', ], 'description' => [ 'shape' => 'Description', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreatePackageGroupResult' => [ 'type' => 'structure', 'members' => [ 'packageGroup' => [ 'shape' => 'PackageGroupDescription', ], ], ], 'CreateRepositoryRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'description' => [ 'shape' => 'Description', ], 'upstreams' => [ 'shape' => 'UpstreamRepositoryList', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateRepositoryResult' => [ 'type' => 'structure', 'members' => [ 'repository' => [ 'shape' => 'RepositoryDescription', ], ], ], 'DeleteDomainPermissionsPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'domain', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'policyRevision' => [ 'shape' => 'PolicyRevision', 'location' => 'querystring', 'locationName' => 'policy-revision', ], ], ], 'DeleteDomainPermissionsPolicyResult' => [ 'type' => 'structure', 'members' => [ 'policy' => [ 'shape' => 'ResourcePolicy', ], ], ], 'DeleteDomainRequest' => [ 'type' => 'structure', 'required' => [ 'domain', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], ], ], 'DeleteDomainResult' => [ 'type' => 'structure', 'members' => [ 'domain' => [ 'shape' => 'DomainDescription', ], ], ], 'DeletePackageGroupRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'packageGroup', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'packageGroup' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'package-group', ], ], ], 'DeletePackageGroupResult' => [ 'type' => 'structure', 'members' => [ 'packageGroup' => [ 'shape' => 'PackageGroupDescription', ], ], ], 'DeletePackageRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', 'format', 'package', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'format' => [ 'shape' => 'PackageFormat', 'location' => 'querystring', 'locationName' => 'format', ], 'namespace' => [ 'shape' => 'PackageNamespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'package' => [ 'shape' => 'PackageName', 'location' => 'querystring', 'locationName' => 'package', ], ], ], 'DeletePackageResult' => [ 'type' => 'structure', 'members' => [ 'deletedPackage' => [ 'shape' => 'PackageSummary', ], ], ], 'DeletePackageVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', 'format', 'package', 'versions', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'format' => [ 'shape' => 'PackageFormat', 'location' => 'querystring', 'locationName' => 'format', ], 'namespace' => [ 'shape' => 'PackageNamespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'package' => [ 'shape' => 'PackageName', 'location' => 'querystring', 'locationName' => 'package', ], 'versions' => [ 'shape' => 'PackageVersionList', ], 'expectedStatus' => [ 'shape' => 'PackageVersionStatus', ], ], ], 'DeletePackageVersionsResult' => [ 'type' => 'structure', 'members' => [ 'successfulVersions' => [ 'shape' => 'SuccessfulPackageVersionInfoMap', ], 'failedVersions' => [ 'shape' => 'PackageVersionErrorMap', ], ], ], 'DeleteRepositoryPermissionsPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'policyRevision' => [ 'shape' => 'PolicyRevision', 'location' => 'querystring', 'locationName' => 'policy-revision', ], ], ], 'DeleteRepositoryPermissionsPolicyResult' => [ 'type' => 'structure', 'members' => [ 'policy' => [ 'shape' => 'ResourcePolicy', ], ], ], 'DeleteRepositoryRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], ], ], 'DeleteRepositoryResult' => [ 'type' => 'structure', 'members' => [ 'repository' => [ 'shape' => 'RepositoryDescription', ], ], ], 'DescribeDomainRequest' => [ 'type' => 'structure', 'required' => [ 'domain', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], ], ], 'DescribeDomainResult' => [ 'type' => 'structure', 'members' => [ 'domain' => [ 'shape' => 'DomainDescription', ], ], ], 'DescribePackageGroupRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'packageGroup', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'packageGroup' => [ 'shape' => 'PackageGroupPattern', 'location' => 'querystring', 'locationName' => 'package-group', ], ], ], 'DescribePackageGroupResult' => [ 'type' => 'structure', 'members' => [ 'packageGroup' => [ 'shape' => 'PackageGroupDescription', ], ], ], 'DescribePackageRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', 'format', 'package', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'format' => [ 'shape' => 'PackageFormat', 'location' => 'querystring', 'locationName' => 'format', ], 'namespace' => [ 'shape' => 'PackageNamespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'package' => [ 'shape' => 'PackageName', 'location' => 'querystring', 'locationName' => 'package', ], ], ], 'DescribePackageResult' => [ 'type' => 'structure', 'required' => [ 'package', ], 'members' => [ 'package' => [ 'shape' => 'PackageDescription', ], ], ], 'DescribePackageVersionRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', 'format', 'package', 'packageVersion', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'format' => [ 'shape' => 'PackageFormat', 'location' => 'querystring', 'locationName' => 'format', ], 'namespace' => [ 'shape' => 'PackageNamespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'package' => [ 'shape' => 'PackageName', 'location' => 'querystring', 'locationName' => 'package', ], 'packageVersion' => [ 'shape' => 'PackageVersion', 'location' => 'querystring', 'locationName' => 'version', ], ], ], 'DescribePackageVersionResult' => [ 'type' => 'structure', 'required' => [ 'packageVersion', ], 'members' => [ 'packageVersion' => [ 'shape' => 'PackageVersionDescription', ], ], ], 'DescribeRepositoryRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], ], ], 'DescribeRepositoryResult' => [ 'type' => 'structure', 'members' => [ 'repository' => [ 'shape' => 'RepositoryDescription', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '\\P{C}*', ], 'DisassociateExternalConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', 'externalConnection', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'externalConnection' => [ 'shape' => 'ExternalConnectionName', 'location' => 'querystring', 'locationName' => 'external-connection', ], ], ], 'DisassociateExternalConnectionResult' => [ 'type' => 'structure', 'members' => [ 'repository' => [ 'shape' => 'RepositoryDescription', ], ], ], 'DisposePackageVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', 'format', 'package', 'versions', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'format' => [ 'shape' => 'PackageFormat', 'location' => 'querystring', 'locationName' => 'format', ], 'namespace' => [ 'shape' => 'PackageNamespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'package' => [ 'shape' => 'PackageName', 'location' => 'querystring', 'locationName' => 'package', ], 'versions' => [ 'shape' => 'PackageVersionList', ], 'versionRevisions' => [ 'shape' => 'PackageVersionRevisionMap', ], 'expectedStatus' => [ 'shape' => 'PackageVersionStatus', ], ], ], 'DisposePackageVersionsResult' => [ 'type' => 'structure', 'members' => [ 'successfulVersions' => [ 'shape' => 'SuccessfulPackageVersionInfoMap', ], 'failedVersions' => [ 'shape' => 'PackageVersionErrorMap', ], ], ], 'DomainDescription' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'DomainName', ], 'owner' => [ 'shape' => 'AccountId', ], 'arn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'DomainStatus', ], 'createdTime' => [ 'shape' => 'Timestamp', ], 'encryptionKey' => [ 'shape' => 'Arn', ], 'repositoryCount' => [ 'shape' => 'Integer', ], 'assetSizeBytes' => [ 'shape' => 'Long', ], 's3BucketArn' => [ 'shape' => 'Arn', ], ], ], 'DomainEntryPoint' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'externalConnectionName' => [ 'shape' => 'ExternalConnectionName', ], ], ], 'DomainName' => [ 'type' => 'string', 'max' => 50, 'min' => 2, 'pattern' => '[a-z][a-z0-9\\-]{0,48}[a-z0-9]', ], 'DomainStatus' => [ 'type' => 'string', 'enum' => [ 'Active', 'Deleted', ], ], 'DomainSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'DomainName', ], 'owner' => [ 'shape' => 'AccountId', ], 'arn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'DomainStatus', ], 'createdTime' => [ 'shape' => 'Timestamp', ], 'encryptionKey' => [ 'shape' => 'Arn', ], ], ], 'DomainSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainSummary', ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'ExternalConnectionName' => [ 'type' => 'string', 'max' => 100, 'min' => 2, 'pattern' => '[A-Za-z0-9][A-Za-z0-9._\\-:]{1,99}', ], 'ExternalConnectionStatus' => [ 'type' => 'string', 'enum' => [ 'Available', ], ], 'GetAssociatedPackageGroupRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'format', 'package', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'format' => [ 'shape' => 'PackageFormat', 'location' => 'querystring', 'locationName' => 'format', ], 'namespace' => [ 'shape' => 'PackageNamespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'package' => [ 'shape' => 'PackageName', 'location' => 'querystring', 'locationName' => 'package', ], ], ], 'GetAssociatedPackageGroupResult' => [ 'type' => 'structure', 'members' => [ 'packageGroup' => [ 'shape' => 'PackageGroupDescription', ], 'associationType' => [ 'shape' => 'PackageGroupAssociationType', ], ], ], 'GetAuthorizationTokenRequest' => [ 'type' => 'structure', 'required' => [ 'domain', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'durationSeconds' => [ 'shape' => 'AuthorizationTokenDurationSeconds', 'location' => 'querystring', 'locationName' => 'duration', ], ], ], 'GetAuthorizationTokenResult' => [ 'type' => 'structure', 'members' => [ 'authorizationToken' => [ 'shape' => 'String', ], 'expiration' => [ 'shape' => 'Timestamp', ], ], 'sensitive' => true, ], 'GetDomainPermissionsPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'domain', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], ], ], 'GetDomainPermissionsPolicyResult' => [ 'type' => 'structure', 'members' => [ 'policy' => [ 'shape' => 'ResourcePolicy', ], ], ], 'GetPackageVersionAssetRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', 'format', 'package', 'packageVersion', 'asset', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'format' => [ 'shape' => 'PackageFormat', 'location' => 'querystring', 'locationName' => 'format', ], 'namespace' => [ 'shape' => 'PackageNamespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'package' => [ 'shape' => 'PackageName', 'location' => 'querystring', 'locationName' => 'package', ], 'packageVersion' => [ 'shape' => 'PackageVersion', 'location' => 'querystring', 'locationName' => 'version', ], 'asset' => [ 'shape' => 'AssetName', 'location' => 'querystring', 'locationName' => 'asset', ], 'packageVersionRevision' => [ 'shape' => 'PackageVersionRevision', 'location' => 'querystring', 'locationName' => 'revision', ], ], ], 'GetPackageVersionAssetResult' => [ 'type' => 'structure', 'members' => [ 'asset' => [ 'shape' => 'Asset', ], 'assetName' => [ 'shape' => 'AssetName', 'location' => 'header', 'locationName' => 'X-AssetName', ], 'packageVersion' => [ 'shape' => 'PackageVersion', 'location' => 'header', 'locationName' => 'X-PackageVersion', ], 'packageVersionRevision' => [ 'shape' => 'PackageVersionRevision', 'location' => 'header', 'locationName' => 'X-PackageVersionRevision', ], ], 'payload' => 'asset', ], 'GetPackageVersionReadmeRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', 'format', 'package', 'packageVersion', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'format' => [ 'shape' => 'PackageFormat', 'location' => 'querystring', 'locationName' => 'format', ], 'namespace' => [ 'shape' => 'PackageNamespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'package' => [ 'shape' => 'PackageName', 'location' => 'querystring', 'locationName' => 'package', ], 'packageVersion' => [ 'shape' => 'PackageVersion', 'location' => 'querystring', 'locationName' => 'version', ], ], ], 'GetPackageVersionReadmeResult' => [ 'type' => 'structure', 'members' => [ 'format' => [ 'shape' => 'PackageFormat', ], 'namespace' => [ 'shape' => 'PackageNamespace', ], 'package' => [ 'shape' => 'PackageName', ], 'version' => [ 'shape' => 'PackageVersion', ], 'versionRevision' => [ 'shape' => 'PackageVersionRevision', ], 'readme' => [ 'shape' => 'String', ], ], ], 'GetRepositoryEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', 'format', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'format' => [ 'shape' => 'PackageFormat', 'location' => 'querystring', 'locationName' => 'format', ], ], ], 'GetRepositoryEndpointResult' => [ 'type' => 'structure', 'members' => [ 'repositoryEndpoint' => [ 'shape' => 'String', ], ], ], 'GetRepositoryPermissionsPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], ], ], 'GetRepositoryPermissionsPolicyResult' => [ 'type' => 'structure', 'members' => [ 'policy' => [ 'shape' => 'ResourcePolicy', ], ], ], 'HashAlgorithm' => [ 'type' => 'string', 'enum' => [ 'MD5', 'SHA-1', 'SHA-256', 'SHA-512', ], ], 'HashValue' => [ 'type' => 'string', 'max' => 512, 'min' => 32, 'pattern' => '[0-9a-f]+', ], 'Integer' => [ 'type' => 'integer', ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'LicenseInfo' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'url' => [ 'shape' => 'String', ], ], ], 'LicenseInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LicenseInfo', ], ], 'ListAllowedRepositoriesForGroupMaxResults' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'ListAllowedRepositoriesForGroupRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'packageGroup', 'originRestrictionType', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'packageGroup' => [ 'shape' => 'PackageGroupPattern', 'location' => 'querystring', 'locationName' => 'package-group', ], 'originRestrictionType' => [ 'shape' => 'PackageGroupOriginRestrictionType', 'location' => 'querystring', 'locationName' => 'originRestrictionType', ], 'maxResults' => [ 'shape' => 'ListAllowedRepositoriesForGroupMaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListAllowedRepositoriesForGroupResult' => [ 'type' => 'structure', 'members' => [ 'allowedRepositories' => [ 'shape' => 'RepositoryNameList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListAssociatedPackagesRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'packageGroup', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'packageGroup' => [ 'shape' => 'PackageGroupPattern', 'location' => 'querystring', 'locationName' => 'package-group', ], 'maxResults' => [ 'shape' => 'ListPackagesMaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'next-token', ], 'preview' => [ 'shape' => 'BooleanOptional', 'location' => 'querystring', 'locationName' => 'preview', ], ], ], 'ListAssociatedPackagesResult' => [ 'type' => 'structure', 'members' => [ 'packages' => [ 'shape' => 'AssociatedPackageList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDomainsMaxResults' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'ListDomainsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListDomainsMaxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDomainsResult' => [ 'type' => 'structure', 'members' => [ 'domains' => [ 'shape' => 'DomainSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListPackageGroupsMaxResults' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'ListPackageGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'domain', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'maxResults' => [ 'shape' => 'ListPackageGroupsMaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'next-token', ], 'prefix' => [ 'shape' => 'PackageGroupPatternPrefix', 'location' => 'querystring', 'locationName' => 'prefix', ], ], ], 'ListPackageGroupsResult' => [ 'type' => 'structure', 'members' => [ 'packageGroups' => [ 'shape' => 'PackageGroupSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListPackageVersionAssetsMaxResults' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'ListPackageVersionAssetsRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', 'format', 'package', 'packageVersion', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'format' => [ 'shape' => 'PackageFormat', 'location' => 'querystring', 'locationName' => 'format', ], 'namespace' => [ 'shape' => 'PackageNamespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'package' => [ 'shape' => 'PackageName', 'location' => 'querystring', 'locationName' => 'package', ], 'packageVersion' => [ 'shape' => 'PackageVersion', 'location' => 'querystring', 'locationName' => 'version', ], 'maxResults' => [ 'shape' => 'ListPackageVersionAssetsMaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListPackageVersionAssetsResult' => [ 'type' => 'structure', 'members' => [ 'format' => [ 'shape' => 'PackageFormat', ], 'namespace' => [ 'shape' => 'PackageNamespace', ], 'package' => [ 'shape' => 'PackageName', ], 'version' => [ 'shape' => 'PackageVersion', ], 'versionRevision' => [ 'shape' => 'PackageVersionRevision', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'assets' => [ 'shape' => 'AssetSummaryList', ], ], ], 'ListPackageVersionDependenciesRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', 'format', 'package', 'packageVersion', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'format' => [ 'shape' => 'PackageFormat', 'location' => 'querystring', 'locationName' => 'format', ], 'namespace' => [ 'shape' => 'PackageNamespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'package' => [ 'shape' => 'PackageName', 'location' => 'querystring', 'locationName' => 'package', ], 'packageVersion' => [ 'shape' => 'PackageVersion', 'location' => 'querystring', 'locationName' => 'version', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListPackageVersionDependenciesResult' => [ 'type' => 'structure', 'members' => [ 'format' => [ 'shape' => 'PackageFormat', ], 'namespace' => [ 'shape' => 'PackageNamespace', ], 'package' => [ 'shape' => 'PackageName', ], 'version' => [ 'shape' => 'PackageVersion', ], 'versionRevision' => [ 'shape' => 'PackageVersionRevision', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'dependencies' => [ 'shape' => 'PackageDependencyList', ], ], ], 'ListPackageVersionsMaxResults' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'ListPackageVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', 'format', 'package', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'format' => [ 'shape' => 'PackageFormat', 'location' => 'querystring', 'locationName' => 'format', ], 'namespace' => [ 'shape' => 'PackageNamespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'package' => [ 'shape' => 'PackageName', 'location' => 'querystring', 'locationName' => 'package', ], 'status' => [ 'shape' => 'PackageVersionStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'sortBy' => [ 'shape' => 'PackageVersionSortType', 'location' => 'querystring', 'locationName' => 'sortBy', ], 'maxResults' => [ 'shape' => 'ListPackageVersionsMaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'next-token', ], 'originType' => [ 'shape' => 'PackageVersionOriginType', 'location' => 'querystring', 'locationName' => 'originType', ], ], ], 'ListPackageVersionsResult' => [ 'type' => 'structure', 'members' => [ 'defaultDisplayVersion' => [ 'shape' => 'PackageVersion', ], 'format' => [ 'shape' => 'PackageFormat', ], 'namespace' => [ 'shape' => 'PackageNamespace', ], 'package' => [ 'shape' => 'PackageName', ], 'versions' => [ 'shape' => 'PackageVersionSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListPackagesMaxResults' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'ListPackagesRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'format' => [ 'shape' => 'PackageFormat', 'location' => 'querystring', 'locationName' => 'format', ], 'namespace' => [ 'shape' => 'PackageNamespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'packagePrefix' => [ 'shape' => 'PackageName', 'location' => 'querystring', 'locationName' => 'package-prefix', ], 'maxResults' => [ 'shape' => 'ListPackagesMaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'next-token', ], 'publish' => [ 'shape' => 'AllowPublish', 'location' => 'querystring', 'locationName' => 'publish', ], 'upstream' => [ 'shape' => 'AllowUpstream', 'location' => 'querystring', 'locationName' => 'upstream', ], ], ], 'ListPackagesResult' => [ 'type' => 'structure', 'members' => [ 'packages' => [ 'shape' => 'PackageSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListRepositoriesInDomainMaxResults' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'ListRepositoriesInDomainRequest' => [ 'type' => 'structure', 'required' => [ 'domain', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'administratorAccount' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'administrator-account', ], 'repositoryPrefix' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository-prefix', ], 'maxResults' => [ 'shape' => 'ListRepositoriesInDomainMaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListRepositoriesInDomainResult' => [ 'type' => 'structure', 'members' => [ 'repositories' => [ 'shape' => 'RepositorySummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListRepositoriesMaxResults' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'ListRepositoriesRequest' => [ 'type' => 'structure', 'members' => [ 'repositoryPrefix' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository-prefix', ], 'maxResults' => [ 'shape' => 'ListRepositoriesMaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListRepositoriesResult' => [ 'type' => 'structure', 'members' => [ 'repositories' => [ 'shape' => 'RepositorySummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListSubPackageGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'packageGroup', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'packageGroup' => [ 'shape' => 'PackageGroupPattern', 'location' => 'querystring', 'locationName' => 'package-group', ], 'maxResults' => [ 'shape' => 'ListPackageGroupsMaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListSubPackageGroupsResult' => [ 'type' => 'structure', 'members' => [ 'packageGroups' => [ 'shape' => 'PackageGroupSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResult' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagList', ], ], ], 'Long' => [ 'type' => 'long', ], 'LongOptional' => [ 'type' => 'long', ], 'OriginRestrictions' => [ 'type' => 'map', 'key' => [ 'shape' => 'PackageGroupOriginRestrictionType', ], 'value' => [ 'shape' => 'PackageGroupOriginRestrictionMode', ], ], 'PackageDependency' => [ 'type' => 'structure', 'members' => [ 'namespace' => [ 'shape' => 'PackageNamespace', ], 'package' => [ 'shape' => 'PackageName', ], 'dependencyType' => [ 'shape' => 'String', ], 'versionRequirement' => [ 'shape' => 'String', ], ], ], 'PackageDependencyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageDependency', ], ], 'PackageDescription' => [ 'type' => 'structure', 'members' => [ 'format' => [ 'shape' => 'PackageFormat', ], 'namespace' => [ 'shape' => 'PackageNamespace', ], 'name' => [ 'shape' => 'PackageName', ], 'originConfiguration' => [ 'shape' => 'PackageOriginConfiguration', ], ], ], 'PackageFormat' => [ 'type' => 'string', 'enum' => [ 'npm', 'pypi', 'maven', 'nuget', 'generic', 'ruby', 'swift', 'cargo', ], ], 'PackageGroupAllowedRepository' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'originRestrictionType' => [ 'shape' => 'PackageGroupOriginRestrictionType', ], ], ], 'PackageGroupAllowedRepositoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageGroupAllowedRepository', ], ], 'PackageGroupAllowedRepositoryUpdate' => [ 'type' => 'map', 'key' => [ 'shape' => 'PackageGroupAllowedRepositoryUpdateType', ], 'value' => [ 'shape' => 'RepositoryNameList', ], ], 'PackageGroupAllowedRepositoryUpdateType' => [ 'type' => 'string', 'enum' => [ 'ADDED', 'REMOVED', ], ], 'PackageGroupAllowedRepositoryUpdates' => [ 'type' => 'map', 'key' => [ 'shape' => 'PackageGroupOriginRestrictionType', ], 'value' => [ 'shape' => 'PackageGroupAllowedRepositoryUpdate', ], ], 'PackageGroupAssociationType' => [ 'type' => 'string', 'enum' => [ 'STRONG', 'WEAK', ], ], 'PackageGroupContactInfo' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '\\P{C}*', ], 'PackageGroupDescription' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'pattern' => [ 'shape' => 'PackageGroupPattern', ], 'domainName' => [ 'shape' => 'DomainName', ], 'domainOwner' => [ 'shape' => 'AccountId', ], 'createdTime' => [ 'shape' => 'Timestamp', ], 'contactInfo' => [ 'shape' => 'PackageGroupContactInfo', ], 'description' => [ 'shape' => 'Description', ], 'originConfiguration' => [ 'shape' => 'PackageGroupOriginConfiguration', ], 'parent' => [ 'shape' => 'PackageGroupReference', ], ], ], 'PackageGroupOriginConfiguration' => [ 'type' => 'structure', 'members' => [ 'restrictions' => [ 'shape' => 'PackageGroupOriginRestrictions', ], ], ], 'PackageGroupOriginRestriction' => [ 'type' => 'structure', 'members' => [ 'mode' => [ 'shape' => 'PackageGroupOriginRestrictionMode', ], 'effectiveMode' => [ 'shape' => 'PackageGroupOriginRestrictionMode', ], 'inheritedFrom' => [ 'shape' => 'PackageGroupReference', ], 'repositoriesCount' => [ 'shape' => 'LongOptional', ], ], ], 'PackageGroupOriginRestrictionMode' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'ALLOW_SPECIFIC_REPOSITORIES', 'BLOCK', 'INHERIT', ], ], 'PackageGroupOriginRestrictionType' => [ 'type' => 'string', 'enum' => [ 'EXTERNAL_UPSTREAM', 'INTERNAL_UPSTREAM', 'PUBLISH', ], ], 'PackageGroupOriginRestrictions' => [ 'type' => 'map', 'key' => [ 'shape' => 'PackageGroupOriginRestrictionType', ], 'value' => [ 'shape' => 'PackageGroupOriginRestriction', ], ], 'PackageGroupPattern' => [ 'type' => 'string', 'max' => 520, 'min' => 2, 'pattern' => '[^\\p{C}\\p{IsWhitespace}]+', ], 'PackageGroupPatternPrefix' => [ 'type' => 'string', 'max' => 520, 'min' => 0, 'pattern' => '\\P{C}*', ], 'PackageGroupReference' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'pattern' => [ 'shape' => 'PackageGroupPattern', ], ], ], 'PackageGroupSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'pattern' => [ 'shape' => 'PackageGroupPattern', ], 'domainName' => [ 'shape' => 'DomainName', ], 'domainOwner' => [ 'shape' => 'AccountId', ], 'createdTime' => [ 'shape' => 'Timestamp', ], 'contactInfo' => [ 'shape' => 'PackageGroupContactInfo', ], 'description' => [ 'shape' => 'Description', ], 'originConfiguration' => [ 'shape' => 'PackageGroupOriginConfiguration', ], 'parent' => [ 'shape' => 'PackageGroupReference', ], ], ], 'PackageGroupSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageGroupSummary', ], ], 'PackageName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[^#/\\s]+', ], 'PackageNamespace' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[^#/\\s]+', ], 'PackageOriginConfiguration' => [ 'type' => 'structure', 'members' => [ 'restrictions' => [ 'shape' => 'PackageOriginRestrictions', ], ], ], 'PackageOriginRestrictions' => [ 'type' => 'structure', 'required' => [ 'publish', 'upstream', ], 'members' => [ 'publish' => [ 'shape' => 'AllowPublish', ], 'upstream' => [ 'shape' => 'AllowUpstream', ], ], ], 'PackageSummary' => [ 'type' => 'structure', 'members' => [ 'format' => [ 'shape' => 'PackageFormat', ], 'namespace' => [ 'shape' => 'PackageNamespace', ], 'package' => [ 'shape' => 'PackageName', ], 'originConfiguration' => [ 'shape' => 'PackageOriginConfiguration', ], ], ], 'PackageSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageSummary', ], ], 'PackageVersion' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[^#/\\s]+', ], 'PackageVersionDescription' => [ 'type' => 'structure', 'members' => [ 'format' => [ 'shape' => 'PackageFormat', ], 'namespace' => [ 'shape' => 'PackageNamespace', ], 'packageName' => [ 'shape' => 'PackageName', ], 'displayName' => [ 'shape' => 'String255', ], 'version' => [ 'shape' => 'PackageVersion', ], 'summary' => [ 'shape' => 'String', ], 'homePage' => [ 'shape' => 'String', ], 'sourceCodeRepository' => [ 'shape' => 'String', ], 'publishedTime' => [ 'shape' => 'Timestamp', ], 'licenses' => [ 'shape' => 'LicenseInfoList', ], 'revision' => [ 'shape' => 'PackageVersionRevision', ], 'status' => [ 'shape' => 'PackageVersionStatus', ], 'origin' => [ 'shape' => 'PackageVersionOrigin', ], ], ], 'PackageVersionError' => [ 'type' => 'structure', 'members' => [ 'errorCode' => [ 'shape' => 'PackageVersionErrorCode', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'PackageVersionErrorCode' => [ 'type' => 'string', 'enum' => [ 'ALREADY_EXISTS', 'MISMATCHED_REVISION', 'MISMATCHED_STATUS', 'NOT_ALLOWED', 'NOT_FOUND', 'SKIPPED', ], ], 'PackageVersionErrorMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'PackageVersion', ], 'value' => [ 'shape' => 'PackageVersionError', ], ], 'PackageVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageVersion', ], 'max' => 100, ], 'PackageVersionOrigin' => [ 'type' => 'structure', 'members' => [ 'domainEntryPoint' => [ 'shape' => 'DomainEntryPoint', ], 'originType' => [ 'shape' => 'PackageVersionOriginType', ], ], ], 'PackageVersionOriginType' => [ 'type' => 'string', 'enum' => [ 'INTERNAL', 'EXTERNAL', 'UNKNOWN', ], ], 'PackageVersionRevision' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '\\S+', ], 'PackageVersionRevisionMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'PackageVersion', ], 'value' => [ 'shape' => 'PackageVersionRevision', ], ], 'PackageVersionSortType' => [ 'type' => 'string', 'enum' => [ 'PUBLISHED_TIME', ], ], 'PackageVersionStatus' => [ 'type' => 'string', 'enum' => [ 'Published', 'Unfinished', 'Unlisted', 'Archived', 'Disposed', 'Deleted', ], ], 'PackageVersionSummary' => [ 'type' => 'structure', 'required' => [ 'version', 'status', ], 'members' => [ 'version' => [ 'shape' => 'PackageVersion', ], 'revision' => [ 'shape' => 'PackageVersionRevision', ], 'status' => [ 'shape' => 'PackageVersionStatus', ], 'origin' => [ 'shape' => 'PackageVersionOrigin', ], ], ], 'PackageVersionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageVersionSummary', ], ], 'PaginationToken' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, 'pattern' => '\\S+', ], 'PolicyDocument' => [ 'type' => 'string', 'max' => 7168, 'min' => 1, 'pattern' => '[\\P{C}\\s]+', ], 'PolicyRevision' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '\\S+', ], 'PublishPackageVersionRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', 'format', 'package', 'packageVersion', 'assetContent', 'assetName', 'assetSHA256', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'format' => [ 'shape' => 'PackageFormat', 'location' => 'querystring', 'locationName' => 'format', ], 'namespace' => [ 'shape' => 'PackageNamespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'package' => [ 'shape' => 'PackageName', 'location' => 'querystring', 'locationName' => 'package', ], 'packageVersion' => [ 'shape' => 'PackageVersion', 'location' => 'querystring', 'locationName' => 'version', ], 'assetContent' => [ 'shape' => 'Asset', ], 'assetName' => [ 'shape' => 'AssetName', 'location' => 'querystring', 'locationName' => 'asset', ], 'assetSHA256' => [ 'shape' => 'SHA256', 'location' => 'header', 'locationName' => 'x-amz-content-sha256', ], 'unfinished' => [ 'shape' => 'BooleanOptional', 'location' => 'querystring', 'locationName' => 'unfinished', ], ], 'payload' => 'assetContent', ], 'PublishPackageVersionResult' => [ 'type' => 'structure', 'members' => [ 'format' => [ 'shape' => 'PackageFormat', ], 'namespace' => [ 'shape' => 'PackageNamespace', ], 'package' => [ 'shape' => 'PackageName', ], 'version' => [ 'shape' => 'PackageVersion', ], 'versionRevision' => [ 'shape' => 'PackageVersionRevision', ], 'status' => [ 'shape' => 'PackageVersionStatus', ], 'asset' => [ 'shape' => 'AssetSummary', ], ], ], 'PutDomainPermissionsPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'policyDocument', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', ], 'domainOwner' => [ 'shape' => 'AccountId', ], 'policyRevision' => [ 'shape' => 'PolicyRevision', ], 'policyDocument' => [ 'shape' => 'PolicyDocument', ], ], ], 'PutDomainPermissionsPolicyResult' => [ 'type' => 'structure', 'members' => [ 'policy' => [ 'shape' => 'ResourcePolicy', ], ], ], 'PutPackageOriginConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', 'format', 'package', 'restrictions', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'format' => [ 'shape' => 'PackageFormat', 'location' => 'querystring', 'locationName' => 'format', ], 'namespace' => [ 'shape' => 'PackageNamespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'package' => [ 'shape' => 'PackageName', 'location' => 'querystring', 'locationName' => 'package', ], 'restrictions' => [ 'shape' => 'PackageOriginRestrictions', ], ], ], 'PutPackageOriginConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'originConfiguration' => [ 'shape' => 'PackageOriginConfiguration', ], ], ], 'PutRepositoryPermissionsPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', 'policyDocument', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'policyRevision' => [ 'shape' => 'PolicyRevision', ], 'policyDocument' => [ 'shape' => 'PolicyDocument', ], ], ], 'PutRepositoryPermissionsPolicyResult' => [ 'type' => 'structure', 'members' => [ 'policy' => [ 'shape' => 'ResourcePolicy', ], ], ], 'RepositoryDescription' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'RepositoryName', ], 'administratorAccount' => [ 'shape' => 'AccountId', ], 'domainName' => [ 'shape' => 'DomainName', ], 'domainOwner' => [ 'shape' => 'AccountId', ], 'arn' => [ 'shape' => 'Arn', ], 'description' => [ 'shape' => 'Description', ], 'upstreams' => [ 'shape' => 'UpstreamRepositoryInfoList', ], 'externalConnections' => [ 'shape' => 'RepositoryExternalConnectionInfoList', ], 'createdTime' => [ 'shape' => 'Timestamp', ], ], ], 'RepositoryExternalConnectionInfo' => [ 'type' => 'structure', 'members' => [ 'externalConnectionName' => [ 'shape' => 'ExternalConnectionName', ], 'packageFormat' => [ 'shape' => 'PackageFormat', ], 'status' => [ 'shape' => 'ExternalConnectionStatus', ], ], ], 'RepositoryExternalConnectionInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryExternalConnectionInfo', ], ], 'RepositoryName' => [ 'type' => 'string', 'max' => 100, 'min' => 2, 'pattern' => '[A-Za-z0-9][A-Za-z0-9._\\-]{1,99}', ], 'RepositoryNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryName', ], ], 'RepositorySummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'RepositoryName', ], 'administratorAccount' => [ 'shape' => 'AccountId', ], 'domainName' => [ 'shape' => 'DomainName', ], 'domainOwner' => [ 'shape' => 'AccountId', ], 'arn' => [ 'shape' => 'Arn', ], 'description' => [ 'shape' => 'Description', ], 'createdTime' => [ 'shape' => 'Timestamp', ], ], ], 'RepositorySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositorySummary', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourcePolicy' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], 'revision' => [ 'shape' => 'PolicyRevision', ], 'document' => [ 'shape' => 'PolicyDocument', ], ], ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'domain', 'repository', 'package', 'package-version', 'asset', ], ], 'RetryAfterSeconds' => [ 'type' => 'integer', ], 'SHA256' => [ 'type' => 'string', 'max' => 64, 'min' => 64, 'pattern' => '[0-9a-f]+', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'String255' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'SuccessfulPackageVersionInfo' => [ 'type' => 'structure', 'members' => [ 'revision' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'PackageVersionStatus', ], ], ], 'SuccessfulPackageVersionInfoMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'PackageVersion', ], 'value' => [ 'shape' => 'SuccessfulPackageVersionInfo', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '\\P{C}+', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResult' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '\\P{C}*', ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdatePackageGroupOriginConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'packageGroup', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'packageGroup' => [ 'shape' => 'PackageGroupPattern', 'location' => 'querystring', 'locationName' => 'package-group', ], 'restrictions' => [ 'shape' => 'OriginRestrictions', ], 'addAllowedRepositories' => [ 'shape' => 'PackageGroupAllowedRepositoryList', ], 'removeAllowedRepositories' => [ 'shape' => 'PackageGroupAllowedRepositoryList', ], ], ], 'UpdatePackageGroupOriginConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'packageGroup' => [ 'shape' => 'PackageGroupDescription', ], 'allowedRepositoryUpdates' => [ 'shape' => 'PackageGroupAllowedRepositoryUpdates', ], ], ], 'UpdatePackageGroupRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'packageGroup', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'packageGroup' => [ 'shape' => 'PackageGroupPattern', ], 'contactInfo' => [ 'shape' => 'PackageGroupContactInfo', ], 'description' => [ 'shape' => 'Description', ], ], ], 'UpdatePackageGroupResult' => [ 'type' => 'structure', 'members' => [ 'packageGroup' => [ 'shape' => 'PackageGroupDescription', ], ], ], 'UpdatePackageVersionsStatusRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', 'format', 'package', 'versions', 'targetStatus', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'format' => [ 'shape' => 'PackageFormat', 'location' => 'querystring', 'locationName' => 'format', ], 'namespace' => [ 'shape' => 'PackageNamespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'package' => [ 'shape' => 'PackageName', 'location' => 'querystring', 'locationName' => 'package', ], 'versions' => [ 'shape' => 'PackageVersionList', ], 'versionRevisions' => [ 'shape' => 'PackageVersionRevisionMap', ], 'expectedStatus' => [ 'shape' => 'PackageVersionStatus', ], 'targetStatus' => [ 'shape' => 'PackageVersionStatus', ], ], ], 'UpdatePackageVersionsStatusResult' => [ 'type' => 'structure', 'members' => [ 'successfulVersions' => [ 'shape' => 'SuccessfulPackageVersionInfoMap', ], 'failedVersions' => [ 'shape' => 'PackageVersionErrorMap', ], ], ], 'UpdateRepositoryRequest' => [ 'type' => 'structure', 'required' => [ 'domain', 'repository', ], 'members' => [ 'domain' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domain', ], 'domainOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'domain-owner', ], 'repository' => [ 'shape' => 'RepositoryName', 'location' => 'querystring', 'locationName' => 'repository', ], 'description' => [ 'shape' => 'Description', ], 'upstreams' => [ 'shape' => 'UpstreamRepositoryList', ], ], ], 'UpdateRepositoryResult' => [ 'type' => 'structure', 'members' => [ 'repository' => [ 'shape' => 'RepositoryDescription', ], ], ], 'UpstreamRepository' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], ], ], 'UpstreamRepositoryInfo' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], ], ], 'UpstreamRepositoryInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpstreamRepositoryInfo', ], ], 'UpstreamRepositoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpstreamRepository', ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'CANNOT_PARSE', 'ENCRYPTION_KEY_ERROR', 'FIELD_VALIDATION_FAILED', 'UNKNOWN_OPERATION', 'OTHER', ], ], ],];
