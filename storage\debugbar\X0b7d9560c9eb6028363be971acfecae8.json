{"__meta": {"id": "X0b7d9560c9eb6028363be971acfecae8", "datetime": "2025-06-30 22:36:20", "utime": **********.341215, "method": "GET", "uri": "/add-to-cart/2353/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751322979.766858, "end": **********.341229, "duration": 0.5743708610534668, "duration_str": "574ms", "measures": [{"label": "Booting", "start": 1751322979.766858, "relative_start": 0, "end": **********.19249, "relative_end": **********.19249, "duration": 0.4256319999694824, "duration_str": "426ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.192503, "relative_start": 0.4256448745727539, "end": **********.341231, "relative_end": 2.1457672119140625e-06, "duration": 0.1487281322479248, "duration_str": "149ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48661072, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.030159999999999996, "accumulated_duration_str": "30.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.259427, "duration": 0.024579999999999998, "duration_str": "24.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 81.499}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.296799, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 81.499, "width_percent": 2.354}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.313672, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 83.853, "width_percent": 2.288}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.316191, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.141, "width_percent": 1.393}, {"sql": "select * from `product_services` where `product_services`.`id` = '2353' limit 1", "type": "query", "params": [], "bindings": ["2353"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.322118, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 87.533, "width_percent": 1.923}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2353 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2353", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.326694, "duration": 0.0028, "duration_str": "2.8ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 89.456, "width_percent": 9.284}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3310359, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 98.74, "width_percent": 1.26}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1983749915 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1983749915\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.320687, "xdebug_link": null}]}, "session": {"_token": "iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:2 [\n  2352 => array:9 [\n    \"name\" => \"مجسم شخصيات مختلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"2352\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2353 => array:8 [\n    \"name\" => \"مفتاح علبه\"\n    \"quantity\" => 1\n    \"price\" => \"6.00\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"id\" => \"2353\"\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2353/pos", "status_code": "<pre class=sf-dump id=sf-dump-1775441062 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1775441062\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1906618118 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1906618118\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-92511631 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-92511631\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=6nrx0y%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1aj6s00%7C1751322883410%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFOSWVKdk9ka2l1cmNCR2o0SjRxS3c9PSIsInZhbHVlIjoiWlBTR0dwOGs2ZnkrQW81cXZxTVI3OUlOVHJ1dDlSVlMwYzRGMzZtNm5wNXR4bklCaVdJM2RIbjhpdjlabU0rOWVaenV3LzhDWDFMenVLV1lTYStDeEdGZXNvVGZzOEpUSFR5UDhkamtBTjBQK1Fnenl4TlFCY1hWeVhSSHZoWnk4VFYycW1WZm5sSU90UCtMOGVlLy9Dc2xLUFlqK2JQYXNGZzNxVGJBS2dmcDlZUzZsRjV4Q0t4ajBuOVlpQUE5blpMQWhGNEdKWEM5cTVJU3JyNDl2bmV5cGhYTGNYRkswMG13T3VpWEJFaUVJSUhGK2VCcFQ3aWI1MUUxMFVEZnYwQkxKRlVpaStsR3BpRmVKaHZSblVVRW43cmErWkFENjNmOVV0Ukl1ekEwUk0wOVBEd2ZjUFhKMnVUVEpWdjJzVlFWV1NzWThIR3dhRjNYOFhOZmIvOU9yNVRoUzQ1Y2FJdHY0T3dUSDJ6TnZyMUZXazkwSnhOZGtBaVlaVCtvNVQwTDVXU25LUXYwbmZ1ZE1Xb2RDV3ZhUXE5a2JFSlhSL0RBcWlkUGh6eHpvcGY5cDNUSzE2QTBzaENyWTArM01kNXF1eDhNU1BuTStkZnpBaG9sa0pSVnhVQ2ZodjBNZDJUSlB4YUI3d2g4dU14bmVEMnZaemVyQ2pYNkQvYVAiLCJtYWMiOiJlNWM5YTNlMDVhMzgzYzYzNWU1YzA4Y2MxNzYyMjg5ZmRiNTM0YWYyN2ZhMTdkNDRiMzU4YjI0MDczMTQ0NmE0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkpNQXdDbURCODVwNFNVaDNidE5oNGc9PSIsInZhbHVlIjoiSWRDakNUR3draytjekVmNXFTTTlpWjQ5dDZyR1dhMHpsd0VTZFk0MXpuUmRxanAxdHFGalkwcjloWHpWVFVaV3hFT2xuQkxHSXFjaDAyZ1RVTnE1NmRNeE5mTDVieGFkaDJCUHJ6SHRHakIySjZJMmxGbFRWSmJpblZFUWpXTEN1U1g0YWlSR3d1UThKSDF6T2RMYzA1YW1uWTU1KzVrMHZYcStaaU05c1grUUduTXhuOXFFWDc3UFNTUWY3THZwd1gxR1ZSakdlRGh0N091V21qWkYwSUJkYjBETlVEaHJ2TGptUVhIb0t3Qnhqc0gvNVpyVmtHOUswVlViOTB1TmU5S2xPc3dpNmFWZmxFdnc1R1ladHlab2FYNDFxR1ozcWp2UmdmenJTLyszU0laejFSU3dEMFkraitBd1VjVXZMR3NJSXgxZFI0YmxMVVl0aXIvMmdZRGo4bkU2MWppbzlGcjdWNWlkd05EemszNFVBVjZydE9iNVUzbE9SOWdiOUJRV3JVbFExK1Q0cWxGSWs2U1QzZjBhL0h4RlJDYmVRYmZ5RTBiZDFOSCtZRkFWOWlkOFg0d0RiRG9HdzVCbk03Y1JCdlE3UGhBNkhxODMyT1Z4ZjNhMjZibmhaMnZDdDEwb1NTSEsvUHF6NVl3Vkt3Z25JcEtla1VPTm90SisiLCJtYWMiOiIyMTk2OTc2ODc1MmE4MTQ3ZTM2MjE3NjA2NDBlZmVjYzgzM2Q0ODA3NzI1MDhlZTM0NDMyMGJhOWI2MWIzYzhmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1570768109 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A9rdyCRm7SKpfljuMnVO7IpcgTBMITMjowAdsmJo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1570768109\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:36:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZHWm9BYXVPeVlJdTdXV3hhTFNvOFE9PSIsInZhbHVlIjoiNzF2MVA5cThrYkN0QWVBb0E3aURyZ3lmTnBSa3VGbW9DN1NqdVAzRVlFTU5hdWVZaGcrd1N0bS9jVStxY0wzS25XeFlPYkNOSmVvcGIxN2kvUzJNUGIwb3kraWh2b2VCdE1mbEh4ZDFlbER4ZEl1S3VPcXNiWi8vRDROb3RvTE81WDdTMmpkY21jK0R5cmViMFZtWUxGb1ZrV2xyVmtxcFlzYm00SHdPbCtvVU54VmxJV0hub3RUY2NCMTU2K3ZvT2M1Z1grTWJZbmd2VHZsejdVVTFsajB5THIvTzc0czNtUXZCazVhZnY0QWpnMkVjTWgreU5VTHNzL0tzZzF2bFBSVkNWYkJBVXVsczhWUlV0R05RQkZscU5PWXlhWU8zQlNMMXZtNllyaXE0Vm4xV2xUL2wxcHAvcWJiK1krUUU3MlV0U3hrSXovdk0xb3BzRjNkdGNkUkFkZjd4THpSMENTV09MMWJJY3hQQzBlOGJUNWpIWlNVVjhZZjlCcHUvSDBKRGQ1N2lGRUFaMVh6VlNYK1BISXFPdUxIaUtCZmFxUHpXblFDWUFORnZDclVKYVIrZFdsRGRqWFNVVUhieVhxcFQ0L2pybGNXZzJycHVqL3Q2c0orblcxaHpsOUpkM2JRNEk0TDVBRW5IOVRPYXFUT3E2Wkd4dFBuVDVTZ3QiLCJtYWMiOiI5MmQwNjQyNzVmMmFhYTFiZmM2ODM1N2Y4ZTY3ODlmMTRhYzM4YjVlMzgyMWExZmU1NWJiYTk3NzI5NjNlNzk5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkRZc0phVm1aenBhdlBwdlpRekYrNFE9PSIsInZhbHVlIjoiQnRqMXdQNmgyTUxwZWZHRnZVUnJHSElGTmlnWUVTNnB3US9KR0dqZVBYS29Jb29TeXpMR050NWg4WVNWcVdWeGZTV0VvNGVtRmlxeU1YQlpUVFIyRTA1TWtWSUdCdmdsNng1ekpVUDdrdTl2R1JJUUk1Uk1wYVJoOU5uOFNFbmo4bXR1UXRiZWpHbnpFM0lZN2wvOXVQdFdPR3ZCSkVqMkQxQyttaVpsbEVsYUxPd25EN1VsR21oSVg3S2VBQ1dXaFhFR2djaERQNzloTmQ2YXFhY1AxUVZYa29SRHNUZ3JOTFl1cnYraDRnVFVWYmVwbll5ZXhoVXJVV29rbmpYYmNDZFBPdk9vMXh0YkxlUWdLL1U5SGdMTm9HNmdSSFZtTllnQTRTVHp3U0lxVW9RZzBtSU1qdTYrYWJUYUxmLzI2OU43YnVmbkJYb2xteUxxNnhZcnFwdWxyMHJTeVp3dkljMk4zalo2TUtnYm9Ed2VpWGZUM3hWblJwdVR2dFpYdlFRZzNreTFQUG9FaHBwTzQ2c2s2eEsvemRPTlFtaFo0SGlaNVBveUU0dkI4Ly9XaDlmSGhnK3krTmxGdjNHTnR6Ny9BUEpOTFNyNGFTZHVVU3dSWUxFSmFBS1Z6NHJpT05iNTRyM1cxb0taKzZVcmFPSFJCQlk1NSthMFpiaWsiLCJtYWMiOiI3YTExZTM3YTkzOTUwOGE3ZjU0ODBmNmQ3MTAxNGMyZDUzYjk1ZDVhNDY3OTgxOTBhZDg0YjI2NDM4ZWU2ODdiIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZHWm9BYXVPeVlJdTdXV3hhTFNvOFE9PSIsInZhbHVlIjoiNzF2MVA5cThrYkN0QWVBb0E3aURyZ3lmTnBSa3VGbW9DN1NqdVAzRVlFTU5hdWVZaGcrd1N0bS9jVStxY0wzS25XeFlPYkNOSmVvcGIxN2kvUzJNUGIwb3kraWh2b2VCdE1mbEh4ZDFlbER4ZEl1S3VPcXNiWi8vRDROb3RvTE81WDdTMmpkY21jK0R5cmViMFZtWUxGb1ZrV2xyVmtxcFlzYm00SHdPbCtvVU54VmxJV0hub3RUY2NCMTU2K3ZvT2M1Z1grTWJZbmd2VHZsejdVVTFsajB5THIvTzc0czNtUXZCazVhZnY0QWpnMkVjTWgreU5VTHNzL0tzZzF2bFBSVkNWYkJBVXVsczhWUlV0R05RQkZscU5PWXlhWU8zQlNMMXZtNllyaXE0Vm4xV2xUL2wxcHAvcWJiK1krUUU3MlV0U3hrSXovdk0xb3BzRjNkdGNkUkFkZjd4THpSMENTV09MMWJJY3hQQzBlOGJUNWpIWlNVVjhZZjlCcHUvSDBKRGQ1N2lGRUFaMVh6VlNYK1BISXFPdUxIaUtCZmFxUHpXblFDWUFORnZDclVKYVIrZFdsRGRqWFNVVUhieVhxcFQ0L2pybGNXZzJycHVqL3Q2c0orblcxaHpsOUpkM2JRNEk0TDVBRW5IOVRPYXFUT3E2Wkd4dFBuVDVTZ3QiLCJtYWMiOiI5MmQwNjQyNzVmMmFhYTFiZmM2ODM1N2Y4ZTY3ODlmMTRhYzM4YjVlMzgyMWExZmU1NWJiYTk3NzI5NjNlNzk5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkRZc0phVm1aenBhdlBwdlpRekYrNFE9PSIsInZhbHVlIjoiQnRqMXdQNmgyTUxwZWZHRnZVUnJHSElGTmlnWUVTNnB3US9KR0dqZVBYS29Jb29TeXpMR050NWg4WVNWcVdWeGZTV0VvNGVtRmlxeU1YQlpUVFIyRTA1TWtWSUdCdmdsNng1ekpVUDdrdTl2R1JJUUk1Uk1wYVJoOU5uOFNFbmo4bXR1UXRiZWpHbnpFM0lZN2wvOXVQdFdPR3ZCSkVqMkQxQyttaVpsbEVsYUxPd25EN1VsR21oSVg3S2VBQ1dXaFhFR2djaERQNzloTmQ2YXFhY1AxUVZYa29SRHNUZ3JOTFl1cnYraDRnVFVWYmVwbll5ZXhoVXJVV29rbmpYYmNDZFBPdk9vMXh0YkxlUWdLL1U5SGdMTm9HNmdSSFZtTllnQTRTVHp3U0lxVW9RZzBtSU1qdTYrYWJUYUxmLzI2OU43YnVmbkJYb2xteUxxNnhZcnFwdWxyMHJTeVp3dkljMk4zalo2TUtnYm9Ed2VpWGZUM3hWblJwdVR2dFpYdlFRZzNreTFQUG9FaHBwTzQ2c2s2eEsvemRPTlFtaFo0SGlaNVBveUU0dkI4Ly9XaDlmSGhnK3krTmxGdjNHTnR6Ny9BUEpOTFNyNGFTZHVVU3dSWUxFSmFBS1Z6NHJpT05iNTRyM1cxb0taKzZVcmFPSFJCQlk1NSthMFpiaWsiLCJtYWMiOiI3YTExZTM3YTkzOTUwOGE3ZjU0ODBmNmQ3MTAxNGMyZDUzYjk1ZDVhNDY3OTgxOTBhZDg0YjI2NDM4ZWU2ODdiIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1297609016 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2352</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1605;&#1580;&#1587;&#1605; &#1588;&#1582;&#1589;&#1610;&#1575;&#1578; &#1605;&#1582;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2352</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2353</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1605;&#1601;&#1578;&#1575;&#1581; &#1593;&#1604;&#1576;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2353</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1297609016\", {\"maxDepth\":0})</script>\n"}}