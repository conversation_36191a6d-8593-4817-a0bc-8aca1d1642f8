{"__meta": {"id": "X711f5c669a2ac8424dacb214a1eb78e7", "datetime": "2025-06-30 22:39:10", "utime": **********.018789, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.459823, "end": **********.018805, "duration": 0.5589821338653564, "duration_str": "559ms", "measures": [{"label": "Booting", "start": **********.459823, "relative_start": 0, "end": **********.915789, "relative_end": **********.915789, "duration": 0.4559659957885742, "duration_str": "456ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.915804, "relative_start": 0.4559810161590576, "end": **********.018806, "relative_end": 9.5367431640625e-07, "duration": 0.10300207138061523, "duration_str": "103ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45348960, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.029049999999999996, "accumulated_duration_str": "29.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.965062, "duration": 0.027239999999999997, "duration_str": "27.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.769}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.007023, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.769, "width_percent": 2.926}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.010706, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 96.695, "width_percent": 3.305}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-459897677 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-459897677\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-592368977 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-592368977\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1178722412 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1178722412\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; _clsk=eiqg7d%7C1751323143882%7C2%7C1%7Co.clarity.ms%2Fcollect; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlBQaFRCdlJQRW9yYkZycDBMSWY4QVE9PSIsInZhbHVlIjoiVWFIT2k2S0NDTmx1YVc3U0tvTUZQZmo4OC82S0dpOVlNSkdMQUJNMkd5a0dPTHYxbXBTcGgzZ3RJM3h2UkcvUE04eEFyTDZFYk13WEtTeTB3WmpaVDM2ZitPYjBCZ1pnZncweHhvcXdSTkZJY1V4dUVoTGpMUENqbmlCMHJqeVJqV1EvZ1gvbjhFVGNjeVRnRTU0bzY0MWl4V1VDMzd0N0NmZlZGbnhnK3IrV0JyMis3eTJiZDVzeXN5Rm9haE9VTGFCNEUvcmQzNlBISlcvYVRLcDJPWTkxUjlwUGxQQkVzNUR1elljTDFCQllvT2RTYlE2cjJUb3RjQThqOGlDeW52UVBxeW5sYXdDeXdQRDk3SkhHZDlTWUczSnBDWVI2cUw5YmloRytTRFpseE5RaGlpTDZldjNwa1dhbDZTaHE1eFJoaXpUU0lPOWtraTlmaEpXa09lQVNNODJjVG9oVDcwVEpST2o5K1JoY09lOHhyTWNIQmhWUGF1cVkrdGRUTlFISUlKN1BNRzIwN2l2RTVLclVYam5EeU8vaHNWYmE5T25YV2NKTkMwQkNRZE0zRktlWjd4ZWgwMUlXM2tkelFpS2MyK0JmTWxiQzFsWkozNlFLaU5qNXA2SW9xaWI2NlpXb1FITElsV0wvYWx0V1ErTlM1K1VkcG5KTmszRjAiLCJtYWMiOiIwZjJlNGFmNmFjNWUwNzM2MGMzZWY2ZTQ4YjE3ZGQzMTBiNzRiZWY4NmQ0ZmI5ZmJiZGQ4OTFhZmFhZGU4ODY3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii9HSCtyeFNsZ1hDZG05d1MvakRIVVE9PSIsInZhbHVlIjoiK1lENThhM2hVTSswTWFYSnQ4b1llSUlBSnhXZENaTDBqcHdFa1JCaWNRL0JiV3QxMXU0Q05ZYTJrVDcxdGlURmZqMXNoMWFSbWFqSUd0TjkraXFoN2xpRS9LQStHblpYN1QzaXhiN0xFM1lZMzN2QjZtRktxQURlNlUrTW9Uc3YxZkUvb0RuaG13SjBKQ0w4NGhFdVhQUTgwQXlYTDlMd2VGTlhnb0VuZzRzbERndVd2NUllUi9oRnZtVGx0L25pOTJ2MW5zSmZOVGd0QVhteFVnK01WRGJmbmZTZVB2Sm9RM216QURla0lhMzQvUmk0QUdRMHdnUy9SK3A3bWUvWVhGSlc2UjRJVUEwak9FSXhwOU5udnRieHlXL2pwWGw3c1V4MTRUV3NtNFJnOHh1VTBZbmgwMk9xZDRWMnA1NVZJTlhmUEk5KzNUUVJlWi80Nm54V3B0aDBEREwwVWt4bFdlNnNlQnhLS3hMVmhQRnNKaXlFTVVMMkZ0ZHRKYyt2eUUxTVhicVk1N3ZueTAzc2k4bzVNNnJLak5iNVMvTS9QRVp6dEl4ak92ZThKcE1lNkVlY3ZXc2h2R3dxTkxLVEVqTUlqaDRRL2p1bWdWYjZtcTBTb3k4TDNESHM1SVd4b2dRUUx0amJ4enN0enZ4OG1VTkRNb0dFd05GM2ZZalYiLCJtYWMiOiJjMzBlYmFkNDI4MjMwNDcyYTI1NTFmYTFkMTdlYmE4YmU2MDY5MTlmYzhiOWM3ZWM4MmQzMWIzY2JjMDljOTcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1742977989 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1742977989\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-445171516 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpQYWE4MzVvZVB1d21ibk1EankxbHc9PSIsInZhbHVlIjoieXA3clBCeEJKcDdDckp5emR6dUpXcmtqczIwZVJUR1hmQnJkL3BFUHowSGxLblNORXRKVE5Cc3grQTdieFl0bW9iSHAxWEVzbHp2Ri9uZkg4WDRaRnVvcVNSbGF1OFo5eXZYSldEbG1qOTFiYW51cFpsT05mUk1TOEFWNFpTVldtL0E0QVdiMHBiZzhHQVhrR2xSMWJTODdWbHJibWIyNEovZmZOa3diRDFMQWRuQkZOSjRjNFRvY0tkQVhhSFJNUXpxTzhFVVNha0FqVStTc29oQ2tzQ3pvRW1hRkFUbmR0NFk3RzFpQkFiQVRjSUE1T0syRHNGWmxkS1QxaDZ5OFdXdUVnMWVhZFR0dnNSTmY2a28ySmtqVVlVZEZwcEsyMWVxdnFoMzNVbmkwVVpEV1g2YmZRRVRVbFlDNGxBOUN1S20xMnc0SzBCTWRhYmF2emRYMWVFT3B4UDVnekJXUTNHNnhieS9SanVvSDNhYVE5K0I0dWdsOVgrVFlWOEhTWEEzZWpnTWxRKzdmZ3VPd2I3UzlvVzkwV0c2OS8xbzFGQXFzeEdsSy8yeEJzaXNzbFZicVZQYTVxQWVVeW42NkgrWFNmQkg2cUxwMmFxSDdmbGNoZlRydXp4bXd0VFAzeHhkVGxsYllBYzlZOXVjU0UrbzFZZnJ5bUNsdGdkRy8iLCJtYWMiOiI1YTlhYjRhY2FlMzM4M2U3NThiOTU4YzkxZmRlODJiNmE3MmY2MTA0Yjk1MmY3YTQwYjRiY2FlZTY5YTMzZDcxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im5nbWlHTDhsaHB6ZnJLVWZreENOMVE9PSIsInZhbHVlIjoibmJjYWV6cktidjBLN1pJWVhudWU1OVJkQVNUbEZTbDZTZ01haFFicVh4Wkt6R295Sm9ST0hjejdLWjZQVy9KNDdjRFA4bVJ2YmlpL01FclZ1S1d1R1E1V1VPUUlUdjNJV0lRcEc1YzcrNGJqSXlpdmhzTXJZejVYdEhVNHprY0k4NmUxZ2dpQjU0aTByeHBWOVYrWHZ3c1R5U1NhUzVxTlhmeEcrYzhQanpCNEE4QnRRbkdOOUFLbEs2cFJRaktIQjRmVmx4QWwvN3YwQ05jVk9yejVVaERYL3h2aVlmZmc1YU8xNjBiL2VDU0h4bjNVL2RPY0VUMnN1UlQySERQbG11S0NXR2lNR0d6cWpBRG84NzlRYnU3bXEvMlBQMm5pTnozNVJ0RWkzTnV4R01TT3dIVWxERkFOODRRQVhRNFpzMXNUNFJIdGhQK1c3cnN2Z0dFR2NqSzcvd0M5Tk54dHc0RG1vbmRDSFowS2hzVjJnYzU2WktGK0NVdThKM2tnbWd4Y0huYU5ENnBXYnFKdGw3S0JZR0ZqRlY4YWxzdTZkQTYrUXFiK1dhUFpiRmZNNFkzNDN0ckEvbjlzakh1L2dXUXF4UEM3L2NKbUUwZnhSVDI4SlBLM3c4Yk82blVqeHVERXVkV21oSENWOUVFYWtsb3U3UlZaVlJiVEo4RSsiLCJtYWMiOiI4ZmEwOThkZTBlODBlMWJhMTI2NTViMDNjMzBmZTY1NjU0MmJjM2RkZjM3N2JiZTI1YjFmYTY0MzFmNWMyODc3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpQYWE4MzVvZVB1d21ibk1EankxbHc9PSIsInZhbHVlIjoieXA3clBCeEJKcDdDckp5emR6dUpXcmtqczIwZVJUR1hmQnJkL3BFUHowSGxLblNORXRKVE5Cc3grQTdieFl0bW9iSHAxWEVzbHp2Ri9uZkg4WDRaRnVvcVNSbGF1OFo5eXZYSldEbG1qOTFiYW51cFpsT05mUk1TOEFWNFpTVldtL0E0QVdiMHBiZzhHQVhrR2xSMWJTODdWbHJibWIyNEovZmZOa3diRDFMQWRuQkZOSjRjNFRvY0tkQVhhSFJNUXpxTzhFVVNha0FqVStTc29oQ2tzQ3pvRW1hRkFUbmR0NFk3RzFpQkFiQVRjSUE1T0syRHNGWmxkS1QxaDZ5OFdXdUVnMWVhZFR0dnNSTmY2a28ySmtqVVlVZEZwcEsyMWVxdnFoMzNVbmkwVVpEV1g2YmZRRVRVbFlDNGxBOUN1S20xMnc0SzBCTWRhYmF2emRYMWVFT3B4UDVnekJXUTNHNnhieS9SanVvSDNhYVE5K0I0dWdsOVgrVFlWOEhTWEEzZWpnTWxRKzdmZ3VPd2I3UzlvVzkwV0c2OS8xbzFGQXFzeEdsSy8yeEJzaXNzbFZicVZQYTVxQWVVeW42NkgrWFNmQkg2cUxwMmFxSDdmbGNoZlRydXp4bXd0VFAzeHhkVGxsYllBYzlZOXVjU0UrbzFZZnJ5bUNsdGdkRy8iLCJtYWMiOiI1YTlhYjRhY2FlMzM4M2U3NThiOTU4YzkxZmRlODJiNmE3MmY2MTA0Yjk1MmY3YTQwYjRiY2FlZTY5YTMzZDcxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im5nbWlHTDhsaHB6ZnJLVWZreENOMVE9PSIsInZhbHVlIjoibmJjYWV6cktidjBLN1pJWVhudWU1OVJkQVNUbEZTbDZTZ01haFFicVh4Wkt6R295Sm9ST0hjejdLWjZQVy9KNDdjRFA4bVJ2YmlpL01FclZ1S1d1R1E1V1VPUUlUdjNJV0lRcEc1YzcrNGJqSXlpdmhzTXJZejVYdEhVNHprY0k4NmUxZ2dpQjU0aTByeHBWOVYrWHZ3c1R5U1NhUzVxTlhmeEcrYzhQanpCNEE4QnRRbkdOOUFLbEs2cFJRaktIQjRmVmx4QWwvN3YwQ05jVk9yejVVaERYL3h2aVlmZmc1YU8xNjBiL2VDU0h4bjNVL2RPY0VUMnN1UlQySERQbG11S0NXR2lNR0d6cWpBRG84NzlRYnU3bXEvMlBQMm5pTnozNVJ0RWkzTnV4R01TT3dIVWxERkFOODRRQVhRNFpzMXNUNFJIdGhQK1c3cnN2Z0dFR2NqSzcvd0M5Tk54dHc0RG1vbmRDSFowS2hzVjJnYzU2WktGK0NVdThKM2tnbWd4Y0huYU5ENnBXYnFKdGw3S0JZR0ZqRlY4YWxzdTZkQTYrUXFiK1dhUFpiRmZNNFkzNDN0ckEvbjlzakh1L2dXUXF4UEM3L2NKbUUwZnhSVDI4SlBLM3c4Yk82blVqeHVERXVkV21oSENWOUVFYWtsb3U3UlZaVlJiVEo4RSsiLCJtYWMiOiI4ZmEwOThkZTBlODBlMWJhMTI2NTViMDNjMzBmZTY1NjU0MmJjM2RkZjM3N2JiZTI1YjFmYTY0MzFmNWMyODc3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-445171516\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-558361878 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-558361878\", {\"maxDepth\":0})</script>\n"}}