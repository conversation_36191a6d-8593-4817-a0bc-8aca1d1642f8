{"__meta": {"id": "X2129c6d7cab9b36aeef34ed3dd6aff62", "datetime": "2025-06-30 23:10:27", "utime": **********.160219, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751325026.68446, "end": **********.160233, "duration": 0.4757730960845947, "duration_str": "476ms", "measures": [{"label": "Booting", "start": 1751325026.68446, "relative_start": 0, "end": **********.092338, "relative_end": **********.092338, "duration": 0.40787816047668457, "duration_str": "408ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.092347, "relative_start": 0.40788698196411133, "end": **********.160235, "relative_end": 1.9073486328125e-06, "duration": 0.06788802146911621, "duration_str": "67.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45348680, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.013090000000000001, "accumulated_duration_str": "13.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.12886, "duration": 0.01221, "duration_str": "12.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.277}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.15122, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.277, "width_percent": 4.125}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.154007, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 97.403, "width_percent": 2.597}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1041505817 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1041505817\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-324122146 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-324122146\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-928315393 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-928315393\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-373826255 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751325022992%7C13%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImFObGhTcG4raUNyYkxWejRUNm9RTHc9PSIsInZhbHVlIjoiUmQzR09ldGp3aXhaTlhMQTI1aDZZbGNzVVl0NkErSHJ4bFc1bG8rM2x4TW1RdEdpZkdlSEVMdzc3MXFCVVROUGIza1hDQTMwOW9OQW5GSjZLZzJXK1lnZFJTMnpSeUFuOXE0UU9aWTAyRjArNFdsay9wNmlNNlBUa1hhUUZralVYZUZ0OHoxU3JTNU1MUTZDRUlncloyT2E0TDNYaFQrdHY2SHlBaW03aE1mVUVieitvcWRsVXVwZ0V6cS9mZTBZSTN1Y1lqY09Ba1IrMFFGb25nUytyTkQrbGhidFV4ZFNHOFV1OVdUNzV4cEdzdmtveERmblk3blhXZjJDWG1Jb2hhMjdKeEJERkIvcUhyS3Y5bG8xbkFidlVrcXBvN1FPdkE1OG9SM2V3c0VoNVhTSk9nZlpoaGZRVGtDME55eXlMS2pvMXRGMTV3T0Zid2Ftdm54cDl6a3FHWUJKT2RvZWlycnpsZkNibkcvbUNNd1U3MktOQnJTMDEySk9NUFp0cGJ0RU54OEsrVVVoRTZyY2ZzdWViZEhtdUFNQnk4Ym5xYzNJcExXS1hRR0NRL3ZUb0ZrdytvdUZPc08zQmdIcHpidXIvS0h1dDlaWXVpRGlwZ29NMTRyYmhabWNnTXdqVTYxREM5Yit3aWFDWG9zbU9MY1RSU3UweVBsNURMVXEiLCJtYWMiOiJjYmY4YjE4NWExNDc1NDU5ZjQ0MTk0MjA1ZGU2M2JhN2M5NjhlYWQyN2Y3Y2U1MjJiNGEwYzVhNTNhMGVjMzQyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InJ5VW5td2phWUFHdURJMDdhbjJtcEE9PSIsInZhbHVlIjoiVit0OWtXeE1wMVJVQ1ZuZlJnejZQNjJvaStPSkwrdi9iN3ZiKytFWWI2K0FOdGc1cnhWTER4MmdWekJXTzRFcFkxaytobXFZbCtRcEZ6ckZ3VmhmU1oxQUJPV2gvUzFHb2hhM0lCZEJGSEtSMG5rWUVpQXlmMDZMbXhPK1VmdnZITDUxdUgwQUtwVzV1bVgreXEzbUxxeDI5dGRzUTBlODV1T20rZ2xBWmNzSmZqaS9ieDBwaTVpSUNpL3lHV1hpVnJINmY4V3psajdxTG4yTjdZRUlkUXlvRzVDclRtNEVzckdzMGVoTmh5TGpybGkrU21oeVAramVJY0R3L0RZWEYvZ3ZzY3F3RTdpSEZyQjFNRXpYdXlRWVZJMHcybWVjVUMxRmFsQ2xoc1Q5elZiK3A5b2Y4ZVIreDRXTkpVUkxiaWl5UWFnZ3hWME51eElDTk54YStvUkl5cEIyOXo4L25abmdVaVkzME96ZkJib0dqWUlEWU8rYy9ySzdwY2U4STRLZTFaekxSVVdERTVWcWVUenp4Q1d0dzFSOHBiSHpkT3BjZ3phZ3hGcE5iRnZUUjNnckg0ZlVLMTl1aWxldDF4eUp1ZWxkSHVlRjM4Wnd2SVJUQmNnVGsxVjVJL0FrREZ3U25jMTdJV1o2MTRsRktKL1ZsVG1pU3l5aXc1WkIiLCJtYWMiOiI1YzRjNDExNjY4NTZmZWEyYTRlN2QxZTA3YzA3OGZhNTJlNjYxZDk5YjgzODI1OWFjMTRkN2M1NDVhZjQ2OTlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-373826255\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1330806210 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1330806210\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-79499462 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:10:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1YaGpSangvd0d0N0tSNVZQOUVkS1E9PSIsInZhbHVlIjoiQjlsRW9DTldsTUFwK3pwUTZzdEFPNWNrYjRGaHZieFNHM2NzYk5OUDlNelRCVko1VHNoQWFWbGFBZW5RVG9xa1VtODlCMEVra2E4VEhibms0WUNMR3RvUjVIN0hSVDhwUkh0djNETXR1aVJGanV6eTZDaWRycmdURnZ6SUxwZ0RBazR4dzJWUXV5QVpUdUZEeVhDMktNWmEzcmhSZTNkQVdqTzZ1RElMOEJqdUI4S3YySEN4a1ZtdjF0S1lGMDRKdS96RncvdEtDZEpNaXlPZ2F5YlI3bUpVQUJDTDd6N3doNVN2d1E2dkI0SitWaFErSm4vRVozRk1OQ1YxNStsMHBvOHhrSlB1b0l5Vkpwbzc2cndCdmlxNGpNSThrSlN2UUQyMUVzeDFaQjU5alhQUitMRmpxK1ZmbDhMa3RXdGpGOXV4VWY0aENTQ3VaQk52emV4dG4rTTlSWnVpTDVlQjBRNUNiMW50VjNBMDFjU0xwQlFkaC9ZeXgrZnhxY0ZkSU5xSkJBWDRrNnEzeDFnd3QwcXpuY0xJZ1pKUVdVOUs5WjU5RCszWWdKR3Q5Y05hQmpiL2EvZFdpL0R5WUdkZjVVbWlCTFNxbll2cEVnYTJhNGRGY2tId2M4UlpnMGpYakxzQ0lXa00rY1NKbGIrSUYrM0VEZzhnNFRDTlVVNFAiLCJtYWMiOiJjMjc1Mjk0NWE0Yjg5YWUxMDdlOWJkNTFlZDVlNmU3ZjJlNWUzNzE2ZjdkYjAyMmIyOGJkYmEwNWIyZGM5NTIyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik0wd2ljTVRTQW1wTThKakZrRUdmNGc9PSIsInZhbHVlIjoiVlpKVFpOYnJCVVUrZUx4MnVsSDc1U0R4ZG1VU01YK2hjY2tkekpoRlR0bnJJNjVwUWlaVmxLMkVtVXYxaVhZaHhhRDBRa3FreGZoeXRGeDJ2a1VKMUhCbWpub2FWcnY2U241UmhRR2I4R2VYWFFWZDR1NzFEUThvK0F0WG1pMXEyYUhLd3pjay9xNkZVaVQxc2FuSFh2QWJVQ0F4c3Y2cUM2ZStVQzBKMThvTjlWU0g4UDJKTXJuZElVelI4K3dMcmJDV0FGZnJuL0JFYy9va1FyaDVkWVpxRkhUd29ySDZoMnAzemk4bGd1N2Q2amJOY0ZzOEJCcEhTQzh6VDVsNVlWV09XOTJScmZjQ3RlYyt2dDh5QUpIbGtveWo4NktyUmhJV3l5N2RqOGhwUEk3UDMvWFhDTFVib3hnY3lhb1pVNG01YzBCQWU3bmpZa0tvM2xuSWhIMjR2enJ5a1dvUklYNnhieXhxZHVhRkxnRmdJM05za0RTMlFtZmFNay84S0VpejlTOEYzSFZ3clpPSEhFNUgyRHU5eEl1bzZqZjN5bUZFd1lWVXp2N2NXam0zOHp6cjNxMGxMNzJsRXJDcGZkRmZPbExObGlQR2NJeVpaSlpyU2tPMXdwNkNxVDhHejZQaWJ1YzF0T1Z6OHNzUXNkU0JzQVZjb2cyK3k4NE0iLCJtYWMiOiIwMWE5NmVhZWI3NWQzMGYyMzA5NDBjMWZjMjU1MWI3OGM4MmQ0YmY3Yzk2ZjQ2NDE4MTc3YTVjZTBjZTBiNmNmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1YaGpSangvd0d0N0tSNVZQOUVkS1E9PSIsInZhbHVlIjoiQjlsRW9DTldsTUFwK3pwUTZzdEFPNWNrYjRGaHZieFNHM2NzYk5OUDlNelRCVko1VHNoQWFWbGFBZW5RVG9xa1VtODlCMEVra2E4VEhibms0WUNMR3RvUjVIN0hSVDhwUkh0djNETXR1aVJGanV6eTZDaWRycmdURnZ6SUxwZ0RBazR4dzJWUXV5QVpUdUZEeVhDMktNWmEzcmhSZTNkQVdqTzZ1RElMOEJqdUI4S3YySEN4a1ZtdjF0S1lGMDRKdS96RncvdEtDZEpNaXlPZ2F5YlI3bUpVQUJDTDd6N3doNVN2d1E2dkI0SitWaFErSm4vRVozRk1OQ1YxNStsMHBvOHhrSlB1b0l5Vkpwbzc2cndCdmlxNGpNSThrSlN2UUQyMUVzeDFaQjU5alhQUitMRmpxK1ZmbDhMa3RXdGpGOXV4VWY0aENTQ3VaQk52emV4dG4rTTlSWnVpTDVlQjBRNUNiMW50VjNBMDFjU0xwQlFkaC9ZeXgrZnhxY0ZkSU5xSkJBWDRrNnEzeDFnd3QwcXpuY0xJZ1pKUVdVOUs5WjU5RCszWWdKR3Q5Y05hQmpiL2EvZFdpL0R5WUdkZjVVbWlCTFNxbll2cEVnYTJhNGRGY2tId2M4UlpnMGpYakxzQ0lXa00rY1NKbGIrSUYrM0VEZzhnNFRDTlVVNFAiLCJtYWMiOiJjMjc1Mjk0NWE0Yjg5YWUxMDdlOWJkNTFlZDVlNmU3ZjJlNWUzNzE2ZjdkYjAyMmIyOGJkYmEwNWIyZGM5NTIyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik0wd2ljTVRTQW1wTThKakZrRUdmNGc9PSIsInZhbHVlIjoiVlpKVFpOYnJCVVUrZUx4MnVsSDc1U0R4ZG1VU01YK2hjY2tkekpoRlR0bnJJNjVwUWlaVmxLMkVtVXYxaVhZaHhhRDBRa3FreGZoeXRGeDJ2a1VKMUhCbWpub2FWcnY2U241UmhRR2I4R2VYWFFWZDR1NzFEUThvK0F0WG1pMXEyYUhLd3pjay9xNkZVaVQxc2FuSFh2QWJVQ0F4c3Y2cUM2ZStVQzBKMThvTjlWU0g4UDJKTXJuZElVelI4K3dMcmJDV0FGZnJuL0JFYy9va1FyaDVkWVpxRkhUd29ySDZoMnAzemk4bGd1N2Q2amJOY0ZzOEJCcEhTQzh6VDVsNVlWV09XOTJScmZjQ3RlYyt2dDh5QUpIbGtveWo4NktyUmhJV3l5N2RqOGhwUEk3UDMvWFhDTFVib3hnY3lhb1pVNG01YzBCQWU3bmpZa0tvM2xuSWhIMjR2enJ5a1dvUklYNnhieXhxZHVhRkxnRmdJM05za0RTMlFtZmFNay84S0VpejlTOEYzSFZ3clpPSEhFNUgyRHU5eEl1bzZqZjN5bUZFd1lWVXp2N2NXam0zOHp6cjNxMGxMNzJsRXJDcGZkRmZPbExObGlQR2NJeVpaSlpyU2tPMXdwNkNxVDhHejZQaWJ1YzF0T1Z6OHNzUXNkU0JzQVZjb2cyK3k4NE0iLCJtYWMiOiIwMWE5NmVhZWI3NWQzMGYyMzA5NDBjMWZjMjU1MWI3OGM4MmQ0YmY3Yzk2ZjQ2NDE4MTc3YTVjZTBjZTBiNmNmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-79499462\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-344549083 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-344549083\", {\"maxDepth\":0})</script>\n"}}