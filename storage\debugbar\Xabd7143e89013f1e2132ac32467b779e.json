{"__meta": {"id": "Xabd7143e89013f1e2132ac32467b779e", "datetime": "2025-06-30 22:39:35", "utime": **********.040882, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323174.570675, "end": **********.040899, "duration": 0.47022414207458496, "duration_str": "470ms", "measures": [{"label": "Booting", "start": 1751323174.570675, "relative_start": 0, "end": 1751323174.973243, "relative_end": 1751323174.973243, "duration": 0.40256810188293457, "duration_str": "403ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751323174.973256, "relative_start": 0.40258121490478516, "end": **********.040901, "relative_end": 1.9073486328125e-06, "duration": 0.06764483451843262, "duration_str": "67.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45047392, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0043300000000000005, "accumulated_duration_str": "4.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.001052, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 42.494}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.011706, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 42.494, "width_percent": 15.704}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.019217, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 58.199, "width_percent": 18.707}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.027077, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 76.905, "width_percent": 23.095}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-930492042 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-930492042\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-164999942 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-164999942\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-480521754 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-480521754\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751323171979%7C10%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InN3SFVwZ1plVUx0bit0MVA2S2JRdWc9PSIsInZhbHVlIjoiWEdhcGU5ejZSM0FHZzg0eEZCOGFkMzR5REtxOHFQazhmUWRob0FFUTByREhYQzl6cnNtQ2tpL0VFYlV4NW5UTTA0MFdEVmwxY1liOXVkM3hUZGtEKzllOEdTOENvcGFvZW5BVkJzMU9JR1FoSlBRNkljdWNuNlFtVkxDVmk5K1VqVFNpSWthSVJtd0JZTDhQSmg0eUJHOTFXN1hNLzI1MXMrc2cxKzNEUW1CcjZVREtFWlQ1d051K0ViQWwzZW5ib2FWa2M3ZjJINW5mTjZzeVZJdXVyYUNXY3o3MTZqQkM4V0NPYm80U0c4bDQ3Z3JlVmlCeDBacDIvTXhWVi9vTDhXYXVrWTMvVGdqWnRLN0JTVnp1R2J1Z0JzVEw3WDVHOEt4cHpoaXFMaG1uSjdJUUVmWGdYdGg0elRLYzg2ZWxOWW5IUTE0OWh0a3FsQXV0VUpkWG5hR1pVazY5bTE0VDM1eFZUT3ZXZ3c0TFZnbHJ2Ky9ETHJraS8xc1JteGp3aHFsWDcwTWZUaDBxaXNvL1FaMEJKUW9UT05PcjdneHFsc1V5ZkVmckplVHBLWnhLSjQ3N1hDeVB6aERtMGQzQlg3blpWcGl6bnB1MnBZWXY0dnh3UmViT3gxQjFNcnZTOGR5bi9PV0h0U08rL043LzdQem9vcUZTYU11YnNPbXoiLCJtYWMiOiI0MDQ0OGU1MzI1NjcxZjZjY2Y0N2RlMDFlY2IxZGE0Y2U5ZDZmY2FjYmU1YWEyZjMwMDhiNjM1ZDc3Nzc5Nzg3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InhuOXRVZlQ4MHVLYy9USUl2QlZQd0E9PSIsInZhbHVlIjoiNnZZaWJJMWwzSXBCRVd0SzRDZWZPMFVQcXQvMmxoeEdma3F6cFFoY2gxSzJSTXc0RjdZUGpFVWNtOHRkVVFnc0pEVm5XNStVQVVianVxeTRGcWZtY2tsaGJ2RUlsMFJnd0h2QUhXbldCUGJCc2ZIL0ZqWk1aUVJORUVzM2xEZzA4NVRmRStzb05nOXNCUjFNaEF0TTk0NlFJVnQ4QzFVTGJRL3pVVncvTkcwQ0JnVTFnQjFjNVZqdVBmemhmVWRmZHhQMXdIOWpYMFBpaHl6Vis2NThpM0pCRThZd3N4NytPUXNmSSs0aVRxTG5Jb3NmczkvenhXR0FPNXRKKzl6MWJyWnFVMjQwL2xFODMyRWY4K1UvbFZkZGlYdmhVdk9qNnB5MEJxQzRnRTZpOGhmNUg4RlJVTm1yWUkrbE1kcUN5cllKMTZOVFRmZW5ldTFkSEprUDIxRjJnYmVEblE3ZUdCK3RYRWlsWWc5ZUhlZEZjL2xPS1BWemRMT0svbG12Tmh2YW1nWTV3bFdHZGMvdnJXOVU1OVBZb0V6YVpOL1dKQTVCUk4zK0xRcE1EQ3p2SE5FOHd4ZXgxeWFtTXVhTTFxQkxBMm1uSWxxYWQ2ZnBwakhlQ2l6dStrdnJseU8rbTJsZFcxWit5VE1nSUFaaC9UczcvS2ZXSFlvSkthc3giLCJtYWMiOiI2ZGNiODJmNjdiYTNhY2IyMzk2YmQ1MTg3YjkwZTIyYmQxMjJkYWVmZTU5ZDE3ODE5NmJlMjdiMDIwNjQ3ZjhkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1244713445 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">K7EIYCLnalanTBUASCKMEOK1yUmooVr7ha2cCSMP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1244713445\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1407179247 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRUWHhJREhCTGZnVitQTnNZVkFjS2c9PSIsInZhbHVlIjoid25yVHl2UzJjYlJreHdlNytIRWVBZ3RJNmVacmUxZURvS3IveFZWK0JML0VnaDZaVmFqRXdOZXUwQTRKUU55em9ONlJ5ayt4N1g1VGs3U0xpZG1SZlBhMi85U1JTMExyZCtXeHJjeDJDWkludklGQ3VrbTg3WWZPWitTR0dTUTFaVmpXWHBsV25LODBkK3JKUk5mOUJ5YUxiS3kyN25XdzlqaWt2ZDN4eEpIKzlUYmMveUF3b3pjZ3NLSnJ0U1IxeDNWanJkY280b2VIMCtxNXp6UFptMm92anYrbWo5U29JRUhGejlmKzJUanYzZXpyOFZ6cGhUcWg5N3ZhSmdwUHJ6Q2JxVkxJZkJsbjB6QnVrWDJod2JhS1cxVGZYcDhrZVdLYzdBVmtUVW13NTlGSzFxbzV3MGdzS24wa1lscVYraUVxWGFyTnVxcjc0Z0ErWERORVo5KzhrMmJFcmRHRDF1WGxsZUs4TTNMdktVRWx1NlpxMEhLekp5a1E5NWo2UWtrWlMrQUN4bnN2YlhoOXhNb3BUNmhNNzdUWFJHcjdIU0k2di9BSDllRHFNdFR2Q250aTlWdWxPMTBXd2F3UURwRUgveTFlVC9rQzhtQjEwY0txTGM2NTdzSGNxV3hlbFN4ZnhYM2psT3JEN0JXWDVjTkdlVmovWXkybE45YXMiLCJtYWMiOiI2ODgyYTUyZGY3ZWM5YTI0ZTA2Mzk5ZTg5M2YwZWVlZWY0ZTNkYTY2OGQ0YTY4ZmEzMDg5OTU3NTY4OGEzNWY4IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlRkL0NvK0x2YnRvNGdleit6ejlqRnc9PSIsInZhbHVlIjoiYWZzUzlwTUJ2U3l3TktvcUx5VWZrUmM4K1R3U0dlQUJYMktRbXUxQXIwK2dxL3JBY1hkaUJ0Z3JzWXhYM0hDSkw1QTZWVUllWldvQnFvNU5CM0tCdWRhOU5OcmdzaHhCWWo2Y1NINHJRUURrTWVONndEa0FLeHpqMmpIOVJWekJFeXhGa2xvVnpCeUZHV2hkOUd4Nkp4TENVL3V6VGtYU0FwalZXaFFUMDBkTldxY0ZzSUwxYkVKZy9RUmxVeHovbXEwanlZUFovdFhtYkZ3MkE1S0lDcmJiMnR2UUlrd0g5VGZOVGprSUdqd3FTQkNEOCswU3gwQUdNdnVsRzQ3ZlI3SS92R09EZG5ZYkJJVDgxdjR0akFDdS9OTVpMNTA4anFRdGRLZVJnUysxQUZLS2hSak1lQjhSZndqWXE5QmdBMUFiLzN5UUZRa3RSaGgwTTZ4eGI4MlNIZG1rVkIvbTRoWHhzM2RsYU5wMkt1MFgrTzFPZisvNHd1SG1mSEtHdFY0aitobjE1QjVpckhObE5GM1pRd044Q1FXWENWSW05MG1vc3Y4Y3dsZU0wN3diQy9DS2ZWL1V6dGZyTnRsR09ZZFRCQnM0R3FxSlhiMEtDTVliZ3lNVnJRdy9VTGp0ZFllN0RhQkZUNk9SdC9BK3pndENMc29KSWpkdktYUDkiLCJtYWMiOiIyNzE1Y2M3MzhmZWRkYzRmYjhiZTVlMmIyZGRlMjZmM2UzODgxOWU5MmNmZTBhY2EyMDliODVjNzIzZmJmNWM0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRUWHhJREhCTGZnVitQTnNZVkFjS2c9PSIsInZhbHVlIjoid25yVHl2UzJjYlJreHdlNytIRWVBZ3RJNmVacmUxZURvS3IveFZWK0JML0VnaDZaVmFqRXdOZXUwQTRKUU55em9ONlJ5ayt4N1g1VGs3U0xpZG1SZlBhMi85U1JTMExyZCtXeHJjeDJDWkludklGQ3VrbTg3WWZPWitTR0dTUTFaVmpXWHBsV25LODBkK3JKUk5mOUJ5YUxiS3kyN25XdzlqaWt2ZDN4eEpIKzlUYmMveUF3b3pjZ3NLSnJ0U1IxeDNWanJkY280b2VIMCtxNXp6UFptMm92anYrbWo5U29JRUhGejlmKzJUanYzZXpyOFZ6cGhUcWg5N3ZhSmdwUHJ6Q2JxVkxJZkJsbjB6QnVrWDJod2JhS1cxVGZYcDhrZVdLYzdBVmtUVW13NTlGSzFxbzV3MGdzS24wa1lscVYraUVxWGFyTnVxcjc0Z0ErWERORVo5KzhrMmJFcmRHRDF1WGxsZUs4TTNMdktVRWx1NlpxMEhLekp5a1E5NWo2UWtrWlMrQUN4bnN2YlhoOXhNb3BUNmhNNzdUWFJHcjdIU0k2di9BSDllRHFNdFR2Q250aTlWdWxPMTBXd2F3UURwRUgveTFlVC9rQzhtQjEwY0txTGM2NTdzSGNxV3hlbFN4ZnhYM2psT3JEN0JXWDVjTkdlVmovWXkybE45YXMiLCJtYWMiOiI2ODgyYTUyZGY3ZWM5YTI0ZTA2Mzk5ZTg5M2YwZWVlZWY0ZTNkYTY2OGQ0YTY4ZmEzMDg5OTU3NTY4OGEzNWY4IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlRkL0NvK0x2YnRvNGdleit6ejlqRnc9PSIsInZhbHVlIjoiYWZzUzlwTUJ2U3l3TktvcUx5VWZrUmM4K1R3U0dlQUJYMktRbXUxQXIwK2dxL3JBY1hkaUJ0Z3JzWXhYM0hDSkw1QTZWVUllWldvQnFvNU5CM0tCdWRhOU5OcmdzaHhCWWo2Y1NINHJRUURrTWVONndEa0FLeHpqMmpIOVJWekJFeXhGa2xvVnpCeUZHV2hkOUd4Nkp4TENVL3V6VGtYU0FwalZXaFFUMDBkTldxY0ZzSUwxYkVKZy9RUmxVeHovbXEwanlZUFovdFhtYkZ3MkE1S0lDcmJiMnR2UUlrd0g5VGZOVGprSUdqd3FTQkNEOCswU3gwQUdNdnVsRzQ3ZlI3SS92R09EZG5ZYkJJVDgxdjR0akFDdS9OTVpMNTA4anFRdGRLZVJnUysxQUZLS2hSak1lQjhSZndqWXE5QmdBMUFiLzN5UUZRa3RSaGgwTTZ4eGI4MlNIZG1rVkIvbTRoWHhzM2RsYU5wMkt1MFgrTzFPZisvNHd1SG1mSEtHdFY0aitobjE1QjVpckhObE5GM1pRd044Q1FXWENWSW05MG1vc3Y4Y3dsZU0wN3diQy9DS2ZWL1V6dGZyTnRsR09ZZFRCQnM0R3FxSlhiMEtDTVliZ3lNVnJRdy9VTGp0ZFllN0RhQkZUNk9SdC9BK3pndENMc29KSWpkdktYUDkiLCJtYWMiOiIyNzE1Y2M3MzhmZWRkYzRmYjhiZTVlMmIyZGRlMjZmM2UzODgxOWU5MmNmZTBhY2EyMDliODVjNzIzZmJmNWM0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1407179247\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2034871861 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2034871861\", {\"maxDepth\":0})</script>\n"}}