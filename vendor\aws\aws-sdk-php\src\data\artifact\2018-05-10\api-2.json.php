<?php
// This file was auto-generated from sdk-root/src/data/artifact/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'artifact', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS Artifact', 'serviceId' => 'Artifact', 'signatureVersion' => 'v4', 'signingName' => 'artifact', 'uid' => 'artifact-2018-05-10', ], 'operations' => [ 'GetAccountSettings' => [ 'name' => 'GetAccountSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/account-settings/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAccountSettingsRequest', ], 'output' => [ 'shape' => 'GetAccountSettingsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'GetReport' => [ 'name' => 'GetReport', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/report/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetReportRequest', ], 'output' => [ 'shape' => 'GetReportResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'GetReportMetadata' => [ 'name' => 'GetReportMetadata', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/report/getMetadata', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetReportMetadataRequest', ], 'output' => [ 'shape' => 'GetReportMetadataResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'GetTermForReport' => [ 'name' => 'GetTermForReport', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/report/getTermForReport', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTermForReportRequest', ], 'output' => [ 'shape' => 'GetTermForReportResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'ListReports' => [ 'name' => 'ListReports', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/report/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListReportsRequest', ], 'output' => [ 'shape' => 'ListReportsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'PutAccountSettings' => [ 'name' => 'PutAccountSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/account-settings/put', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutAccountSettingsRequest', ], 'output' => [ 'shape' => 'PutAccountSettingsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AcceptanceType' => [ 'type' => 'string', 'enum' => [ 'PASSTHROUGH', 'EXPLICIT', ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccountSettings' => [ 'type' => 'structure', 'members' => [ 'notificationSubscriptionStatus' => [ 'shape' => 'NotificationSubscriptionStatus', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'GetAccountSettingsRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetAccountSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'accountSettings' => [ 'shape' => 'AccountSettings', ], ], ], 'GetReportMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'reportId', ], 'members' => [ 'reportId' => [ 'shape' => 'ReportId', 'location' => 'querystring', 'locationName' => 'reportId', ], 'reportVersion' => [ 'shape' => 'VersionAttribute', 'location' => 'querystring', 'locationName' => 'reportVersion', ], ], ], 'GetReportMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'reportDetails' => [ 'shape' => 'ReportDetail', ], ], ], 'GetReportRequest' => [ 'type' => 'structure', 'required' => [ 'reportId', 'termToken', ], 'members' => [ 'reportId' => [ 'shape' => 'ReportId', 'location' => 'querystring', 'locationName' => 'reportId', ], 'reportVersion' => [ 'shape' => 'VersionAttribute', 'location' => 'querystring', 'locationName' => 'reportVersion', ], 'termToken' => [ 'shape' => 'ShortStringAttribute', 'location' => 'querystring', 'locationName' => 'termToken', ], ], ], 'GetReportResponse' => [ 'type' => 'structure', 'members' => [ 'documentPresignedUrl' => [ 'shape' => 'GetReportResponseDocumentPresignedUrlString', ], ], ], 'GetReportResponseDocumentPresignedUrlString' => [ 'type' => 'string', 'max' => 10240, 'min' => 1, ], 'GetTermForReportRequest' => [ 'type' => 'structure', 'required' => [ 'reportId', ], 'members' => [ 'reportId' => [ 'shape' => 'ReportId', 'location' => 'querystring', 'locationName' => 'reportId', ], 'reportVersion' => [ 'shape' => 'VersionAttribute', 'location' => 'querystring', 'locationName' => 'reportVersion', ], ], ], 'GetTermForReportResponse' => [ 'type' => 'structure', 'members' => [ 'documentPresignedUrl' => [ 'shape' => 'GetTermForReportResponseDocumentPresignedUrlString', ], 'termToken' => [ 'shape' => 'String', ], ], ], 'GetTermForReportResponseDocumentPresignedUrlString' => [ 'type' => 'string', 'max' => 10240, 'min' => 1, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'ListReportsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResultsAttribute', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextTokenAttribute', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListReportsResponse' => [ 'type' => 'structure', 'members' => [ 'reports' => [ 'shape' => 'ReportsList', ], 'nextToken' => [ 'shape' => 'NextTokenAttribute', ], ], ], 'LongStringAttribute' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[^<>]*', ], 'MaxResultsAttribute' => [ 'type' => 'integer', 'box' => true, 'max' => 300, 'min' => 1, ], 'NextTokenAttribute' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'NotificationSubscriptionStatus' => [ 'type' => 'string', 'enum' => [ 'SUBSCRIBED', 'NOT_SUBSCRIBED', ], ], 'PublishedState' => [ 'type' => 'string', 'enum' => [ 'PUBLISHED', 'UNPUBLISHED', ], ], 'PutAccountSettingsRequest' => [ 'type' => 'structure', 'members' => [ 'notificationSubscriptionStatus' => [ 'shape' => 'NotificationSubscriptionStatus', ], ], ], 'PutAccountSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'accountSettings' => [ 'shape' => 'AccountSettings', ], ], ], 'ReportDetail' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ReportId', ], 'name' => [ 'shape' => 'ShortStringAttribute', ], 'description' => [ 'shape' => 'LongStringAttribute', ], 'periodStart' => [ 'shape' => 'TimestampAttribute', ], 'periodEnd' => [ 'shape' => 'TimestampAttribute', ], 'createdAt' => [ 'shape' => 'TimestampAttribute', ], 'lastModifiedAt' => [ 'shape' => 'TimestampAttribute', ], 'deletedAt' => [ 'shape' => 'TimestampAttribute', ], 'state' => [ 'shape' => 'PublishedState', ], 'arn' => [ 'shape' => 'LongStringAttribute', ], 'series' => [ 'shape' => 'ShortStringAttribute', ], 'category' => [ 'shape' => 'ShortStringAttribute', ], 'companyName' => [ 'shape' => 'ShortStringAttribute', ], 'productName' => [ 'shape' => 'ShortStringAttribute', ], 'termArn' => [ 'shape' => 'LongStringAttribute', ], 'version' => [ 'shape' => 'VersionAttribute', ], 'acceptanceType' => [ 'shape' => 'AcceptanceType', ], 'sequenceNumber' => [ 'shape' => 'SequenceNumberAttribute', ], 'uploadState' => [ 'shape' => 'UploadState', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], ], ], 'ReportId' => [ 'type' => 'string', 'pattern' => 'report-[a-zA-Z0-9]{16}', ], 'ReportSummary' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ReportId', ], 'name' => [ 'shape' => 'ShortStringAttribute', ], 'state' => [ 'shape' => 'PublishedState', ], 'arn' => [ 'shape' => 'LongStringAttribute', ], 'version' => [ 'shape' => 'VersionAttribute', ], 'uploadState' => [ 'shape' => 'UploadState', ], 'description' => [ 'shape' => 'LongStringAttribute', ], 'periodStart' => [ 'shape' => 'TimestampAttribute', ], 'periodEnd' => [ 'shape' => 'TimestampAttribute', ], 'series' => [ 'shape' => 'ShortStringAttribute', ], 'category' => [ 'shape' => 'ShortStringAttribute', ], 'companyName' => [ 'shape' => 'ShortStringAttribute', ], 'productName' => [ 'shape' => 'ShortStringAttribute', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'acceptanceType' => [ 'shape' => 'AcceptanceType', ], ], ], 'ReportsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReportSummary', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'SequenceNumberAttribute' => [ 'type' => 'long', 'box' => true, 'min' => 1, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', 'serviceCode', 'quotaCode', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'ShortStringAttribute' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9_\\-\\s]*', ], 'StatusMessage' => [ 'type' => 'string', ], 'String' => [ 'type' => 'string', ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'TimestampAttribute' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'UploadState' => [ 'type' => 'string', 'enum' => [ 'PROCESSING', 'COMPLETE', 'FAILED', 'FAULT', ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'unknownOperation', 'cannotParse', 'fieldValidationFailed', 'invalidToken', 'other', ], ], 'VersionAttribute' => [ 'type' => 'long', 'box' => true, 'min' => 1, ], ],];
