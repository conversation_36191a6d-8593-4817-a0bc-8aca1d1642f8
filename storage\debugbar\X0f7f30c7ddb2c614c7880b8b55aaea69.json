{"__meta": {"id": "X0f7f30c7ddb2c614c7880b8b55aaea69", "datetime": "2025-06-30 23:13:24", "utime": **********.243488, "method": "GET", "uri": "/login-with-company/exit", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751325203.832376, "end": **********.243511, "duration": 0.4111349582672119, "duration_str": "411ms", "measures": [{"label": "Booting", "start": 1751325203.832376, "relative_start": 0, "end": **********.181821, "relative_end": **********.181821, "duration": 0.349445104598999, "duration_str": "349ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.181831, "relative_start": 0.3494548797607422, "end": **********.243512, "relative_end": 9.5367431640625e-07, "duration": 0.06168103218078613, "duration_str": "61.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43760024, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login-with-company/exit", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@ExitCompany", "namespace": null, "prefix": "", "where": [], "as": "exit.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FUserController.php&line=669\" onclick=\"\">app/Http/Controllers/UserController.php:669-673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01544, "accumulated_duration_str": "15.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2098489, "duration": 0.01514, "duration_str": "15.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 98.057}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Services/ImpersonateManager.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\lab404\\laravel-impersonate\\src\\Services\\ImpersonateManager.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Services/ImpersonateManager.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\lab404\\laravel-impersonate\\src\\Services\\ImpersonateManager.php", "line": 137}, {"index": 19, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Models/Impersonate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\lab404\\laravel-impersonate\\src\\Models\\Impersonate.php", "line": 64}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\UserController.php", "line": 671}], "start": **********.228485, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 98.057, "width_percent": 1.943}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login-with-company/exit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login-with-company/exit", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1541046896 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1541046896\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1717838014 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1717838014\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325166735%7C25%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktQL2xQRGt4SVRwdktucHc3d2YxZHc9PSIsInZhbHVlIjoidFJEdHd5R0NiZk9teHVxZ1orR0FRWDd2UEZ1U1pQOXJNUStHRU0xQklhV1JsbnI1V2hNMFlBaURhRXZtaStIWlE3eXpqRTFVNUl0Y21ZcmErNlhEMVM5ZlVhTEs0ejA4aS9UeWYwZUw0TVZZUGFkM01STHFxWWN2dWczckcxbGVzdFJvMkRNeEpCT09LRkJnK1pqVVFkWXpoY0RZREJoc1R3ZVkrb1ZLK2tIY3MyQVMrUjg2U3FPVzBHcnlBUnVlb3I3U2xnTUF6d0t0VW4wSDdGQTIyZU5saTVLbTFMOTNzZTdQRVdVV3V3S1VicU55dVJMRGx6clFJM08xb1JHOEp0S2d4MEFHYnB2ZHVHSXk4OEt1VmU0QUlpYkJrc1FqNTJ3NW1mOXZYKzk0QzhEck51ZUdmOW5lekRHQllxakhMUGRuOHg1VC94NmE4YnB5S3A2QWhlVkQxRzVoTVVMcndQM202VG9mak9RblNLTE0yZExsUHpkcTJGRUVhWWxYeWRXQTJISVU1UTV2cFFjUTJnUDlTSDJyU05vc3BkVVgvTXR4MTJaL05CeGx2TVIweUJiVjh6Y1puMkRYemxCZkh2NFdKa0NPZHlwQmRSTnJBL0tFc3BsVHJSNXJvMEcvdTB6VGQ2TUU0SG9HaDJnMzNVMW5tTzMyMTFSNXFmNS8iLCJtYWMiOiJkYzZmMTQxNTEyYjE1ZTNhYjcwZGUzOWM2MjNiMTExNWFjMGM1NzliODVmY2RkOTAxZjg4MDhlYWE3ZmQ2NTRmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlN3bXRxK20vMTh1d0lKT2hmemhNamc9PSIsInZhbHVlIjoiaWVYeTFzVXdUd1poZ09qKzdZS2xnVEFmaExvZU1rd21UYldtYWZ3U09iUDhUc3FrMit3MGZWZGVVSFNSZzVSWTdGaFNJSmhiWkxzY0JPK29UQkI5UHVKZlF0RWVETy81eFovZkFVRGVsRVFqRTdodUNGcEVIMkhRbFR0QThma0FvZUVNVXlYam1jeFBxaWlZamdaeWxSdjZzSHVTa0ZNd0ljUXN2WmdqeTcwLzE5NExwWnNaNlNDbEE2Sm0yVVZIZ0NsZDZKblRmaDdUK3NqOEpvOFJSaUVkblV5YVJRbVBTSzI2S2hDYThKcmpSOG9vMVVJanM5YzdKelZNeW9STFlYNHV4UzR5eThVZUQzbmtWdzRiZmdaM1RRTDZSUmltS0NRNDVzb3dTaGx4V08zay9UZDJBZDAyanlja3BzeERjejQvOGNjQS9wQTFpQUE0Skh1dXR0MFVqR1lCUDFyS0sxWGMwRDNhamFJamNTWHdlZ25LU0UrSERJQ2hqY3loVzVWcUcwWHZ2UTJwTmQwSU5Cb3M0VWJxNlh6ZmJUZzVCNVZmRCtKTXMwSUxOUmtFRFpvS2o1a3pLYzZ1alVVcHJoZkNETTd2WUFUemFaK0ZCM1NadDBSQmkzeFVHczM4QWdTTWE2dlZSZHQ4citYVVBYQyt1WHpGQW4xTjhIbzAiLCJtYWMiOiI4NjE4ZGY0M2VmOTg3ZjNkNTY5Zjk1MmI1MDZlOTVmYjUwYzI1NzBiNjRlNDBlMTUwMDI3NmY5Y2Q3OTAwOTg2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1053039476 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0M1K0sdNadPD2LVrnt8LmBw227nA9F1U5e3ROaJK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1053039476\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:13:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNzR2pxZUs4b0NNcnlLajNDeUlKb3c9PSIsInZhbHVlIjoiREJjcmtVQVZZRVBmTEdEenVxTjlXeFZOWk9lTnJFSzA3aTYyc3dhL0dvamtiazFXSWhpb29MZXdrelBSOStsbWgzcmRQQUtlVXFxYitzNFpVYUhsS2pZbDVNbEl3eWgrdElucWd6clIwTU9ZS1NuVERKcE81Nkx1VVVYbUVvdXJLMzg1RzRFNDB0RGdmZGZzS2RUT1E1Tnp4NDNNalUyTFN2VTR6REt1WGR3VHU3ZWgrdTF3TGRRbGZMVkxZZWQ4bFAvTVlUaTZ1OThaK1l1OXhEUUJLMmZUVHpIZkNHQll3Z2dYRUV6VGthMGpCMXhqQ1hNb3E4ekZHeElhTGNxWVJHbGxjNzVOVGU1blZUR1E3WXhnTG1tMmxTRGkrVmpvNXlBUmswczNDNU1hL0EvMDd4bW9HNjBPWW52aTdJSUd1VWdITHFXLzljZ2FlbldIZWNHaGZqUnpKL0gwMXBEUG81Qm1oMVFJK3p6R2ZRZjBPeW5KYkJOblRKSE12cHpuVkY5U1QrOEtKb0pCR3Z5MDYwU2N1OWdEZTJOOFQyUml0YzVWTVYwbTg2RFh2ZWJwZG5aRWR1RERYTUxLRnNydTdVdWtWNzkyWmpGa09wNEc4U0MrWktHVUFJSWtrOUd3S0VrazA0SGNDcGtnUTBQK0lNWXcvNzE3Z05neUZXK3QiLCJtYWMiOiJmMzNjZjllZDk3NWIwZTk2ZTE0ZjljMGUwZWIxOTc0Yjc5YWU4NDkwOWY4MjkwODI3NTU0ZTRiNjMwZTAyMGUxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik1tUytQY0ZhT3hkcGpCRzNYdk5DcFE9PSIsInZhbHVlIjoiMzRrUjV3R3FaUHR6ZVRqbEtWVlkxbE1sTnF3eVpkYW8zMytUU3kxeGFKNzlBQTZEcklzaDYranZXYWU5aGswMXBxeFNRaUZ5T3pzYmxCcWpqQUpCeHBCVTU4WUdqU0VIQUI5VGlhM3FlMXFMNE9JcmkwWU5ld3VVNlRidGV4VDhSM1F6RCtTbitkelp2UThabk54RW5KblVqR210d3psYm8zOUFteGFKTzhURG55bGZmZlR3dnJTdi9JVTVnZGRLY2NycDl5V0xXb3Njb0FrTE81ZE96WmNuZDJ5UFl0M1NzMm9TczJBdXFoRTVKZ2t0azQxalYyY0FGVGtRZ1R5aWlackxBT0VaMHNGR0tvZmpuWHF2RkcvNHAvUHNuZFVGTmFYbmZmdjBTOHV6UVRBeEo2aW1nR0RkclBPOUNiVUV0ZU9nV3dzZTJMT2pPdS9CWCt5dEdyVDhUakFuTzg1Q3JvTXo3SWMzTS9GMkUwRVdkcEs0a0diUFpqcFA4aFkrZHdCRnA0SlhyTWp0ZGwyTkQ0MnlES25TUGxucGZUckFzMTBwS1RoSW5STTRFa0xCUlYzdG9tMEdTSE5Pa3ZDQkFUdDJDOWxESVBrZEhkU2FsRW9sL0p2bkVhRURrRVNlNG54azNqVGJ5Ylo4eVVlaXZidkRzaXJRSStwNXpWdmUiLCJtYWMiOiJlMDliZWY5ZDE5MGFhNGFmNTkxMmY2NTY0MmNlNWEzNDUzYThhMDRlMjNlN2FjN2QxNWFkYzljN2IzMGI2MTY4IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNzR2pxZUs4b0NNcnlLajNDeUlKb3c9PSIsInZhbHVlIjoiREJjcmtVQVZZRVBmTEdEenVxTjlXeFZOWk9lTnJFSzA3aTYyc3dhL0dvamtiazFXSWhpb29MZXdrelBSOStsbWgzcmRQQUtlVXFxYitzNFpVYUhsS2pZbDVNbEl3eWgrdElucWd6clIwTU9ZS1NuVERKcE81Nkx1VVVYbUVvdXJLMzg1RzRFNDB0RGdmZGZzS2RUT1E1Tnp4NDNNalUyTFN2VTR6REt1WGR3VHU3ZWgrdTF3TGRRbGZMVkxZZWQ4bFAvTVlUaTZ1OThaK1l1OXhEUUJLMmZUVHpIZkNHQll3Z2dYRUV6VGthMGpCMXhqQ1hNb3E4ekZHeElhTGNxWVJHbGxjNzVOVGU1blZUR1E3WXhnTG1tMmxTRGkrVmpvNXlBUmswczNDNU1hL0EvMDd4bW9HNjBPWW52aTdJSUd1VWdITHFXLzljZ2FlbldIZWNHaGZqUnpKL0gwMXBEUG81Qm1oMVFJK3p6R2ZRZjBPeW5KYkJOblRKSE12cHpuVkY5U1QrOEtKb0pCR3Z5MDYwU2N1OWdEZTJOOFQyUml0YzVWTVYwbTg2RFh2ZWJwZG5aRWR1RERYTUxLRnNydTdVdWtWNzkyWmpGa09wNEc4U0MrWktHVUFJSWtrOUd3S0VrazA0SGNDcGtnUTBQK0lNWXcvNzE3Z05neUZXK3QiLCJtYWMiOiJmMzNjZjllZDk3NWIwZTk2ZTE0ZjljMGUwZWIxOTc0Yjc5YWU4NDkwOWY4MjkwODI3NTU0ZTRiNjMwZTAyMGUxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik1tUytQY0ZhT3hkcGpCRzNYdk5DcFE9PSIsInZhbHVlIjoiMzRrUjV3R3FaUHR6ZVRqbEtWVlkxbE1sTnF3eVpkYW8zMytUU3kxeGFKNzlBQTZEcklzaDYranZXYWU5aGswMXBxeFNRaUZ5T3pzYmxCcWpqQUpCeHBCVTU4WUdqU0VIQUI5VGlhM3FlMXFMNE9JcmkwWU5ld3VVNlRidGV4VDhSM1F6RCtTbitkelp2UThabk54RW5KblVqR210d3psYm8zOUFteGFKTzhURG55bGZmZlR3dnJTdi9JVTVnZGRLY2NycDl5V0xXb3Njb0FrTE81ZE96WmNuZDJ5UFl0M1NzMm9TczJBdXFoRTVKZ2t0azQxalYyY0FGVGtRZ1R5aWlackxBT0VaMHNGR0tvZmpuWHF2RkcvNHAvUHNuZFVGTmFYbmZmdjBTOHV6UVRBeEo2aW1nR0RkclBPOUNiVUV0ZU9nV3dzZTJMT2pPdS9CWCt5dEdyVDhUakFuTzg1Q3JvTXo3SWMzTS9GMkUwRVdkcEs0a0diUFpqcFA4aFkrZHdCRnA0SlhyTWp0ZGwyTkQ0MnlES25TUGxucGZUckFzMTBwS1RoSW5STTRFa0xCUlYzdG9tMEdTSE5Pa3ZDQkFUdDJDOWxESVBrZEhkU2FsRW9sL0p2bkVhRURrRVNlNG54azNqVGJ5Ylo4eVVlaXZidkRzaXJRSStwNXpWdmUiLCJtYWMiOiJlMDliZWY5ZDE5MGFhNGFmNTkxMmY2NTY0MmNlNWEzNDUzYThhMDRlMjNlN2FjN2QxNWFkYzljN2IzMGI2MTY4IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost/login-with-company/exit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}