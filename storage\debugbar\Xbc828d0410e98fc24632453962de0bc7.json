{"__meta": {"id": "Xbc828d0410e98fc24632453962de0bc7", "datetime": "2025-06-30 22:39:55", "utime": **********.376154, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323194.873807, "end": **********.376172, "duration": 0.5023651123046875, "duration_str": "502ms", "measures": [{"label": "Booting", "start": 1751323194.873807, "relative_start": 0, "end": **********.309653, "relative_end": **********.309653, "duration": 0.43584609031677246, "duration_str": "436ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.309663, "relative_start": 0.4358561038970947, "end": **********.376175, "relative_end": 2.86102294921875e-06, "duration": 0.06651186943054199, "duration_str": "66.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45028808, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01482, "accumulated_duration_str": "14.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.337891, "duration": 0.01401, "duration_str": "14.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.534}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3609939, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.534, "width_percent": 2.632}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3673189, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.166, "width_percent": 2.834}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management?end_date=&start_date=&status=all&warehouse_id=8\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-893041137 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-893041137\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-331360487 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-331360487\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1095044720 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1095044720\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-651875395 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"87 characters\">http://localhost/branch-cash-management?status=all&amp;warehouse_id=8&amp;start_date=&amp;end_date=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751323184510%7C13%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJReGJ2a3FacFlhZjdFeDlXQW9CS1E9PSIsInZhbHVlIjoiV2xsUXNUSHZ3Sml6R3Z6bVRsdU9NQjg2cGEvTWVrTk1xbUxyV05jWGpjVVJlVEowcGwvTVE5cVdkN0cxQk1OOGhRNXRjalpGMEVNTkQ5YTd2Wlkrdi9wRmVDU0QxNTNLVmlmemJ5aW94NUR2R2NINXBJMnAvOCtWUDhWWmRBYXd4KzhTd2d4U3o3c3lrbmhvbW5jZ3pyeWh6a2tMTG5FYXZudW5aWWFuUkxvZC80MUd0by9QTkNBaENnT1dVMXhZSVpENE45ZitEaGJGWURGdHEyR3E2VzIwZVN6b3BWWitkYTJHNjF3UmVteUkxY2ZQeTlWQ0UxUGtoRU8wQXc1REN5OXROL2xxa2dJcnU2QUg4SGltVzVKWWY3czkrSWJyVHJWTEdsUjhYdGY0V3VkSERLOW8zeXNsMGhEMW00UU5qOG16NmlSZHVkbVpNVHAzbnEvbkpIUU5GUzRWc29KNFUrU1JtSkVzbkc3MklzRm5YaTZXSmlpRUdkaUY4UStYRStQZDVkcjdSS2E3cmkvWkN2YTg2aVhqUXlsanN5MEJmUEpQdkdDdWptUGt0MHlJVi8yaWlJM1E2SFJULzVQZmpVSmJzbTROZ0ZUaGViRkZaUC9BTFJJK2RvTzVsYTYzMXBGeThUNyt3eTRyN25tQmtOMGlKM1M5U3dCb1A3TTQiLCJtYWMiOiI4NmFmYjEyZTQ2ZTgzNzMzMzBkMDY2Y2MxM2NiYjIwM2IwMDgwZmY2MmM5MTc4NzZiYjYxZjE3YTczMzE5M2JjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ilo2dVRFT1dpcjZiUFFvQmMxNVhOSmc9PSIsInZhbHVlIjoiQjM2emxQaXF5UGpQUkhsOXh3YmR1Z0RBT1ZtVEF0OHNhc2wwWjVBc1hadm1wek85V1ZiaUFabys0TzNMc0VNa2xFbnp3akplVWNxbUFSRFpkOWcwUEpFeFFkUU8wakFMMksrMDlWL29Zc0FQdXd3YjIzQ3hvOU9LdG52NjNlZzNFcWJ0NDFySHlXM3J6SXlTTkZWUHpacFp1R0JhTm5IVWRjQjYwUzM3TVBCN01XQVUvWkxFM2F5ZGs2OGd4L1U5OTBQZWIvT3grQjFJMnB1ZUc3Z1hIeEhSTHlxUkVtMUdjK3BIZUErYjF4b2dadWVWMEdSTytFaUtRakJ1bmJtbFNQQy95QmovRkVhTWlwVTlRTXJyaHNJa2lsS3liYmplVkJlUFRWT0gwUzYxeWttWkEzOVRhMVMrYmc5cGdkcDJnOFFTcGRJbHd5cEI2WDIzaXlBamxGRTNtaEVBNnM1UEU5a2tMazFUS2hrRVRNa2pobVVjd2YvZ3R1bnBJS0ltU0FaS2RON2VHSWJrVXNSblNQbE1BN0Q1Z0lkWEpmMHhOSnN2Z0J4S0JSZXpGNnZEbSs1bGQ0M21jQlZsQXRGY0hDaUcvQ2hzTVN3T3BFdVBSM2tQcGpBZ2JwTDEzSWNIampkU1JobjRMRzVzQitQeklJUUZ2QjE1UEt6d2pYck4iLCJtYWMiOiI0NTY0YzFjZTA5ZjZhZjAzMDBiOTk4MmI0YjBiMWZmMWY1NWM0YjM1OWEwMjc0OTJlZWY2NzIwMzJhMzc0YmQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-651875395\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-649380900 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0M1K0sdNadPD2LVrnt8LmBw227nA9F1U5e3ROaJK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-649380900\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-143474466 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJtZC9ZWFR2NTkrV2toTEJnQkd3SkE9PSIsInZhbHVlIjoib2d4ZDR2aTNkQmxsOGhXd0hYUVdvbE1lUHN0UlhKdTNobjJzUUVXUE5uaEd0TFMvSjVaZ2lrTytUTURRcUNiNG1ReVlEcGtmVGJOZzhVd1lrLzd2Z2VkNGlnbm1XQlNFY1ArZXFIYXVjNDJSR1NjaFpOcDkwNnFiSS9wQ3grM1pTVnBRcnpjUitqZHBmRTFYQ2NIWlB1R2hNQlpLa3BHcXRvdzM2YmwzSU5wU01CMEhjVXhCL1c0OVJCNGlvUThidmc3VXdoNlY4UmpVYW04THlXbmNtZCtwOUhZMVp4ZmZQU2V5MjJmZVV2dmJES09JaFd4MUVSVExJbDV3TXpDeU4xSXlER2ZaWjhVWVBicStTbGpwemVESkVyTW5hSTB6bSt6RXJ3aVVNYlplU3ZJaHJ6M2VsdVk1NnJtcG9NSVlCU2VPYWZZRFpoSGo2THVBRmpWN2xqbTFFWVJiQ1p0cE44RUYwWGloMmk0TjQvbDlLWGI1ZlRtT3dlSFlaa2ZyWERQejJHWDhCc1pOdFZoL3RiQzlhcm9pNjRMclM2aUZUL3BjQnNyNXNRb3VZQmlubnhrQXBmUkJjSkV4eVhiY2x2aVI3VG52NWZXQVkxWER2Wk1sQnFoY1g0cTRGYVZ3NnNjNjNyd0c0VmVlMzRhK0JydWVsWEtqQks3eTE3a2kiLCJtYWMiOiI2NGRkZjJlZmRlZmE4M2U0MzA5ZGQ2ZGFiZjA5YTI2YTA5ZWQwM2IwZTFiMWI1ZGRkNDU5YTAzYzBjNjY3NGIwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkMzb3ptZlllRU5lZjliemVFSnRlU3c9PSIsInZhbHVlIjoiSkg2U3hrZXh4L0RVdXdObnRYcXpkUkZZY2pySFdHUlMyZGJ1OW5nazUyQmhPaVpDNFErRU5QL1dTRHlWdWtTeE1pUFZ3a0JmdEY1T25BcUNmOXNsMGtVc0Qwdk9tWEdqZ1UzdU5yTjdWb3VIbjg3Sm82cEU0eUE5eUM3M1BWWTk3VHE3WnE0Tnduek5RLzNyK2FuNkduOXRZOEhPV09PVEk0NnRZRHgxY002b25TTFErMnNDRTNIc1FtRU4vemNMQkZTVWhGcitGWlhOcE9GTEZaTzRDeG16bkRKbEFja3d3SlJmYnFEcmtFY05xdjJMZDArd1lPREgyc3dpOHZUbzhIaTU5eDNUckt3cjBPN3RuVVZYQmUyVWFXV0p3b3NaVWxnZDZLa0x3SmgxSHk2ckpvMlV4c0N2MnJicWN5dlJZZEtRdFhwS1oxR3IwZ3JVNENCbzdXeW9UMlBUMG1VemEraGoyeWlJRFQ1NHRVUTZCM3V0MTYzK2lNUzdsRGlQclhGNDhocjF4Mm10MUVEOTZENmlLSzM2eDJjS0NCUGdYVnJkNWlTOHZ0MW1tc2lhTnppdXh2MFV2Tkw1R3pFcjFESGRPS1FFRjNzZys1dW1nMkV1MFlQd2FpbVlNY01oOEtkeGRlZVdjejdMcWdQUVZkYm1aalAyelNCeGV1Y0ciLCJtYWMiOiJlMmE4YTk4YmJmZTIxZWNmMzBjNWEyMWIyNjNkNzU3YmY5MDAxYTBlNDEyNjJmNGE2OTlmNzg3YWJiYTUxMjgwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJtZC9ZWFR2NTkrV2toTEJnQkd3SkE9PSIsInZhbHVlIjoib2d4ZDR2aTNkQmxsOGhXd0hYUVdvbE1lUHN0UlhKdTNobjJzUUVXUE5uaEd0TFMvSjVaZ2lrTytUTURRcUNiNG1ReVlEcGtmVGJOZzhVd1lrLzd2Z2VkNGlnbm1XQlNFY1ArZXFIYXVjNDJSR1NjaFpOcDkwNnFiSS9wQ3grM1pTVnBRcnpjUitqZHBmRTFYQ2NIWlB1R2hNQlpLa3BHcXRvdzM2YmwzSU5wU01CMEhjVXhCL1c0OVJCNGlvUThidmc3VXdoNlY4UmpVYW04THlXbmNtZCtwOUhZMVp4ZmZQU2V5MjJmZVV2dmJES09JaFd4MUVSVExJbDV3TXpDeU4xSXlER2ZaWjhVWVBicStTbGpwemVESkVyTW5hSTB6bSt6RXJ3aVVNYlplU3ZJaHJ6M2VsdVk1NnJtcG9NSVlCU2VPYWZZRFpoSGo2THVBRmpWN2xqbTFFWVJiQ1p0cE44RUYwWGloMmk0TjQvbDlLWGI1ZlRtT3dlSFlaa2ZyWERQejJHWDhCc1pOdFZoL3RiQzlhcm9pNjRMclM2aUZUL3BjQnNyNXNRb3VZQmlubnhrQXBmUkJjSkV4eVhiY2x2aVI3VG52NWZXQVkxWER2Wk1sQnFoY1g0cTRGYVZ3NnNjNjNyd0c0VmVlMzRhK0JydWVsWEtqQks3eTE3a2kiLCJtYWMiOiI2NGRkZjJlZmRlZmE4M2U0MzA5ZGQ2ZGFiZjA5YTI2YTA5ZWQwM2IwZTFiMWI1ZGRkNDU5YTAzYzBjNjY3NGIwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkMzb3ptZlllRU5lZjliemVFSnRlU3c9PSIsInZhbHVlIjoiSkg2U3hrZXh4L0RVdXdObnRYcXpkUkZZY2pySFdHUlMyZGJ1OW5nazUyQmhPaVpDNFErRU5QL1dTRHlWdWtTeE1pUFZ3a0JmdEY1T25BcUNmOXNsMGtVc0Qwdk9tWEdqZ1UzdU5yTjdWb3VIbjg3Sm82cEU0eUE5eUM3M1BWWTk3VHE3WnE0Tnduek5RLzNyK2FuNkduOXRZOEhPV09PVEk0NnRZRHgxY002b25TTFErMnNDRTNIc1FtRU4vemNMQkZTVWhGcitGWlhOcE9GTEZaTzRDeG16bkRKbEFja3d3SlJmYnFEcmtFY05xdjJMZDArd1lPREgyc3dpOHZUbzhIaTU5eDNUckt3cjBPN3RuVVZYQmUyVWFXV0p3b3NaVWxnZDZLa0x3SmgxSHk2ckpvMlV4c0N2MnJicWN5dlJZZEtRdFhwS1oxR3IwZ3JVNENCbzdXeW9UMlBUMG1VemEraGoyeWlJRFQ1NHRVUTZCM3V0MTYzK2lNUzdsRGlQclhGNDhocjF4Mm10MUVEOTZENmlLSzM2eDJjS0NCUGdYVnJkNWlTOHZ0MW1tc2lhTnppdXh2MFV2Tkw1R3pFcjFESGRPS1FFRjNzZys1dW1nMkV1MFlQd2FpbVlNY01oOEtkeGRlZVdjejdMcWdQUVZkYm1aalAyelNCeGV1Y0ciLCJtYWMiOiJlMmE4YTk4YmJmZTIxZWNmMzBjNWEyMWIyNjNkNzU3YmY5MDAxYTBlNDEyNjJmNGE2OTlmNzg3YWJiYTUxMjgwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-143474466\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-827279402 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"87 characters\">http://localhost/branch-cash-management?end_date=&amp;start_date=&amp;status=all&amp;warehouse_id=8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-827279402\", {\"maxDepth\":0})</script>\n"}}