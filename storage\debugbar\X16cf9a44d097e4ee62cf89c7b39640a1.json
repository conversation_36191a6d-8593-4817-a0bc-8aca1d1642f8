{"__meta": {"id": "X16cf9a44d097e4ee62cf89c7b39640a1", "datetime": "2025-06-30 23:13:42", "utime": **********.394169, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751325221.982534, "end": **********.394181, "duration": 0.41164708137512207, "duration_str": "412ms", "measures": [{"label": "Booting", "start": 1751325221.982534, "relative_start": 0, "end": **********.337221, "relative_end": **********.337221, "duration": 0.354686975479126, "duration_str": "355ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.33723, "relative_start": 0.35469603538513184, "end": **********.394183, "relative_end": 1.9073486328125e-06, "duration": 0.05695295333862305, "duration_str": "56.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45043352, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029200000000000003, "accumulated_duration_str": "2.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.368748, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.932}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.381562, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.932, "width_percent": 20.89}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.387414, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.822, "width_percent": 19.178}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-650759327 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-650759327\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1341900032 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1341900032\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-237381309 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-237381309\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1238184780 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325221303%7C29%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImhTeGtJZ0RqNlgxZHJoU2doVHdXRkE9PSIsInZhbHVlIjoiSGZJQTRIak5BWWNaY3R6U29jNk9kMERMZ01HY2JYbWl6amQxWmtmdmViVzRFc1N6RXNsdk51bGVHdmxLMm4yeUJBN2dDdncwejVHMHNKVUgxemZtR2RHQlhyMytadHVKQnNNWk5pZlZObXlBbmJMaEMvWGYxaTdZS0dtc1kvbC9WM0h1ZXJiNHVlRHRGcmZpdjhXWHJCbzBSNDJtSnBYSFpxVklMMDZWdzRuWXh2N1hYQmdvTG5qY2wzY250Z055OHlCN0xoSkllZFlWNE9ZSTVCVDBESHFVcFZNK2puNVptdm50bHE3aTdFT05VdnB4UjBQZTlZMWdLZUtGanczdUQzZXFQV3h3Y1hURmlpUFFWaTE4NjdnNFlpMHNodXZFejJmWEMxalkvU3hucVRZcWpYUG5pLzZhTUxzZ0lwaG54dVdaWjNiRzdDNXMrVkRUSjE2SWxiMGZvVHBsWENKdzFhNzErNkIvMWMzR2hia1RkMWhTdnZ4TU53TkFjU3ZHbGhocUNJSER2Mk96SW5uQ1J4Z2JkK01XY1NoYUIrdWxhT0hRb0VTSy84MWFhY1FxdWlCZ1QvQys5bWlvN3lZT015YS9MZi9yU1FqNGowbVBNbTJPK3RXVjdFcUlOd09tRjZLRWhKS054WFJTOXZWVDlOc2U0UG45enVFQTl4YUgiLCJtYWMiOiIwMWNhNDQ2YjYyMDExM2YxOWFiODY0NjEyNzg3NzkyYjMyOWMxNjc0YWRlNzVjMjQ5NzUxZGUyN2QyODAzZTk0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkhhdHJDald1WGRkY1ZhYW5MQTJvNGc9PSIsInZhbHVlIjoiTldxZGRSWFhUMDE1VEZjcnZGTGRmc3Q0blFJWVg0SjY1cWpHcFJKVGRHWDduaHR0eGJFMXZQNVZTSW5FNWExalZQYzRMa3V6TUdXOGV3RlR5cVcrZ1c4MkhBV2FLd0xDM1BIalNuemw0TzNVRkVkSEtIc2t5U2VveGxSSGt4cWxYWDArZ3hqTHU2Q2IzVFFnMkFIeHQ3RDk0UnhaaDJrdkRPS21nOGMrYUZSTUgyK0hoUzNEZ0ZTOEp6TmdVK1RxbXJzZFI2c3pSQ0tnUEIyd3hwcDIxRVlvbi9HWGtWOXZYZ25qYXJiZU8xanE0N2lpNHQwRlhscHNRcFIwQkVoOVBGU3VFaDZtcURkL2g3Y1JwMlJjdUg0ZlFWbnpNS2lWblpjTWRVNmZKaDR5WHdBbForRFk2K0xDdjY2djMwd29iTFVGbHRyZXV3WjJYWFNDWkRvNUNZUCtiK25VU2pSZFkvNzkwcDF2eGE3NUVpWFJnUkNYMUdTbVFyUThjRUNsZDdVUmNOWWo4K3pYTzNsa0ZDZlAyUzhPMGdONENBcjBGZ21VRHQ1b0NKOUJwTTVOcjZhNTE2SFBqdkZKZGJWcFFobXRGNGlwTk1zeE5yaG8yVU12OXYwUERtaWlrQWp4NzlxZ1IyMXcvRzNMWkRicFRkRW9YZmZQQkgzckpKa3EiLCJtYWMiOiIyMjBlNzU3Y2ViMzQzMGYwYzk3MzY0ZGMxOGRkOTQ3NTc5ZWU5MDE3NDg5NThmOTI2NDUyNWM0ZDZkMjg5NTU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1238184780\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1223221335 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">H4YK57QUXyJ63T0CnkmwYWGCllGQ9A8V3d5cZCUG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1223221335\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2101172944 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:13:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikh5VmxnMDV5Vm5Oc2tqUUNaN3NkSmc9PSIsInZhbHVlIjoiQ3F6Ukdweno1L2tDWjVIWGlDWVZKNnQvT3ZGQ2dqVjFFNkhueHk2MXRzSkFzOThkL1lWU0lSNkNBNVVqd21YbE1yeHNGMllvYlBXMGpURG5NRmNJdnkvK1Azd1l0TlVOUUc3bHcySEdHSU43MThZQzd3VGtGNm9vamxOakxHZjNmQkt5Y0RGS3dpbFJJZ3lpa0FJRFNTWFNuRERoMktDcUlXYU9DaHhFai9oRExTOEI3d1VmODcvQW1PcVlIbncvb3d4Z0UxdE1RYVp4eU9SUUdIU25aUnJYV2RJcFlZeTlldXlKNGthUDE5Qkp2bEwwNzdlRG1xUlVoeWNHMUlaU0w5K3REUmM4RzlNV0tFKzNkc0pnRmNFLyt1V3g2ZU1ENnVVUklsYml3NldMcWJpVG5qaFRBa0lza0l6VWk5U0xZZ1dlbkFxNlVYZXNDWEtMTXhrTE5FRGVFMllEdUtxR09nSXAwWWE3SU5UZFYwU2FiM1JkQWJNMUdwMldESnlaVEE2bXpRRkVPK28zd1JyMjIwb0dnc0lhOFpPVmdFWnhYWDVvOUVOK2FzTzV3QTFKYXFpT0ZJMXFHbGZOL00xZDR0WllZdDZCNVVHWExuLys1NXlOZ3h4bXRQT2hDZE9PakxjY2tSTlpSZFVsTGs2QWU3VmlXTVlFRC9LMUV5VjYiLCJtYWMiOiJlNWMxNjA0YjA2NmJjNTEzNDZhNzg4ZGQ4Yjg2ZDkyMzBkNmE2OTc3Yzg2ZTg0NzFlYWY3ZWFmMzA2OWI4YjhkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImVSTitMUnliT2V0bThLTXg3Q1NqNWc9PSIsInZhbHVlIjoiK05qaVVRVm4rWWU3cmUybG1PYWh2cFdjY1E5RnFHek5HZ2tiTTlsTHA2Qzk3SUl6a2FqSi9qUmlRM1Jwc0YyVy9qTWc4MGdxeWtXelNGcTA1NkFORmpSL2ZXUEFQckQvMUxZQW54alRYQ2ZnbFVhYTNiMk9KY2FCaXRHNzUyTHgrSTRlRFdpdEJ5emplOEJwYmVlZkRGcFYyM1lBWFFTL1FzTnNsbnZHSzY5aWtQVWc4Z3FUTUI3T0RGcEJoTEcxSUxxNEtsRmxCeDZZSEZmN3ZyN3FKRFJDdkN4bkNaSzQ2UWk2ZDFPZURpTTdsNEt5ay9rQTZ5d2p0a2VDOXpLMjdiZVVpK2RCRHZPamdrbnNxNW96RFhqbzd3ZlMrajkyTGpxYWgrSHJVQ2wzZFlpNXVZOUhnWmtDOHY0aHoycHJjVkZ2cnQrbUF3TkorRjNESU1vWitaNmpDc0pra05IWFJGU0hzTmJKSk9hSjJqc3lzOHpYY1NEVkFpZzg1YzNnV2EwZGtGdVR6ekdDcWwwUkVrREVtdFJoUkRpZVlVam56cW45SGxkeDNkQXRVaTZJam8xS3lBc2tHZWQ4Vkw4S0pzWGdwUXlLYTk0dnU0SmFZK0lsZkpVZGtkQ2tnWlUwRjRNUnRMNjU5OGdGelBFVWNJbkFvTmRkajdNRy9xRWEiLCJtYWMiOiI1OGZiOGE3NTBiOTQ5YzdkMDJlYWRmY2U1Njk3N2MxZGNlZjI5ZjNiMTE1NTAzZDFjODU2MWU3MWY1NWIzZjQ1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikh5VmxnMDV5Vm5Oc2tqUUNaN3NkSmc9PSIsInZhbHVlIjoiQ3F6Ukdweno1L2tDWjVIWGlDWVZKNnQvT3ZGQ2dqVjFFNkhueHk2MXRzSkFzOThkL1lWU0lSNkNBNVVqd21YbE1yeHNGMllvYlBXMGpURG5NRmNJdnkvK1Azd1l0TlVOUUc3bHcySEdHSU43MThZQzd3VGtGNm9vamxOakxHZjNmQkt5Y0RGS3dpbFJJZ3lpa0FJRFNTWFNuRERoMktDcUlXYU9DaHhFai9oRExTOEI3d1VmODcvQW1PcVlIbncvb3d4Z0UxdE1RYVp4eU9SUUdIU25aUnJYV2RJcFlZeTlldXlKNGthUDE5Qkp2bEwwNzdlRG1xUlVoeWNHMUlaU0w5K3REUmM4RzlNV0tFKzNkc0pnRmNFLyt1V3g2ZU1ENnVVUklsYml3NldMcWJpVG5qaFRBa0lza0l6VWk5U0xZZ1dlbkFxNlVYZXNDWEtMTXhrTE5FRGVFMllEdUtxR09nSXAwWWE3SU5UZFYwU2FiM1JkQWJNMUdwMldESnlaVEE2bXpRRkVPK28zd1JyMjIwb0dnc0lhOFpPVmdFWnhYWDVvOUVOK2FzTzV3QTFKYXFpT0ZJMXFHbGZOL00xZDR0WllZdDZCNVVHWExuLys1NXlOZ3h4bXRQT2hDZE9PakxjY2tSTlpSZFVsTGs2QWU3VmlXTVlFRC9LMUV5VjYiLCJtYWMiOiJlNWMxNjA0YjA2NmJjNTEzNDZhNzg4ZGQ4Yjg2ZDkyMzBkNmE2OTc3Yzg2ZTg0NzFlYWY3ZWFmMzA2OWI4YjhkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImVSTitMUnliT2V0bThLTXg3Q1NqNWc9PSIsInZhbHVlIjoiK05qaVVRVm4rWWU3cmUybG1PYWh2cFdjY1E5RnFHek5HZ2tiTTlsTHA2Qzk3SUl6a2FqSi9qUmlRM1Jwc0YyVy9qTWc4MGdxeWtXelNGcTA1NkFORmpSL2ZXUEFQckQvMUxZQW54alRYQ2ZnbFVhYTNiMk9KY2FCaXRHNzUyTHgrSTRlRFdpdEJ5emplOEJwYmVlZkRGcFYyM1lBWFFTL1FzTnNsbnZHSzY5aWtQVWc4Z3FUTUI3T0RGcEJoTEcxSUxxNEtsRmxCeDZZSEZmN3ZyN3FKRFJDdkN4bkNaSzQ2UWk2ZDFPZURpTTdsNEt5ay9rQTZ5d2p0a2VDOXpLMjdiZVVpK2RCRHZPamdrbnNxNW96RFhqbzd3ZlMrajkyTGpxYWgrSHJVQ2wzZFlpNXVZOUhnWmtDOHY0aHoycHJjVkZ2cnQrbUF3TkorRjNESU1vWitaNmpDc0pra05IWFJGU0hzTmJKSk9hSjJqc3lzOHpYY1NEVkFpZzg1YzNnV2EwZGtGdVR6ekdDcWwwUkVrREVtdFJoUkRpZVlVam56cW45SGxkeDNkQXRVaTZJam8xS3lBc2tHZWQ4Vkw4S0pzWGdwUXlLYTk0dnU0SmFZK0lsZkpVZGtkQ2tnWlUwRjRNUnRMNjU5OGdGelBFVWNJbkFvTmRkajdNRy9xRWEiLCJtYWMiOiI1OGZiOGE3NTBiOTQ5YzdkMDJlYWRmY2U1Njk3N2MxZGNlZjI5ZjNiMTE1NTAzZDFjODU2MWU3MWY1NWIzZjQ1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2101172944\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-760116716 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-760116716\", {\"maxDepth\":0})</script>\n"}}