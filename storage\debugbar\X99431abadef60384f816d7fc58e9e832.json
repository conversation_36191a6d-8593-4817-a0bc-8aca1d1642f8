{"__meta": {"id": "X99431abadef60384f816d7fc58e9e832", "datetime": "2025-06-30 23:13:03", "utime": **********.490535, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.05311, "end": **********.49055, "duration": 0.4374401569366455, "duration_str": "437ms", "measures": [{"label": "Booting", "start": **********.05311, "relative_start": 0, "end": **********.411245, "relative_end": **********.411245, "duration": 0.3581352233886719, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.411253, "relative_start": 0.3581430912017822, "end": **********.490551, "relative_end": 9.5367431640625e-07, "duration": 0.07929801940917969, "duration_str": "79.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48188848, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00763, "accumulated_duration_str": "7.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4460092, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 26.606}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4565182, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 26.606, "width_percent": 8.388}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.470181, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 34.993, "width_percent": 6.684}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.472033, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 41.678, "width_percent": 6.291}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4768379, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 47.969, "width_percent": 33.159}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.481742, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 81.127, "width_percent": 18.873}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-460602619 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-460602619\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.475909, "xdebug_link": null}]}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-875967037 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-875967037\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-108789853 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-108789853\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1693375868 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1693375868\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2115566183 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2818 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; laravel_session=eyJpdiI6ImpxTklOWXhTYU1XWEJkQTBqVTRXZlE9PSIsInZhbHVlIjoiTXFGZEpQRXlGa212dDlvcjMyZUgzOTJTdTZUUld1Y0RIbVM4elBEck42T2RTa0tjV2NoaTJNekRxM3Vkc3lrR1dOeGt5blA0Ym55U2pNZWY5am1QNC9lMm5SZE1VcGh6OVBneXMxN2Y4Qmx1TWhKckg0OXNOdzJBV1ZmTzVubEw3cmlyUDRwVnhxYzd4NmhqaEJiZThRcHlESndxYTY0WWNKZ1pqeVdFSTZuZnRmZ1BDZy91c3ZCbWloYytBQnpaczBrTmh2S3Z1dllBcHJYMkhWOXM3alFFWENqVkhpc2Rnb1lHSlpwM2dNb0s5cUxpakE5OVNEbEZRZ0VUVDJBWFZKWmZyYjZRM2ZNL1BuR0l5L3BsYXhVbzEwQ1ZlaXYySVUrQnk5a0dvY1BKK2dwQnEzM3ppZnFlaW9XTW9Ga1pSQ29mZktuT3BjSnc5ampzYU1SdTIxNzd4Wms4eUJ6eWdrajg1UUhYS2puSkhMNDVRbEh3WENJazQ2Qzk0Vlk5cE5NYW1qQ0xCbWJBVHZ5ZUlzUzBGRTJCWGliL1ovUHlOejhGSHJzRS9UOC8wK0QvTTQ4WHdveVBkTE1CdHlWcnFrQWdDcU1jR0hscFd0SUgzYWRtbFRaT0pzTGUzSkVDS2k4YlhiU0s3b29aMU5tbWtnL1NUaWZ1VmhxYnZ3ZFoiLCJtYWMiOiJjNTgzZDkxNDlmYmY2OTAwMmNmYTUwNTg0OWMwNDQ3MTVmZWQ0YWFiNjk4ZTJkZDM4YTkzNjhiMzE3YzIyNjlmIiwidGFnIjoiIn0%3D; _clsk=eiqg7d%7C1751325181289%7C15%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imc5MXFBZ0pMTFlpUWJqRjFoNWVyWVE9PSIsInZhbHVlIjoiM0IyTkhPNlg1R3NzUTEzOHpIbUhZdUFzbFl5KzRNdmt0NWZGQTlVeWxReEJGSVRKb2NKbSsyVXhRUW1HcDBIRGIwazZ0M2VxSVFxVUl5VXc5aThPaGNEUUxPQml5T2pjbWlWSXpnak1YNm5WaGlFTEZaVlZNL3daVTUrY2JUbVlOOEl2dWNXZDRNYU5XMEZxQ2ZrNlBiK09MTjdXV2cveE5xaVp0Qmt0VTVzZXdkeDRRdEM4cW81R3RVV0pOT1J0QUtRdk03aitOdEVKNGwwSFdiUjdBSE1NaFcyYWtkaUw2RmpKT2RjUkRmN0dIdE4wUVRFVk42TVVkUmRvaUhmeEFoMWZZQ3BzUER2ZFZpSzF3S3YybGJuTXFSVW90Wi96Q0g3UGFjdUFHa1ZCOE1tOTRHKzh5WTMwUFZBcUZGQ0VxblNqczdRSmNJMy9tWnIvOENEczlKU1lQMEl1RGg3b2hoTjcwZDQ3RnhEV0RaNlNRYmtJNmgrZVR6NkxQT280QnV3ck10c2hWTkx1eEtlQmUrQ0hjUlg1TWRFNjZGaEg3L014MnYxUXEvNEFMWThVbU5yWGdxbFRnenk5cTkyeU9FRDh0aEpSa0ZlV0FiWm1VWlBJRERGSUhLTTZ1eHFBelJYZTRIc1B4VlZoU3ZTRnBpOGFiMUpHNm5WWmpPangiLCJtYWMiOiIwNjAyZmY5OTI5ZmE1Zjk5ZGYxOWI0YmIzN2E0YjUyNTc2Y2YzOGIwMTQzMTE1YmI3NWFkZmY0YmFiMGIwMWM1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkZpK1FLNHFWTnBLcHE5Y3JXRGpQMnc9PSIsInZhbHVlIjoiVTdKUWFmc2I2ZWJjRkZFWmEyZGdrbnNDT3NsV3JmNFZheU1MdjNzOGJReDJIcGZrWDNJa3dUck9ycmlSamtnbk95VHEzNmY2ditmalM3T2hWVERVcGlDTGhRNkpKUnhuYksxTHpCRFBTdTFBYzVpN29vVFFFR1JMN3pjUjdFZG1heTlKZ2NKN1lpcTU2VVBDdnQrSy82cG4wazZXNE0rN3Z3MmpOaEdSVTlnYWpwRmRWVVpCajVWUmJFa1d1NmFvMzMrRitjamhYbTRJZzN6a1FLaExGcnFnbjkyZEdjRFJBU1plVUdJNnhTdS9XL2duRUN5ZVBDaTFQL0FUWFBBcCtkTFFNYnZjT0VTM0VJZjlVSW5YZ2dWZk5wM0xUN3Q5LzR0YXNQLytGK0RNcHVRUjlqaUNUNzNVYUZtU3k1ZnYwc09wTjYxV0o3blluYkpwTWNvclJZWWl6N2NydWQ5ZFNQQytwaCs1bUJJQmIxODJGN052bGdKaWZ4NEV6SDFQZlAwSER0bWNKSnJFd1dvSXoxMDRtaXBwOWluRnNnRTh6VXh4U2pJSDVqdW5HZVpQVVhFNkV0RlhDcU9BcERCdUoyTE14UWZnVzZXeWpyZW1HZnBXWE44UUU1YXlVdzJNellvMVJEaVN1bG9YNi9nMTV1R3ZicDhud1VPUDFPcjAiLCJtYWMiOiI3MGJkYzcwOTA5OGJmZTdiMTlkYzkyZTY5YTA3OWNjYjY5MDY3NTIxZGY4NTA5ZTZjMDQwZjcyMjY3Y2M1ZTUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2115566183\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nMXQGGGSGQFIfGwlrnEYCl0LIB0DtCvcZqQRlbGj</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-401554003 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:13:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjIwREo2TFlFbFRNaHk4Y25XMXdEOFE9PSIsInZhbHVlIjoiNGlXYTFlYXIrUlkzSVlrbm5PMWNNN3B5ZkRSL1pKWkxLUjhDYi93VEJJOUNreURCVEs5MjFMNko5YXd4cGFJQ2dNbmFOVGY3QUMyRWRyMlJKcERLdSs0b1ZNNXZpMEw2SWJaeHV3clRZOC9HWDkxMlV6QjQ4dFpxMi9MR1poT09xY2tBSmxFRklvYURPcHdHcm45dFlTd1VReTF4QjlKSitIVk1iV2QwN0NpdU1TOTZUTlNLaVE5UnN3aXljanYxTERwWmF6VlhNbU4rTzFlRDZDc2RrZGlzV2FwWm9CL2NSNHJkaUREemVSR2FtdzF2RjdjSytFbkhpZ2RRa01PdW1vMFIzTk1TN3JIVkZtc2NrcVIvc3FmNjd1b1J4RkorSG14N2RWbEVxTXkwWnd0b3JxNVBhNURlY0JDeXkzQWVoT29zZnU4M0tDMFk4dUNxaXM0QWxmZnpNMjdBUndyOXdSTk1xRkV3a0pHWldiWUhVa3JKRHhEdkFBVlJZdzRtbXRtZG11aW5MbnlUY3AzVmtEYTJ1Sk9VbE03U2w3amw4NnJQQW5SMEdHdi9hQXNVT3ZRK2p3eVh5MGk3Z1B6MWhScEFQZjFrQnZlc2t2Yi9XTGd5NHgxa0huQ0xqYTBBOW5NTXV2OG1hUEpCVnV0SFpOdFRhTElPQnpxT3JxWlMiLCJtYWMiOiJlYmFhYWM1Y2MzMjgzNWUwNDNlNDc2MmUxOTViOTM0YWMwYjFjOGZkNWNiZjBkM2ZkNGIwODA2NjVlOTNiYzExIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjU2YXVJYm5jR1NXV1dIMWRqcmIzVlE9PSIsInZhbHVlIjoieUY4d0ZybFp0d3J6V1E1ci9JU0dMRzYzS1k4ekJQaStWSWhtSURpWjdEZ3NDRXNSSlVtSzM4VnBGdzNxZ1JsWjd3SSt1Q0RyNmVTK2wvNkdobTJmSFJhVnZLVWFQVU94SURZZjhzeVJQSnU5ZndRNU42WDJlelNXakpxRFJUWjZmdzRzdzlzL2dtakVSUnhpSDRSbmNJNll4dVFFOVhMVklGeks2c3podEQrb3FzY2hJaUgzQytocklwdWhSQmF6RmZqZFUzZTQ5RDBpQm5nVnhadGFRWmlwV00xQzRSRUZwdFVPZVFyc0xNbW0rSG1MS3FZMFhUMHUzYUtmU2lSZEZHcEYzaVVyQ1JJUW56UVA3bUtuR1VsRTNYeFh2a1ZKTkRic0ZWNFZJUWRpOUNWZ1NkdXZ5aDNTNlhiUFlEN0lETUV5dTN6d0lHYUN4ZEQ0MXpvNGZBanI1a2hQWmc2SUg1OEF0SG9CNU9vd2lLa2ZqMmlCdEJEczl0V2x0SzF3QngxT0s3ZDVSMzhSZDRwdVR3QjBFOUhaT0ptbUNnWU1MOUg5blF4aS81dlBhZWxudDVRWFRmZE1OZUw4b2JHc29YWUM5TnhRTjlJVk9RNVlJdnNCS0dRTUgxWnFMM1FjdGQ1Q043V0RmUVpmUWpHRHdKanpFaVdnWXZpMVRJOWYiLCJtYWMiOiJiNWM3MTc2MzMzY2U2MzA1NzYxYjEyYzE1NDkyMWU3MzYwMjgxNjM3NmI3ZjQ2ZjA5NDg4MjczNTJlMTU0ZTRhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjIwREo2TFlFbFRNaHk4Y25XMXdEOFE9PSIsInZhbHVlIjoiNGlXYTFlYXIrUlkzSVlrbm5PMWNNN3B5ZkRSL1pKWkxLUjhDYi93VEJJOUNreURCVEs5MjFMNko5YXd4cGFJQ2dNbmFOVGY3QUMyRWRyMlJKcERLdSs0b1ZNNXZpMEw2SWJaeHV3clRZOC9HWDkxMlV6QjQ4dFpxMi9MR1poT09xY2tBSmxFRklvYURPcHdHcm45dFlTd1VReTF4QjlKSitIVk1iV2QwN0NpdU1TOTZUTlNLaVE5UnN3aXljanYxTERwWmF6VlhNbU4rTzFlRDZDc2RrZGlzV2FwWm9CL2NSNHJkaUREemVSR2FtdzF2RjdjSytFbkhpZ2RRa01PdW1vMFIzTk1TN3JIVkZtc2NrcVIvc3FmNjd1b1J4RkorSG14N2RWbEVxTXkwWnd0b3JxNVBhNURlY0JDeXkzQWVoT29zZnU4M0tDMFk4dUNxaXM0QWxmZnpNMjdBUndyOXdSTk1xRkV3a0pHWldiWUhVa3JKRHhEdkFBVlJZdzRtbXRtZG11aW5MbnlUY3AzVmtEYTJ1Sk9VbE03U2w3amw4NnJQQW5SMEdHdi9hQXNVT3ZRK2p3eVh5MGk3Z1B6MWhScEFQZjFrQnZlc2t2Yi9XTGd5NHgxa0huQ0xqYTBBOW5NTXV2OG1hUEpCVnV0SFpOdFRhTElPQnpxT3JxWlMiLCJtYWMiOiJlYmFhYWM1Y2MzMjgzNWUwNDNlNDc2MmUxOTViOTM0YWMwYjFjOGZkNWNiZjBkM2ZkNGIwODA2NjVlOTNiYzExIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjU2YXVJYm5jR1NXV1dIMWRqcmIzVlE9PSIsInZhbHVlIjoieUY4d0ZybFp0d3J6V1E1ci9JU0dMRzYzS1k4ekJQaStWSWhtSURpWjdEZ3NDRXNSSlVtSzM4VnBGdzNxZ1JsWjd3SSt1Q0RyNmVTK2wvNkdobTJmSFJhVnZLVWFQVU94SURZZjhzeVJQSnU5ZndRNU42WDJlelNXakpxRFJUWjZmdzRzdzlzL2dtakVSUnhpSDRSbmNJNll4dVFFOVhMVklGeks2c3podEQrb3FzY2hJaUgzQytocklwdWhSQmF6RmZqZFUzZTQ5RDBpQm5nVnhadGFRWmlwV00xQzRSRUZwdFVPZVFyc0xNbW0rSG1MS3FZMFhUMHUzYUtmU2lSZEZHcEYzaVVyQ1JJUW56UVA3bUtuR1VsRTNYeFh2a1ZKTkRic0ZWNFZJUWRpOUNWZ1NkdXZ5aDNTNlhiUFlEN0lETUV5dTN6d0lHYUN4ZEQ0MXpvNGZBanI1a2hQWmc2SUg1OEF0SG9CNU9vd2lLa2ZqMmlCdEJEczl0V2x0SzF3QngxT0s3ZDVSMzhSZDRwdVR3QjBFOUhaT0ptbUNnWU1MOUg5blF4aS81dlBhZWxudDVRWFRmZE1OZUw4b2JHc29YWUM5TnhRTjlJVk9RNVlJdnNCS0dRTUgxWnFMM1FjdGQ1Q043V0RmUVpmUWpHRHdKanpFaVdnWXZpMVRJOWYiLCJtYWMiOiJiNWM3MTc2MzMzY2U2MzA1NzYxYjEyYzE1NDkyMWU3MzYwMjgxNjM3NmI3ZjQ2ZjA5NDg4MjczNTJlMTU0ZTRhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-401554003\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-438773457 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-438773457\", {\"maxDepth\":0})</script>\n"}}