{"__meta": {"id": "X62ae895d8f2699b19e32f8589b7af8af", "datetime": "2025-06-30 22:39:32", "utime": **********.393059, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.939, "end": **********.393077, "duration": 0.*****************, "duration_str": "454ms", "measures": [{"label": "Booting", "start": **********.939, "relative_start": 0, "end": **********.310651, "relative_end": **********.310651, "duration": 0.*****************, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.310661, "relative_start": 0.****************, "end": **********.393078, "relative_end": 1.1920928955078125e-06, "duration": 0.*****************, "duration_str": "82.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.027469999999999998, "accumulated_duration_str": "27.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.341783, "duration": 0.026449999999999998, "duration_str": "26.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.287}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.377046, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.287, "width_percent": 1.857}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.385369, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 98.143, "width_percent": 1.857}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751323019449%7C9%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBMM0VYK3VmKzNRUmsxT0JmQXZNemc9PSIsInZhbHVlIjoidERLSytNamppUncvWnRqUUhDajcxaFpTaE1ob1BVNVVJOU5hQlVqVVhnUHNBUGgvdm12UXkzZDBqa0NVbTFDelh4d2NyQ0d1WnlwSS85dXgrTW5qYWxZdnkrQXRGK3NLN2UxSGNoblZyYngrbEs0QWdzM0t1OFUvVXplQUZGOE9aenhvMmpqRkRVbHNhR3Y1UXZQZGlyVHAwZVFXVnRCdENPREpzTDRMc2d5WEdMZk5lSWRBTDJDZlE3Z1NqNDZLYkEyb1F6OGFPTGJaRmNhRlJYcXlEeXdObm9jVysvSFp6L3R6Rll2MUxCSnJzS3JpVENVQkJTNlRuUDVGcUxRVEh1QUFxdU8rOEdYdUNReEZWY1BzdFFleHJ2QkgyRGxWdUkyVjV2RVNrazdmaEdWSnRqVVRQNHFTcTNrc09Nb09SUXh4amRjdFQ5S0x5dVkvdHJkT2ZDaE9NZFlmMGRpRFhWZkFYUmdUZ0s5QWtwelEzOUdhcnB2c1FkRnZyZmR1a205d3BsNlhMcGVhL1BHKzJtN0Y5bEl3OUxMYURzOGtyQnlOd29KRVdxQnhUYng5ZlBVV3hCVTgxejN4SmNOU3JiQ2tYN3JMcE02cXdoT1grbk5MT2hmcnhSVmE2ZFI3WldHMlBGL0I2d20xaEc3b09HRDhHanp0NmxVSDFPWnoiLCJtYWMiOiJjOWRhOTViNTZiZjllNGUyNjdhMmQ0MDE1MjI5ZDMxZDQ0OTc2NzFiMjQyNWRjMDdkNmMyOGFhMjk4ZDc4ZGE5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InNycEpHWXJ4OFB4NTdzTWlMTWxURWc9PSIsInZhbHVlIjoiZkZXQk9pSlg4aWlSZTMzYWkwM0hVVFJpT2pnOS9udkxqODBYQlZ4NlRwd1dmckhXVHpmN1dyenpteG56dFhRbWYwVUU2Z1phSnI3bFdWdGFxWVI5NG9iYWJqMXdlTTVSUXV0MFM5ZDArZkp6T1ZodlZtZXh3OFp2TVhNQnphOW9sQkJHSTZ0RGpYa0p5OU1sSFJPY2dCc2U5M2U4T084U3lnL0h2T2lDZzJQcm1nbWFpdjhpNVBaZ0xWMU1jLzNwQ2pnT015cHJmclNHY2JUK2NBSmM0Qk4vRjZRQXJCeTRmR05JSG8vQmJCNVQ2N0ZlZnlhV21KVmppdjRvYjA5NkdNM2ZPMjczL29VY3luUGFyVmFTNlJ0OXQ0clZIa1p1dEpGSHB5QVNZVkh6NEFnYW93dFdZcGJlRm55aWgraHRhNTRLdHRjZmVrMStLYlJtV1gxOWpvdWx6VzVobzlSNnVRT1lRVGlaRVp3cWl3VWcveDlvRk03NFp1YjB1aytxZDVvWjlxTHE5TVkxbjRocHY0S2N1RFhxaCtNOUhwT09MVnZFUnNpTmtQMXBrREk3Qk5Oa0l3Ly9zK3F3UmxnRUdmNkNpcWpFaHRIdEhsREMwZUtpQnhsbXE1ajBtMnErTzBJekxUTlFHUUI4bWZQRjBiWTdxcFBjZGpPb2pZOXoiLCJtYWMiOiIwZTExMDVlYTRiOGI0MTg1OGFmYjc4OWRlNGI1OTY4YTQ4NzY0YThmZWE5NThhNTIwMzc1ZDkxOTJiNmE2MjIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">K7EIYCLnalanTBUASCKMEOK1yUmooVr7ha2cCSMP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-476521611 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijl6bkpCVmlpM0JGcCtISmhERk91Nmc9PSIsInZhbHVlIjoiQzZHbkgvdTFPUkJrZnR6T3JocmVSWkJvMnMza2Y4TnBvbm1yTGpCNk91L1NmOGxWbkQ3TUJtTHBtZWxMbzJINDNMS0JZajQyMElTbEl0N2pRM3d2bDRHZ3lsMDFDVnYzV2ZORDdtdUErcGd2ZnRuaG42UUZIdFlsbFovbXhNT1RyUm5uSDAxWk5ZSWZDU1NRb2JicnRnYnlvN1QxL2d5dlkyZXpKUXFnSnRrVHhlVWxGV1B2ZWRjUSs4bktTd1haQ0d5ODBtTVZDUEMzZFZDYVZQQkZSa29SRXFhN2pDcDBEMEN6RDlvWGtianlkb081dlJtcmltQzlRUExYcCtndE03MTFTSnE3ZHoxZFZFZjdhV0FqcFVDRkZaN0ZFTUJxYzUwQ09tKzlYbTV0OGlUeE1wSmJkWDVydjRYL3huTXFzTkFSOTJpNDUvYkFxNzZhUTY0eEF5emdhd2J6aFAxZU9Ka3U2SzM0ZzlDVjhuaWRyYW56VXBGbkc5UDhEZnQ5V1ZyaDZseUpMdmRZSWtISmZpWk5HN2x0d0ZwK1hzdndORlMyenpoUXFGUDdtQk5sMTh2Qjl3SDFNWXE4NWpLTmNOQWUvNzhnT0JqMWVzWDM2QVlVc3dtd3lHUG9DR2VCby9TdEtKTGtScUtXM2t0SS9DZ2VXR0pJK2FITHAxY2ciLCJtYWMiOiI4N2YzZGE2ZmMwNmM4ZDUzYzk3YzM5ZjU0Y2QwZjU3MDdkODMwNGNlMzFlOTZlMTI3NTcwYTNmZmY3MTJhOTU2IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InpzbCtoQWFTbU1SNDdXWDR4Yk5WMkE9PSIsInZhbHVlIjoiRmZ6aUhxMnJsNmRTZUR0bWg2VFBDTmswNU15bzgwZ21BTUhwQlY3VDJabmZydlEvWEtkc1JublBzWlFSM3NROUowT2Zrc0t6aGhIU1EwY3JVYzVzSGs5UCtRWWtoQkpyOHd6OWE0U24xZGRHcWtRV0gzbXBVay82WVlUZTZTTHVqQnZrb2VzMmtJWllidDBtL2lyZ1NFdU1GM2hGRmNGbGZQMHh0bElSTUd0N3BmRGtFMXpkNkgxUHF6VjZlZWJwWjlKcEFUM3d0YXVIYU9JeUVONlBZNURHWnVKTm1TVUR6dXk1MFV2TlpxMFlyL0lQSHBZNmdoME9qNWtNQjJuaE1RZS9Ua29hT3pwV2JDTm91d0VsQWMvRjV0VW9ENEtLN1M0d25OQUhQQXNBZnRtOUV0bEJvd3QzVkJrcWl1Y3FnaXZvTmRTOU95L1NSZkxLMWFZR2VpNGxVM2tsbUMxaFpRcWFKRjNIVHdZNzh2WnIxOFc5dmJ3K0xwWE5wUjBhR2RHYk5Da29mcHZ6WDZQbW5tZ3FDQUJJbURTcWhqVllqM0JzVkxwNFpRMlk4NDAyN3BhKzE3Vm1tS1lmbEhvSUpMeEE4eWx3UVI1eGRHRWNFblNYa0RGRERXNWpHVHFVRG1xTTVabHBCV0xiVm1sZFNtb0ZjMGlJcDVaUDlheWoiLCJtYWMiOiI0NDFmODQxYTdmMGUyNGI5NTU4YjBlOTA3MGRiNGMzNTEwNTlkNWUzYTIxYmI2NGY0M2RiMDhmMTJjMWU1NGRhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijl6bkpCVmlpM0JGcCtISmhERk91Nmc9PSIsInZhbHVlIjoiQzZHbkgvdTFPUkJrZnR6T3JocmVSWkJvMnMza2Y4TnBvbm1yTGpCNk91L1NmOGxWbkQ3TUJtTHBtZWxMbzJINDNMS0JZajQyMElTbEl0N2pRM3d2bDRHZ3lsMDFDVnYzV2ZORDdtdUErcGd2ZnRuaG42UUZIdFlsbFovbXhNT1RyUm5uSDAxWk5ZSWZDU1NRb2JicnRnYnlvN1QxL2d5dlkyZXpKUXFnSnRrVHhlVWxGV1B2ZWRjUSs4bktTd1haQ0d5ODBtTVZDUEMzZFZDYVZQQkZSa29SRXFhN2pDcDBEMEN6RDlvWGtianlkb081dlJtcmltQzlRUExYcCtndE03MTFTSnE3ZHoxZFZFZjdhV0FqcFVDRkZaN0ZFTUJxYzUwQ09tKzlYbTV0OGlUeE1wSmJkWDVydjRYL3huTXFzTkFSOTJpNDUvYkFxNzZhUTY0eEF5emdhd2J6aFAxZU9Ka3U2SzM0ZzlDVjhuaWRyYW56VXBGbkc5UDhEZnQ5V1ZyaDZseUpMdmRZSWtISmZpWk5HN2x0d0ZwK1hzdndORlMyenpoUXFGUDdtQk5sMTh2Qjl3SDFNWXE4NWpLTmNOQWUvNzhnT0JqMWVzWDM2QVlVc3dtd3lHUG9DR2VCby9TdEtKTGtScUtXM2t0SS9DZ2VXR0pJK2FITHAxY2ciLCJtYWMiOiI4N2YzZGE2ZmMwNmM4ZDUzYzk3YzM5ZjU0Y2QwZjU3MDdkODMwNGNlMzFlOTZlMTI3NTcwYTNmZmY3MTJhOTU2IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InpzbCtoQWFTbU1SNDdXWDR4Yk5WMkE9PSIsInZhbHVlIjoiRmZ6aUhxMnJsNmRTZUR0bWg2VFBDTmswNU15bzgwZ21BTUhwQlY3VDJabmZydlEvWEtkc1JublBzWlFSM3NROUowT2Zrc0t6aGhIU1EwY3JVYzVzSGs5UCtRWWtoQkpyOHd6OWE0U24xZGRHcWtRV0gzbXBVay82WVlUZTZTTHVqQnZrb2VzMmtJWllidDBtL2lyZ1NFdU1GM2hGRmNGbGZQMHh0bElSTUd0N3BmRGtFMXpkNkgxUHF6VjZlZWJwWjlKcEFUM3d0YXVIYU9JeUVONlBZNURHWnVKTm1TVUR6dXk1MFV2TlpxMFlyL0lQSHBZNmdoME9qNWtNQjJuaE1RZS9Ua29hT3pwV2JDTm91d0VsQWMvRjV0VW9ENEtLN1M0d25OQUhQQXNBZnRtOUV0bEJvd3QzVkJrcWl1Y3FnaXZvTmRTOU95L1NSZkxLMWFZR2VpNGxVM2tsbUMxaFpRcWFKRjNIVHdZNzh2WnIxOFc5dmJ3K0xwWE5wUjBhR2RHYk5Da29mcHZ6WDZQbW5tZ3FDQUJJbURTcWhqVllqM0JzVkxwNFpRMlk4NDAyN3BhKzE3Vm1tS1lmbEhvSUpMeEE4eWx3UVI1eGRHRWNFblNYa0RGRERXNWpHVHFVRG1xTTVabHBCV0xiVm1sZFNtb0ZjMGlJcDVaUDlheWoiLCJtYWMiOiI0NDFmODQxYTdmMGUyNGI5NTU4YjBlOTA3MGRiNGMzNTEwNTlkNWUzYTIxYmI2NGY0M2RiMDhmMTJjMWU1NGRhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-476521611\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-378222452 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-378222452\", {\"maxDepth\":0})</script>\n"}}