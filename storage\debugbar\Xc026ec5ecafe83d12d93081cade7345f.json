{"__meta": {"id": "Xc026ec5ecafe83d12d93081cade7345f", "datetime": "2025-06-30 22:36:59", "utime": **********.571841, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.066362, "end": **********.571863, "duration": 0.5055010318756104, "duration_str": "506ms", "measures": [{"label": "Booting", "start": **********.066362, "relative_start": 0, "end": **********.477556, "relative_end": **********.477556, "duration": 0.4111940860748291, "duration_str": "411ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.477565, "relative_start": 0.41120314598083496, "end": **********.571865, "relative_end": 2.1457672119140625e-06, "duration": 0.0943000316619873, "duration_str": "94.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45045488, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.023880000000000002, "accumulated_duration_str": "23.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5101352, "duration": 0.02084, "duration_str": "20.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 87.27}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.543113, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 87.27, "width_percent": 3.727}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5541892, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 90.997, "width_percent": 4.146}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.561321, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.142, "width_percent": 4.858}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-608192783 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-608192783\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1917138653 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1917138653\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1105053853 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1105053853\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2026763730 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751322886293%7C8%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhkT1dIQ0VlZG91bUd4MWZHbDhSREE9PSIsInZhbHVlIjoicU1sMVoyMldPUjczQU1CQTVEZXdXSkdiZWJMbG5HN3U1VDhGRjFheUdhL3hrcXUxazZzbnVSSmMrdGxOM2ZXd2N6ZUdIYnJNbmdOQTdxRUZOUm5xaWhwSE9pQjlwVURjTHZ4anpGVU5QSWxxcU1JTGl3YkJVcUNXOExBTXZacXhMaE5hc2Z2Vm9yY0NDTGdCellaQWJPQyt6azAwcW5veGJ5aUd4OGk5NzkxUVFxYmFxcDRlUjVvd29TUkxsME5mWUlDb3ZPWXBKaXovOVFXb3d4eFdQRFlBTkxZLytnUEppQXFjWkZObElUQjI3SUZXMWMrTldrZEg5S1lPcGpFTmxsaWd2UnpJdEFtZm5aMVRPTmYrcm92R2syMGg3TWlXOTUyS0pHakxxcnV2WDU1N2NkZnNUUW42MXRuMnNjOXJNTitIdTE5eTkwQnJBRi9NM1BqSnVNT2Z3d25nb0pVQXRzN0RPZDZxb29CMGFLY0NTZUw5L3hneStWbkUyb1RzR002NDIrSXhwc015bVhRNlFOdFBZVm04Wm5ESldiVENUbi9jTVg3dGRjeUVuNm5wKzg3c2JMbGxobUJOOEluSDM4ZTkxci9jSit2a0R0UklTQVoxWTZUaFlWSnRHWEkzUm5lbDZVQmlyK0hJbTQwZmZYaFdLaUlXNHVqa3g5aXoiLCJtYWMiOiJiODc2ZTE1YzMwYzFmZmYzMTFiMzNkZjAxZDBhZTAwZTk2Y2U4MDdmOGE4ZjA1ZmMzYzkxMzlmODViM2Q0ZWE5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InBGaW5kbkVoT1dmY0tVdVduVm9Ea3c9PSIsInZhbHVlIjoiZW13YzNMVUF3NFJ3alM4S0xnTTNuRWNjRlIzVTMyd2dQVmlUZ3JLYlNMMnlVY2dLb282aHFDK2ZOeW0vSUs0K2xoUzdITWhOOE16dHRxb25qMFJFNlhheWxHeFRWbnY5dnhycy9XL21PUGFXQ25RN2lzMGZuWnRIMEx0YlNZdjRxM3Z5ZzUvZzRtQ2NQcjlubU5ucDI3U0ZBWnFZejlxdFhzc1d0QUE0ZEtlamRDS0xmTWpsTG85N3NMaEV4REtCOHV6RjZWa3FwRVNab3BjRG9vVTkxdWw5bzE0SGdpVm9sbUJ3elZXY21MSXZUSHViL2g3UlU5SCt6S3JXNFoyVTlrTSt4bG9MOWplMGk5Q0tQNHpUY0J2RkdKakpxMS9XQ0N6a2FUTU01U1RYK09lSW5qbmlyV05nVy93MDJUZnRsMUxHQU5rZE1CMWFxZHdSY0Y2Z3pCWWNBYTFuQWdaWVVjSW5Na24yQldFTnpVek1NaUtXNGpjcTFhaW5SM1dlTjlpeHJaanNIcHNWMkt5a1ZsWGVxQkFQT2dBVjJVZmFVYzZNWkdteWVoOE1hbmNHUG53bUFFOThFbUJnOGZqUWpjTHNMUEh6Q2pFTTdkbkZBeGJhTXYveERGYmNwa09CaTU4dzV6b0lYelBURXVIRUpHUVhTKytNZ21aWHo4VUsiLCJtYWMiOiJkZDE5YTI4MGRjOTA4MGI1YjNjYmRhMGQ2N2NhOTYyMmVkODkzMzJkNjIyOWRmYzNlYTcxOWE2ZDg2MzYwMGQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2026763730\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1692033175 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">K7EIYCLnalanTBUASCKMEOK1yUmooVr7ha2cCSMP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1692033175\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-804928784 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:36:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVTUXZoVllkU1B5Q1RQUVBtazkrZVE9PSIsInZhbHVlIjoiSDFFbjZSNnFqd2VzYjNvbzhZYW1Qb1ZCSWJzYkVQdUdsRmpkbFZBYUdHRExudkR2R0taNHcxeWVtQXM2NWsxMmIyQWZQbjUwS2hkY1g4RWY2dFIwNnA2UUU2SjJmaTRSWGFxQ2UydVBaNnRVd0s1NXdmQTFheE9aMDdFYUVoU1VlcjU4dUtyMVpQUUROd1VLVzlvYUF1dHdIMzhNRm9SSUJOMnhHZjZvMXdmdzUreDh4SG1yQ3FmUnZCdFJhT3VRbWtHNHg1STAvR1VBdlIwSm9ZS0hSRnNKcDRXNHFJY1lkSE9RRUdnby9VaWthcldYeUw1a09ZRzJmRWVuN3I3WE55aFZtM2NRR3pJTmlGcmdoUzZUL2lEMEhXU2NIcVFzOURGWU9hankxR2FGeHNzak4vT0w1UTBJUURhdktxMi9vYjNsM1g0MVE1cCtFc0M0YWtvUHVKV0RBYXdBL3R3L0FWbmZici9QalFhdVE0VU1vallZNWQxcmFSYWlYQ0d2Vml6SkxOdmlhWWRUMVB2SDgxdllaam5iZURDZ251ekloSWxCR0htS0NjU21FTE9NRXI0QUpURWVvOUZaM0h4NlpvblZkbHpDVWJaemRoNUlKbEZSTzhueVR1UHpxZUJ5NkRDR1NWN1ZRSFgxTWNLQkRXaFB6S2FkMXJoVjdnM2QiLCJtYWMiOiI0OWEwY2E5NWQwYmJlZDUwMjM3MjNmNjAxMTQzNjBlZWYzYzg1ZDNhYTQyOTA3YThhMGRlNzBjMDVkNDQwODg4IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im5UeTlmUXViSGdQUjhZZ2lKM1hmN3c9PSIsInZhbHVlIjoiQkl5OGtKRTFKZUh5dWV6elVLM0pXUWhiWXFKc2plcjR4NkNlditlRmdzR3Z5bTB1RVNlV1Zoc0YrNXczYkEvSlRNbVZscmJKWWZSU2NzOHh2elNMN2MyS1pKcVUrVEJkU3dyMGF5M1lodzgvOVZubjZjKzIyTnhKbjh6U0VhSG93akp2MVpHNXl1UW0yL3EzRFVIYXVRL1owbDBLWFU2UHJwUXdMaFE1Z3VaK0ZjYlRJQXY0MjNLK09DRWtEdHBLcW9teFVLTW85ejdPVWorbWtkMXZhanFFeWZZV3JMOSs2Q28vR2hLZDI5VVZkeVZ2RG01VG5wS2hBakdIQ0ZBYVFIbFNKYStqam1CTXdLTWN0TXNHUlpGMjZTWEx6UEZleGJ0Q2Nwai9aSzNrK1BQY0xmZFdMRXVYV1I3dnBiRC9jYjgva3Z1L21EQml5NDBFaXNPbHl4TnNJVVJQeTZPNGlPZXRTckk4c0ttcVYwa29FSW1DVGxaUjIwVjNzS1NNRmkzZG1RellZTVVwdHJxWGVEQW1nYVUzMytDMDlkSjQ2VzllMmVlN215dzRrTCtjOGhOMG9tRUhqdXpZY05CZGxqK2FUYWlzWWc4QnJzQi9qdW51eURieGlrNTlyc3JyMWxkQWRMR3NoODNtME5tdERWVC85YVJLRVR1UmpXL3EiLCJtYWMiOiJhOGFlYWUzNGE5YTMxN2YwZTE1ZDQ1NTg1YzlmOWU5NGExYTQ3ZjNmYTlkMTNjZjI3MDAzZDhlNWJhNjI4YjVkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVTUXZoVllkU1B5Q1RQUVBtazkrZVE9PSIsInZhbHVlIjoiSDFFbjZSNnFqd2VzYjNvbzhZYW1Qb1ZCSWJzYkVQdUdsRmpkbFZBYUdHRExudkR2R0taNHcxeWVtQXM2NWsxMmIyQWZQbjUwS2hkY1g4RWY2dFIwNnA2UUU2SjJmaTRSWGFxQ2UydVBaNnRVd0s1NXdmQTFheE9aMDdFYUVoU1VlcjU4dUtyMVpQUUROd1VLVzlvYUF1dHdIMzhNRm9SSUJOMnhHZjZvMXdmdzUreDh4SG1yQ3FmUnZCdFJhT3VRbWtHNHg1STAvR1VBdlIwSm9ZS0hSRnNKcDRXNHFJY1lkSE9RRUdnby9VaWthcldYeUw1a09ZRzJmRWVuN3I3WE55aFZtM2NRR3pJTmlGcmdoUzZUL2lEMEhXU2NIcVFzOURGWU9hankxR2FGeHNzak4vT0w1UTBJUURhdktxMi9vYjNsM1g0MVE1cCtFc0M0YWtvUHVKV0RBYXdBL3R3L0FWbmZici9QalFhdVE0VU1vallZNWQxcmFSYWlYQ0d2Vml6SkxOdmlhWWRUMVB2SDgxdllaam5iZURDZ251ekloSWxCR0htS0NjU21FTE9NRXI0QUpURWVvOUZaM0h4NlpvblZkbHpDVWJaemRoNUlKbEZSTzhueVR1UHpxZUJ5NkRDR1NWN1ZRSFgxTWNLQkRXaFB6S2FkMXJoVjdnM2QiLCJtYWMiOiI0OWEwY2E5NWQwYmJlZDUwMjM3MjNmNjAxMTQzNjBlZWYzYzg1ZDNhYTQyOTA3YThhMGRlNzBjMDVkNDQwODg4IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im5UeTlmUXViSGdQUjhZZ2lKM1hmN3c9PSIsInZhbHVlIjoiQkl5OGtKRTFKZUh5dWV6elVLM0pXUWhiWXFKc2plcjR4NkNlditlRmdzR3Z5bTB1RVNlV1Zoc0YrNXczYkEvSlRNbVZscmJKWWZSU2NzOHh2elNMN2MyS1pKcVUrVEJkU3dyMGF5M1lodzgvOVZubjZjKzIyTnhKbjh6U0VhSG93akp2MVpHNXl1UW0yL3EzRFVIYXVRL1owbDBLWFU2UHJwUXdMaFE1Z3VaK0ZjYlRJQXY0MjNLK09DRWtEdHBLcW9teFVLTW85ejdPVWorbWtkMXZhanFFeWZZV3JMOSs2Q28vR2hLZDI5VVZkeVZ2RG01VG5wS2hBakdIQ0ZBYVFIbFNKYStqam1CTXdLTWN0TXNHUlpGMjZTWEx6UEZleGJ0Q2Nwai9aSzNrK1BQY0xmZFdMRXVYV1I3dnBiRC9jYjgva3Z1L21EQml5NDBFaXNPbHl4TnNJVVJQeTZPNGlPZXRTckk4c0ttcVYwa29FSW1DVGxaUjIwVjNzS1NNRmkzZG1RellZTVVwdHJxWGVEQW1nYVUzMytDMDlkSjQ2VzllMmVlN215dzRrTCtjOGhOMG9tRUhqdXpZY05CZGxqK2FUYWlzWWc4QnJzQi9qdW51eURieGlrNTlyc3JyMWxkQWRMR3NoODNtME5tdERWVC85YVJLRVR1UmpXL3EiLCJtYWMiOiJhOGFlYWUzNGE5YTMxN2YwZTE1ZDQ1NTg1YzlmOWU5NGExYTQ3ZjNmYTlkMTNjZjI3MDAzZDhlNWJhNjI4YjVkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-804928784\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1134474680 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1134474680\", {\"maxDepth\":0})</script>\n"}}