{"__meta": {"id": "X43fbef5f5897820ecd5c9248c3fcb62e", "datetime": "2025-06-30 22:39:31", "utime": **********.534909, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.043678, "end": **********.534929, "duration": 0.49125099182128906, "duration_str": "491ms", "measures": [{"label": "Booting", "start": **********.043678, "relative_start": 0, "end": **********.455104, "relative_end": **********.455104, "duration": 0.4114260673522949, "duration_str": "411ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.455115, "relative_start": 0.4114370346069336, "end": **********.534931, "relative_end": 1.9073486328125e-06, "duration": 0.07981586456298828, "duration_str": "79.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45062120, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.014580000000000003, "accumulated_duration_str": "14.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.487062, "duration": 0.01255, "duration_str": "12.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 86.077}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5083022, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 86.077, "width_percent": 3.086}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5175529, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 89.163, "width_percent": 6.31}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5243862, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.473, "width_percent": 4.527}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-386429249 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-386429249\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-570300479 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-570300479\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1395104229 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1395104229\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-385076352 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751323019449%7C9%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjF5b2NhYkEvSWdYaUhsWEw4WjJ5RHc9PSIsInZhbHVlIjoiUGYyR0c1aUE2V212TDI4THJjWERsTnRuaW9CRkdHQm90dmVZQmFTaUJBS1pDQWo5VGk3emN2dHBvNW5KSlV0SzQwekJOaWdsbkd4QUpBaVM1K3ROdDJtUlFFcDNvckdGUnU5c0VYMVp6bFpsOXRZRHN0cUIrOURLZnBIbnJnK0hsVkI2dzJ6cTA5N0xvVGIrR3FrdlNtdy9XUnlGK1B3d01WVUcrWi9UREo5Ukd5RVg2Z0h5dHQwYlEvb013VzBwak01SmhPTlFYSnluM2pYVnpmZjY1WkxuQlVRYVJDNVlIRU1JMWZNbUNxREZCM09HUmJ1MUxTMmdpcmFUR2tMYzhSN0tHdmFaNGdmNkdKOWl3VE55M0VGODlCUEM3MkV6TGxKTlg5dWkrV0ozdjA4Yk91T2VvdmRtTTQ3amllVkVvUk96bmdmaUVmbE4zZTkwVm1EY0dEbkhna2VsTXhtRHoxdVFqNmdFUjhaM2RvMVM0RlVnN1YvSSs0N0RISUdJVitCR3h1VE82aDd2aDZjVGpRQ3FMZXJoM3QzSE5YWURYOS80VTBaNWw4N3hQc0VFYUdUd2NUbWVIMkVoem9jc1lQQ21sS3BmNGNTRzRpQVN1bGpMMWk5cXlqTWJlWndEZkJtRkZXaFYzQysrZmtzQWREaEw2K1JiaDZ1c0wrV3MiLCJtYWMiOiIzZGI4ZmVlZTliNjM0MTFmYzY4ODg2NWE5YWYxYjg2YmViYTRlODRhMmVhNDI2YzJlMjBiNTYxYjhiZDU4MWMzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik85cjlMOXJ6M2d0U0JXaUovMWpqWXc9PSIsInZhbHVlIjoibFpjdDJ0OXhCMGtMVkkyWDB1aUllaEQxY000QnIxVGh1QkdtMGlnUnY0YjBkL2FCZjlFa3RGeDNaK2YvejFqTVpPdGIxU1NXVVZRT0dRclNodmZMZVZlK0IzZTd0RG8ybDBkbWpkS1dYSXF0SUhiajFqemdGb21YMkxnUXdlV1k5VEZGcldvcTh1SVcyc3ZVREs5cmNxb0hCZ2JHNURpZXBLSmtiQ3A3SjBsK3J5SE9aZ2NIMFpYS292eFpob2dtdSt5dnVOUlJobk5OZ1FudGU2dXpzWXlKSUNQUUxjQVJTZ29zN3I5VG1RcUQzdkhIYUJuTHFKRXBZWFl3eTRnSjIxOE1ITU4xTmFvQXVFWWsvMW9ZaEJRd01VTG4wNVdoTWZISnpFMTJoWUtLMUlSNmhWa2pOWU5XalYraHBVSGhxT0ZzYWRBZWhkd3V2aHY3Y3Q5d01WVFRFekQvVGlmVUFIZisyOUNMUjZEZmdkeHAzeXJPdlgzNm9RbHkvT1V0UVpkQmlHRW4wcWlxalVYQ2M5TjFwSWFJUWJSeWVUbzU4TXphdW9QL0ZBQ1Q2SndTL0NDRld6aW9Bd050WExVYTRWbm42aE5qMEI5U0tXNlBjRXhFWThPOUNkVnZuOFFLaFFkWGNBNU9WRFl0RW9ZakNNekJjcHA4bUxpN2U0ZnciLCJtYWMiOiJjMzIyNDI1NjY1ZTgyNGRhNDA5ZWM2NjM2YWZhYjIxN2U0Njg2NGJmMThkMjQ3Y2UzNTc0Y2E3ZWI0NmZiZDdkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-385076352\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-803609045 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">K7EIYCLnalanTBUASCKMEOK1yUmooVr7ha2cCSMP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-803609045\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBMM0VYK3VmKzNRUmsxT0JmQXZNemc9PSIsInZhbHVlIjoidERLSytNamppUncvWnRqUUhDajcxaFpTaE1ob1BVNVVJOU5hQlVqVVhnUHNBUGgvdm12UXkzZDBqa0NVbTFDelh4d2NyQ0d1WnlwSS85dXgrTW5qYWxZdnkrQXRGK3NLN2UxSGNoblZyYngrbEs0QWdzM0t1OFUvVXplQUZGOE9aenhvMmpqRkRVbHNhR3Y1UXZQZGlyVHAwZVFXVnRCdENPREpzTDRMc2d5WEdMZk5lSWRBTDJDZlE3Z1NqNDZLYkEyb1F6OGFPTGJaRmNhRlJYcXlEeXdObm9jVysvSFp6L3R6Rll2MUxCSnJzS3JpVENVQkJTNlRuUDVGcUxRVEh1QUFxdU8rOEdYdUNReEZWY1BzdFFleHJ2QkgyRGxWdUkyVjV2RVNrazdmaEdWSnRqVVRQNHFTcTNrc09Nb09SUXh4amRjdFQ5S0x5dVkvdHJkT2ZDaE9NZFlmMGRpRFhWZkFYUmdUZ0s5QWtwelEzOUdhcnB2c1FkRnZyZmR1a205d3BsNlhMcGVhL1BHKzJtN0Y5bEl3OUxMYURzOGtyQnlOd29KRVdxQnhUYng5ZlBVV3hCVTgxejN4SmNOU3JiQ2tYN3JMcE02cXdoT1grbk5MT2hmcnhSVmE2ZFI3WldHMlBGL0I2d20xaEc3b09HRDhHanp0NmxVSDFPWnoiLCJtYWMiOiJjOWRhOTViNTZiZjllNGUyNjdhMmQ0MDE1MjI5ZDMxZDQ0OTc2NzFiMjQyNWRjMDdkNmMyOGFhMjk4ZDc4ZGE5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InNycEpHWXJ4OFB4NTdzTWlMTWxURWc9PSIsInZhbHVlIjoiZkZXQk9pSlg4aWlSZTMzYWkwM0hVVFJpT2pnOS9udkxqODBYQlZ4NlRwd1dmckhXVHpmN1dyenpteG56dFhRbWYwVUU2Z1phSnI3bFdWdGFxWVI5NG9iYWJqMXdlTTVSUXV0MFM5ZDArZkp6T1ZodlZtZXh3OFp2TVhNQnphOW9sQkJHSTZ0RGpYa0p5OU1sSFJPY2dCc2U5M2U4T084U3lnL0h2T2lDZzJQcm1nbWFpdjhpNVBaZ0xWMU1jLzNwQ2pnT015cHJmclNHY2JUK2NBSmM0Qk4vRjZRQXJCeTRmR05JSG8vQmJCNVQ2N0ZlZnlhV21KVmppdjRvYjA5NkdNM2ZPMjczL29VY3luUGFyVmFTNlJ0OXQ0clZIa1p1dEpGSHB5QVNZVkh6NEFnYW93dFdZcGJlRm55aWgraHRhNTRLdHRjZmVrMStLYlJtV1gxOWpvdWx6VzVobzlSNnVRT1lRVGlaRVp3cWl3VWcveDlvRk03NFp1YjB1aytxZDVvWjlxTHE5TVkxbjRocHY0S2N1RFhxaCtNOUhwT09MVnZFUnNpTmtQMXBrREk3Qk5Oa0l3Ly9zK3F3UmxnRUdmNkNpcWpFaHRIdEhsREMwZUtpQnhsbXE1ajBtMnErTzBJekxUTlFHUUI4bWZQRjBiWTdxcFBjZGpPb2pZOXoiLCJtYWMiOiIwZTExMDVlYTRiOGI0MTg1OGFmYjc4OWRlNGI1OTY4YTQ4NzY0YThmZWE5NThhNTIwMzc1ZDkxOTJiNmE2MjIxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBMM0VYK3VmKzNRUmsxT0JmQXZNemc9PSIsInZhbHVlIjoidERLSytNamppUncvWnRqUUhDajcxaFpTaE1ob1BVNVVJOU5hQlVqVVhnUHNBUGgvdm12UXkzZDBqa0NVbTFDelh4d2NyQ0d1WnlwSS85dXgrTW5qYWxZdnkrQXRGK3NLN2UxSGNoblZyYngrbEs0QWdzM0t1OFUvVXplQUZGOE9aenhvMmpqRkRVbHNhR3Y1UXZQZGlyVHAwZVFXVnRCdENPREpzTDRMc2d5WEdMZk5lSWRBTDJDZlE3Z1NqNDZLYkEyb1F6OGFPTGJaRmNhRlJYcXlEeXdObm9jVysvSFp6L3R6Rll2MUxCSnJzS3JpVENVQkJTNlRuUDVGcUxRVEh1QUFxdU8rOEdYdUNReEZWY1BzdFFleHJ2QkgyRGxWdUkyVjV2RVNrazdmaEdWSnRqVVRQNHFTcTNrc09Nb09SUXh4amRjdFQ5S0x5dVkvdHJkT2ZDaE9NZFlmMGRpRFhWZkFYUmdUZ0s5QWtwelEzOUdhcnB2c1FkRnZyZmR1a205d3BsNlhMcGVhL1BHKzJtN0Y5bEl3OUxMYURzOGtyQnlOd29KRVdxQnhUYng5ZlBVV3hCVTgxejN4SmNOU3JiQ2tYN3JMcE02cXdoT1grbk5MT2hmcnhSVmE2ZFI3WldHMlBGL0I2d20xaEc3b09HRDhHanp0NmxVSDFPWnoiLCJtYWMiOiJjOWRhOTViNTZiZjllNGUyNjdhMmQ0MDE1MjI5ZDMxZDQ0OTc2NzFiMjQyNWRjMDdkNmMyOGFhMjk4ZDc4ZGE5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InNycEpHWXJ4OFB4NTdzTWlMTWxURWc9PSIsInZhbHVlIjoiZkZXQk9pSlg4aWlSZTMzYWkwM0hVVFJpT2pnOS9udkxqODBYQlZ4NlRwd1dmckhXVHpmN1dyenpteG56dFhRbWYwVUU2Z1phSnI3bFdWdGFxWVI5NG9iYWJqMXdlTTVSUXV0MFM5ZDArZkp6T1ZodlZtZXh3OFp2TVhNQnphOW9sQkJHSTZ0RGpYa0p5OU1sSFJPY2dCc2U5M2U4T084U3lnL0h2T2lDZzJQcm1nbWFpdjhpNVBaZ0xWMU1jLzNwQ2pnT015cHJmclNHY2JUK2NBSmM0Qk4vRjZRQXJCeTRmR05JSG8vQmJCNVQ2N0ZlZnlhV21KVmppdjRvYjA5NkdNM2ZPMjczL29VY3luUGFyVmFTNlJ0OXQ0clZIa1p1dEpGSHB5QVNZVkh6NEFnYW93dFdZcGJlRm55aWgraHRhNTRLdHRjZmVrMStLYlJtV1gxOWpvdWx6VzVobzlSNnVRT1lRVGlaRVp3cWl3VWcveDlvRk03NFp1YjB1aytxZDVvWjlxTHE5TVkxbjRocHY0S2N1RFhxaCtNOUhwT09MVnZFUnNpTmtQMXBrREk3Qk5Oa0l3Ly9zK3F3UmxnRUdmNkNpcWpFaHRIdEhsREMwZUtpQnhsbXE1ajBtMnErTzBJekxUTlFHUUI4bWZQRjBiWTdxcFBjZGpPb2pZOXoiLCJtYWMiOiIwZTExMDVlYTRiOGI0MTg1OGFmYjc4OWRlNGI1OTY4YTQ4NzY0YThmZWE5NThhNTIwMzc1ZDkxOTJiNmE2MjIxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-86121567 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-86121567\", {\"maxDepth\":0})</script>\n"}}