{"__meta": {"id": "X0b430e04d849656c20a100c30bc8d1f3", "datetime": "2025-06-30 22:39:36", "utime": **********.754414, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.31514, "end": **********.754436, "duration": 0.43929600715637207, "duration_str": "439ms", "measures": [{"label": "Booting", "start": **********.31514, "relative_start": 0, "end": **********.681677, "relative_end": **********.681677, "duration": 0.36653709411621094, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.681689, "relative_start": 0.366549015045166, "end": **********.754438, "relative_end": 1.9073486328125e-06, "duration": 0.07274889945983887, "duration_str": "72.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43747544, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02689, "accumulated_duration_str": "26.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.709588, "duration": 0.0265, "duration_str": "26.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 98.55}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.739243, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "kdmkjkqknb", "start_percent": 98.55, "width_percent": 1.45}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-30346793 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-30346793\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1390728785 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1390728785\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1338200073 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1338200073\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1702255651 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751323174926%7C11%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBPNHhDN2VPekhNZTJLaW83VkZYa0E9PSIsInZhbHVlIjoiS2NVSGl0NzR4UTBYVHpjZlorRWFsRUV6UkwvR1U5b09pRGVaVXMrdTJ5MjYrSGNYazJxQ3BXNVEyc1R5ajRDYUFON3ViN2diRE1mTXJ5SFhBNTh2c3RyRHpnWW5pUXJDWEpXbFV2WTFOb0lXejljMkVtNUdEWWZCQnpLa1IrQ010cUNmaU90YTdqaTRQREhsd2tJcU9KN29pNVBWM2l1SkxtVlRQRkdPSzk1dDdvWnhrUUljd0RmM2lnK25PRXRzVkhNZ2VPalJPbkNiWWdQbWxGdjdnekRod3YvNDEzL1hBdFZNTjljME5OeVFBQk9lbFNuMEo3bGV1eSs2ME5PMkVaYk9FV2hoNW9WS0dxWUJLOFFvMFBNQ3FOcllYNFFwVzFYNnpwb0tYYlorK1k0Y1cvTUtVVEQwVnNURDN2NEN1b3MyODZ5aE5UOTJHekpFVSt5ci90MUtCSTBiTXBERG9FQlRwUzZLUmFnemxqUm1CbjlqRDNFR1JIeExJMkxBcDdTL1lxY1JRdTdTTHhLRXhOczhmNFpGd3hrU3ZzMG5nLzljSmtMS21vTjV2OXdvL3l2Q0JzQitVQUFTeDhBN1loRVNQTjcwbjI5M0h4Q0J3VHBlSmoyckVVd04vMDNoWG45NU1scFJwWmJvUG1GcXZvWTU1Sy9TT2Y3bHM1MG0iLCJtYWMiOiI4Yjk1YzM4YWNmZmY1NDhmOTg5ZTAzNmFmZWU1ZGRhZGM0ZTBiYzRkN2FmNjI4ZGIwYTE1NTY1M2I1ZmNhYTBiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im5rWnhxMHgxaytQNmRZWkNkYkV5blE9PSIsInZhbHVlIjoiM3BERGIvYytQQlowdUtCOGMyVWNpUk82R0ZLYmdWakdsQUJNR1ljaXdLRVRQMUJ4NmF5UEpMYUFjYkc5c2JUNTVsWVltWExYQzdLNzRPRGFBRHYrZUtIWnFXWm5PSzVIN2oxMFg2blVTMVRtYnc4OHhrd05tMGZEajZnSHl5QUdETlVKbFY4dnh0NHBEbTNqTGl6S0NPZDJla2p1alFMSVJ2d3h4Q3RXbU83bG9Ka2MwV1NJamF6Y05qRmhBWXVBVzhyOHNUY05KS1ZQdVFoTGtuN1JCMkRXTDhDR0hhWXhzODRwK2VEOTdDV0VibHZ1NGt1VGJDN0hEbGt0M3JYRlpLeks3WTc2N0tIRS9hK0YxNTVyMzEwS3orbmx3VXFnR042Qk9NMFhxd2ZVWHliREVTSitBTmkvR3djbkxmQmdiZ1BEcG5OLzBqVDRUR2FOK2pmSDhPN3pFQlJHWUYwTnhGMFhJd0hGa2tYUlFHWVFxQUR6ZHZyMjhjb2UyTjNzY05jdGd6enRWaEt3NjhybXZUaDc4eW1qcFdGSHpJVW93NE5PWEcxcFhNdHFxU25pUCtzWnQvQ1hRU2phN3R4SVJCaElnQlRmT2Q5MG0wVWhaSm54YVFKOWV1TTJOL3NDNzBZVU1tMWV5QzV4c09aWXpFVlVXSzVJSERGMGg4UUUiLCJtYWMiOiI1ZTBkM2NhZmNlZTA2MWNjYzg4ZmIxMzhhNzBhOTg3N2Q1MWRmNTcyZTBjYTQwYWI2ZTEzYjc5OTIxZWY2YzgxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1702255651\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1106409592 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">K7EIYCLnalanTBUASCKMEOK1yUmooVr7ha2cCSMP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1106409592\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1965895708 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFjRGwvZW9tSnpsa240NGlONWpTbnc9PSIsInZhbHVlIjoiMGw4dEE0by8wRVkrRzMxUTkycGpqaVV1NTc0enRsWFp2QWRkalJaWkZjVGxZVW93djVVT1JHL0F1amhPTzdQNnBqRHNPcktHVEJuTzd4b3dtamJVOVEyeUJ3SUxmakJvNEk0K3VJVGdzZm9MTVcyOGhoYWJWdlBRM2wrMkhTZytEd0dNaXpiZ05DeXhreWtrOGE4eUZRQjNhTWc0RHE1SlNKS3BaaURQb0Vmbml1ZEtwZGUwNUZmYUU4YnJLcXJoQWRjZ0J3eDhPOXppRXAyWDFqa1dnT3BhbHBMRGRMbTNnbFE0VG83eWtnZHhzSjdKdW1uTm5ockNZOFJWMFBTQi9TbGcyaU5RSWhYS29telkwdTBrTHQ0WEdWRmtkYndBM2ZvRmNmUmtmUTZ2R091cmM0RnBibXdUN1BaZ01GeVdrWWQrMWN6SGRTSndiYkJLR1A1K21PVTBWdEcwMzFsd01yWnpBZno2NkZ4UE9tN3c0SkpVUmdxYVVoUXdRRUg1VFdKR2F5QXhqZW4zT0dVdGs0V0FnekxNbUxDcGhRTDhoeVh6UXBlelhFWXJ3RE1POVVOVmZTMlhKVHNCcDBKYkhINktLZ09WbzYzd3QxalN3OHhYS1h0TUd5cUpKeVFTRE5BbHUyWjM2ZmRGeWpza0MyRXl4c1YzelpTL2pMcC8iLCJtYWMiOiI1YzhkYWU5ZGQ4N2UzMjJkYmIxZjUyYzQ3YWUxY2E1MmE4ZTM1Njk3ZmQ3MDQ4MzA3ZjIwMDRmOTRkY2ZiOTY0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZPWG1GVmRLWUJBSVBVWHAwZmwxMEE9PSIsInZhbHVlIjoiQ0xTNDUxSDRTejcwd2pSQnNFZ3ZscHo1V3lZeGhOeTNRK3pKVTk1UEJ1bm5Ockw3TUlUV2hsTFdRUkNjYTA0eWFCN2p4a1MyeXljWFRla1ZsQWwzU0NnV2JBT3dVcHAwa1VXNHUvUzJlN2FkTk1nNS9JVGUyQURwb2V6K25xUzg4QzdVM1VERElaZm50Ym9MZ2VLbnkyV2Z4WnFxajd2cUpicTRPMkZzSnVFMEZmUmRYaFJLUUIvSDRNSWZqNU1HWU1EL2E5ME1NVUJhS3ViR1RsQUx5aXo0Yi9YeGduQ3JTeUlIMjEzaXkwc2t0d2xVMGMwY3lkOWwxdmNPRE9DN2dqQ0k0VlI4YkNsWWpEd0MrNmZBUkZzeFJlWUNpaThjM2txNVRCWUNkMEhXNmFHVm1xbTZwZmJRNWRMSlhWaldZdTZXeEtSZTk5VHQwZVBJL2dlalo0RmpTZWhWdHhqSXdoVlMyRFpCc2Nnc2QvQ0tRSUdPSWRGdEVITG9UWW1vNVRuUDAvT2F3aVhjTXlxK0pnNGU1N3J6WUlUSURmekQrd1JvUGpYQXRzalg2SGdic01WSXM3czllNGVpTFhWaEdNRU44cE9ITzlvcWhIN0pDMTgvT3o1clE3MEpBWG5OdTlicEF6MjhteXlXOUpxRzEzMnNpc1MySEI0NlV4K0siLCJtYWMiOiI3NGIyMWVjOGEzNDE4MThkYmYyYjBlNzA0NDBmM2U5MDdmMTllNjYyNmZhMDgzNDU0YjhmYzMyZGE1ZjFjZmJkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFjRGwvZW9tSnpsa240NGlONWpTbnc9PSIsInZhbHVlIjoiMGw4dEE0by8wRVkrRzMxUTkycGpqaVV1NTc0enRsWFp2QWRkalJaWkZjVGxZVW93djVVT1JHL0F1amhPTzdQNnBqRHNPcktHVEJuTzd4b3dtamJVOVEyeUJ3SUxmakJvNEk0K3VJVGdzZm9MTVcyOGhoYWJWdlBRM2wrMkhTZytEd0dNaXpiZ05DeXhreWtrOGE4eUZRQjNhTWc0RHE1SlNKS3BaaURQb0Vmbml1ZEtwZGUwNUZmYUU4YnJLcXJoQWRjZ0J3eDhPOXppRXAyWDFqa1dnT3BhbHBMRGRMbTNnbFE0VG83eWtnZHhzSjdKdW1uTm5ockNZOFJWMFBTQi9TbGcyaU5RSWhYS29telkwdTBrTHQ0WEdWRmtkYndBM2ZvRmNmUmtmUTZ2R091cmM0RnBibXdUN1BaZ01GeVdrWWQrMWN6SGRTSndiYkJLR1A1K21PVTBWdEcwMzFsd01yWnpBZno2NkZ4UE9tN3c0SkpVUmdxYVVoUXdRRUg1VFdKR2F5QXhqZW4zT0dVdGs0V0FnekxNbUxDcGhRTDhoeVh6UXBlelhFWXJ3RE1POVVOVmZTMlhKVHNCcDBKYkhINktLZ09WbzYzd3QxalN3OHhYS1h0TUd5cUpKeVFTRE5BbHUyWjM2ZmRGeWpza0MyRXl4c1YzelpTL2pMcC8iLCJtYWMiOiI1YzhkYWU5ZGQ4N2UzMjJkYmIxZjUyYzQ3YWUxY2E1MmE4ZTM1Njk3ZmQ3MDQ4MzA3ZjIwMDRmOTRkY2ZiOTY0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZPWG1GVmRLWUJBSVBVWHAwZmwxMEE9PSIsInZhbHVlIjoiQ0xTNDUxSDRTejcwd2pSQnNFZ3ZscHo1V3lZeGhOeTNRK3pKVTk1UEJ1bm5Ockw3TUlUV2hsTFdRUkNjYTA0eWFCN2p4a1MyeXljWFRla1ZsQWwzU0NnV2JBT3dVcHAwa1VXNHUvUzJlN2FkTk1nNS9JVGUyQURwb2V6K25xUzg4QzdVM1VERElaZm50Ym9MZ2VLbnkyV2Z4WnFxajd2cUpicTRPMkZzSnVFMEZmUmRYaFJLUUIvSDRNSWZqNU1HWU1EL2E5ME1NVUJhS3ViR1RsQUx5aXo0Yi9YeGduQ3JTeUlIMjEzaXkwc2t0d2xVMGMwY3lkOWwxdmNPRE9DN2dqQ0k0VlI4YkNsWWpEd0MrNmZBUkZzeFJlWUNpaThjM2txNVRCWUNkMEhXNmFHVm1xbTZwZmJRNWRMSlhWaldZdTZXeEtSZTk5VHQwZVBJL2dlalo0RmpTZWhWdHhqSXdoVlMyRFpCc2Nnc2QvQ0tRSUdPSWRGdEVITG9UWW1vNVRuUDAvT2F3aVhjTXlxK0pnNGU1N3J6WUlUSURmekQrd1JvUGpYQXRzalg2SGdic01WSXM3czllNGVpTFhWaEdNRU44cE9ITzlvcWhIN0pDMTgvT3o1clE3MEpBWG5OdTlicEF6MjhteXlXOUpxRzEzMnNpc1MySEI0NlV4K0siLCJtYWMiOiI3NGIyMWVjOGEzNDE4MThkYmYyYjBlNzA0NDBmM2U5MDdmMTllNjYyNmZhMDgzNDU0YjhmYzMyZGE1ZjFjZmJkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1965895708\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1275479372 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1275479372\", {\"maxDepth\":0})</script>\n"}}