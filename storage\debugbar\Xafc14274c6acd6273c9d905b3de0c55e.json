{"__meta": {"id": "Xafc14274c6acd6273c9d905b3de0c55e", "datetime": "2025-06-30 23:11:57", "utime": **********.396145, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751325116.909614, "end": **********.396162, "duration": 0.48654794692993164, "duration_str": "487ms", "measures": [{"label": "Booting", "start": 1751325116.909614, "relative_start": 0, "end": **********.325106, "relative_end": **********.325106, "duration": 0.41549181938171387, "duration_str": "415ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.325115, "relative_start": 0.4155008792877197, "end": **********.396163, "relative_end": 9.5367431640625e-07, "duration": 0.07104802131652832, "duration_str": "71.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45026456, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.003, "accumulated_duration_str": "3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.368442, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.667}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.380235, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.667, "width_percent": 18.667}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.388161, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.333, "width_percent": 17.667}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-159693965 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-159693965\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1712598919 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1712598919\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-370159724 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-370159724\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-982419655 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325102720%7C19%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBTbzFxZW5oT0RyV1NXaXNvMStNc1E9PSIsInZhbHVlIjoiemVPWTZ1L016T2JDTi9zanJ2bDJmTmZJVXhYQUc5S3A1TFI4UUFrQ0pQMG9rak1lVmNYODlIdld2QkZDTDNQZjhyR0dEQ3QzTWhLUFB1TlBDUHV6bmpXUzVtNFdOU1dpc01vb2EwZ3VqUlNlMXFTM3BRSDNiQXZ3ZWovN3Bsd1pQb1JPVkhGMytzSkU1SWxQL00zamRyQWpKSHlJRXQzaStBUzl0bU1ua2pHSHNnbVo0Z1p2bnFGL0k3SEd0T3BQY3FEZGF5dUNmVVRZRmdVeWI0cUtxUGE0NC8xbGpYTUE5c2xjRFhOSUZrbmNaQ3JIeUJ5bXo0aXIyeXRkY3IzajMwWi90SkxTWWJqOStMaFlFdXMwTjVDU3h1RTJKaFNKdnoxRTREZWxUTzI5ZmNsc0d3V2VLVjJxTU43MUpqMXpQTm1mc3c1Ykp3ZmZEd09WQmoyZFByWE9SYU9uTXB1ejJmT2cwQW82dFlHRHN3bTBvQU1VMkJ0T0JnNVB0RUpxOXliNDhVaC9WRWlwbldCTnV0d0tXaXg1M3pXZHZaTzlzV0hmUzFsU2h4WUl4c1BHb3JZTkkzZWJaZzBURUtJWkdKNTBQMVNaeEtwZnA0TGZYbjRLalRDMllXdmFjNWdWY3pJWURXSkNvaVM2TExXZkZZaHJvUVE4blkvbnFuaGIiLCJtYWMiOiIwZGRmZDVjNjg5NDNiOGQ2YzQ3M2E0Yjk0NTMyYTQ1OTZiNzhmYjBhNTIwNzMxNGNmODg2NTBlNzljZjliZmViIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImpMVGIrYTdOZXlFbkRCaDVXRllwdHc9PSIsInZhbHVlIjoibG9wRjExWDRWS1h4WnU3UTZrT2FlQTNPd2QrRy9ta09yUlV3N1FMQnVkbTJvY2luaEpIRVV4dWZxbnhIODJqY0FiQXYvMWlVaHRENVhmYnlXclc0QW1zOFRoR2RKc1pFVWFyVnFaR256N2EvWE1PZ2p2WXNIZ0FDd2o4ZXg1ZExUUk1mMzMvS0xPMUYrZW1pTHZSRGk3UHdJZmFzK0VDck5OdFJXTDRIOGIxZ3ZCaVBpaFQ1NFRXVU1BVHlBeEM0dVZIalR6ejQ0dGxvN1lYZXpjTGJhQzR1R0o1aVpRNVpkMUlPYkVPYW9zcDVNc294OFIyRm1DSWtkSStlNi9ua3JHdytmMWJLbkR3WXREQVc2WUptL3pDeEhzeENzcHE4a2dVSWo5WnY1Zjd1RzFsdTlxNzdGZnhTOEl5SXYvS1VvWlBBNCthK2NjK08xQWhHT2FPQmdYVXpEWllmNk9yczVPZkU0QWdzYUEvbUZ5ZjNZeWpMb0tTOFZlVGRaU1dvVEYwN0hOeGhMOVdjTjZMZ0FDSFBLMmhRaGROV1dzS3JBVFdINUE1ZkFjb1BpTjlVVXVqV0NaS294cGhBVTZoZ216ZnEwdjZOK0VGUlM4czhyWm1PMmduTVQ0WFczTXY2cVFoVTZpb0UwSnZLWWNQR3VObXEydkxKMDJreVRtTmEiLCJtYWMiOiI4ZDkwYzkyMzA3MTA3ZTdhMWNhZGYwZDhmNzc3YWI2MTFhMmVlZDM0ZTA3OTk1YzA2NTIzZGU1YzZlZjYxM2M4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-982419655\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-548642707 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0M1K0sdNadPD2LVrnt8LmBw227nA9F1U5e3ROaJK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-548642707\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1144866285 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:11:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNHMk1xeWEvUFI0eE9PVC9WL0orNnc9PSIsInZhbHVlIjoibkJHZS9DUFlBak5kWWhxYkhvRy91b2Foc3lTWnlLTnh3Qy9MOU0ySUlnb2hpT2hna0FlYzZ3Y1NmK0tSdmVyVFJSYTVCZ3NYY3BIdktvRFE0NnJaMU5JbXd0YXFsRnNCZFZzSmt0U1A5ZHFTd091R0ZKTXc3dGQxa2V4M2xiMVY4SG1FR3dTN0oyWGxOUGVwMndUWVdsWXRnQ3c1WjZwdU9ZaDc0Q2ZvYkxUOE1IQ0d3YmFrTzZnbkVWWGJwYlo1K2VjZkJkUHZpczRFQ3oyR25obEx3TytQdjl1eGhXWkNqdkI1TDluNkMzYnRRY2RqaFhiT2J1VGQyL3dmczVUaFVvUmlFRlpNeVRkcTFHS1hJTVdrSG9VR3NLZFVleG5kSGdtcTF4b3pDVjcxV0JuMWxIelJLOXA0NUJXUEwyazJLeDhkZ0V5cXdVMFg4VFd2MEZJcFZNZTBCUllxY2JQaXZMMk9ZRmFnSkJTL3Yyd3J2MGdaY0hkRnRxT2QwS2dFT0J3NXU0VkVWQUxXWlU4SFlHVzJYWmJQVWV1Q0xRU2h3YlNhSEQ4WGZPRmNaZk5sUldlRkF2SXdUZVVSSC91bnBtditWUUhsZ0J4Z2thRk8vOFBsdzhkaWRBelpucSs5Zmo1QXZ4dzJHR2RHSjhaVzM5a1FacEZaSnhzWXVBQTgiLCJtYWMiOiJjM2NhOTRjNjQ4ZmJjMTRkODk2OWU3ZmVjYzk5MDNiNmUzMDNmNzQ1MzRjODQ4MDNlZDU2ODZhYmFlNTRkYjkwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:11:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InBya2pGTUFaaCtKWjBGNU9XTXhSR3c9PSIsInZhbHVlIjoibmFiUlpZQzdQYzF0YjVhUm12RnUxMEVJNlp3azdYc0lkb3dueitGUTRyaStyWkliL1ljZ2RhQnQ0R2lVTHpZb1hxZlVBeVhJQVl0eHJyejBIbXU3MnRmRmhVVGgwNEdMcGp4UVR6YVFyZXFwcXhzNmVRUjV3aXE1MjVleVRnZDhmcllobTEzRG5PWG41ckxYRUxWWG5XZlVjMlEyMVVQRWxEZ0RPRW9TaVdDTk9vck4rUmNnWExqWWFPWlZQT1VxUExrbFlCT0tyb3JyaC9welJoV21DUnBIcmpDYmNGWlZUWHdSN1NpU1pzWFVWV2VHRUh4UmpSNmZGWEtNNnl6emVNQmN3TGRvdVN0QVBhZlB4ZlY5Zm83Y0FKM2l4MmFqeEJCTkdxUXFUN0NGekh6YU84TUROYUl0NUhwdFlGdCs0cDVkQkZ6RTJPUmI0K24zWWE0YXlBdC82YXE3RXFyVzJSdmxJV1pQVUw2cUJUMEpxa2I5cEZvRTRRWmpmNXdHQXFKZzNXeitGYXRqYW5OSGV6NGFyUGorZzBMN3hYMXkycGdTdkxuc1VzNzVZRitPUkFMNUFxUUF1UWl1K3VHcUVEQmpobFRsbHdxbHRDcUhmMi9rU0s4KzEwSHBaL2FHYlI5NVVzbjZLNGp4RWNjVjNKNnNrcGlkN2JvZHZHTCsiLCJtYWMiOiJkOTRiMzkwYTcyN2NlYTRhMDJjNjgxMGI4ODUwMjhkNDdlNTYyOWEyMTM3YjAxNDc5NTMyMzk4MzE1MmRjZGEzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:11:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNHMk1xeWEvUFI0eE9PVC9WL0orNnc9PSIsInZhbHVlIjoibkJHZS9DUFlBak5kWWhxYkhvRy91b2Foc3lTWnlLTnh3Qy9MOU0ySUlnb2hpT2hna0FlYzZ3Y1NmK0tSdmVyVFJSYTVCZ3NYY3BIdktvRFE0NnJaMU5JbXd0YXFsRnNCZFZzSmt0U1A5ZHFTd091R0ZKTXc3dGQxa2V4M2xiMVY4SG1FR3dTN0oyWGxOUGVwMndUWVdsWXRnQ3c1WjZwdU9ZaDc0Q2ZvYkxUOE1IQ0d3YmFrTzZnbkVWWGJwYlo1K2VjZkJkUHZpczRFQ3oyR25obEx3TytQdjl1eGhXWkNqdkI1TDluNkMzYnRRY2RqaFhiT2J1VGQyL3dmczVUaFVvUmlFRlpNeVRkcTFHS1hJTVdrSG9VR3NLZFVleG5kSGdtcTF4b3pDVjcxV0JuMWxIelJLOXA0NUJXUEwyazJLeDhkZ0V5cXdVMFg4VFd2MEZJcFZNZTBCUllxY2JQaXZMMk9ZRmFnSkJTL3Yyd3J2MGdaY0hkRnRxT2QwS2dFT0J3NXU0VkVWQUxXWlU4SFlHVzJYWmJQVWV1Q0xRU2h3YlNhSEQ4WGZPRmNaZk5sUldlRkF2SXdUZVVSSC91bnBtditWUUhsZ0J4Z2thRk8vOFBsdzhkaWRBelpucSs5Zmo1QXZ4dzJHR2RHSjhaVzM5a1FacEZaSnhzWXVBQTgiLCJtYWMiOiJjM2NhOTRjNjQ4ZmJjMTRkODk2OWU3ZmVjYzk5MDNiNmUzMDNmNzQ1MzRjODQ4MDNlZDU2ODZhYmFlNTRkYjkwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:11:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InBya2pGTUFaaCtKWjBGNU9XTXhSR3c9PSIsInZhbHVlIjoibmFiUlpZQzdQYzF0YjVhUm12RnUxMEVJNlp3azdYc0lkb3dueitGUTRyaStyWkliL1ljZ2RhQnQ0R2lVTHpZb1hxZlVBeVhJQVl0eHJyejBIbXU3MnRmRmhVVGgwNEdMcGp4UVR6YVFyZXFwcXhzNmVRUjV3aXE1MjVleVRnZDhmcllobTEzRG5PWG41ckxYRUxWWG5XZlVjMlEyMVVQRWxEZ0RPRW9TaVdDTk9vck4rUmNnWExqWWFPWlZQT1VxUExrbFlCT0tyb3JyaC9welJoV21DUnBIcmpDYmNGWlZUWHdSN1NpU1pzWFVWV2VHRUh4UmpSNmZGWEtNNnl6emVNQmN3TGRvdVN0QVBhZlB4ZlY5Zm83Y0FKM2l4MmFqeEJCTkdxUXFUN0NGekh6YU84TUROYUl0NUhwdFlGdCs0cDVkQkZ6RTJPUmI0K24zWWE0YXlBdC82YXE3RXFyVzJSdmxJV1pQVUw2cUJUMEpxa2I5cEZvRTRRWmpmNXdHQXFKZzNXeitGYXRqYW5OSGV6NGFyUGorZzBMN3hYMXkycGdTdkxuc1VzNzVZRitPUkFMNUFxUUF1UWl1K3VHcUVEQmpobFRsbHdxbHRDcUhmMi9rU0s4KzEwSHBaL2FHYlI5NVVzbjZLNGp4RWNjVjNKNnNrcGlkN2JvZHZHTCsiLCJtYWMiOiJkOTRiMzkwYTcyN2NlYTRhMDJjNjgxMGI4ODUwMjhkNDdlNTYyOWEyMTM3YjAxNDc5NTMyMzk4MzE1MmRjZGEzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:11:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1144866285\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1558097527 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1558097527\", {\"maxDepth\":0})</script>\n"}}