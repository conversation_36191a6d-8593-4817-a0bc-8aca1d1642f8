{"__meta": {"id": "X82eb8c5805e0caf813de77f9779fc1b0", "datetime": "2025-06-30 23:14:11", "utime": **********.487139, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.012106, "end": **********.487155, "duration": 0.4750490188598633, "duration_str": "475ms", "measures": [{"label": "Booting", "start": **********.012106, "relative_start": 0, "end": **********.429342, "relative_end": **********.429342, "duration": 0.4172360897064209, "duration_str": "417ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.429354, "relative_start": 0.417248010635376, "end": **********.487157, "relative_end": 2.1457672119140625e-06, "duration": 0.05780315399169922, "duration_str": "57.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45363592, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00381, "accumulated_duration_str": "3.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 23 limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4646711, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 53.018}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4758239, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 53.018, "width_percent": 28.609}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.479766, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 81.627, "width_percent": 18.373}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WuYcucCgl4bBOLSdLTqnajP2UNBzqFh0FtUk6GCo", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "23"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-96636410 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-96636410\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-61219994 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-61219994\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1349167161 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WuYcucCgl4bBOLSdLTqnajP2UNBzqFh0FtUk6GCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1349167161\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1732415131 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325245466%7C33%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImF1eWpRWkRSc3RWTVhkTDJvcjZHaWc9PSIsInZhbHVlIjoibTZtcjdEeFh4aW4rL01KdnpqQ1RsMnBVRFJHeS9qT2V0MW94RUp0dnNYR3FlYnBpbWJGTWlqTCttTlVkcEFnWSszZ3lHbnFsVXBQT3MyRGJPMmRWZC9xR2tHUUhCampvMjA1bUwrd2tyU2Qxbkx5THhsTjVpazVlc0pkYnc0b1Fhd3F4dVhjVi9kSEp3MTNPd2swMDNRNVVmeTE2NThSWlowWG5Tc0NVbHpybXFZK3hmNk1oQ3dLRFA4bUpzeC9qRFJrRUFvQWdTeEFBNno2V1planB3UWE4TCs2TGNudDFGZCtCOWozdjBWKzkrZWRJRVlxOUpJL2FSQ1hPTFBMZGcyTEd0RStZVkc4dXU4NExzTllzdjFvaWdNbUE4UE10VXloamI3K0xKazJqNE4waVRQRXQ2bmZiTVhSb25SRWsyM1JTSlU4V2x5OVFRRjhneG9GVjhta1g3YTFCUGJCS3VJd0lDVzZpYjhROEtPUHlOTWNQR0F3Mlc5OURzdVd6ODVpMmpnUXlGbkhhUHhZbm5OMEltK2xoRFMrVitIVkJvY3plMXpqd2V4U1NDcFM0bUhNc0JPT1VMV2pMVXRucHlXNklad3VjZ3BoQ3VuQVF2MnhmVElMS1lXSTdyWUNMNGRwckFZRjJHcnZDSHE0M1dZZGE1YTFjd3VQT3dIQ3oiLCJtYWMiOiIwMDhhZTQ2ODdhNjY3ODAyZTczZDcxZWVmYWM1NTFjY2U4NmI1ZDJlMjcwOGU1MjkxOTIyMzNiNzZmZjI5ZGJhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IldVaUJNTWFBbnlxZExpRkxtZVdqNkE9PSIsInZhbHVlIjoiQnd3dFF3UXBVd1M5V2RtYzRrbHBrMDM4THBiY1FBbzVkL3lyUklQVmJDbks0SzJLR0hjd3RtSmtGbmRvak93ZjNHZTVwZGpxZDRoc2x4am5XMlNqeVAreXdzZ1ZUMzFiSlRwaG1HNlJoYzV5MXI3YStaZHJmQzBEbjExOGsvN2JWUng4Z0U5ZnAvV2ZhYzBoWlIyRWhvc3RjUTFKTWpuZ1cySGpwbjRiTS90dVpWanl4NkJUOEhVR2Nmcm9vOXJHQlhnNGJhb0IrM3JBeStUQ2Y1OHcrNjJZa1NWaTlqVFltMWgrU09iVjRKS0lsNUhLd0s2QWd4S2ppNSt0VnRzYnA4djNPTEQ2MGRqNmlHT0cxMEtqWEI4SWhOZG5UaGxyL2loN1dkRC9FYzVNQ0tGQUo2ZmVOWVlkaHZDb0F6TTU5UlE3eit6WkdPb3BVUFdlM3NydHVaSFJEdUIyZld1UTJGVGdqVWx1WnJDZlpMZldCQTl1ZktQTVB5bWtZaERDT0QvUndJM0dQbzFFT09wSXpTTFdSVGtDTkRqdHZ3cm5TZWNQMy9IZTBldTh1SjlzYk5IYU41dlNwakxpeU9MeERmRkp5MzRwVXduSEFWckFwRWZlWWdONW1HTEtIUGVkOEgvaTgva3pDc29maEcyeWwzczZiQk1ucUh0clNMWWkiLCJtYWMiOiJmOTU1ZThmNzZlZDQzOTkxZmJlZTZlMzExZmEwOWZhNWNkODM4MDE2MjBiYzM5ZGE4Yzc2NWM1Y2EyNzcyOWQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1732415131\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1152887184 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WuYcucCgl4bBOLSdLTqnajP2UNBzqFh0FtUk6GCo</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">m2aShS6RaaTcTNL9OqnWxuHeTxyTGRJLBdK2tqlv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1152887184\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1323133394 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:14:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZSK1crbGk3TzAwa3hkVk9zTnFTMUE9PSIsInZhbHVlIjoiL0F6d1NGNk9vRnd1MEk4QXgySm1zbGhaZG02V2U1NXNuQ3BCaGhtUUhPc0lmMnZVbk4rOGxUanFwQm1uMVVFakJGbE5xUlg1dE5iYXRQd3NHcW4raWVHNlYrbUVUTEZLc0o2UHN2T2RQY2NianBIb0tISFBqakE1OU0yWURIWGVUUzZkTUVhTkVpM1pCeUJkSHNsVVhXVG5wV1Y4VjBGaSs0MDVMWllwKzRnRmZYWnZuODByekkybDJmcjB2bUZxSm1nU3ZTdmVEejZaTUkvcTNwY2NZS044R0ZBbWpCYkZ6Y0NFNCtoWFYvaGhtUlFUc01QVThqeG5nVnZ6WU1ROHNuWWxvWi8yZk5uamRhUzZzaXdRNXNNYVE0MUh1emVqMkhsQ0dlZVBOckJqOWJ4VjAzOUl5YXE4Z1ZvdXhaUzhCSzJkUFdGSXJEdUdoYlAweFdrNHFjQjdPMTFwV01SMEU4VjBuQkhwb0RXZlFvYXlFOXhBeFJLd3V3THU0b3FGb1JvRHZ5bEJVa3hqWnBoTU1vRlB4cm9QelVPVUI1cDBJQlZRVVJ1cnovbjZrYjJqbUVIZ2pXWHVSdVJEYlhqUE9IUjQ0a2hCamQ1aGN3Uk1kQ2FOZ3V1a3ZIaHdCYkJRZUptcTVrT0VDakJKSTQxY1FEK1g2anpydUFuUHBtRmwiLCJtYWMiOiJmMjU5ODNkOTJlM2ExZDkwOGE4MzA1MmRiMWU3MTZiNTQyMzlmYTUzY2MxNzhhNTk1M2UyNjUwMjBlZGZmNjc4IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkRIR2NlUVhQcy9kV29nSGExZ2t1UlE9PSIsInZhbHVlIjoibmFQcGVXMEN3WmZSSEwwbVhuZFBtOGJxcnA4N0ZMVUU0aHQ4SFZoVFJrbHZiR2tnOUUrZk50NWxrdjhENEJ2SjNBcUo0U2lPMTJKdVNRT1dZUUV5WWpGWEs0Vm04THBYbmRwZldzaThpZjk2MnRzZWNJclhtVGhCTWpEME9KanFQTVhDcHMzSm1QVDF0YWxYWTV3SHJoRnhwUC80U1BlSGkySmp2VXZ1K1U3SW1WcnVKN0laUy8yOFFRSjZPTStUTkh0UEtrSGNHNVdpODRjbFZncCtRUEx5UElVVWZWQnVZQWI0akFXbVBXSmxnTUhIRzRoNkFXek1lUi84SzB2VGY5VWJzWUxWWGNOOTFaQXVXaGRzSkQvY2xxNklKbXFtbDZQM3lLM1ZlTmFJYk5CNnI1eTVXUkl5eThxL04razNsZ1FuYWV4UkVPS3JrMFV5NnN3VmNadEd3RXg5dUVUUlZvZ1MwOHR2TXJMbGdvazBQajRtcG83TVVuY0J4K1ZHLzRYT2l0d2FsRGp1WDd3QmhRSXB6RXlvMFE2aEhBM0VEU1o5Vi8yc0RHWEZsMXFuS0R2U3hFdXdYOFpIYXV3TmxMeXlxcEFJOEZiZVY5NGJsRStIUU5jS1l1NDAyb3ZZMzVyS0d5NU1sbC9ranpNb2dFdkxJdDhKUkJXVDJwWFMiLCJtYWMiOiJhMjA3ZjBlYTc1NDM4YTgxYjU4MWVjMGY3ZTM2OTYzMzY5YzJmMjM5MjI0OGQyYzJlZmFhZWU5YzJhZDI4YWYyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZSK1crbGk3TzAwa3hkVk9zTnFTMUE9PSIsInZhbHVlIjoiL0F6d1NGNk9vRnd1MEk4QXgySm1zbGhaZG02V2U1NXNuQ3BCaGhtUUhPc0lmMnZVbk4rOGxUanFwQm1uMVVFakJGbE5xUlg1dE5iYXRQd3NHcW4raWVHNlYrbUVUTEZLc0o2UHN2T2RQY2NianBIb0tISFBqakE1OU0yWURIWGVUUzZkTUVhTkVpM1pCeUJkSHNsVVhXVG5wV1Y4VjBGaSs0MDVMWllwKzRnRmZYWnZuODByekkybDJmcjB2bUZxSm1nU3ZTdmVEejZaTUkvcTNwY2NZS044R0ZBbWpCYkZ6Y0NFNCtoWFYvaGhtUlFUc01QVThqeG5nVnZ6WU1ROHNuWWxvWi8yZk5uamRhUzZzaXdRNXNNYVE0MUh1emVqMkhsQ0dlZVBOckJqOWJ4VjAzOUl5YXE4Z1ZvdXhaUzhCSzJkUFdGSXJEdUdoYlAweFdrNHFjQjdPMTFwV01SMEU4VjBuQkhwb0RXZlFvYXlFOXhBeFJLd3V3THU0b3FGb1JvRHZ5bEJVa3hqWnBoTU1vRlB4cm9QelVPVUI1cDBJQlZRVVJ1cnovbjZrYjJqbUVIZ2pXWHVSdVJEYlhqUE9IUjQ0a2hCamQ1aGN3Uk1kQ2FOZ3V1a3ZIaHdCYkJRZUptcTVrT0VDakJKSTQxY1FEK1g2anpydUFuUHBtRmwiLCJtYWMiOiJmMjU5ODNkOTJlM2ExZDkwOGE4MzA1MmRiMWU3MTZiNTQyMzlmYTUzY2MxNzhhNTk1M2UyNjUwMjBlZGZmNjc4IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkRIR2NlUVhQcy9kV29nSGExZ2t1UlE9PSIsInZhbHVlIjoibmFQcGVXMEN3WmZSSEwwbVhuZFBtOGJxcnA4N0ZMVUU0aHQ4SFZoVFJrbHZiR2tnOUUrZk50NWxrdjhENEJ2SjNBcUo0U2lPMTJKdVNRT1dZUUV5WWpGWEs0Vm04THBYbmRwZldzaThpZjk2MnRzZWNJclhtVGhCTWpEME9KanFQTVhDcHMzSm1QVDF0YWxYWTV3SHJoRnhwUC80U1BlSGkySmp2VXZ1K1U3SW1WcnVKN0laUy8yOFFRSjZPTStUTkh0UEtrSGNHNVdpODRjbFZncCtRUEx5UElVVWZWQnVZQWI0akFXbVBXSmxnTUhIRzRoNkFXek1lUi84SzB2VGY5VWJzWUxWWGNOOTFaQXVXaGRzSkQvY2xxNklKbXFtbDZQM3lLM1ZlTmFJYk5CNnI1eTVXUkl5eThxL04razNsZ1FuYWV4UkVPS3JrMFV5NnN3VmNadEd3RXg5dUVUUlZvZ1MwOHR2TXJMbGdvazBQajRtcG83TVVuY0J4K1ZHLzRYT2l0d2FsRGp1WDd3QmhRSXB6RXlvMFE2aEhBM0VEU1o5Vi8yc0RHWEZsMXFuS0R2U3hFdXdYOFpIYXV3TmxMeXlxcEFJOEZiZVY5NGJsRStIUU5jS1l1NDAyb3ZZMzVyS0d5NU1sbC9ranpNb2dFdkxJdDhKUkJXVDJwWFMiLCJtYWMiOiJhMjA3ZjBlYTc1NDM4YTgxYjU4MWVjMGY3ZTM2OTYzMzY5YzJmMjM5MjI0OGQyYzJlZmFhZWU5YzJhZDI4YWYyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1323133394\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-109147643 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WuYcucCgl4bBOLSdLTqnajP2UNBzqFh0FtUk6GCo</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>23</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-109147643\", {\"maxDepth\":0})</script>\n"}}