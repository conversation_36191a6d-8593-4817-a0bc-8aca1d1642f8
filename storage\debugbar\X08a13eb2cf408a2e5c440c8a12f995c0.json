{"__meta": {"id": "X08a13eb2cf408a2e5c440c8a12f995c0", "datetime": "2025-06-30 23:11:42", "utime": **********.878865, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.45749, "end": **********.878882, "duration": 0.42139196395874023, "duration_str": "421ms", "measures": [{"label": "Booting", "start": **********.45749, "relative_start": 0, "end": **********.824023, "relative_end": **********.824023, "duration": 0.3665330410003662, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.824032, "relative_start": 0.36654210090637207, "end": **********.878884, "relative_end": 2.1457672119140625e-06, "duration": 0.05485200881958008, "duration_str": "54.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041168, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0031999999999999997, "accumulated_duration_str": "3.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.854366, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.313}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8651621, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.313, "width_percent": 19.375}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.871154, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.688, "width_percent": 15.313}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-245998963 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-245998963\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1353233521 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1353233521\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-433557993 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-433557993\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1536642992 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325098367%7C18%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlB4S1dWRkx0WmVrUjFDNWl6RDdiK0E9PSIsInZhbHVlIjoia3I3eU9kNVlQQ3owb2NNa2huVzk0ZzRHMnIvdWpOUSs2RW5BZyt4NEY1bFp0RUJHeTBhdFRjVUVjbG1ZUWMrVC9WNFpWSC9mUGZIbm44cjRuN1hMSDUxNSsycklKOGM3TDZiaC9NZzRMU3hQMkxEc1d6VDF2SWhSZjBuUGVodi9GNjhaQ1NpNFJyaEFEME54Y295ZkdsL3dPTXdFUjVkekNSK3R1UnBvMFhHZ1ozWjhWa3dKWEE4aTVmdWJDWnZ1c3NnbkJZVDZDY1picHBROS9YWFdlZE9weVlwcVNXc2dmM0dFU2tUeG1ubHJtL2RnVkp1Y1VwUmIvQTB0Q1RFazhpaXgzL25PREx2eHhuQTQvR3lwdHBtaUlIQkoxelJpcW9iN1BOZUU3MzlQU0FmYURyVmpnMjZsSjc0cE9FZ1J2ZWNYeXB1WVdPQWNTeEtpWUQ1VjVwdmFpL2ZRRklFYWVkeTMxSTduZEJXVTNuYzZKK2lZNXZvalFlc2J2ZURpQUlPeC9LWTZKbWRQMlZ3akptQ2pBa20yS1ZqSkMzZDhFdTVrU2V4V3dNS2RoQ09aZ2RIMUhDcTNxS2NKQVRRS0NTWEMzQjFKd2lpN05WR05JcDczNmpMUGhTN2tXMXBJY3Y3anpMMTB1c3ZlOHBBNW9vOEw4UTlBczMwQmpYZmsiLCJtYWMiOiJjYjZmN2YxNWFjNTQ1YmViYjNkMzgxOTY2NjU0N2NjYjcyNDMwMGNlNDNkZjY2YjZhZGVmZWNhMTJmY2MzMDMzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjdDUWhBb1ZidzVkVS8yYlFKN1N5Znc9PSIsInZhbHVlIjoiNGJmU0MrRUtyNGZiUms1RnE2bGZKdWNmSzhwd3hseHBkYkVDTFIvZXloaGtVelFJa04rVitTWXExVGVlQ3dVeEdCVkJ0cHM5L2JWN0ZqVW9DdG9CNFF2M2NUWVc2ckY0WHRDaEk0Y2daa2hhM2llbUdlcWI2Yi9rL1NzYW5uRk0wbjlzSXNwU0I0NkNxVHdxNlJPY3NGcDRHS1hsTzRISzBXVUVCTjdDTUJkVDY4U0pFcmYvOTdsSXp4UUlTOTVYTVl5Z1gzUTZZalNwSC9VbXBEaGs3cXRiand3OW9tUVRjNE9jSmVqSHVFbGo1d1loNWNTVDNqSk9mSkMrMjhuMGhnSzh2YnArU0ZmUXNMZE0yTmhkYng0Sm1PYm1VNXdaVUUzN1BlUWRndDB1dlN4U2taMWtnVVlzdW9BWHlMUU9RMGJMcXNNV2lhQkZXV1RsTkpBM1NqUk5HVjhsbEFYZEVxanhWZnErRGF3VEVyNzMwVFlRM2k4UDUvSWN5OXFvcWxtZTR0YW9Kb0ozaFdmWW1WdVlQWWVldkFnQUtaVWdFcG9MeFl4eTU2YkV2ckdiREYwT3h2eFczRmlSMjAxVnJ2ZEVHb1dTMVhON3B4elVUL2pmR3BiNTJPQTNDMmJ5SlhmemhVMEZ4KzNUbmhSOGZZK1N4TVlYWXljS1pSak4iLCJtYWMiOiJlZWRkOTlmMzc0OTRhZDgzMDY3YzI5MzE0NWJhZDJjNjkwY2Q3MDU1MjMxN2VlMDA2MDVjMjdhNGViNzYwNDM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1536642992\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-127167934 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0M1K0sdNadPD2LVrnt8LmBw227nA9F1U5e3ROaJK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-127167934\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-295668702 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:11:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdiZncxaHlGaHYxWTJKVTZ4MXhEMEE9PSIsInZhbHVlIjoiM0RldnFXV1JxdTN6SWZRUmhxUkt5R2h5ejR0Z1UrcmNnN2dCWmo4MEZ6WkN5UTg1bkRkQy9VdWNiL09SZ0trK0g0Y3M2RDhlbjBtd1Y3SGtHbUJXQTE0WU5NM3ZwcUZtUFNYd2xkaVJXVXR2bGNTWVZadENja2FvOWtFMENWdVpVN0VhSFdrZTVGNmNNbXhNYk55VEkwWm9maHVCbmVKU08veUY2L2tVS2t1NjNISC9ZMUd1ZERyMm5oNTBnaXZLOEtaUWhBT05EcWllcXhySFRISFNtR2hGWGo0V0pxYzhqeUZIVFNtZ1psQkROYXZKT1Y0SHNqeklFcHd4ZkxrUkVuUjV6WUpUMHJvZk5IaGw4SUVPWGhNZ0t1TUdCRDJ3S1lvd2tXNmVQZlE1WStkMXJnZkxWUmZkbStWQWtZNGxObWFMaW9GTS9sU1RXYkltalE0YnRhbE1CTTVyNE9ISkpYazROTVNkTTFNMHNQelZvUVBEdXRxYUtUaldoZnBQS2xnbS9pWVc0dTl4Z1FtNVRvOTdQVC9FQ0dEeW9UQ05Ec0E4bFFCb3l0RnlUWmJra0VqMWhHUWNjUDNHVkRiL2piNWJJOW44SDVHa3gxL0FJQTFqWFp6d0kweDR3aWdLY29HRHlpRTJ3akNJUU10ZUFTS2k4WXpLaFVFYjhWaUkiLCJtYWMiOiJhZTlhMzE3M2E3MjNjNGNhY2Q5ZTIwNGNjNjg3OTE4NjA4YWYxYTdmYWI1ZmZkMDVjNjFiNzIxOGY0MTUyYTkyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:11:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkszNytkNFdBbkJnZW50Znh3ZXpiRmc9PSIsInZhbHVlIjoiQkczWWdSb0grNDlqMmhlR0xnb1BvazMwOHdUcmwzOTQ4K1Vkaml2UmZDOEVnQUtubHBlQytqRmpRL1lqSDBvUDFjZFRoQUowU2ZZZDFEdUJ3elNvbFVUSXc4bENhNXI5QmYyZ3VraTBrVmp0aFVRTkI2RmM4clBmMmxzUEhqUkltVlZ6MnRDYmNodGhkVW4yRytvbGYxRUtJeUpHOFE2OEc3b1VYUW5rK2dVcEhJRUJ4SDZGTkhlWnRlRWVKVk1maTk1TVgrajZkTm1idHZiR3ZoNHpyc3VCb2dUTmcrOHQxOENYOXFVNXlkdjh1SkhPZnNiWG5ReWIrMU9Mc1M2ZHdrK2c1MUw0NDMxTzdKOE5vK1pnd2tRbi82TmQ1cFl4cjNyeUlCQUR5QjRUUXdyYkRBZFpJQ29lTys2aWJUVXQ0SWpvMWxpakYwdkdnQ0FHcjNINXpIY0xqZWE5Q3dIalZ2R0xFS3ZWZDVZSW5QcmNhSDRYZFhLVGpnYVlNem8rbFFoODV3ZFBvT2pwNm1aeUt0dHkvTkZ4Y1JjaHF5V0R4ckFDRHpibERFdDlwbEJGWFpEeW1pVWlRS2V0cnZ2QWQ3SnZ5Mk1iMnFZTk1OWU1TZTJhYm5oYXhVUTNLWkpiU0NyWXJpOHRsdS81YkpHVnc5TlIrU3IwYmsreGt6cWsiLCJtYWMiOiJkYjJjNDUzODVjOGNjNTFhNTY5ZmQ0NDRjMmIzMTM0NmRjMjJkMWM0MTJmNjkzMTFiZTBkN2YyNzdiNDhiZGMxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:11:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdiZncxaHlGaHYxWTJKVTZ4MXhEMEE9PSIsInZhbHVlIjoiM0RldnFXV1JxdTN6SWZRUmhxUkt5R2h5ejR0Z1UrcmNnN2dCWmo4MEZ6WkN5UTg1bkRkQy9VdWNiL09SZ0trK0g0Y3M2RDhlbjBtd1Y3SGtHbUJXQTE0WU5NM3ZwcUZtUFNYd2xkaVJXVXR2bGNTWVZadENja2FvOWtFMENWdVpVN0VhSFdrZTVGNmNNbXhNYk55VEkwWm9maHVCbmVKU08veUY2L2tVS2t1NjNISC9ZMUd1ZERyMm5oNTBnaXZLOEtaUWhBT05EcWllcXhySFRISFNtR2hGWGo0V0pxYzhqeUZIVFNtZ1psQkROYXZKT1Y0SHNqeklFcHd4ZkxrUkVuUjV6WUpUMHJvZk5IaGw4SUVPWGhNZ0t1TUdCRDJ3S1lvd2tXNmVQZlE1WStkMXJnZkxWUmZkbStWQWtZNGxObWFMaW9GTS9sU1RXYkltalE0YnRhbE1CTTVyNE9ISkpYazROTVNkTTFNMHNQelZvUVBEdXRxYUtUaldoZnBQS2xnbS9pWVc0dTl4Z1FtNVRvOTdQVC9FQ0dEeW9UQ05Ec0E4bFFCb3l0RnlUWmJra0VqMWhHUWNjUDNHVkRiL2piNWJJOW44SDVHa3gxL0FJQTFqWFp6d0kweDR3aWdLY29HRHlpRTJ3akNJUU10ZUFTS2k4WXpLaFVFYjhWaUkiLCJtYWMiOiJhZTlhMzE3M2E3MjNjNGNhY2Q5ZTIwNGNjNjg3OTE4NjA4YWYxYTdmYWI1ZmZkMDVjNjFiNzIxOGY0MTUyYTkyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:11:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkszNytkNFdBbkJnZW50Znh3ZXpiRmc9PSIsInZhbHVlIjoiQkczWWdSb0grNDlqMmhlR0xnb1BvazMwOHdUcmwzOTQ4K1Vkaml2UmZDOEVnQUtubHBlQytqRmpRL1lqSDBvUDFjZFRoQUowU2ZZZDFEdUJ3elNvbFVUSXc4bENhNXI5QmYyZ3VraTBrVmp0aFVRTkI2RmM4clBmMmxzUEhqUkltVlZ6MnRDYmNodGhkVW4yRytvbGYxRUtJeUpHOFE2OEc3b1VYUW5rK2dVcEhJRUJ4SDZGTkhlWnRlRWVKVk1maTk1TVgrajZkTm1idHZiR3ZoNHpyc3VCb2dUTmcrOHQxOENYOXFVNXlkdjh1SkhPZnNiWG5ReWIrMU9Mc1M2ZHdrK2c1MUw0NDMxTzdKOE5vK1pnd2tRbi82TmQ1cFl4cjNyeUlCQUR5QjRUUXdyYkRBZFpJQ29lTys2aWJUVXQ0SWpvMWxpakYwdkdnQ0FHcjNINXpIY0xqZWE5Q3dIalZ2R0xFS3ZWZDVZSW5QcmNhSDRYZFhLVGpnYVlNem8rbFFoODV3ZFBvT2pwNm1aeUt0dHkvTkZ4Y1JjaHF5V0R4ckFDRHpibERFdDlwbEJGWFpEeW1pVWlRS2V0cnZ2QWQ3SnZ5Mk1iMnFZTk1OWU1TZTJhYm5oYXhVUTNLWkpiU0NyWXJpOHRsdS81YkpHVnc5TlIrU3IwYmsreGt6cWsiLCJtYWMiOiJkYjJjNDUzODVjOGNjNTFhNTY5ZmQ0NDRjMmIzMTM0NmRjMjJkMWM0MTJmNjkzMTFiZTBkN2YyNzdiNDhiZGMxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:11:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-295668702\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}