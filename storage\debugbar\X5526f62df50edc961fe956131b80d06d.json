{"__meta": {"id": "X5526f62df50edc961fe956131b80d06d", "datetime": "2025-06-30 23:15:00", "utime": **********.886117, "method": "GET", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.48616, "end": **********.886131, "duration": 0.39997100830078125, "duration_str": "400ms", "measures": [{"label": "Booting", "start": **********.48616, "relative_start": 0, "end": **********.811004, "relative_end": **********.811004, "duration": 0.3248438835144043, "duration_str": "325ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.811012, "relative_start": 0.32485198974609375, "end": **********.886132, "relative_end": 9.5367431640625e-07, "duration": 0.0751199722290039, "duration_str": "75.12ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47592448, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@index", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=33\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:33-84</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00359, "accumulated_duration_str": "3.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 23 limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.843698, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 47.075}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.85349, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 47.075, "width_percent": 12.256}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.856468, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:37", "source": "app/Http/Controllers/FinancialRecordController.php:37", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=37", "ajax": false, "filename": "FinancialRecordController.php", "line": "37"}, "connection": "kdmkjkqknb", "start_percent": 59.331, "width_percent": 11.699}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 23 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["23", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.8702672, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 71.031, "width_percent": 18.384}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (23) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.8721979, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 89.415, "width_percent": 10.585}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 5, "messages": [{"message": "[ability => manage pos, result => null, user => 23, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-874174247 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-874174247\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.875489, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 23, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1332006930 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1332006930\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.876555, "xdebug_link": null}, {"message": "[ability => manage pos, result => null, user => 23, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-428130405 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-428130405\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.87728, "xdebug_link": null}, {"message": "[ability => manage pos, result => null, user => 23, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-641645770 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-641645770\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.877954, "xdebug_link": null}, {"message": "[ability => manage delevery, result => null, user => 23, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-249782347 data-indent-pad=\"  \"><span class=sf-dump-note>manage delevery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage delevery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-249782347\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.878855, "xdebug_link": null}]}, "session": {"_token": "WuYcucCgl4bBOLSdLTqnajP2UNBzqFh0FtUk6GCo", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "23", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-2490073 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2490073\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1472890442 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1472890442\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-437542669 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-437542669\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-859296206 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IlFYS2NhMWttS09RMlV0eXVJVEd3bXc9PSIsInZhbHVlIjoiOHM5bjVYL1c0ejZkdXlyWnpCdlZuUT09IiwibWFjIjoiOWI4M2UzNjYyZTU3MjBmZDU3ZWY0YTRiYTYxYmFlZjBlMGU0NWZlMjA1NmY5YjVmYWUxODQ2ZDY5MzQyMzFiOCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325273213%7C36%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZNVEErcFhUOC9BRy9kS0NsdkFCckE9PSIsInZhbHVlIjoibUxVMkVrcXc0cWJHallUeDdXY2lyekh1bFRVYVg3RnZpbWw2ZnRBUWt3TmZQcGYxRC9XYWRNUXFXdHNhQmkwWXR3Y1ZkN1k5bVVpSVdyWU41YytKRHhmMUVFTis3VXRHMkNMMjRBOGJhVzRiaFdiQldSU0gxY3R2OHlIZmFMZjB5OTVOQWF3azhtNW5nc0RGNGt4ZzdIQTZLb01jZDZTQmlVeFhBSkRCbFlHK0xvaXhIbC9Ld3JER21VdUZuNTJhMnVmZGZFbk1wTndoUks3UG8rTlpMdXlQZDhtNWpCem9mUzNnNCs3UC9GazBKamIzeDdSK0hWa0xKL2ExSWV5M2lkOVp6ckJoYzNsL3B5NzZDYldLWC9jQ0wwMzRpMS9SY3ZNK1E4M2ljQ0JrM3lJRVExUmNPdW5EanN2NFMwZ2diUU0rYUl2SHU1M1ExMjlSaGl2UDM3NFpobFhNVG5FaC9YazJGbzc4cjQ0TEFaWlpyTzYydEdmK215SGhpWTR3ZVRLT0x6WDNyYU12WDlTcXZMM2JSQVdsR3l1SmwvQTFPaW5DWnVyWFh1V3hXSkFHVmV1ZjJJbGwwYzZ3Sm5sc0RGU3pnb3gyWktjTVlsTG1PQTd2KytVTHdTY3F0Z1N1TDR1dlNia3FIWGo1cExQbHU5cktrUUNIaWxPaUlqYVYiLCJtYWMiOiI5Y2FiZjUzYmY4YzgyOGIzMWRjNzMyMGI4MDA4NDhlZmFkMWE0MjgyYWNhZDZhNWQwOTY3MDI4YTM4N2FmOWU5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IitPRzBneUxCbUJ2eTZMd0J3VmVIV1E9PSIsInZhbHVlIjoiaThuejVnNXZSNDNNQTU4RXBuSnU3ZmJHSDgvWnY5aGY4cWRGUUJITTYzOVhBNDYzYTlyQzU2Y2ZKUHpXMWVhRXcxVmZZOEw3OVJ6N2JqTmRjb3QvLzlMV1RaTVZEUjIxVkJhOFhuSjg5VU9hM2psUU92MlJUMzVSSklQajJiblpPVzMzVis5ZUF2eUltZHp6ZG1qNUZSYWhrVjArOGM1MkZ1VXN0L0M2dXRFMFk5cHlMRHUzVXBZT2JoOU5VRkdQNVcraHBNS0MrTHJyaFNBTHJEaXN3OHpQZ0RiTXJsbjJpUytTdFovWmRub0pRaE0zRWNkc05hbXkyVVJlYXJlMEZ2SW1hMjJNN1pFenk4VmRQRXZaUk05MjNjanVPYWVDeWIva3FMMWRGNCtjSzdwYzJSVGJ5eWt3bmhJdk5WVTB5d01CaVZ3T1V2QkFTWGZQZXgyN2lYaUl3UFBHQjhRMjRobVNCMUlpblhyU25mdGlYOHFQWWZzSFdYd1lHZDNKRmw0emo0NlRjbm0wTDVnZGVuU2JjTVphMUVrWWc5TU1oT2ZwUGczU0ZpYTFpQmNhZDdwY0RQL0ZZRGcwZFlaMG1BcmpndUxNb2JxRjJldkx3aTEway9raDArRm5BVThxZ2FYcWZSSEkxUjMyR2QxK2ovTTBaM1lUUmZoRFEvL2ciLCJtYWMiOiI2YzVmY2Q0NzllOWQzZDI5YTNmMDZjNjVjZWI4ZDYwMTY0ZGMzZTQ2MDU4ZmVjZWRiNWNmNTllMWEzNTJkNmQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-859296206\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1467334381 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WuYcucCgl4bBOLSdLTqnajP2UNBzqFh0FtUk6GCo</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">m2aShS6RaaTcTNL9OqnWxuHeTxyTGRJLBdK2tqlv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1467334381\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1567903427 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:15:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJ4TFZKRllzSjNiUDU3SnpPT2VDWXc9PSIsInZhbHVlIjoiTzdmZ1JySzZNZldlM0NOYXBLM3hCRzNaa0xwc20zY2htNVlaaEtuME82enpDckpnK3JtcmZUeWRJT2hMQ1d4Y1FsRlA0VzBHS0FjRm11ek5WVU8xdTVBQzFvajdUNWY4LzNsNXpNZHJvdWxjaGxTT0xGdE1CMmdBSHVWZUhISytzOXhYZEM0eXZUWFZKZW5vclh0clhPQmwzbGVVajM2aVd0Um13ZmVoZlpuM2pidkNxc0xsbDYvOFUzRTJTVVljcWE0OGZCQXRtNUFzU2wxRXJwNzI5SXVHalh4eXNsVXFUNmRUTmEySE9tdDk1bG5qRDZzSytNZWRXendzNDN3UmtCRnVHb2V6ckRsUTBhUHhPZFFqbWJ6bU5XUmFkU2NmQldvM3JOanlSdVhNRGVxQ0RVUU12N2tpUGZkSEVuWW5reEREcnVhUFNZWmtnYWd0VUxPdGRFdnFLYU5tWjdKSjhHRVZNTzdpRmkwNnl5cE1JcjRmNFJUZ0FtaG5uMER0TTR2V0tNR3l2WDRsd0M1TmJiZlh5NjArbDgxeXBvNkF6dDZaWGw0Y2p4aG9saVIyRXRIcC9mMU5kRDlYWTRIUUhqNUFNMmp5L1kxZEFYWThOR1JDNjRaTkNOb3dvQ0dzSFR2Wi94WjF5TmtrTGJkTVR1d2VjanhZMEdkc2NyMmwiLCJtYWMiOiI1OThlOTE1NWJhMDNkYzNkYzMzNTIwZmEwY2Q2ZmE1MDMzMDlhZDkwMTE5NTcxMmFlYTY2NjdkZTFlMDQxZGJiIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:15:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ill4ZnhueVRtNnEvdUNmUDZnVkQzUkE9PSIsInZhbHVlIjoieVBhL3hiY1Bac1V4WitLN2xQKzg0aDhVSHlueGFqRVR6STB6SGFpTnYybjhnTkUyS0FUbXFERWN0N3VSelJCb3dJTWJMNkxNTy9aOXRGMVlOWGdlOXM5TlRoNzRHUzh3MG1CSmZtMHNxTUxWaVZTd0p5UllvZWg4NW9YTHF1d3d0M3ZrSGowYmpsQ2JkdmZxVlVybFhnck11c2lZcUw2ZCtORlF2NkJlV2NMRjdzOEVNMXpVUlQrUDdlTW94MUg3QURGczFmMklDb0ZNVzVsdG9aYnc0TElTaUt2L3JyYU0yYmRGL1IvQ24vUmYrckVuNXVQcUZCcDFETlg3NmdZblQ5M3JIOHhoK1FIQ3l5M1NWRGxVdUFvcm5sN0RKUHRlN2MwZjRITFFzWllMUXA2SjBCZThVNGdpUlY4djVlUUlkNWdUbkJ6NUdqWjZaRlA4ekUvblhvekdtNmVWcUNjZkZFb1QrWk81czQ4WTBrZ3o3czd4OEwyV3VRZVdYaFdGemRhcDBScWZ0MXE1ZkFRN3U3TnVvY0g3T2VEOWNjUDgvMzl5Tm1wOXhRaForRUhoTlZxRTFPdHF3TEl4cVJVT3N2K1BnV2FjZnZad1JVdUtLcUJZMzFpRFB5NVZhWkEyakxmMUk0YzRnd0tNT3ZDRUdRRDNiYlpXUXJvTHdUQk0iLCJtYWMiOiI5MWY3NzFmYzM1ZGNhMTQ0ODU5M2RkMWNlOGFhNTk3ZTE1NDIyYzM5MTgyOWU1OTcyMDc0NTQ5N2IxNzZkNGUxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:15:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJ4TFZKRllzSjNiUDU3SnpPT2VDWXc9PSIsInZhbHVlIjoiTzdmZ1JySzZNZldlM0NOYXBLM3hCRzNaa0xwc20zY2htNVlaaEtuME82enpDckpnK3JtcmZUeWRJT2hMQ1d4Y1FsRlA0VzBHS0FjRm11ek5WVU8xdTVBQzFvajdUNWY4LzNsNXpNZHJvdWxjaGxTT0xGdE1CMmdBSHVWZUhISytzOXhYZEM0eXZUWFZKZW5vclh0clhPQmwzbGVVajM2aVd0Um13ZmVoZlpuM2pidkNxc0xsbDYvOFUzRTJTVVljcWE0OGZCQXRtNUFzU2wxRXJwNzI5SXVHalh4eXNsVXFUNmRUTmEySE9tdDk1bG5qRDZzSytNZWRXendzNDN3UmtCRnVHb2V6ckRsUTBhUHhPZFFqbWJ6bU5XUmFkU2NmQldvM3JOanlSdVhNRGVxQ0RVUU12N2tpUGZkSEVuWW5reEREcnVhUFNZWmtnYWd0VUxPdGRFdnFLYU5tWjdKSjhHRVZNTzdpRmkwNnl5cE1JcjRmNFJUZ0FtaG5uMER0TTR2V0tNR3l2WDRsd0M1TmJiZlh5NjArbDgxeXBvNkF6dDZaWGw0Y2p4aG9saVIyRXRIcC9mMU5kRDlYWTRIUUhqNUFNMmp5L1kxZEFYWThOR1JDNjRaTkNOb3dvQ0dzSFR2Wi94WjF5TmtrTGJkTVR1d2VjanhZMEdkc2NyMmwiLCJtYWMiOiI1OThlOTE1NWJhMDNkYzNkYzMzNTIwZmEwY2Q2ZmE1MDMzMDlhZDkwMTE5NTcxMmFlYTY2NjdkZTFlMDQxZGJiIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:15:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ill4ZnhueVRtNnEvdUNmUDZnVkQzUkE9PSIsInZhbHVlIjoieVBhL3hiY1Bac1V4WitLN2xQKzg0aDhVSHlueGFqRVR6STB6SGFpTnYybjhnTkUyS0FUbXFERWN0N3VSelJCb3dJTWJMNkxNTy9aOXRGMVlOWGdlOXM5TlRoNzRHUzh3MG1CSmZtMHNxTUxWaVZTd0p5UllvZWg4NW9YTHF1d3d0M3ZrSGowYmpsQ2JkdmZxVlVybFhnck11c2lZcUw2ZCtORlF2NkJlV2NMRjdzOEVNMXpVUlQrUDdlTW94MUg3QURGczFmMklDb0ZNVzVsdG9aYnc0TElTaUt2L3JyYU0yYmRGL1IvQ24vUmYrckVuNXVQcUZCcDFETlg3NmdZblQ5M3JIOHhoK1FIQ3l5M1NWRGxVdUFvcm5sN0RKUHRlN2MwZjRITFFzWllMUXA2SjBCZThVNGdpUlY4djVlUUlkNWdUbkJ6NUdqWjZaRlA4ekUvblhvekdtNmVWcUNjZkZFb1QrWk81czQ4WTBrZ3o3czd4OEwyV3VRZVdYaFdGemRhcDBScWZ0MXE1ZkFRN3U3TnVvY0g3T2VEOWNjUDgvMzl5Tm1wOXhRaForRUhoTlZxRTFPdHF3TEl4cVJVT3N2K1BnV2FjZnZad1JVdUtLcUJZMzFpRFB5NVZhWkEyakxmMUk0YzRnd0tNT3ZDRUdRRDNiYlpXUXJvTHdUQk0iLCJtYWMiOiI5MWY3NzFmYzM1ZGNhMTQ0ODU5M2RkMWNlOGFhNTk3ZTE1NDIyYzM5MTgyOWU1OTcyMDc0NTQ5N2IxNzZkNGUxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:15:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1567903427\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1318856308 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WuYcucCgl4bBOLSdLTqnajP2UNBzqFh0FtUk6GCo</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1318856308\", {\"maxDepth\":0})</script>\n"}}