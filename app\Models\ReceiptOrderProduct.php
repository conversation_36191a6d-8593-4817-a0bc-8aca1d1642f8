<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReceiptOrderProduct extends Model
{
    use HasFactory;

    protected $fillable = [
        'receipt_order_id',
        'product_id',
        'quantity',
        'unit_cost',
        'total_cost',
        'expiry_date',
        'is_return',
        'notes',
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'expiry_date' => 'date',
        'is_return' => 'boolean',
    ];

    /**
     * العلاقة مع أمر الاستلام
     */
    public function receiptOrder()
    {
        return $this->belongsTo(ReceiptOrder::class, 'receipt_order_id');
    }

    /**
     * العلاقة مع المنتج
     */
    public function product()
    {
        return $this->belongsTo(ProductService::class, 'product_id');
    }

    /**
     * حساب التكلفة الإجمالية تلقائياً
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($model) {
            $model->total_cost = $model->quantity * $model->unit_cost;
        });
    }

    /**
     * الحصول على اسم المنتج
     */
    public function getProductNameAttribute()
    {
        return $this->product ? $this->product->name : 'غير محدد';
    }

    /**
     * الحصول على SKU المنتج
     */
    public function getProductSkuAttribute()
    {
        return $this->product ? $this->product->sku : 'غير محدد';
    }

    /**
     * التحقق من انتهاء الصلاحية
     */
    public function isExpired()
    {
        if (!$this->expiry_date) {
            return false;
        }
        
        return $this->expiry_date < now();
    }

    /**
     * التحقق من قرب انتهاء الصلاحية
     */
    public function isNearExpiry($days = 30)
    {
        if (!$this->expiry_date) {
            return false;
        }
        
        return $this->expiry_date <= now()->addDays($days);
    }

    /**
     * الحصول على حالة الصلاحية
     */
    public function getExpiryStatusAttribute()
    {
        if (!$this->expiry_date) {
            return 'غير محدد';
        }
        
        if ($this->isExpired()) {
            return 'منتهي الصلاحية';
        }
        
        if ($this->isNearExpiry()) {
            return 'قريب الانتهاء';
        }
        
        return 'صالح';
    }

    /**
     * الحصول على لون حالة الصلاحية
     */
    public function getExpiryStatusColorAttribute()
    {
        if (!$this->expiry_date) {
            return 'secondary';
        }
        
        if ($this->isExpired()) {
            return 'danger';
        }
        
        if ($this->isNearExpiry()) {
            return 'warning';
        }
        
        return 'success';
    }

    /**
     * الحصول على نوع العنصر (عادي أم مرتجع)
     */
    public function getTypeAttribute()
    {
        return $this->is_return ? 'مرتجع' : 'عادي';
    }

    /**
     * الحصول على لون نوع العنصر
     */
    public function getTypeColorAttribute()
    {
        return $this->is_return ? 'danger' : 'success';
    }

    /**
     * البحث في منتجات الأوامر
     */
    public function scopeSearch($query, $search)
    {
        return $query->whereHas('product', function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('sku', 'like', "%{$search}%");
        });
    }

    /**
     * فلترة المنتجات المرتجعة
     */
    public function scopeReturned($query)
    {
        return $query->where('is_return', true);
    }

    /**
     * فلترة المنتجات العادية
     */
    public function scopeNormal($query)
    {
        return $query->where('is_return', false);
    }

    /**
     * فلترة المنتجات منتهية الصلاحية
     */
    public function scopeExpired($query)
    {
        return $query->where('expiry_date', '<', now());
    }

    /**
     * فلترة المنتجات قريبة الانتهاء
     */
    public function scopeNearExpiry($query, $days = 30)
    {
        return $query->where('expiry_date', '<=', now()->addDays($days))
                    ->where('expiry_date', '>=', now());
    }

    /**
     * فلترة بالمنتج
     */
    public function scopeByProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * فلترة بنطاق الكمية
     */
    public function scopeByQuantityRange($query, $minQuantity, $maxQuantity)
    {
        return $query->whereBetween('quantity', [$minQuantity, $maxQuantity]);
    }

    /**
     * فلترة بنطاق التكلفة
     */
    public function scopeByCostRange($query, $minCost, $maxCost)
    {
        return $query->whereBetween('total_cost', [$minCost, $maxCost]);
    }

    /**
     * ترتيب بالتكلفة الإجمالية
     */
    public function scopeOrderByTotalCost($query, $direction = 'desc')
    {
        return $query->orderBy('total_cost', $direction);
    }

    /**
     * ترتيب بالكمية
     */
    public function scopeOrderByQuantity($query, $direction = 'desc')
    {
        return $query->orderBy('quantity', $direction);
    }

    /**
     * ترتيب بتاريخ الصلاحية
     */
    public function scopeOrderByExpiryDate($query, $direction = 'asc')
    {
        return $query->orderBy('expiry_date', $direction);
    }
}
