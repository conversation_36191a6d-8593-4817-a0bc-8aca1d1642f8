{"__meta": {"id": "X6e7657df21c65c9ad6850101919000d9", "datetime": "2025-06-30 23:13:52", "utime": **********.758868, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.328813, "end": **********.758881, "duration": 0.4300680160522461, "duration_str": "430ms", "measures": [{"label": "Booting", "start": **********.328813, "relative_start": 0, "end": **********.705221, "relative_end": **********.705221, "duration": 0.3764078617095947, "duration_str": "376ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.70523, "relative_start": 0.3764169216156006, "end": **********.758883, "relative_end": 1.9073486328125e-06, "duration": 0.05365300178527832, "duration_str": "53.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45028616, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00243, "accumulated_duration_str": "2.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.736244, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 70.782}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.746374, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 70.782, "width_percent": 15.638}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.751976, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.42, "width_percent": 13.58}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1781086111 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1781086111\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1539356962 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1539356962\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-683363884 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-683363884\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-158691350 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325222314%7C30%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZpOXBRNlJSZGVnZFFOaDREZk5pcmc9PSIsInZhbHVlIjoiZW1XVmc5Z0k5REFmNys4RERERmtLdWdyVnpXZHNkV0E4UFdzWTF0cEtOYldKTkVNK1h3YmE5cHI1TXBqdU9hS2xLM0hHeXBMSlBtbnZVQlM3K1NjbE5nTFlpQmdnMUlPbjhHbkx0TERHblVYZ0R3c3pWVnd2cnBpbUdCMk9RUmJVbUNYKzJpRUFDenppZlRKcUM4NTlTWU14NzRoMXJQZVBGSTdDUzdBb1NSb29xeFRLK3EyU0JndVJ5b3M1VzhZWk9raXhUVW8wU3JkelA2UnltQTRPNGVqRXZhM1FPSW1EWEZGRTAwSW51RDRMYUQvLzJ6TklwakFYRHJhdzlocE45Y056OXdEaHdEZWxHTW9scjVBTVE1VHlPVlNjbFkvN2FzSVdaSGdtS0ZLc3lEa2ZTeGFhbVNybW14Vzk3Q2wrcE9LN1Myek0rWUhEZEFzMXhkbEhJbWlPZVNZTE1YRmFlOEhJdm1nTzRwU3ZncEJqRXBiZmNGWjB1UFF0UFNaQ1VnRExZZU5aeHhLQjQ3QU1ic2t1bjVEK05rUE4wWXlMd04yRHhjMGtka0draUk1VTFuelRVQXM0NkJzb3ZlQlhIOU5SRmxkVUI3KzVrTHh6TC9ORmRDT2hWb2kzcjg3UUU1azM2d1hlTjBES3ZGR0dRUEs1bTUxRlRoVTJIQnMiLCJtYWMiOiIzMmI0NjYxMDEzMTIxNDZkMWU2MTYwNjhhMTcwNjZjYTM1MGMxYzM1YTBjOWEyYWJiZDk0NjUxMjJiOTdkYTUyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZQSW1QbGFZQzY2TXV2MWMyOEZIc0E9PSIsInZhbHVlIjoiMEtGdjVPRnpRRmtTdXNhMmVGQnBzcUhJbXdvVDV2dDJDMmFOUStrdVBRTWc5cE9iWTJpelFPMTMwdmRWbEo3ZU00TW9kMXdycWJhQUtubkhkQmQ5VW9Sa0tWK2Q5dncrRkhoc1Jna1hZZkt5NGNHendxTVpLcDdJQm51elpuMHFkREpBeUpXaUprUGUzV3dDemRBcS8yRWVsdURPNHRYWUp6SjBWOWxFaHpSd2dGYWRKaG5qYmk4TThIcmpmd3djNVNnbW9sYXpRL2ljQlBRb1JPa2lzK3FVdXlaazZZWm1TNm9HVVV4UTh1ZHM2OTBSakNqaU9OZGZSVVNjamNtd3hmb055N1FtdlZUbGJlRjgzZEhyOFFGY2szZ2dOU0dzWjEzRW1SL2NEZjVBbVFxdDBOSlFpZnRtZkhwYWszSFRNMG9rNlg5MUZvODJBbitSR0d1alZSQkxnY3ozSlBkcWh5cWpiZ243WmpxeUlKYTRKQmJaVmdYVzlqL3RBU25jSUpFaU4xOFdtV1lVTUNLUlVFdjlHeG50TVdaY1FFd0wvRFV5S3Z0T3JuWlRmUXQyMmV2TnpSTEFJOHUyaWYrVWtCV3lieUdDbzRzN05id21VMXZhNS9IMjZhQ1NYTVVseHdGYTFEdkFCREo2RTlwaisrcFdtdkZ5bnVEMzRLS3YiLCJtYWMiOiI3OGJhZmVkZDY5YzY4Njc3YWNmZTA1ZjY5ZGIzNGVkMjdiYTNiODk1YmRiODU2MzNkYjNiNjljOTRhM2FmMjM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-158691350\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-632314656 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">H4YK57QUXyJ63T0CnkmwYWGCllGQ9A8V3d5cZCUG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-632314656\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2000801030 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:13:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZiZU9tSDJKbWRFSXBkM2V0WC85V1E9PSIsInZhbHVlIjoicDZIdGpXclJXMXZheVVlZERzK3p5ODQ4YnhERFhYMkhWaTc1WFZGaWdPTTBZVWsxa0pQTGZHSjJEM1JXS0dnbHF6Z3drL0hDUHlieDF4UzlhbHpyWmxTdy9ncHZ5Rm1tR0NNYmJBZFg1YmxwekZjQkFoeVdaL0tWTjZiVUJVUS9WbGRHRDZNT1NlQThHN2dtTVdsL3RHTU9wV1JZRG5oVjU2bFlvVUV2WjhJWG5BdG8yNFdRc0pTUVhiWDVMTE9BOURtNUQ5OHVlSXBIbnBwZFlDZ1JKSVpkNTRwRjRsVjBRRHV2b2Znek85YWNxQXBCL1RZWnNaNWh2emVkYmE0cy9sRFJOL2Y3dnRXNE9RTlBlRFgybUgwbndJSW04THVkeVVJdW5VZDdQQ2FTWW9Pby81OEQ4cHZuUzE2NHZ1Q3o5dTdoUFNKSnQvZ05hRWRrYzlNNFNZMy9BbkpTRVpmbUp1RnlvVDNhS1ZNOHpMNjVMWGpQVHl6UWQwUFdhL29KYTE4QkNweEZKSVBuK1VhYnBpUkQrWDNtVXUyRlRheUdIWll6NHFMZTRQUHE1Z2Z4Qld0eHlTSS9SQUk5MjdjT1JPZC90UWR2UTgreHVwRFBpUmVmU3A0Q0dsVUJBTnVHcHVDMlZwOGlVdGJBVHpCNzJDRnQzUVB6QUhUNU5NczkiLCJtYWMiOiJiMDdhNGJjYWUzYzExZjc3ZTdmZWJlZTVkMWFmZmQ1YjA4MjI0ZDdlMWUwZjMxYmRjNjBmYzhjYjYzNzhiNGNhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImNFeURjdWFPNENDMTdwbnUyRmtORFE9PSIsInZhbHVlIjoiT2l1SW9wYlQrQysyeVk5RVZ2YS9rcm03RUdPQVZ0aElYaUl4UmFmZkFQODgwOElNckZKVk9iMlkzM2J1ZjlNZ2ZjK1FTUk1neFQrSmN0ejdTM2JhU1Y4cHAxM0Q2QUQzY0VpUzhHOGdtK3VPRHdOcmpxUVhPRWlmcUlud3MzSnQxaUFrenpzdjVXbW1EczBEYW5sRlVWNzU5RkthOG1xUnh1bmwwdk1GR1MrNDZPU2ZCcCs2N2xEbXBHTzZuN3lBWDBJTmduZ3ZRd2sxZytZSmdKNWdjY2k4anc2NFNRNHR2bUdxalJZelBzUytLeURSVEhjN3hhNCsrMGRMQzR2dUNGOFdpWmxvMGZmemxGT0J6aTh0ZElIQ1l3TCtlY1JOTjI0TTZ2bkZ2Q0hVNERSWWZsbzlJUDNsaFI1SG5DWExSV3VVTWU1WG95NGhTdkdhVlMxbkY2cW1mK3N4MkpjMXRWMFpPcGdGWUg5N0dFelNKTHU1bmVYNnZXN2RzbDFFS1FpdlNoVzBvN2dxdkRJdHhaUm14ODJEN243alhSRkdvRGc3VzNBa21jUDFCSjF0VGYxbjlQYWx0RHRXZlR1aVplalhCQ2dUbjd6SEFCajFLd0NJVTBCdzUxRVVrSk5JVFFhVG5Kcm0zT0VrczBHL3pnV3lIU3lvR2o1WllSREIiLCJtYWMiOiJjZGY5MTA0NDUzNDNkODAwZjYwNzUzNWJkMzJiZjg4ZGE4YWJiMGYyMDM0ZDU5OGRlYzM3MTRmMWM5NWNiYjVkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZiZU9tSDJKbWRFSXBkM2V0WC85V1E9PSIsInZhbHVlIjoicDZIdGpXclJXMXZheVVlZERzK3p5ODQ4YnhERFhYMkhWaTc1WFZGaWdPTTBZVWsxa0pQTGZHSjJEM1JXS0dnbHF6Z3drL0hDUHlieDF4UzlhbHpyWmxTdy9ncHZ5Rm1tR0NNYmJBZFg1YmxwekZjQkFoeVdaL0tWTjZiVUJVUS9WbGRHRDZNT1NlQThHN2dtTVdsL3RHTU9wV1JZRG5oVjU2bFlvVUV2WjhJWG5BdG8yNFdRc0pTUVhiWDVMTE9BOURtNUQ5OHVlSXBIbnBwZFlDZ1JKSVpkNTRwRjRsVjBRRHV2b2Znek85YWNxQXBCL1RZWnNaNWh2emVkYmE0cy9sRFJOL2Y3dnRXNE9RTlBlRFgybUgwbndJSW04THVkeVVJdW5VZDdQQ2FTWW9Pby81OEQ4cHZuUzE2NHZ1Q3o5dTdoUFNKSnQvZ05hRWRrYzlNNFNZMy9BbkpTRVpmbUp1RnlvVDNhS1ZNOHpMNjVMWGpQVHl6UWQwUFdhL29KYTE4QkNweEZKSVBuK1VhYnBpUkQrWDNtVXUyRlRheUdIWll6NHFMZTRQUHE1Z2Z4Qld0eHlTSS9SQUk5MjdjT1JPZC90UWR2UTgreHVwRFBpUmVmU3A0Q0dsVUJBTnVHcHVDMlZwOGlVdGJBVHpCNzJDRnQzUVB6QUhUNU5NczkiLCJtYWMiOiJiMDdhNGJjYWUzYzExZjc3ZTdmZWJlZTVkMWFmZmQ1YjA4MjI0ZDdlMWUwZjMxYmRjNjBmYzhjYjYzNzhiNGNhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImNFeURjdWFPNENDMTdwbnUyRmtORFE9PSIsInZhbHVlIjoiT2l1SW9wYlQrQysyeVk5RVZ2YS9rcm03RUdPQVZ0aElYaUl4UmFmZkFQODgwOElNckZKVk9iMlkzM2J1ZjlNZ2ZjK1FTUk1neFQrSmN0ejdTM2JhU1Y4cHAxM0Q2QUQzY0VpUzhHOGdtK3VPRHdOcmpxUVhPRWlmcUlud3MzSnQxaUFrenpzdjVXbW1EczBEYW5sRlVWNzU5RkthOG1xUnh1bmwwdk1GR1MrNDZPU2ZCcCs2N2xEbXBHTzZuN3lBWDBJTmduZ3ZRd2sxZytZSmdKNWdjY2k4anc2NFNRNHR2bUdxalJZelBzUytLeURSVEhjN3hhNCsrMGRMQzR2dUNGOFdpWmxvMGZmemxGT0J6aTh0ZElIQ1l3TCtlY1JOTjI0TTZ2bkZ2Q0hVNERSWWZsbzlJUDNsaFI1SG5DWExSV3VVTWU1WG95NGhTdkdhVlMxbkY2cW1mK3N4MkpjMXRWMFpPcGdGWUg5N0dFelNKTHU1bmVYNnZXN2RzbDFFS1FpdlNoVzBvN2dxdkRJdHhaUm14ODJEN243alhSRkdvRGc3VzNBa21jUDFCSjF0VGYxbjlQYWx0RHRXZlR1aVplalhCQ2dUbjd6SEFCajFLd0NJVTBCdzUxRVVrSk5JVFFhVG5Kcm0zT0VrczBHL3pnV3lIU3lvR2o1WllSREIiLCJtYWMiOiJjZGY5MTA0NDUzNDNkODAwZjYwNzUzNWJkMzJiZjg4ZGE4YWJiMGYyMDM0ZDU5OGRlYzM3MTRmMWM5NWNiYjVkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2000801030\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1809141841 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1809141841\", {\"maxDepth\":0})</script>\n"}}