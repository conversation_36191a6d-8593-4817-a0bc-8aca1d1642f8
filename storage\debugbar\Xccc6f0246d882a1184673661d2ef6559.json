{"__meta": {"id": "Xccc6f0246d882a1184673661d2ef6559", "datetime": "2025-06-30 22:39:44", "utime": **********.536156, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.077247, "end": **********.536175, "duration": 0.45892810821533203, "duration_str": "459ms", "measures": [{"label": "Booting", "start": **********.077247, "relative_start": 0, "end": **********.477499, "relative_end": **********.477499, "duration": 0.400252103805542, "duration_str": "400ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.47751, "relative_start": 0.40026307106018066, "end": **********.536178, "relative_end": 3.0994415283203125e-06, "duration": 0.05866813659667969, "duration_str": "58.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45026504, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00293, "accumulated_duration_str": "2.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.511626, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.259}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5220058, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.259, "width_percent": 14.334}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.528131, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.594, "width_percent": 17.406}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1191797855 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1191797855\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-964721299 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-964721299\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1363456433 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1363456433\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1175271471 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/branch-cash-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751323179076%7C12%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImNFa0xkd1c0TERyMHUxcUdDYVh5c3c9PSIsInZhbHVlIjoidWhvR3hWL1ZDaGpnU3dNYVd0Y2ttc0hpbDF5cnYwSU9ndkNjMUdhWG81SkFVZVgyMGNyelVIejE0Y3ZDdlZXc3NMOVJwc0FLMWxyUDZLclZxRXoyRHhwRjVTaDdFVVBZM2ZtSkZpOW9ZdWV6RVBiRkpJOTkzRTcveDhPU3REbm5HdTQ2dE5oSXZPUFZhcGY3SE5yZnMrcERycDI3OTIrNWNXTHZxbTBWQnA5SUpFcExKVFFPS09GV2wzWFVSdjBuaWpzY01KWEx5L3kzYzhVUVVZL1BacjZ3TFZZZW1taGJmSU9wYkZJMCt3cyt6VDFVZ2tMVkxJelAyS08xUUozTHhnQ1VJVGNpb25sY2lGV2FXeTlCZjNETnp6YVJxOGFUeE9VWW1ld1Zjd2tFbklqbG9xV3RWNVlZTjRrM2o4cjZITXZvZjJWc1NBOUhKNmtxYXM2WXU0NW5jNTFXNUxLVDlnR0FrdnR1Ly8zU0ZVM1NpUEY3bmlxbVlJWmFEaHp1MUdkeWRHdW4zQ01mRFNybFZmbk5yUzlpVjQrbExvQjhRTDFuNWRPMXpKMkhNWXdJdWpvOGVsdVBxUTNNZEVkQm8rSjY0YzVXamk5MUk1aU96Wk12d3AyR2IyM0ZhRzZoMWhyV0U2TjlzV2tMUzBGTkRwNzEzSHQvZ2lUeG9MOFEiLCJtYWMiOiJkZWFmYTY1M2EwODRiZjk5NzI3YWMyMWQyZmM1ODEzN2JlODFhZjgwNzk0MDMxNmJjZTM0YzYwODFhNDk0MWExIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ilp0MFYybUFZR1N4RllOUVlnVDMyMVE9PSIsInZhbHVlIjoiSnVtVjhaVmc1WC9qSllsQitwTWx3eURLRkpXb3Vlblc2RzVCYlFvUVNzczJvVDcrQkluVnNRQ3FCWWpGTElyMWo5WDJsWFpuVU9vUWtGLzZlTkZuUHdGaDhvOUlVNWhKR2J5M0hyellzUFlhclJtelpUVDNFSnNGWG43endYMllRS1kyRjRhQmFKV1BYdXYrK3B3cWZNRVFTSEZpK0QvWTZIdlB5RHkwblErR0U1UzRzcldxOTYyWjV1ODZpUVhwM2FUY2NQVEMzTEhXL09XSXFPbXJWaWRSbFRIVWZBNkorTFZqM3ZReGIzYVdUZC8yUE9odFI0blFwRk0rVm9jR3FoaFBKWWwzb285UFE3QVJWNFVSNnpvQ2xIaWtFZU5wWXBvMitzcWQrUC90U1hUdUo4R2tuTGVwMmxvMzc1blE1UG9LdHg5ZDNKN0lFK3FmdlpSaXEzTE5NbDFLeVpEd2hIMVN5dENzOHpuRXRabEt2a1hQNUYzcUNvL1ExN1BaV1pPSjdRTDd3SSsyTUpybFhjZlJaODhyeEVnMXErZHFOcWh0em1mTldEaEJYM1p4bG9CRjNWblpTSG10NVpxa2lJTkRjb0R6eGl2aHNXdkdtQ0FSa2sxeXJ0SWQzdkowL2M2dFNnT20xdFJwdlF1VnhRYmJnYngya1hSb0dyQlIiLCJtYWMiOiJjMDBkMTA1NTdjZjFjODBlZjdiZDIxZmJkOWJhMzk5ZTk3NmMwYWM5ZTI3Yzc2ODAyM2RjYTE5OGYxNWM4MzU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1175271471\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1222845486 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0M1K0sdNadPD2LVrnt8LmBw227nA9F1U5e3ROaJK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1222845486\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1198764733 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZXMU1TeVE1Z05SUmsrT0pIT0xuL0E9PSIsInZhbHVlIjoiby8rZHdEU203U2ZrSzQvL2xmdFlzL3cyd1V1b2NRMzJOL3BRbVpKcWRBOXVCRXFma3BKVExoYXR2S3dYRkQwVUxyenJiNzJYazZYalBmQmtoSTZka2MyMlNaUjFZSFQ5WGlXbkZnT0FTSkY0QlFFTm1vUDEvVUV4MUExTm5QeXpqbGVERDFIdzNkOHNSL245U0V3QzVvMzF5WjhXMTcrY1Iwbkkzb0ttc2Z0TXYvTjdBWFkzZEIyaVRTcnpud3RBRTZib01HNUFKNCswTjdqYk55djFnV3MzRGhlSWw0azJUc2NGQm9NRGt5QXZPNFdVWlozZVNEcko0NEN0US9tL3BQMll1OUEwbXkwRjJhRE1kdlBJaGNmbGlORjhaMVE0K3lxMlhQOXdtRlE4K0VrdnBlYnZpd0VIWnlrMDV1S2Y5QURLLytBek5uT2ZKSDgrczJZQUt0eWZiT0NyK1ZTZ2RmRDdlUFJ6MGRrNGcxbGJsNzV1a3RsSW04Zy80V2gyUnFVbmZZTldTQzQ1b1FWelRBdjF5UDlCUWhDZlYxTC9tMUR4VyttQ0RxMEtqUVdSUmFIbklDVWE3Zmt0RHhnaHZEbWcwL3FPUDJJRzZpUXEvVkEvT2hodnRZSnY0Zi9hMFB0RzRyM3F0N2hyRndwTFE3dVh2YnpCUzdwQk9WRjciLCJtYWMiOiIzMzdhYzVlMGIzNDE3MDM2ODNjMmEwNzlmNmJmZDVhMTZjOWM2OWRhNDBhZTY5YmMwMmMzZWNjZTZhZDFkNjEwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZmWUZabXhLaldGdkVJZ1kwWkVxUnc9PSIsInZhbHVlIjoiRGNIT2VBakwzcktqdTMzbnRiTXJwclllZDRWWnJ0a2FPaFowTHc3eUFwTGJBUzJzbmRpdUI1VERYTzBMSHRQcVJSWUFvZnRRaVBRZzdMVHA4ZVFTVlF4aUt1VGJzb2xUUGxuMk44c0V4MTdDL0g0b0t1bGljKy9IMjg0eDhVQjRROU9EVmZ5RWxncm54dE41YnFHN2VFTUFMWHZhd2k2cjJUS2RxWHB4cnhGVGlsQSsxOElVcm0ydktWQm5WWGZWelJQVVJybytNV05BNzkvTHhYOElNUGJ2UllYRDczTEdJeHVNUFlnOU5oTzJzbjlqSjNsZ1Z4MXd1OTNrcUZDWUtOQWxXeUllQk0wSVRHeDJpM3ZYT2tEeHdmNmk0ZmJldmFmT2JKMnhOUk55UWpKTlcvN0JLZDd3Vm1qSEgvSXg0QVpPRWlNMzRrUDJPNDlMZkZEMTJuS0ttRnM4WHhnNFY5VTNIbHg3cXRXZ21zWm5jMXgyR2ZaWFhpK1N4RS9Zb0JzRWIzWXdWTnFPODRYUzlnM2JUSm5HS2tHYUlGM0oxQzJaVXZuVlNOZTdLUUFRMHdrUjBFNW9kdmwzWnlucVBDR1plR0E0dXpFaHhORmQ0OTJzMnFPV21IYnhNRE1qTzNUOXlxYW5pSlcwS3dZYnV6QXc1djBGaVlBOFJLVU0iLCJtYWMiOiJjNDM4MTIxMTRiMTA3MjQ2NDFlZTUzMGNlYzRjOTE2MzczODhhNTk2ZTExMmEzN2UyMzg5ZDczOGEwMTNkODQwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZXMU1TeVE1Z05SUmsrT0pIT0xuL0E9PSIsInZhbHVlIjoiby8rZHdEU203U2ZrSzQvL2xmdFlzL3cyd1V1b2NRMzJOL3BRbVpKcWRBOXVCRXFma3BKVExoYXR2S3dYRkQwVUxyenJiNzJYazZYalBmQmtoSTZka2MyMlNaUjFZSFQ5WGlXbkZnT0FTSkY0QlFFTm1vUDEvVUV4MUExTm5QeXpqbGVERDFIdzNkOHNSL245U0V3QzVvMzF5WjhXMTcrY1Iwbkkzb0ttc2Z0TXYvTjdBWFkzZEIyaVRTcnpud3RBRTZib01HNUFKNCswTjdqYk55djFnV3MzRGhlSWw0azJUc2NGQm9NRGt5QXZPNFdVWlozZVNEcko0NEN0US9tL3BQMll1OUEwbXkwRjJhRE1kdlBJaGNmbGlORjhaMVE0K3lxMlhQOXdtRlE4K0VrdnBlYnZpd0VIWnlrMDV1S2Y5QURLLytBek5uT2ZKSDgrczJZQUt0eWZiT0NyK1ZTZ2RmRDdlUFJ6MGRrNGcxbGJsNzV1a3RsSW04Zy80V2gyUnFVbmZZTldTQzQ1b1FWelRBdjF5UDlCUWhDZlYxTC9tMUR4VyttQ0RxMEtqUVdSUmFIbklDVWE3Zmt0RHhnaHZEbWcwL3FPUDJJRzZpUXEvVkEvT2hodnRZSnY0Zi9hMFB0RzRyM3F0N2hyRndwTFE3dVh2YnpCUzdwQk9WRjciLCJtYWMiOiIzMzdhYzVlMGIzNDE3MDM2ODNjMmEwNzlmNmJmZDVhMTZjOWM2OWRhNDBhZTY5YmMwMmMzZWNjZTZhZDFkNjEwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZmWUZabXhLaldGdkVJZ1kwWkVxUnc9PSIsInZhbHVlIjoiRGNIT2VBakwzcktqdTMzbnRiTXJwclllZDRWWnJ0a2FPaFowTHc3eUFwTGJBUzJzbmRpdUI1VERYTzBMSHRQcVJSWUFvZnRRaVBRZzdMVHA4ZVFTVlF4aUt1VGJzb2xUUGxuMk44c0V4MTdDL0g0b0t1bGljKy9IMjg0eDhVQjRROU9EVmZ5RWxncm54dE41YnFHN2VFTUFMWHZhd2k2cjJUS2RxWHB4cnhGVGlsQSsxOElVcm0ydktWQm5WWGZWelJQVVJybytNV05BNzkvTHhYOElNUGJ2UllYRDczTEdJeHVNUFlnOU5oTzJzbjlqSjNsZ1Z4MXd1OTNrcUZDWUtOQWxXeUllQk0wSVRHeDJpM3ZYT2tEeHdmNmk0ZmJldmFmT2JKMnhOUk55UWpKTlcvN0JLZDd3Vm1qSEgvSXg0QVpPRWlNMzRrUDJPNDlMZkZEMTJuS0ttRnM4WHhnNFY5VTNIbHg3cXRXZ21zWm5jMXgyR2ZaWFhpK1N4RS9Zb0JzRWIzWXdWTnFPODRYUzlnM2JUSm5HS2tHYUlGM0oxQzJaVXZuVlNOZTdLUUFRMHdrUjBFNW9kdmwzWnlucVBDR1plR0E0dXpFaHhORmQ0OTJzMnFPV21IYnhNRE1qTzNUOXlxYW5pSlcwS3dZYnV6QXc1djBGaVlBOFJLVU0iLCJtYWMiOiJjNDM4MTIxMTRiMTA3MjQ2NDFlZTUzMGNlYzRjOTE2MzczODhhNTk2ZTExMmEzN2UyMzg5ZDczOGEwMTNkODQwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1198764733\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2017359751 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/branch-cash-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2017359751\", {\"maxDepth\":0})</script>\n"}}