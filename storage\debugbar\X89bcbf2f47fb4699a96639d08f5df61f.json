{"__meta": {"id": "X89bcbf2f47fb4699a96639d08f5df61f", "datetime": "2025-06-30 23:13:31", "utime": **********.075878, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751325210.658873, "end": **********.075897, "duration": 0.4170238971710205, "duration_str": "417ms", "measures": [{"label": "Booting", "start": 1751325210.658873, "relative_start": 0, "end": **********.029827, "relative_end": **********.029827, "duration": 0.3709540367126465, "duration_str": "371ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.029837, "relative_start": 0.37096381187438965, "end": **********.0759, "relative_end": 3.0994415283203125e-06, "duration": 0.04606318473815918, "duration_str": "46.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43747768, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00199, "accumulated_duration_str": "1.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.056882, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 87.94}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.061508, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "kdmkjkqknb", "start_percent": 87.94, "width_percent": 12.06}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1718694779 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1718694779\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1982805454 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1982805454\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325209071%7C27%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImpMa3YwMGhkV3VzeFFnTkgzMFU1YVE9PSIsInZhbHVlIjoiaVpyWXJhclpvZ0VrQkI2U3pkNHV3d3EwQkpMUnFIeTMzTytsMGM0N2tqNHNPRHl5bUdsMGp1MHdtTHYxbWpMcmVnOTRFcW82RVkram9JOE4rNkk4SVRtOGRsKzRFeWtJcTlkb1pOcmhtdzg3a3NKR25sQ0ZrdFI3WTBOV1ZKMThCcnJGYXNDd29tQ05kQ0hNUUExemFIM1VzTFd0S2dOQm0rdmtqQm9XZWh2Sm5XNmNpUU8zNS9tWTg0dmpCdkZOWFZGUWlYQVd6Z2tVM3N1K0Rzc1hmZERnRFdya1JVcUhUVFRQTmtzSmJLRSs3UXAvN1dhQmFGNDlEeDFnSjVTclo0QzBrZWY4YzI3ZlFvV2dGVDRBay9DcUxnZmFGU0ZQLzlKS2Q2YXN1aFZsamQvZXZxdU1ZbkppVlhIUTNmMC9ZRm5PS3RDZW5PdDNpZ0NDV0ZLUzhqalVMNFB0NzdURU9tb3JZWE5QRkpLcGs0Y3Y1bFlVdW12Z0ZWV0JNQUl6WFZvaW9mRnc3Q3RmWkRYZDJrN08wT1FtOXZxVFZOcFJIVjlYQ1FIWHpVSTA0K2N2aEJJOW9QeVhsWGlvaVVPOXA5cXdaeVdibGpPMFM1emVpLzRSWHovQ3pmRUVuMm45WUNyMFFpSVdBSFpuK2tCVW1zK2Z4b0tXOTRMcDZTb00iLCJtYWMiOiI1ZGY4Y2YwMThiODcwMTE1NjFhYmU3N2VhNmE2OGM4YjRlNDlmZTdmMmFlM2FkMGVmNDQwZmE0MDNiNmUxZGEyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IndnaUU5SkF0TmpTNCtWVUg4ZVdPU2c9PSIsInZhbHVlIjoiZklGMnVMdE5iejdoOWJKV1FSWmNBUFhad1djcml0cWxvRDFTcHZrUnNxNkZFRElQcEZTYXVHL3ZnbStGRnp1Q3pmQmN1bm9WdFllS3VLOFp3UlZHandMRmcwZWtaNCtyekY1WWFzNW9ReGZ4czIzYnFTbkMyTVA4bFV3MlV1YjJ2a3o3QzlPb01FcFdRVUw0TXlKOWlVZnpMNzRqUk5TMEYwMllMNXZ0VUlFQnNrZngrT083NHFSSmRVWENDTzhlZ3ZlVCtJMERCMTF2bU9zL3c3eklzdDhYTmdLLzRNM2tybDZseEtwUHhGUnhJRHZhRGo2U1pjU3BZOTNhU3Job29EUEZ6Zys5SVFzTFZDNHBON2hSeHdVVmdKc2t0K3kwaFhodW04eFdmMDVXeHEvaGx4aWFFZHF4VDdEd3hDWlI3ZnFuWExtWklWRWREYlpTZVlVYTlEOGdIN2pCOVE5WGdlUFNxaE8vNC9UQWppSGxvSnc0VEEyU3QrWGZ6VWZ5ZlVId0wxdkJwbWdoOHp5QXFNZlhjYTRRUFZIWlp0aU9JcGZ4TDNMU3lKeWVndmdOU1ZlcXA2SUtPV3czYmVQODB3RzkxbzBzcXJ2UlpxSi9DYWdrb25XRWJobk5wblpyVFJiMTBiK3VISjFIM1F4UHVxZU5OekY3R1ZvQkpXaVEiLCJtYWMiOiJkMzVlNTAzNTU0YzIzNTBjN2EyZDMzZjI4NGRlOGY3NjU4NTM4NTJlMDQwNjJkYjU3Njc0Njk3MWY0NmU1MDZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1911595224 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0aUeGT9vcZQoKvixhqnXLFDjX39UXyfabqLnLub</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1911595224\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1711497019 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:13:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZJOGJsNUxRbHJOc3Y1dmJWWExKRGc9PSIsInZhbHVlIjoiM3B4czY2d0t2TTgwZlV0YzVtOFp1SDNXSDFDMnY1MkNLcGtWU3Iva3paRGJOaXVhM0dTOTJvMTQ1N3gvRlRURmRmSFdYeWU3SkFLVDA5bjhnMllOTFpYUE1BaUpUMmYwSU1uMjZUMG9kaFJ6dGkwVEtqQ2dZbGY1c3ZGNmw0OTlLMVkzTkE0ak91WGprTTViN0EzaHE1c3JsYXd2aXQ5Y3NKaDB1ZmVkWDExRjU2L00yUEhRYnFwTFk3aG5zTmdoMjQyc3VhQW5UOTM5QStVSG9NYVhMekZZU0tCVDZxVDlhaDF0TWdsYW5FY2FGbWg2Q05LR1NHUG9rL2FiQUxCKzVEMjVxd250czdObVNZZndISVB0aVZYaHhYbHFpZVFYbzdsMnAwWEZhN3RhMG1obENrdmJxeWx1UUJ1NXFjdHNtVzFOUmo3RXJwaVRUWGR1NHl0Zkg5MUkzKzhOZG10VHI0b0tUcXYySTZ5RXN1WVJuQy9WWXk5OTduTXM2VC9UWm5mZGpEazA3Ym93QWFyVGlQR3k2dm51L3BBWno2SzN1TnVob1F3SWdua0dOQjA0NG5pRisvZHRiQzB2STREZXBHZ3Rsdm5GS1VLM1VjK1NYRWJlTlA3eVBPU1I5Q004Q3h1K3RjakdjdVVleUZCUDc1TTBoVXdKWVFxVDdxcXQiLCJtYWMiOiJlNDczYzQzM2NhYTJjZDE5NDFkMmU5NzQwZTI1ODgyNWMwNmJhYmUyYzFlNjQ1MmQwM2QzMmVhYWUwOTQ3MzY5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjBJMk0yb0piVmowTTdvSjFSbW5lYmc9PSIsInZhbHVlIjoidC9XTDlZaGx3N1VISmExcURjQWRQdk1QODB6ZWMzdURHYUUxN256dWxMOVdlL09kUlpPYmFXd24wQUVtdkRWeWZDYkV6bHhMUUVPUkI5c21aVUxqM1B5QkdlSGxFSTc5anM5RjUvbGRHRHQ2T1FIcVptKzlyczFwMUtBNEFFbEJwaXVjTFJ0TGVhZkF3ZlRTQ2ZQVDU4TnpNZjBYRTVzREM0TUwzeGVSVDF6VXk1YnZxNVhEbnJROGRnaHkvcnFodnNmbVVTUXRrY3p3SCtCVU05V1FXUEJGb3dETGxmUUpGbjBDWm5uSk10MVA4V1htM3Q4RTMvMStIdjJaVWlaWUVPbUlkalZjcnNGcmNWblNEUzg3OTg2NnprRXE2V3k4WjU1MDBkYTRNdUcya2w2MVRHQUp4eU1qSEwyMVR2RGV5MDh3MHVYY2VndXBFQjhaNWhWWVdiUTRPYXc0YjJpQ25TT3M3TnMrSXpQbytEZDlENFR6YjUrLzRMTFpIL2pQVmRVdG1XR0p1STBzVjV5Q05KRjZiaGdOVmZWbVFpSmpOTFE3ZUJhTDdFcWRPMEs3eXMvWC9weHMzZjRCeHJmMmwrTlEyMUlETTlhenYwbDdPQ2Exb2x4bGtCeTdIcjl1ZlFCY3V1d0wzQUljVWRGeURMWmdhdk5wTkpTVytGeUQiLCJtYWMiOiI3MjYyMDY0Mjc5MmQ4ZmJmMDFiNjFiN2QxMzM5Mjk4Y2NjYjdhZGNjYjkxMTdkNzdkNjk1MjE3MGIwZTcyZDUxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZJOGJsNUxRbHJOc3Y1dmJWWExKRGc9PSIsInZhbHVlIjoiM3B4czY2d0t2TTgwZlV0YzVtOFp1SDNXSDFDMnY1MkNLcGtWU3Iva3paRGJOaXVhM0dTOTJvMTQ1N3gvRlRURmRmSFdYeWU3SkFLVDA5bjhnMllOTFpYUE1BaUpUMmYwSU1uMjZUMG9kaFJ6dGkwVEtqQ2dZbGY1c3ZGNmw0OTlLMVkzTkE0ak91WGprTTViN0EzaHE1c3JsYXd2aXQ5Y3NKaDB1ZmVkWDExRjU2L00yUEhRYnFwTFk3aG5zTmdoMjQyc3VhQW5UOTM5QStVSG9NYVhMekZZU0tCVDZxVDlhaDF0TWdsYW5FY2FGbWg2Q05LR1NHUG9rL2FiQUxCKzVEMjVxd250czdObVNZZndISVB0aVZYaHhYbHFpZVFYbzdsMnAwWEZhN3RhMG1obENrdmJxeWx1UUJ1NXFjdHNtVzFOUmo3RXJwaVRUWGR1NHl0Zkg5MUkzKzhOZG10VHI0b0tUcXYySTZ5RXN1WVJuQy9WWXk5OTduTXM2VC9UWm5mZGpEazA3Ym93QWFyVGlQR3k2dm51L3BBWno2SzN1TnVob1F3SWdua0dOQjA0NG5pRisvZHRiQzB2STREZXBHZ3Rsdm5GS1VLM1VjK1NYRWJlTlA3eVBPU1I5Q004Q3h1K3RjakdjdVVleUZCUDc1TTBoVXdKWVFxVDdxcXQiLCJtYWMiOiJlNDczYzQzM2NhYTJjZDE5NDFkMmU5NzQwZTI1ODgyNWMwNmJhYmUyYzFlNjQ1MmQwM2QzMmVhYWUwOTQ3MzY5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjBJMk0yb0piVmowTTdvSjFSbW5lYmc9PSIsInZhbHVlIjoidC9XTDlZaGx3N1VISmExcURjQWRQdk1QODB6ZWMzdURHYUUxN256dWxMOVdlL09kUlpPYmFXd24wQUVtdkRWeWZDYkV6bHhMUUVPUkI5c21aVUxqM1B5QkdlSGxFSTc5anM5RjUvbGRHRHQ2T1FIcVptKzlyczFwMUtBNEFFbEJwaXVjTFJ0TGVhZkF3ZlRTQ2ZQVDU4TnpNZjBYRTVzREM0TUwzeGVSVDF6VXk1YnZxNVhEbnJROGRnaHkvcnFodnNmbVVTUXRrY3p3SCtCVU05V1FXUEJGb3dETGxmUUpGbjBDWm5uSk10MVA4V1htM3Q4RTMvMStIdjJaVWlaWUVPbUlkalZjcnNGcmNWblNEUzg3OTg2NnprRXE2V3k4WjU1MDBkYTRNdUcya2w2MVRHQUp4eU1qSEwyMVR2RGV5MDh3MHVYY2VndXBFQjhaNWhWWVdiUTRPYXc0YjJpQ25TT3M3TnMrSXpQbytEZDlENFR6YjUrLzRMTFpIL2pQVmRVdG1XR0p1STBzVjV5Q05KRjZiaGdOVmZWbVFpSmpOTFE3ZUJhTDdFcWRPMEs3eXMvWC9weHMzZjRCeHJmMmwrTlEyMUlETTlhenYwbDdPQ2Exb2x4bGtCeTdIcjl1ZlFCY3V1d0wzQUljVWRGeURMWmdhdk5wTkpTVytGeUQiLCJtYWMiOiI3MjYyMDY0Mjc5MmQ4ZmJmMDFiNjFiN2QxMzM5Mjk4Y2NjYjdhZGNjYjkxMTdkNzdkNjk1MjE3MGIwZTcyZDUxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1711497019\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1222744879 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1222744879\", {\"maxDepth\":0})</script>\n"}}