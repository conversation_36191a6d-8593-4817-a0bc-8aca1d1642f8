{"__meta": {"id": "X25ada104e45468f21cf9bc5873ebd17a", "datetime": "2025-06-30 22:40:09", "utime": **********.334587, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323208.902439, "end": **********.334601, "duration": 0.432161808013916, "duration_str": "432ms", "measures": [{"label": "Booting", "start": 1751323208.902439, "relative_start": 0, "end": **********.279783, "relative_end": **********.279783, "duration": 0.37734389305114746, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.279793, "relative_start": 0.3773539066314697, "end": **********.334603, "relative_end": 2.1457672119140625e-06, "duration": 0.0548100471496582, "duration_str": "54.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041264, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00311, "accumulated_duration_str": "3.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.30949, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.344}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.319982, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.344, "width_percent": 20.9}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.32713, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.244, "width_percent": 15.756}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management/25\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-925774440 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-925774440\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1068279589 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1068279589\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-885321003 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-885321003\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-44368107 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751323200249%7C15%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZrOW5yN1B6TWRmd0J5ZEVTVURTOWc9PSIsInZhbHVlIjoiQ0J5Y0lrZkRYcFBWVXkvNzRONS9COUk0Tll6bzFUNzhhUmc4YzJsS2ZBOVpta0VvdXJMVjM1Uy9uTER5VHFGd28vNW5BMmRqeFZWWGRNT0luc3RFdVJITG5jQUdtemJSNGRxR2xycVkvRlNyTzNxeXM2WkwzbVdIMVNROWtOZTA3cDdEVFRwTUFqQjErd0lQVlFGanFyaFoyR1M3QUFOZU5lOHQ1czN2ays1UW5ra2s3WkVMejhrZWZaamM1M3YxR1h1UkQ4d2NIZXQyenlQVFdocCtwVlhYV1ZMNkNpd3NYaGxWcEJpQm42eDVINldJQWFlQ3gxdTV6WVgxem1rQS84MzZsZWFlY3o5S0Rmc2MyRFZhZndZeGhJaktFQTMrZThudDJ6NnJCZkcxWHlsZ3U0Mml6NkJ0VER3NTZwOXUyYUtOdVFFb08zaVhqK1VCNnZlT0RvSjcwL2ZmN3FreEVxcEpobDhVME1CWHpjZFFnSXBUMU1abWZmcEYwKzFYVUlVV0g4dzh2ZWM5UjBab2NRZ0xRQlg1VnA4djVZZVc1b2pCcUVHMm5sdnUxMExGMWk1MCtHWDVnYXFRYkJwSFIydTFPRUluR1dGSVJpcG13VzhDbkZWZGlUSEJwN3c4N2dYdEE1QW9VQ0RmYnFJekJhMWlheisvd3lKT3NrR3ciLCJtYWMiOiI4YjhjZTU1NjI4NzNiYjdiYWI0NWE5Yjc4OGIwY2Y0Yjg5MDYyNGM1NzQxMDgwYzYyOTQzNGQzZDczNzBlNDUwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Imltd3A3RG8zZzdzeDlEWTFoUGFrZWc9PSIsInZhbHVlIjoiVlFxZ29QeGNFR1dMdVozUmkzdEdzV3JMbTgycFVwVVdSUVhlVDR2amUxNUtDYjUvcWUrd1pGdmZFallsUnEvbnJocjdqanFoQ2RrdUp2aFA4UWZ3NkJuZEprWUlJQnlYc3pDaXErR2J2U3RKMFhWeW4xR3BUQkNKZFdiQzhGb1dvaUc3VFFnS3dSNGtuS1dhZi9WRGJzaTNGSVd4dGcyc1VmZFhqMmQ3NzJtTnhOWURDeWFIdHZXQzRrOGFnajRRZEFOckVDYXlZelFjMGdzWGhWVDFNUGQ4NkttaHlFUjdBUy9renFqNGV1MlEzaHBWVFh0aW1zZlFGUWJVNmxPSVNFVXBoSjBqcnRpT2tYeG1VTUN5aWZhR0gzbzVDSEFtNHo1S1hWQng3bUszUjdhTmloeVpZekxwUmcxTmtXK0F0SWJQbVpGNDFRTG1mcVMrTW9SWllrYjZRSHpMc0IvUzdHeWF6UzlVcVNpSk5mSys1ZnBxRk1GQWljc0VaWnN6Y1UxUEpHaU9QTEpWUi9RVUhGeHJkUW40ZDdHN3BUc2RyQ0FEK2FCWkoxZkZIc3Fhd3pJMUZYUVFGVHI3LzA3Rk9qYW1OQ1F0M0JTSGQ4ZG4rYTRnNGtiOXN4Ly9rYzE1bXdrTWJZK1I5bExBcEhIN1RxL0puRmc2bmw5QVF3MWwiLCJtYWMiOiIyNGQ3MmU2YjQyMTE4ZDliMjExMmI2YzY5MmRlZDZmODc3NjUxNmIzMjkyYTYwNmFiMTg0ZmFlNDNkMDExYzJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-44368107\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1733133756 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0M1K0sdNadPD2LVrnt8LmBw227nA9F1U5e3ROaJK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1733133756\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1050129600 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:40:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdNUmtqdzRhMTE3c2N3K1BIZ1U0Q3c9PSIsInZhbHVlIjoibmRYLytwNVBIc29NbnNBb2pzRlQvTEwvQW1RSjB2WExJNTVTU3NrdnhsWEUzeExvSitOTkp2cVM2aktXaVVQU1Y5N0hYZWkzUW41RmZaMG5KVVRoWElQeHViWFMvdlpMQlBrM0d2THlEK0hwTFl0SHRhd1ZVNXZ3TkRXV2dOT0lITXRNalNUbmNnR3diZ2w3L3VodDlGL1ZjQTBWTE5VSUhFa3JjQ1d5Vis5SVNCRi8wd0I3UjlnVWRldk9XcXN4bVI1M2VrKzFaWk81a1diTzc2SXlPK2VTN3JmaG1Cbi85cEUrUkg4Qk90WEtXTWlIL3g5dHhCUk8yK0xSTXBUb0VPcndRQXJLTElCbmpLU0ZtK3FDZEZ6T2pmZ04vazhkNjFlamdnMDFPSlk0TGxpSXArRFUvZUVIM01NWW1LY1hnSUZRTGU4ZzJXTU12TXlHYkZIanAwQXQzZFZhenFFMm5neDhDSTNvRU93V2p5OUYrVWhMOVdna1EvanRub0M1TmF5c005cmY3WWUxNGZNc0p5TE1obzFBTHpkRW9NYTdJN3VKS09pc1NJdVBYQkp2cm9aL2tDNDU1N2NRczJoMVRONjMxK3NqTEYrQ3liSEN6azFHTTdnNVNPUk5XRFk0WS8zejNaeXkrbGhtaGl0WXB1OEUxdlpPNE1nY0R4K0kiLCJtYWMiOiI4NjUzMjQwOWFiNzUwMzMyNWViM2FmM2QwM2Y3MjYxYmNmNjQ4ZTA0M2QyMmI2NmI1OWQ3ZjEyNWZhYTU5ODBiIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InlmcmJDRlRjTkpkc29kVTdXUnNzNkE9PSIsInZhbHVlIjoiK3N0aHlDUkNJekw2Zk5lT25oTmwrYkowNDFhODdVeW1jTEpCMEN3bFBraDJUS1dpNEhudXFwRmhzQTVPYWRqN2hzbFhFaUVGK0RzbVpmdEh1b2N1TnZQY0VRSmhjR2I1UHBLVEpjQmRRcGcveGpaanA1L0pTOFhNOHZEZjRFeDhLZFlVTHJNaDh3USt6RTMyZm1NWm9HMTNCaDZjZExjams5cmVtWWxqWSt2WjNCNStIOVdSM2xnMUVTTHNOUHk0ai9aUHc1cFlialdVTytnZ2c1Mm9pTExiU2NHVFFHcmVJY3BXVDJhVis0RlM0Wk5Mam9tWUFuZ1ZyMS9Jc1M0MzJ5WEgwcmIybitTVVU3NEgrL3M0UFBPVFBlalY3dU1QcUVUb3Q2VzlhSXROaGRjSVN4cnAvbzZiT0lnd24xdER1MGozQVFmQzk2NHM0RGVFSlJhNG1sQ2s4VHZUN2xyTTQ2WUtReXViSml1R0x2TUJMMlk0bFcxL2kvZUNzeGFvSjJCay9jSkgwMy9kNDlVU2lKQmhKSXdNdWxDbVJnWlgrbzl6VVI5U1ljemhKMmhQeGZBZ3hVNlp0cW5rcElMMVBEQWtQVHNzL0hNVzlMNVVaSzh3M2lES29POGpuZ1BjM3NrMEQrRkJBNWF1M3pTdWx5S2hUTWZVcndybFFPV3UiLCJtYWMiOiI2MDYwMzcxYjYyNDEwMDkwMTAwZDVlOTE4NmUxZGNiMmIyMmJhYzFlODA4ZmYwNGIxN2YyNTdkM2Y2MjFkZDk0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdNUmtqdzRhMTE3c2N3K1BIZ1U0Q3c9PSIsInZhbHVlIjoibmRYLytwNVBIc29NbnNBb2pzRlQvTEwvQW1RSjB2WExJNTVTU3NrdnhsWEUzeExvSitOTkp2cVM2aktXaVVQU1Y5N0hYZWkzUW41RmZaMG5KVVRoWElQeHViWFMvdlpMQlBrM0d2THlEK0hwTFl0SHRhd1ZVNXZ3TkRXV2dOT0lITXRNalNUbmNnR3diZ2w3L3VodDlGL1ZjQTBWTE5VSUhFa3JjQ1d5Vis5SVNCRi8wd0I3UjlnVWRldk9XcXN4bVI1M2VrKzFaWk81a1diTzc2SXlPK2VTN3JmaG1Cbi85cEUrUkg4Qk90WEtXTWlIL3g5dHhCUk8yK0xSTXBUb0VPcndRQXJLTElCbmpLU0ZtK3FDZEZ6T2pmZ04vazhkNjFlamdnMDFPSlk0TGxpSXArRFUvZUVIM01NWW1LY1hnSUZRTGU4ZzJXTU12TXlHYkZIanAwQXQzZFZhenFFMm5neDhDSTNvRU93V2p5OUYrVWhMOVdna1EvanRub0M1TmF5c005cmY3WWUxNGZNc0p5TE1obzFBTHpkRW9NYTdJN3VKS09pc1NJdVBYQkp2cm9aL2tDNDU1N2NRczJoMVRONjMxK3NqTEYrQ3liSEN6azFHTTdnNVNPUk5XRFk0WS8zejNaeXkrbGhtaGl0WXB1OEUxdlpPNE1nY0R4K0kiLCJtYWMiOiI4NjUzMjQwOWFiNzUwMzMyNWViM2FmM2QwM2Y3MjYxYmNmNjQ4ZTA0M2QyMmI2NmI1OWQ3ZjEyNWZhYTU5ODBiIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InlmcmJDRlRjTkpkc29kVTdXUnNzNkE9PSIsInZhbHVlIjoiK3N0aHlDUkNJekw2Zk5lT25oTmwrYkowNDFhODdVeW1jTEpCMEN3bFBraDJUS1dpNEhudXFwRmhzQTVPYWRqN2hzbFhFaUVGK0RzbVpmdEh1b2N1TnZQY0VRSmhjR2I1UHBLVEpjQmRRcGcveGpaanA1L0pTOFhNOHZEZjRFeDhLZFlVTHJNaDh3USt6RTMyZm1NWm9HMTNCaDZjZExjams5cmVtWWxqWSt2WjNCNStIOVdSM2xnMUVTTHNOUHk0ai9aUHc1cFlialdVTytnZ2c1Mm9pTExiU2NHVFFHcmVJY3BXVDJhVis0RlM0Wk5Mam9tWUFuZ1ZyMS9Jc1M0MzJ5WEgwcmIybitTVVU3NEgrL3M0UFBPVFBlalY3dU1QcUVUb3Q2VzlhSXROaGRjSVN4cnAvbzZiT0lnd24xdER1MGozQVFmQzk2NHM0RGVFSlJhNG1sQ2s4VHZUN2xyTTQ2WUtReXViSml1R0x2TUJMMlk0bFcxL2kvZUNzeGFvSjJCay9jSkgwMy9kNDlVU2lKQmhKSXdNdWxDbVJnWlgrbzl6VVI5U1ljemhKMmhQeGZBZ3hVNlp0cW5rcElMMVBEQWtQVHNzL0hNVzlMNVVaSzh3M2lES29POGpuZ1BjM3NrMEQrRkJBNWF1M3pTdWx5S2hUTWZVcndybFFPV3UiLCJtYWMiOiI2MDYwMzcxYjYyNDEwMDkwMTAwZDVlOTE4NmUxZGNiMmIyMmJhYzFlODA4ZmYwNGIxN2YyNTdkM2Y2MjFkZDk0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1050129600\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-541424971 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-541424971\", {\"maxDepth\":0})</script>\n"}}