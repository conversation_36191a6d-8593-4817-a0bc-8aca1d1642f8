{"__meta": {"id": "X0a7f2e9fdc128d5bfa711d20c329f1a2", "datetime": "2025-06-30 23:10:18", "utime": **********.361658, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751325017.88874, "end": **********.361677, "duration": 0.47293686866760254, "duration_str": "473ms", "measures": [{"label": "Booting", "start": 1751325017.88874, "relative_start": 0, "end": **********.306811, "relative_end": **********.306811, "duration": 0.41807103157043457, "duration_str": "418ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.30682, "relative_start": 0.41807985305786133, "end": **********.361679, "relative_end": 2.1457672119140625e-06, "duration": 0.054859161376953125, "duration_str": "54.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45724064, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029799999999999996, "accumulated_duration_str": "2.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.336148, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.43}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3462622, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.43, "width_percent": 15.101}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3523679, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.53, "width_percent": 20.47}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1293309332 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1293309332\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1707568224 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1707568224\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1426454205 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426454205\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1429212378 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751325016632%7C10%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InQ1dnc3dUdnYXBFSWxwQWR5dVd1WWc9PSIsInZhbHVlIjoiOUJaenErbmdtWmRGbm1QMEdhckEvYjE0SmxRNE9GSlpLTkRVMHJHRkhrZ0pNYzlENnhNOHFEK2NOcmp5bFE3M0hPQ1VNN1E0SjhYclEzb1cxTjEvMzBwM3ZBeHFLOWtmQmg5Q3RiUlRuTU9SYjkydnh2OWtwWEJaaGVZa2VJQktCRlB2SXlKWmpsODJDeCtLN3JTMS9oTE1ZeXVDQ3lNSGdWNHJKdS8wRkFBdTFJK2V3c0NVYTNBYjlITThLQWNhS2svWm5Yeml4eWJtZmtCcE5tS2U3ZTRyUFhmcUFmYjZ4VWF6M0tFMG1lVW9pdWEvMktWTXVLOVBjVkIxU0NVRHQ2T2FHcWlQSXlxV01COGNyY01QMEZ0Nm1UZEtiQjZHTC9uTTAzcjg3ZzdralRCcEV3M1JoWVdWdEpEU2Uydk5tcDE1elFONDkyTllBMTdRb2o1VXFkUnVJTEVrNk9RaDBtd3RsQ1hNRlM2UW9MQ3ErZ0pqS2xFRDZKUE1IckthV3FCMGxwSlIxNUhPYU1CcitwamhsYWN3ZnUrRmlqMGE2cnliTEZhU0dWbnJsclpIMHNhRVF0dEVPUG9uRW04KzBjcWlDUEF2Mmw0L1FkMnQ5WHN2QWlJLzBtWUdDa0J4Z3hITkVaMTVEOWRGTlR3R2JURitLTVhKdlRtc3AxOHoiLCJtYWMiOiI4MjUzYjEwMDZlYTlhMDA2YWQ4NGNjZGU4MDc1ZTIyMTJhMGZhNzBiODQ4NWQ0NjBlYTVmOGU1YzJhYWQ3MTg2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImNlRFhUZXJJZ1lVZnFRcnlNUTVSZ0E9PSIsInZhbHVlIjoibW5wWHJTMlphb0NHVlNWSEZYN2ZiNnN2MnNPb1Q5S0o0RmlXVnVHbFQ0U1VuK08vUEREQW9ETXJ0VUhmT2VoMEVablQvanlxeHUzTHVBNHBiVVUxcjI1dTRQWDFEM2JIUmFpY2FIaGpUOTl1OTZOeWttNXFMcnZCamdITGU1UnZ6aGl0YldUb1huSTliZUZqbmtiOUVaN3QyelVtWUZJU0JIZTVVenVQVXZOZGg2NTZkSlVkaWU2ODc2M3E3WmFGd29QdS81MGVqWFhENGpZY054c3ZWbDY5UVhSMERUM2hCNmtiWC8rWFhXM2ZWTStkQUVydDUrUVUxU25iS24za2cwRzNSMFI3RTJjWVh3Q29oTnY4eEJYOWo3T1dwVEZjcXM5dmlUQUd2ckFibk01STAzbWorSG5pVEVJZ0JvdTlVWWFpSHhrNkhaU2pMZDdPdXpXblV3WnEvMUJlb2FqQmNvK21FR2FzOXJwZ1l1bW5HN05GVTROdHcyaENFaGVDU0JtUXY0T0ZYalpYTXZ6bE54RXFuc0lkSU1YcDNSWVlpcWh5QTdQRkowSm1TVFJ4UjhIWUdwTm5jOVRRdnNENVFYdVF0SUpTd0dkeVJiRm8rRllJNTJ6WW5Zd0QrNHo2TnRpaGcxOHV6OUkrcWF0OUgrUTVGdXNjY1diRUljRnYiLCJtYWMiOiJiNjliYjZiMTBiN2NmYzBkOTY2MGY1YmViZGI5MDEzYWRkNGUzMGIwNmZhYmM1OTgzNWI0ODE4ODQ0NDRiOGVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429212378\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-539562934 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-539562934\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1021959843 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:10:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRBbU0wTEd0UXFmSCtWTW5XQXBKQUE9PSIsInZhbHVlIjoiK1NpelhBMHNmeGQ1dnFZc1VwRFJtSDdXSmpqMjJwckVKRENTOGFNZDhOWTJNZlowSGpnTEYvemJJSXVBN2NlbVZydEZrMmdzREpWa3E2aGQrbDBPQlgvNm0reFEwY3cyNHFTQUhDM3VkQldTRDdJOGMyL09xSG5LOS9yTWFVUW4rRW9ScklJb3ZRK3JkL0NIbDJCMU1La0pzMTFvU1VUWTRxeUJsb3Z6b3ZveWlwS0liWlV5WWhsZWViWXFYRjU3ellDSm04alA2YnAvWVVUSmFQeFNXbXY5NWJSR01EOHh5aFJNVHZ1NmE0VWJhdjN2OUptZm9HT3Vkb01tbTBWUTZGNHhhOFE0OEpWcUdaQ3lXbkF0TzJvdjIvNUkzMER2Wk9NUGhSb3hpenh6TGVqZTl1T1ZNQ0dvRXFnMUtqdGQrQm5tYVBXOWZCblVycm5SRlBhR0srdC9oc1lPSjdlOVYyQVhURFMyOUNmUGFnL2NGMXBEQldDRVp1NEpFM2pWQlhWNmFjQVFCbUc1ZXN1MHJUYkxYdER4TmhXeENPdW81eEY3MW15OFprR25qeTM4S0huSDVqbXRhRUFBNTl5VFJJODFrbUJzQzBWYjRsem00L2dsRTJ4Lys5clpyanNDUTlzYkxPR2VXMXMxbVVxaGprQTA1Tm9RQ1FaQWhTTU0iLCJtYWMiOiIyNWE2NjI2NjVjNjM4ZWZjZThjYTEwMjQ4MWUyYWJiYTI2M2IxZGZiMTJhMWI0Y2NmMzAzZjk1MTEzYjYxN2UyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkFNTldlWUNLMjl3S1dNRWxzdi9CVGc9PSIsInZhbHVlIjoiWjZmeFF3MnhJTVBSNUpXOHhBdXlOYUNzS1QvbnpZN0xsdjNvajFaUEgvRUpvSXdjOExhMG81MlBrTFNIR2xjUks4Q3RDWTJHWlJvVWZOK0VhdkNQdzhCRGg0MnRGTGUxeEh1dVdqY2c3bWFlRWQvakpIdnQxdWFFdXZheDlxNkQ3K1M1bWlxbWtjeWJGcHdUYmVWY2hkWHpaUjZKWE9QdHIxejAwZnVkN3dLeTl3TzIrcldjTnRNRkRJNmQyNTBlVEYvNGRoVzR5YTZCNDdndGNpcmdtUXNHN1UvK2tBRFFBVkR5aWJvRmlHSEZ1aCt2WFdWN1luTnJQMjJvWFNnbmtDQU5mY0F5L3NKdklQNjBBZzVvbEp2QmU2TEJwNkpxUE82ZEdqOVowNjJCUVNsMTROMXFkVTJWRFR3eTFocEVMakJWZ21jUDQ1UTRoa0owSkwxK0pXVG9wbFNMd296NFlmcEZQbnJpN0ZveDFrRU50WVRCTUU3cWpSTE1DQlp0aHUyTW5RVVl0ZHpuWU0zZGtpYW1Nc0l1enhRRFlCNnc2c0hVVjNuNUJPS0dZdjUwL1JiNm9iZ0xmRW03MGtqOEQ2ZmJ6cTdiTk9kUVJBNlp1bk1WVGN0Z2dxTWxaM3h6cUZ6TkhUOHZDRWswWG02ZUcrRVBPVXNVK1FSNzM4ajQiLCJtYWMiOiJiOTY2ODk5YjdlNDA0M2VmMDk5YTA1NWM4OWY3YWMzYmY5MmI1MzE0OWMxZGU0YmQ4YjdiOTkyMjAxNWQwNTEwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRBbU0wTEd0UXFmSCtWTW5XQXBKQUE9PSIsInZhbHVlIjoiK1NpelhBMHNmeGQ1dnFZc1VwRFJtSDdXSmpqMjJwckVKRENTOGFNZDhOWTJNZlowSGpnTEYvemJJSXVBN2NlbVZydEZrMmdzREpWa3E2aGQrbDBPQlgvNm0reFEwY3cyNHFTQUhDM3VkQldTRDdJOGMyL09xSG5LOS9yTWFVUW4rRW9ScklJb3ZRK3JkL0NIbDJCMU1La0pzMTFvU1VUWTRxeUJsb3Z6b3ZveWlwS0liWlV5WWhsZWViWXFYRjU3ellDSm04alA2YnAvWVVUSmFQeFNXbXY5NWJSR01EOHh5aFJNVHZ1NmE0VWJhdjN2OUptZm9HT3Vkb01tbTBWUTZGNHhhOFE0OEpWcUdaQ3lXbkF0TzJvdjIvNUkzMER2Wk9NUGhSb3hpenh6TGVqZTl1T1ZNQ0dvRXFnMUtqdGQrQm5tYVBXOWZCblVycm5SRlBhR0srdC9oc1lPSjdlOVYyQVhURFMyOUNmUGFnL2NGMXBEQldDRVp1NEpFM2pWQlhWNmFjQVFCbUc1ZXN1MHJUYkxYdER4TmhXeENPdW81eEY3MW15OFprR25qeTM4S0huSDVqbXRhRUFBNTl5VFJJODFrbUJzQzBWYjRsem00L2dsRTJ4Lys5clpyanNDUTlzYkxPR2VXMXMxbVVxaGprQTA1Tm9RQ1FaQWhTTU0iLCJtYWMiOiIyNWE2NjI2NjVjNjM4ZWZjZThjYTEwMjQ4MWUyYWJiYTI2M2IxZGZiMTJhMWI0Y2NmMzAzZjk1MTEzYjYxN2UyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkFNTldlWUNLMjl3S1dNRWxzdi9CVGc9PSIsInZhbHVlIjoiWjZmeFF3MnhJTVBSNUpXOHhBdXlOYUNzS1QvbnpZN0xsdjNvajFaUEgvRUpvSXdjOExhMG81MlBrTFNIR2xjUks4Q3RDWTJHWlJvVWZOK0VhdkNQdzhCRGg0MnRGTGUxeEh1dVdqY2c3bWFlRWQvakpIdnQxdWFFdXZheDlxNkQ3K1M1bWlxbWtjeWJGcHdUYmVWY2hkWHpaUjZKWE9QdHIxejAwZnVkN3dLeTl3TzIrcldjTnRNRkRJNmQyNTBlVEYvNGRoVzR5YTZCNDdndGNpcmdtUXNHN1UvK2tBRFFBVkR5aWJvRmlHSEZ1aCt2WFdWN1luTnJQMjJvWFNnbmtDQU5mY0F5L3NKdklQNjBBZzVvbEp2QmU2TEJwNkpxUE82ZEdqOVowNjJCUVNsMTROMXFkVTJWRFR3eTFocEVMakJWZ21jUDQ1UTRoa0owSkwxK0pXVG9wbFNMd296NFlmcEZQbnJpN0ZveDFrRU50WVRCTUU3cWpSTE1DQlp0aHUyTW5RVVl0ZHpuWU0zZGtpYW1Nc0l1enhRRFlCNnc2c0hVVjNuNUJPS0dZdjUwL1JiNm9iZ0xmRW03MGtqOEQ2ZmJ6cTdiTk9kUVJBNlp1bk1WVGN0Z2dxTWxaM3h6cUZ6TkhUOHZDRWswWG02ZUcrRVBPVXNVK1FSNzM4ajQiLCJtYWMiOiJiOTY2ODk5YjdlNDA0M2VmMDk5YTA1NWM4OWY3YWMzYmY5MmI1MzE0OWMxZGU0YmQ4YjdiOTkyMjAxNWQwNTEwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1021959843\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1994511491 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1994511491\", {\"maxDepth\":0})</script>\n"}}