{"__meta": {"id": "Xe351c5b79597f5b93bc07ec550dd3059", "datetime": "2025-06-30 23:14:19", "utime": **********.546182, "method": "GET", "uri": "/printview/pos?vc_name=11&user_id=23&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 198, "messages": [{"message": "[23:14:19] LOG.warning: Implicit conversion from float 160.79999999999998 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 186", "message_html": null, "is_string": false, "label": "warning", "time": **********.51369, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 1.4 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.515798, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 3.5999999999999996 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.516916, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 3.8 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.517023, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 7.199999999999999 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.517095, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 7.399999999999999 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.517168, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 13.2 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.517234, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 13.399999999999999 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.517296, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 16.799999999999997 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.517363, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 16.999999999999996 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.517424, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 20.399999999999995 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.517487, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 21.799999999999994 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.517548, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 26.399999999999995 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.517608, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 28.999999999999996 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.517666, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 31.199999999999996 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.517726, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 33.8 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.517783, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 37.4 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.517842, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 39.6 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.517901, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 39.800000000000004 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.517959, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 44.400000000000006 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.518017, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 47.00000000000001 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.518081, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 49.20000000000001 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.518165, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 50.60000000000001 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.518247, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 52.80000000000001 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.518331, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 54.20000000000001 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.518412, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 56.40000000000001 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.518499, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 59.000000000000014 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.518567, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 61.20000000000002 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.518628, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 61.40000000000002 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.518686, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 66.00000000000001 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.518745, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 66.20000000000002 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.518802, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 68.40000000000002 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.518861, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 71.00000000000001 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.518918, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 73.20000000000002 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.518977, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 77.00000000000001 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.519034, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 79.20000000000002 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.519094, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 79.40000000000002 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.51915, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 82.80000000000003 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.519208, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 83.00000000000003 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.519266, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 86.40000000000003 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.519328, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 87.80000000000004 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.519386, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 92.40000000000003 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.519445, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 93.80000000000004 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.519502, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 96.00000000000004 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.519563, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 98.60000000000004 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.51962, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 103.20000000000003 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.51968, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 103.40000000000003 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.519738, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 105.60000000000004 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.519797, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 105.80000000000004 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.519857, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 108.00000000000004 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.519919, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 111.80000000000004 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.520018, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 114.00000000000004 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.520114, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 116.60000000000004 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.520182, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 118.80000000000004 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.520249, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 121.40000000000003 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.520315, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 123.60000000000004 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.520384, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 123.80000000000004 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.520447, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 127.20000000000005 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.520512, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 128.60000000000005 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.520574, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 132.00000000000006 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.520639, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 132.20000000000005 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.520701, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 135.60000000000005 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.520764, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 137.00000000000006 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.520826, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 142.80000000000007 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.520893, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 143.00000000000006 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.520953, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 145.20000000000005 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.521015, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 146.60000000000005 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.521073, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 151.20000000000005 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.521137, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 153.80000000000004 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.521218, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 156.00000000000003 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.521284, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 156.20000000000002 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.521354, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 158.4 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.521416, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 159.8 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.521474, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 160.8 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.521534, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 159.8 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.521592, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 160.8 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.52165, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 159.8 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.521709, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.528829, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.528948, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.529025, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.529092, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.529158, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.529228, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.529292, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.529357, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.529422, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.529486, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.529553, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.52962, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.529686, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.529755, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.529829, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.529899, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.529968, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.530036, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 18.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.530105, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 19.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.530175, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 20.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.530244, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 21.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.530313, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 22.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.530424, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 23.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.530532, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 24.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.530631, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 25.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.530729, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 26.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.530827, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 27.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.530925, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 28.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.531026, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 29.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.531122, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 30.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.531221, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 31.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.531327, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 32.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.531425, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 33.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.531495, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 34.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.531562, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 35.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.531628, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 36.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.531693, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 37.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.531761, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 38.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.531829, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 39.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.531895, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 40.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.531964, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 41.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.532044, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 42.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.532117, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 43.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.532185, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 44.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.532254, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 45.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.532323, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 46.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.532392, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 47.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.532468, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 48.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.532545, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 49.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.532615, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 50.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.532692, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 51.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.53277, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 52.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.532841, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 53.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.532932, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 54.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.533045, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 55.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.533119, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 56.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.533184, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 57.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.53325, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 58.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.533316, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 59.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.533381, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 60.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.533447, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 61.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.533511, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 62.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.533577, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 63.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.533644, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 64.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.533709, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 65.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.533773, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 66.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.53384, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 67.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.533905, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 68.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.533969, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 69.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.534034, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 70.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.534099, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 71.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.534165, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 72.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.534229, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 73.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.534295, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 74.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.53436, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 75.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.534424, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 76.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.534488, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 77.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.534552, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 78.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.534618, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 79.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.534694, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 80.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.534789, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 81.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.534857, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 82.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.534923, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 83.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.534988, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 84.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.535053, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 85.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.535126, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 86.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.535198, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 87.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.535263, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 88.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.535327, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 89.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.535393, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 90.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.535461, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 91.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.535526, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 92.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.535591, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 93.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.535655, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 94.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.535719, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 95.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.535784, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 96.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.535848, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.535913, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.535979, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.536044, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.536108, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.536174, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.536237, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.536301, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.536372, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.536438, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.536502, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.536567, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.536632, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.536698, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.536762, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.53683, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.536912, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.536989, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.53707, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 18.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.537156, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 19.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.537226, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 20.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.537299, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 21.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.537365, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 22.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.53743, "xdebug_link": null, "collector": "log"}, {"message": "[23:14:19] LOG.warning: Implicit conversion from float 23.5 to int loses precision in C:\\laragon\\www\\erpq22\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.537499, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751325258.912141, "end": **********.546335, "duration": 0.6341938972473145, "duration_str": "634ms", "measures": [{"label": "Booting", "start": 1751325258.912141, "relative_start": 0, "end": **********.272166, "relative_end": **********.272166, "duration": 0.36002492904663086, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.272175, "relative_start": 0.3600339889526367, "end": **********.546337, "relative_end": 1.9073486328125e-06, "duration": 0.27416181564331055, "duration_str": "274ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 60385448, "peak_usage_str": "58MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.printview", "param_count": null, "params": [], "start": **********.398503, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\resources\\views/pos/printview.blade.phppos.printview", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fresources%2Fviews%2Fpos%2Fprintview.blade.php&line=1", "ajax": false, "filename": "printview.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.printview"}]}, "route": {"uri": "GET printview/pos", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\PosController@printView", "namespace": null, "prefix": "", "where": [], "as": "pos.printview", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1318\" onclick=\"\">app/Http/Controllers/PosController.php:1318-1420</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.01687, "accumulated_duration_str": "16.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.313593, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 10.907}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.324183, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 10.907, "width_percent": 2.845}, {"sql": "select * from `customers` where `name` = '11' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["11", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\PosController.php", "line": 1326}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.327577, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1326", "source": "app/Http/Controllers/PosController.php:1326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1326", "ajax": false, "filename": "PosController.php", "line": "1326"}, "connection": "kdmkjkqknb", "start_percent": 13.752, "width_percent": 2.786}, {"sql": "select * from `warehouses` where `id` = '8' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\PosController.php", "line": 1327}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.329707, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1327", "source": "app/Http/Controllers/PosController.php:1327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1327", "ajax": false, "filename": "PosController.php", "line": "1327"}, "connection": "kdmkjkqknb", "start_percent": 16.538, "width_percent": 2.015}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.343992, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 18.554, "width_percent": 3.557}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.34594, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 22.11, "width_percent": 1.778}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\PosController.php", "line": 529}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\PosController.php", "line": 1330}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.35042, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "PosController.php:529", "source": "app/Http/Controllers/PosController.php:529", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FPosController.php&line=529", "ajax": false, "filename": "PosController.php", "line": "529"}, "connection": "kdmkjkqknb", "start_percent": 23.889, "width_percent": 2.727}, {"sql": "select * from `product_services` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\PosController.php", "line": 1405}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.367458, "duration": 0.01151, "duration_str": "11.51ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1405", "source": "app/Http/Controllers/PosController.php:1405", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1405", "ajax": false, "filename": "PosController.php", "line": "1405"}, "connection": "kdmkjkqknb", "start_percent": 26.615, "width_percent": 68.228}, {"sql": "select * from `pos` where `created_by` = 15 order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\PosController.php", "line": 1414}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.390243, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1414", "source": "app/Http/Controllers/PosController.php:1414", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1414", "ajax": false, "filename": "PosController.php", "line": "1414"}, "connection": "kdmkjkqknb", "start_percent": 94.843, "width_percent": 2.015}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "pos.printview", "file": "C:\\laragon\\www\\erpq22\\resources\\views/pos/printview.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.506368, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 96.858, "width_percent": 3.142}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 2391, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\Pos": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2396, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1992045855 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992045855\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.349582, "xdebug_link": null}]}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:2 [\n  2354 => array:9 [\n    \"name\" => \"العاب شكل بطه\"\n    \"quantity\" => 1\n    \"price\" => \"6.50\"\n    \"id\" => \"2354\"\n    \"tax\" => 0\n    \"subtotal\" => 6.5\n    \"originalquantity\" => 9\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2355 => array:8 [\n    \"name\" => \"العاب اطفال\"\n    \"quantity\" => 1\n    \"price\" => \"6.50\"\n    \"tax\" => 0\n    \"subtotal\" => 6.5\n    \"id\" => \"2355\"\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/printview/pos", "status_code": "<pre class=sf-dump id=sf-dump-1470132643 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1470132643\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1637256760 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1637256760\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-978427953 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-978427953\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-672937994 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2818 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; laravel_session=eyJpdiI6ImpxTklOWXhTYU1XWEJkQTBqVTRXZlE9PSIsInZhbHVlIjoiTXFGZEpQRXlGa212dDlvcjMyZUgzOTJTdTZUUld1Y0RIbVM4elBEck42T2RTa0tjV2NoaTJNekRxM3Vkc3lrR1dOeGt5blA0Ym55U2pNZWY5am1QNC9lMm5SZE1VcGh6OVBneXMxN2Y4Qmx1TWhKckg0OXNOdzJBV1ZmTzVubEw3cmlyUDRwVnhxYzd4NmhqaEJiZThRcHlESndxYTY0WWNKZ1pqeVdFSTZuZnRmZ1BDZy91c3ZCbWloYytBQnpaczBrTmh2S3Z1dllBcHJYMkhWOXM3alFFWENqVkhpc2Rnb1lHSlpwM2dNb0s5cUxpakE5OVNEbEZRZ0VUVDJBWFZKWmZyYjZRM2ZNL1BuR0l5L3BsYXhVbzEwQ1ZlaXYySVUrQnk5a0dvY1BKK2dwQnEzM3ppZnFlaW9XTW9Ga1pSQ29mZktuT3BjSnc5ampzYU1SdTIxNzd4Wms4eUJ6eWdrajg1UUhYS2puSkhMNDVRbEh3WENJazQ2Qzk0Vlk5cE5NYW1qQ0xCbWJBVHZ5ZUlzUzBGRTJCWGliL1ovUHlOejhGSHJzRS9UOC8wK0QvTTQ4WHdveVBkTE1CdHlWcnFrQWdDcU1jR0hscFd0SUgzYWRtbFRaT0pzTGUzSkVDS2k4YlhiU0s3b29aMU5tbWtnL1NUaWZ1VmhxYnZ3ZFoiLCJtYWMiOiJjNTgzZDkxNDlmYmY2OTAwMmNmYTUwNTg0OWMwNDQ3MTVmZWQ0YWFiNjk4ZTJkZDM4YTkzNjhiMzE3YzIyNjlmIiwidGFnIjoiIn0%3D; _clsk=eiqg7d%7C1751325181289%7C15%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImNPUkszd0tLTWpWbWlyQTNCTFFaaVE9PSIsInZhbHVlIjoiV0FpZ2VTa1ZYSjZNY3o4cjJyb05lNjk3Nk0xL1E1MnlMU0wxNG43R3JJRmR4bmFJZmF3SEIyWnNDTURJZUdUOGlYbTE0NHVXS3NVMHoreWs3Z0J0UmFORnVlOGl6UEV4cU4zQUVmSStYbE9uMmc4MjBvcFgzTTQwSjZWUWdqNkJTdnNTbHk3MVBDcFY3eVdFeFpGM21kZnowU0h1L2JzUVVsMVFjRlFMRzBnaTh2UE5Nc3I3cEZZR0hEYkx4dHE0RE5wRmlHMU9qR043ZlloWGFUMjRlQUY0RHd3azZpUk5ad2h4a2lQdHVWZ21LY2ZERHRweHA1bzRPTGRGQkIwc2hqK21ZN1h4bWIwaG41ZnNCNXJsYnY2dEcrU09RaW5LWXp0blQ4cHVEZGo4eEtDRU9EZjM1Y2EraGlwTWU3UkJNK3lUV0kxcGVoaFZoaVBSajV0M2hkTy9tcjNlWjhzNVl2Ymo5Y1g1NDVnK0VKVnEvankwVnZ0aWQ1NzI1WEptWGFEdU9MeGpxVm1rMnpNaGRDRjg5UTVINjROb2p5KzdRSm1ZdjFSdTNVZjM0V2d2UHZhSE1qcS8zdXNzeGVFcnQ0bnE3VnB0WHdzekNUeDlzdFdzaUZqV3Bmcy9qUW9rRndEV29oR21nT25KOEhjL2M4SEhQbExPek1rYnZUaGgiLCJtYWMiOiJiN2E2MTk0YjY2ZGIxZTgzOTZjZTllZDExYTc4OGE1NjY5YTc0YzhlMzhmYjI5NWFhNWZmMmE4NTA2OWU4NjIyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjFHemdLbmJsclRBRC93OWFTWWFTUVE9PSIsInZhbHVlIjoiR1Zoc1F3ajJYRDd3bWFXYW96cW9CTkFlbEZnN1BYeDhCVi9mb1l5ckJPbXZNWmJiaWRLUzZDdDFzQ0FwdDRWK0ZKSkxTUk4yYzFtamQ4azd4VUVUa3NBUmdsOFZVVzhXZzRsd1BmZFltYTkvbi9oYzYwMHkyeFpEN3AvUnFVVThPK3RXb1BsWGF0TUVzR3dPWXRxZWNkeFMwQ21qYS8yYTVzQW5YelRPNUVoNGI4dzNjTFoxcWJsZnkvd082QnJ4R0pmWndXK0V4b0xrSHF5bEIyQkFCeFAraVh1RTRJVTFZZ2dERU1TSGxvaFNGT2haV3E3QlUxcFRJT2ZkbWhMdDlnaWpmcHVMRWN0ZjJqczh5S3lLYlFqSWZCUGRaYm55d0JxZS80Q2laQlV6M1VqbStrcGJvc3JHS2R4c0Y5Z3hIU0Jya0JqLzlpMytabnVtbFFwcDI3VTNoQTFkWVF3RGdkbEk4Z0Z2Vi9jYk4rYk5sb1Z6UzIyV0QzMTVlYVFqVTFhWXkwM1l4SitJekU5ajQ0ZElRUEl6eW5aSVVteEpyZzhSS3ZpaThFSFBTRFJBOWZDT3B0Qk5uNW9NRzY3cmQxZ05DOVhUYWRhM3dpT2U0UEl5ZG0xc2YzejVlTUJWczFHeFJHWmtEeEVjVzdwaFZPS3ZRNVRQT25mdmdYelIiLCJtYWMiOiIxNWJmMGYxZmNkMGYzNDFiY2E0OGIwY2EwOTYyZmVmYjkyMzc4YTlmODNjNDc2OWYzOWM4OWY0MmRjNzQ3OThlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-672937994\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-506893146 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nMXQGGGSGQFIfGwlrnEYCl0LIB0DtCvcZqQRlbGj</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-506893146\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:14:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZ5YThxdHlWVkJSVy9KalFITzAvbEE9PSIsInZhbHVlIjoibnpaYkdSSGdzVm9QclY1ZTFKQndhOWFKakFyUFhsYmEwcjdRV1hpWlhmaWZlYXNKa0VFUmFiSmNxYnZTSlREUzJDWnhic3JjMEFxQ0xWYlp5U0ljRjYzRTYreFNFZDgxbGUxRXFieVJ5R3BpZUlnMXRhdXNxWUJ6RUpRZDM5VG5qSEZPYVJCSFZrU2xOd1hGd2IrdXplcjV0bnVMbXMrdm96Q3dFV1hDV1RkOW1zUFhiaVp1clRNdVNucSs0TE01ZnkwR2lEMmlPUjRUMEF2bTh0aTJzeGREVlJsaFFsWVZqQ0xDUnVoMlBiZ1ZoRGlYbTk2RGlrdzJQUWNYZzBsRUNyN3ROSjNQUE1EVXhMa0pTeld5eXUrNUx6aG1zdlpvQm05VkR4REVNUVpmRm0wV1M2UWRuWENKYVdGNER6QXM5Yk5sa2FyV051RHhiYW1vdWRLSHVhdzcreEJQSDVBRGg1b2t6Z21DVFVxMWd6N2lWSlUvQUwzNzlBYkg1ZEVhMGlRY0R6NGc5d25FMnp0UlUvNUc1NWprUFBhMWxnYVJVMUgwTWNZWXI5TUxTRFQ3ZS9RMkwzMUNFUVpIMktiVmYrTTczMHJVN3lxNi93bG00eTFQMzNiNjlsbktBK2tQY0RNMCtTMEVnTmIwaVI0Y2l1M1IzQm01YWc1WjNrV0YiLCJtYWMiOiI1MGExZDVkZmJiMTViMmMzZmNlM2ZiODkxZDFkN2Y1MmM3NzI3MzEyZDM5MDA0YTJlMzNiMzBmZmI3OGMwYjAwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InFxRHNZMm1tZ2dVb3p1UnRTS0o1cUE9PSIsInZhbHVlIjoiOHludkpiQURYVktjbDMyM25Wb3lia1U3emEvYUtTdDhpZHdRR3ZkNE5CaDhWa3FHUUxVTnlUckdycGJrREhTQ0sya0hLYm5vK2dFeExaWXdPWG9LaU8wd0JIYkYrL3hwWk83Ym9tVWVVS2RRa2tacnVJTTNpbWtCblJVVUhsT3BYcGQrQzlPUS85Uks4ZlBwbTV4KzkzMjZoMmJqRVh1cDZPcVlRVUZhME9VY3hta1BwR0Q1MGZSY1hkbHJmWXlzdDZxZVRKS1B5cGlMaEtnYU4wWSthR0NyaTBQTS91WlJoT3J5emU2Zzc0K05qUGNENW1ZREVhUldFZjlrM3dmUkEvamFsLzQ3Q0UyZnhuM2VvTDUvbU5DaGdMR05JRU1YSTdpSU5jUTFueG5XREVyUXQ5QzBLVmU4RUNGTTkvZlV5ZW9Ma2F4dzBQbk42bEJxRlZMYVI2S25UMG41bmQ0WEsxVHc0L2R1QzNXVDN5L2k2WHozanZQWmdYRWZqQnNTY2ZTN2MvNnVqbWh3UlllS1hobHNoV0JWUno2NmZGcUZWSkxmRStDMTFGN24yVGhlblR1ay9VL3doSkdUWFhvd1hLY1R2VVkwZy9ISlBVZzkzbFBRZUhNc2N2ZWZrRkJjWEdiQlFpYmRuRWV1dlNFTExvQW1tT2VnSkc4Skh6aFUiLCJtYWMiOiJmOTcwMjk2ZTdjZjM4NjIzZmQ3NGRmNjczMDRjMzJmMDdkMGFjOTYwNDUxNGY4ODdhYjdiNDVmZTU0ZTIzZDE5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZ5YThxdHlWVkJSVy9KalFITzAvbEE9PSIsInZhbHVlIjoibnpaYkdSSGdzVm9QclY1ZTFKQndhOWFKakFyUFhsYmEwcjdRV1hpWlhmaWZlYXNKa0VFUmFiSmNxYnZTSlREUzJDWnhic3JjMEFxQ0xWYlp5U0ljRjYzRTYreFNFZDgxbGUxRXFieVJ5R3BpZUlnMXRhdXNxWUJ6RUpRZDM5VG5qSEZPYVJCSFZrU2xOd1hGd2IrdXplcjV0bnVMbXMrdm96Q3dFV1hDV1RkOW1zUFhiaVp1clRNdVNucSs0TE01ZnkwR2lEMmlPUjRUMEF2bTh0aTJzeGREVlJsaFFsWVZqQ0xDUnVoMlBiZ1ZoRGlYbTk2RGlrdzJQUWNYZzBsRUNyN3ROSjNQUE1EVXhMa0pTeld5eXUrNUx6aG1zdlpvQm05VkR4REVNUVpmRm0wV1M2UWRuWENKYVdGNER6QXM5Yk5sa2FyV051RHhiYW1vdWRLSHVhdzcreEJQSDVBRGg1b2t6Z21DVFVxMWd6N2lWSlUvQUwzNzlBYkg1ZEVhMGlRY0R6NGc5d25FMnp0UlUvNUc1NWprUFBhMWxnYVJVMUgwTWNZWXI5TUxTRFQ3ZS9RMkwzMUNFUVpIMktiVmYrTTczMHJVN3lxNi93bG00eTFQMzNiNjlsbktBK2tQY0RNMCtTMEVnTmIwaVI0Y2l1M1IzQm01YWc1WjNrV0YiLCJtYWMiOiI1MGExZDVkZmJiMTViMmMzZmNlM2ZiODkxZDFkN2Y1MmM3NzI3MzEyZDM5MDA0YTJlMzNiMzBmZmI3OGMwYjAwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InFxRHNZMm1tZ2dVb3p1UnRTS0o1cUE9PSIsInZhbHVlIjoiOHludkpiQURYVktjbDMyM25Wb3lia1U3emEvYUtTdDhpZHdRR3ZkNE5CaDhWa3FHUUxVTnlUckdycGJrREhTQ0sya0hLYm5vK2dFeExaWXdPWG9LaU8wd0JIYkYrL3hwWk83Ym9tVWVVS2RRa2tacnVJTTNpbWtCblJVVUhsT3BYcGQrQzlPUS85Uks4ZlBwbTV4KzkzMjZoMmJqRVh1cDZPcVlRVUZhME9VY3hta1BwR0Q1MGZSY1hkbHJmWXlzdDZxZVRKS1B5cGlMaEtnYU4wWSthR0NyaTBQTS91WlJoT3J5emU2Zzc0K05qUGNENW1ZREVhUldFZjlrM3dmUkEvamFsLzQ3Q0UyZnhuM2VvTDUvbU5DaGdMR05JRU1YSTdpSU5jUTFueG5XREVyUXQ5QzBLVmU4RUNGTTkvZlV5ZW9Ma2F4dzBQbk42bEJxRlZMYVI2S25UMG41bmQ0WEsxVHc0L2R1QzNXVDN5L2k2WHozanZQWmdYRWZqQnNTY2ZTN2MvNnVqbWh3UlllS1hobHNoV0JWUno2NmZGcUZWSkxmRStDMTFGN24yVGhlblR1ay9VL3doSkdUWFhvd1hLY1R2VVkwZy9ISlBVZzkzbFBRZUhNc2N2ZWZrRkJjWEdiQlFpYmRuRWV1dlNFTExvQW1tT2VnSkc4Skh6aFUiLCJtYWMiOiJmOTcwMjk2ZTdjZjM4NjIzZmQ3NGRmNjczMDRjMzJmMDdkMGFjOTYwNDUxNGY4ODdhYjdiNDVmZTU0ZTIzZDE5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1583219101 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2354</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1588;&#1603;&#1604; &#1576;&#1591;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.50</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2354</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.5</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2355</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1575;&#1591;&#1601;&#1575;&#1604;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2355</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1583219101\", {\"maxDepth\":0})</script>\n"}}