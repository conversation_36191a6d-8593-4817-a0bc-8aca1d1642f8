<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PosPayment extends Model
{
    protected $fillable = [
        'pos_id',
        'date',
        'amount',
        'discount',
        'created_by',
        'payment_type',
        'cash_amount',
        'network_amount',
        'transaction_number',
    ];


    public function bankAccount()
    {
        return $this->hasOne('App\Models\BankAccount', 'id', 'account_id');
    }
}
