<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PosPayment extends Model
{
    protected $fillable = [
        'pos_id',
        'date',
        'amount',
        'discount',
        'created_by',
        'payment_type',
        'cash_amount',
        'network_amount',
        'transaction_number',
    ];

    /**
     * Get deferred payment details from transaction_number field
     * For deferred payments, transaction_number contains JSON data
     */
    public function getDeferredDetailsAttribute()
    {
        if ($this->payment_type === 'deferred' && $this->transaction_number) {
            return json_decode($this->transaction_number, true);
        }
        return null;
    }

    /**
     * Check if this is a deferred payment
     */
    public function isDeferredPayment()
    {
        return $this->payment_type === 'deferred';
    }

    /**
     * Get the amount that should be counted as deferred
     * For deferred payments, this is the full amount
     */
    public function getDeferredAmountAttribute()
    {
        return $this->isDeferredPayment() ? $this->amount : 0;
    }

    public function bankAccount()
    {
        return $this->hasOne('App\Models\BankAccount', 'id', 'account_id');
    }
}
