{"__meta": {"id": "X152b006a215fd9b85af86b31196bd188", "datetime": "2025-06-30 23:13:07", "utime": **********.406636, "method": "GET", "uri": "/customer/check/delivery?customer_id=11", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.007854, "end": **********.40665, "duration": 0.39879608154296875, "duration_str": "399ms", "measures": [{"label": "Booting", "start": **********.007854, "relative_start": 0, "end": **********.364816, "relative_end": **********.364816, "duration": 0.3569619655609131, "duration_str": "357ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.364825, "relative_start": 0.35697102546691895, "end": **********.406652, "relative_end": 1.9073486328125e-06, "duration": 0.04182696342468262, "duration_str": "41.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43889512, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0016799999999999999, "accumulated_duration_str": "1.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.395488, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 85.119}, {"sql": "select * from `customers` where `customers`.`id` = '11' limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4000769, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 85.119, "width_percent": 14.881}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1995533575 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1995533575\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1605002196 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1605002196\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1001272747 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1001272747\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-593435004 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2818 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; laravel_session=eyJpdiI6ImpxTklOWXhTYU1XWEJkQTBqVTRXZlE9PSIsInZhbHVlIjoiTXFGZEpQRXlGa212dDlvcjMyZUgzOTJTdTZUUld1Y0RIbVM4elBEck42T2RTa0tjV2NoaTJNekRxM3Vkc3lrR1dOeGt5blA0Ym55U2pNZWY5am1QNC9lMm5SZE1VcGh6OVBneXMxN2Y4Qmx1TWhKckg0OXNOdzJBV1ZmTzVubEw3cmlyUDRwVnhxYzd4NmhqaEJiZThRcHlESndxYTY0WWNKZ1pqeVdFSTZuZnRmZ1BDZy91c3ZCbWloYytBQnpaczBrTmh2S3Z1dllBcHJYMkhWOXM3alFFWENqVkhpc2Rnb1lHSlpwM2dNb0s5cUxpakE5OVNEbEZRZ0VUVDJBWFZKWmZyYjZRM2ZNL1BuR0l5L3BsYXhVbzEwQ1ZlaXYySVUrQnk5a0dvY1BKK2dwQnEzM3ppZnFlaW9XTW9Ga1pSQ29mZktuT3BjSnc5ampzYU1SdTIxNzd4Wms4eUJ6eWdrajg1UUhYS2puSkhMNDVRbEh3WENJazQ2Qzk0Vlk5cE5NYW1qQ0xCbWJBVHZ5ZUlzUzBGRTJCWGliL1ovUHlOejhGSHJzRS9UOC8wK0QvTTQ4WHdveVBkTE1CdHlWcnFrQWdDcU1jR0hscFd0SUgzYWRtbFRaT0pzTGUzSkVDS2k4YlhiU0s3b29aMU5tbWtnL1NUaWZ1VmhxYnZ3ZFoiLCJtYWMiOiJjNTgzZDkxNDlmYmY2OTAwMmNmYTUwNTg0OWMwNDQ3MTVmZWQ0YWFiNjk4ZTJkZDM4YTkzNjhiMzE3YzIyNjlmIiwidGFnIjoiIn0%3D; _clsk=eiqg7d%7C1751325181289%7C15%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktldVpWdWQzcEVIbGFJS2dDN05GSVE9PSIsInZhbHVlIjoieERuZ1dVL0VqMTlETFBQdWpSanJZbTRQSnZBeUZXQUN0ZW0xVHk3UFFESWsrUENMMmRUNUpvTGtQSk9vOWhlRGN4SHM1RVg5cko2QkJWeEJmSksvTDRmblhqbm9aZFAwb0t6TW1yM1lCNlJISzBlYVoxbzFyTEZTNnRZbEhZZlV0RjRVUVF6eXZaUjA4bU5PV0gzUFA2b3M2SkExRFdaQlhuZTA4U1dEUmdJdXl2WUhTMHY1a1FLWjR5bFZUdXVOQXAzT1pMWDhnWVdFWlI3ZjNtZllPdUhxZ1kwbFFPN0p1RVdGV1BjUU1vTHRYMThrUjlsQ0poRXVNTEltb1oxZGtsK0xHdnBBZURncmZJUCtPY05IdGRIZVQzNU80ejZPZXFGK3Zpb0Z5SlRoQmhxK2RZMFhTRnN0d3JhTm5SYWJYc3UzdGxyN25Ia3VFNGJaT21ISGNGaDVNV1BqVU9xTmdtUTF4aWZxSFhWSWlGMWxkK2tEdDNDMkJkaEZCVHNWZFZKZkVpTGZMdE5ielNtOEdEK0VoOVBpeGF4L3g5THR0T24wbG5xcDhMRGloaGgzbUIvb1JLWjR0SGNmc0krTGRaK1RMWkN0WEVuQUxsR1kwakk5RkdGQ1k5QUZEa2t2Qm5CY2hlT0dyWEN3bmo2Ni9LOGxBSU9yM2VlbHYwVHIiLCJtYWMiOiI1NzNkNmJmNDZkOTIxNmFlNjE4OTgwODk2ZjhkMDBiNTA2ZmMzNWQ2MTE2ZmEzYTAyZDdiNzA0MDZiZThhNzVlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik5TSlI1Mk1TaG1wc25DbEFZRkJRQVE9PSIsInZhbHVlIjoia0RySTRJbWFYK25sUHd3OUYxeDJjeit0R1FHblRrTkFRUUE5TXR5UkUxZUJRM2ptUGk3OGZoZXJwS3lDSXBmaUx3blFPbnVGNFQ4cFpuZTZocngwQjFTUjJiSlA0eitHeC9CL2pHUFlweTZiQTFOMFhlblZXbXh5allVajhKVG5pL2FoTmJ1MFBoTUhhMTN1aCtDVkh1NWhqT2c2bE5iMy9JbXRscXlhYzkyZnlNWTBXSy9yRWxoS1FnWDAyZzFVeXJHNXZFUkxUSGx5UXdVbzhReFp6RGxtUmErb3FISjNENmMyMGhkTzVqaFM3RWlFOXNXYmVTWVVsOFJMQVNiME5KZkRpTXhmbWRKSkNpSTMzeElPMkFHajB5SXUwNFl3V2pmY2xzeHVJR0NSeVVHY1ZWbm02bU1OYTB3ODNzK3dvczZGWVZTWm9pRTFYOTlKdGlnYis4eW54empGWE5pSGpSb1VUTFo1VGh2bkJLS0t5STZoMkVOS0FldTNNODN2MldNYVpURU9WNFRWcFQ1cWZUeHNra3hTOHdMdDNKc0hCNXpoQ1NtYkJmd3plWEV2akwzc3I3MGliVWdpU1Q1UFNWVG1BN1M2T2c1Si85UUkzKzFPYVR6UDN2R3hvU3R3Qm9FSEI2K1lwV1BhdW5NUi9CK3AxWXlSaERJaE1nc0oiLCJtYWMiOiI1MThmYjBlM2YwMzkyYWYzYjljN2E5MDFmNjQ4ZDZhZDY1MDVjNTA5Y2UzZGI1NTZjMGU3YWI2N2IyOWYyYjliIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-593435004\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1473827943 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nMXQGGGSGQFIfGwlrnEYCl0LIB0DtCvcZqQRlbGj</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1473827943\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:13:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNmRkNmeUo3bWZQQkRzeUZOTnVWSEE9PSIsInZhbHVlIjoiUFFTYll3dHprTHl3NXVsQW0rS2lMUHY3M0lJN2JyUjJwUDdLV0l6MVVxREdmQ1VsSXhvaWZ2MEwxcGNzbFlSamw5bFpTeHBEWEZtdDBjQzdTTlg4UUEydGNXaXg4YkZQcS92QXNKY2JRYkZEN1AxOE5SaW82TEpvWi85SjZuTkNLWW8vY09jUTE0NlZCNXhudEJBN3FnbXZwemZSZ2Q1TVduWHA2WHVkb2dUTUlEall1VWpsWllTL3pkMFJFMHlEK3ZocGxySjh1eFZDSEVXY2lBRC9SaDJHd3l5Y3V3cDJaUWNLQTN3dVg4b2MxaE41NDJCVE40R3JyZGkxc1Z3a0hPL2lZOVFieFlNWFF6dnhhc2locHhHaUE2SzhwNXNQbkJqSDlXMG4vMXI3ZXJrUEd3aCt5MFlyWWtIdlhVR0hvaUxaclJGYW4yN2NaTk0xSnRkWm5JWmJvOCtvaFMzM1BDcDBqTDBoUllSS3MzN2JiUTRJOEdHYUJwN2g3WnVGYS9yUC9ac3g5dU5JY2ZxdXZTU2k4aEJXdDI0Vmk3VEN5QlVaY2tjMHBPcnlpaE83aEEzN25RdHJVLzRMdUhEdU8rVHBVaElma3Vkb2NlSGR6cVhaQmNKcmRTSmxaNTBDS2NqSFAwYUhVL1UwRm5yOXdwcnhrWDA3bFJsTHVHNnkiLCJtYWMiOiJjNjVjMjk5MjFmMGMzMjMyYTBkYTY5YTg0MTM5MDc3M2ViZDg2OWE1ZDkyYmMyZDMxYzdkNmEzNTU3Yjg2OWZmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjNlRXBUMVR4YlUwRmxhU2dpNkNwMnc9PSIsInZhbHVlIjoiakxLdkwzaE1kdkUyQzg2QXcxSTJnVkFKVUtIbXFvRTlNbGhTYlp2a2I3L2dlZlBHYXBjbFZQSGJ0VXhlUnFJeG5HNnFpNmFFTGtFWS8vZ3B1SHMwbzJnQkJ0dFNuL2pQL0xybUZpaHVHQUsvbThLTTloaGVuUUMybE0xWnFQc3FsVEg4dTUxemRCNUwyUXBDazBSQkxKQmtobG14c3VYK2NhcTRPdW56UWdkdFloOFNVSzJhN01GU2NnZHZOMGZtZDEwV0NCTjBmd0dVcGh4TXZWR1F3Ni9CYUNOTFp0RnpwZWJ6SXJUdFpiMmFjQ2pGakhuY3FXU2VKYldRL25vYkl2UVoraVFaYWQxMVR5ZTFDczFBcU5qVGNwQzlPSmNEbkozOW14cVAzSnhOM0dDb2F6T1ltcTBRWDlsanBHV2dRWGc0dEZ6MUQ4Um9FK3ZocXlXcFEzcFViSW8wRC9vYUN3Y2tCUVB2NVNNbkF1ekMvZlB2VkFHSTEwQnpMMUpyVFBHQWUvQ09XcXlZbjRtaTlFMFREOGJFbTZjQlFCVGlVay9QMWdUZkJCYmN4RnJLMmM0VzZvWmVCQS9tYjJoNDQ5SzJYYTVxUkJocnBZVlB1SlBsK0xjendDWThLNWgxQTRpUFFuLzFVWG9VTEhuTExsREpOODRwdTlhL1N5SUsiLCJtYWMiOiJkZjhjMTA0NzFjMzU1Y2JkMjY2NDI3OWU4MTliMWNiMmM1Yjk4ODUxODVhYThmODI2OTFhZjk1N2FiOGJjYmU0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNmRkNmeUo3bWZQQkRzeUZOTnVWSEE9PSIsInZhbHVlIjoiUFFTYll3dHprTHl3NXVsQW0rS2lMUHY3M0lJN2JyUjJwUDdLV0l6MVVxREdmQ1VsSXhvaWZ2MEwxcGNzbFlSamw5bFpTeHBEWEZtdDBjQzdTTlg4UUEydGNXaXg4YkZQcS92QXNKY2JRYkZEN1AxOE5SaW82TEpvWi85SjZuTkNLWW8vY09jUTE0NlZCNXhudEJBN3FnbXZwemZSZ2Q1TVduWHA2WHVkb2dUTUlEall1VWpsWllTL3pkMFJFMHlEK3ZocGxySjh1eFZDSEVXY2lBRC9SaDJHd3l5Y3V3cDJaUWNLQTN3dVg4b2MxaE41NDJCVE40R3JyZGkxc1Z3a0hPL2lZOVFieFlNWFF6dnhhc2locHhHaUE2SzhwNXNQbkJqSDlXMG4vMXI3ZXJrUEd3aCt5MFlyWWtIdlhVR0hvaUxaclJGYW4yN2NaTk0xSnRkWm5JWmJvOCtvaFMzM1BDcDBqTDBoUllSS3MzN2JiUTRJOEdHYUJwN2g3WnVGYS9yUC9ac3g5dU5JY2ZxdXZTU2k4aEJXdDI0Vmk3VEN5QlVaY2tjMHBPcnlpaE83aEEzN25RdHJVLzRMdUhEdU8rVHBVaElma3Vkb2NlSGR6cVhaQmNKcmRTSmxaNTBDS2NqSFAwYUhVL1UwRm5yOXdwcnhrWDA3bFJsTHVHNnkiLCJtYWMiOiJjNjVjMjk5MjFmMGMzMjMyYTBkYTY5YTg0MTM5MDc3M2ViZDg2OWE1ZDkyYmMyZDMxYzdkNmEzNTU3Yjg2OWZmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjNlRXBUMVR4YlUwRmxhU2dpNkNwMnc9PSIsInZhbHVlIjoiakxLdkwzaE1kdkUyQzg2QXcxSTJnVkFKVUtIbXFvRTlNbGhTYlp2a2I3L2dlZlBHYXBjbFZQSGJ0VXhlUnFJeG5HNnFpNmFFTGtFWS8vZ3B1SHMwbzJnQkJ0dFNuL2pQL0xybUZpaHVHQUsvbThLTTloaGVuUUMybE0xWnFQc3FsVEg4dTUxemRCNUwyUXBDazBSQkxKQmtobG14c3VYK2NhcTRPdW56UWdkdFloOFNVSzJhN01GU2NnZHZOMGZtZDEwV0NCTjBmd0dVcGh4TXZWR1F3Ni9CYUNOTFp0RnpwZWJ6SXJUdFpiMmFjQ2pGakhuY3FXU2VKYldRL25vYkl2UVoraVFaYWQxMVR5ZTFDczFBcU5qVGNwQzlPSmNEbkozOW14cVAzSnhOM0dDb2F6T1ltcTBRWDlsanBHV2dRWGc0dEZ6MUQ4Um9FK3ZocXlXcFEzcFViSW8wRC9vYUN3Y2tCUVB2NVNNbkF1ekMvZlB2VkFHSTEwQnpMMUpyVFBHQWUvQ09XcXlZbjRtaTlFMFREOGJFbTZjQlFCVGlVay9QMWdUZkJCYmN4RnJLMmM0VzZvWmVCQS9tYjJoNDQ5SzJYYTVxUkJocnBZVlB1SlBsK0xjendDWThLNWgxQTRpUFFuLzFVWG9VTEhuTExsREpOODRwdTlhL1N5SUsiLCJtYWMiOiJkZjhjMTA0NzFjMzU1Y2JkMjY2NDI3OWU4MTliMWNiMmM1Yjk4ODUxODVhYThmODI2OTFhZjk1N2FiOGJjYmU0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1924892203 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1924892203\", {\"maxDepth\":0})</script>\n"}}