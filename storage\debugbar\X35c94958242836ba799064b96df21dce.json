{"__meta": {"id": "X35c94958242836ba799064b96df21dce", "datetime": "2025-06-30 23:10:29", "utime": **********.72223, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.266984, "end": **********.722244, "duration": 0.4552600383758545, "duration_str": "455ms", "measures": [{"label": "Booting", "start": **********.266984, "relative_start": 0, "end": **********.639566, "relative_end": **********.639566, "duration": 0.37258195877075195, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.639575, "relative_start": 0.3725910186767578, "end": **********.722246, "relative_end": 1.9073486328125e-06, "duration": 0.08267092704772949, "duration_str": "82.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48184888, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00759, "accumulated_duration_str": "7.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6748161, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 20.685}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.686046, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 20.685, "width_percent": 7.246}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.701422, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 27.931, "width_percent": 7.246}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.703531, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 35.178, "width_percent": 6.061}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.708099, "duration": 0.00265, "duration_str": "2.65ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 41.238, "width_percent": 34.914}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.712986, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 76.153, "width_percent": 23.847}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2018632736 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2018632736\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.707098, "xdebug_link": null}]}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1887135753 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1887135753\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-697913125 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-697913125\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-33908478 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-33908478\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2099174237 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751325027058%7C14%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktneC84dUdURUliS0ZvNnZRV3B6VVE9PSIsInZhbHVlIjoidmp3SW0wMnY1ZWNHOCsvYmdJT1dURGpJclZkbEg3WllFMkwxdUEyRzIxTWIwWlJtTTUwaTUvM2hZdm54T0JPU3JoRk5IWExqUW9LblZmcVYrcHo2Z3E2YVl0RGpZcnNUcG5pMmZyUUIvMVU5S0c2OGdlTWsyREo0LzUxY2IyZ2dHenkxRTFLWldsZHArNkNDNHFNdk5mMzhPcm8wYXlSN1VObEpta3R1VEtIY1h3UDZxTXZMMi9icnFMSWxZdGN6TGdEa3ZrWVU0OFUxOU9QeWg1MEFvd1FTMlU3SGFUckhVUUJ5cStTa08wdG5PK2I3K2d1TkpxYWhNWFpqc1lYNzBnN2RMbG9teVRRdm5xRC9BU0F3QUxJRmdNaVc3QzIrL0loV2tiOGdRMU9zdHRoc2hXM0VNeDFROVhKSXp1WlJzS0FlRENLK0cwNStwSGVHQ0U4YXpGVTM4N08rNHhvdTdVb2R6YktEdzQ1dFZBcW1iZWpaVGV5Z1ZKTjB4c3cyTlA5TjBudVpYcDNpQ3JwRXpYNFBQU2YzbXJyNVdnZ3lQUGUrOCsxVTd0Y3ZkVWRtZUpTblMrVTJYQnV4a1ZTQXVOZGNzckxpRW1HTjFHMGxxWmdKTGtqQWpTRFVrMXBXR05EN1hSUm0zUFpuSThQQUZLZE1hdm91L1NMTjArZisiLCJtYWMiOiJjMDliYzc4MDAxYmY2YmM2N2E2ZDM2M2E2OTc4OTkwMzEyMDc0YzhhMWRhYWUwNWE1MzY3MTk5OTNhODZjYzI0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkVQU2gvcGhDbm9uK082UXp0MFVHakE9PSIsInZhbHVlIjoiRDBxdVFHY3liR05SenNiN3JXWGw1ZmlnUHU4d3hzQzJ0T1NyMndWdjAxYWlQSTl5WlMxbmJldlRFV2FsaUU5R0xPUFBBNTBZYzl6cDhtbzBkamNUdVlDcGFXeUZCZGt2VFVYZFo4TnVYVVFXVVd0TGordnRlSjlIeVZ2NXhIRGlrVVI4bGFMWTVHWGJoZmc3clRtUnlwNkVxeTB4YXh6Q0gvL1pKLzNWLzYwRk81d0FzR1ZkVzNYenVST2w1NnQ5dmhXVVBxaEtBeGdFK2RvUWxWSGpnVU1yc1pHR2RUSVgyNEpTa3p5U0V0U0J1cnppYk1UTDBxalEvL2lteWtQOFVveTNmUi81b3JzdEJwRGlHaUplb3ZQcFNiTUd2clBqWXIyNTJTVEU5WUxxZEJZTFlsdlNKM2xSWmJVR3QvdkJ4VmgrMjhaVDN3T3lKb2RSdVlreG5RYXJ1SnVwVWM3NlphUmNHc0hzQVU4N3JHbUR5QTVLRWdWTkJscisxUzZBWGtyQlNuTDZzWWdBNVdKQnNrN3J4dERROEoyOGpidTJJVDM5TEF5eHd6MUVPbHNXQ2pUOS9TMXFvTkZWZys5VXVXTEFvUXBvVVYyMlFmMURmcHNRQkhqYTRRdmYxRVltSE0xdDhndVdKUGt5QVJYY1Bpb3hZLzdpVGFEckdqU2EiLCJtYWMiOiJiYmJjN2E3NjIwM2IwNTljZWQ2ZWViODhiZDk4NzRhZjhhZmI2NWM4ODJjNGNmOTcwNWZhZjVmMzUxYTU0OGQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2099174237\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1342296431 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1342296431\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1813041445 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:10:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikx5TU55ejh4eFhubHpQSHVwMDNwVnc9PSIsInZhbHVlIjoid0RYVnNjajJ6MjlUVS9FdU1XeHRKNEthV1FjNXdUNTh1a1MxS2xrYitMV1R2a3VwMHZCN3NWTkhGU1ExK2phaFZqd0pvNFVPZTRLLy80YVFFUEJ0U3VzRWdzS1JrdjFmTitwYm5IMXMwY2NnajB3OVp3a28zRXpiRGJhRytSN2pXeHhLMjJPSFQ4dFB3cE5saldkM1k4TzJ2cmE5ejFvYmVyenZWUERiN2FwK25rQ1hYN3dMWTR6YU05K2dhZ0ozVjZQODB4VVpZMGVLbmFVME0yeHlseVN0OW04NUJGWDltWmFzNDIrbnlHdVZTK0xMQm5rQmJZSkVHWkkrZEs3VkFxZ0ZxQytkNWtJZERpZTNTU3cxVnZlWHFKTnZaU2Rsa1AyS05xVjNYSWRsa0N6bTkwcXd1Y3Bwd2hNMXRkSWpvaTdlL0dTam4wT3V5ZmRCTk94d0orWGRBdksrQ28wUHB4OWNLNE96VWdTSkRVakE2WWxJYWNQczFRNTRvUDZFNHJLdVlCRjgwRXdja3hpREJsRmFsTVdzMGo2RkJUK0Y2MlBzZUpuVkpwODYvTnIyT0tUaXR5OEwxb3phYnU2Umk3VVRtMzFvMmhlTGhhVEF1UE9tVlk1YUdJSzB4MG5zT3ZwNTNWbW9rajlnYmRRU05ITkJJRENsMEJCekc1dngiLCJtYWMiOiIwMGIzMTkyZWZjMjU3ZWVmMTAwZGEwNjkzZDY0NTg4YmVkNTA0NDJiYTI4MTE3OTAzNDY2MDhhZTBhYWFiMmI3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImdwaDdNV2phclQ2TmdoYVVxN0YrRkE9PSIsInZhbHVlIjoiRkFrNU51NVEwbVlySTMvd2JDcFY3MHVXN0liMDhuTGl4ajBWUHk5UUViYnBiY0lGNHF4S1Z1VEV5VEpzT2dTaGVIZnVFWVorQXlIZytWeVFaWm5CbnM5eWREZHNEUldFTXV2YlVDT25JVmg5UGNsdlkwQS8ya3hxZjY0OXdIeGRXbU9mM3pVcjBoK0Iwc0dicG91VDNoOGZMa3FFdndsVno2MmhJcjVmWVRkcjl2VDEzTVNuZlYwc0kxdndHSnRuYThJbzRJVUI5T0FGNVgrL0o5dmtpSGhIOTNnTG80N2dZMHhZeDZ6S3lSRjJLVktLaHM1RVVhYm1VL3UzbkpMRjNxc1NIYUxwbWdEUFEvbHVhNFVmL2RCYkQ0Uzh2THVzQmZ2bHpmNHUwazJMdkpuTzR1RnVnNzdSNGxTMnArQ05uNzNxMng0RGVzSVFmR0pJcDR5VmVPaE9Eb2QyV1BrR21zMjFOT3cyL0tIUG5QVE1tSDZyN0M3RHNsektPM01ScTY5VU4rR2ZsMmF6NWU5Wm9yNHBYc2c5djVsSUoxU2d1R3ZOeFM5STJWN2djdEJYai9uVjNHODY4aVhZdWRNTjY0ME1nZEFRMnpkQmp5Snl3TE1lZUZjYkluc0FYaTFkenJkeCtQd0x2QXp4bUxjMmxxYk5TV3lXWGo3cytrakYiLCJtYWMiOiJiZDliYjliN2JmYWUwNzllYzgyMjVkZGZiYjA5NzcwNjg5NjI4N2JiOTI0MjBjZDIxMGRhN2Q5MWVlMjk0ZTJkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikx5TU55ejh4eFhubHpQSHVwMDNwVnc9PSIsInZhbHVlIjoid0RYVnNjajJ6MjlUVS9FdU1XeHRKNEthV1FjNXdUNTh1a1MxS2xrYitMV1R2a3VwMHZCN3NWTkhGU1ExK2phaFZqd0pvNFVPZTRLLy80YVFFUEJ0U3VzRWdzS1JrdjFmTitwYm5IMXMwY2NnajB3OVp3a28zRXpiRGJhRytSN2pXeHhLMjJPSFQ4dFB3cE5saldkM1k4TzJ2cmE5ejFvYmVyenZWUERiN2FwK25rQ1hYN3dMWTR6YU05K2dhZ0ozVjZQODB4VVpZMGVLbmFVME0yeHlseVN0OW04NUJGWDltWmFzNDIrbnlHdVZTK0xMQm5rQmJZSkVHWkkrZEs3VkFxZ0ZxQytkNWtJZERpZTNTU3cxVnZlWHFKTnZaU2Rsa1AyS05xVjNYSWRsa0N6bTkwcXd1Y3Bwd2hNMXRkSWpvaTdlL0dTam4wT3V5ZmRCTk94d0orWGRBdksrQ28wUHB4OWNLNE96VWdTSkRVakE2WWxJYWNQczFRNTRvUDZFNHJLdVlCRjgwRXdja3hpREJsRmFsTVdzMGo2RkJUK0Y2MlBzZUpuVkpwODYvTnIyT0tUaXR5OEwxb3phYnU2Umk3VVRtMzFvMmhlTGhhVEF1UE9tVlk1YUdJSzB4MG5zT3ZwNTNWbW9rajlnYmRRU05ITkJJRENsMEJCekc1dngiLCJtYWMiOiIwMGIzMTkyZWZjMjU3ZWVmMTAwZGEwNjkzZDY0NTg4YmVkNTA0NDJiYTI4MTE3OTAzNDY2MDhhZTBhYWFiMmI3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImdwaDdNV2phclQ2TmdoYVVxN0YrRkE9PSIsInZhbHVlIjoiRkFrNU51NVEwbVlySTMvd2JDcFY3MHVXN0liMDhuTGl4ajBWUHk5UUViYnBiY0lGNHF4S1Z1VEV5VEpzT2dTaGVIZnVFWVorQXlIZytWeVFaWm5CbnM5eWREZHNEUldFTXV2YlVDT25JVmg5UGNsdlkwQS8ya3hxZjY0OXdIeGRXbU9mM3pVcjBoK0Iwc0dicG91VDNoOGZMa3FFdndsVno2MmhJcjVmWVRkcjl2VDEzTVNuZlYwc0kxdndHSnRuYThJbzRJVUI5T0FGNVgrL0o5dmtpSGhIOTNnTG80N2dZMHhZeDZ6S3lSRjJLVktLaHM1RVVhYm1VL3UzbkpMRjNxc1NIYUxwbWdEUFEvbHVhNFVmL2RCYkQ0Uzh2THVzQmZ2bHpmNHUwazJMdkpuTzR1RnVnNzdSNGxTMnArQ05uNzNxMng0RGVzSVFmR0pJcDR5VmVPaE9Eb2QyV1BrR21zMjFOT3cyL0tIUG5QVE1tSDZyN0M3RHNsektPM01ScTY5VU4rR2ZsMmF6NWU5Wm9yNHBYc2c5djVsSUoxU2d1R3ZOeFM5STJWN2djdEJYai9uVjNHODY4aVhZdWRNTjY0ME1nZEFRMnpkQmp5Snl3TE1lZUZjYkluc0FYaTFkenJkeCtQd0x2QXp4bUxjMmxxYk5TV3lXWGo3cytrakYiLCJtYWMiOiJiZDliYjliN2JmYWUwNzllYzgyMjVkZGZiYjA5NzcwNjg5NjI4N2JiOTI0MjBjZDIxMGRhN2Q5MWVlMjk0ZTJkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1813041445\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1234397053 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1234397053\", {\"maxDepth\":0})</script>\n"}}