{"__meta": {"id": "X48f2f67085c8072eb7465edb07837034", "datetime": "2025-06-30 22:40:57", "utime": **********.653963, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.16571, "end": **********.653978, "duration": 0.4882681369781494, "duration_str": "488ms", "measures": [{"label": "Booting", "start": **********.16571, "relative_start": 0, "end": **********.568745, "relative_end": **********.568745, "duration": 0.40303492546081543, "duration_str": "403ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.568755, "relative_start": 0.4030449390411377, "end": **********.653979, "relative_end": 9.5367431640625e-07, "duration": 0.08522415161132812, "duration_str": "85.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48170176, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00853, "accumulated_duration_str": "8.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.608129, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 26.846}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6190689, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 26.846, "width_percent": 6.917}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.6328359, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 33.763, "width_percent": 6.331}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.634755, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 40.094, "width_percent": 6.213}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.639749, "duration": 0.0029100000000000003, "duration_str": "2.91ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 46.307, "width_percent": 34.115}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.645087, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 80.422, "width_percent": 19.578}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-603868075 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-603868075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.638478, "xdebug_link": null}]}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-313120198 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-313120198\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-860778165 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-860778165\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2017922860 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2017922860\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-705695312 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323255132%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZraG8wMnJlTDVaYnFxREgwM2l6V1E9PSIsInZhbHVlIjoidGlRZVVSa0VmUVh0QllZS0NUdTFra0tJZTZDRC9MRDRaR1phVjFNbVNWQURwWnp1YVZ0bGwzNGgzMGtrZEgxUERvTlNJcW5XMVhLd0oxOERNS2x4aEw5aGwwck9FUE16Tk94SzkvYTJuUThma2dUMkhlcmFWTmh0MmFBUzkrU1lwY2xnQVV5RVZRUUF3c1NmM1ovVGprSkdOODVZWFdUaHc4bjladm1UWnd5RHRKSEpKK1R3aHhoeGxaNmNITGs1L0dieUJuVGdkU0tIbWd0V2FvZW1TMWFWZDY4MTFLUVhBaXVhaTZMT3hpd2cyZm5EWE1YSUREejNvaTF4dXM2bWZNVng3eGVLQml5Q29iRlJ0TG9ScDQ4Mk9EbWRqa2FhQ3g2cWEyNzkwejFBbzRtSnR3eU5JWjdQemhqdmxWTVF1L0FaQTk5TEIvVzMxUmdkbUFzRVVCbFlpaDFMaUptejMvNXBHVWw3Qk10UWZUR3VtSTRlbTYrOW40blBTVlZvNEZNMnJCTGhZQUdDQm5oaEl0Q2poMngwaDFWWUg4MEVjUUN6TEN6b2o3QmJRNllHalJtcktOK0xFc2pJeHo3R2FIcXVuR2t1MHZWeUxzR3M3bXR6SEtmSlJaSTVnaGJOQ0ZqR0hTQk9jZ3IyeUtqRDk2bnpNSHpSb2pQSmNDaDIiLCJtYWMiOiJhNzM5ZjE2ODc5MDdjYWU4ZDg3OWU1ODJkMWZjMTQ2NThiYjc5ZjU4Mzg3MzkwZTQ4ZGFhNTMzMGRhYzdjMTdkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Imc3bmFVRjFway9Xd0hQREI3MG5JZnc9PSIsInZhbHVlIjoiUzhaSFhWOHhUN3E4UFlFREM5K0IxRTVhQ3JRUldBbFROMnF1NWJJbWdrRmQ0cVg5bXZWU0xtQ3pMZzFnQlpPTkhpNDhIUGZmeVZXZDBGZ3JkdTRwVy9oalpmMlNNenVsMCswYUluZzNmY2VXQ1pteVEva2dLQnFvcXZJRHVrY0xxbWZyTWF2ZFg5RkI4b25zOXREZjNwS2JiV20vOW9vNzZFa1VUaUxWcHpYNVRDaWhnUzNyVnRsSmE1Z1NEUFdkdlIrS01FTFU2VE8xcU03TlN0ZEtrdjJEZ0h5Uk9uRlJhRVk1dEFaSlJXU3p5emRSdXVhUnNDd09sQytQQmNKREY2cDFXVTB5bEVJSm45MnlKNVI4UEpxVjFXYjdFbC94VzUzSGJHWVdFejN3cFFkNnhITkVJQzRxTVE4cXVHMU8rUmE5T2xoQUIzZW9WUzBzV3ZudUxSZzl5YkZCdWw5K2lzQkhZMGlTSWVNL2pLRmxhbDRESEJpYThZS1NSbG1FTWtvaDNnS2pEZFFDZ0k4NGJwY2NrOHJKMkJQSjA1M09UMkozcnFReWdRaktzQTJDbDAzZmhIUU14ekk3TWdLN294Zys1eTVtdEE2eW9WUDVhZE5rRXNoNU1hOVBtNG91UCtBU0dyMGlsaFc3ZXg4aExIdnFBZ0hHRzh0bmg4TSsiLCJtYWMiOiJkNTFiYmE4N2UwM2NjODliOGZkYjNkYTBiNGExMzZmYTA3MTBhZWViNGZmZDM1NWQ4NDExNWZjYWM0OTE3NThkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-705695312\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1254626237 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1254626237\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:40:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5ESjVXbGdUOXNQOXNvVU9ZeG1BWFE9PSIsInZhbHVlIjoiSjlMeGhBY1hHMzdMTDA3M2NkTWR5SlVmZy9aM0dRVzY5SUZPZ3FRb1R1YTBOOEhYVjBHR0l3ZVFGQ05pWTcyOGFUNDY2ZzVwajQvSEpIeitmeVdvcURlaEJteUt4c0dsdUpyV2RzZFpiakh3RVRGSUhBaEtITGU1VVNWSVR1OXp4WnE0Y2pENk5kQXN4RDhvSlZ5NUVUYkVpZXNESWo1VFREMksyRFgyVjVHcktPMFBaaUhqbS84STEvMzBiNUlEY2cxdjVDOUh0N1pybVh0aEw0K0thaG9NNFdUN21uODJ3cm1WdkFGYjBDMGVLdE93NzdNZmFGdHpWVWZpOWtCaExGblNybWFpdlF5NWFHRzdjUC9MTWk0TXN6SkRkbzVYSkJKNFlIZjFZWUJQM2lHZXppTlJMZEY1K0psNDU4Umt4WDZWUk9kTDdjbXJLNUEwUkxPWUNtNlU0WjE2M0tKdEVaSWhQVlVBUUlSMGU5bGxIcHprZjhvOEZUMFpzMTB5NnRpNFRvRVZwM3lBcGpaakNJRFV5ZWdoMGJNY0JJcXBjbzJBQVloOW5yZ1ZGUmFYODRhaG1lSlU0RlFCeUZCR3M3OE5sOGxRMTBNYXBHd3o3Wk1XZGk1VUJmSUltMURCT1FPUGZkQWRNRXBWRjhkUkw2RllOeWNwNkRTV1o2anciLCJtYWMiOiJmNzA5YWE5MzExZGZmYWI3ODU5NGI1Mzc2Y2Q3MDZkNDgzMzFlY2JkNWQwNTFhODBkZDQ2NmExYmU5YjBkZDEzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InNSRlBEM1hUUjg2NVNVSTdRclhrR2c9PSIsInZhbHVlIjoiQ2xvZlFBdzVwRE9KbTVuZnk4bGJKNUlpSGtsbUpUbjR2TVZvY0N3WUdHYzFGRHBOdXZwMVlJeVBZZjBLeVVPNDY5VHltRTVwSGU0cG0yYlFJLzMvcjBZSG1meHZJaUR4T1JTd0RSL1BqU2ZESWJVbTVxUzJYVjdxNTlRUi95dVh4bFJmN0J3b1Y0TlRMc0c0ZTdwS1l5L3Zsalg1bGsxMHJjQjlmYitsQ0FDK1BmTG9aeUdRK0dxYkJQVnlGdVU0TXhyNmVrOWthNVN2dHJmZHhwbjd2QUY5MHYrWDdaQmI2Qkx0ZzVMeDcwQ2ovb1JjMVFMTjRPOTZsUHZGSEJ2cXZxVThUcTlWYW1PMVZHbXFWdVFVWjZvNW9WbXdwR3RIWG1XUHJTREEraFlvOVF4MU96K3I5R2hnd0JiaXFkZjVvMTJiNldmNVlqczRnQjlrMWk1d3RCMzdPb1dpZUJYdEZRWnFEbWpGTzEyZURWQmxhTndlOFUyOTRoaVhDa084WEdib0phRTdDd0Y5K2JydEh1V2ZrdUlKMjZ1dkhwSDEvaHloSit5cXhEM2QxR0FpTS9kSWpYczJWOVJVSGZZRldaWnJVNTNuUEl2OG11bmV6ckhmYjlQcU44c3NraXZERWlXSFJCWGtyc0p6SXphZHYzdGxWSGFIMkNsdFo4ZFAiLCJtYWMiOiJkODdiMTA0NDQ4Y2M1NzgzM2ZhNjYwNGFhZDEwMTA4MzdmOTI1NDE5YWE5OWMxNzNkNmViMjc3ODg1ZDYyZjlkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5ESjVXbGdUOXNQOXNvVU9ZeG1BWFE9PSIsInZhbHVlIjoiSjlMeGhBY1hHMzdMTDA3M2NkTWR5SlVmZy9aM0dRVzY5SUZPZ3FRb1R1YTBOOEhYVjBHR0l3ZVFGQ05pWTcyOGFUNDY2ZzVwajQvSEpIeitmeVdvcURlaEJteUt4c0dsdUpyV2RzZFpiakh3RVRGSUhBaEtITGU1VVNWSVR1OXp4WnE0Y2pENk5kQXN4RDhvSlZ5NUVUYkVpZXNESWo1VFREMksyRFgyVjVHcktPMFBaaUhqbS84STEvMzBiNUlEY2cxdjVDOUh0N1pybVh0aEw0K0thaG9NNFdUN21uODJ3cm1WdkFGYjBDMGVLdE93NzdNZmFGdHpWVWZpOWtCaExGblNybWFpdlF5NWFHRzdjUC9MTWk0TXN6SkRkbzVYSkJKNFlIZjFZWUJQM2lHZXppTlJMZEY1K0psNDU4Umt4WDZWUk9kTDdjbXJLNUEwUkxPWUNtNlU0WjE2M0tKdEVaSWhQVlVBUUlSMGU5bGxIcHprZjhvOEZUMFpzMTB5NnRpNFRvRVZwM3lBcGpaakNJRFV5ZWdoMGJNY0JJcXBjbzJBQVloOW5yZ1ZGUmFYODRhaG1lSlU0RlFCeUZCR3M3OE5sOGxRMTBNYXBHd3o3Wk1XZGk1VUJmSUltMURCT1FPUGZkQWRNRXBWRjhkUkw2RllOeWNwNkRTV1o2anciLCJtYWMiOiJmNzA5YWE5MzExZGZmYWI3ODU5NGI1Mzc2Y2Q3MDZkNDgzMzFlY2JkNWQwNTFhODBkZDQ2NmExYmU5YjBkZDEzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InNSRlBEM1hUUjg2NVNVSTdRclhrR2c9PSIsInZhbHVlIjoiQ2xvZlFBdzVwRE9KbTVuZnk4bGJKNUlpSGtsbUpUbjR2TVZvY0N3WUdHYzFGRHBOdXZwMVlJeVBZZjBLeVVPNDY5VHltRTVwSGU0cG0yYlFJLzMvcjBZSG1meHZJaUR4T1JTd0RSL1BqU2ZESWJVbTVxUzJYVjdxNTlRUi95dVh4bFJmN0J3b1Y0TlRMc0c0ZTdwS1l5L3Zsalg1bGsxMHJjQjlmYitsQ0FDK1BmTG9aeUdRK0dxYkJQVnlGdVU0TXhyNmVrOWthNVN2dHJmZHhwbjd2QUY5MHYrWDdaQmI2Qkx0ZzVMeDcwQ2ovb1JjMVFMTjRPOTZsUHZGSEJ2cXZxVThUcTlWYW1PMVZHbXFWdVFVWjZvNW9WbXdwR3RIWG1XUHJTREEraFlvOVF4MU96K3I5R2hnd0JiaXFkZjVvMTJiNldmNVlqczRnQjlrMWk1d3RCMzdPb1dpZUJYdEZRWnFEbWpGTzEyZURWQmxhTndlOFUyOTRoaVhDa084WEdib0phRTdDd0Y5K2JydEh1V2ZrdUlKMjZ1dkhwSDEvaHloSit5cXhEM2QxR0FpTS9kSWpYczJWOVJVSGZZRldaWnJVNTNuUEl2OG11bmV6ckhmYjlQcU44c3NraXZERWlXSFJCWGtyc0p6SXphZHYzdGxWSGFIMkNsdFo4ZFAiLCJtYWMiOiJkODdiMTA0NDQ4Y2M1NzgzM2ZhNjYwNGFhZDEwMTA4MzdmOTI1NDE5YWE5OWMxNzNkNmViMjc3ODg1ZDYyZjlkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}