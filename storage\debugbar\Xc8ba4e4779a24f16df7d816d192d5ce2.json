{"__meta": {"id": "Xc8ba4e4779a24f16df7d816d192d5ce2", "datetime": "2025-06-30 23:13:25", "utime": **********.917806, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.493484, "end": **********.91782, "duration": 0.*****************, "duration_str": "424ms", "measures": [{"label": "Booting", "start": **********.493484, "relative_start": 0, "end": **********.860316, "relative_end": **********.860316, "duration": 0.*****************, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.860329, "relative_start": 0.*****************, "end": **********.917822, "relative_end": 1.9073486328125e-06, "duration": 0.057492971420288086, "duration_str": "57.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0030099999999999997, "accumulated_duration_str": "3.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.889081, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.465}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9004312, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.465, "width_percent": 15.282}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.908711, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 75.748, "width_percent": 24.252}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325166735%7C25%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5maUtsY0VnbTJFSnFNZmpSUE9CQ0E9PSIsInZhbHVlIjoiNUxuNkorRWsvR0w4SWkxK3MrWldRMC82Q3JjUmJDY1R5cTU5bjF2czlFZkVOSFpMTUN6c0dpNzZMMWtkVVdObzQ2M3poNEpCSVFLWndPdXYwcWRKemFlaTk4UzFBaG5xdG0vRlA4ZllvWGsrOHFURXcvUm5RbUZ3ZXBmYk1TZnh6MWpNTlB3ODV4dmIwYy81RWJ6WThjbW1xSTF4TUlGSmRIazJUdFRRdzQ5bkZ0MWlKWVR6cDlRaDRzd2s1dkN6RnR0SlNUV3gzenpUbXFoMW8yZVZUWnVQamRSYUhQSVlqUjA2Tkd1aHY2aGk4V0E4ZFRobjNKQ2l3OVVnYjdrQ2hSNFJRSll5ajNCMzVyU2dGMzZHeUR1WTJhdDVzV2JnNldSM3p6T3VLa0dTTGZlNnErZmJaMXdCemhPWVVsOFIwQ2NmWWFKNDdtcTNiQmVtN2pac3EyNjAxdFUrQlNwTkRTLzlJSWZsQzdhdDFtdS9BRSt1MGRDbFRHRElOVVIwTnJNODIwNzFJeDFZN0o2M2FxSGx5NGNxcVVmNzc2dUpmMlNORlMza043RDZHc2ExOVlFeTZWd3RwMitObjJSeHhVRGxKVTVvNEtScjBkV2tUanFpd2tRK0xyQ2l0aFJiNzNrcGdPOS84TkRyM3B3YXFBQVBKYUFwVTl6ZjJDNGsiLCJtYWMiOiI4MWE4NThhMzdjOWVmZTMzYzA5MDllNjFmYmNhMDUzZmNkMmU2MDI0YjNhZmM5NDY0ODU5NWEyOWJhYzhhNTVmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im5EMHRURXo0QW5GQzd6QjRWS3VBY2c9PSIsInZhbHVlIjoibk44RUxKeENBbXV6cCt5WmNnNWlLSEdjbTVBWFhFOCtsejU1TUJzODZZMWRyeCsxNHJwR1R3R2NLSmFtbHMrNWNzaHFuM09iZEJGSkR6QnRacU1aTXl3cW42cTdTRlhTMktWTldFaXVQNUNCKzVQR05oODQydEEyRWdFcEtaOGw1N2ovQURQZU43VnREQStHZlE1WUdUSHJIUytHL2dMUytPWlZkd1JPWXlXNzlnY2VMclpQeHRTYk5OdTh5TDh5V3lWMy9JWmFxTHd3NXE4citVRXVQQUlITGwxaXhVZFVwOFVwdVYrZDRvU2MvWGlUem90a0RZSUdKYzNFRDl1QkNsWGNqUFZwaWFjU0JQWk1QTVZZR3ozR0ZydExwcEdFNzlmaU1Sa2hES0p1RDZZaVo0SnFvNVA3eEtsMjdIbUd0RktuODVvU0p0SE81Wlk1cERnWmJnZStEUEo1b1RWN0RLcDZxQzJjelRyVXhIWHBUc3hFdHlHT2QvdkpacWJTdXBjT3d6MTVNOWxtNlR5bE1jTFI5WFg4U29RZ3d2SjArU3VtMUJ2WXhLeGdMWG5zejluVXM5c1VXck01aGh2VTA1aFMzeWJGNmJIWjVzYU4yTS94R2hnSVd4ZXhtTmhDUDBIOUFmVkpCS1ZYNmIxK1NURDBTemhjd0IrNCtxUDAiLCJtYWMiOiJkODFiNDEwNGExN2U1N2M1ZWZhNjlmMGM2ZDFhZDA3N2MwOGJkYmEyNmNkZTk0MGI5MDJhZDdiZmIwZjlkMGU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-997058123 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0aUeGT9vcZQoKvixhqnXLFDjX39UXyfabqLnLub</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-997058123\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1208694536 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:13:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhSazFmZWdpNU1OcUZaclF5cVNOSHc9PSIsInZhbHVlIjoiVjJtVlYzaEhydkxqRGtiVmZjTDlUVHhCeUcrTmFYcXFMc0V6b001L2czdk9jR2U5Sm13bUhaZThRd2k2K0J0TUdDV0NoZVdDV1dGdGRodzdPTmd0TkttcTJKY2owTkVNaFMxK095Yk5aRDhSZWhVNGdhamVISlV6WW9PRGlRQ1ZQc2JDZVlMM3RyVHBMaFBDdHNvYm1jRzVsV1UyT2FoTlV1U2UzdGhvQTl1bUFETmlnZWFnRlBLMVFpNWpyRGhYVEtxZlhIRm1KeTVaa01Tb2ExbEF3bllhRzBCUFR3VHBUbnlqSURYVjFWU2xHR0Z4NWtCUE5LS3h0dkoyL3BYWXN4TkE4UkxHa090Ris2WHR0M0dHUndCWHFQbjU4T1RsQjFpcGtaTkNIdGNmaWhhaUkwZjFOTm5oY3RiQWhmZjlzR0QwOVA4NHRERXpITllCdVFPaUdySWFCNGpUc1JqYitpaGIrSGhvWVp3bkNZTGtpdERhVERFVUJhOFBVYmhoNjhYV21iRHlFY1hXajcwUUVuSzZJMHBWYURjYzZDZEFKNzlNalY2eFhEbjFtYlhvMFUrS09KczRqeVVZTmZpWitpOHdtYW85YUh6dGxNdXQ3REpKcXowR3hrcnEwd1N4M0RScFFwWFlNZ1ZjV05jQzVLR3FFUWh5dTBHaWR4RnkiLCJtYWMiOiI2OWY3YzMzNmZjMzZhNjViNTFkN2U4OGI2ZjQ1N2JkNjUzMDQwMmM2ODljOTIzZmE4M2IwZDQ2MGM5NjE1MzViIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZWL2l0R0UwNFlxRG9DSVI0VWE3QWc9PSIsInZhbHVlIjoiTDVwYm42bHE4UVFLTGtSZ0VJbGZIcHBVZ1FpcWxHaWNBL1AxRjRxWUtYMnk4ZWlCdW1nazNLS2xIaC96ZUtkcWtWMVo5ZjNOUmU1T21odlZ0cDZaUTNDZWROQUpaVGJnc050YldjWW5MaGVQUUFVc09nYU0zcndMQ1ZTSFdSQXJpM2xiWXBYNEVmMWxia3ozRnd2SUM2WStlYUkxVjVKTnVmc2swOWd6TEJ4WllvV2kwTjhjZzlVc0VPRzB3NDFISStrSmVsdmtmWHJhalJtRCtRc1hTdGNrNmJDMUNZQllCbzdDY1IxZlVGUWJVUE1DM2E4cWhSV1NHcmcrckhvOWRiZzJmMnM3Y29zSkIxS21tWlJveW9DQlJuaFFhM2c4dUlNZkVaZzZYTitSOU84VXVGcW0xQzFwNVBxeDRiUzRYbUl4a1A4eE50ckkyREtXYlYyVis3VEtXemNYcHo2OUVHUWlaUFdHQkpLcDVyakxhTitwRDhRUjMrM1V5TVV3YVRha0hnUENTenR5alQzbFRTanF6cVcrcVJDUE94em5pbitrYlk3VTlUWlZuZC9vOFBXZVpjK2FuU1IvaytoMnBqcW1zbkhnOVhNKythZVVZL2kvdVdWRG1nM2xPenJuZWtMY2JWS0h1aW5WaW1jNmJoSlFjVkg0MVpaS2o1SUUiLCJtYWMiOiI0OTdlNzAyNzkzZWU1ZjhlYTBhZDkxZTgyODEwNjcwOTE3ZjE0NDM4MThmYmU4MDgyZjEzMDk5ZTkxODhkYTQ1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhSazFmZWdpNU1OcUZaclF5cVNOSHc9PSIsInZhbHVlIjoiVjJtVlYzaEhydkxqRGtiVmZjTDlUVHhCeUcrTmFYcXFMc0V6b001L2czdk9jR2U5Sm13bUhaZThRd2k2K0J0TUdDV0NoZVdDV1dGdGRodzdPTmd0TkttcTJKY2owTkVNaFMxK095Yk5aRDhSZWhVNGdhamVISlV6WW9PRGlRQ1ZQc2JDZVlMM3RyVHBMaFBDdHNvYm1jRzVsV1UyT2FoTlV1U2UzdGhvQTl1bUFETmlnZWFnRlBLMVFpNWpyRGhYVEtxZlhIRm1KeTVaa01Tb2ExbEF3bllhRzBCUFR3VHBUbnlqSURYVjFWU2xHR0Z4NWtCUE5LS3h0dkoyL3BYWXN4TkE4UkxHa090Ris2WHR0M0dHUndCWHFQbjU4T1RsQjFpcGtaTkNIdGNmaWhhaUkwZjFOTm5oY3RiQWhmZjlzR0QwOVA4NHRERXpITllCdVFPaUdySWFCNGpUc1JqYitpaGIrSGhvWVp3bkNZTGtpdERhVERFVUJhOFBVYmhoNjhYV21iRHlFY1hXajcwUUVuSzZJMHBWYURjYzZDZEFKNzlNalY2eFhEbjFtYlhvMFUrS09KczRqeVVZTmZpWitpOHdtYW85YUh6dGxNdXQ3REpKcXowR3hrcnEwd1N4M0RScFFwWFlNZ1ZjV05jQzVLR3FFUWh5dTBHaWR4RnkiLCJtYWMiOiI2OWY3YzMzNmZjMzZhNjViNTFkN2U4OGI2ZjQ1N2JkNjUzMDQwMmM2ODljOTIzZmE4M2IwZDQ2MGM5NjE1MzViIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZWL2l0R0UwNFlxRG9DSVI0VWE3QWc9PSIsInZhbHVlIjoiTDVwYm42bHE4UVFLTGtSZ0VJbGZIcHBVZ1FpcWxHaWNBL1AxRjRxWUtYMnk4ZWlCdW1nazNLS2xIaC96ZUtkcWtWMVo5ZjNOUmU1T21odlZ0cDZaUTNDZWROQUpaVGJnc050YldjWW5MaGVQUUFVc09nYU0zcndMQ1ZTSFdSQXJpM2xiWXBYNEVmMWxia3ozRnd2SUM2WStlYUkxVjVKTnVmc2swOWd6TEJ4WllvV2kwTjhjZzlVc0VPRzB3NDFISStrSmVsdmtmWHJhalJtRCtRc1hTdGNrNmJDMUNZQllCbzdDY1IxZlVGUWJVUE1DM2E4cWhSV1NHcmcrckhvOWRiZzJmMnM3Y29zSkIxS21tWlJveW9DQlJuaFFhM2c4dUlNZkVaZzZYTitSOU84VXVGcW0xQzFwNVBxeDRiUzRYbUl4a1A4eE50ckkyREtXYlYyVis3VEtXemNYcHo2OUVHUWlaUFdHQkpLcDVyakxhTitwRDhRUjMrM1V5TVV3YVRha0hnUENTenR5alQzbFRTanF6cVcrcVJDUE94em5pbitrYlk3VTlUWlZuZC9vOFBXZVpjK2FuU1IvaytoMnBqcW1zbkhnOVhNKythZVVZL2kvdVdWRG1nM2xPenJuZWtMY2JWS0h1aW5WaW1jNmJoSlFjVkg0MVpaS2o1SUUiLCJtYWMiOiI0OTdlNzAyNzkzZWU1ZjhlYTBhZDkxZTgyODEwNjcwOTE3ZjE0NDM4MThmYmU4MDgyZjEzMDk5ZTkxODhkYTQ1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1208694536\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-746655687 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-746655687\", {\"maxDepth\":0})</script>\n"}}