{"__meta": {"id": "Xa7d4f0e8d3e913f12c682a30e3ed0e66", "datetime": "2025-06-30 23:06:18", "utime": **********.112534, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751324777.69628, "end": **********.112549, "duration": 0.41626906394958496, "duration_str": "416ms", "measures": [{"label": "Booting", "start": 1751324777.69628, "relative_start": 0, "end": **********.063403, "relative_end": **********.063403, "duration": 0.3671228885650635, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.063412, "relative_start": 0.36713194847106934, "end": **********.112551, "relative_end": 1.9073486328125e-06, "duration": 0.04913902282714844, "duration_str": "49.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43881424, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0030800000000000003, "accumulated_duration_str": "3.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.094209, "duration": 0.0024700000000000004, "duration_str": "2.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.195}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1007118, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 80.195, "width_percent": 19.805}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2353 => array:9 [\n    \"name\" => \"مفتاح علبه\"\n    \"quantity\" => 1\n    \"price\" => \"6.00\"\n    \"id\" => \"2353\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1792000670 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1792000670\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323255132%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjV2aTZBNTBUelh5ZEcvbm1vREhHTHc9PSIsInZhbHVlIjoiMGFqWGZ0THlsZStoUU9FNXRVQmVwRkxmdTFYcFNPSm9peGNkclpHK2Z3VXZOY2JoWlp1UVBPUlhrbVBwVi9vQ0hHT1lHWmRJOXFHRnMvZVhpSWMzNzlnZWZPK0x2bmR4VU1tWEN2b0M0czk0YW1RN0FRUDcrOFlzcCt3MWFDZkdzTEUxSkovUVRnNWpNVklqS2ZBcGNUY28zWnJhNjZzV25FcVU1Smk0YWxoeXZEcndRQ08rYVh6ekZBSzQrYTNxYi93L3dSb3NES2lZSkpHYjVYcEZJN3lMcmdtSHNzV0NKTTZFVFVnelY4N0VFRXNEbHc5Q090ZUViZ1Q0SG10alI1SThxRkUwK2VkVFpPRTB6SDFVdXBHZmwxb21YYWs1c21rVjRyb1RmdVpDRFZzWUFTZzhSUHV0TmRBNzduZ1hyMGRNc1pnMHJYWGJ3T3RFQlRCNHpXbFJDUXhLMWI4QUdQSXZyejEyN3p3QkQzbFNnOUxhOGF1YzY3VGhNVU5xcUFzaHh5RW9oQURwSG9ucmFkZGVzTU9ndjI0cE8wT3d3c1BiNTFHNjVZamp5TkxLVy8wdEVGd2d5TnFIcTVTSk1PZlVFODdKOHpCMnRPRk8wVlc1cEJYMFdESGltamVWRSswNW5naWVCS1hlOXhYOG11ZW1ic2J6LzRuckhQZXgiLCJtYWMiOiI5M2JkZWJlNDAzNTI5YjAxMzg1MGQ3YjgyN2M2YzMwNDhlZGE5NjM3MmMxMTZjNzg2ZGVhNTM5ZDFjZDc4ZjI0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlQ3VDJZT2FKaGZUQkIvSTNvUUppTHc9PSIsInZhbHVlIjoiZUc0MUNpb0FpTXRoblhaTndKc0Rib3NYeWw2WXlnZU5LMEpLSzd1bjhaeXZLVjRicWx6WDNzNXpaaWlOc09DaWZZU1lOV0tBUmVnNmNUZ0tpWHdGSjd4eUp1Q0lBVTF0dG5tZHlrZTRvb1FZZWI2UnNTNHFkbHBjb1p4aFpkMlp0US9XNTkrcEZMazd6TlMvc2Rqa00zS3oyVkx5UksvVnhybEJIT3RzTGszUklSV2l1SnJ4aXppRTZrNVJFODJMY2ZLZXRUcmxGeFJJckdEaGdYTS9WWFhuQmlWYjBFZ1FrNWZibFhBcWVMb1RkVzNQaC8rdTJlc3BxazRyRVBlQ1dmbGVJdnBpYTFLT2Y5QlNXekphc01pdFRJajd0Q0M5TXZmaUhvdjdZQWZ5MThpNDQrUWM4TGsydGMzSjNidXViWmd2SmxNUlFQb3kxQUlpUmk1MzZmSDRBSzd6WmVPdElsSGVUcVNTWS95aHN3bTZDVDM5Q0JndWF0Kzk4ckxDVE41aStsaFNOb1BtdGx5K2xxK21IeFYxRVMwYUtHVWZxSTZvVVJVWUFYRG1palVwbW0vb1UwWUNqZ3BvdS8wZXpnV0NHWGh3Y2dQVnhSa0k3UUlod0F1TmFBaWQzSmltS3dxbXV1cFpWNnZRK3VCYityVGRuOGxlWC9qTXF0V2IiLCJtYWMiOiI1NDI5ZDc1YjA0OTM1ZDc1NjI1MGYzMTUwMGRhZDQ4OTlmNmUwOWJhNjA2NGIxMjRiYmIwN2M2NDlmYTI2ZmEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-958017574 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:06:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im55enIvN0tBUlhEYS9Vby93djAxYkE9PSIsInZhbHVlIjoiZkkrWGlNUkJQd2RWMWdyTCtWb2xLcElnTzlDSlFLcU5seW9uZUxwS2pibCticTJ4OXdyanVlM0djaWF1a2tFYy9Ed2RMaUlLVTZlcHF1Tm1YVDUwMmRsbkZpVDJ4czJma2hmZmR2ZUxlR1FCalVVOTkrNGlSZ1BDaHpwVmpxOTRCenlnRE1hcW5MZTNMTytMZEJwZ3F5eHVSZDlZTmRYaUgrTmI0Mit4QUZER01USWwra2luQWtIK1VINVo2OS9MSDI0MHUxbWxsYXNUaGZ6RWpFRTZRNVdUM3crZUVLUVN4bkZHdG1Va0RYNVUzblp2K1ZNUjZWOFpISE9ZQWNUWU56RTdIRUdabkx6S1RLQlYvL3BCVklFc2JYYVBWTEkwQzk3V3lYZVZYZEZ4dHhYRlVlaVB2d1RKNG1qamZwMUcyVVgwY2toaHp2Y3J1YUR2MDg0ZDRPWXl1LzlNbGMrSmhFdW5NQTdaSDU2US9xY0hOWUJpR2ZRdGRybzJoM0ZCeVNtRlkrWmg2SUZBU3Z5bk80Qm9ucHFIWERXbzB2Uk0xb0dqVGYxR1BTaTZwQTBLcXFlNDVIMXpYeFQ4Mjk2QkJDY3JOUzB5MXpNdVV3MEY3VlhNL2JTdnBQKytLZHRSdFN5S1pmQXZiWnVWMFYzSkI3YTU0bktFc3RqQWRvSFgiLCJtYWMiOiIyYzljN2Y5YzQ3NjQyNTE1ZjllNDg2MWNiMWFkMDkzMjEwOTlmMWJiZmE4MjMzMDQ3ZjBkYjQ2M2RkMzE1ZDQ4IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:06:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImI5K1doaEsyVXdxTDdwTGtPcDJXWkE9PSIsInZhbHVlIjoiK3JwcDQwVHl4MWc0UVhlckcrSjNaM0RrcHNyQ3JneWZtNWV3ZWp3Vld0TGZhbnZUS3FnWHJDS2lGSk5IcEtzcG5pOEdzT21ZVk5tN1NWMHBCZDVoNEFiSmtrZmRwZ21oK21nYTZwUXNXNU9NV2lhbUF0ZSt5NGN2L2xCbnJ5b3VETlp1ZElEYmRhL0R0SGExUGZLeWNwN2VoV2VDZFBjU3RuSzhKMjQ4ZGoyQ0g5azc1TWs0bnJQcTYwWjBkUXRoUzZSc1dWVVNmaTFIcUVJR0VDcWtkTWQ5ZXIxbjl4Vkc5UjUraU9zVFhhdWJ2WmRURUlzd3p6S2Y3QktQTEkyZHhzcFc4am80MGVvL2VSWDVyd3RwbDNKb0R1V0lzdndaMFE3QU9tRm9WSmx4U25LaGJYSjVROWxMMHBXUEE1UGgxNll3bFFJOWNJb0FLT2MyYS8vMHJWOFkxaFFwbTh5c0taUnVZYSs1SXJyWkNyR2JlcFhRRW1qYXloOTFCSTQ3MlM4TnAzemNlWU1obW5kQXcrL0Jic3ZvNlUxRDZnYysxUSswZXNFNVg3YlJqRTJQRVVScFFuVGlEdXZWT0h5aTRyRjZTakk5Mnl1SzA0MFFYaHdZck03T09LNXV4UWpZMVJCV3BwSEhRYlJ2bHFONU0vVVJKb3c3UU9zRGNPUjYiLCJtYWMiOiJjMWI4OGU2ZWZjYTliYzNlMmUxNzhlZDc1Y2E1MzY4MTU2ZDlkNWFkZmEyODNmMDkxMWM5NDQyMTA2M2VmMTY0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:06:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im55enIvN0tBUlhEYS9Vby93djAxYkE9PSIsInZhbHVlIjoiZkkrWGlNUkJQd2RWMWdyTCtWb2xLcElnTzlDSlFLcU5seW9uZUxwS2pibCticTJ4OXdyanVlM0djaWF1a2tFYy9Ed2RMaUlLVTZlcHF1Tm1YVDUwMmRsbkZpVDJ4czJma2hmZmR2ZUxlR1FCalVVOTkrNGlSZ1BDaHpwVmpxOTRCenlnRE1hcW5MZTNMTytMZEJwZ3F5eHVSZDlZTmRYaUgrTmI0Mit4QUZER01USWwra2luQWtIK1VINVo2OS9MSDI0MHUxbWxsYXNUaGZ6RWpFRTZRNVdUM3crZUVLUVN4bkZHdG1Va0RYNVUzblp2K1ZNUjZWOFpISE9ZQWNUWU56RTdIRUdabkx6S1RLQlYvL3BCVklFc2JYYVBWTEkwQzk3V3lYZVZYZEZ4dHhYRlVlaVB2d1RKNG1qamZwMUcyVVgwY2toaHp2Y3J1YUR2MDg0ZDRPWXl1LzlNbGMrSmhFdW5NQTdaSDU2US9xY0hOWUJpR2ZRdGRybzJoM0ZCeVNtRlkrWmg2SUZBU3Z5bk80Qm9ucHFIWERXbzB2Uk0xb0dqVGYxR1BTaTZwQTBLcXFlNDVIMXpYeFQ4Mjk2QkJDY3JOUzB5MXpNdVV3MEY3VlhNL2JTdnBQKytLZHRSdFN5S1pmQXZiWnVWMFYzSkI3YTU0bktFc3RqQWRvSFgiLCJtYWMiOiIyYzljN2Y5YzQ3NjQyNTE1ZjllNDg2MWNiMWFkMDkzMjEwOTlmMWJiZmE4MjMzMDQ3ZjBkYjQ2M2RkMzE1ZDQ4IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:06:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImI5K1doaEsyVXdxTDdwTGtPcDJXWkE9PSIsInZhbHVlIjoiK3JwcDQwVHl4MWc0UVhlckcrSjNaM0RrcHNyQ3JneWZtNWV3ZWp3Vld0TGZhbnZUS3FnWHJDS2lGSk5IcEtzcG5pOEdzT21ZVk5tN1NWMHBCZDVoNEFiSmtrZmRwZ21oK21nYTZwUXNXNU9NV2lhbUF0ZSt5NGN2L2xCbnJ5b3VETlp1ZElEYmRhL0R0SGExUGZLeWNwN2VoV2VDZFBjU3RuSzhKMjQ4ZGoyQ0g5azc1TWs0bnJQcTYwWjBkUXRoUzZSc1dWVVNmaTFIcUVJR0VDcWtkTWQ5ZXIxbjl4Vkc5UjUraU9zVFhhdWJ2WmRURUlzd3p6S2Y3QktQTEkyZHhzcFc4am80MGVvL2VSWDVyd3RwbDNKb0R1V0lzdndaMFE3QU9tRm9WSmx4U25LaGJYSjVROWxMMHBXUEE1UGgxNll3bFFJOWNJb0FLT2MyYS8vMHJWOFkxaFFwbTh5c0taUnVZYSs1SXJyWkNyR2JlcFhRRW1qYXloOTFCSTQ3MlM4TnAzemNlWU1obW5kQXcrL0Jic3ZvNlUxRDZnYysxUSswZXNFNVg3YlJqRTJQRVVScFFuVGlEdXZWT0h5aTRyRjZTakk5Mnl1SzA0MFFYaHdZck03T09LNXV4UWpZMVJCV3BwSEhRYlJ2bHFONU0vVVJKb3c3UU9zRGNPUjYiLCJtYWMiOiJjMWI4OGU2ZWZjYTliYzNlMmUxNzhlZDc1Y2E1MzY4MTU2ZDlkNWFkZmEyODNmMDkxMWM5NDQyMTA2M2VmMTY0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:06:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-958017574\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2353</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1605;&#1601;&#1578;&#1575;&#1581; &#1593;&#1604;&#1576;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2353</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}