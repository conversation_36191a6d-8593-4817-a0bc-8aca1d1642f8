{"__meta": {"id": "X49c25e09908ceb81baf77a8389425d5d", "datetime": "2025-06-30 22:36:40", "utime": **********.249313, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751322999.72825, "end": **********.249326, "duration": 0.521075963973999, "duration_str": "521ms", "measures": [{"label": "Booting", "start": 1751322999.72825, "relative_start": 0, "end": **********.171593, "relative_end": **********.171593, "duration": 0.443342924118042, "duration_str": "443ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.171602, "relative_start": 0.44335198402404785, "end": **********.249329, "relative_end": 3.0994415283203125e-06, "duration": 0.07772707939147949, "duration_str": "77.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45349128, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01577, "accumulated_duration_str": "15.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.217771, "duration": 0.014839999999999999, "duration_str": "14.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.103}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.241076, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.103, "width_percent": 3.171}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.243644, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 97.273, "width_percent": 2.727}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1475046162 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=6nrx0y%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1aj6s00%7C1751322883410%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImhKQ0t2ak5BMEtwUmY1dk52NFloNXc9PSIsInZhbHVlIjoieGxLa1FzTktNVXhYT0dyUWFMQi9VZ2lGc0hpQ0IrZ3JUWkp1U1V0TzhmRmtSMU5FQTU3V1VKUFptRCt1VFljZG9VS0RVV1Zkd0s5OWZQS0YyZk1QOTdWWmFaeGZHSEprM2NUU1V2WFhnS0NJQ2dOanNubCsvcklEQzJoRmpiZTFRZVlkMVBUa0xqbWErTlI5SyszbTJPcEEvWkswM0VlUUM5TVJ4bC9LbklhNWlGcTlkby94cHRZRTVIOUpCWlJYK2c2MUorL0s4dUVERFRoYkNKWnFydzR3Y3cyM0ZKNEpHYkZqTXg3NlNJQ0lXeUZqcmVuZ1lWR29yMHZ6VlFPZGYyYmc1N05nQTdUNkM1ZXhpYndKVlNJdGNsdlZNNm54RURRbndmWVdKemQ1Q2JOK1k0T1l2RWpNU0NKV2tJMytBcFcrSTFIcUNldzlEV29mektjVWFSOHY2M3YwN2xFbDIwcFEyeE8xa0U4Mk0yVXF0SmUvaWYrRnBtNWhLMWdHb1RXemVlQjl2R284YTlUQVhRMDdsNXlqenpjWXBXeW51MEhiczVEbXdTZVptMFJpZTVMdjBwU25CL0tEMjNwc3ZNRlhya09QSzVFL1Fmc0lGc29wcHdQdmFMbU1xcFRuazJTeWlvYitHbzdPQ2NGeGIzVUx3dk5GaE5xTy9qYmwiLCJtYWMiOiI1MmQ4ZGQ0MTc2M2VjM2I1NDM0ODY5MzI5NDkxYTI5YWRlNTUzYzc5NmZhZDFlZmFhY2Y4NjhlZGM4YmI5NWY1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InIvUDBVYmNZemp2cGUvdXFZRXpMcGc9PSIsInZhbHVlIjoiYm1BamYrc3cwWkdGOW9YOFJjU2MvMFk2VVpLTE9GYjIrOE1VN3BUeGRqRWFid1kycVJNWkZhS09ERUVPZ3pQL2NUOW5RN3RCdko3RFFaeVNvYUNoNTFaTUNpdmNLblgrQzlCcHQwRWgwNDZOZlJDakptYWw5WnZMZU5ObmpQRWNRU0R3NWg2T0lFaUVQdkEzVXltcG15dC9MamVpdHNaeVFVazhNNHBTdDV3SlFwTVNvR1BqRFJ6c0t0MlVGcFRvYUpqWmRKU0Fab1pHN1ZrUVQzcy91RWNSdGt5S001RjB6UWRQTUdQNXNzN3NXbzI1TEtsNSttb3dIWTJQaTBzNmQ0VGtiWjB5YkQvS28zYVVHYXZzL2RKZHhWSTBLb3lJeVpRVCtjd093QTJaYW85WE0xQkUyUXNwUWcveStDKzVoV0RVOS8zeFRxV0FKYnMwZzlPRGZmN2Q5V1BvWkV3cDFBR21mRzhJSmhUWkFqTUVMMEFvdFRPU2lJMmtSbFMxRGpSd21vbVhBSU9zQ0lNV1dIVkJHbSt2dE9hUTEvK01aU2U3d0pINFZIOUVVUmJrZHFPMHErTlJGNW5VM053emlnM3VHdzZrNDZtQ2tlMmV2UWQ0ZFE5bzNsODJtbjJnV212L0tZYnUzVTVvOGFEVkVoWXAyV3pScVdIQUxSZTMiLCJtYWMiOiJjZDViMzUyMDFmY2FkN2UwZDZmNzI1YmQ1YTMxMjNlMGU5N2MwNzQwNzg4ZjcwOGMwYTc1MDg4NGY3NGE0NGZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1475046162\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-218258284 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A9rdyCRm7SKpfljuMnVO7IpcgTBMITMjowAdsmJo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-218258284\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1883812971 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:36:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9MZnVINTZGaDh1NW14SXl1Vm0rVXc9PSIsInZhbHVlIjoiSkZwUjJINW0zTm9oMk1IQ1J4dkZ1bUFmMGZoRkZiZFhSb3QxU3FZWEtEVjJXVWFaQ25ZLzlDZGpDVUFMbmdOV3NlRDBQNXB1RjNaS2E2elNDNDhOU2V4dzJyZ0Rab1Boa2Q5dE9qa052RGRiTGVpRGI3eHJJTTB0TzZMTTk3RHVobjNoOTQ4NHQ5eVE3aVlPdlZNajcxbFRoZCs1SDRFY0V0TVBuRHBoSUVNcUV2Rng5L3VtUk94eVFFTVJHOUY1MnQ3cXowQVB5TU44YjVNKzdDNWtLUG9MRHZWWm5IOXh1TEFQb2x0aFk4UktrS25nVlcwWUVTQlRVWi9KNStnTU91SmEySmYwSGg5ZjdXMkZDVDVMSjVNdVIxVGU4QzAwVEtncnNwOE9JS0M0NnM4YjVRTnY2N0V5MXFYd1E0N3RBcXlDUWtSVllubFNyR1BKRXBZK1Jub1IwTkN0M1ppa0Y5M2JvSzhpZDhMaTNjRDVjQXk1RmpRZVg5R0poUWoyQXYrOUh6a0tMUEFZdWthRnR1RUhNOTUrNWlkc1F0NDRSQWpHREZ3ZjlQSUcvVVFIVGtSbGYyY0IyclkvZzFTVmhGRTZBRWMvdTJvM094NEJHZExndGdaV2xzRm9IcUpsK0VoZG5FbTNPY05sVHVxanNKSVVLTnRvQ3hmd045ZjciLCJtYWMiOiI5NGU2ZjRlNDZhOWU0MTQxMzA5NzQzNzlkMTEyODdmZjZiOTUyMmZjYWFkZmZlYjMzZDQyZmYzZWU3ODQ4Yzg1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImZjUTdNNVRaTUZZUXNFTXdVQTQzR1E9PSIsInZhbHVlIjoib2pCRE5Vakhpc2ZkQVRkTml5cUtGM0U4S1FVTnJqbWpMdU9SbzIwTUlGZXA1b1p0aEw0YjRXd3VHZEhicjdEZkQrVS9EZ2FYUCtJbVV6QU5xazBxNkQ1TmhUdjFacmxTNmpWU0pJdEJ2S29BcEI0dlRjWEZFMFA3MnBDM0dtWFRXS2EvZ055YXczdWUzSDF4NGpzMDREN2h1SGVIcCtyc1hJVDN6VmxrY2dFYXN1NjlvTlRvVExwRXBoMnNLb3ZWSEgrZ09rcXlRTC9ta1pUNXpFUzlxNXJFR3ZKaWRRVHdXaURhZm9XeWVkS0pWNlFNNHRLVFVkdDUzMFd3Z1pscWw3c1psMFF0YStEa3hUL0lPVktaR01ENmpmWFRHU2NiRE5LSGZQcFREa09iWjlFUGhGZlRodXllMndxMUdTOGxHMkxlRVdqNFRHcU9oYXZISVI4N0tPNjJqZER1MDgrd2hhUXp0YklLQkUwZ2V3ZmRRaXJpMUc3dmRsNkNxVDE1WlVvWHlUUzQ0aXRvTXNNZDBpNkh0NFNRWG5UYXVBTWhxeGhhR0t0dWRpMlBNYkdObk0wcmRmVTJnR0hRQnRzU2pLUWIyRGllcDZGR0VwZkZ3RVJGMkljZk9oOHRiV0NlWTcvNFlUWS9oOHVORU9EWlU1dVc0aVBaTVRoM25SVnciLCJtYWMiOiIwNTUzYmRjYWMyOWEyY2ExMDJiODEyZDY4NGEwODM2NWU4NGJlMWMzNTE5YjFjNDNlYzA1OTI4YzkwNGY3MGUzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9MZnVINTZGaDh1NW14SXl1Vm0rVXc9PSIsInZhbHVlIjoiSkZwUjJINW0zTm9oMk1IQ1J4dkZ1bUFmMGZoRkZiZFhSb3QxU3FZWEtEVjJXVWFaQ25ZLzlDZGpDVUFMbmdOV3NlRDBQNXB1RjNaS2E2elNDNDhOU2V4dzJyZ0Rab1Boa2Q5dE9qa052RGRiTGVpRGI3eHJJTTB0TzZMTTk3RHVobjNoOTQ4NHQ5eVE3aVlPdlZNajcxbFRoZCs1SDRFY0V0TVBuRHBoSUVNcUV2Rng5L3VtUk94eVFFTVJHOUY1MnQ3cXowQVB5TU44YjVNKzdDNWtLUG9MRHZWWm5IOXh1TEFQb2x0aFk4UktrS25nVlcwWUVTQlRVWi9KNStnTU91SmEySmYwSGg5ZjdXMkZDVDVMSjVNdVIxVGU4QzAwVEtncnNwOE9JS0M0NnM4YjVRTnY2N0V5MXFYd1E0N3RBcXlDUWtSVllubFNyR1BKRXBZK1Jub1IwTkN0M1ppa0Y5M2JvSzhpZDhMaTNjRDVjQXk1RmpRZVg5R0poUWoyQXYrOUh6a0tMUEFZdWthRnR1RUhNOTUrNWlkc1F0NDRSQWpHREZ3ZjlQSUcvVVFIVGtSbGYyY0IyclkvZzFTVmhGRTZBRWMvdTJvM094NEJHZExndGdaV2xzRm9IcUpsK0VoZG5FbTNPY05sVHVxanNKSVVLTnRvQ3hmd045ZjciLCJtYWMiOiI5NGU2ZjRlNDZhOWU0MTQxMzA5NzQzNzlkMTEyODdmZjZiOTUyMmZjYWFkZmZlYjMzZDQyZmYzZWU3ODQ4Yzg1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImZjUTdNNVRaTUZZUXNFTXdVQTQzR1E9PSIsInZhbHVlIjoib2pCRE5Vakhpc2ZkQVRkTml5cUtGM0U4S1FVTnJqbWpMdU9SbzIwTUlGZXA1b1p0aEw0YjRXd3VHZEhicjdEZkQrVS9EZ2FYUCtJbVV6QU5xazBxNkQ1TmhUdjFacmxTNmpWU0pJdEJ2S29BcEI0dlRjWEZFMFA3MnBDM0dtWFRXS2EvZ055YXczdWUzSDF4NGpzMDREN2h1SGVIcCtyc1hJVDN6VmxrY2dFYXN1NjlvTlRvVExwRXBoMnNLb3ZWSEgrZ09rcXlRTC9ta1pUNXpFUzlxNXJFR3ZKaWRRVHdXaURhZm9XeWVkS0pWNlFNNHRLVFVkdDUzMFd3Z1pscWw3c1psMFF0YStEa3hUL0lPVktaR01ENmpmWFRHU2NiRE5LSGZQcFREa09iWjlFUGhGZlRodXllMndxMUdTOGxHMkxlRVdqNFRHcU9oYXZISVI4N0tPNjJqZER1MDgrd2hhUXp0YklLQkUwZ2V3ZmRRaXJpMUc3dmRsNkNxVDE1WlVvWHlUUzQ0aXRvTXNNZDBpNkh0NFNRWG5UYXVBTWhxeGhhR0t0dWRpMlBNYkdObk0wcmRmVTJnR0hRQnRzU2pLUWIyRGllcDZGR0VwZkZ3RVJGMkljZk9oOHRiV0NlWTcvNFlUWS9oOHVORU9EWlU1dVc0aVBaTVRoM25SVnciLCJtYWMiOiIwNTUzYmRjYWMyOWEyY2ExMDJiODEyZDY4NGEwODM2NWU4NGJlMWMzNTE5YjFjNDNlYzA1OTI4YzkwNGY3MGUzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1883812971\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}