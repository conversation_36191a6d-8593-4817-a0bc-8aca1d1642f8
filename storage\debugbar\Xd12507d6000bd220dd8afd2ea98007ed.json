{"__meta": {"id": "Xd12507d6000bd220dd8afd2ea98007ed", "datetime": "2025-06-30 23:14:01", "utime": **********.940126, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.519572, "end": **********.940142, "duration": 0.42056989669799805, "duration_str": "421ms", "measures": [{"label": "Booting", "start": **********.519572, "relative_start": 0, "end": **********.881326, "relative_end": **********.881326, "duration": 0.3617539405822754, "duration_str": "362ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.881334, "relative_start": 0.36176204681396484, "end": **********.940144, "relative_end": 2.1457672119140625e-06, "duration": 0.05880999565124512, "duration_str": "58.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45060224, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0029200000000000003, "accumulated_duration_str": "2.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.909134, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 55.137}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9186502, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 55.137, "width_percent": 15.753}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.926986, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 70.89, "width_percent": 18.836}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9323611, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 89.726, "width_percent": 10.274}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-395733023 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-395733023\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1360515990 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1360515990\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1352512290 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1352512290\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1432526692 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325232686%7C31%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imo1ajdsa2RHeGhZdS9XcEpQMUdITlE9PSIsInZhbHVlIjoiaksxenhmeUZVbmdaZEFTR2dUM2JqdDIzT1VLNHlCZDNTTWVDbVNMVkdzSml4RGxEc3A0WHArU3NBVFJ4SEtkSG95NnFCNGp1NVFVcU9CMXNRL3JGYVFZTU1qalExNDlCaU9XRHZPTlhGaG1wT0hyK2ZTRXUwOER0Q3hNK1VLS1dwdHI0WkJhQStuVHBsYkhxajdZUWhnd0FlZnVEYjUxVUFob29Nb3NlRG81UmJtZzZ1YnhpV2lqUWN3T3BUVkFkU2t1SHJCQzgvRm1NTWpuRDdaWFd2WExWRE9QMkg4NDVadE5Vb2wraWZiOVFNRGxxZHNoa01SaElYRkh3ai9nTC82dTZsZkxXTTRlamtzWFkwbW05NWsyM29WZ0pXSENhVFZEREJoL2QvWFAxZ0R6c200OVBBdW9oQ09LQkx0MWFGakFBVUNBMEFIRkNuNDVuTURBaGNuZXlXaVVCbGZVRG1COXI5UXB3UnlmRkJoblZKSnRVUlMvallqTGdhQTBIdHJMczcvYkYxNkEzT0FZSUszVjhmVC8zRElEd3JrNWNOVUhrbE9LTGxsUTRBMXcvTUtXL3cwaDFZdkZMa3RyYXRoSTlxem5aUER6Z0xDT0tGOEQ3NEpyajVsK3NxaG5IRU0vaWNUZ20zTWNTR0Fqam9nVm5DRjgxalBnMXdXdDQiLCJtYWMiOiI0MGZkNDhmZjk1M2U4NzAzNzY2YWNkZGMzZWEyYmRkMDNmNDcxNjRiNTE2YjBmNDI2ZTYxNGE5YjY1ZDRhNGNjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkUrUlJBV1l2Sk5kdkVlU0g4ekpyZVE9PSIsInZhbHVlIjoibG5iV3hHdEZuM21NY2xDK2ZjMnJYbEJ2N3hMQm85OXdRUFl2YnA0UmtmNlpwYnRNYmRrdC9mbkh1WDRQN0lnSEp6eXJ4YzFpbXMwMFFZS0h4NHJuTHFpcFU5dnV5WUNYMDQxK2wzMkpvditQNkp4WWhYYWtPVG9aNTRFc1BzY005Vnh1SVR1Z09JZFA1cStLMW5EYjFWbk5SZUZuOS8vZGFsRVVvTFN4SkhGWGpaTEh2SThCZE84NTkwUisxOHR6Sm1zMHM0OEJMdlRvb3htRHFiT05RS3duaWpMZlJBT1UxZmhZSWtmcHJNVUJ1Z2pjSFVDcC9sQ3RyNmh2OTB0YjNSV1lCR0FJK3h3UXEvRk43aStwcFNzdVhuYkZEYXMwVE53MVZ6UW5oNnQ3YzB1a2s2NlJUaUprdWM5V0x2RHNLNUxRNEdRNkJ3M21tZjU3anBDRXNwbEdrNTJnZjVGYlFkb2N0VXNvY2pTQXNCZlp2YWdkYWRhMUlQZi8yeFdYUkc3cUtKWEtHc0pLdHFHQ0xQZTVSYnlmNW4zd2o4d1lZRExEemJ1WTdCNFNCOW0rT2NuUWNxSUkvcGZka0Q0V2VZK0NlWW56U3JGSVdxR2NxZ1ZOSnBOTU1VRWlOU0VUY3ZwTFgwSG9GY2h2VzYxcXZieldQSm5PQ3p5RnVBK3MiLCJtYWMiOiJlZjY1ZWI1MjZiNmY0OThmMjZjN2M3ZTA3ZDA5NjFjYjczYmZmY2YxZTI1NGMwZDI0YTZkYmVmYjE4Zjk3NWNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1432526692\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LzQkrwbVBtmiKyPqbktIsF6wiC8bftN1cyLybXUc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2084074238 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:14:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZHVTBJNXArSFcwS3NpdVBSSGxnWWc9PSIsInZhbHVlIjoieVFUNmdWYkRJTStnOElNTUdaeUFwdHNobmZGSDg2RmZVMzd5MmIydHZWS0o2cGxnR0NXVEFpWjNVVDdObTRNZEJkNURabWZ2M2I4cUFmbFlzOVpWYjZyaGp0U24yZ08xTG9xcGFkY2dOYzdYWWx5a1hsbGR6KzQxZWVMbk8rYzk4UEZnSE44OTVqbDhmUlVvVVlZTWJhdkY0cGdrdEliK3F0eGRxbmNTOU9sYjdXR0dlMkgvaWRTYjlraDdoLzlFUjVndlNHRG9nM1pGQk5JYkdINFJHNjQrZjRBeVd5L0FOaU9aZzc4SDZhbndiN1pGU1pDYWpGMVRDYnZnbDlWdWFCN29RZ014MlRHOFJNcUp2WVFkWlRmbUc4QzFqSjcxaVdNdEE3UmdwbWh6Q2VyTm9JbWNVQ0xLbVB0UGkzZERTV0NwNGhTakxiQUt5eWFmRVc5RllQSUU1b2VRdmlwNnNuOGZlZVNxNDJQOW00UGVRSWtNa3Y0V0Y3bW1rblRzUFFrSUJNSC9ReS9wMDA4bWdHYkdaKzBQcHJTWGdidlcwakZNekFhaU81T0Q1M0lzMk1vRnRiOHNqQzFUMG1JSk5LelZQNjV2QjBGRGpOc3Z0RmtwVnNJU1pvUmxLVFFZbEwxRlNLYXhiZ2g2YW80WVJ2MUtFbnc2RGNkR2NVVjkiLCJtYWMiOiJjMGExMmEyMTZkYTU1OWVlODdkYmIyOWJjOTIwNTU4MjFmZjM1MGExYWVlOGRjZmM3YTA4NTdhOWMzY2U1ZGNjIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkdTYkJVSlAySHkyWDNlU3hOaHJQQ3c9PSIsInZhbHVlIjoiUHowdHg3RHlNS04zZm9NMW5qay84UHFzVWpZM2hqTWZ3dkZEN3VPRjNUL2drbExYWktmOTBYalM5QmdaRW43RmcrNnQvZTFVRmNEbUZ5cWJuN1FUTEM2NFh4WHVhSFdqRUcxWGhwcFUyZlBrNHVQYjlhMG5DRktIcTA1dGxtNEdZc3g4UmxCTXk3TW1OSTdMenRpWmhuQ0ozY3pTTVNFYUU4d3ZpVE1VR2ZQWFdUV3J1NFpQMi9jUkVJTm1pNDJMYnQ2U2Z2Y2YvZEs2ZC9hZFdJRWYwa3RQRnpZV2tFUXoreXZWV0VzTUQ1dVlKNWxoR2JiZEhlWXl1OGVTYm1kWWFSai8rOVNQYXMxS2NyUFhWMUE5WTVOVkNNSkVkeTQ2QW92eDlJOHFLUmtBbWYwazM4M25IS1pmTVVVdzA1M3JmcTgwS1U3UncwYTd4Z2NweCtlcStxSWU0T0FTQTlxZ2o5UzF2RFhaQmlCUFptT0FUZm9uanl3cTNxYzNWUElaaEwxTmxqWitjdFRWY2k1WVJ3QXNHQzZ3WHpKT1JqZTJFVFN6a2hhNXlMcVVHL0RlTW1IMURqa0hpelZiSEQ3dndrUjBhYU1zdkhkZ1E1QUNvRVc4Rzdmc0VDTzZyU0JXT2JQMHdWT3VEUi9hRnFpS1M0UFRJNHZ2S1VTYzVpbnIiLCJtYWMiOiJhNDQwOTJlYTVkMWIyY2MwMmE4NzdkNDk5MzEzZWQ5NGRhNDIwYzRmYjU1ODllZTI3OGVjMGE4YjYwNjYxM2I1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZHVTBJNXArSFcwS3NpdVBSSGxnWWc9PSIsInZhbHVlIjoieVFUNmdWYkRJTStnOElNTUdaeUFwdHNobmZGSDg2RmZVMzd5MmIydHZWS0o2cGxnR0NXVEFpWjNVVDdObTRNZEJkNURabWZ2M2I4cUFmbFlzOVpWYjZyaGp0U24yZ08xTG9xcGFkY2dOYzdYWWx5a1hsbGR6KzQxZWVMbk8rYzk4UEZnSE44OTVqbDhmUlVvVVlZTWJhdkY0cGdrdEliK3F0eGRxbmNTOU9sYjdXR0dlMkgvaWRTYjlraDdoLzlFUjVndlNHRG9nM1pGQk5JYkdINFJHNjQrZjRBeVd5L0FOaU9aZzc4SDZhbndiN1pGU1pDYWpGMVRDYnZnbDlWdWFCN29RZ014MlRHOFJNcUp2WVFkWlRmbUc4QzFqSjcxaVdNdEE3UmdwbWh6Q2VyTm9JbWNVQ0xLbVB0UGkzZERTV0NwNGhTakxiQUt5eWFmRVc5RllQSUU1b2VRdmlwNnNuOGZlZVNxNDJQOW00UGVRSWtNa3Y0V0Y3bW1rblRzUFFrSUJNSC9ReS9wMDA4bWdHYkdaKzBQcHJTWGdidlcwakZNekFhaU81T0Q1M0lzMk1vRnRiOHNqQzFUMG1JSk5LelZQNjV2QjBGRGpOc3Z0RmtwVnNJU1pvUmxLVFFZbEwxRlNLYXhiZ2g2YW80WVJ2MUtFbnc2RGNkR2NVVjkiLCJtYWMiOiJjMGExMmEyMTZkYTU1OWVlODdkYmIyOWJjOTIwNTU4MjFmZjM1MGExYWVlOGRjZmM3YTA4NTdhOWMzY2U1ZGNjIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkdTYkJVSlAySHkyWDNlU3hOaHJQQ3c9PSIsInZhbHVlIjoiUHowdHg3RHlNS04zZm9NMW5qay84UHFzVWpZM2hqTWZ3dkZEN3VPRjNUL2drbExYWktmOTBYalM5QmdaRW43RmcrNnQvZTFVRmNEbUZ5cWJuN1FUTEM2NFh4WHVhSFdqRUcxWGhwcFUyZlBrNHVQYjlhMG5DRktIcTA1dGxtNEdZc3g4UmxCTXk3TW1OSTdMenRpWmhuQ0ozY3pTTVNFYUU4d3ZpVE1VR2ZQWFdUV3J1NFpQMi9jUkVJTm1pNDJMYnQ2U2Z2Y2YvZEs2ZC9hZFdJRWYwa3RQRnpZV2tFUXoreXZWV0VzTUQ1dVlKNWxoR2JiZEhlWXl1OGVTYm1kWWFSai8rOVNQYXMxS2NyUFhWMUE5WTVOVkNNSkVkeTQ2QW92eDlJOHFLUmtBbWYwazM4M25IS1pmTVVVdzA1M3JmcTgwS1U3UncwYTd4Z2NweCtlcStxSWU0T0FTQTlxZ2o5UzF2RFhaQmlCUFptT0FUZm9uanl3cTNxYzNWUElaaEwxTmxqWitjdFRWY2k1WVJ3QXNHQzZ3WHpKT1JqZTJFVFN6a2hhNXlMcVVHL0RlTW1IMURqa0hpelZiSEQ3dndrUjBhYU1zdkhkZ1E1QUNvRVc4Rzdmc0VDTzZyU0JXT2JQMHdWT3VEUi9hRnFpS1M0UFRJNHZ2S1VTYzVpbnIiLCJtYWMiOiJhNDQwOTJlYTVkMWIyY2MwMmE4NzdkNDk5MzEzZWQ5NGRhNDIwYzRmYjU1ODllZTI3OGVjMGE4YjYwNjYxM2I1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2084074238\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1083881296 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1083881296\", {\"maxDepth\":0})</script>\n"}}