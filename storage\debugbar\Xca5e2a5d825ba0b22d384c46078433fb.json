{"__meta": {"id": "Xca5e2a5d825ba0b22d384c46078433fb", "datetime": "2025-06-30 22:38:56", "utime": **********.723964, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.22954, "end": **********.723981, "duration": 0.49444079399108887, "duration_str": "494ms", "measures": [{"label": "Booting", "start": **********.22954, "relative_start": 0, "end": **********.625011, "relative_end": **********.625011, "duration": 0.39547085762023926, "duration_str": "395ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.625021, "relative_start": 0.3954808712005615, "end": **********.723983, "relative_end": 2.1457672119140625e-06, "duration": 0.09896206855773926, "duration_str": "98.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45937936, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.687468, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.693701, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.71467, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.717673, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq22\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.019549999999999998, "accumulated_duration_str": "19.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6555629, "duration": 0.01339, "duration_str": "13.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.491}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.671347, "duration": 0.0028, "duration_str": "2.8ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 68.491, "width_percent": 14.322}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6768062, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 82.813, "width_percent": 2.302}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\erpq22\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.688035, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 85.115, "width_percent": 2.455}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq22\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6944869, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 87.57, "width_percent": 2.148}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq22\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.706032, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "kdmkjkqknb", "start_percent": 89.719, "width_percent": 3.529}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq22\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.709128, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "kdmkjkqknb", "start_percent": 93.248, "width_percent": 1.995}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq22\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.710839, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 95.243, "width_percent": 1.944}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\erpq22\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\erpq22\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.715728, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 97.187, "width_percent": 2.813}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "RtHbcRqD8V7q1fDzvV41Fzq9te9B2VUCpYlSqZiL", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-561473008 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-561473008\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1216840620 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1216840620\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-713647968 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-713647968\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1193075905 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6ImRjOXJKbHpHSmQwQ3dNMjlZVGR6ZHc9PSIsInZhbHVlIjoicEVyaGExWDduV1JBekJFRlJZa05HOXZlWXc4ZjluRjJ3S0RFYnlkenBYdWFCdUEybk03RU51UHhhcDZLeW4vZENtUUdyZ3FDeTRjNFZBeWhXRVI4OEhaamlqa2lUVi9NdUl6bSs0U3FlUXQxdHptSWZnMndqY3FHV2IzWUxNSGZLUjQ3M01WLzZPNzBsdDZQSGxxOWd4TW1OTzRodDkyL2J4MXpRU0J6Z3owMThSVmkxUHowR201eVZhTHJpZ2lwTzd5eVlSNVZzOW1hNUdabk9TcXpCaHJScWt0YkRMQ2NnSCsxYkFadkhkcHFqQnMvQmF2TVByR0x6OU1uRTJlaEVnYkFBZGNnM0Q4anRMaWdlNHFiaW5LTnRaY1B6b0gxekxUVHZZbDB0ZnF3bGl6Wk1vdGZYQnBtYVVpY04rWEVRTFdMbHhqaUlWTUdtSEpPeEI1RUVCWWZSRWs2bTRZc3VDeUk3bWZGaWpyclpCRW54YXMvWlRRbmtYSUNGRmlGRVl2WlR1Uzd0QWlDVkJzWXpMVzFYMythNU5SczRiMHNhbFJFNisxL1ZDTThzTk1oODRoa1A3d2JSdlI2ajBreFl2bXlhZ0RwNWVVclhqa3lzODFCcmRTc0NzZ2p1YklLZ1YzY0dMbU5aeXdqVmJUVHY2QWZvWWZxZXBsamxkaVoiLCJtYWMiOiIxNDVkYzM0ZjE3NDYxYzdiYmExNmNkZjNmYjgyNTdmZjAwNTZjMGI1OTAzNWUyMWJjYzE3NjJiZDU3NDgyNjZhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im5YcmxPMTRjRVNNU2dtNDlHcU5nWGc9PSIsInZhbHVlIjoiM1lCem5NOUQyZkQ0dlM1QTlJclpwRTlBd09mWVRhSndFcFppeFlHbi9ma2ZlNHFXM1pzRkUvWGNUZTMvZWhTMlh1WWRXdFU0NyttdEsvVTg2VUpyOGd1ZllRRVZ3MzhQc0FJYVZhUUxRNmFnYzU5c1dVcjI4aTVGbTVtTVJGUGErelZYY0F6MGtZU2taTlRwOWIxeEdQQTRVeDlSemxqeUU1cmRlTWo4RUdMeERFV3dmUXdENHkrREVoWmovUXA3V0lyd1c4NDV1KzJweU9GdUtSMUZLblkxV1FycmU2T1cyeWdudlM4bkJ4d2Y3cHdPUG5rK1NWUHQ0M0lZRzlrb0hMYlNyTGVsTUNVWE9JMWpCcytRQXBOcWxGM0l1RC8yNENYVVIwMGlMTy9qQ3RTNENCcjZmaHBsZWZ6OU5ZdHo1V3RZdzNsdGJaSmhZVktQWGI2OWxodzIwVmFsRWhmM3BEV3lCSjVmTzh5QmNtZWU2L0xUNHRmVU8xM1F1a2VsZmFWMXFmYlZOWXdSWjlKTFU2OVp2Znl1NzYzZ2FEU1lEMUF0V1A3bjYrd1cyMnloMEcyZnNrQ0FiTTEyT2l6Vk9xdktvTXhJeS9KeCtDbUw4V1A2RWhjdDVRZHpNeE1iVGtoblZ3V2RmQndXWkxjOTlMUTJBaDZDUExEZWZhN3MiLCJtYWMiOiI2MjE2NTI0ODY4MWVjNjM5YWIxZDM4MWIwZTYwZjBjMmI3ZjIzYTk4Mzk1NTcxYWFmNDI3YjFhOTcwODVlY2YyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1193075905\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1149775055 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RtHbcRqD8V7q1fDzvV41Fzq9te9B2VUCpYlSqZiL</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">91LwqcXsNZmfmRkjBpCz6hEenAJOpgI6hGqx9k4u</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1149775055\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1719523513 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:38:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9aQS9hSXM0N0puNGduU2w1d3FreVE9PSIsInZhbHVlIjoiZzcrMEtsMVV2Q3Q0amgybFl5bmxnNng5QnJ3M01wSEM0dHMyNDhkSHd4bk1VenFTbFdTZ2hGcmpLUEEya2xQUnZqaVFhdndLV2VDWmdlTUNYTkdsZC9ranE4Y1dNVzh0RzA4S0w4WWRtZ3VGcFhSbVBFWUk5dGt0ZENaSmlrcnUvT0lGRGF1YlFOYXVjUVVnWGZNNk1XbTh6WGczalJmajd1bmF5aWtGSHRHQWUyZkRua1B0WEVUejFQSlduV3BycDJoaHZzU0treE5qWU54SlQvdmVrbUFPTU9Zanp3SGI4Qk5pRjErZy91Y2dSTzJjbmoxb0d5OGJYNmpDbUxNK1dyT1VoWGdHYTIwMU5ZNUZxeDF0ZU5BNGlYbDJjRUdJNVduUEUwbGlnSnREWEFsaFlSSDh6bHFubWJiQW94anB6VGc2THFMUU1UaVQ3WWtGVnFsWFVIaDZYQWFieHoyM3ZJT0RuUXJPM05EQ3gyMWhhM2M1MFQyRnEwSDkwdGRibjIzczE1VXczMktjNlVKR1N6KzV4N20yenBpNW5YZU84SGhJRDE0ZGFOdW9YWWxVOXgzSGtTTGQxUXUwUlFkTGhQL3A5UGdJaEZHTFZLWHpzT0wwNDczWTRMRk9LZ1ZuZGRKbnMzSFU2TjNISHNaWTYxblgwV2ZZZ3pMaFo0L3UiLCJtYWMiOiI2NDY5OTk4NzRjNjMxOWE0ODhlNzkzNzY2MDAyNTQ5ZjhjMzEwNjIxMzc2ZDlhNWViMzEyY2YzZDZjMDc5NzBkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:38:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjlTeC9zQmw1VVVSeWhOYUY4NzZrWVE9PSIsInZhbHVlIjoiK0Q3UFNPbHRIKzdWRTc1N0diVXdmWCtoemU3bXV5ZjhqWDVDRGZUalVndEhZZnkxNXdRaldTSnFMUVNmclZCZ2sxRld5WjI0MTdWTlZuZE9IazJoRWYvTFE4S294bVh2NHNtd200d0ZsQnN3NjZLUElXMlg1M0tFaXZaNk9INHVJaWpUWEpPaDloYVJkbys4V09kY2xmZDR1c2ZCQ2hUa0J1MzY4alM5OVhwZmR0Mms3QkdMSm1IL0dxNmxhb3k4ZmpEdGpGVitLQ004MTZqblB6ajY1KzNiQURxb2NIYU1FR1NUalRXQzFPWTIxK3ZWU0JhbHZjRkcraGdmMy9kTlAreURMVnNWTDBHS3VEc3pKR3h5TEkvcVBZckVQUGp2VXRwaWNJenJENmRVZnJ6bEhkYnZQUUd0UEF4bEd5cVdxYVMzeE1NN0w4U21vb3RLMUlOcFFZbldlb0VSQXFrUXBpdm15NnFCYzdON2tHc1dMNU9YTVVScm9HemlTbGI2bHNzNlVrMjZaZ0xZbis0VzNGbVVSTzhzbVJUQXg5UEVvLzRZNnMzVUF5UlpMWW1kSE9IaXIrWGRibjUrUkZiWmhieDdFTDBud2UybmxLMlJQZGc1b2hueU9NeEZ0OFpNUXRkWTFFdCs2NnBrWWdvbjRyRzRYNW9YdWxydThubUQiLCJtYWMiOiI2MzdkN2ZiZDM5NjJkNmQ4OWZjNDg3MTBiY2NhMWE1MjZkNjIwNWRkNjJkMTdmMmQwNGM5ZTViYmM5OWQ4YzRhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:38:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9aQS9hSXM0N0puNGduU2w1d3FreVE9PSIsInZhbHVlIjoiZzcrMEtsMVV2Q3Q0amgybFl5bmxnNng5QnJ3M01wSEM0dHMyNDhkSHd4bk1VenFTbFdTZ2hGcmpLUEEya2xQUnZqaVFhdndLV2VDWmdlTUNYTkdsZC9ranE4Y1dNVzh0RzA4S0w4WWRtZ3VGcFhSbVBFWUk5dGt0ZENaSmlrcnUvT0lGRGF1YlFOYXVjUVVnWGZNNk1XbTh6WGczalJmajd1bmF5aWtGSHRHQWUyZkRua1B0WEVUejFQSlduV3BycDJoaHZzU0treE5qWU54SlQvdmVrbUFPTU9Zanp3SGI4Qk5pRjErZy91Y2dSTzJjbmoxb0d5OGJYNmpDbUxNK1dyT1VoWGdHYTIwMU5ZNUZxeDF0ZU5BNGlYbDJjRUdJNVduUEUwbGlnSnREWEFsaFlSSDh6bHFubWJiQW94anB6VGc2THFMUU1UaVQ3WWtGVnFsWFVIaDZYQWFieHoyM3ZJT0RuUXJPM05EQ3gyMWhhM2M1MFQyRnEwSDkwdGRibjIzczE1VXczMktjNlVKR1N6KzV4N20yenBpNW5YZU84SGhJRDE0ZGFOdW9YWWxVOXgzSGtTTGQxUXUwUlFkTGhQL3A5UGdJaEZHTFZLWHpzT0wwNDczWTRMRk9LZ1ZuZGRKbnMzSFU2TjNISHNaWTYxblgwV2ZZZ3pMaFo0L3UiLCJtYWMiOiI2NDY5OTk4NzRjNjMxOWE0ODhlNzkzNzY2MDAyNTQ5ZjhjMzEwNjIxMzc2ZDlhNWViMzEyY2YzZDZjMDc5NzBkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:38:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjlTeC9zQmw1VVVSeWhOYUY4NzZrWVE9PSIsInZhbHVlIjoiK0Q3UFNPbHRIKzdWRTc1N0diVXdmWCtoemU3bXV5ZjhqWDVDRGZUalVndEhZZnkxNXdRaldTSnFMUVNmclZCZ2sxRld5WjI0MTdWTlZuZE9IazJoRWYvTFE4S294bVh2NHNtd200d0ZsQnN3NjZLUElXMlg1M0tFaXZaNk9INHVJaWpUWEpPaDloYVJkbys4V09kY2xmZDR1c2ZCQ2hUa0J1MzY4alM5OVhwZmR0Mms3QkdMSm1IL0dxNmxhb3k4ZmpEdGpGVitLQ004MTZqblB6ajY1KzNiQURxb2NIYU1FR1NUalRXQzFPWTIxK3ZWU0JhbHZjRkcraGdmMy9kTlAreURMVnNWTDBHS3VEc3pKR3h5TEkvcVBZckVQUGp2VXRwaWNJenJENmRVZnJ6bEhkYnZQUUd0UEF4bEd5cVdxYVMzeE1NN0w4U21vb3RLMUlOcFFZbldlb0VSQXFrUXBpdm15NnFCYzdON2tHc1dMNU9YTVVScm9HemlTbGI2bHNzNlVrMjZaZ0xZbis0VzNGbVVSTzhzbVJUQXg5UEVvLzRZNnMzVUF5UlpMWW1kSE9IaXIrWGRibjUrUkZiWmhieDdFTDBud2UybmxLMlJQZGc1b2hueU9NeEZ0OFpNUXRkWTFFdCs2NnBrWWdvbjRyRzRYNW9YdWxydThubUQiLCJtYWMiOiI2MzdkN2ZiZDM5NjJkNmQ4OWZjNDg3MTBiY2NhMWE1MjZkNjIwNWRkNjJkMTdmMmQwNGM5ZTViYmM5OWQ4YzRhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:38:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1719523513\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-62057518 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RtHbcRqD8V7q1fDzvV41Fzq9te9B2VUCpYlSqZiL</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-62057518\", {\"maxDepth\":0})</script>\n"}}