{"__meta": {"id": "Xcc94bc7b4a34f20fe2b091e5e84c5e83", "datetime": "2025-06-30 23:13:30", "utime": **********.021836, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.613169, "end": **********.021852, "duration": 0.*****************, "duration_str": "409ms", "measures": [{"label": "Booting", "start": **********.613169, "relative_start": 0, "end": **********.967938, "relative_end": **********.967938, "duration": 0.****************, "duration_str": "355ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.967947, "relative_start": 0.****************, "end": **********.021854, "relative_end": 1.9073486328125e-06, "duration": 0.053906917572021484, "duration_str": "53.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0030600000000000002, "accumulated_duration_str": "3.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.996352, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 57.843}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.006801, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 57.843, "width_percent": 18.301}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.014244, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 76.144, "width_percent": 23.856}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C**********071%7C27%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IncvZUpONWZCQlUydzNiby9Ma2pZS0E9PSIsInZhbHVlIjoiNE5CbW5sRTVhaklqeTR4Tm9iWmgraHVlaHVBR2xZTi9TZHgrQ0dDR21nRTIzS0YyK1VMa2svU3dmbE5nRGxDNHdmYzMwVGpxUkczUXRXMU9iRjZJMEZVQzhackR0bTF3Qi9iUEJkZ1VHNkJvWS9TZFQyUjlxSlVjOTc5VHJ2V1FDVU1xbjM3ZFVJOGhLdm1DQXR4cHQ3QTd6VUtJbDFMWVVmOGlXSUZER2FwNTNDU3REblVGN1hCUDRTbERxTUoycFBqaE8wbnE3L1puaW5tWkdJUWJMbzUrVlN0bTRUTWlhWVJpVEI4MEQ3dStydCtkb1RqelFzaTAvd1JXUzcybVl5SVZxam45SmtnQlFGMW4rOEE0N1NKQUlRa0E3SVJSTmd6QWR0NzRDNnhoNUtXWGt4VnFsOFR0d3V3R3BwelhMdWFzdHY5b0twQVc2ejY4SFRkOUp6YzIxQ3RuR3kyRld3Q0EwL0JGWThsVDVjaXhWS3JZRzY1WmJzNFBYazIwRzF6UDR5Umcrc3J3VG9lN2RLSEtIYnBVeWNOOTd5NnRzOEhVYUZOV0ttYkJuMGJzYW50QXFTdnQrNTIrbDBrai90VlFrVU16YXNlRFdIS25Ca0VoeG85V0FVeVZobXB3eGdyMHFSREk3RFdzdllLWnVrVmVWZGJKd01nVnFudXQiLCJtYWMiOiI4ZDEyMThkNGMwM2ZkYjI4OGIzZDgxMGEzYmU3MjIxOTgzNmZkMGEwMDFhM2YzODZhZDQxM2VkY2M3NTM0NWQxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ikc2b25TNzNkLzl3cU9HNE9kRzZxL2c9PSIsInZhbHVlIjoiS1BUSkZrM2s0UTFNWDAvY2Q3SVRpSnduaGE1SzE0ZTRFVkR3aHhzL0FNUUVoVmxKR2xRSEwvMXNGeHZkaU1ZYkJ1RlpVUGVwNDgyL2E5YVNZMmVDV2JLaWgyNUNoLzZPYkVHem1pdHc0TElJNkZBTWs0TVdUQjV0Q2hoUk9NdU1BWTVZQVhlYmpBOWI4a1RRWUlLWWE3K09TZkNWYjd3QW9BM2c3RSttOWhQbzRJQm1jSWliL24yWUJrZWF0L1V6VmpoZDFaNzltYkhIQlg0WjJ2T2MrVHkwYXpGNG5iUGdBM0sxSU1kZjJjZmk0M042TlFBeEhrL25ZZlVpVGQzbjlOUjV5TXU3WXBSV0pjRGQ2NkJ4WkVBeWtaS2l2aERITFpZOFFPci9FeXFaZStYQXdwd3lSRUFHYUFNTlFuZ1ZodGMvVFdFRUphTE9vSzA2YzI5L1lzWFhjZk1mSy9lSDUxSnc1bi9HV3oySm9aWUdLWlZ0dUZnWjkrNk9KUlJWWWFhUW1zUHBVSWFGdTJNK1VGYVhUVVVad3FPL09lQUI2bXl6MWdWV2ZrYnBNQXk1VW1nVDdCTm1rYlpaQTNqRXRUM1pSR3FIUEZuZWEra20xZGV2eWI2ajA5WGlRcUxMT1pZMTY5VElsWVZTK1JQMldSeG53YnY2N2RCMVpDWmQiLCJtYWMiOiJiYTQ1NThhNGQzNzhmY2M3MWEzZDQ2YTY1MDJhZTcwMzY2MjE5NzU3YjI4YzgwYzM2YzFlZjlmYmMxNWVhZTY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0aUeGT9vcZQoKvixhqnXLFDjX39UXyfabqLnLub</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:13:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktwdU9jY0R0c1hteGZHTTdyelNOWlE9PSIsInZhbHVlIjoiNGxMSEYwQ3BqM2cvOW9CR2YwRU1yWnZEdmdHc3g4VnZVZG8wcUMvd0lPYit0WVQ3RzFuTGJ0ZEtiSXpqdnRJS2hYM0NTTDhIbHNVRkVxdW10M3Y4LzBWcnlSVWVrUTN2UWNES01nbklHaDhOVVhibStCNm9nenZYVnRUNFBtZjZTYVN1Tkh0b0o5a3d1YWR5clFjMEJJV254S2pnWVRZMjF2K0UwSEQrWHpCNThIMlFGYmJtR0ZxVFlYRGRBcGxQd09MN2V3QXN5aWZBTDl6Um8yT2dKd3ZiVEpFMndSZXBUTXplT2d5UTNCUUIrZG15TTRoeFQxSHBUdExSZ2dlZG9rMXhwa3ZLY2w1ZmxKZUkyZGNpSVBhVVdKS1lOY3VmMEp5dkQydUY2d0lVTnp1SVI5YUdvRUxhUEJDaUhOUXFmU1haMW5xUnZFNXN5Y0xUbHBuOGxUNnpVbk5aaGQ3MXhuWTZHWmlkeE9EdnZEVmYrL2lVcmszbjJaSG1qazBMRDUzcjhPa2dOUWVuLzlVQnFFdkhBcXkrQXJGTHpuNDRxTEJrcW9jaGVHdHJLU2ZubE9zODJYYkpIWnphSlBWNEI1NUZrZ09SRkY2bk5iYllKak1CTngxNktyUE9hSVIwT3VZbnBWMUdyYm1CcnVHYzdIYm1objEycWlxTTdqdkQiLCJtYWMiOiJiNTkwYjc3NTllODYxOTk5ODYwYjg4MmYzNTc1OWE2ZjZhZDYyMTU5YTczZTYwNGMxYmI3ZmQwMzk5MGIxZGZmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImZ5akhlYlNZK1N3bHZiMGdBTVBoVFE9PSIsInZhbHVlIjoiMjQ2ODdPa1lONVBWQ0dyclV2ODlORGZyWFRhWS9tM3h2MjN0YnBSOU1EbnRoM0tDbUk3UkQzMUtkalFKVGlWU1ZURk5mWmVxaDd5eWZOM0ZZZ1R5S1pjVGNaWlJ0T25XR3hIRS9XS0V3R3FtSzZaYWg4YWs5U1VkcmFRMFZSTlZOdnk2M3hSYmxESi8xMGlabzZoLzRtTHNTaGZaTzFiOUNOU284WGZDVGJYNU9jM015UHFFUi9kY1p5VjZMWjVzOHREU1pIM3pUWVdFSE1DNWYvaGxSSW9KUWFpNzZhZEpKRmhLK3ZleTVsUDhNamFod1hteUhreXIvV3hJaWRDYUttbUpRNFJWaW5NWGs3U1NwOWNFU29LSE9JdllRU1pid0FTNXVjYTIwZmJSL1ZhbWp3Y0k3WFZDK1dCcHc5bHZXaXN1SmJma1VCT3VoUWo5bnZPK01vOVdQYVZpYTJSR29iZGh0U1p6dEp0QXo3bjdnbWJDU1pKekpDMHBGZm80VkpYT2s4VFBDYjdGekFZZjVwc0U0UVczT2xpZUZyWnFXN2habWxNR3VFNHpKMHFrbWQ4VUZyc291TVM1MC9BWUVQbHNkaFFYdHllTWZBR2RWbnZUTnc4Z2dTb1VDSzRURXJlU0RFM1VWbGNWdUpTUjRWeHJXN0hYVmFoZy9KOUUiLCJtYWMiOiI0NjhkYzExM2M2NzhjYTI5ODNiMDQyNjIzMWRiZmIyYjg5NTkzNGQ2MzVmN2E3NDhmNTcxOGEwYWU2NmQxMmIyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktwdU9jY0R0c1hteGZHTTdyelNOWlE9PSIsInZhbHVlIjoiNGxMSEYwQ3BqM2cvOW9CR2YwRU1yWnZEdmdHc3g4VnZVZG8wcUMvd0lPYit0WVQ3RzFuTGJ0ZEtiSXpqdnRJS2hYM0NTTDhIbHNVRkVxdW10M3Y4LzBWcnlSVWVrUTN2UWNES01nbklHaDhOVVhibStCNm9nenZYVnRUNFBtZjZTYVN1Tkh0b0o5a3d1YWR5clFjMEJJV254S2pnWVRZMjF2K0UwSEQrWHpCNThIMlFGYmJtR0ZxVFlYRGRBcGxQd09MN2V3QXN5aWZBTDl6Um8yT2dKd3ZiVEpFMndSZXBUTXplT2d5UTNCUUIrZG15TTRoeFQxSHBUdExSZ2dlZG9rMXhwa3ZLY2w1ZmxKZUkyZGNpSVBhVVdKS1lOY3VmMEp5dkQydUY2d0lVTnp1SVI5YUdvRUxhUEJDaUhOUXFmU1haMW5xUnZFNXN5Y0xUbHBuOGxUNnpVbk5aaGQ3MXhuWTZHWmlkeE9EdnZEVmYrL2lVcmszbjJaSG1qazBMRDUzcjhPa2dOUWVuLzlVQnFFdkhBcXkrQXJGTHpuNDRxTEJrcW9jaGVHdHJLU2ZubE9zODJYYkpIWnphSlBWNEI1NUZrZ09SRkY2bk5iYllKak1CTngxNktyUE9hSVIwT3VZbnBWMUdyYm1CcnVHYzdIYm1objEycWlxTTdqdkQiLCJtYWMiOiJiNTkwYjc3NTllODYxOTk5ODYwYjg4MmYzNTc1OWE2ZjZhZDYyMTU5YTczZTYwNGMxYmI3ZmQwMzk5MGIxZGZmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImZ5akhlYlNZK1N3bHZiMGdBTVBoVFE9PSIsInZhbHVlIjoiMjQ2ODdPa1lONVBWQ0dyclV2ODlORGZyWFRhWS9tM3h2MjN0YnBSOU1EbnRoM0tDbUk3UkQzMUtkalFKVGlWU1ZURk5mWmVxaDd5eWZOM0ZZZ1R5S1pjVGNaWlJ0T25XR3hIRS9XS0V3R3FtSzZaYWg4YWs5U1VkcmFRMFZSTlZOdnk2M3hSYmxESi8xMGlabzZoLzRtTHNTaGZaTzFiOUNOU284WGZDVGJYNU9jM015UHFFUi9kY1p5VjZMWjVzOHREU1pIM3pUWVdFSE1DNWYvaGxSSW9KUWFpNzZhZEpKRmhLK3ZleTVsUDhNamFod1hteUhreXIvV3hJaWRDYUttbUpRNFJWaW5NWGs3U1NwOWNFU29LSE9JdllRU1pid0FTNXVjYTIwZmJSL1ZhbWp3Y0k3WFZDK1dCcHc5bHZXaXN1SmJma1VCT3VoUWo5bnZPK01vOVdQYVZpYTJSR29iZGh0U1p6dEp0QXo3bjdnbWJDU1pKekpDMHBGZm80VkpYT2s4VFBDYjdGekFZZjVwc0U0UVczT2xpZUZyWnFXN2habWxNR3VFNHpKMHFrbWQ4VUZyc291TVM1MC9BWUVQbHNkaFFYdHllTWZBR2RWbnZUTnc4Z2dTb1VDSzRURXJlU0RFM1VWbGNWdUpTUjRWeHJXN0hYVmFoZy9KOUUiLCJtYWMiOiI0NjhkYzExM2M2NzhjYTI5ODNiMDQyNjIzMWRiZmIyYjg5NTkzNGQ2MzVmN2E3NDhmNTcxOGEwYWU2NmQxMmIyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1704234582 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1704234582\", {\"maxDepth\":0})</script>\n"}}