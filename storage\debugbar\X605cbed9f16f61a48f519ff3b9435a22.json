{"__meta": {"id": "X605cbed9f16f61a48f519ff3b9435a22", "datetime": "2025-06-30 22:39:29", "utime": **********.592822, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.125871, "end": **********.592837, "duration": 0.4669661521911621, "duration_str": "467ms", "measures": [{"label": "Booting", "start": **********.125871, "relative_start": 0, "end": **********.531351, "relative_end": **********.531351, "duration": 0.40548014640808105, "duration_str": "405ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.531361, "relative_start": 0.4054901599884033, "end": **********.592839, "relative_end": 1.9073486328125e-06, "duration": 0.0614778995513916, "duration_str": "61.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43576576, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.02698, "accumulated_duration_str": "26.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "guest", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\RedirectIfAuthenticated.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5589092, "duration": 0.02698, "duration_str": "26.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-165267056 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-165267056\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2125088890 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2125088890\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-735876744 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751323019449%7C9%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik12VTFuUGc0ck1OYUtKOWZvVDY5Umc9PSIsInZhbHVlIjoiUkFnZDFOMXBmb1RXenlhdjRWaDVoNTVOclk3OEY5dkE5emJGenNFblcxT09vSWNGS2tyQUxyTWgyU3Fqay9LOVNMUTZPdU5KZm4remc1cEFWL2ZMOE0rLzkwbVVvemk5YUtydXRLUkdMQU05KzY2MGx1K01OYmRxK1c2VU80K3hPZWxBMC91UWRhM1pvYnRsUWF0MXcxUWdVOFYxaTVjUUtYOCtSbkN6dTJFU013T3k2UTdXTkoyOUJVWUhOYUViRVVyUzNNbHhMdWVKeUpTMmJPNjFRWW5CZEpMR21CMkN4VHUzSnlhSnZkZzRaV0c2MGlOS2dtc1VGV1QwQXlEQ1ZYVUNaRDF1Q29HUXpHdzlyK0JVUTV6eW5aWjl1RmsyQXVKMWZEaUV0S1cvdG5EQWQwTnZIY0IwUDFFTU1KZTRqK0R3ODBCZ2h3cCt1cTdnVlZYd3RrSHRSd0dVUGFLU1JNWnNtNzdhdmNCeW1pNHZXa3VVRElZM09PZjFUNnVwMkx4M2lNai9pVG9XZFdoYkJrY1VpNm9sT1FiUURTNWZOekJGYzFwMStuaC81MTgrM1Qxb1l4NmQwSDZhZXhxSWU0TG5ObjhjczBkdDI3TFAvaHUxR0JlRjVCU0VJeTVUTVVZKzBmc2xnd2l3akFBR21SakVJU0ticlFBblovTTYiLCJtYWMiOiIxZTk0Nzc4ODIwMWY2ODU0ODQ5NWJiY2IyNWYzMmU5ZTI0YzQ3ZmM5ZTE1ZDExNzBkZTFiN2Y3ZmFiODllYTA0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InBDaXhRVjErUnVrY3FiK1B0K3ZjakE9PSIsInZhbHVlIjoielE2ZWZKZkFvYUtOVitZSnB3dmRCREFodmg1RmY3R1dXUjdPTkhZRWIzUHpzRjBJcjhKTG9VRE5LQUZEeTRwMDRsbkVqRTJEa0JJbzc0ZDlTY3RZUTNhdi9EbUcxenhpUzdHdmxIWWRiaFhXNTFrZGkzMDFEeElIVEcrUVp0MHFoNStpOUZOSTBQZU82V3BIZkdaUnVNVktDNUV3VHNVME02dzkwRk8xeUlSZit4RDFYVG5LNVJGTWFPdWtxOFowa1IvTE1Va3Z3MVA3OUR2aXRqMVVwRHBqOXRKYjJYbzQ0WjdoamI1Z1lTVE51NkVPWmJydzdVYVh5ekw4VXVyUkJTQTNEcy95cWdEK2hYd1JrOXpIWmt5NzZxdjFZYXZiWjB6alplcVIycVdNY0d0aGRHQklldGE5RVhxR0NOdVRWckFSRzJFdnl6bW1lM1Ewc0VNQitVZkJyRFptZ3ROVHBWek01RE1hcVptcUhqekJiajFqR2lrWm0za1c5WVV0L1VSdmU0UkZLTFhlNEZ4eVE0cjJKUVNtNndtaDVJTmM0ZEtBejM4dHFLWTFxR1dtTklxNkUrTkxqZkMwL3pnaWxKUGU3aDBuYnhGRStMWmh5aGVYNFd4RnRrM0V4akhXa0lod1dkTTZkVC9zM2MxVnArMHExVU56QVRBbXNmQUwiLCJtYWMiOiI2MWFkYmU5NWVmZWJiMDljZGNkMjI1ODk1ZGJhNzJlYzhjZTllY2FjMzhhNzU0ODFmYzFhYTVhZTQwMmMzNmFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-735876744\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1993189232 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">K7EIYCLnalanTBUASCKMEOK1yUmooVr7ha2cCSMP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1993189232\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1569616159 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpuSVc4dVI4aTZEeTA5RERaUGlSRXc9PSIsInZhbHVlIjoiUE42RjBnTHIrVTJYWURYTWF0a0RPbVRqUXFJWStrSDliNmVHZjNUQUYxeEtpOGw4b2liLzhacGtJaDc3ZFlhbXRuMHRuenJISFB0S2crN3lYM2g2UjdZYVZYak9UenloaFY2VG1LelBVOFFBQ2NQcVlvNGtpV2FCODlrc2RYc1B1a2VlQXZVdnZFdGQvcUpxQlAyVHltVE9QcDI2M2VObXZ0ODJIaWIwV0hJNWtoVEZrQURoSUwwR29WU25LbWtxbUp3MWhLQzlBSk9ETmFPb0p3VzFJNDA0TWhvR1l1YWxuNzV1NVcyR2U3QzVITTRXQ2NIeUtHdnhhVGY2UDU2aWlMZEgxamFUc0N6M1BJbm1IRE5sTThKZHVncHNMZUZ1bXBzbldTdUVkMHFmODZ1YWxqSTlkTHFOZmZ5cjh3b0JFaW9WY1R6UDNMYVVCYmxnbkVicklKK3BMMlZtM3FhSkVXNXNqWUVLZis2OThpYmhUSS9aLzF3S091SmZid1BzRFZXdFJ6TDdLZndMdVorZjhjVzEySHJnUTlrQi9ISm1Ob2h4aWRuckZpR0krKzJ6YTAyNkllRzF0blJ6ZnI1ajc5YUhkbVRwM3VYdzkrT3V5OWRoQ3doUWxQLzNsZmhJa2hIZVVDbFZDbndKci93eDZ0SUo3MjJrZWRQcDVSYkEiLCJtYWMiOiI0NWU5Y2IzYzdlM2Q4NTFjM2Q2ZmNjZmRmZmMwN2Q0ZDMzZTQ0OGE1ODUyMWMyZTk4MzBhYWYzMGY1ZmEzMGJmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkVnMXZnKzlvUnkzSUJmcVVCMk53Q1E9PSIsInZhbHVlIjoidWxreC9IYkNTbnd6bFNrY0pnQ1NWT2NxMXZYTjl2MEt1MEh3cTMybFpRVUxZRHlwTGVtUkt5dmlpUzMzakNvcGQ3Zll1dXh1YWY3RkM0ZXlOc0Fub0FLOTlDYks4bkpkdTBJUVk2VlBFcWlHc3FsSXM5VmpzN1ZtTWt1OUVMUGcrTmhWK3pRZnlFbmZWSHZOTmVqNG05VEFnMW50MnNIaWVFOTQzT2kydmo1dFRSSU5pNnp4cmxHVGlVeEZhMHUwK2hKeWlKSmxrV1l1d3BJbWlJOG1sSEVUNVNqMGV1SEFyQS92TWljb3pkZlp5Ris5b3YyOXdFb1MySHA4aWE3UXR3elJzV1hNc1dHeGFNVTNjaVhEazArTkxJT0oxTzZ2MmZzZjI3SndzQmFmTUJUUXdKNXNjamVxRWw4SUNrSnBEMmY3eGdSOC80dDVicjRZMG0xTCtrdEVHd2xBUnA1eTRET0NIRzdLeTcrNU83NmZ6VFRSTkR1ZFE2U3IyOXk5UjBvV2VXcndna29nUnlsNkROL3VYRWVWQkIxNnhTQ3NzS2xtcklHclZUajVKVjdXYTJtbmlSTC92SThqZmJkMmZraDYwRUN3UUpRRE4xSWdLVTVEVXlJcWF4RWgwMW1xeHR3MlJhSDh4ZGFHVGljNjRzdVlLME1FY3lwQXp1SysiLCJtYWMiOiI5YWU3YWQzODE1OGU4MTQ2ZWYwZjMyZjE5MTgwYTc5ZDViZTNmOTViYzJlNzQxZjBiMjIwYjY5MzU0YjdiYzgxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpuSVc4dVI4aTZEeTA5RERaUGlSRXc9PSIsInZhbHVlIjoiUE42RjBnTHIrVTJYWURYTWF0a0RPbVRqUXFJWStrSDliNmVHZjNUQUYxeEtpOGw4b2liLzhacGtJaDc3ZFlhbXRuMHRuenJISFB0S2crN3lYM2g2UjdZYVZYak9UenloaFY2VG1LelBVOFFBQ2NQcVlvNGtpV2FCODlrc2RYc1B1a2VlQXZVdnZFdGQvcUpxQlAyVHltVE9QcDI2M2VObXZ0ODJIaWIwV0hJNWtoVEZrQURoSUwwR29WU25LbWtxbUp3MWhLQzlBSk9ETmFPb0p3VzFJNDA0TWhvR1l1YWxuNzV1NVcyR2U3QzVITTRXQ2NIeUtHdnhhVGY2UDU2aWlMZEgxamFUc0N6M1BJbm1IRE5sTThKZHVncHNMZUZ1bXBzbldTdUVkMHFmODZ1YWxqSTlkTHFOZmZ5cjh3b0JFaW9WY1R6UDNMYVVCYmxnbkVicklKK3BMMlZtM3FhSkVXNXNqWUVLZis2OThpYmhUSS9aLzF3S091SmZid1BzRFZXdFJ6TDdLZndMdVorZjhjVzEySHJnUTlrQi9ISm1Ob2h4aWRuckZpR0krKzJ6YTAyNkllRzF0blJ6ZnI1ajc5YUhkbVRwM3VYdzkrT3V5OWRoQ3doUWxQLzNsZmhJa2hIZVVDbFZDbndKci93eDZ0SUo3MjJrZWRQcDVSYkEiLCJtYWMiOiI0NWU5Y2IzYzdlM2Q4NTFjM2Q2ZmNjZmRmZmMwN2Q0ZDMzZTQ0OGE1ODUyMWMyZTk4MzBhYWYzMGY1ZmEzMGJmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkVnMXZnKzlvUnkzSUJmcVVCMk53Q1E9PSIsInZhbHVlIjoidWxreC9IYkNTbnd6bFNrY0pnQ1NWT2NxMXZYTjl2MEt1MEh3cTMybFpRVUxZRHlwTGVtUkt5dmlpUzMzakNvcGQ3Zll1dXh1YWY3RkM0ZXlOc0Fub0FLOTlDYks4bkpkdTBJUVk2VlBFcWlHc3FsSXM5VmpzN1ZtTWt1OUVMUGcrTmhWK3pRZnlFbmZWSHZOTmVqNG05VEFnMW50MnNIaWVFOTQzT2kydmo1dFRSSU5pNnp4cmxHVGlVeEZhMHUwK2hKeWlKSmxrV1l1d3BJbWlJOG1sSEVUNVNqMGV1SEFyQS92TWljb3pkZlp5Ris5b3YyOXdFb1MySHA4aWE3UXR3elJzV1hNc1dHeGFNVTNjaVhEazArTkxJT0oxTzZ2MmZzZjI3SndzQmFmTUJUUXdKNXNjamVxRWw4SUNrSnBEMmY3eGdSOC80dDVicjRZMG0xTCtrdEVHd2xBUnA1eTRET0NIRzdLeTcrNU83NmZ6VFRSTkR1ZFE2U3IyOXk5UjBvV2VXcndna29nUnlsNkROL3VYRWVWQkIxNnhTQ3NzS2xtcklHclZUajVKVjdXYTJtbmlSTC92SThqZmJkMmZraDYwRUN3UUpRRE4xSWdLVTVEVXlJcWF4RWgwMW1xeHR3MlJhSDh4ZGFHVGljNjRzdVlLME1FY3lwQXp1SysiLCJtYWMiOiI5YWU3YWQzODE1OGU4MTQ2ZWYwZjMyZjE5MTgwYTc5ZDViZTNmOTViYzJlNzQxZjBiMjIwYjY5MzU0YjdiYzgxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1569616159\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-755646002 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755646002\", {\"maxDepth\":0})</script>\n"}}