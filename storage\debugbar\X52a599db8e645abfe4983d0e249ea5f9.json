{"__meta": {"id": "X52a599db8e645abfe4983d0e249ea5f9", "datetime": "2025-06-30 23:13:00", "utime": **********.307365, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751325179.847831, "end": **********.307381, "duration": 0.4595499038696289, "duration_str": "460ms", "measures": [{"label": "Booting", "start": 1751325179.847831, "relative_start": 0, "end": **********.232099, "relative_end": **********.232099, "duration": 0.38426804542541504, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.232107, "relative_start": 0.3842759132385254, "end": **********.307383, "relative_end": 2.1457672119140625e-06, "duration": 0.07527613639831543, "duration_str": "75.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45711152, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02176, "accumulated_duration_str": "21.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.262358, "duration": 0.0205, "duration_str": "20.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.21}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2920408, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.21, "width_percent": 2.757}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2980351, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.967, "width_percent": 3.033}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1343494943 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2818 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751325027058%7C14%7C1%7Co.clarity.ms%2Fcollect; laravel_session=eyJpdiI6ImpxTklOWXhTYU1XWEJkQTBqVTRXZlE9PSIsInZhbHVlIjoiTXFGZEpQRXlGa212dDlvcjMyZUgzOTJTdTZUUld1Y0RIbVM4elBEck42T2RTa0tjV2NoaTJNekRxM3Vkc3lrR1dOeGt5blA0Ym55U2pNZWY5am1QNC9lMm5SZE1VcGh6OVBneXMxN2Y4Qmx1TWhKckg0OXNOdzJBV1ZmTzVubEw3cmlyUDRwVnhxYzd4NmhqaEJiZThRcHlESndxYTY0WWNKZ1pqeVdFSTZuZnRmZ1BDZy91c3ZCbWloYytBQnpaczBrTmh2S3Z1dllBcHJYMkhWOXM3alFFWENqVkhpc2Rnb1lHSlpwM2dNb0s5cUxpakE5OVNEbEZRZ0VUVDJBWFZKWmZyYjZRM2ZNL1BuR0l5L3BsYXhVbzEwQ1ZlaXYySVUrQnk5a0dvY1BKK2dwQnEzM3ppZnFlaW9XTW9Ga1pSQ29mZktuT3BjSnc5ampzYU1SdTIxNzd4Wms4eUJ6eWdrajg1UUhYS2puSkhMNDVRbEh3WENJazQ2Qzk0Vlk5cE5NYW1qQ0xCbWJBVHZ5ZUlzUzBGRTJCWGliL1ovUHlOejhGSHJzRS9UOC8wK0QvTTQ4WHdveVBkTE1CdHlWcnFrQWdDcU1jR0hscFd0SUgzYWRtbFRaT0pzTGUzSkVDS2k4YlhiU0s3b29aMU5tbWtnL1NUaWZ1VmhxYnZ3ZFoiLCJtYWMiOiJjNTgzZDkxNDlmYmY2OTAwMmNmYTUwNTg0OWMwNDQ3MTVmZWQ0YWFiNjk4ZTJkZDM4YTkzNjhiMzE3YzIyNjlmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlpCSklubjBqc0N4cFFNNnIrZXZYWEE9PSIsInZhbHVlIjoiYUVDYXJXMHFpUnlVc0JhRWxuQUdGMFh5WkdZeHBUZG1PM09hRnFYaDNOV293SnFEaDZrRzR5ZWdGRkhKYURwTkVLck5YbE4xWkxIN2hkdkdvMnNyQ2xQVnVhT0RBZjNpRlovdGdJN2QwSzJ2TWxNVmdIYWVLbFQyZFM4UmVGZU5jdy9VY1V6c3VKY2lZWjQyM0d0LzBHNFFCNSs3WVl3L1hUMFp1K1Z3UHlPVEFSTmpkS2RQMmV2aUIwejBRNzM5Und1NVZ1QWcyakswN2V5aGdTbzFJTEhyTG1WZndQUDJieTRNc3JhQnlTOXBvVTVGcFZ2eHdlUFlkcUt6TVdxa1hvNVYycHBaWGpBY2UxeTA4TTBsSHZIemZVVzhoOVNDS3FhbEFCSVU0M0hWeXJZZlFBYXJKYmxMUTZDTFlXekhpbnNndGh6MzdTZVFwU2d0OFd4R2FnVXFMRUFUVXc5ZUgwQjMzMXBGaDVwZW8wTlNveWQ4bTB0Wm5MU1RuNExTdWphUTd4RHQxbmVFdzNieGZUOVBXbFJPT21HbnZDSW1MdFFWekpoNDJSckZ5aW5pUUdtVTZjYzI5SDZyeW82eWRQamg2NlpYaCtnZFFmWU01ZkpWWGZ0Y2RkQkt3VXUyWk9sNGxjQi9OVGpkV1ZHK01oYlNLS3pKWkVnam5URVAiLCJtYWMiOiI2OTM2OWQzZGVhNTVhYWVmMGI2MmVmNjkzYjg3N2FhN2I5OGFjYmEyYzFhNWExZmVjMTY3ZDdlNWNlOGIwZmZmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlpSNlp3N0t3SXpROVAzWWh3R0VKQ2c9PSIsInZhbHVlIjoiZEVmTy9HTlVLYStZNlFjc25LRkhIV0Rtd2pKa3c2TjhrMHp2M3NDdi8wVEg3WWg0d3grTnhqK0xsdm1ieVBmWXE1cnBUVHEyQjlaTzhCK25qLzRrSXRzcTRjM1hZZ0NIS0p2L2c1MTBUVmw0TTc0QWxGay9XWm5COHB6Sml3Nng3TTR1VEtMWUVrK0NXRVdXeDkzYUtqNmJ5dWZPbThTc3NrZU9ENXB6cnlVY3BNei9HZmtFMXBpTUVWaU5xVFV0aHhxZEQ2VTJZMkgyUlNjUmZ0MmkvcTZIMUhNcWJtUmR6SXl1YXZZVEdxK3hkOGxqM1lrdFRMaWNtUjFPcE5rMkl3U29ZSVhubVF1NXVMWjFuQXNyamh2REpNeHNvekszS1czY0lhS20xUWhWQmFnVE8wMTMvYzBwMFo4dXZKcCtXeE4xVnJnVnpqcmE0eS9RR1VzNmJtSEVmOG4raWpGRk5zL1V6MXcyenFUMmVkYzZ5dEx6WmVZeCtaVXNvYlF4d1pUczZ3RURTOTJhcmFUWUh5ZVVmWWdZRWZNeE9wZ1ZnMEYvTEVEdHdjaFdSS3gyNjAwcTZHZ082U1drRjlUTGsrb1ljNG43WmZLUCtpckJ5RmMvdEJJSzdPQ0Iwc29Uc2Iwa3ozVlhhdUJEdm5LTEVaZ3FlR0JKUXpvbFhZekgiLCJtYWMiOiJkZDMwZGY2ODRmNjIxY2U2Y2NmMjZhNzdmMjhhNDMyOGQyOTIxN2U1ZTJjNjIyZWNmNTIxNzNhZGU4ZThkOGI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1343494943\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1470466154 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nMXQGGGSGQFIfGwlrnEYCl0LIB0DtCvcZqQRlbGj</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1470466154\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:13:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik11VmZINFJoa2lhLytFejlVY3R3Qnc9PSIsInZhbHVlIjoiMm9jbWZ5aHVHemZKellFNkxqU1NHdHcwT1VCK1F4eTFqL3BsUjhCYm13WmxKM2VBMGxNcWlrQUgraDYxWlJkTllBTXpnekRWSXd3QU5JTWIxZ3JtdFdtNjdJNVdQK1JWV0ZCVGFuSVd3M01FVUNzanY0cm9MZER0clUvLzl4cTBlV1M3SzhmTmlCdjNqRjZoU294MGpCTVdiV20rR1JLL0hHWjRNQVhvRC9lOGdQaFRMeGRkVCtPQ0FlemNMSWdBNUdEQTEzRXZVTUNYYmhUREE1d3dqZUVyUnhyTWJMQkpVWUY3ZVpDNFVuRjdtZU92YmVxZTBMazIxZWx0N1VLUjMwZ2JpUVNBR0VZTWJodWNiRVRSOGZWWm1iMXQ3OUVGY2VZbGdUZ29mREp5amRTcVV2Z25LZGNvdHJtbzFFVXVPV0t0N3EwQTZ4OVdTeld6Rjl0WlNRTC9rRTdDc052NFhUNHNhbE5vK05mS04vejZaOWNqOGo5WndGdGc1dldscGltemRINTFVb0cvYTFqRjlyL2pPVUt6d214MStwK3VWcjkrTmlKbnN2aEpsdys5dVpTcktxVzdSenNMemlFelRKU3RXK0toNWp3TlRDTXNONzY4b3ZLTGdpaGg1eGF5a1hUdHc4cU1iMHJLcEQ2MlJQaFZzQUNST2RvK1ZCcjAiLCJtYWMiOiIwODU0NDY1YjZjNjRkMTM2MzAwOTljODJmZjRiMzIwZTgxYjY4ODkyYWE4OWNiYmRhYmE1MDM4MGMyMTU0NTliIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ing4RlZUcFZMa0pxLzdDWm1vTEpUMUE9PSIsInZhbHVlIjoiYUVWb0NJY0JEeHMzaXBhMUpIaTZ5RTRYLzFHSnZmZVJoV3ZSWkd0SXZnVnZyU1FLRzB4SUloWGxBNjlwOU5aRTN0dXVsSWxZUk0xckpoZklxZmFrelJwbHV0TjNwZVpaSjRJOE9pK1FOdDRMaU1oQldLZnV5S2MwdTV0ZDRKRlkxelh0b3Vxak5LaTVNTEs1U3ZBcGRrNUlPanE2bVI5eEFTTTFGVWhsUytVMmRKNXRmeEJiNTFnOGMvTzY4dnVyTi94aDRwZ1FHSUxGdjJ1SEt1dmNHVHVqSkhyUitqV0Q3S3R1d3RYOHVuYjRvWGlJRm9neHBnekFhdHdMUmM0a25NZm1aNHpZZUZmNy9OT2JWbnBWMnM3NFViVXYreDY5akRmZTdOeEVNZnZNNWs0aEtIbkRPQmpENEw2UlF6L0JOQU1zcE11NFFEcmxVblZ1VXZSbHF4Q1JidzE4T2o3UVFIcUQxUTZuZitZdldvMmluUDE2ZFY0Y0ZoMG9xZVhZaWNZUEJ6eGlBemJHYy9VeUU4TkdPZzJkRFJjTnFuSkcxVEVINmFWdUU3ZDdKeFdjYWhwU3FCMFJoSjRidjJhV3lnbzExRDIzUm5ZdER0ZUwxOHRiNm5IZi94d3o0clVKZ0tKRHYvUXpxT3JqTG9sZ3RyQktzVXQ3U2I4cXFEK3oiLCJtYWMiOiJhYzIwYzRmMjViZDZkZjY4MmM1ZjRmNzUzNTc0MzZjMTg2NDM4NzgyMTZiMTY1ZDJlMzRiZWU3YjM3ZTY4YTE2IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik11VmZINFJoa2lhLytFejlVY3R3Qnc9PSIsInZhbHVlIjoiMm9jbWZ5aHVHemZKellFNkxqU1NHdHcwT1VCK1F4eTFqL3BsUjhCYm13WmxKM2VBMGxNcWlrQUgraDYxWlJkTllBTXpnekRWSXd3QU5JTWIxZ3JtdFdtNjdJNVdQK1JWV0ZCVGFuSVd3M01FVUNzanY0cm9MZER0clUvLzl4cTBlV1M3SzhmTmlCdjNqRjZoU294MGpCTVdiV20rR1JLL0hHWjRNQVhvRC9lOGdQaFRMeGRkVCtPQ0FlemNMSWdBNUdEQTEzRXZVTUNYYmhUREE1d3dqZUVyUnhyTWJMQkpVWUY3ZVpDNFVuRjdtZU92YmVxZTBMazIxZWx0N1VLUjMwZ2JpUVNBR0VZTWJodWNiRVRSOGZWWm1iMXQ3OUVGY2VZbGdUZ29mREp5amRTcVV2Z25LZGNvdHJtbzFFVXVPV0t0N3EwQTZ4OVdTeld6Rjl0WlNRTC9rRTdDc052NFhUNHNhbE5vK05mS04vejZaOWNqOGo5WndGdGc1dldscGltemRINTFVb0cvYTFqRjlyL2pPVUt6d214MStwK3VWcjkrTmlKbnN2aEpsdys5dVpTcktxVzdSenNMemlFelRKU3RXK0toNWp3TlRDTXNONzY4b3ZLTGdpaGg1eGF5a1hUdHc4cU1iMHJLcEQ2MlJQaFZzQUNST2RvK1ZCcjAiLCJtYWMiOiIwODU0NDY1YjZjNjRkMTM2MzAwOTljODJmZjRiMzIwZTgxYjY4ODkyYWE4OWNiYmRhYmE1MDM4MGMyMTU0NTliIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ing4RlZUcFZMa0pxLzdDWm1vTEpUMUE9PSIsInZhbHVlIjoiYUVWb0NJY0JEeHMzaXBhMUpIaTZ5RTRYLzFHSnZmZVJoV3ZSWkd0SXZnVnZyU1FLRzB4SUloWGxBNjlwOU5aRTN0dXVsSWxZUk0xckpoZklxZmFrelJwbHV0TjNwZVpaSjRJOE9pK1FOdDRMaU1oQldLZnV5S2MwdTV0ZDRKRlkxelh0b3Vxak5LaTVNTEs1U3ZBcGRrNUlPanE2bVI5eEFTTTFGVWhsUytVMmRKNXRmeEJiNTFnOGMvTzY4dnVyTi94aDRwZ1FHSUxGdjJ1SEt1dmNHVHVqSkhyUitqV0Q3S3R1d3RYOHVuYjRvWGlJRm9neHBnekFhdHdMUmM0a25NZm1aNHpZZUZmNy9OT2JWbnBWMnM3NFViVXYreDY5akRmZTdOeEVNZnZNNWs0aEtIbkRPQmpENEw2UlF6L0JOQU1zcE11NFFEcmxVblZ1VXZSbHF4Q1JidzE4T2o3UVFIcUQxUTZuZitZdldvMmluUDE2ZFY0Y0ZoMG9xZVhZaWNZUEJ6eGlBemJHYy9VeUU4TkdPZzJkRFJjTnFuSkcxVEVINmFWdUU3ZDdKeFdjYWhwU3FCMFJoSjRidjJhV3lnbzExRDIzUm5ZdER0ZUwxOHRiNm5IZi94d3o0clVKZ0tKRHYvUXpxT3JqTG9sZ3RyQktzVXQ3U2I4cXFEK3oiLCJtYWMiOiJhYzIwYzRmMjViZDZkZjY4MmM1ZjRmNzUzNTc0MzZjMTg2NDM4NzgyMTZiMTY1ZDJlMzRiZWU3YjM3ZTY4YTE2IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}