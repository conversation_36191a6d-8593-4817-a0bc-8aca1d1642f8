<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PosReturn extends Model
{
    use HasFactory;

    protected $fillable = [
        'pos_id',
        'return_date',
        'return_note',
        'created_by',
    ];

    public function pos()
    {
        return $this->belongsTo(Pos::class);
    }

    public function items()
    {
        return $this->hasMany(PosReturnItem::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }
}
