{"__meta": {"id": "X6ddb4d2994e0e42621725de23377abdc", "datetime": "2025-06-30 22:37:00", "utime": **********.413645, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.981767, "end": **********.41366, "duration": 0.*****************, "duration_str": "432ms", "measures": [{"label": "Booting", "start": **********.981767, "relative_start": 0, "end": **********.347244, "relative_end": **********.347244, "duration": 0.****************, "duration_str": "365ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.347254, "relative_start": 0.*****************, "end": **********.413662, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "66.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01307, "accumulated_duration_str": "13.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.377292, "duration": 0.01148, "duration_str": "11.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 87.835}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3971741, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 87.835, "width_percent": 4.973}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.406055, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 92.808, "width_percent": 7.192}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C**********449%7C9%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVTUXZoVllkU1B5Q1RQUVBtazkrZVE9PSIsInZhbHVlIjoiSDFFbjZSNnFqd2VzYjNvbzhZYW1Qb1ZCSWJzYkVQdUdsRmpkbFZBYUdHRExudkR2R0taNHcxeWVtQXM2NWsxMmIyQWZQbjUwS2hkY1g4RWY2dFIwNnA2UUU2SjJmaTRSWGFxQ2UydVBaNnRVd0s1NXdmQTFheE9aMDdFYUVoU1VlcjU4dUtyMVpQUUROd1VLVzlvYUF1dHdIMzhNRm9SSUJOMnhHZjZvMXdmdzUreDh4SG1yQ3FmUnZCdFJhT3VRbWtHNHg1STAvR1VBdlIwSm9ZS0hSRnNKcDRXNHFJY1lkSE9RRUdnby9VaWthcldYeUw1a09ZRzJmRWVuN3I3WE55aFZtM2NRR3pJTmlGcmdoUzZUL2lEMEhXU2NIcVFzOURGWU9hankxR2FGeHNzak4vT0w1UTBJUURhdktxMi9vYjNsM1g0MVE1cCtFc0M0YWtvUHVKV0RBYXdBL3R3L0FWbmZici9QalFhdVE0VU1vallZNWQxcmFSYWlYQ0d2Vml6SkxOdmlhWWRUMVB2SDgxdllaam5iZURDZ251ekloSWxCR0htS0NjU21FTE9NRXI0QUpURWVvOUZaM0h4NlpvblZkbHpDVWJaemRoNUlKbEZSTzhueVR1UHpxZUJ5NkRDR1NWN1ZRSFgxTWNLQkRXaFB6S2FkMXJoVjdnM2QiLCJtYWMiOiI0OWEwY2E5NWQwYmJlZDUwMjM3MjNmNjAxMTQzNjBlZWYzYzg1ZDNhYTQyOTA3YThhMGRlNzBjMDVkNDQwODg4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im5UeTlmUXViSGdQUjhZZ2lKM1hmN3c9PSIsInZhbHVlIjoiQkl5OGtKRTFKZUh5dWV6elVLM0pXUWhiWXFKc2plcjR4NkNlditlRmdzR3Z5bTB1RVNlV1Zoc0YrNXczYkEvSlRNbVZscmJKWWZSU2NzOHh2elNMN2MyS1pKcVUrVEJkU3dyMGF5M1lodzgvOVZubjZjKzIyTnhKbjh6U0VhSG93akp2MVpHNXl1UW0yL3EzRFVIYXVRL1owbDBLWFU2UHJwUXdMaFE1Z3VaK0ZjYlRJQXY0MjNLK09DRWtEdHBLcW9teFVLTW85ejdPVWorbWtkMXZhanFFeWZZV3JMOSs2Q28vR2hLZDI5VVZkeVZ2RG01VG5wS2hBakdIQ0ZBYVFIbFNKYStqam1CTXdLTWN0TXNHUlpGMjZTWEx6UEZleGJ0Q2Nwai9aSzNrK1BQY0xmZFdMRXVYV1I3dnBiRC9jYjgva3Z1L21EQml5NDBFaXNPbHl4TnNJVVJQeTZPNGlPZXRTckk4c0ttcVYwa29FSW1DVGxaUjIwVjNzS1NNRmkzZG1RellZTVVwdHJxWGVEQW1nYVUzMytDMDlkSjQ2VzllMmVlN215dzRrTCtjOGhOMG9tRUhqdXpZY05CZGxqK2FUYWlzWWc4QnJzQi9qdW51eURieGlrNTlyc3JyMWxkQWRMR3NoODNtME5tdERWVC85YVJLRVR1UmpXL3EiLCJtYWMiOiJhOGFlYWUzNGE5YTMxN2YwZTE1ZDQ1NTg1YzlmOWU5NGExYTQ3ZjNmYTlkMTNjZjI3MDAzZDhlNWJhNjI4YjVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1419213640 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">K7EIYCLnalanTBUASCKMEOK1yUmooVr7ha2cCSMP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1419213640\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1348135521 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:37:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImprMDBmTEIwTHllNnhQY0NlMVVheGc9PSIsInZhbHVlIjoia29seHJnaXdtaUNMR3NNSHJYVm1nWWR4c1hCcUlBNVVHZjk1SXB1ekJaMmpid0VtVTNXcm5oQU9HaTM4TWdneGR3TlF2d1VZaUhTSEhWYjI2RGpQamJxMnlrSERMOGZIMHlhbXdzQ1UreERIVGtVMUhWUUJxU3lhYVJOc0I0anY1S1VrZ1lvVVlNWjZxVWRuMUU1TXBkUWp3Vm8rTXBUeC9zb0I0SXJvaDcxWGZ1S2hJK3RzOUZ1QUpvZ0w2ZFAzSkFsb2ZTWC92UkpVd1RQQ0RKMFAvbkFWNlhzQzVXbDRDdm9JeW5YN2VjdWZ3U3NVem5aTEZnV3lYdkRQTkdYZEx1Mm5rZzVRV0pPZjlDazQyUTRmVkRCaGY4d2dzWVNZWHJySHBkMk05aVJVeGVlT3pyVnBhaWhnejdhWGoycmgzU0lyc0tZcGZONnJYazVnaUQzYWp6Yi9DU2I0VTk1ZktXYnJldzZvQ0JOdDJQTjZML3ozMGYzME50SW9xV1RUZTk4ZHNQUDlPU0tWMEtYMzJQMUFHL3JBZGtaNlBKN2ptaHRxOUJKZXJOMGhRcklpRkxOKzNOOHpZUFRmVUk2c3ozTVpEMkY3MmtZWUNHNFBRU1d5ZVBnVUlBK0IwOUxIa3N0Z0VzbGNSZlM3eVdZTjBjYVdpSU9uZXJQcHFYTkciLCJtYWMiOiI2MTM3MjY3NDI2OTI4MzFjNDdlNTdjYzcxNDIzMmZjOGU0ZDZkNDNmZmMwYTkxNTZmNzcxZDYwODY0MWRhZDhiIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:37:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImRWemtXMkN5RHY1MVVJQlRnMnFKSmc9PSIsInZhbHVlIjoiZUJjT2VydG9oNXgzK0k4MlRmNjBWQ3ozcVBTbjJscW5RenUxc3BPSFp3RjE3Q21Eb1VJMUMwTGNTYklETjUrNS9sQm5xRTVDdk9zcWQxMENadUpKa1RqbU5DTGJDZC9qMDRrZ1ZQSnFBbGhFWXNNS1ZaZ0xUR1F1N2ZhbWZQREdiUFEzdllucWROYkJyL1MxSFZoMlpKL0xUa2VmcVY2bkt4MjNBRURiM3VXanJFb3BlUG9CVXp6TXJjQmFLb25YNGxTaWZYdzNENVRBZTRMUzRYNCsvVHRQNHMrTnhYWVBsZEZZOW9YamlvYmduVjNHZ2RhUHVUWWorcnlRaWtob3Yxbk9YallveENLK0N1cWxZYlI3ME4vdm1QZHB1cnpGcEtjY3g1bzFUT00xc1ppa1pKNkVReGVGRWZUNEtza0dOSWxtMUhFYXF4dy9Kc2l6VGY2Zy8xUG5MMHJYTmx2Y2krTGFqaDE1QzZoSHN6QWtvQjBHSGN5QmNoVGFLb2dDZnJLY0l1Z0ZWQUJDdE5aN1ZDOWZJaW9lZmZ1ZnlSb2dqdTVNd2Y2NjQ3bDRpMTRYc0paM2UzZmQrR3V5eVU3OTU3SmpjbXlnMHdmWFFSZHgxbERwSXFieU9yeVdFejZmOFBWUGdNTlcrOEpxUnVQK2JPaDlZd0diM2tiYWh4dkwiLCJtYWMiOiJiZTYzYTY2NjhhN2I1YzQxMWNjMzk5ZjRlZDU3Y2M3NTU0NzE5NDI0YjQ4MGNmYjkyZTQzMzliZWYzMTliMmI4IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:37:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImprMDBmTEIwTHllNnhQY0NlMVVheGc9PSIsInZhbHVlIjoia29seHJnaXdtaUNMR3NNSHJYVm1nWWR4c1hCcUlBNVVHZjk1SXB1ekJaMmpid0VtVTNXcm5oQU9HaTM4TWdneGR3TlF2d1VZaUhTSEhWYjI2RGpQamJxMnlrSERMOGZIMHlhbXdzQ1UreERIVGtVMUhWUUJxU3lhYVJOc0I0anY1S1VrZ1lvVVlNWjZxVWRuMUU1TXBkUWp3Vm8rTXBUeC9zb0I0SXJvaDcxWGZ1S2hJK3RzOUZ1QUpvZ0w2ZFAzSkFsb2ZTWC92UkpVd1RQQ0RKMFAvbkFWNlhzQzVXbDRDdm9JeW5YN2VjdWZ3U3NVem5aTEZnV3lYdkRQTkdYZEx1Mm5rZzVRV0pPZjlDazQyUTRmVkRCaGY4d2dzWVNZWHJySHBkMk05aVJVeGVlT3pyVnBhaWhnejdhWGoycmgzU0lyc0tZcGZONnJYazVnaUQzYWp6Yi9DU2I0VTk1ZktXYnJldzZvQ0JOdDJQTjZML3ozMGYzME50SW9xV1RUZTk4ZHNQUDlPU0tWMEtYMzJQMUFHL3JBZGtaNlBKN2ptaHRxOUJKZXJOMGhRcklpRkxOKzNOOHpZUFRmVUk2c3ozTVpEMkY3MmtZWUNHNFBRU1d5ZVBnVUlBK0IwOUxIa3N0Z0VzbGNSZlM3eVdZTjBjYVdpSU9uZXJQcHFYTkciLCJtYWMiOiI2MTM3MjY3NDI2OTI4MzFjNDdlNTdjYzcxNDIzMmZjOGU0ZDZkNDNmZmMwYTkxNTZmNzcxZDYwODY0MWRhZDhiIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:37:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImRWemtXMkN5RHY1MVVJQlRnMnFKSmc9PSIsInZhbHVlIjoiZUJjT2VydG9oNXgzK0k4MlRmNjBWQ3ozcVBTbjJscW5RenUxc3BPSFp3RjE3Q21Eb1VJMUMwTGNTYklETjUrNS9sQm5xRTVDdk9zcWQxMENadUpKa1RqbU5DTGJDZC9qMDRrZ1ZQSnFBbGhFWXNNS1ZaZ0xUR1F1N2ZhbWZQREdiUFEzdllucWROYkJyL1MxSFZoMlpKL0xUa2VmcVY2bkt4MjNBRURiM3VXanJFb3BlUG9CVXp6TXJjQmFLb25YNGxTaWZYdzNENVRBZTRMUzRYNCsvVHRQNHMrTnhYWVBsZEZZOW9YamlvYmduVjNHZ2RhUHVUWWorcnlRaWtob3Yxbk9YallveENLK0N1cWxZYlI3ME4vdm1QZHB1cnpGcEtjY3g1bzFUT00xc1ppa1pKNkVReGVGRWZUNEtza0dOSWxtMUhFYXF4dy9Kc2l6VGY2Zy8xUG5MMHJYTmx2Y2krTGFqaDE1QzZoSHN6QWtvQjBHSGN5QmNoVGFLb2dDZnJLY0l1Z0ZWQUJDdE5aN1ZDOWZJaW9lZmZ1ZnlSb2dqdTVNd2Y2NjQ3bDRpMTRYc0paM2UzZmQrR3V5eVU3OTU3SmpjbXlnMHdmWFFSZHgxbERwSXFieU9yeVdFejZmOFBWUGdNTlcrOEpxUnVQK2JPaDlZd0diM2tiYWh4dkwiLCJtYWMiOiJiZTYzYTY2NjhhN2I1YzQxMWNjMzk5ZjRlZDU3Y2M3NTU0NzE5NDI0YjQ4MGNmYjkyZTQzMzliZWYzMTliMmI4IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:37:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1348135521\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-911312681 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-911312681\", {\"maxDepth\":0})</script>\n"}}