{"__meta": {"id": "X70955c98a53957e4ec4a1fa192be380c", "datetime": "2025-06-30 22:36:40", "utime": **********.201441, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751322999.729249, "end": **********.201462, "duration": 0.4722130298614502, "duration_str": "472ms", "measures": [{"label": "Booting", "start": 1751322999.729249, "relative_start": 0, "end": **********.13944, "relative_end": **********.13944, "duration": 0.41019105911254883, "duration_str": "410ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.139449, "relative_start": 0.4101998805999756, "end": **********.201464, "relative_end": 1.9073486328125e-06, "duration": 0.06201505661010742, "duration_str": "62.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45706936, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0036899999999999997, "accumulated_duration_str": "3.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.168584, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.583}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.179263, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.583, "width_percent": 14.092}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.185416, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.675, "width_percent": 20.325}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-438949 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-438949\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=6nrx0y%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1aj6s00%7C1751322883410%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImhKQ0t2ak5BMEtwUmY1dk52NFloNXc9PSIsInZhbHVlIjoieGxLa1FzTktNVXhYT0dyUWFMQi9VZ2lGc0hpQ0IrZ3JUWkp1U1V0TzhmRmtSMU5FQTU3V1VKUFptRCt1VFljZG9VS0RVV1Zkd0s5OWZQS0YyZk1QOTdWWmFaeGZHSEprM2NUU1V2WFhnS0NJQ2dOanNubCsvcklEQzJoRmpiZTFRZVlkMVBUa0xqbWErTlI5SyszbTJPcEEvWkswM0VlUUM5TVJ4bC9LbklhNWlGcTlkby94cHRZRTVIOUpCWlJYK2c2MUorL0s4dUVERFRoYkNKWnFydzR3Y3cyM0ZKNEpHYkZqTXg3NlNJQ0lXeUZqcmVuZ1lWR29yMHZ6VlFPZGYyYmc1N05nQTdUNkM1ZXhpYndKVlNJdGNsdlZNNm54RURRbndmWVdKemQ1Q2JOK1k0T1l2RWpNU0NKV2tJMytBcFcrSTFIcUNldzlEV29mektjVWFSOHY2M3YwN2xFbDIwcFEyeE8xa0U4Mk0yVXF0SmUvaWYrRnBtNWhLMWdHb1RXemVlQjl2R284YTlUQVhRMDdsNXlqenpjWXBXeW51MEhiczVEbXdTZVptMFJpZTVMdjBwU25CL0tEMjNwc3ZNRlhya09QSzVFL1Fmc0lGc29wcHdQdmFMbU1xcFRuazJTeWlvYitHbzdPQ2NGeGIzVUx3dk5GaE5xTy9qYmwiLCJtYWMiOiI1MmQ4ZGQ0MTc2M2VjM2I1NDM0ODY5MzI5NDkxYTI5YWRlNTUzYzc5NmZhZDFlZmFhY2Y4NjhlZGM4YmI5NWY1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InIvUDBVYmNZemp2cGUvdXFZRXpMcGc9PSIsInZhbHVlIjoiYm1BamYrc3cwWkdGOW9YOFJjU2MvMFk2VVpLTE9GYjIrOE1VN3BUeGRqRWFid1kycVJNWkZhS09ERUVPZ3pQL2NUOW5RN3RCdko3RFFaeVNvYUNoNTFaTUNpdmNLblgrQzlCcHQwRWgwNDZOZlJDakptYWw5WnZMZU5ObmpQRWNRU0R3NWg2T0lFaUVQdkEzVXltcG15dC9MamVpdHNaeVFVazhNNHBTdDV3SlFwTVNvR1BqRFJ6c0t0MlVGcFRvYUpqWmRKU0Fab1pHN1ZrUVQzcy91RWNSdGt5S001RjB6UWRQTUdQNXNzN3NXbzI1TEtsNSttb3dIWTJQaTBzNmQ0VGtiWjB5YkQvS28zYVVHYXZzL2RKZHhWSTBLb3lJeVpRVCtjd093QTJaYW85WE0xQkUyUXNwUWcveStDKzVoV0RVOS8zeFRxV0FKYnMwZzlPRGZmN2Q5V1BvWkV3cDFBR21mRzhJSmhUWkFqTUVMMEFvdFRPU2lJMmtSbFMxRGpSd21vbVhBSU9zQ0lNV1dIVkJHbSt2dE9hUTEvK01aU2U3d0pINFZIOUVVUmJrZHFPMHErTlJGNW5VM053emlnM3VHdzZrNDZtQ2tlMmV2UWQ0ZFE5bzNsODJtbjJnV212L0tZYnUzVTVvOGFEVkVoWXAyV3pScVdIQUxSZTMiLCJtYWMiOiJjZDViMzUyMDFmY2FkN2UwZDZmNzI1YmQ1YTMxMjNlMGU5N2MwNzQwNzg4ZjcwOGMwYTc1MDg4NGY3NGE0NGZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-498154136 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A9rdyCRm7SKpfljuMnVO7IpcgTBMITMjowAdsmJo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-498154136\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-135547653 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:36:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBIaThTcDB6YS81ZVI5dm53cUxXaWc9PSIsInZhbHVlIjoieit3eVlCWEo2dFg4YXpVUmhNYktoNWE3cnZLMHdOQksxbzJtZFNycTRLSDhicTMxNURIRHFQeWFTMjdEOXJXd0tneWxFazRlcVkyNmxVOGozOU94bUZicE1WNklOa0sxcjlPK3JyNHdRdkNEZVdJalZqTjhUckN1cjZSZElQZXRzVFcwSGRoeGZUaE5IUjQ2clFyVitMbjNRM05lcDJIQ0tBR3N1VkE5MG1MVFN1ZGZTSUpYMEhETzE2TUd1bUZSZWN0ZmFwV3Q5V1lEU2xKVGJzTHQxRldCZmpWR0pRK1Q5d29BNUhvcXRjajRyYllnVmJaMVVDd1FJNEdTdjNFQjQ0Y1o1cDBudFYraEowSFc4VkJjWUk1QWEwNnp0WktKcW9xMWg3cThOUDYrZTZlTkxaR1FiQWgyMFlwTFdkdklxVVJCT0xycnVGUmdiMklDdXdpcFhaWjVsOWZ6bVBhczg2T1p5RHN6dHdnQXFxb1ZtQ0FaNDh0YVhlQUNFNUtqdUZZSmNpUjdFZlpqN1VNNWIzL29CS1NXa3JoWU03Ynh5N0NkbEFma0dPK0tEMGoxbFV5cmRhRHBjSnZxUnI4MGhWaEt3Y3lXNXpXNnh4dlkzVVRlaWwxalBHK21aUnRqWWpNVjJHeDVnNHVsL3BNYy9Jb3BJOXFpczduYVB3aHEiLCJtYWMiOiJmMDYxMmZjY2YwMGIyZDZkOGUxNmMzMzI5OGJjMWJkNmI3N2VjM2NiYWIxNjgxZWE3MjZkYWFjYWM4ZDVmZjM0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InBwNnM0dUR6Nk53TGh5MW1XMm9KREE9PSIsInZhbHVlIjoiYTVOY25NRXJxYnlTWEI4WFBPT0dVZGpOQWpLYVgvakJ5MmcxdGN5cGd0SEZDaTBRaE9OWjFCYlRZT1h1dHJ1SGJSamFqMnVRS1V6NHhuWGVmaFEzQmpFRDY5akpEZkYwVDhvQVNLRjhQOURaZ2JPMW1pb3ZXeVd6ODRlTnYvY3RudzVsVmYzekJ3eUp0MXYrOTh1VDV0OEZjejRmNGhxczRkcEhMc1VFTFVZMURCSzNhNGFNQmg5TlpmRDNrK3BxZmtPcW1NOWhJcTlVVGw3TXptbmhWRlhWb1VMbFlmcmU4SXJLanpJQVkrUUhHRWQ5SS9zMGJKZXduUWlveGlqanpkSWxJQlM1SkJsRWgyU1VsNzRnZWo3WHhnS1h6cXZVSkNPL0xMOGx6ZEVzK0xhQXloTzZxYks1T25aa0NSc0FqeitZYmYrRENsc0F0bFFmOXpzZktPNy9NUmFMdjdSdkpyTkc1ZVJ1eS9iNC9sYWZLemVxL1huUndXeDgvUmpLRjVwNUMwTExnZW9qL2pqYTZRbGQ0Q1M3SC9TUHB2NWVBb0MrRnlsWC9INVhNTjNIck9UUFVCM3J2QU9XK2NQbXlSUDkzRW1ZMGR1QytpS3MyWnFKb2ZvakRSb3ZwYWdBck9ST25iZ01rcnlYZkR6TWtPQVRxbWUyeE5OczVTQ0ciLCJtYWMiOiIzNGYzNTc1NDhlOGYyMGNkZTgyNGVmYTYxNjUxMzE3Y2U1ZjQ5MzhmN2I2MzMzODc5MmJlNjMxNDk0M2IyYmQ0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBIaThTcDB6YS81ZVI5dm53cUxXaWc9PSIsInZhbHVlIjoieit3eVlCWEo2dFg4YXpVUmhNYktoNWE3cnZLMHdOQksxbzJtZFNycTRLSDhicTMxNURIRHFQeWFTMjdEOXJXd0tneWxFazRlcVkyNmxVOGozOU94bUZicE1WNklOa0sxcjlPK3JyNHdRdkNEZVdJalZqTjhUckN1cjZSZElQZXRzVFcwSGRoeGZUaE5IUjQ2clFyVitMbjNRM05lcDJIQ0tBR3N1VkE5MG1MVFN1ZGZTSUpYMEhETzE2TUd1bUZSZWN0ZmFwV3Q5V1lEU2xKVGJzTHQxRldCZmpWR0pRK1Q5d29BNUhvcXRjajRyYllnVmJaMVVDd1FJNEdTdjNFQjQ0Y1o1cDBudFYraEowSFc4VkJjWUk1QWEwNnp0WktKcW9xMWg3cThOUDYrZTZlTkxaR1FiQWgyMFlwTFdkdklxVVJCT0xycnVGUmdiMklDdXdpcFhaWjVsOWZ6bVBhczg2T1p5RHN6dHdnQXFxb1ZtQ0FaNDh0YVhlQUNFNUtqdUZZSmNpUjdFZlpqN1VNNWIzL29CS1NXa3JoWU03Ynh5N0NkbEFma0dPK0tEMGoxbFV5cmRhRHBjSnZxUnI4MGhWaEt3Y3lXNXpXNnh4dlkzVVRlaWwxalBHK21aUnRqWWpNVjJHeDVnNHVsL3BNYy9Jb3BJOXFpczduYVB3aHEiLCJtYWMiOiJmMDYxMmZjY2YwMGIyZDZkOGUxNmMzMzI5OGJjMWJkNmI3N2VjM2NiYWIxNjgxZWE3MjZkYWFjYWM4ZDVmZjM0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InBwNnM0dUR6Nk53TGh5MW1XMm9KREE9PSIsInZhbHVlIjoiYTVOY25NRXJxYnlTWEI4WFBPT0dVZGpOQWpLYVgvakJ5MmcxdGN5cGd0SEZDaTBRaE9OWjFCYlRZT1h1dHJ1SGJSamFqMnVRS1V6NHhuWGVmaFEzQmpFRDY5akpEZkYwVDhvQVNLRjhQOURaZ2JPMW1pb3ZXeVd6ODRlTnYvY3RudzVsVmYzekJ3eUp0MXYrOTh1VDV0OEZjejRmNGhxczRkcEhMc1VFTFVZMURCSzNhNGFNQmg5TlpmRDNrK3BxZmtPcW1NOWhJcTlVVGw3TXptbmhWRlhWb1VMbFlmcmU4SXJLanpJQVkrUUhHRWQ5SS9zMGJKZXduUWlveGlqanpkSWxJQlM1SkJsRWgyU1VsNzRnZWo3WHhnS1h6cXZVSkNPL0xMOGx6ZEVzK0xhQXloTzZxYks1T25aa0NSc0FqeitZYmYrRENsc0F0bFFmOXpzZktPNy9NUmFMdjdSdkpyTkc1ZVJ1eS9iNC9sYWZLemVxL1huUndXeDgvUmpLRjVwNUMwTExnZW9qL2pqYTZRbGQ0Q1M3SC9TUHB2NWVBb0MrRnlsWC9INVhNTjNIck9UUFVCM3J2QU9XK2NQbXlSUDkzRW1ZMGR1QytpS3MyWnFKb2ZvakRSb3ZwYWdBck9ST25iZ01rcnlYZkR6TWtPQVRxbWUyeE5OczVTQ0ciLCJtYWMiOiIzNGYzNTc1NDhlOGYyMGNkZTgyNGVmYTYxNjUxMzE3Y2U1ZjQ5MzhmN2I2MzMzODc5MmJlNjMxNDk0M2IyYmQ0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-135547653\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}