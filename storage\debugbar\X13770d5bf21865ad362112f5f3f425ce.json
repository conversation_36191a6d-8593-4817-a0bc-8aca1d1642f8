{"__meta": {"id": "X13770d5bf21865ad362112f5f3f425ce", "datetime": "2025-06-30 23:14:30", "utime": **********.147492, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751325269.714831, "end": **********.147511, "duration": 0.4326798915863037, "duration_str": "433ms", "measures": [{"label": "Booting", "start": 1751325269.714831, "relative_start": 0, "end": **********.097771, "relative_end": **********.097771, "duration": 0.38293981552124023, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.097779, "relative_start": 0.3829479217529297, "end": **********.147513, "relative_end": 1.9073486328125e-06, "duration": 0.049733877182006836, "duration_str": "49.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45015392, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0021899999999999997, "accumulated_duration_str": "2.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 23 limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.124743, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.47}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.133998, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.47, "width_percent": 16.438}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (23) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1395192, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.909, "width_percent": 20.091}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WuYcucCgl4bBOLSdLTqnajP2UNBzqFh0FtUk6GCo", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "23", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-958824280 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-958824280\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1969519964 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1969519964\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1523723637 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WuYcucCgl4bBOLSdLTqnajP2UNBzqFh0FtUk6GCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1523723637\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2113177024 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325251418%7C34%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpmRXFHcWN0ajBjamFmVkNaNjhUc3c9PSIsInZhbHVlIjoib1hHZnEzOVk0OWVmU3p5VWMwaVFDRTgwYUR5UCtZZ1czcStCQUJ4SEtOQkYvbzBZSUZQNzk5T3Irb0pRTnQvZHcyYWYzYlUvZFRlQnM5S0VMeElSUW0rM2RWUE1oMm9VdFd6cmJpSmJRMGFpakRBZEFTdGJWZlBDT1JyOXFHUXd0WmVSeWdCVGhNQXZBeDlYeTU3cXFyZ3VLRkNneW9QbjZZRWpsamtjT0czQ2c1QlI3MjdSeDd4T0xSeTg3OTdNU0dwWXJ5c2pKMVlrMVZjL0JGREdSNjJuVHpXQTZNVVFYbDVwVmVXajgvUVF0OHRRZWFUUTcvZExHMzQ2a21wWFM2cnJwZVFyUU9ydUpwYU5xVm85NTBBWG5rd3M4V3hia2dHRHJPQTdlVGNlNGpob05zVDJQNzNHdXVLZzZWM29QYU5oaVJLRnZQVmx4RU0zNzVZd3VlcmFvNlFpeDhtNlo5d3NEZ21IRGp4UnByKzh1UURoSGUvOTkwRGkwaktvR3pwV3kwbnVtcVNabWY4ZlVwTzJScHFwbVUwVFFqamJ5Q2VFSHRRTDBRNGdjbGc2Z2Vzc1kvR20vWlhsZ0o4dC9IMDE4SC9Qd2RaL0Y5cm1QM0d4L2MrYzJQcHBWYXR3dmxnc1U4WHdoeTlnUmM4WkdOSzJjckxqMHBmNjdJSGwiLCJtYWMiOiI0YmMyYzgxNzU3NTMxZWNiMjdjMzY0YzA2NTkyYTY4ZDIxOWYzZTM1YTQzYjI3Yzk5OTE1NjUzMDY0MWFlNDFlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IitKVEdqNzBJV2grVnB2dzNnc3NpTXc9PSIsInZhbHVlIjoibldCTm1iNEhuT1dVcHArUzZtZldiaWJNUUx3RU5seFIwNzVLeEhUcHFkVUhqMGs0N1c0QkJzRDd1cDR0OHZuRHk3U1orM3l5MldBUC9FUmhlYklaMVNxak1EeVJxMHJkdlkwc1BlamZvenZJbzlqSzNtVDZoY1NNWVN2L1VnSkltanFJNHVFZWdDR1pWV2dYb3cyRXlaVExycWZQT0xqZDRTaTNCYlZnYUliN3hYVU5naEZBQmlTZWZ0QnBSTWc1UFA0V281YnQyd3IvVmRFdHRVclNjdjVlU0Q0ditWais3RnVKellrRmx6N3pLMFE2WHVjeUhza3RJbVZtSmFFSVFGNXRxQ3dyVjBRYVRDQXcwRzRQdWg5R3RXTUN4ZVNzbWtJZ1QzVkp2OWpNdkpaTFZVb2lOZDd1ZnVyQkFubU9oSUxMbHUveUtxV0pqMHRLNkdibHcyQ1ZINmgxK2hKbHVqME1ZODVoUjRGQWR4dWo1dTZydkxqV1owaTk3VUhXRmhTdDc0SUpodENvZHprRmt6WmIrTTV2d0xTUWhHT29aZTNmaWJDblVxeWMxY0s0bS9ScW8wdksxbW83Z3phNytGWW8wMGxRVjJ5dFQ1dkVhaG1iZUZ3Ym9wSk80N29wM29JOU1Sb05HdndvVWlyc0t5QWRCb2MxOXdpOE8rL0kiLCJtYWMiOiIxNDI4YjA0YmVkNDE5MzU5OWQ3OTFlOWYyMTdmOGMwZTFkZmY5ZjUwYjJlNDBmNDM1MWVhZDRkOTg1YTVmODBlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2113177024\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-225139693 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WuYcucCgl4bBOLSdLTqnajP2UNBzqFh0FtUk6GCo</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">m2aShS6RaaTcTNL9OqnWxuHeTxyTGRJLBdK2tqlv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-225139693\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1035867494 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:14:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjB3d2IxYXBKd2wvR1NyZXVRS2ZkaHc9PSIsInZhbHVlIjoiQVY3OVlIUW5Sbkk5emN6OUwvTWhzeWFPTG1ZcHM5clR4bXVGeGZJNERPL0JqckdjUXh6OEwxWHVwYkNac0tZUy9tNzI5bGxVMlJyN2ErUkZSb0RWU3EzenV4NEVXendNSmR6anRuQnBjY2tJcFM5cWxLVW0rVzZvNW1Sb3lLNXYvMG93VGNHUFpEaTNNS2dkbDgxUmZTL0lZYi9iZDdlY0xrRWd6WXQxZUUybUJuVUx6eFYzZFpJQzdKbGJaOWVSYWp2eUdySGdHalVYbmNiZnJiT0xHbGZ3bmlnQkpXYkpHY3V4ZjE2OGVvN1pETjZtTE00UmtLZktud1RZMG5NeG1EbUxyVTZtSnFPTlFXaUpqbDRydXNjMmxFREpKWTQ4d1BrSVJYWHVtRGJRaFkxM1UzZWgyY2Z0SFpwS1FmbUpmU2FuNVo5OFl2aXoxWlVJbU4rdEtRUUxQcE0xYTJqeGlzYi9ZY2RiRTJnbEhzTzYxUWFDNGQzMks4UnVQVFpnZkFDZ0lNN250R2g5OWJCL1pqenp1S09WZmZkUUlYd0VPRkxYc2ZOK1RXTGdTTWtyZ3hsVGhKU0hBd2lOWnY3MTNSUlJoenE4T1pBMmREYjZCN2czN1RaNy9udUpmYlFsNGhhMkFyVUZHR0NPRDdwUFZwdmczNFZjZ1M4M2I4VVkiLCJtYWMiOiI2NmRmOTE1ZDRjMGQ5NGUzYjRlMzMwNDI4OTQ5ZjExNGE5NDYxY2IyYmZiZGQwZDNiMWQ0YjdiNWFkMzJhZjdlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkNNNGJOZy9kV29rcUxkbGdjT3Y4Wnc9PSIsInZhbHVlIjoiR1pHZDhqRmFHclkxVUJuT3B1OU12ZUEzdTJ1VlBtTDB5aHJ6dnA1V0hIdWNLSEwyZXY4Wm1pNVBZTlEvdzRGQmw1eG5vSTlOMHI1UG41Sytwd0Jnc0hKOFY4a1dLMzl1b3Ywd3dGNlZWa3JrdHN0VmxESzZUUHlrN01VdlQwZ2g5Q05yVGlPckswZmNIdVZCTG9DL29IbU93N09lTmw3cFIyeFRBSWJLSEtPczlya3NLbHNwUkRGZldybzVQbk9EbzdCZEN1bmZObUc3YkM4WG0yOFdSZ1pQMWR0UUpwTkNCMGFSSTFBQmFNeC9XR1pLLzl3WUZod3h4WXppVEwyTitHYlFuNnBsaDloYXIwNDhaNmIvU3VtRytuTnJuaCtxaXU1d3BXbVlRcEI1QUppdDZIUllZRGxkRmRxaUp1WGgzdFNOWlBaa3ZqdmdmVDZBYnllMERjb0w1TkVLOGNDSTlPckRKMDk4WWpsbUFRbkRjU1N0ZE5BZUxsSlhwT3d4K3Q2WWs4L3ZOQStlUW96VGlWZERnOHdRanl5YXlMdWtoSVVRbXU1bStFQlI4S0xHc2JRVjl4U3ZPVFBCWm9GSk9tRVozN1lERUhURGZvNmhFZUxzSTd3a3dBZkZ4Y2F6WUdhbGhOWFhPTHdzZEJEcW9CVG5KUDhYZzg5cFpPb1giLCJtYWMiOiI3NDdiYWE5NTM4YmZlMjJlYmNhOGE3MWYzYThlYjYyYWJjNTQ1YzNhZGJkZjQyYjU3MGQ5ODEzZDE0OWQ1MWY0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjB3d2IxYXBKd2wvR1NyZXVRS2ZkaHc9PSIsInZhbHVlIjoiQVY3OVlIUW5Sbkk5emN6OUwvTWhzeWFPTG1ZcHM5clR4bXVGeGZJNERPL0JqckdjUXh6OEwxWHVwYkNac0tZUy9tNzI5bGxVMlJyN2ErUkZSb0RWU3EzenV4NEVXendNSmR6anRuQnBjY2tJcFM5cWxLVW0rVzZvNW1Sb3lLNXYvMG93VGNHUFpEaTNNS2dkbDgxUmZTL0lZYi9iZDdlY0xrRWd6WXQxZUUybUJuVUx6eFYzZFpJQzdKbGJaOWVSYWp2eUdySGdHalVYbmNiZnJiT0xHbGZ3bmlnQkpXYkpHY3V4ZjE2OGVvN1pETjZtTE00UmtLZktud1RZMG5NeG1EbUxyVTZtSnFPTlFXaUpqbDRydXNjMmxFREpKWTQ4d1BrSVJYWHVtRGJRaFkxM1UzZWgyY2Z0SFpwS1FmbUpmU2FuNVo5OFl2aXoxWlVJbU4rdEtRUUxQcE0xYTJqeGlzYi9ZY2RiRTJnbEhzTzYxUWFDNGQzMks4UnVQVFpnZkFDZ0lNN250R2g5OWJCL1pqenp1S09WZmZkUUlYd0VPRkxYc2ZOK1RXTGdTTWtyZ3hsVGhKU0hBd2lOWnY3MTNSUlJoenE4T1pBMmREYjZCN2czN1RaNy9udUpmYlFsNGhhMkFyVUZHR0NPRDdwUFZwdmczNFZjZ1M4M2I4VVkiLCJtYWMiOiI2NmRmOTE1ZDRjMGQ5NGUzYjRlMzMwNDI4OTQ5ZjExNGE5NDYxY2IyYmZiZGQwZDNiMWQ0YjdiNWFkMzJhZjdlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkNNNGJOZy9kV29rcUxkbGdjT3Y4Wnc9PSIsInZhbHVlIjoiR1pHZDhqRmFHclkxVUJuT3B1OU12ZUEzdTJ1VlBtTDB5aHJ6dnA1V0hIdWNLSEwyZXY4Wm1pNVBZTlEvdzRGQmw1eG5vSTlOMHI1UG41Sytwd0Jnc0hKOFY4a1dLMzl1b3Ywd3dGNlZWa3JrdHN0VmxESzZUUHlrN01VdlQwZ2g5Q05yVGlPckswZmNIdVZCTG9DL29IbU93N09lTmw3cFIyeFRBSWJLSEtPczlya3NLbHNwUkRGZldybzVQbk9EbzdCZEN1bmZObUc3YkM4WG0yOFdSZ1pQMWR0UUpwTkNCMGFSSTFBQmFNeC9XR1pLLzl3WUZod3h4WXppVEwyTitHYlFuNnBsaDloYXIwNDhaNmIvU3VtRytuTnJuaCtxaXU1d3BXbVlRcEI1QUppdDZIUllZRGxkRmRxaUp1WGgzdFNOWlBaa3ZqdmdmVDZBYnllMERjb0w1TkVLOGNDSTlPckRKMDk4WWpsbUFRbkRjU1N0ZE5BZUxsSlhwT3d4K3Q2WWs4L3ZOQStlUW96VGlWZERnOHdRanl5YXlMdWtoSVVRbXU1bStFQlI4S0xHc2JRVjl4U3ZPVFBCWm9GSk9tRVozN1lERUhURGZvNmhFZUxzSTd3a3dBZkZ4Y2F6WUdhbGhOWFhPTHdzZEJEcW9CVG5KUDhYZzg5cFpPb1giLCJtYWMiOiI3NDdiYWE5NTM4YmZlMjJlYmNhOGE3MWYzYThlYjYyYWJjNTQ1YzNhZGJkZjQyYjU3MGQ5ODEzZDE0OWQ1MWY0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1035867494\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1362068330 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WuYcucCgl4bBOLSdLTqnajP2UNBzqFh0FtUk6GCo</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1362068330\", {\"maxDepth\":0})</script>\n"}}