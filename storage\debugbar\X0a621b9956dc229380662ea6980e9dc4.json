{"__meta": {"id": "X0a621b9956dc229380662ea6980e9dc4", "datetime": "2025-06-30 22:39:35", "utime": **********.904068, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.471148, "end": **********.904082, "duration": 0.***************, "duration_str": "433ms", "measures": [{"label": "Booting", "start": **********.471148, "relative_start": 0, "end": **********.829617, "relative_end": **********.829617, "duration": 0.*****************, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.829627, "relative_start": 0.*****************, "end": **********.904084, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "74.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01905, "accumulated_duration_str": "19.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.862145, "duration": 0.01763, "duration_str": "17.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.546}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.88855, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.546, "width_percent": 3.36}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8967848, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 95.906, "width_percent": 4.094}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751323174926%7C11%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imtpa0VyemY3U1JiWnJITGZ5M2E1QkE9PSIsInZhbHVlIjoiMlB6cElDT2tibUwxaTlXQThJeVRhV0tZRE81c29teDVTVTJkd1NYWlA0a3NZb0tSK2RvRHNIaG84REZHSDB6NndPOEJzMzNoMWJSTWJ5UzFUTlNnZllhbFhncGpHeUVJclRBSnRKNWQwcVJkYzN4MnFkemtRb3YrckZjVXVmVjFUVmRLbHVPekhBMmFadXE1QUQvS1Z2MFRjVVhjVEUvNGlyZUttSmtGTjU4cER0UWlRVHcvMWlEQWVGWkZ4OFBwdVEzUlZnZlZhYnRpQVkvdHhneHNkVE1HSUlzeXp6WGM1bUlIMVB6Sm52ay94RWZvNjdHbGZjMDdvamh4bGFNMGZYdDBFcmNqR3Jkc1JpdkZWY1BlSEdKQzk0OEZwTEZaQTk0WnptcDhoZmp3WkRkT0xvRVZWWWg3a1BxbUMyRURFZHBtTEhpRzNZT0d5OVJNVGx1cGVmNkw2Yk5RbmQzUVVrQ3hQZ0IvQXJrQUtVSnM1V3ZDNWxJNEx2TWpxcitCaWhETDlKcXNmTG5PQktMQWVXc000Y2V3Q0VNUmcrVDBYV3ZJNmhJak0ydXFjNjFiM1RvME5IRlFCamJrWEdSUVFjMHNHbmdMMFRoWEdhT2R6cG53MmlWMDc5OUJWK3NRVHlPN0x3ZzZOZnQ2OTJkeStMK3p5d3FDVXlWSzRBdEwiLCJtYWMiOiIyODFkMzc0NzBjMGZiODdiMGU0NmNjYmJkZWFiNDk5ZGZiY2RjMjMxNGJhZjc2ZDJhMGM2Y2IyN2YxMDVjMTIxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlUvcHNEVStGTVc4bi9wV01BZ05JSVE9PSIsInZhbHVlIjoiSFFMK21XbnB5ZEVWQVlVRjM2ZDhWNGF5dTl6bDZpNmZVNTBnc3NZclRZYlRWR0lIaFdyMEk1aUd0WXN0Z2NoREx3YU5SZFo1RzRsSGMyb3pnVTdBOXZMSU5nQVZBWnJoZlJGMHJWN0doRnRxR01VL2pBN3U4MG11QWZPbk5BWTZmU2xKbTVMN0FsV3F2TlE3Q1hrajNoMUFocmhGaUQzU2J2QXc4WXFiVy91SWF2cHBQKy9DT2RkaHJVTFBTenJHSGljMnlLL2VhRUUvTU1NaDJTa09zYjZYTnk3RndlU1NpQVBJYko0OCtJUDAyQVp5ZW42VGVwQTFIY2xZMkN5WGJKbGJyaUEvUXd2RDI0TkV3QjNnb0dFcWNYS05xUzZDR1Zzcm1EM0ZDWEc2cXV3NGhyTW9pNVZOaVEvcU9jSjY0OC90ek5Dd2hWLzIxTUowdE1KbUh5S0RUQmlBU2ZGS2tsR2RZRzNXcWtSMGpOaEhnVmpxcko0NVJIeG8yK2N0eW5BMTcxREhLMS9EOVIzK0s5a2VabEdDQmJZSElZbHE5amhTQ0QxTjBXYWhOTU91cjZnOE4yWjI4ZHA3SXIrb0Ywc0ZBejd0NmZDUE50bzFTR1RNTVJ4SHR1eHRub2o2bkFrRFdJSmhEYXphZjM5S2MvNHZFcTdKcWkvOHdDZ3QiLCJtYWMiOiJiMmVkYTJiZTdiODBlMzcwZjZiYzU1ZGJiZDYyZGU2YjVhYmYzZmRiM2Q1OGVhNzlhZWVjZGI2MDA5NzNlMDAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-965863477 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">K7EIYCLnalanTBUASCKMEOK1yUmooVr7ha2cCSMP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-965863477\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9WeTNmV0RHdW4wNWZTTHQ3b3p2VEE9PSIsInZhbHVlIjoiMU9KbWtMbWVkcDRybks0TG1qNEUwTnl1U0pSdEZIakFnTTVTM0pFcDhtcTBNSlprd2JKQWROT3NIaTB4bHJ2THpyUk5YRkxMK2dkMGJjeDQ4ZVVBZjRTYU1MbVhxU1VqcVkwUWkrNEgwUXdJeG1QYWtaMHpWRlc2bDlkSC9hS0l4U090VzFWbHBzMTZycWRlVEtxdHFjM0lGLzdKanNQM1hWWmNMQXdLM1VrblAxRGFPV04vMkF3SnB6Umg0Y2EvYjMybkxOZDJXSUFXWjJiY2I4U096NWV0NkVVc25haHZoakc4UjB1cVc5dXRzMDZFOHJVa0x1TUlSayt6UldWcmxhN1ZTdzhOdlN4NDY3cko2SmhTeVUzdlBiMTdsOHJHTlp0RnhIcmNYV1VSK2V4cE5HSFEwUHk3eTlXVXU2Zm53dlhkQUJQd2lIVWRCbTJ0d3RsZlkycjhMZXFHakhqL1p0dGNUWmhmbnh0YzRQTEwrRVFyK09ERmNMTFRtZzNBckxERWxlRTJxRU5xSFcwRU1rZWUyWDlHVjdNVXkvdlRYaWY3YmZHNkg3enRHRUM0OXl0K3VqNmpudWdWMTNsV0wxTWFTMGNaN0NxYW1iQWpYcDVCdHczQVVlTFJPTnpSNGd5TXBQQ0k0bFQ0djhTbnRqeStMR0NhamU0Z0ZvYlEiLCJtYWMiOiIwNjhmZDY3YjMxNzgzMzlkNjM4ODQ4YjRiMGIyNzNjN2M5MmY0ZTlkNjZiZDQ5NzA1NGY3NDgyYjc1ZGZjYmIyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im04d1J5WUFUZFRuT3JuS2NzSitxYVE9PSIsInZhbHVlIjoiZWdTVy92MVVyWS9qR05Wc3M1N3oxSHhmcmpOMTVacTJJR0Nxa3VsTjg1MllHbEhsU1laR3hIblBZd3BSUVlyZUFEbXBMVWhRRUFKZFpjaS9CMk1YU0dZK1VWL3Q0QzZydXNjZjdjZGpaVEdzaDg0NS9Sa0g1V1JJdVhTaGNNN1VEbE5PWmp6ZEd5RUxFc1RXeVBiSUluVTJBOFU2NHkwQ0FHRmNLUDJKazhjczVVY3hJYWtHN24xcDUwY052TStGZGYySFV3SHdleno1QUNCRkJTL0s5dHFMN2tCamtqbVk3Und2MncwdDZlb05zN0xvYVFycU5iWXBjYmt3Z2IrZFFTbkdWYzhrUCtvYWNYdE9TeUtRMW01L2pTVVRLMnBxbkI2WWNrWThkSXNCOHV6SmUzMXF1Tk1HOC82OUZrK1VEN3djNytFM1pIZyt6OGdXYThBdURxVXp4MktOazVxUVVHWkhpRUhCakIzc1FNd0VLZWttbE04ZjZLaXNHSUJRTmJ6NUJ4b3paTUpia3JERmQwVzJWUE00UkJ5YmhUZ0tFN0kzTTZhbGI4dkkrUnhyaWxhMGpUZHNlNldqOWFnc3JHRzlIMkNkQW1SeG8wZlhlejUzZzhMaVlzWGU5eFQwQ0s0L0xiRzFyM0RhOVRlYXpwVmpyMTQxWlY0NUtRZWUiLCJtYWMiOiJkZmMzN2NhMjYwMWRiNWVkYTc1NGFkODU0OTNhZmJhM2QxM2ExOWI1M2I0N2RmMWYyNGY4MTVlNTA4ODE2NjNjIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9WeTNmV0RHdW4wNWZTTHQ3b3p2VEE9PSIsInZhbHVlIjoiMU9KbWtMbWVkcDRybks0TG1qNEUwTnl1U0pSdEZIakFnTTVTM0pFcDhtcTBNSlprd2JKQWROT3NIaTB4bHJ2THpyUk5YRkxMK2dkMGJjeDQ4ZVVBZjRTYU1MbVhxU1VqcVkwUWkrNEgwUXdJeG1QYWtaMHpWRlc2bDlkSC9hS0l4U090VzFWbHBzMTZycWRlVEtxdHFjM0lGLzdKanNQM1hWWmNMQXdLM1VrblAxRGFPV04vMkF3SnB6Umg0Y2EvYjMybkxOZDJXSUFXWjJiY2I4U096NWV0NkVVc25haHZoakc4UjB1cVc5dXRzMDZFOHJVa0x1TUlSayt6UldWcmxhN1ZTdzhOdlN4NDY3cko2SmhTeVUzdlBiMTdsOHJHTlp0RnhIcmNYV1VSK2V4cE5HSFEwUHk3eTlXVXU2Zm53dlhkQUJQd2lIVWRCbTJ0d3RsZlkycjhMZXFHakhqL1p0dGNUWmhmbnh0YzRQTEwrRVFyK09ERmNMTFRtZzNBckxERWxlRTJxRU5xSFcwRU1rZWUyWDlHVjdNVXkvdlRYaWY3YmZHNkg3enRHRUM0OXl0K3VqNmpudWdWMTNsV0wxTWFTMGNaN0NxYW1iQWpYcDVCdHczQVVlTFJPTnpSNGd5TXBQQ0k0bFQ0djhTbnRqeStMR0NhamU0Z0ZvYlEiLCJtYWMiOiIwNjhmZDY3YjMxNzgzMzlkNjM4ODQ4YjRiMGIyNzNjN2M5MmY0ZTlkNjZiZDQ5NzA1NGY3NDgyYjc1ZGZjYmIyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im04d1J5WUFUZFRuT3JuS2NzSitxYVE9PSIsInZhbHVlIjoiZWdTVy92MVVyWS9qR05Wc3M1N3oxSHhmcmpOMTVacTJJR0Nxa3VsTjg1MllHbEhsU1laR3hIblBZd3BSUVlyZUFEbXBMVWhRRUFKZFpjaS9CMk1YU0dZK1VWL3Q0QzZydXNjZjdjZGpaVEdzaDg0NS9Sa0g1V1JJdVhTaGNNN1VEbE5PWmp6ZEd5RUxFc1RXeVBiSUluVTJBOFU2NHkwQ0FHRmNLUDJKazhjczVVY3hJYWtHN24xcDUwY052TStGZGYySFV3SHdleno1QUNCRkJTL0s5dHFMN2tCamtqbVk3Und2MncwdDZlb05zN0xvYVFycU5iWXBjYmt3Z2IrZFFTbkdWYzhrUCtvYWNYdE9TeUtRMW01L2pTVVRLMnBxbkI2WWNrWThkSXNCOHV6SmUzMXF1Tk1HOC82OUZrK1VEN3djNytFM1pIZyt6OGdXYThBdURxVXp4MktOazVxUVVHWkhpRUhCakIzc1FNd0VLZWttbE04ZjZLaXNHSUJRTmJ6NUJ4b3paTUpia3JERmQwVzJWUE00UkJ5YmhUZ0tFN0kzTTZhbGI4dkkrUnhyaWxhMGpUZHNlNldqOWFnc3JHRzlIMkNkQW1SeG8wZlhlejUzZzhMaVlzWGU5eFQwQ0s0L0xiRzFyM0RhOVRlYXpwVmpyMTQxWlY0NUtRZWUiLCJtYWMiOiJkZmMzN2NhMjYwMWRiNWVkYTc1NGFkODU0OTNhZmJhM2QxM2ExOWI1M2I0N2RmMWYyNGY4MTVlNTA4ODE2NjNjIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}