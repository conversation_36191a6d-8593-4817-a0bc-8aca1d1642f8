<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('معالجة النقد للفروع')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('الرئيسية')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('إدارة عمليات الفروع')); ?></li>
    <li class="breadcrumb-item"><?php echo e(__('معالجة النقد للفروع')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('css-page'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/datatable/buttons.dataTables.min.css')); ?>">
    <style>
        /* General Styles */
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 15px 30px rgba(0,0,0,0.12);
        }

        .card-header {
            background: linear-gradient(135deg, #6259ca, #8567f7);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 15px 20px;
        }

        .card-body {
            padding: 25px;
        }

        /* Form Controls */
        .form-control, .form-select {
            border-radius: 10px;
            border: 1px solid #e1e5ef;
            padding: 10px 15px;
            transition: all 0.3s;
            box-shadow: none;
        }

        .form-control:focus, .form-select:focus {
            border-color: #6259ca;
            box-shadow: 0 0 0 0.2rem rgba(98, 89, 202, 0.25);
        }

        label {
            font-weight: 600;
            color: #4f4f4f;
            margin-bottom: 8px;
        }

        /* Buttons */
        .btn {
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s;
        }

        .btn-primary {
            background: linear-gradient(135deg, #6259ca, #8567f7);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5349b5, #7456e6);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(98, 89, 202, 0.4);
        }

        .btn-secondary {
            background: #f0f2f5;
            color: #4f4f4f;
            border: none;
        }

        .btn-secondary:hover {
            background: #e1e5ef;
            color: #333;
            transform: translateY(-2px);
        }

        /* Table Styles */
        .table {
            border-collapse: separate;
            border-spacing: 0 8px;
            margin-top: 15px;
        }

        .table thead th {
            border-bottom: none;
            background-color: #f8f9fa;
            color: #4f4f4f;
            font-weight: 600;
            padding: 15px;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 1px;
        }

        .table tbody tr {
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-radius: 10px;
            transition: all 0.3s;
        }

        .table tbody tr:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .table tbody td {
            padding: 15px;
            vertical-align: middle;
            border: none;
            background-color: white;
        }

        .table tbody tr td:first-child {
            border-top-left-radius: 10px;
            border-bottom-left-radius: 10px;
        }

        .table tbody tr td:last-child {
            border-top-right-radius: 10px;
            border-bottom-right-radius: 10px;
        }

        /* Status Badges */
        .status-badge {
            padding: 8px 15px;
            border-radius: 50px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            text-align: center;
            min-width: 100px;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
        }

        .status-open {
            background: linear-gradient(135deg, #28a745, #5fd778);
            color: white;
        }

        .status-closed {
            background: linear-gradient(135deg, #dc3545, #ff6b7d);
            color: white;
        }

        /* Action Buttons */
        .btn-sm {
            padding: 8px 12px;
            border-radius: 8px;
        }

        /* Filter Section */
        .filter-section {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        }

        /* Pagination */
        .pagination {
            justify-content: center;
            margin-top: 30px;
        }

        .page-item.active .page-link {
            background-color: #6259ca;
            border-color: #6259ca;
        }

        .page-link {
            color: #6259ca;
            border-radius: 5px;
            margin: 0 3px;
        }

        /* Editable Cell */
        .editable-cell {
            cursor: pointer;
            position: relative;
            transition: all 0.3s;
            border-bottom: 1px dashed #e1e5ef;
        }

        .editable-cell:hover {
            background-color: #f8f9fa;
        }

        .editable-cell:hover::after {
            content: "\f044"; /* FontAwesome edit icon */
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            position: absolute;
            right: 10px;
            color: #6259ca;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .card-body {
                padding: 15px;
            }

            .table thead {
                display: none;
            }

            .table tbody tr {
                display: block;
                margin-bottom: 15px;
                border-radius: 10px;
                overflow: hidden;
            }

            .table tbody td {
                display: block;
                text-align: right;
                padding: 10px 15px;
                position: relative;
                border-bottom: 1px solid #f0f2f5;
            }

            .table tbody td:before {
                content: attr(data-label);
                float: left;
                font-weight: bold;
                text-transform: uppercase;
                font-size: 12px;
            }

            .table tbody td:last-child {
                border-bottom: none;
            }
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-money-bill-wave me-2"></i><?php echo e(__('معالجة النقد للفروع')); ?></h5>
                        <div class="card-actions">
                            <button class="btn btn-sm btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
                                <i class="fas fa-filter me-1"></i> <?php echo e(__('تصفية')); ?>

                            </button>
                        </div>
                    </div>
                </div>

                <div class="collapse show" id="filterCollapse">
                    <div class="card-body filter-section">
                        <form action="<?php echo e(route('branch.cash.management')); ?>" method="GET" class="row align-items-end">
                            <div class="col-md-3 form-group mb-3">
                                <label for="status">
                                    <i class="fas fa-tag me-1"></i> <?php echo e(__('الحالة')); ?>

                                </label>
                                <select name="status" id="status" class="form-control form-select">
                                    <option value="all" <?php echo e($status == 'all' ? 'selected' : ''); ?>><?php echo e(__('الكل')); ?></option>
                                    <option value="open" <?php echo e($status == 'open' ? 'selected' : ''); ?>><?php echo e(__('مفتوح')); ?></option>
                                    <option value="closed" <?php echo e($status == 'closed' ? 'selected' : ''); ?>><?php echo e(__('مغلق')); ?></option>
                                </select>
                            </div>
                            <div class="col-md-3 form-group mb-3">
                                <label for="warehouse_id">
                                    <i class="fas fa-building me-1"></i> <?php echo e(__('الفرع')); ?>

                                </label>
                                <select name="warehouse_id" id="warehouse_id" class="form-control form-select">
                                    <option value=""><?php echo e(__('جميع الفروع')); ?></option>
                                    <?php $__currentLoopData = $warehouses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $warehouse): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($warehouse->id); ?>" <?php echo e($warehouse_id == $warehouse->id ? 'selected' : ''); ?>><?php echo e($warehouse->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-2 form-group mb-3">
                                <label for="start_date">
                                    <i class="fas fa-calendar-alt me-1"></i> <?php echo e(__('تاريخ البدء')); ?>

                                </label>
                                <input type="date" name="start_date" id="start_date" class="form-control" value="<?php echo e($start_date); ?>">
                            </div>
                            <div class="col-md-2 form-group mb-3">
                                <label for="end_date">
                                    <i class="fas fa-calendar-alt me-1"></i> <?php echo e(__('تاريخ الانتهاء')); ?>

                                </label>
                                <input type="date" name="end_date" id="end_date" class="form-control" value="<?php echo e($end_date); ?>">
                            </div>
                            <div class="col-md-2 form-group mb-3 d-flex">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-1"></i> <?php echo e(__('تصفية')); ?>

                                </button>
                                <a href="<?php echo e(route('branch.cash.management')); ?>" class="btn btn-secondary">
                                    <i class="fas fa-redo-alt me-1"></i> <?php echo e(__('إعادة تعيين')); ?>

                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4 col-sm-6 mb-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-0"><?php echo e(__('إجمالي الورديات')); ?></h6>
                                            <h3 class="mb-0 mt-2"><?php echo e($shifts->total()); ?></h3>
                                        </div>
                                        <div>
                                            <i class="fas fa-clock fa-2x opacity-50"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 col-sm-6 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-0"><?php echo e(__('الورديات المفتوحة')); ?></h6>
                                            <h3 class="mb-0 mt-2"><?php echo e($shifts->where('is_closed', false)->count()); ?></h3>
                                        </div>
                                        <div>
                                            <i class="fas fa-door-open fa-2x opacity-50"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 col-sm-6 mb-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-0"><?php echo e(__('الورديات المغلقة')); ?></h6>
                                            <h3 class="mb-0 mt-2"><?php echo e($shifts->where('is_closed', true)->count()); ?></h3>
                                        </div>
                                        <div>
                                            <i class="fas fa-door-closed fa-2x opacity-50"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-0"><?php echo e(__('إجمالي النقد الحالي')); ?></h6>
                                            <h3 class="mb-0 mt-2">
                                                <?php echo e(number_format($shifts->sum(function($shift) {
                                                    return $shift->financialRecord ? $shift->financialRecord->current_cash : 0;
                                                }), 2)); ?>

                                            </h3>
                                        </div>
                                        <div>
                                            <i class="fas fa-money-bill-wave fa-2x opacity-50"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="card bg-info text-white">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-0"><?php echo e(__('إجمالي النقد عبر الشبكة')); ?></h6>
                                            <h3 class="mb-0 mt-2">
                                                <?php echo e(number_format($shifts->sum(function($shift) {
                                                    return $shift->financialRecord ? $shift->financialRecord->overnetwork_cash : 0;
                                                }), 2)); ?>

                                            </h3>
                                        </div>
                                        <div>
                                            <i class="fas fa-network-wired fa-2x opacity-50"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-0"><?php echo e(__('إجمالي نقد التوصيل')); ?></h6>
                                            <h3 class="mb-0 mt-2">
                                                <?php echo e(number_format($shifts->sum(function($shift) {
                                                    return $shift->financialRecord ? $shift->financialRecord->delivery_cash : 0;
                                                }), 2)); ?>

                                            </h3>
                                        </div>
                                        <div>
                                            <i class="fas fa-truck fa-2x opacity-50"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="card bg-dark text-white">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-0"><?php echo e(__('إجمالي النقد')); ?> <small class="d-block text-white-50">(<?php echo e(__('النقد الحالي')); ?> + <?php echo e(__('النقد عبر الشبكة')); ?>)</small></h6>
                                            <h3 class="mb-0 mt-2">
                                                <?php echo e(number_format($shifts->sum(function($shift) {
                                                    return $shift->financialRecord ? $shift->financialRecord->total_cash : 0;
                                                }), 2)); ?>

                                            </h3>
                                        </div>
                                        <div>
                                            <i class="fas fa-money-bill-alt fa-2x opacity-50"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table datatable">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('ID')); ?></th>
                                    <th><?php echo e(__('الفرع')); ?></th>
                                    <th><?php echo e(__('الرصيد الافتتاحي')); ?></th>
                                    <th><?php echo e(__('النقد الحالي')); ?></th>
                                    <th><?php echo e(__('النقد عبر الشبكة')); ?></th>
                                    <th><?php echo e(__('نقد التوصيل')); ?></th>
                                    <th><?php echo e(__('إجمالي النقد')); ?> <small class="d-block text-muted">(<?php echo e(__('النقد الحالي')); ?> + <?php echo e(__('النقد عبر الشبكة')); ?>)</small></th>
                                    <th><?php echo e(__('الحالة')); ?></th>
                                    <th><?php echo e(__('تم إنشاؤه بواسطة')); ?></th>
                                    <th><?php echo e(__('تم إنشاؤه في')); ?></th>
                                    <th><?php echo e(__('تم إغلاقه في')); ?></th>
                                    <th><?php echo e(__('الإجراء')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $shifts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $shift): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td data-label="<?php echo e(__('ID')); ?>"><?php echo e($shift->id); ?></td>
                                        <td data-label="<?php echo e(__('الفرع')); ?>"><?php echo e($shift->warehouse_name); ?></td>
                                        <td data-label="<?php echo e(__('الرصيد الافتتاحي')); ?>"><?php echo e($shift->financialRecord ? number_format($shift->financialRecord->opening_balance, 2) : '0.00'); ?></td>
                                        <td data-label="<?php echo e(__('النقد الحالي')); ?>"><?php echo e($shift->financialRecord ? number_format($shift->financialRecord->current_cash, 2) : '0.00'); ?></td>
                                        <td data-label="<?php echo e(__('النقد عبر الشبكة')); ?>"><?php echo e($shift->financialRecord ? number_format($shift->financialRecord->overnetwork_cash, 2) : '0.00'); ?></td>
                                        <td data-label="<?php echo e(__('نقد التوصيل')); ?>"><?php echo e($shift->financialRecord ? number_format($shift->financialRecord->delivery_cash, 2) : '0.00'); ?></td>
                                        <td data-label="<?php echo e(__('إجمالي النقد')); ?>"><?php echo e($shift->financialRecord ? number_format($shift->financialRecord->total_cash, 2) : '0.00'); ?></td>
                                        <td data-label="<?php echo e(__('الحالة')); ?>">
                                            <span class="status-badge <?php echo e($shift->is_closed ? 'status-closed' : 'status-open'); ?>">
                                                <i class="fas <?php echo e($shift->is_closed ? 'fa-lock' : 'fa-unlock'); ?> me-1"></i>
                                                <?php echo e($shift->is_closed ? __('مغلق') : __('مفتوح')); ?>

                                            </span>
                                        </td>
                                        <td data-label="<?php echo e(__('تم إنشاؤه بواسطة')); ?>"><?php echo e($shift->creator ? $shift->creator->name : ''); ?></td>
                                        <td data-label="<?php echo e(__('تم إنشاؤه في')); ?>"><?php echo e(\App\Models\Utility::getDateFormated($shift->created_at)); ?></td>
                                        <td data-label="<?php echo e(__('تم إغلاقه في')); ?>"><?php echo e($shift->closed_at ? \App\Models\Utility::getDateFormated($shift->closed_at) : '-'); ?></td>
                                        <td data-label="<?php echo e(__('الإجراء')); ?>">
                                            <a href="<?php echo e(route('branch.cash.management.show', $shift->id)); ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="<?php echo e(__('عرض التفاصيل')); ?>">
                                                <i class="ti ti-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        <?php echo e($shifts->links()); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
<script>
    $(document).ready(function() {
        $('.datatable').DataTable({
            dom: 'Bfrtip',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ],
            "language": {
                "sEmptyTable": "<?php echo e(__('No data available in table')); ?>",
                "sInfo": "<?php echo e(__('Showing _START_ to _END_ of _TOTAL_ entries')); ?>",
                "sInfoEmpty": "<?php echo e(__('Showing 0 to 0 of 0 entries')); ?>",
                "sInfoFiltered": "<?php echo e(__('(filtered from _MAX_ total entries)')); ?>",
                "sInfoPostFix": "",
                "sInfoThousands": ",",
                "sLengthMenu": "<?php echo e(__('Show _MENU_ entries')); ?>",
                "sLoadingRecords": "<?php echo e(__('Loading...')); ?>",
                "sProcessing": "<?php echo e(__('Processing...')); ?>",
                "sSearch": "<?php echo e(__('Search:')); ?>",
                "sZeroRecords": "<?php echo e(__('No matching records found')); ?>",
                "oPaginate": {
                    "sFirst": "<?php echo e(__('First')); ?>",
                    "sLast": "<?php echo e(__('Last')); ?>",
                    "sNext": "<?php echo e(__('Next')); ?>",
                    "sPrevious": "<?php echo e(__('Previous')); ?>"
                },
            },
            "pageLength": 25,
            "order": [[ 0, "desc" ]]
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\erpq22\resources\views/company_operations/branch_cash_management/index.blade.php ENDPATH**/ ?>