<?php
// This file was auto-generated from sdk-root/src/data/deadline/2023-10-12/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-10-12', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'deadline', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWSDeadlineCloud', 'serviceId' => 'deadline', 'signatureVersion' => 'v4', 'signingName' => 'deadline', 'uid' => 'deadline-2023-10-12', ], 'operations' => [ 'AssociateMemberToFarm' => [ 'name' => 'AssociateMemberToFarm', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2023-10-12/farms/{farmId}/members/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateMemberToFarmRequest', ], 'output' => [ 'shape' => 'AssociateMemberToFarmResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'AssociateMemberToFleet' => [ 'name' => 'AssociateMemberToFleet', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/members/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateMemberToFleetRequest', ], 'output' => [ 'shape' => 'AssociateMemberToFleetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'AssociateMemberToJob' => [ 'name' => 'AssociateMemberToJob', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/members/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateMemberToJobRequest', ], 'output' => [ 'shape' => 'AssociateMemberToJobResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'AssociateMemberToQueue' => [ 'name' => 'AssociateMemberToQueue', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/members/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateMemberToQueueRequest', ], 'output' => [ 'shape' => 'AssociateMemberToQueueResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'AssumeFleetRoleForRead' => [ 'name' => 'AssumeFleetRoleForRead', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/read-roles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssumeFleetRoleForReadRequest', ], 'output' => [ 'shape' => 'AssumeFleetRoleForReadResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'AssumeFleetRoleForWorker' => [ 'name' => 'AssumeFleetRoleForWorker', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers/{workerId}/fleet-roles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssumeFleetRoleForWorkerRequest', ], 'output' => [ 'shape' => 'AssumeFleetRoleForWorkerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'scheduling.', ], ], 'AssumeQueueRoleForRead' => [ 'name' => 'AssumeQueueRoleForRead', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/read-roles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssumeQueueRoleForReadRequest', ], 'output' => [ 'shape' => 'AssumeQueueRoleForReadResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'AssumeQueueRoleForUser' => [ 'name' => 'AssumeQueueRoleForUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/user-roles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssumeQueueRoleForUserRequest', ], 'output' => [ 'shape' => 'AssumeQueueRoleForUserResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'AssumeQueueRoleForWorker' => [ 'name' => 'AssumeQueueRoleForWorker', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers/{workerId}/queue-roles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssumeQueueRoleForWorkerRequest', ], 'output' => [ 'shape' => 'AssumeQueueRoleForWorkerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'scheduling.', ], ], 'BatchGetJobEntity' => [ 'name' => 'BatchGetJobEntity', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers/{workerId}/batchGetJobEntity', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetJobEntityRequest', ], 'output' => [ 'shape' => 'BatchGetJobEntityResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'scheduling.', ], ], 'CopyJobTemplate' => [ 'name' => 'CopyJobTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/template', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CopyJobTemplateRequest', ], 'output' => [ 'shape' => 'CopyJobTemplateResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'CreateBudget' => [ 'name' => 'CreateBudget', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/budgets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateBudgetRequest', ], 'output' => [ 'shape' => 'CreateBudgetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateFarm' => [ 'name' => 'CreateFarm', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateFarmRequest', ], 'output' => [ 'shape' => 'CreateFarmResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateFleet' => [ 'name' => 'CreateFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateFleetRequest', ], 'output' => [ 'shape' => 'CreateFleetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateJob' => [ 'name' => 'CreateJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateJobRequest', ], 'output' => [ 'shape' => 'CreateJobResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateLicenseEndpoint' => [ 'name' => 'CreateLicenseEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/license-endpoints', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateLicenseEndpointRequest', ], 'output' => [ 'shape' => 'CreateLicenseEndpointResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateMonitor' => [ 'name' => 'CreateMonitor', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/monitors', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMonitorRequest', ], 'output' => [ 'shape' => 'CreateMonitorResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateQueue' => [ 'name' => 'CreateQueue', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/queues', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateQueueRequest', ], 'output' => [ 'shape' => 'CreateQueueResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateQueueEnvironment' => [ 'name' => 'CreateQueueEnvironment', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/environments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateQueueEnvironmentRequest', ], 'output' => [ 'shape' => 'CreateQueueEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateQueueFleetAssociation' => [ 'name' => 'CreateQueueFleetAssociation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2023-10-12/farms/{farmId}/queue-fleet-associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateQueueFleetAssociationRequest', ], 'output' => [ 'shape' => 'CreateQueueFleetAssociationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateStorageProfile' => [ 'name' => 'CreateStorageProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/storage-profiles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateStorageProfileRequest', ], 'output' => [ 'shape' => 'CreateStorageProfileResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateWorker' => [ 'name' => 'CreateWorker', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateWorkerRequest', ], 'output' => [ 'shape' => 'CreateWorkerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'scheduling.', ], 'idempotent' => true, ], 'DeleteBudget' => [ 'name' => 'DeleteBudget', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/budgets/{budgetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteBudgetRequest', ], 'output' => [ 'shape' => 'DeleteBudgetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteFarm' => [ 'name' => 'DeleteFarm', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteFarmRequest', ], 'output' => [ 'shape' => 'DeleteFarmResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteFleet' => [ 'name' => 'DeleteFleet', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteFleetRequest', ], 'output' => [ 'shape' => 'DeleteFleetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteLicenseEndpoint' => [ 'name' => 'DeleteLicenseEndpoint', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/license-endpoints/{licenseEndpointId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteLicenseEndpointRequest', ], 'output' => [ 'shape' => 'DeleteLicenseEndpointResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteMeteredProduct' => [ 'name' => 'DeleteMeteredProduct', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/license-endpoints/{licenseEndpointId}/metered-products/{productId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMeteredProductRequest', ], 'output' => [ 'shape' => 'DeleteMeteredProductResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteMonitor' => [ 'name' => 'DeleteMonitor', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/monitors/{monitorId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMonitorRequest', ], 'output' => [ 'shape' => 'DeleteMonitorResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteQueue' => [ 'name' => 'DeleteQueue', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteQueueRequest', ], 'output' => [ 'shape' => 'DeleteQueueResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteQueueEnvironment' => [ 'name' => 'DeleteQueueEnvironment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/environments/{queueEnvironmentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteQueueEnvironmentRequest', ], 'output' => [ 'shape' => 'DeleteQueueEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteQueueFleetAssociation' => [ 'name' => 'DeleteQueueFleetAssociation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/queue-fleet-associations/{queueId}/{fleetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteQueueFleetAssociationRequest', ], 'output' => [ 'shape' => 'DeleteQueueFleetAssociationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteStorageProfile' => [ 'name' => 'DeleteStorageProfile', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/storage-profiles/{storageProfileId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteStorageProfileRequest', ], 'output' => [ 'shape' => 'DeleteStorageProfileResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteWorker' => [ 'name' => 'DeleteWorker', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers/{workerId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteWorkerRequest', ], 'output' => [ 'shape' => 'DeleteWorkerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DisassociateMemberFromFarm' => [ 'name' => 'DisassociateMemberFromFarm', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/members/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateMemberFromFarmRequest', ], 'output' => [ 'shape' => 'DisassociateMemberFromFarmResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DisassociateMemberFromFleet' => [ 'name' => 'DisassociateMemberFromFleet', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/members/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateMemberFromFleetRequest', ], 'output' => [ 'shape' => 'DisassociateMemberFromFleetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DisassociateMemberFromJob' => [ 'name' => 'DisassociateMemberFromJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/members/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateMemberFromJobRequest', ], 'output' => [ 'shape' => 'DisassociateMemberFromJobResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DisassociateMemberFromQueue' => [ 'name' => 'DisassociateMemberFromQueue', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/members/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateMemberFromQueueRequest', ], 'output' => [ 'shape' => 'DisassociateMemberFromQueueResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'GetBudget' => [ 'name' => 'GetBudget', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/budgets/{budgetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBudgetRequest', ], 'output' => [ 'shape' => 'GetBudgetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetFarm' => [ 'name' => 'GetFarm', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFarmRequest', ], 'output' => [ 'shape' => 'GetFarmResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetFleet' => [ 'name' => 'GetFleet', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFleetRequest', ], 'output' => [ 'shape' => 'GetFleetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetJob' => [ 'name' => 'GetJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetJobRequest', ], 'output' => [ 'shape' => 'GetJobResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetLicenseEndpoint' => [ 'name' => 'GetLicenseEndpoint', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/license-endpoints/{licenseEndpointId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLicenseEndpointRequest', ], 'output' => [ 'shape' => 'GetLicenseEndpointResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetMonitor' => [ 'name' => 'GetMonitor', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/monitors/{monitorId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMonitorRequest', ], 'output' => [ 'shape' => 'GetMonitorResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetQueue' => [ 'name' => 'GetQueue', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQueueRequest', ], 'output' => [ 'shape' => 'GetQueueResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetQueueEnvironment' => [ 'name' => 'GetQueueEnvironment', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/environments/{queueEnvironmentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQueueEnvironmentRequest', ], 'output' => [ 'shape' => 'GetQueueEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetQueueFleetAssociation' => [ 'name' => 'GetQueueFleetAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queue-fleet-associations/{queueId}/{fleetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQueueFleetAssociationRequest', ], 'output' => [ 'shape' => 'GetQueueFleetAssociationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetSession' => [ 'name' => 'GetSession', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/sessions/{sessionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSessionRequest', ], 'output' => [ 'shape' => 'GetSessionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetSessionAction' => [ 'name' => 'GetSessionAction', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/session-actions/{sessionActionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSessionActionRequest', ], 'output' => [ 'shape' => 'GetSessionActionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetSessionsStatisticsAggregation' => [ 'name' => 'GetSessionsStatisticsAggregation', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/sessions-statistics-aggregation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSessionsStatisticsAggregationRequest', ], 'output' => [ 'shape' => 'GetSessionsStatisticsAggregationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetStep' => [ 'name' => 'GetStep', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/steps/{stepId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStepRequest', ], 'output' => [ 'shape' => 'GetStepResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetStorageProfile' => [ 'name' => 'GetStorageProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/storage-profiles/{storageProfileId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStorageProfileRequest', ], 'output' => [ 'shape' => 'GetStorageProfileResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetStorageProfileForQueue' => [ 'name' => 'GetStorageProfileForQueue', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/storage-profiles/{storageProfileId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStorageProfileForQueueRequest', ], 'output' => [ 'shape' => 'GetStorageProfileForQueueResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetTask' => [ 'name' => 'GetTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/steps/{stepId}/tasks/{taskId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTaskRequest', ], 'output' => [ 'shape' => 'GetTaskResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetWorker' => [ 'name' => 'GetWorker', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers/{workerId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWorkerRequest', ], 'output' => [ 'shape' => 'GetWorkerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListAvailableMeteredProducts' => [ 'name' => 'ListAvailableMeteredProducts', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/metered-products', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAvailableMeteredProductsRequest', ], 'output' => [ 'shape' => 'ListAvailableMeteredProductsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListBudgets' => [ 'name' => 'ListBudgets', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/budgets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBudgetsRequest', ], 'output' => [ 'shape' => 'ListBudgetsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListFarmMembers' => [ 'name' => 'ListFarmMembers', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/members', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFarmMembersRequest', ], 'output' => [ 'shape' => 'ListFarmMembersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListFarms' => [ 'name' => 'ListFarms', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFarmsRequest', ], 'output' => [ 'shape' => 'ListFarmsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListFleetMembers' => [ 'name' => 'ListFleetMembers', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/members', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFleetMembersRequest', ], 'output' => [ 'shape' => 'ListFleetMembersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListFleets' => [ 'name' => 'ListFleets', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFleetsRequest', ], 'output' => [ 'shape' => 'ListFleetsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListJobMembers' => [ 'name' => 'ListJobMembers', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/members', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListJobMembersRequest', ], 'output' => [ 'shape' => 'ListJobMembersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListJobs' => [ 'name' => 'ListJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListJobsRequest', ], 'output' => [ 'shape' => 'ListJobsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListLicenseEndpoints' => [ 'name' => 'ListLicenseEndpoints', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/license-endpoints', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListLicenseEndpointsRequest', ], 'output' => [ 'shape' => 'ListLicenseEndpointsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListMeteredProducts' => [ 'name' => 'ListMeteredProducts', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/license-endpoints/{licenseEndpointId}/metered-products', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMeteredProductsRequest', ], 'output' => [ 'shape' => 'ListMeteredProductsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListMonitors' => [ 'name' => 'ListMonitors', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/monitors', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMonitorsRequest', ], 'output' => [ 'shape' => 'ListMonitorsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListQueueEnvironments' => [ 'name' => 'ListQueueEnvironments', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/environments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListQueueEnvironmentsRequest', ], 'output' => [ 'shape' => 'ListQueueEnvironmentsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListQueueFleetAssociations' => [ 'name' => 'ListQueueFleetAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queue-fleet-associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListQueueFleetAssociationsRequest', ], 'output' => [ 'shape' => 'ListQueueFleetAssociationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListQueueMembers' => [ 'name' => 'ListQueueMembers', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/members', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListQueueMembersRequest', ], 'output' => [ 'shape' => 'ListQueueMembersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListQueues' => [ 'name' => 'ListQueues', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListQueuesRequest', ], 'output' => [ 'shape' => 'ListQueuesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListSessionActions' => [ 'name' => 'ListSessionActions', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/session-actions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSessionActionsRequest', ], 'output' => [ 'shape' => 'ListSessionActionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListSessions' => [ 'name' => 'ListSessions', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/sessions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSessionsRequest', ], 'output' => [ 'shape' => 'ListSessionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListSessionsForWorker' => [ 'name' => 'ListSessionsForWorker', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers/{workerId}/sessions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSessionsForWorkerRequest', ], 'output' => [ 'shape' => 'ListSessionsForWorkerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListStepConsumers' => [ 'name' => 'ListStepConsumers', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/steps/{stepId}/consumers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStepConsumersRequest', ], 'output' => [ 'shape' => 'ListStepConsumersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListStepDependencies' => [ 'name' => 'ListStepDependencies', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/steps/{stepId}/dependencies', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStepDependenciesRequest', ], 'output' => [ 'shape' => 'ListStepDependenciesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListSteps' => [ 'name' => 'ListSteps', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/steps', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStepsRequest', ], 'output' => [ 'shape' => 'ListStepsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListStorageProfiles' => [ 'name' => 'ListStorageProfiles', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/storage-profiles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStorageProfilesRequest', ], 'output' => [ 'shape' => 'ListStorageProfilesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListStorageProfilesForQueue' => [ 'name' => 'ListStorageProfilesForQueue', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/storage-profiles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStorageProfilesForQueueRequest', ], 'output' => [ 'shape' => 'ListStorageProfilesForQueueResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListTasks' => [ 'name' => 'ListTasks', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/steps/{stepId}/tasks', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTasksRequest', ], 'output' => [ 'shape' => 'ListTasksResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListWorkers' => [ 'name' => 'ListWorkers', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListWorkersRequest', ], 'output' => [ 'shape' => 'ListWorkersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'PutMeteredProduct' => [ 'name' => 'PutMeteredProduct', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2023-10-12/license-endpoints/{licenseEndpointId}/metered-products/{productId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutMeteredProductRequest', ], 'output' => [ 'shape' => 'PutMeteredProductResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'SearchJobs' => [ 'name' => 'SearchJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/search/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchJobsRequest', ], 'output' => [ 'shape' => 'SearchJobsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'SearchSteps' => [ 'name' => 'SearchSteps', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/search/steps', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchStepsRequest', ], 'output' => [ 'shape' => 'SearchStepsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'SearchTasks' => [ 'name' => 'SearchTasks', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/search/tasks', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchTasksRequest', ], 'output' => [ 'shape' => 'SearchTasksResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'SearchWorkers' => [ 'name' => 'SearchWorkers', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/search/workers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchWorkersRequest', ], 'output' => [ 'shape' => 'SearchWorkersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'StartSessionsStatisticsAggregation' => [ 'name' => 'StartSessionsStatisticsAggregation', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/sessions-statistics-aggregation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartSessionsStatisticsAggregationRequest', ], 'output' => [ 'shape' => 'StartSessionsStatisticsAggregationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateBudget' => [ 'name' => 'UpdateBudget', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/budgets/{budgetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateBudgetRequest', ], 'output' => [ 'shape' => 'UpdateBudgetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateFarm' => [ 'name' => 'UpdateFarm', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFarmRequest', ], 'output' => [ 'shape' => 'UpdateFarmResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateFleet' => [ 'name' => 'UpdateFleet', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFleetRequest', ], 'output' => [ 'shape' => 'UpdateFleetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateJob' => [ 'name' => 'UpdateJob', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateJobRequest', ], 'output' => [ 'shape' => 'UpdateJobResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateMonitor' => [ 'name' => 'UpdateMonitor', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/monitors/{monitorId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMonitorRequest', ], 'output' => [ 'shape' => 'UpdateMonitorResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateQueue' => [ 'name' => 'UpdateQueue', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateQueueRequest', ], 'output' => [ 'shape' => 'UpdateQueueResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateQueueEnvironment' => [ 'name' => 'UpdateQueueEnvironment', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/environments/{queueEnvironmentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateQueueEnvironmentRequest', ], 'output' => [ 'shape' => 'UpdateQueueEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'UpdateQueueFleetAssociation' => [ 'name' => 'UpdateQueueFleetAssociation', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/queue-fleet-associations/{queueId}/{fleetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateQueueFleetAssociationRequest', ], 'output' => [ 'shape' => 'UpdateQueueFleetAssociationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateSession' => [ 'name' => 'UpdateSession', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/sessions/{sessionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSessionRequest', ], 'output' => [ 'shape' => 'UpdateSessionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateStep' => [ 'name' => 'UpdateStep', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/steps/{stepId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateStepRequest', ], 'output' => [ 'shape' => 'UpdateStepResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateStorageProfile' => [ 'name' => 'UpdateStorageProfile', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/storage-profiles/{storageProfileId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateStorageProfileRequest', ], 'output' => [ 'shape' => 'UpdateStorageProfileResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateTask' => [ 'name' => 'UpdateTask', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/steps/{stepId}/tasks/{taskId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTaskRequest', ], 'output' => [ 'shape' => 'UpdateTaskResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateWorker' => [ 'name' => 'UpdateWorker', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers/{workerId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateWorkerRequest', ], 'output' => [ 'shape' => 'UpdateWorkerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'scheduling.', ], 'idempotent' => true, ], 'UpdateWorkerSchedule' => [ 'name' => 'UpdateWorkerSchedule', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers/{workerId}/schedule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateWorkerScheduleRequest', ], 'output' => [ 'shape' => 'UpdateWorkerScheduleResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'scheduling.', ], 'idempotent' => true, ], ], 'shapes' => [ 'AcceleratorCountRange' => [ 'type' => 'structure', 'required' => [ 'min', ], 'members' => [ 'min' => [ 'shape' => 'MinZeroMaxInteger', ], 'max' => [ 'shape' => 'MinZeroMaxInteger', ], ], ], 'AcceleratorTotalMemoryMiBRange' => [ 'type' => 'structure', 'required' => [ 'min', ], 'members' => [ 'min' => [ 'shape' => 'MinZeroMaxInteger', ], 'max' => [ 'shape' => 'MinZeroMaxInteger', ], ], ], 'AcceleratorType' => [ 'type' => 'string', 'enum' => [ 'gpu', ], ], 'AcceleratorTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AcceleratorType', ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'context' => [ 'shape' => 'ExceptionContext', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccessKeyId' => [ 'type' => 'string', 'sensitive' => true, ], 'AggregationId' => [ 'type' => 'string', 'pattern' => '[0-9a-f]{32}', ], 'AllowedStorageProfileIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageProfileId', ], 'max' => 20, 'min' => 0, ], 'AmountCapabilityName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '([a-zA-Z][a-zA-Z0-9]{0,63}:)?amount(\\.[a-zA-Z][a-zA-Z0-9]{0,63})+', ], 'AssignedEnvironmentEnterSessionActionDefinition' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', ], ], ], 'AssignedEnvironmentExitSessionActionDefinition' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', ], ], ], 'AssignedSession' => [ 'type' => 'structure', 'required' => [ 'queueId', 'jobId', 'sessionActions', 'logConfiguration', ], 'members' => [ 'queueId' => [ 'shape' => 'QueueId', ], 'jobId' => [ 'shape' => 'JobId', ], 'sessionActions' => [ 'shape' => 'AssignedSessionActions', ], 'logConfiguration' => [ 'shape' => 'LogConfiguration', ], ], ], 'AssignedSessionAction' => [ 'type' => 'structure', 'required' => [ 'sessionActionId', 'definition', ], 'members' => [ 'sessionActionId' => [ 'shape' => 'SessionActionId', ], 'definition' => [ 'shape' => 'AssignedSessionActionDefinition', ], ], ], 'AssignedSessionActionDefinition' => [ 'type' => 'structure', 'members' => [ 'envEnter' => [ 'shape' => 'AssignedEnvironmentEnterSessionActionDefinition', ], 'envExit' => [ 'shape' => 'AssignedEnvironmentExitSessionActionDefinition', ], 'taskRun' => [ 'shape' => 'AssignedTaskRunSessionActionDefinition', ], 'syncInputJobAttachments' => [ 'shape' => 'AssignedSyncInputJobAttachmentsSessionActionDefinition', ], ], 'union' => true, ], 'AssignedSessionActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssignedSessionAction', ], ], 'AssignedSessions' => [ 'type' => 'map', 'key' => [ 'shape' => 'SessionId', ], 'value' => [ 'shape' => 'AssignedSession', ], ], 'AssignedSyncInputJobAttachmentsSessionActionDefinition' => [ 'type' => 'structure', 'members' => [ 'stepId' => [ 'shape' => 'StepId', ], ], ], 'AssignedTaskRunSessionActionDefinition' => [ 'type' => 'structure', 'required' => [ 'taskId', 'stepId', 'parameters', ], 'members' => [ 'taskId' => [ 'shape' => 'TaskId', ], 'stepId' => [ 'shape' => 'StepId', ], 'parameters' => [ 'shape' => 'TaskParameters', ], ], ], 'AssociateMemberToFarmRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'principalId', 'principalType', 'identityStoreId', 'membershipLevel', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'uri', 'locationName' => 'principalId', ], 'principalType' => [ 'shape' => 'PrincipalType', ], 'identityStoreId' => [ 'shape' => 'IdentityStoreId', ], 'membershipLevel' => [ 'shape' => 'MembershipLevel', ], ], ], 'AssociateMemberToFarmResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateMemberToFleetRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'principalId', 'principalType', 'identityStoreId', 'membershipLevel', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'uri', 'locationName' => 'principalId', ], 'principalType' => [ 'shape' => 'PrincipalType', ], 'identityStoreId' => [ 'shape' => 'IdentityStoreId', ], 'membershipLevel' => [ 'shape' => 'MembershipLevel', ], ], ], 'AssociateMemberToFleetResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateMemberToJobRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'jobId', 'principalId', 'principalType', 'identityStoreId', 'membershipLevel', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'uri', 'locationName' => 'principalId', ], 'principalType' => [ 'shape' => 'PrincipalType', ], 'identityStoreId' => [ 'shape' => 'IdentityStoreId', ], 'membershipLevel' => [ 'shape' => 'MembershipLevel', ], ], ], 'AssociateMemberToJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateMemberToQueueRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'principalId', 'principalType', 'identityStoreId', 'membershipLevel', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'uri', 'locationName' => 'principalId', ], 'principalType' => [ 'shape' => 'PrincipalType', ], 'identityStoreId' => [ 'shape' => 'IdentityStoreId', ], 'membershipLevel' => [ 'shape' => 'MembershipLevel', ], ], ], 'AssociateMemberToQueueResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssumeFleetRoleForReadRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], ], ], 'AssumeFleetRoleForReadResponse' => [ 'type' => 'structure', 'required' => [ 'credentials', ], 'members' => [ 'credentials' => [ 'shape' => 'AwsCredentials', ], ], 'sensitive' => true, ], 'AssumeFleetRoleForWorkerRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'workerId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'workerId' => [ 'shape' => 'WorkerId', 'location' => 'uri', 'locationName' => 'workerId', ], ], ], 'AssumeFleetRoleForWorkerResponse' => [ 'type' => 'structure', 'required' => [ 'credentials', ], 'members' => [ 'credentials' => [ 'shape' => 'AwsCredentials', ], ], 'sensitive' => true, ], 'AssumeQueueRoleForReadRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'AssumeQueueRoleForReadResponse' => [ 'type' => 'structure', 'required' => [ 'credentials', ], 'members' => [ 'credentials' => [ 'shape' => 'AwsCredentials', ], ], 'sensitive' => true, ], 'AssumeQueueRoleForUserRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'AssumeQueueRoleForUserResponse' => [ 'type' => 'structure', 'required' => [ 'credentials', ], 'members' => [ 'credentials' => [ 'shape' => 'AwsCredentials', ], ], 'sensitive' => true, ], 'AssumeQueueRoleForWorkerRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'workerId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'workerId' => [ 'shape' => 'WorkerId', 'location' => 'uri', 'locationName' => 'workerId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'querystring', 'locationName' => 'queueId', ], ], ], 'AssumeQueueRoleForWorkerResponse' => [ 'type' => 'structure', 'members' => [ 'credentials' => [ 'shape' => 'AwsCredentials', ], ], 'sensitive' => true, ], 'Attachments' => [ 'type' => 'structure', 'required' => [ 'manifests', ], 'members' => [ 'manifests' => [ 'shape' => 'ManifestPropertiesList', ], 'fileSystem' => [ 'shape' => 'JobAttachmentsFileSystem', ], ], ], 'AttributeCapabilityName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '([a-zA-Z][a-zA-Z0-9]{0,63}:)?attr(\\.[a-zA-Z][a-zA-Z0-9]{0,63})+', ], 'AttributeCapabilityValue' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z_]([a-zA-Z0-9_\\-]{0,99})', ], 'AttributeCapabilityValuesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeCapabilityValue', ], 'max' => 10, 'min' => 1, ], 'AutoScalingMode' => [ 'type' => 'string', 'enum' => [ 'NO_SCALING', 'EVENT_BASED_AUTO_SCALING', ], ], 'AutoScalingStatus' => [ 'type' => 'string', 'enum' => [ 'GROWING', 'STEADY', 'SHRINKING', ], ], 'AwsCredentials' => [ 'type' => 'structure', 'required' => [ 'accessKeyId', 'secretAccessKey', 'sessionToken', 'expiration', ], 'members' => [ 'accessKeyId' => [ 'shape' => 'AccessKeyId', ], 'secretAccessKey' => [ 'shape' => 'SecretAccessKey', ], 'sessionToken' => [ 'shape' => 'SessionToken', ], 'expiration' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], 'sensitive' => true, ], 'BatchGetJobEntityErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'GetJobEntityError', ], ], 'BatchGetJobEntityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobEntity', ], 'max' => 25, 'min' => 0, ], 'BatchGetJobEntityRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'workerId', 'identifiers', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'workerId' => [ 'shape' => 'WorkerId', 'location' => 'uri', 'locationName' => 'workerId', ], 'identifiers' => [ 'shape' => 'JobEntityIdentifiers', ], ], ], 'BatchGetJobEntityResponse' => [ 'type' => 'structure', 'required' => [ 'entities', 'errors', ], 'members' => [ 'entities' => [ 'shape' => 'BatchGetJobEntityList', ], 'errors' => [ 'shape' => 'BatchGetJobEntityErrors', ], ], ], 'BoundedString' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'BudgetActionToAdd' => [ 'type' => 'structure', 'required' => [ 'type', 'thresholdPercentage', ], 'members' => [ 'type' => [ 'shape' => 'BudgetActionType', ], 'thresholdPercentage' => [ 'shape' => 'ThresholdPercentage', ], 'description' => [ 'shape' => 'Description', ], ], ], 'BudgetActionToRemove' => [ 'type' => 'structure', 'required' => [ 'type', 'thresholdPercentage', ], 'members' => [ 'type' => [ 'shape' => 'BudgetActionType', ], 'thresholdPercentage' => [ 'shape' => 'ThresholdPercentage', ], ], ], 'BudgetActionType' => [ 'type' => 'string', 'enum' => [ 'STOP_SCHEDULING_AND_COMPLETE_TASKS', 'STOP_SCHEDULING_AND_CANCEL_TASKS', ], ], 'BudgetActionsToAdd' => [ 'type' => 'list', 'member' => [ 'shape' => 'BudgetActionToAdd', ], 'max' => 10, 'min' => 0, ], 'BudgetActionsToRemove' => [ 'type' => 'list', 'member' => [ 'shape' => 'BudgetActionToRemove', ], 'max' => 10, 'min' => 0, ], 'BudgetId' => [ 'type' => 'string', 'pattern' => 'budget-[0-9a-f]{32}', ], 'BudgetSchedule' => [ 'type' => 'structure', 'members' => [ 'fixed' => [ 'shape' => 'FixedBudgetSchedule', ], ], 'union' => true, ], 'BudgetStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'BudgetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BudgetSummary', ], ], 'BudgetSummary' => [ 'type' => 'structure', 'required' => [ 'budgetId', 'usageTrackingResource', 'status', 'displayName', 'approximateDollarLimit', 'usages', 'createdBy', 'createdAt', ], 'members' => [ 'budgetId' => [ 'shape' => 'BudgetId', ], 'usageTrackingResource' => [ 'shape' => 'UsageTrackingResource', ], 'status' => [ 'shape' => 'BudgetStatus', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'Description', 'deprecated' => true, 'deprecatedMessage' => 'ListBudgets no longer supports description. Use GetBudget if description is needed.', ], 'approximateDollarLimit' => [ 'shape' => 'ConsumedUsageLimit', ], 'usages' => [ 'shape' => 'ConsumedUsages', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], ], ], 'CancelSessionActions' => [ 'type' => 'map', 'key' => [ 'shape' => 'SessionId', ], 'value' => [ 'shape' => 'SessionActionIdList', ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'CombinationExpression' => [ 'type' => 'string', 'max' => 1280, 'min' => 1, ], 'ComparisonOperator' => [ 'type' => 'string', 'enum' => [ 'EQUAL', 'NOT_EQUAL', 'GREATER_THAN_EQUAL_TO', 'GREATER_THAN', 'LESS_THAN_EQUAL_TO', 'LESS_THAN', ], ], 'CompletedStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'FAILED', 'INTERRUPTED', 'CANCELED', 'NEVER_ATTEMPTED', ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ConflictExceptionReason', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'context' => [ 'shape' => 'ExceptionContext', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ConflictExceptionReason' => [ 'type' => 'string', 'enum' => [ 'CONFLICT_EXCEPTION', 'CONCURRENT_MODIFICATION', 'RESOURCE_ALREADY_EXISTS', 'RESOURCE_IN_USE', 'STATUS_CONFLICT', ], ], 'ConsumedUsageLimit' => [ 'type' => 'float', 'box' => true, 'min' => 0.01, ], 'ConsumedUsages' => [ 'type' => 'structure', 'required' => [ 'approximateDollarUsage', ], 'members' => [ 'approximateDollarUsage' => [ 'shape' => 'Float', ], ], ], 'CopyJobTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'queueId', 'targetS3Location', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'targetS3Location' => [ 'shape' => 'S3Location', ], ], ], 'CopyJobTemplateResponse' => [ 'type' => 'structure', 'required' => [ 'templateType', ], 'members' => [ 'templateType' => [ 'shape' => 'JobTemplateType', ], ], ], 'CpuArchitectureType' => [ 'type' => 'string', 'enum' => [ 'x86_64', 'arm64', ], ], 'CreateBudgetRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'usageTrackingResource', 'displayName', 'approximateDollarLimit', 'actions', 'schedule', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'usageTrackingResource' => [ 'shape' => 'UsageTrackingResource', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'Description', ], 'approximateDollarLimit' => [ 'shape' => 'ConsumedUsageLimit', ], 'actions' => [ 'shape' => 'BudgetActionsToAdd', ], 'schedule' => [ 'shape' => 'BudgetSchedule', ], ], ], 'CreateBudgetResponse' => [ 'type' => 'structure', 'required' => [ 'budgetId', ], 'members' => [ 'budgetId' => [ 'shape' => 'BudgetId', ], ], ], 'CreateFarmRequest' => [ 'type' => 'structure', 'required' => [ 'displayName', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'Description', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateFarmResponse' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', ], ], ], 'CreateFleetRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'displayName', 'roleArn', 'maxWorkerCount', 'configuration', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'Description', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'minWorkerCount' => [ 'shape' => 'MinZeroMaxInteger', ], 'maxWorkerCount' => [ 'shape' => 'MinZeroMaxInteger', ], 'configuration' => [ 'shape' => 'FleetConfiguration', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateFleetResponse' => [ 'type' => 'structure', 'required' => [ 'fleetId', ], 'members' => [ 'fleetId' => [ 'shape' => 'FleetId', ], ], ], 'CreateJobRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'template', 'templateType', 'priority', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'template' => [ 'shape' => 'JobTemplate', ], 'templateType' => [ 'shape' => 'JobTemplateType', ], 'priority' => [ 'shape' => 'JobPriority', ], 'parameters' => [ 'shape' => 'JobParameters', ], 'attachments' => [ 'shape' => 'Attachments', ], 'storageProfileId' => [ 'shape' => 'StorageProfileId', ], 'targetTaskRunStatus' => [ 'shape' => 'CreateJobTargetTaskRunStatus', ], 'maxFailedTasksCount' => [ 'shape' => 'MaxFailedTasksCount', ], 'maxRetriesPerTask' => [ 'shape' => 'MaxRetriesPerTask', ], ], ], 'CreateJobResponse' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], ], ], 'CreateJobTargetTaskRunStatus' => [ 'type' => 'string', 'enum' => [ 'READY', 'SUSPENDED', ], ], 'CreateLicenseEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'vpcId', 'subnetIds', 'securityGroupIds', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'vpcId' => [ 'shape' => 'VpcId', ], 'subnetIds' => [ 'shape' => 'CreateLicenseEndpointRequestSubnetIdsList', ], 'securityGroupIds' => [ 'shape' => 'CreateLicenseEndpointRequestSecurityGroupIdsList', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateLicenseEndpointRequestSecurityGroupIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 10, 'min' => 1, ], 'CreateLicenseEndpointRequestSubnetIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], 'max' => 10, 'min' => 1, ], 'CreateLicenseEndpointResponse' => [ 'type' => 'structure', 'required' => [ 'licenseEndpointId', ], 'members' => [ 'licenseEndpointId' => [ 'shape' => 'LicenseEndpointId', ], ], ], 'CreateMonitorRequest' => [ 'type' => 'structure', 'required' => [ 'displayName', 'identityCenterInstanceArn', 'subdomain', 'roleArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'identityCenterInstanceArn' => [ 'shape' => 'IdentityCenterInstanceArn', ], 'subdomain' => [ 'shape' => 'Subdomain', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'CreateMonitorResponse' => [ 'type' => 'structure', 'required' => [ 'monitorId', 'identityCenterApplicationArn', ], 'members' => [ 'monitorId' => [ 'shape' => 'MonitorId', ], 'identityCenterApplicationArn' => [ 'shape' => 'IdentityCenterApplicationArn', ], ], ], 'CreateQueueEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'priority', 'templateType', 'template', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'priority' => [ 'shape' => 'Priority', ], 'templateType' => [ 'shape' => 'EnvironmentTemplateType', ], 'template' => [ 'shape' => 'EnvironmentTemplate', ], ], ], 'CreateQueueEnvironmentResponse' => [ 'type' => 'structure', 'required' => [ 'queueEnvironmentId', ], 'members' => [ 'queueEnvironmentId' => [ 'shape' => 'QueueEnvironmentId', ], ], ], 'CreateQueueFleetAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'fleetId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', ], 'fleetId' => [ 'shape' => 'FleetId', ], ], ], 'CreateQueueFleetAssociationResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateQueueRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'displayName', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'Description', ], 'defaultBudgetAction' => [ 'shape' => 'DefaultQueueBudgetAction', ], 'jobAttachmentSettings' => [ 'shape' => 'JobAttachmentSettings', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'jobRunAsUser' => [ 'shape' => 'JobRunAsUser', ], 'requiredFileSystemLocationNames' => [ 'shape' => 'RequiredFileSystemLocationNames', ], 'allowedStorageProfileIds' => [ 'shape' => 'AllowedStorageProfileIds', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateQueueResponse' => [ 'type' => 'structure', 'required' => [ 'queueId', ], 'members' => [ 'queueId' => [ 'shape' => 'QueueId', ], ], ], 'CreateStorageProfileRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'displayName', 'osFamily', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'osFamily' => [ 'shape' => 'StorageProfileOperatingSystemFamily', ], 'fileSystemLocations' => [ 'shape' => 'FileSystemLocationsList', ], ], ], 'CreateStorageProfileResponse' => [ 'type' => 'structure', 'required' => [ 'storageProfileId', ], 'members' => [ 'storageProfileId' => [ 'shape' => 'StorageProfileId', ], ], ], 'CreateWorkerRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'hostProperties' => [ 'shape' => 'HostPropertiesRequest', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], ], ], 'CreateWorkerResponse' => [ 'type' => 'structure', 'required' => [ 'workerId', ], 'members' => [ 'workerId' => [ 'shape' => 'WorkerId', ], ], ], 'CreatedAt' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'CreatedBy' => [ 'type' => 'string', ], 'CustomFleetAmountCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetAmountCapability', ], 'max' => 15, 'min' => 1, ], 'CustomFleetAttributeCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetAttributeCapability', ], 'max' => 15, 'min' => 1, ], 'CustomerManagedFleetConfiguration' => [ 'type' => 'structure', 'required' => [ 'mode', 'workerCapabilities', ], 'members' => [ 'mode' => [ 'shape' => 'AutoScalingMode', ], 'workerCapabilities' => [ 'shape' => 'CustomerManagedWorkerCapabilities', ], 'storageProfileId' => [ 'shape' => 'StorageProfileId', ], ], ], 'CustomerManagedFleetOperatingSystemFamily' => [ 'type' => 'string', 'enum' => [ 'WINDOWS', 'LINUX', 'MACOS', ], ], 'CustomerManagedWorkerCapabilities' => [ 'type' => 'structure', 'required' => [ 'vCpuCount', 'memoryMiB', 'osFamily', 'cpuArchitectureType', ], 'members' => [ 'vCpuCount' => [ 'shape' => 'VCpuCountRange', ], 'memoryMiB' => [ 'shape' => 'MemoryMiBRange', ], 'acceleratorTypes' => [ 'shape' => 'AcceleratorTypes', ], 'acceleratorCount' => [ 'shape' => 'AcceleratorCountRange', ], 'acceleratorTotalMemoryMiB' => [ 'shape' => 'AcceleratorTotalMemoryMiBRange', ], 'osFamily' => [ 'shape' => 'CustomerManagedFleetOperatingSystemFamily', ], 'cpuArchitectureType' => [ 'shape' => 'CpuArchitectureType', ], 'customAmounts' => [ 'shape' => 'CustomFleetAmountCapabilities', ], 'customAttributes' => [ 'shape' => 'CustomFleetAttributeCapabilities', ], ], ], 'DateTimeFilterExpression' => [ 'type' => 'structure', 'required' => [ 'name', 'operator', 'dateTime', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'operator' => [ 'shape' => 'ComparisonOperator', ], 'dateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'DefaultQueueBudgetAction' => [ 'type' => 'string', 'enum' => [ 'NONE', 'STOP_SCHEDULING_AND_COMPLETE_TASKS', 'STOP_SCHEDULING_AND_CANCEL_TASKS', ], ], 'DeleteBudgetRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'budgetId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'budgetId' => [ 'shape' => 'BudgetId', 'location' => 'uri', 'locationName' => 'budgetId', ], ], ], 'DeleteBudgetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteFarmRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], ], ], 'DeleteFarmResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteFleetRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], ], ], 'DeleteFleetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLicenseEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'licenseEndpointId', ], 'members' => [ 'licenseEndpointId' => [ 'shape' => 'LicenseEndpointId', 'location' => 'uri', 'locationName' => 'licenseEndpointId', ], ], ], 'DeleteLicenseEndpointResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMeteredProductRequest' => [ 'type' => 'structure', 'required' => [ 'licenseEndpointId', 'productId', ], 'members' => [ 'licenseEndpointId' => [ 'shape' => 'LicenseEndpointId', 'location' => 'uri', 'locationName' => 'licenseEndpointId', ], 'productId' => [ 'shape' => 'MeteredProductId', 'location' => 'uri', 'locationName' => 'productId', ], ], ], 'DeleteMeteredProductResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMonitorRequest' => [ 'type' => 'structure', 'required' => [ 'monitorId', ], 'members' => [ 'monitorId' => [ 'shape' => 'MonitorId', 'location' => 'uri', 'locationName' => 'monitorId', ], ], ], 'DeleteMonitorResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteQueueEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'queueEnvironmentId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'queueEnvironmentId' => [ 'shape' => 'QueueEnvironmentId', 'location' => 'uri', 'locationName' => 'queueEnvironmentId', ], ], ], 'DeleteQueueEnvironmentResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteQueueFleetAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'fleetId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], ], ], 'DeleteQueueFleetAssociationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteQueueRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'DeleteQueueResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteStorageProfileRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'storageProfileId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'storageProfileId' => [ 'shape' => 'StorageProfileId', 'location' => 'uri', 'locationName' => 'storageProfileId', ], ], ], 'DeleteStorageProfileResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWorkerRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'workerId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'workerId' => [ 'shape' => 'WorkerId', 'location' => 'uri', 'locationName' => 'workerId', ], ], ], 'DeleteWorkerResponse' => [ 'type' => 'structure', 'members' => [], ], 'DependenciesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepId', ], ], 'DependencyConsumerResolutionStatus' => [ 'type' => 'string', 'enum' => [ 'RESOLVED', 'UNRESOLVED', ], ], 'DependencyCounts' => [ 'type' => 'structure', 'required' => [ 'dependenciesResolved', 'dependenciesUnresolved', 'consumersResolved', 'consumersUnresolved', ], 'members' => [ 'dependenciesResolved' => [ 'shape' => 'Integer', ], 'dependenciesUnresolved' => [ 'shape' => 'Integer', ], 'consumersResolved' => [ 'shape' => 'Integer', ], 'consumersUnresolved' => [ 'shape' => 'Integer', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'sensitive' => true, ], 'DesiredWorkerStatus' => [ 'type' => 'string', 'enum' => [ 'STOPPED', ], ], 'DisassociateMemberFromFarmRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'principalId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'uri', 'locationName' => 'principalId', ], ], ], 'DisassociateMemberFromFarmResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateMemberFromFleetRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'principalId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'uri', 'locationName' => 'principalId', ], ], ], 'DisassociateMemberFromFleetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateMemberFromJobRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'jobId', 'principalId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'uri', 'locationName' => 'principalId', ], ], ], 'DisassociateMemberFromJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateMemberFromQueueRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'principalId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'uri', 'locationName' => 'principalId', ], ], ], 'DisassociateMemberFromQueueResponse' => [ 'type' => 'structure', 'members' => [], ], 'DnsName' => [ 'type' => 'string', 'pattern' => 'vpce-[\\w]+-[\\w]+.vpce-svc-[\\w]+.*.vpce.amazonaws.com', ], 'Document' => [ 'type' => 'structure', 'members' => [], 'document' => true, 'sensitive' => true, ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'EbsIops' => [ 'type' => 'integer', 'box' => true, 'max' => 16000, 'min' => 3000, ], 'EbsThroughputMiB' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 125, ], 'Ec2EbsVolume' => [ 'type' => 'structure', 'members' => [ 'sizeGiB' => [ 'shape' => 'Integer', ], 'iops' => [ 'shape' => 'EbsIops', ], 'throughputMiB' => [ 'shape' => 'EbsThroughputMiB', ], ], ], 'Ec2MarketType' => [ 'type' => 'string', 'enum' => [ 'on-demand', 'spot', ], ], 'EndedAt' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'EndsAt' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'EnvironmentDetailsEntity' => [ 'type' => 'structure', 'required' => [ 'jobId', 'environmentId', 'schemaVersion', 'template', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'schemaVersion' => [ 'shape' => 'String', ], 'template' => [ 'shape' => 'Document', ], ], ], 'EnvironmentDetailsError' => [ 'type' => 'structure', 'required' => [ 'jobId', 'environmentId', 'code', 'message', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'code' => [ 'shape' => 'JobEntityErrorCode', ], 'message' => [ 'shape' => 'String', ], ], ], 'EnvironmentDetailsIdentifiers' => [ 'type' => 'structure', 'required' => [ 'jobId', 'environmentId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], ], ], 'EnvironmentEnterSessionActionDefinition' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', ], ], ], 'EnvironmentEnterSessionActionDefinitionSummary' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', ], ], ], 'EnvironmentExitSessionActionDefinition' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', ], ], ], 'EnvironmentExitSessionActionDefinitionSummary' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', ], ], ], 'EnvironmentId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '(STEP:step-[0-9a-f]{32}:.*)|(JOB:job-[0-9a-f]{32}:.*)', ], 'EnvironmentName' => [ 'type' => 'string', ], 'EnvironmentTemplate' => [ 'type' => 'string', 'max' => 15000, 'min' => 1, 'sensitive' => true, ], 'EnvironmentTemplateType' => [ 'type' => 'string', 'enum' => [ 'JSON', 'YAML', ], ], 'ExceptionContext' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'FarmId' => [ 'type' => 'string', 'pattern' => 'farm-[0-9a-f]{32}', ], 'FarmMember' => [ 'type' => 'structure', 'required' => [ 'farmId', 'principalId', 'principalType', 'identityStoreId', 'membershipLevel', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', ], 'principalType' => [ 'shape' => 'PrincipalType', ], 'identityStoreId' => [ 'shape' => 'IdentityStoreId', ], 'membershipLevel' => [ 'shape' => 'MembershipLevel', ], ], ], 'FarmMembers' => [ 'type' => 'list', 'member' => [ 'shape' => 'FarmMember', ], ], 'FarmSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'FarmSummary', ], ], 'FarmSummary' => [ 'type' => 'structure', 'required' => [ 'farmId', 'displayName', 'createdAt', 'createdBy', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'FieldSortExpression' => [ 'type' => 'structure', 'required' => [ 'sortOrder', 'name', ], 'members' => [ 'sortOrder' => [ 'shape' => 'SortOrder', ], 'name' => [ 'shape' => 'String', ], ], ], 'FileSystemLocation' => [ 'type' => 'structure', 'required' => [ 'name', 'path', 'type', ], 'members' => [ 'name' => [ 'shape' => 'FileSystemLocationName', ], 'path' => [ 'shape' => 'PathString', ], 'type' => [ 'shape' => 'FileSystemLocationType', ], ], 'sensitive' => true, ], 'FileSystemLocationName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[0-9A-Za-z ]*', 'sensitive' => true, ], 'FileSystemLocationType' => [ 'type' => 'string', 'enum' => [ 'SHARED', 'LOCAL', ], ], 'FileSystemLocationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileSystemLocation', ], 'max' => 20, 'min' => 0, ], 'FixedBudgetSchedule' => [ 'type' => 'structure', 'required' => [ 'startTime', 'endTime', ], 'members' => [ 'startTime' => [ 'shape' => 'StartsAt', ], 'endTime' => [ 'shape' => 'EndsAt', ], ], ], 'FleetAmountCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetAmountCapability', ], 'max' => 25, 'min' => 1, ], 'FleetAmountCapability' => [ 'type' => 'structure', 'required' => [ 'name', 'min', ], 'members' => [ 'name' => [ 'shape' => 'AmountCapabilityName', ], 'min' => [ 'shape' => 'Float', ], 'max' => [ 'shape' => 'Float', ], ], ], 'FleetAttributeCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetAttributeCapability', ], 'max' => 25, 'min' => 1, ], 'FleetAttributeCapability' => [ 'type' => 'structure', 'required' => [ 'name', 'values', ], 'members' => [ 'name' => [ 'shape' => 'AttributeCapabilityName', ], 'values' => [ 'shape' => 'AttributeCapabilityValuesList', ], ], ], 'FleetCapabilities' => [ 'type' => 'structure', 'members' => [ 'amounts' => [ 'shape' => 'FleetAmountCapabilities', ], 'attributes' => [ 'shape' => 'FleetAttributeCapabilities', ], ], ], 'FleetConfiguration' => [ 'type' => 'structure', 'members' => [ 'customerManaged' => [ 'shape' => 'CustomerManagedFleetConfiguration', ], 'serviceManagedEc2' => [ 'shape' => 'ServiceManagedEc2FleetConfiguration', ], ], 'union' => true, ], 'FleetId' => [ 'type' => 'string', 'pattern' => 'fleet-[0-9a-f]{32}', ], 'FleetMember' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'principalId', 'principalType', 'identityStoreId', 'membershipLevel', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', ], 'principalType' => [ 'shape' => 'PrincipalType', ], 'identityStoreId' => [ 'shape' => 'IdentityStoreId', ], 'membershipLevel' => [ 'shape' => 'MembershipLevel', ], ], ], 'FleetMembers' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetMember', ], ], 'FleetStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'CREATE_IN_PROGRESS', 'UPDATE_IN_PROGRESS', 'CREATE_FAILED', 'UPDATE_FAILED', ], ], 'FleetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetSummary', ], ], 'FleetSummary' => [ 'type' => 'structure', 'required' => [ 'fleetId', 'farmId', 'displayName', 'status', 'workerCount', 'minWorkerCount', 'maxWorkerCount', 'configuration', 'createdAt', 'createdBy', ], 'members' => [ 'fleetId' => [ 'shape' => 'FleetId', ], 'farmId' => [ 'shape' => 'FarmId', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'status' => [ 'shape' => 'FleetStatus', ], 'autoScalingStatus' => [ 'shape' => 'AutoScalingStatus', ], 'targetWorkerCount' => [ 'shape' => 'Integer', ], 'workerCount' => [ 'shape' => 'Integer', ], 'minWorkerCount' => [ 'shape' => 'MinZeroMaxInteger', ], 'maxWorkerCount' => [ 'shape' => 'MinZeroMaxInteger', ], 'configuration' => [ 'shape' => 'FleetConfiguration', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'Float' => [ 'type' => 'float', 'box' => true, ], 'FloatString' => [ 'type' => 'string', 'max' => 26, 'min' => 1, 'pattern' => '[-]?(0|[1-9][0-9]*)([.][0-9]+)?([eE][+-]?[0-9]+)?', ], 'GetBudgetRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'budgetId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'budgetId' => [ 'shape' => 'BudgetId', 'location' => 'uri', 'locationName' => 'budgetId', ], ], ], 'GetBudgetResponse' => [ 'type' => 'structure', 'required' => [ 'budgetId', 'usageTrackingResource', 'status', 'displayName', 'approximateDollarLimit', 'usages', 'actions', 'schedule', 'createdBy', 'createdAt', ], 'members' => [ 'budgetId' => [ 'shape' => 'BudgetId', ], 'usageTrackingResource' => [ 'shape' => 'UsageTrackingResource', ], 'status' => [ 'shape' => 'BudgetStatus', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'Description', ], 'approximateDollarLimit' => [ 'shape' => 'ConsumedUsageLimit', ], 'usages' => [ 'shape' => 'ConsumedUsages', ], 'actions' => [ 'shape' => 'ResponseBudgetActionList', ], 'schedule' => [ 'shape' => 'BudgetSchedule', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'queueStoppedAt' => [ 'shape' => 'UpdatedAt', ], ], ], 'GetFarmRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], ], ], 'GetFarmResponse' => [ 'type' => 'structure', 'required' => [ 'farmId', 'displayName', 'kmsKeyArn', 'createdAt', 'createdBy', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'Description', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetFleetRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], ], ], 'GetFleetResponse' => [ 'type' => 'structure', 'required' => [ 'fleetId', 'farmId', 'displayName', 'status', 'workerCount', 'minWorkerCount', 'maxWorkerCount', 'configuration', 'roleArn', 'createdAt', 'createdBy', ], 'members' => [ 'fleetId' => [ 'shape' => 'FleetId', ], 'farmId' => [ 'shape' => 'FarmId', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'Description', ], 'status' => [ 'shape' => 'FleetStatus', ], 'autoScalingStatus' => [ 'shape' => 'AutoScalingStatus', ], 'targetWorkerCount' => [ 'shape' => 'Integer', ], 'workerCount' => [ 'shape' => 'Integer', ], 'minWorkerCount' => [ 'shape' => 'MinZeroMaxInteger', ], 'maxWorkerCount' => [ 'shape' => 'MinZeroMaxInteger', ], 'configuration' => [ 'shape' => 'FleetConfiguration', ], 'capabilities' => [ 'shape' => 'FleetCapabilities', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetJobEntityError' => [ 'type' => 'structure', 'members' => [ 'jobDetails' => [ 'shape' => 'JobDetailsError', ], 'jobAttachmentDetails' => [ 'shape' => 'JobAttachmentDetailsError', ], 'stepDetails' => [ 'shape' => 'StepDetailsError', ], 'environmentDetails' => [ 'shape' => 'EnvironmentDetailsError', ], ], 'union' => true, ], 'GetJobRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'GetJobResponse' => [ 'type' => 'structure', 'required' => [ 'jobId', 'name', 'lifecycleStatus', 'lifecycleStatusMessage', 'priority', 'createdAt', 'createdBy', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'name' => [ 'shape' => 'JobName', ], 'lifecycleStatus' => [ 'shape' => 'JobLifecycleStatus', ], 'lifecycleStatusMessage' => [ 'shape' => 'String', ], 'priority' => [ 'shape' => 'JobPriority', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'taskRunStatus' => [ 'shape' => 'TaskRunStatus', ], 'targetTaskRunStatus' => [ 'shape' => 'JobTargetTaskRunStatus', ], 'taskRunStatusCounts' => [ 'shape' => 'TaskRunStatusCounts', ], 'storageProfileId' => [ 'shape' => 'StorageProfileId', ], 'maxFailedTasksCount' => [ 'shape' => 'MaxFailedTasksCount', ], 'maxRetriesPerTask' => [ 'shape' => 'MaxRetriesPerTask', ], 'parameters' => [ 'shape' => 'JobParameters', ], 'attachments' => [ 'shape' => 'Attachments', ], 'description' => [ 'shape' => 'JobDescription', ], ], ], 'GetLicenseEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'licenseEndpointId', ], 'members' => [ 'licenseEndpointId' => [ 'shape' => 'LicenseEndpointId', 'location' => 'uri', 'locationName' => 'licenseEndpointId', ], ], ], 'GetLicenseEndpointResponse' => [ 'type' => 'structure', 'required' => [ 'licenseEndpointId', 'status', 'statusMessage', ], 'members' => [ 'licenseEndpointId' => [ 'shape' => 'LicenseEndpointId', ], 'status' => [ 'shape' => 'LicenseEndpointStatus', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'vpcId' => [ 'shape' => 'VpcId', ], 'dnsName' => [ 'shape' => 'DnsName', ], 'subnetIds' => [ 'shape' => 'GetLicenseEndpointResponseSubnetIdsList', ], 'securityGroupIds' => [ 'shape' => 'GetLicenseEndpointResponseSecurityGroupIdsList', ], ], ], 'GetLicenseEndpointResponseSecurityGroupIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 10, 'min' => 1, ], 'GetLicenseEndpointResponseSubnetIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], 'max' => 10, 'min' => 1, ], 'GetMonitorRequest' => [ 'type' => 'structure', 'required' => [ 'monitorId', ], 'members' => [ 'monitorId' => [ 'shape' => 'MonitorId', 'location' => 'uri', 'locationName' => 'monitorId', ], ], ], 'GetMonitorResponse' => [ 'type' => 'structure', 'required' => [ 'monitorId', 'displayName', 'subdomain', 'url', 'roleArn', 'identityCenterInstanceArn', 'identityCenterApplicationArn', 'createdAt', 'createdBy', ], 'members' => [ 'monitorId' => [ 'shape' => 'MonitorId', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'subdomain' => [ 'shape' => 'Subdomain', ], 'url' => [ 'shape' => 'Url', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'identityCenterInstanceArn' => [ 'shape' => 'IdentityCenterInstanceArn', ], 'identityCenterApplicationArn' => [ 'shape' => 'IdentityCenterApplicationArn', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetQueueEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'queueEnvironmentId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'queueEnvironmentId' => [ 'shape' => 'QueueEnvironmentId', 'location' => 'uri', 'locationName' => 'queueEnvironmentId', ], ], ], 'GetQueueEnvironmentResponse' => [ 'type' => 'structure', 'required' => [ 'queueEnvironmentId', 'name', 'priority', 'templateType', 'template', 'createdAt', 'createdBy', ], 'members' => [ 'queueEnvironmentId' => [ 'shape' => 'QueueEnvironmentId', ], 'name' => [ 'shape' => 'EnvironmentName', ], 'priority' => [ 'shape' => 'Priority', ], 'templateType' => [ 'shape' => 'EnvironmentTemplateType', ], 'template' => [ 'shape' => 'EnvironmentTemplate', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetQueueFleetAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'fleetId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], ], ], 'GetQueueFleetAssociationResponse' => [ 'type' => 'structure', 'required' => [ 'queueId', 'fleetId', 'status', 'createdAt', 'createdBy', ], 'members' => [ 'queueId' => [ 'shape' => 'QueueId', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'status' => [ 'shape' => 'QueueFleetAssociationStatus', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetQueueRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'GetQueueResponse' => [ 'type' => 'structure', 'required' => [ 'queueId', 'displayName', 'farmId', 'status', 'defaultBudgetAction', 'createdAt', 'createdBy', ], 'members' => [ 'queueId' => [ 'shape' => 'QueueId', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'Description', ], 'farmId' => [ 'shape' => 'FarmId', ], 'status' => [ 'shape' => 'QueueStatus', ], 'defaultBudgetAction' => [ 'shape' => 'DefaultQueueBudgetAction', ], 'blockedReason' => [ 'shape' => 'QueueBlockedReason', ], 'jobAttachmentSettings' => [ 'shape' => 'JobAttachmentSettings', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'requiredFileSystemLocationNames' => [ 'shape' => 'RequiredFileSystemLocationNames', ], 'allowedStorageProfileIds' => [ 'shape' => 'AllowedStorageProfileIds', ], 'jobRunAsUser' => [ 'shape' => 'JobRunAsUser', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetSessionActionRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'jobId', 'sessionActionId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'sessionActionId' => [ 'shape' => 'SessionActionId', 'location' => 'uri', 'locationName' => 'sessionActionId', ], ], ], 'GetSessionActionResponse' => [ 'type' => 'structure', 'required' => [ 'sessionActionId', 'status', 'sessionId', 'definition', ], 'members' => [ 'sessionActionId' => [ 'shape' => 'SessionActionId', ], 'status' => [ 'shape' => 'SessionActionStatus', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'workerUpdatedAt' => [ 'shape' => 'Timestamp', ], 'progressPercent' => [ 'shape' => 'SessionActionProgressPercent', ], 'sessionId' => [ 'shape' => 'SessionId', ], 'processExitCode' => [ 'shape' => 'ProcessExitCode', ], 'progressMessage' => [ 'shape' => 'SessionActionProgressMessage', ], 'definition' => [ 'shape' => 'SessionActionDefinition', ], ], ], 'GetSessionRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'jobId', 'sessionId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'sessionId' => [ 'shape' => 'SessionId', 'location' => 'uri', 'locationName' => 'sessionId', ], ], ], 'GetSessionResponse' => [ 'type' => 'structure', 'required' => [ 'sessionId', 'fleetId', 'workerId', 'startedAt', 'log', 'lifecycleStatus', ], 'members' => [ 'sessionId' => [ 'shape' => 'SessionId', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'workerId' => [ 'shape' => 'WorkerId', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'log' => [ 'shape' => 'LogConfiguration', ], 'lifecycleStatus' => [ 'shape' => 'SessionLifecycleStatus', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'targetLifecycleStatus' => [ 'shape' => 'SessionLifecycleTargetStatus', ], 'hostProperties' => [ 'shape' => 'HostPropertiesResponse', ], 'workerLog' => [ 'shape' => 'LogConfiguration', ], ], ], 'GetSessionsStatisticsAggregationRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'aggregationId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'aggregationId' => [ 'shape' => 'AggregationId', 'location' => 'querystring', 'locationName' => 'aggregationId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetSessionsStatisticsAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'statistics' => [ 'shape' => 'StatisticsList', ], 'nextToken' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'SessionsStatisticsAggregationStatus', ], 'statusMessage' => [ 'shape' => 'String', ], ], ], 'GetStepRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'jobId', 'stepId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'stepId' => [ 'shape' => 'StepId', 'location' => 'uri', 'locationName' => 'stepId', ], ], ], 'GetStepResponse' => [ 'type' => 'structure', 'required' => [ 'stepId', 'name', 'lifecycleStatus', 'taskRunStatus', 'taskRunStatusCounts', 'createdAt', 'createdBy', ], 'members' => [ 'stepId' => [ 'shape' => 'StepId', ], 'name' => [ 'shape' => 'StepName', ], 'lifecycleStatus' => [ 'shape' => 'StepLifecycleStatus', ], 'lifecycleStatusMessage' => [ 'shape' => 'String', ], 'taskRunStatus' => [ 'shape' => 'TaskRunStatus', ], 'taskRunStatusCounts' => [ 'shape' => 'TaskRunStatusCounts', ], 'targetTaskRunStatus' => [ 'shape' => 'StepTargetTaskRunStatus', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'dependencyCounts' => [ 'shape' => 'DependencyCounts', ], 'requiredCapabilities' => [ 'shape' => 'StepRequiredCapabilities', ], 'parameterSpace' => [ 'shape' => 'ParameterSpace', ], 'description' => [ 'shape' => 'StepDescription', ], ], ], 'GetStorageProfileForQueueRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'storageProfileId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'storageProfileId' => [ 'shape' => 'StorageProfileId', 'location' => 'uri', 'locationName' => 'storageProfileId', ], ], ], 'GetStorageProfileForQueueResponse' => [ 'type' => 'structure', 'required' => [ 'storageProfileId', 'displayName', 'osFamily', ], 'members' => [ 'storageProfileId' => [ 'shape' => 'StorageProfileId', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'osFamily' => [ 'shape' => 'StorageProfileOperatingSystemFamily', ], 'fileSystemLocations' => [ 'shape' => 'FileSystemLocationsList', ], ], ], 'GetStorageProfileRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'storageProfileId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'storageProfileId' => [ 'shape' => 'StorageProfileId', 'location' => 'uri', 'locationName' => 'storageProfileId', ], ], ], 'GetStorageProfileResponse' => [ 'type' => 'structure', 'required' => [ 'storageProfileId', 'displayName', 'osFamily', 'createdAt', 'createdBy', ], 'members' => [ 'storageProfileId' => [ 'shape' => 'StorageProfileId', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'osFamily' => [ 'shape' => 'StorageProfileOperatingSystemFamily', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'fileSystemLocations' => [ 'shape' => 'FileSystemLocationsList', ], ], ], 'GetTaskRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'jobId', 'stepId', 'taskId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'stepId' => [ 'shape' => 'StepId', 'location' => 'uri', 'locationName' => 'stepId', ], 'taskId' => [ 'shape' => 'TaskId', 'location' => 'uri', 'locationName' => 'taskId', ], ], ], 'GetTaskResponse' => [ 'type' => 'structure', 'required' => [ 'taskId', 'createdAt', 'createdBy', 'runStatus', ], 'members' => [ 'taskId' => [ 'shape' => 'TaskId', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'runStatus' => [ 'shape' => 'TaskRunStatus', ], 'targetRunStatus' => [ 'shape' => 'TaskTargetRunStatus', ], 'failureRetryCount' => [ 'shape' => 'TaskRetryCount', ], 'parameters' => [ 'shape' => 'TaskParameters', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'latestSessionActionId' => [ 'shape' => 'SessionActionId', ], ], ], 'GetWorkerRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'workerId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'workerId' => [ 'shape' => 'WorkerId', 'location' => 'uri', 'locationName' => 'workerId', ], ], ], 'GetWorkerResponse' => [ 'type' => 'structure', 'required' => [ 'workerId', 'farmId', 'fleetId', 'status', 'createdAt', 'createdBy', ], 'members' => [ 'workerId' => [ 'shape' => 'WorkerId', ], 'farmId' => [ 'shape' => 'FarmId', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'hostProperties' => [ 'shape' => 'HostPropertiesResponse', ], 'status' => [ 'shape' => 'WorkerStatus', ], 'log' => [ 'shape' => 'LogConfiguration', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'HostName' => [ 'type' => 'string', 'pattern' => '[a-zA-Z0-9_\\.\\-]{0,255}', ], 'HostPropertiesRequest' => [ 'type' => 'structure', 'members' => [ 'ipAddresses' => [ 'shape' => 'IpAddresses', ], 'hostName' => [ 'shape' => 'HostName', ], ], ], 'HostPropertiesResponse' => [ 'type' => 'structure', 'members' => [ 'ipAddresses' => [ 'shape' => 'IpAddresses', ], 'hostName' => [ 'shape' => 'HostName', ], 'ec2InstanceArn' => [ 'shape' => 'String', ], 'ec2InstanceType' => [ 'shape' => 'InstanceType', ], ], ], 'IamRoleArn' => [ 'type' => 'string', 'pattern' => 'arn:(aws[a-zA-Z-]*):iam::\\d{12}:role(/[!-.0-~]+)*/[\\w+=,.@-]+', ], 'IdentityCenterApplicationArn' => [ 'type' => 'string', ], 'IdentityCenterInstanceArn' => [ 'type' => 'string', 'pattern' => 'arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso:::instance/(sso)?ins-[a-zA-Z0-9-.]{16}', ], 'IdentityCenterPrincipalId' => [ 'type' => 'string', 'max' => 47, 'min' => 1, 'pattern' => '([0-9a-f]{10}-|)[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}', ], 'IdentityStoreId' => [ 'type' => 'string', 'max' => 36, 'min' => 1, 'pattern' => 'd-[0-9a-f]{10}$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'InstanceType' => [ 'type' => 'string', 'pattern' => '[a-zA-Z0-9]+\\.[a-zA-Z0-9]+', ], 'InstanceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceType', ], 'max' => 100, 'min' => 1, ], 'IntString' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '[-]?(0|[1-9][0-9]*)', ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerErrorException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'IpAddresses' => [ 'type' => 'structure', 'members' => [ 'ipV4Addresses' => [ 'shape' => 'IpV4Addresses', ], 'ipV6Addresses' => [ 'shape' => 'IpV6Addresses', ], ], ], 'IpV4Address' => [ 'type' => 'string', 'pattern' => '(?:[0-9]{1,3}\\.){3}[0-9]{1,3}', ], 'IpV4Addresses' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpV4Address', ], ], 'IpV6Address' => [ 'type' => 'string', 'pattern' => '(?:[A-F0-9]{1,4}:){7}[A-F0-9]{1,4}', ], 'IpV6Addresses' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpV6Address', ], ], 'JobAttachmentDetailsEntity' => [ 'type' => 'structure', 'required' => [ 'jobId', 'attachments', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'attachments' => [ 'shape' => 'Attachments', ], ], ], 'JobAttachmentDetailsError' => [ 'type' => 'structure', 'required' => [ 'jobId', 'code', 'message', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'code' => [ 'shape' => 'JobEntityErrorCode', ], 'message' => [ 'shape' => 'String', ], ], ], 'JobAttachmentDetailsIdentifiers' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], ], ], 'JobAttachmentSettings' => [ 'type' => 'structure', 'required' => [ 's3BucketName', 'rootPrefix', ], 'members' => [ 's3BucketName' => [ 'shape' => 'S3BucketName', ], 'rootPrefix' => [ 'shape' => 'S3Prefix', ], ], ], 'JobAttachmentsFileSystem' => [ 'type' => 'string', 'enum' => [ 'COPIED', 'VIRTUAL', ], ], 'JobDescription' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'sensitive' => true, ], 'JobDetailsEntity' => [ 'type' => 'structure', 'required' => [ 'jobId', 'logGroupName', 'schemaVersion', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'jobAttachmentSettings' => [ 'shape' => 'JobAttachmentSettings', ], 'jobRunAsUser' => [ 'shape' => 'JobRunAsUser', ], 'logGroupName' => [ 'shape' => 'String', ], 'queueRoleArn' => [ 'shape' => 'IamRoleArn', ], 'parameters' => [ 'shape' => 'JobParameters', ], 'schemaVersion' => [ 'shape' => 'String', ], 'pathMappingRules' => [ 'shape' => 'PathMappingRules', ], ], ], 'JobDetailsError' => [ 'type' => 'structure', 'required' => [ 'jobId', 'code', 'message', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'code' => [ 'shape' => 'JobEntityErrorCode', ], 'message' => [ 'shape' => 'String', ], ], ], 'JobDetailsIdentifiers' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], ], ], 'JobEntity' => [ 'type' => 'structure', 'members' => [ 'jobDetails' => [ 'shape' => 'JobDetailsEntity', ], 'jobAttachmentDetails' => [ 'shape' => 'JobAttachmentDetailsEntity', ], 'stepDetails' => [ 'shape' => 'StepDetailsEntity', ], 'environmentDetails' => [ 'shape' => 'EnvironmentDetailsEntity', ], ], 'union' => true, ], 'JobEntityErrorCode' => [ 'type' => 'string', 'enum' => [ 'AccessDeniedException', 'InternalServerException', 'ValidationException', 'ResourceNotFoundException', 'MaxPayloadSizeExceeded', 'ConflictException', ], ], 'JobEntityIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobEntityIdentifiersUnion', ], 'max' => 10, 'min' => 1, ], 'JobEntityIdentifiersUnion' => [ 'type' => 'structure', 'members' => [ 'jobDetails' => [ 'shape' => 'JobDetailsIdentifiers', ], 'jobAttachmentDetails' => [ 'shape' => 'JobAttachmentDetailsIdentifiers', ], 'stepDetails' => [ 'shape' => 'StepDetailsIdentifiers', ], 'environmentDetails' => [ 'shape' => 'EnvironmentDetailsIdentifiers', ], ], 'union' => true, ], 'JobId' => [ 'type' => 'string', 'pattern' => 'job-[0-9a-f]{32}', ], 'JobLifecycleStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'CREATE_COMPLETE', 'UPLOAD_IN_PROGRESS', 'UPLOAD_FAILED', 'UPDATE_IN_PROGRESS', 'UPDATE_FAILED', 'UPDATE_SUCCEEDED', 'ARCHIVED', ], ], 'JobMember' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'jobId', 'principalId', 'principalType', 'identityStoreId', 'membershipLevel', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', ], 'queueId' => [ 'shape' => 'QueueId', ], 'jobId' => [ 'shape' => 'JobId', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', ], 'principalType' => [ 'shape' => 'PrincipalType', ], 'identityStoreId' => [ 'shape' => 'IdentityStoreId', ], 'membershipLevel' => [ 'shape' => 'MembershipLevel', ], ], ], 'JobMembers' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobMember', ], ], 'JobName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'JobParameter' => [ 'type' => 'structure', 'members' => [ 'int' => [ 'shape' => 'IntString', ], 'float' => [ 'shape' => 'FloatString', ], 'string' => [ 'shape' => 'ParameterString', ], 'path' => [ 'shape' => 'PathString', ], ], 'union' => true, ], 'JobParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'JobParameter', ], 'sensitive' => true, ], 'JobPriority' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 0, ], 'JobRunAsUser' => [ 'type' => 'structure', 'required' => [ 'runAs', ], 'members' => [ 'posix' => [ 'shape' => 'PosixUser', ], 'windows' => [ 'shape' => 'WindowsUser', ], 'runAs' => [ 'shape' => 'RunAs', ], ], ], 'JobSearchSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobSearchSummary', ], ], 'JobSearchSummary' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'queueId' => [ 'shape' => 'QueueId', ], 'name' => [ 'shape' => 'JobName', ], 'lifecycleStatus' => [ 'shape' => 'JobLifecycleStatus', ], 'lifecycleStatusMessage' => [ 'shape' => 'String', ], 'taskRunStatus' => [ 'shape' => 'TaskRunStatus', ], 'targetTaskRunStatus' => [ 'shape' => 'JobTargetTaskRunStatus', ], 'taskRunStatusCounts' => [ 'shape' => 'TaskRunStatusCounts', ], 'priority' => [ 'shape' => 'JobPriority', ], 'maxFailedTasksCount' => [ 'shape' => 'MaxFailedTasksCount', ], 'maxRetriesPerTask' => [ 'shape' => 'MaxRetriesPerTask', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'jobParameters' => [ 'shape' => 'JobParameters', ], ], ], 'JobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobSummary', ], ], 'JobSummary' => [ 'type' => 'structure', 'required' => [ 'jobId', 'name', 'lifecycleStatus', 'lifecycleStatusMessage', 'priority', 'createdAt', 'createdBy', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'name' => [ 'shape' => 'JobName', ], 'lifecycleStatus' => [ 'shape' => 'JobLifecycleStatus', ], 'lifecycleStatusMessage' => [ 'shape' => 'String', ], 'priority' => [ 'shape' => 'JobPriority', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'taskRunStatus' => [ 'shape' => 'TaskRunStatus', ], 'targetTaskRunStatus' => [ 'shape' => 'JobTargetTaskRunStatus', ], 'taskRunStatusCounts' => [ 'shape' => 'TaskRunStatusCounts', ], 'maxFailedTasksCount' => [ 'shape' => 'MaxFailedTasksCount', ], 'maxRetriesPerTask' => [ 'shape' => 'MaxRetriesPerTask', ], ], ], 'JobTargetTaskRunStatus' => [ 'type' => 'string', 'enum' => [ 'READY', 'FAILED', 'SUCCEEDED', 'CANCELED', 'SUSPENDED', 'PENDING', ], ], 'JobTemplate' => [ 'type' => 'string', 'max' => 300000, 'min' => 1, 'sensitive' => true, ], 'JobTemplateType' => [ 'type' => 'string', 'enum' => [ 'JSON', 'YAML', ], ], 'KmsKeyArn' => [ 'type' => 'string', 'pattern' => 'arn:(aws[a-zA-Z-]*):kms:[a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1}:\\d{12}:key/[\\w-]{1,120}', ], 'LicenseEndpointId' => [ 'type' => 'string', 'pattern' => 'le-[0-9a-f]{32}', ], 'LicenseEndpointStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'DELETE_IN_PROGRESS', 'READY', 'NOT_READY', ], ], 'LicenseEndpointSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'LicenseEndpointSummary', ], ], 'LicenseEndpointSummary' => [ 'type' => 'structure', 'members' => [ 'licenseEndpointId' => [ 'shape' => 'LicenseEndpointId', ], 'status' => [ 'shape' => 'LicenseEndpointStatus', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'vpcId' => [ 'shape' => 'VpcId', ], ], ], 'LicenseProduct' => [ 'type' => 'string', ], 'ListAttributeCapabilityValue' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeCapabilityValue', ], ], 'ListAvailableMeteredProductsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAvailableMeteredProductsResponse' => [ 'type' => 'structure', 'required' => [ 'meteredProducts', ], 'members' => [ 'meteredProducts' => [ 'shape' => 'MeteredProductSummaryList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListBudgetsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'status' => [ 'shape' => 'BudgetStatus', 'location' => 'querystring', 'locationName' => 'status', ], ], ], 'ListBudgetsResponse' => [ 'type' => 'structure', 'required' => [ 'budgets', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'budgets' => [ 'shape' => 'BudgetSummaries', ], ], ], 'ListFarmMembersRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListFarmMembersResponse' => [ 'type' => 'structure', 'required' => [ 'members', ], 'members' => [ 'members' => [ 'shape' => 'FarmMembers', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListFarmsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'querystring', 'locationName' => 'principalId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListFarmsResponse' => [ 'type' => 'structure', 'required' => [ 'farms', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'farms' => [ 'shape' => 'FarmSummaries', ], ], ], 'ListFleetMembersRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListFleetMembersResponse' => [ 'type' => 'structure', 'required' => [ 'members', ], 'members' => [ 'members' => [ 'shape' => 'FleetMembers', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListFleetsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'querystring', 'locationName' => 'principalId', ], 'displayName' => [ 'shape' => 'ResourceName', 'location' => 'querystring', 'locationName' => 'displayName', ], 'status' => [ 'shape' => 'FleetStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListFleetsResponse' => [ 'type' => 'structure', 'required' => [ 'fleets', ], 'members' => [ 'fleets' => [ 'shape' => 'FleetSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListJobMembersRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'jobId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListJobMembersResponse' => [ 'type' => 'structure', 'required' => [ 'members', ], 'members' => [ 'members' => [ 'shape' => 'JobMembers', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListJobsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'querystring', 'locationName' => 'principalId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListJobsResponse' => [ 'type' => 'structure', 'required' => [ 'jobs', ], 'members' => [ 'jobs' => [ 'shape' => 'JobSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListLicenseEndpointsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListLicenseEndpointsResponse' => [ 'type' => 'structure', 'required' => [ 'licenseEndpoints', ], 'members' => [ 'licenseEndpoints' => [ 'shape' => 'LicenseEndpointSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListMeteredProductsRequest' => [ 'type' => 'structure', 'required' => [ 'licenseEndpointId', ], 'members' => [ 'licenseEndpointId' => [ 'shape' => 'LicenseEndpointId', 'location' => 'uri', 'locationName' => 'licenseEndpointId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListMeteredProductsResponse' => [ 'type' => 'structure', 'required' => [ 'meteredProducts', ], 'members' => [ 'meteredProducts' => [ 'shape' => 'MeteredProductSummaryList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListMonitorsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListMonitorsResponse' => [ 'type' => 'structure', 'required' => [ 'monitors', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'monitors' => [ 'shape' => 'MonitorSummaries', ], ], ], 'ListQueueEnvironmentsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListQueueEnvironmentsResponse' => [ 'type' => 'structure', 'required' => [ 'environments', ], 'members' => [ 'environments' => [ 'shape' => 'QueueEnvironmentSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListQueueFleetAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'querystring', 'locationName' => 'queueId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'querystring', 'locationName' => 'fleetId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListQueueFleetAssociationsResponse' => [ 'type' => 'structure', 'required' => [ 'queueFleetAssociations', ], 'members' => [ 'queueFleetAssociations' => [ 'shape' => 'QueueFleetAssociationSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListQueueMembersRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListQueueMembersResponse' => [ 'type' => 'structure', 'required' => [ 'members', ], 'members' => [ 'members' => [ 'shape' => 'QueueMemberList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListQueuesRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'querystring', 'locationName' => 'principalId', ], 'status' => [ 'shape' => 'QueueStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListQueuesResponse' => [ 'type' => 'structure', 'required' => [ 'queues', ], 'members' => [ 'queues' => [ 'shape' => 'QueueSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListSessionActionsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'jobId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'sessionId' => [ 'shape' => 'SessionId', 'location' => 'querystring', 'locationName' => 'sessionId', ], 'taskId' => [ 'shape' => 'TaskId', 'location' => 'querystring', 'locationName' => 'taskId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListSessionActionsResponse' => [ 'type' => 'structure', 'required' => [ 'sessionActions', ], 'members' => [ 'sessionActions' => [ 'shape' => 'SessionActionSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListSessionsForWorkerRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'workerId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'workerId' => [ 'shape' => 'WorkerId', 'location' => 'uri', 'locationName' => 'workerId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListSessionsForWorkerResponse' => [ 'type' => 'structure', 'required' => [ 'sessions', ], 'members' => [ 'sessions' => [ 'shape' => 'ListSessionsForWorkerSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListSessionsForWorkerSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkerSessionSummary', ], ], 'ListSessionsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'jobId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListSessionsResponse' => [ 'type' => 'structure', 'required' => [ 'sessions', ], 'members' => [ 'sessions' => [ 'shape' => 'SessionSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListStepConsumersRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'jobId', 'stepId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'stepId' => [ 'shape' => 'StepId', 'location' => 'uri', 'locationName' => 'stepId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ListStepConsumersRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListStepConsumersRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ListStepConsumersResponse' => [ 'type' => 'structure', 'required' => [ 'consumers', ], 'members' => [ 'consumers' => [ 'shape' => 'StepConsumers', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListStepDependenciesRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'jobId', 'stepId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'stepId' => [ 'shape' => 'StepId', 'location' => 'uri', 'locationName' => 'stepId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ListStepDependenciesRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListStepDependenciesRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ListStepDependenciesResponse' => [ 'type' => 'structure', 'required' => [ 'dependencies', ], 'members' => [ 'dependencies' => [ 'shape' => 'StepDependencies', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListStepsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'jobId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListStepsResponse' => [ 'type' => 'structure', 'required' => [ 'steps', ], 'members' => [ 'steps' => [ 'shape' => 'StepSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListStorageProfilesForQueueRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListStorageProfilesForQueueResponse' => [ 'type' => 'structure', 'required' => [ 'storageProfiles', ], 'members' => [ 'storageProfiles' => [ 'shape' => 'StorageProfileSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListStorageProfilesRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListStorageProfilesResponse' => [ 'type' => 'structure', 'required' => [ 'storageProfiles', ], 'members' => [ 'storageProfiles' => [ 'shape' => 'StorageProfileSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'ListTasksRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'jobId', 'stepId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'stepId' => [ 'shape' => 'StepId', 'location' => 'uri', 'locationName' => 'stepId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListTasksResponse' => [ 'type' => 'structure', 'required' => [ 'tasks', ], 'members' => [ 'tasks' => [ 'shape' => 'TaskSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListWorkersRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListWorkersResponse' => [ 'type' => 'structure', 'required' => [ 'workers', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'workers' => [ 'shape' => 'WorkerSummaries', ], ], ], 'LogConfiguration' => [ 'type' => 'structure', 'required' => [ 'logDriver', ], 'members' => [ 'logDriver' => [ 'shape' => 'LogDriver', ], 'options' => [ 'shape' => 'LogOptions', ], 'parameters' => [ 'shape' => 'LogParameters', ], 'error' => [ 'shape' => 'LogError', ], ], ], 'LogDriver' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'LogError' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'LogOptions' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'LogParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'LogicalOperator' => [ 'type' => 'string', 'enum' => [ 'AND', 'OR', ], ], 'ManifestProperties' => [ 'type' => 'structure', 'required' => [ 'rootPath', 'rootPathFormat', ], 'members' => [ 'fileSystemLocationName' => [ 'shape' => 'FileSystemLocationName', ], 'rootPath' => [ 'shape' => 'ManifestPropertiesRootPathString', ], 'rootPathFormat' => [ 'shape' => 'PathFormat', ], 'outputRelativeDirectories' => [ 'shape' => 'OutputRelativeDirectoriesList', ], 'inputManifestPath' => [ 'shape' => 'ManifestPropertiesInputManifestPathString', ], 'inputManifestHash' => [ 'shape' => 'ManifestPropertiesInputManifestHashString', ], ], 'sensitive' => true, ], 'ManifestPropertiesInputManifestHashString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ManifestPropertiesInputManifestPathString' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'ManifestPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManifestProperties', ], 'max' => 10, 'min' => 1, ], 'ManifestPropertiesRootPathString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'MaxFailedTasksCount' => [ 'type' => 'integer', 'box' => true, 'max' => **********, 'min' => 0, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxRetriesPerTask' => [ 'type' => 'integer', 'box' => true, 'max' => **********, 'min' => 0, ], 'MembershipLevel' => [ 'type' => 'string', 'enum' => [ 'VIEWER', 'CONTRIBUTOR', 'OWNER', 'MANAGER', ], ], 'MemoryAmountMiB' => [ 'type' => 'integer', 'box' => true, 'max' => **********, 'min' => 512, ], 'MemoryMiBRange' => [ 'type' => 'structure', 'required' => [ 'min', ], 'members' => [ 'min' => [ 'shape' => 'MemoryAmountMiB', ], 'max' => [ 'shape' => 'MemoryAmountMiB', ], ], ], 'MeteredProductId' => [ 'type' => 'string', 'pattern' => '[0-9a-z]{1,32}-[.0-9a-z]{1,32}', ], 'MeteredProductSummary' => [ 'type' => 'structure', 'required' => [ 'productId', 'family', 'vendor', 'port', ], 'members' => [ 'productId' => [ 'shape' => 'MeteredProductId', ], 'family' => [ 'shape' => 'BoundedString', ], 'vendor' => [ 'shape' => 'BoundedString', ], 'port' => [ 'shape' => 'PortNumber', ], ], ], 'MeteredProductSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MeteredProductSummary', ], ], 'MinOneMaxTenThousand' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 1, ], 'MinZeroMaxInteger' => [ 'type' => 'integer', 'box' => true, 'max' => **********, 'min' => 0, ], 'MonitorId' => [ 'type' => 'string', 'pattern' => 'monitor-[0-9a-f]{32}', ], 'MonitorSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'MonitorSummary', ], ], 'MonitorSummary' => [ 'type' => 'structure', 'required' => [ 'monitorId', 'displayName', 'subdomain', 'url', 'roleArn', 'identityCenterInstanceArn', 'identityCenterApplicationArn', 'createdAt', 'createdBy', ], 'members' => [ 'monitorId' => [ 'shape' => 'MonitorId', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'subdomain' => [ 'shape' => 'Subdomain', ], 'url' => [ 'shape' => 'Url', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'identityCenterInstanceArn' => [ 'shape' => 'IdentityCenterInstanceArn', ], 'identityCenterApplicationArn' => [ 'shape' => 'IdentityCenterApplicationArn', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'NextItemOffset' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 0, ], 'OutputRelativeDirectoriesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputRelativeDirectoriesListMemberString', ], 'max' => 100, 'min' => 0, ], 'OutputRelativeDirectoriesListMemberString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ParameterFilterExpression' => [ 'type' => 'structure', 'required' => [ 'name', 'operator', 'value', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'operator' => [ 'shape' => 'ComparisonOperator', ], 'value' => [ 'shape' => 'ParameterValue', ], ], ], 'ParameterSortExpression' => [ 'type' => 'structure', 'required' => [ 'sortOrder', 'name', ], 'members' => [ 'sortOrder' => [ 'shape' => 'SortOrder', ], 'name' => [ 'shape' => 'String', ], ], ], 'ParameterSpace' => [ 'type' => 'structure', 'required' => [ 'parameters', ], 'members' => [ 'parameters' => [ 'shape' => 'StepParameterList', ], 'combination' => [ 'shape' => 'CombinationExpression', ], ], ], 'ParameterString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'ParameterValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'PathFormat' => [ 'type' => 'string', 'enum' => [ 'windows', 'posix', ], ], 'PathMappingRule' => [ 'type' => 'structure', 'required' => [ 'sourcePathFormat', 'sourcePath', 'destinationPath', ], 'members' => [ 'sourcePathFormat' => [ 'shape' => 'PathFormat', ], 'sourcePath' => [ 'shape' => 'String', ], 'destinationPath' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'PathMappingRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'PathMappingRule', ], ], 'PathString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'Period' => [ 'type' => 'string', 'enum' => [ 'HOURLY', 'DAILY', 'WEEKLY', 'MONTHLY', ], ], 'PortNumber' => [ 'type' => 'integer', 'box' => true, 'max' => 65535, 'min' => 1024, ], 'PosixUser' => [ 'type' => 'structure', 'required' => [ 'user', 'group', ], 'members' => [ 'user' => [ 'shape' => 'PosixUserUserString', ], 'group' => [ 'shape' => 'PosixUserGroupString', ], ], ], 'PosixUserGroupString' => [ 'type' => 'string', 'max' => 31, 'min' => 0, 'pattern' => '(?:[a-z][a-z0-9-]{0,30})?', ], 'PosixUserUserString' => [ 'type' => 'string', 'max' => 31, 'min' => 0, 'pattern' => '(?:[a-z][a-z0-9-]{0,30})?', ], 'PrincipalType' => [ 'type' => 'string', 'enum' => [ 'USER', 'GROUP', ], ], 'Priority' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 0, ], 'ProcessExitCode' => [ 'type' => 'integer', 'box' => true, 'max' => **********, 'min' => -**********, ], 'PutMeteredProductRequest' => [ 'type' => 'structure', 'required' => [ 'licenseEndpointId', 'productId', ], 'members' => [ 'licenseEndpointId' => [ 'shape' => 'LicenseEndpointId', 'location' => 'uri', 'locationName' => 'licenseEndpointId', ], 'productId' => [ 'shape' => 'MeteredProductId', 'location' => 'uri', 'locationName' => 'productId', ], ], ], 'PutMeteredProductResponse' => [ 'type' => 'structure', 'members' => [], ], 'QueueBlockedReason' => [ 'type' => 'string', 'enum' => [ 'NO_BUDGET_CONFIGURED', 'BUDGET_THRESHOLD_REACHED', ], ], 'QueueEnvironmentId' => [ 'type' => 'string', 'pattern' => 'queueenv-[0-9a-f]{32}', ], 'QueueEnvironmentSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueEnvironmentSummary', ], ], 'QueueEnvironmentSummary' => [ 'type' => 'structure', 'required' => [ 'queueEnvironmentId', 'name', 'priority', ], 'members' => [ 'queueEnvironmentId' => [ 'shape' => 'QueueEnvironmentId', ], 'name' => [ 'shape' => 'EnvironmentName', ], 'priority' => [ 'shape' => 'Priority', ], ], ], 'QueueFleetAssociationStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'STOP_SCHEDULING_AND_COMPLETE_TASKS', 'STOP_SCHEDULING_AND_CANCEL_TASKS', 'STOPPED', ], ], 'QueueFleetAssociationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueFleetAssociationSummary', ], ], 'QueueFleetAssociationSummary' => [ 'type' => 'structure', 'required' => [ 'queueId', 'fleetId', 'status', 'createdAt', 'createdBy', ], 'members' => [ 'queueId' => [ 'shape' => 'QueueId', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'status' => [ 'shape' => 'QueueFleetAssociationStatus', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'QueueId' => [ 'type' => 'string', 'pattern' => 'queue-[0-9a-f]{32}', ], 'QueueMember' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'principalId', 'principalType', 'identityStoreId', 'membershipLevel', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', ], 'queueId' => [ 'shape' => 'QueueId', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', ], 'principalType' => [ 'shape' => 'PrincipalType', ], 'identityStoreId' => [ 'shape' => 'IdentityStoreId', ], 'membershipLevel' => [ 'shape' => 'MembershipLevel', ], ], ], 'QueueMemberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueMember', ], ], 'QueueStatus' => [ 'type' => 'string', 'enum' => [ 'IDLE', 'SCHEDULING', 'SCHEDULING_BLOCKED', ], ], 'QueueSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueSummary', ], ], 'QueueSummary' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'displayName', 'status', 'defaultBudgetAction', 'createdAt', 'createdBy', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', ], 'queueId' => [ 'shape' => 'QueueId', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'status' => [ 'shape' => 'QueueStatus', ], 'defaultBudgetAction' => [ 'shape' => 'DefaultQueueBudgetAction', ], 'blockedReason' => [ 'shape' => 'QueueBlockedReason', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'RequiredFileSystemLocationNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileSystemLocationName', ], 'max' => 20, 'min' => 0, ], 'ResourceName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'context' => [ 'shape' => 'ExceptionContext', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResponseBudgetAction' => [ 'type' => 'structure', 'required' => [ 'type', 'thresholdPercentage', ], 'members' => [ 'type' => [ 'shape' => 'BudgetActionType', ], 'thresholdPercentage' => [ 'shape' => 'ThresholdPercentage', ], 'description' => [ 'shape' => 'Description', ], ], ], 'ResponseBudgetActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponseBudgetAction', ], 'max' => 10, 'min' => 0, ], 'RunAs' => [ 'type' => 'string', 'enum' => [ 'QUEUE_CONFIGURED_USER', 'WORKER_AGENT_USER', ], ], 'S3BucketName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'S3Key' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'S3Location' => [ 'type' => 'structure', 'required' => [ 'bucketName', 'key', ], 'members' => [ 'bucketName' => [ 'shape' => 'S3BucketName', ], 'key' => [ 'shape' => 'S3Key', ], ], ], 'S3Prefix' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '[a-zA-Z0-9-_/]+', ], 'SearchFilterExpression' => [ 'type' => 'structure', 'members' => [ 'dateTimeFilter' => [ 'shape' => 'DateTimeFilterExpression', ], 'parameterFilter' => [ 'shape' => 'ParameterFilterExpression', ], 'searchTermFilter' => [ 'shape' => 'SearchTermFilterExpression', ], 'stringFilter' => [ 'shape' => 'StringFilterExpression', ], 'groupFilter' => [ 'shape' => 'SearchGroupedFilterExpressions', ], ], 'union' => true, ], 'SearchFilterExpressions' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchFilterExpression', ], 'max' => 3, 'min' => 1, ], 'SearchGroupedFilterExpressions' => [ 'type' => 'structure', 'required' => [ 'filters', 'operator', ], 'members' => [ 'filters' => [ 'shape' => 'SearchFilterExpressions', ], 'operator' => [ 'shape' => 'LogicalOperator', ], ], ], 'SearchJobsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueIds', 'itemOffset', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueIds' => [ 'shape' => 'SearchJobsRequestQueueIdsList', ], 'filterExpressions' => [ 'shape' => 'SearchGroupedFilterExpressions', ], 'sortExpressions' => [ 'shape' => 'SearchSortExpressions', ], 'itemOffset' => [ 'shape' => 'SearchJobsRequestItemOffsetInteger', ], 'pageSize' => [ 'shape' => 'SearchJobsRequestPageSizeInteger', ], ], ], 'SearchJobsRequestItemOffsetInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 0, ], 'SearchJobsRequestPageSizeInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'SearchJobsRequestQueueIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueId', ], 'max' => 10, 'min' => 1, ], 'SearchJobsResponse' => [ 'type' => 'structure', 'required' => [ 'jobs', 'totalResults', ], 'members' => [ 'jobs' => [ 'shape' => 'JobSearchSummaries', ], 'nextItemOffset' => [ 'shape' => 'NextItemOffset', ], 'totalResults' => [ 'shape' => 'TotalResults', ], ], ], 'SearchSortExpression' => [ 'type' => 'structure', 'members' => [ 'userJobsFirst' => [ 'shape' => 'UserJobsFirst', ], 'fieldSort' => [ 'shape' => 'FieldSortExpression', ], 'parameterSort' => [ 'shape' => 'ParameterSortExpression', ], ], 'union' => true, ], 'SearchSortExpressions' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchSortExpression', ], 'max' => 5, 'min' => 1, ], 'SearchStepsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueIds', 'itemOffset', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueIds' => [ 'shape' => 'SearchStepsRequestQueueIdsList', ], 'jobId' => [ 'shape' => 'JobId', ], 'filterExpressions' => [ 'shape' => 'SearchGroupedFilterExpressions', ], 'sortExpressions' => [ 'shape' => 'SearchSortExpressions', ], 'itemOffset' => [ 'shape' => 'SearchStepsRequestItemOffsetInteger', ], 'pageSize' => [ 'shape' => 'SearchStepsRequestPageSizeInteger', ], ], ], 'SearchStepsRequestItemOffsetInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 0, ], 'SearchStepsRequestPageSizeInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'SearchStepsRequestQueueIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueId', ], 'max' => 10, 'min' => 1, ], 'SearchStepsResponse' => [ 'type' => 'structure', 'required' => [ 'steps', 'totalResults', ], 'members' => [ 'steps' => [ 'shape' => 'StepSearchSummaries', ], 'nextItemOffset' => [ 'shape' => 'NextItemOffset', ], 'totalResults' => [ 'shape' => 'TotalResults', ], ], ], 'SearchTasksRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueIds', 'itemOffset', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueIds' => [ 'shape' => 'SearchTasksRequestQueueIdsList', ], 'jobId' => [ 'shape' => 'JobId', ], 'filterExpressions' => [ 'shape' => 'SearchGroupedFilterExpressions', ], 'sortExpressions' => [ 'shape' => 'SearchSortExpressions', ], 'itemOffset' => [ 'shape' => 'SearchTasksRequestItemOffsetInteger', ], 'pageSize' => [ 'shape' => 'SearchTasksRequestPageSizeInteger', ], ], ], 'SearchTasksRequestItemOffsetInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 0, ], 'SearchTasksRequestPageSizeInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'SearchTasksRequestQueueIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueId', ], 'max' => 10, 'min' => 1, ], 'SearchTasksResponse' => [ 'type' => 'structure', 'required' => [ 'tasks', 'totalResults', ], 'members' => [ 'tasks' => [ 'shape' => 'TaskSearchSummaries', ], 'nextItemOffset' => [ 'shape' => 'NextItemOffset', ], 'totalResults' => [ 'shape' => 'TotalResults', ], ], ], 'SearchTerm' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'SearchTermFilterExpression' => [ 'type' => 'structure', 'required' => [ 'searchTerm', ], 'members' => [ 'searchTerm' => [ 'shape' => 'SearchTerm', ], ], ], 'SearchWorkersRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetIds', 'itemOffset', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetIds' => [ 'shape' => 'SearchWorkersRequestFleetIdsList', ], 'filterExpressions' => [ 'shape' => 'SearchGroupedFilterExpressions', ], 'sortExpressions' => [ 'shape' => 'SearchSortExpressions', ], 'itemOffset' => [ 'shape' => 'SearchWorkersRequestItemOffsetInteger', ], 'pageSize' => [ 'shape' => 'SearchWorkersRequestPageSizeInteger', ], ], ], 'SearchWorkersRequestFleetIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetId', ], 'max' => 10, 'min' => 1, ], 'SearchWorkersRequestItemOffsetInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 0, ], 'SearchWorkersRequestPageSizeInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'SearchWorkersResponse' => [ 'type' => 'structure', 'required' => [ 'workers', 'totalResults', ], 'members' => [ 'workers' => [ 'shape' => 'WorkerSearchSummaries', ], 'nextItemOffset' => [ 'shape' => 'NextItemOffset', ], 'totalResults' => [ 'shape' => 'TotalResults', ], ], ], 'SecretAccessKey' => [ 'type' => 'string', 'sensitive' => true, ], 'SecurityGroupId' => [ 'type' => 'string', 'pattern' => 'sg-[\\w]{1,120}', ], 'ServiceManagedEc2FleetConfiguration' => [ 'type' => 'structure', 'required' => [ 'instanceCapabilities', 'instanceMarketOptions', ], 'members' => [ 'instanceCapabilities' => [ 'shape' => 'ServiceManagedEc2InstanceCapabilities', ], 'instanceMarketOptions' => [ 'shape' => 'ServiceManagedEc2InstanceMarketOptions', ], ], ], 'ServiceManagedEc2InstanceCapabilities' => [ 'type' => 'structure', 'required' => [ 'vCpuCount', 'memoryMiB', 'osFamily', 'cpuArchitectureType', ], 'members' => [ 'vCpuCount' => [ 'shape' => 'VCpuCountRange', ], 'memoryMiB' => [ 'shape' => 'MemoryMiBRange', ], 'osFamily' => [ 'shape' => 'ServiceManagedFleetOperatingSystemFamily', ], 'cpuArchitectureType' => [ 'shape' => 'CpuArchitectureType', ], 'rootEbsVolume' => [ 'shape' => 'Ec2EbsVolume', ], 'allowedInstanceTypes' => [ 'shape' => 'InstanceTypes', ], 'excludedInstanceTypes' => [ 'shape' => 'InstanceTypes', ], 'customAmounts' => [ 'shape' => 'CustomFleetAmountCapabilities', ], 'customAttributes' => [ 'shape' => 'CustomFleetAttributeCapabilities', ], ], ], 'ServiceManagedEc2InstanceMarketOptions' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'Ec2MarketType', ], ], ], 'ServiceManagedFleetOperatingSystemFamily' => [ 'type' => 'string', 'enum' => [ 'WINDOWS', 'LINUX', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', 'resourceType', 'serviceCode', 'quotaCode', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ServiceQuotaExceededExceptionReason', ], 'resourceType' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'context' => [ 'shape' => 'ExceptionContext', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'ServiceQuotaExceededExceptionReason' => [ 'type' => 'string', 'enum' => [ 'SERVICE_QUOTA_EXCEEDED_EXCEPTION', 'KMS_KEY_LIMIT_EXCEEDED', ], ], 'SessionActionDefinition' => [ 'type' => 'structure', 'members' => [ 'envEnter' => [ 'shape' => 'EnvironmentEnterSessionActionDefinition', ], 'envExit' => [ 'shape' => 'EnvironmentExitSessionActionDefinition', ], 'taskRun' => [ 'shape' => 'TaskRunSessionActionDefinition', ], 'syncInputJobAttachments' => [ 'shape' => 'SyncInputJobAttachmentsSessionActionDefinition', ], ], 'union' => true, ], 'SessionActionDefinitionSummary' => [ 'type' => 'structure', 'members' => [ 'envEnter' => [ 'shape' => 'EnvironmentEnterSessionActionDefinitionSummary', ], 'envExit' => [ 'shape' => 'EnvironmentExitSessionActionDefinitionSummary', ], 'taskRun' => [ 'shape' => 'TaskRunSessionActionDefinitionSummary', ], 'syncInputJobAttachments' => [ 'shape' => 'SyncInputJobAttachmentsSessionActionDefinitionSummary', ], ], 'union' => true, ], 'SessionActionId' => [ 'type' => 'string', 'pattern' => 'sessionaction-[0-9a-f]{32}-(0|([1-9][0-9]{0,9}))', ], 'SessionActionIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SessionActionId', ], 'max' => 100, 'min' => 0, ], 'SessionActionProgressMessage' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'sensitive' => true, ], 'SessionActionProgressPercent' => [ 'type' => 'float', 'box' => true, 'max' => 100, 'min' => 0, ], 'SessionActionStatus' => [ 'type' => 'string', 'enum' => [ 'ASSIGNED', 'RUNNING', 'CANCELING', 'SUCCEEDED', 'FAILED', 'INTERRUPTED', 'CANCELED', 'NEVER_ATTEMPTED', 'SCHEDULED', 'RECLAIMING', 'RECLAIMED', ], ], 'SessionActionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SessionActionSummary', ], ], 'SessionActionSummary' => [ 'type' => 'structure', 'required' => [ 'sessionActionId', 'status', 'definition', ], 'members' => [ 'sessionActionId' => [ 'shape' => 'SessionActionId', ], 'status' => [ 'shape' => 'SessionActionStatus', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'workerUpdatedAt' => [ 'shape' => 'Timestamp', ], 'progressPercent' => [ 'shape' => 'SessionActionProgressPercent', ], 'definition' => [ 'shape' => 'SessionActionDefinitionSummary', ], ], ], 'SessionId' => [ 'type' => 'string', 'pattern' => 'session-[0-9a-f]{32}', ], 'SessionLifecycleStatus' => [ 'type' => 'string', 'enum' => [ 'STARTED', 'UPDATE_IN_PROGRESS', 'UPDATE_SUCCEEDED', 'UPDATE_FAILED', 'ENDED', ], ], 'SessionLifecycleTargetStatus' => [ 'type' => 'string', 'enum' => [ 'ENDED', ], ], 'SessionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SessionSummary', ], ], 'SessionSummary' => [ 'type' => 'structure', 'required' => [ 'sessionId', 'fleetId', 'workerId', 'startedAt', 'lifecycleStatus', ], 'members' => [ 'sessionId' => [ 'shape' => 'SessionId', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'workerId' => [ 'shape' => 'WorkerId', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'lifecycleStatus' => [ 'shape' => 'SessionLifecycleStatus', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'targetLifecycleStatus' => [ 'shape' => 'SessionLifecycleTargetStatus', ], ], ], 'SessionToken' => [ 'type' => 'string', 'sensitive' => true, ], 'SessionsStatisticsAggregationStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'TIMEOUT', 'FAILED', 'COMPLETED', ], ], 'SessionsStatisticsResources' => [ 'type' => 'structure', 'members' => [ 'queueIds' => [ 'shape' => 'SessionsStatisticsResourcesQueueIdsList', ], 'fleetIds' => [ 'shape' => 'SessionsStatisticsResourcesFleetIdsList', ], ], 'union' => true, ], 'SessionsStatisticsResourcesFleetIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetId', ], 'max' => 10, 'min' => 1, ], 'SessionsStatisticsResourcesQueueIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueId', ], 'max' => 10, 'min' => 1, ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'StartSessionsStatisticsAggregationRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'resourceIds', 'startTime', 'endTime', 'groupBy', 'statistics', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'resourceIds' => [ 'shape' => 'SessionsStatisticsResources', ], 'startTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'endTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'timezone' => [ 'shape' => 'Timezone', ], 'period' => [ 'shape' => 'Period', ], 'groupBy' => [ 'shape' => 'UsageGroupBy', ], 'statistics' => [ 'shape' => 'UsageStatistics', ], ], ], 'StartSessionsStatisticsAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'aggregationId', ], 'members' => [ 'aggregationId' => [ 'shape' => 'AggregationId', ], ], ], 'StartedAt' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'StartsAt' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'Statistics' => [ 'type' => 'structure', 'required' => [ 'count', 'costInUsd', 'runtimeInSeconds', ], 'members' => [ 'queueId' => [ 'shape' => 'QueueId', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'jobId' => [ 'shape' => 'JobId', ], 'jobName' => [ 'shape' => 'JobName', ], 'userId' => [ 'shape' => 'UserId', ], 'usageType' => [ 'shape' => 'UsageType', ], 'licenseProduct' => [ 'shape' => 'LicenseProduct', ], 'instanceType' => [ 'shape' => 'InstanceType', ], 'count' => [ 'shape' => 'Integer', ], 'costInUsd' => [ 'shape' => 'Stats', ], 'runtimeInSeconds' => [ 'shape' => 'Stats', ], 'aggregationStartTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'aggregationEndTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'StatisticsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Statistics', ], ], 'Stats' => [ 'type' => 'structure', 'members' => [ 'min' => [ 'shape' => 'Double', ], 'max' => [ 'shape' => 'Double', ], 'avg' => [ 'shape' => 'Double', ], 'sum' => [ 'shape' => 'Double', ], ], ], 'StatusMessage' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'StepAmountCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepAmountCapability', ], ], 'StepAmountCapability' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'AmountCapabilityName', ], 'min' => [ 'shape' => 'Double', ], 'max' => [ 'shape' => 'Double', ], 'value' => [ 'shape' => 'Double', ], ], ], 'StepAttributeCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepAttributeCapability', ], ], 'StepAttributeCapability' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'AttributeCapabilityName', ], 'anyOf' => [ 'shape' => 'ListAttributeCapabilityValue', ], 'allOf' => [ 'shape' => 'ListAttributeCapabilityValue', ], ], ], 'StepConsumer' => [ 'type' => 'structure', 'required' => [ 'stepId', 'status', ], 'members' => [ 'stepId' => [ 'shape' => 'StepId', ], 'status' => [ 'shape' => 'DependencyConsumerResolutionStatus', ], ], ], 'StepConsumers' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepConsumer', ], ], 'StepDependencies' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepDependency', ], ], 'StepDependency' => [ 'type' => 'structure', 'required' => [ 'stepId', 'status', ], 'members' => [ 'stepId' => [ 'shape' => 'StepId', ], 'status' => [ 'shape' => 'DependencyConsumerResolutionStatus', ], ], ], 'StepDescription' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'sensitive' => true, ], 'StepDetailsEntity' => [ 'type' => 'structure', 'required' => [ 'jobId', 'stepId', 'schemaVersion', 'template', 'dependencies', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'stepId' => [ 'shape' => 'StepId', ], 'schemaVersion' => [ 'shape' => 'String', ], 'template' => [ 'shape' => 'Document', ], 'dependencies' => [ 'shape' => 'DependenciesList', ], ], ], 'StepDetailsError' => [ 'type' => 'structure', 'required' => [ 'jobId', 'stepId', 'code', 'message', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'stepId' => [ 'shape' => 'StepId', ], 'code' => [ 'shape' => 'JobEntityErrorCode', ], 'message' => [ 'shape' => 'String', ], ], ], 'StepDetailsIdentifiers' => [ 'type' => 'structure', 'required' => [ 'jobId', 'stepId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'stepId' => [ 'shape' => 'StepId', ], ], ], 'StepId' => [ 'type' => 'string', 'pattern' => 'step-[0-9a-f]{32}', ], 'StepLifecycleStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_COMPLETE', 'UPDATE_IN_PROGRESS', 'UPDATE_FAILED', 'UPDATE_SUCCEEDED', ], ], 'StepName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'StepParameter' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'name' => [ 'shape' => 'StepParameterName', ], 'type' => [ 'shape' => 'StepParameterType', ], ], ], 'StepParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepParameter', ], ], 'StepParameterName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'StepParameterType' => [ 'type' => 'string', 'enum' => [ 'INT', 'FLOAT', 'STRING', 'PATH', ], ], 'StepRequiredCapabilities' => [ 'type' => 'structure', 'required' => [ 'attributes', 'amounts', ], 'members' => [ 'attributes' => [ 'shape' => 'StepAttributeCapabilities', ], 'amounts' => [ 'shape' => 'StepAmountCapabilities', ], ], ], 'StepSearchSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepSearchSummary', ], ], 'StepSearchSummary' => [ 'type' => 'structure', 'members' => [ 'stepId' => [ 'shape' => 'StepId', ], 'jobId' => [ 'shape' => 'JobId', ], 'queueId' => [ 'shape' => 'QueueId', ], 'name' => [ 'shape' => 'StepName', ], 'lifecycleStatus' => [ 'shape' => 'StepLifecycleStatus', ], 'lifecycleStatusMessage' => [ 'shape' => 'String', ], 'taskRunStatus' => [ 'shape' => 'TaskRunStatus', ], 'targetTaskRunStatus' => [ 'shape' => 'StepTargetTaskRunStatus', ], 'taskRunStatusCounts' => [ 'shape' => 'TaskRunStatusCounts', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'parameterSpace' => [ 'shape' => 'ParameterSpace', ], ], ], 'StepSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepSummary', ], ], 'StepSummary' => [ 'type' => 'structure', 'required' => [ 'stepId', 'name', 'lifecycleStatus', 'taskRunStatus', 'taskRunStatusCounts', 'createdAt', 'createdBy', ], 'members' => [ 'stepId' => [ 'shape' => 'StepId', ], 'name' => [ 'shape' => 'StepName', ], 'lifecycleStatus' => [ 'shape' => 'StepLifecycleStatus', ], 'lifecycleStatusMessage' => [ 'shape' => 'String', ], 'taskRunStatus' => [ 'shape' => 'TaskRunStatus', ], 'taskRunStatusCounts' => [ 'shape' => 'TaskRunStatusCounts', ], 'targetTaskRunStatus' => [ 'shape' => 'StepTargetTaskRunStatus', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'dependencyCounts' => [ 'shape' => 'DependencyCounts', ], ], ], 'StepTargetTaskRunStatus' => [ 'type' => 'string', 'enum' => [ 'READY', 'FAILED', 'SUCCEEDED', 'CANCELED', 'SUSPENDED', 'PENDING', ], ], 'StorageProfileId' => [ 'type' => 'string', 'pattern' => 'sp-[0-9a-f]{32}', ], 'StorageProfileOperatingSystemFamily' => [ 'type' => 'string', 'enum' => [ 'WINDOWS', 'LINUX', 'MACOS', ], ], 'StorageProfileSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageProfileSummary', ], ], 'StorageProfileSummary' => [ 'type' => 'structure', 'required' => [ 'storageProfileId', 'displayName', 'osFamily', ], 'members' => [ 'storageProfileId' => [ 'shape' => 'StorageProfileId', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'osFamily' => [ 'shape' => 'StorageProfileOperatingSystemFamily', ], ], ], 'String' => [ 'type' => 'string', ], 'StringFilter' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'StringFilterExpression' => [ 'type' => 'structure', 'required' => [ 'name', 'operator', 'value', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'operator' => [ 'shape' => 'ComparisonOperator', ], 'value' => [ 'shape' => 'StringFilter', ], ], ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Subdomain' => [ 'type' => 'string', 'pattern' => '[a-z0-9-]{1,100}', ], 'SubnetId' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => 'subnet-[\\w]{1,120}', ], 'SyncInputJobAttachmentsSessionActionDefinition' => [ 'type' => 'structure', 'members' => [ 'stepId' => [ 'shape' => 'StepId', ], ], ], 'SyncInputJobAttachmentsSessionActionDefinitionSummary' => [ 'type' => 'structure', 'members' => [ 'stepId' => [ 'shape' => 'StepId', ], ], ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'TaskId' => [ 'type' => 'string', 'pattern' => 'task-[0-9a-f]{32}-(0|([1-9][0-9]{0,9}))', ], 'TaskParameterValue' => [ 'type' => 'structure', 'members' => [ 'int' => [ 'shape' => 'IntString', ], 'float' => [ 'shape' => 'FloatString', ], 'string' => [ 'shape' => 'ParameterString', ], 'path' => [ 'shape' => 'PathString', ], ], 'sensitive' => true, 'union' => true, ], 'TaskParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'TaskParameterValue', ], 'sensitive' => true, ], 'TaskRetryCount' => [ 'type' => 'integer', 'box' => true, 'max' => **********, 'min' => 0, ], 'TaskRunSessionActionDefinition' => [ 'type' => 'structure', 'required' => [ 'taskId', 'stepId', 'parameters', ], 'members' => [ 'taskId' => [ 'shape' => 'TaskId', ], 'stepId' => [ 'shape' => 'StepId', ], 'parameters' => [ 'shape' => 'TaskParameters', ], ], ], 'TaskRunSessionActionDefinitionSummary' => [ 'type' => 'structure', 'required' => [ 'taskId', 'stepId', ], 'members' => [ 'taskId' => [ 'shape' => 'TaskId', ], 'stepId' => [ 'shape' => 'StepId', ], ], ], 'TaskRunStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'READY', 'ASSIGNED', 'STARTING', 'SCHEDULED', 'INTERRUPTING', 'RUNNING', 'SUSPENDED', 'CANCELED', 'FAILED', 'SUCCEEDED', 'NOT_COMPATIBLE', ], ], 'TaskRunStatusCounts' => [ 'type' => 'map', 'key' => [ 'shape' => 'TaskRunStatus', ], 'value' => [ 'shape' => 'Integer', ], ], 'TaskSearchSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaskSearchSummary', ], ], 'TaskSearchSummary' => [ 'type' => 'structure', 'members' => [ 'taskId' => [ 'shape' => 'TaskId', ], 'stepId' => [ 'shape' => 'StepId', ], 'jobId' => [ 'shape' => 'JobId', ], 'queueId' => [ 'shape' => 'QueueId', ], 'runStatus' => [ 'shape' => 'TaskRunStatus', ], 'targetRunStatus' => [ 'shape' => 'TaskTargetRunStatus', ], 'parameters' => [ 'shape' => 'TaskParameters', ], 'failureRetryCount' => [ 'shape' => 'TaskRetryCount', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'endedAt' => [ 'shape' => 'EndedAt', ], ], ], 'TaskSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaskSummary', ], ], 'TaskSummary' => [ 'type' => 'structure', 'required' => [ 'taskId', 'createdAt', 'createdBy', 'runStatus', ], 'members' => [ 'taskId' => [ 'shape' => 'TaskId', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'runStatus' => [ 'shape' => 'TaskRunStatus', ], 'targetRunStatus' => [ 'shape' => 'TaskTargetRunStatus', ], 'failureRetryCount' => [ 'shape' => 'TaskRetryCount', ], 'parameters' => [ 'shape' => 'TaskParameters', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'latestSessionActionId' => [ 'shape' => 'SessionActionId', ], ], ], 'TaskTargetRunStatus' => [ 'type' => 'string', 'enum' => [ 'READY', 'FAILED', 'SUCCEEDED', 'CANCELED', 'SUSPENDED', 'PENDING', ], ], 'ThresholdPercentage' => [ 'type' => 'float', 'box' => true, 'max' => 100, 'min' => 0, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], 'context' => [ 'shape' => 'ExceptionContext', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'Timezone' => [ 'type' => 'string', 'max' => 9, 'min' => 9, 'pattern' => 'UTC[-+][01][0-9]:(30|00)', ], 'TotalResults' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 0, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'StringList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateBudgetRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'budgetId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'budgetId' => [ 'shape' => 'BudgetId', 'location' => 'uri', 'locationName' => 'budgetId', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'Description', ], 'status' => [ 'shape' => 'BudgetStatus', ], 'approximateDollarLimit' => [ 'shape' => 'ConsumedUsageLimit', ], 'actionsToAdd' => [ 'shape' => 'BudgetActionsToAdd', ], 'actionsToRemove' => [ 'shape' => 'BudgetActionsToRemove', ], 'schedule' => [ 'shape' => 'BudgetSchedule', ], ], ], 'UpdateBudgetResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateFarmRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'Description', ], ], ], 'UpdateFarmResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateFleetRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'Description', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'minWorkerCount' => [ 'shape' => 'MinZeroMaxInteger', ], 'maxWorkerCount' => [ 'shape' => 'MinZeroMaxInteger', ], 'configuration' => [ 'shape' => 'FleetConfiguration', ], ], ], 'UpdateFleetResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateJobLifecycleStatus' => [ 'type' => 'string', 'enum' => [ 'ARCHIVED', ], ], 'UpdateJobRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'jobId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'targetTaskRunStatus' => [ 'shape' => 'JobTargetTaskRunStatus', ], 'priority' => [ 'shape' => 'JobPriority', ], 'maxFailedTasksCount' => [ 'shape' => 'MaxFailedTasksCount', ], 'maxRetriesPerTask' => [ 'shape' => 'MaxRetriesPerTask', ], 'lifecycleStatus' => [ 'shape' => 'UpdateJobLifecycleStatus', ], ], ], 'UpdateJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateMonitorRequest' => [ 'type' => 'structure', 'required' => [ 'monitorId', ], 'members' => [ 'monitorId' => [ 'shape' => 'MonitorId', 'location' => 'uri', 'locationName' => 'monitorId', ], 'subdomain' => [ 'shape' => 'Subdomain', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'UpdateMonitorResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateQueueEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'queueEnvironmentId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'queueEnvironmentId' => [ 'shape' => 'QueueEnvironmentId', 'location' => 'uri', 'locationName' => 'queueEnvironmentId', ], 'priority' => [ 'shape' => 'Priority', ], 'templateType' => [ 'shape' => 'EnvironmentTemplateType', ], 'template' => [ 'shape' => 'EnvironmentTemplate', ], ], ], 'UpdateQueueEnvironmentResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateQueueFleetAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'fleetId', 'status', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'status' => [ 'shape' => 'UpdateQueueFleetAssociationStatus', ], ], ], 'UpdateQueueFleetAssociationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateQueueFleetAssociationStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'STOP_SCHEDULING_AND_COMPLETE_TASKS', 'STOP_SCHEDULING_AND_CANCEL_TASKS', ], ], 'UpdateQueueRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'Description', ], 'defaultBudgetAction' => [ 'shape' => 'DefaultQueueBudgetAction', ], 'jobAttachmentSettings' => [ 'shape' => 'JobAttachmentSettings', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'jobRunAsUser' => [ 'shape' => 'JobRunAsUser', ], 'requiredFileSystemLocationNamesToAdd' => [ 'shape' => 'RequiredFileSystemLocationNames', ], 'requiredFileSystemLocationNamesToRemove' => [ 'shape' => 'RequiredFileSystemLocationNames', ], 'allowedStorageProfileIdsToAdd' => [ 'shape' => 'AllowedStorageProfileIds', ], 'allowedStorageProfileIdsToRemove' => [ 'shape' => 'AllowedStorageProfileIds', ], ], ], 'UpdateQueueResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateSessionRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'jobId', 'sessionId', 'targetLifecycleStatus', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'sessionId' => [ 'shape' => 'SessionId', 'location' => 'uri', 'locationName' => 'sessionId', ], 'targetLifecycleStatus' => [ 'shape' => 'SessionLifecycleTargetStatus', ], ], ], 'UpdateSessionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateStepRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'jobId', 'stepId', 'targetTaskRunStatus', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'stepId' => [ 'shape' => 'StepId', 'location' => 'uri', 'locationName' => 'stepId', ], 'targetTaskRunStatus' => [ 'shape' => 'StepTargetTaskRunStatus', ], ], ], 'UpdateStepResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateStorageProfileRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'storageProfileId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'storageProfileId' => [ 'shape' => 'StorageProfileId', 'location' => 'uri', 'locationName' => 'storageProfileId', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'osFamily' => [ 'shape' => 'StorageProfileOperatingSystemFamily', ], 'fileSystemLocationsToAdd' => [ 'shape' => 'FileSystemLocationsList', ], 'fileSystemLocationsToRemove' => [ 'shape' => 'FileSystemLocationsList', ], ], ], 'UpdateStorageProfileResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTaskRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'jobId', 'stepId', 'taskId', 'targetRunStatus', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'stepId' => [ 'shape' => 'StepId', 'location' => 'uri', 'locationName' => 'stepId', ], 'taskId' => [ 'shape' => 'TaskId', 'location' => 'uri', 'locationName' => 'taskId', ], 'targetRunStatus' => [ 'shape' => 'TaskTargetRunStatus', ], ], ], 'UpdateTaskResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateWorkerRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'workerId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'workerId' => [ 'shape' => 'WorkerId', 'location' => 'uri', 'locationName' => 'workerId', ], 'status' => [ 'shape' => 'UpdatedWorkerStatus', ], 'capabilities' => [ 'shape' => 'WorkerCapabilities', ], 'hostProperties' => [ 'shape' => 'HostPropertiesRequest', ], ], ], 'UpdateWorkerResponse' => [ 'type' => 'structure', 'members' => [ 'log' => [ 'shape' => 'LogConfiguration', ], ], ], 'UpdateWorkerScheduleInterval' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'UpdateWorkerScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'workerId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'workerId' => [ 'shape' => 'WorkerId', 'location' => 'uri', 'locationName' => 'workerId', ], 'updatedSessionActions' => [ 'shape' => 'UpdatedSessionActions', ], ], ], 'UpdateWorkerScheduleResponse' => [ 'type' => 'structure', 'required' => [ 'assignedSessions', 'cancelSessionActions', 'updateIntervalSeconds', ], 'members' => [ 'assignedSessions' => [ 'shape' => 'AssignedSessions', ], 'cancelSessionActions' => [ 'shape' => 'CancelSessionActions', ], 'desiredWorkerStatus' => [ 'shape' => 'DesiredWorkerStatus', ], 'updateIntervalSeconds' => [ 'shape' => 'UpdateWorkerScheduleInterval', ], ], ], 'UpdatedAt' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'UpdatedBy' => [ 'type' => 'string', ], 'UpdatedSessionActionInfo' => [ 'type' => 'structure', 'members' => [ 'completedStatus' => [ 'shape' => 'CompletedStatus', ], 'processExitCode' => [ 'shape' => 'ProcessExitCode', ], 'progressMessage' => [ 'shape' => 'SessionActionProgressMessage', ], 'startedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'endedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'progressPercent' => [ 'shape' => 'SessionActionProgressPercent', ], ], ], 'UpdatedSessionActions' => [ 'type' => 'map', 'key' => [ 'shape' => 'SessionActionId', ], 'value' => [ 'shape' => 'UpdatedSessionActionInfo', ], ], 'UpdatedWorkerStatus' => [ 'type' => 'string', 'enum' => [ 'STARTED', 'STOPPING', 'STOPPED', ], ], 'Url' => [ 'type' => 'string', ], 'UsageGroupBy' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageGroupByField', ], 'max' => 2, 'min' => 1, ], 'UsageGroupByField' => [ 'type' => 'string', 'enum' => [ 'QUEUE_ID', 'FLEET_ID', 'JOB_ID', 'USER_ID', 'USAGE_TYPE', 'INSTANCE_TYPE', 'LICENSE_PRODUCT', ], ], 'UsageStatistic' => [ 'type' => 'string', 'enum' => [ 'SUM', 'MIN', 'MAX', 'AVG', ], ], 'UsageStatistics' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageStatistic', ], 'max' => 4, 'min' => 1, ], 'UsageTrackingResource' => [ 'type' => 'structure', 'members' => [ 'queueId' => [ 'shape' => 'QueueId', ], ], 'union' => true, ], 'UsageType' => [ 'type' => 'string', 'enum' => [ 'COMPUTE', 'LICENSE', ], ], 'UserId' => [ 'type' => 'string', ], 'UserJobsFirst' => [ 'type' => 'structure', 'required' => [ 'userIdentityId', ], 'members' => [ 'userIdentityId' => [ 'shape' => 'String', ], ], ], 'VCpuCountRange' => [ 'type' => 'structure', 'required' => [ 'min', ], 'members' => [ 'min' => [ 'shape' => 'MinOneMaxTenThousand', ], 'max' => [ 'shape' => 'MinOneMaxTenThousand', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], 'context' => [ 'shape' => 'ExceptionContext', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN_OPERATION', 'CANNOT_PARSE', 'FIELD_VALIDATION_FAILED', 'OTHER', ], ], 'VpcId' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => 'vpc-[\\w]{1,120}', ], 'WindowsUser' => [ 'type' => 'structure', 'required' => [ 'user', 'passwordArn', ], 'members' => [ 'user' => [ 'shape' => 'WindowsUserUserString', ], 'passwordArn' => [ 'shape' => 'WindowsUserPasswordArnString', ], ], ], 'WindowsUserPasswordArnString' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:(aws[a-zA-Z-]*):secretsmanager:[a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1}:\\d{12}:secret:[a-zA-Z0-9-/_+=.@]{1,2028}', ], 'WindowsUserUserString' => [ 'type' => 'string', 'max' => 111, 'min' => 0, 'pattern' => '[^"\'/\\[\\]:;|=,+*?<>\\s]*', ], 'WorkerAmountCapability' => [ 'type' => 'structure', 'required' => [ 'name', 'value', ], 'members' => [ 'name' => [ 'shape' => 'AmountCapabilityName', ], 'value' => [ 'shape' => 'Float', ], ], ], 'WorkerAmountCapabilityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkerAmountCapability', ], 'max' => 17, 'min' => 2, ], 'WorkerAttributeCapability' => [ 'type' => 'structure', 'required' => [ 'name', 'values', ], 'members' => [ 'name' => [ 'shape' => 'AttributeCapabilityName', ], 'values' => [ 'shape' => 'AttributeCapabilityValuesList', ], ], ], 'WorkerAttributeCapabilityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkerAttributeCapability', ], 'max' => 17, 'min' => 2, ], 'WorkerCapabilities' => [ 'type' => 'structure', 'required' => [ 'amounts', 'attributes', ], 'members' => [ 'amounts' => [ 'shape' => 'WorkerAmountCapabilityList', ], 'attributes' => [ 'shape' => 'WorkerAttributeCapabilityList', ], ], ], 'WorkerId' => [ 'type' => 'string', 'pattern' => 'worker-[0-9a-f]{32}', ], 'WorkerSearchSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkerSearchSummary', ], ], 'WorkerSearchSummary' => [ 'type' => 'structure', 'members' => [ 'fleetId' => [ 'shape' => 'FleetId', ], 'workerId' => [ 'shape' => 'WorkerId', ], 'status' => [ 'shape' => 'WorkerStatus', ], 'hostProperties' => [ 'shape' => 'HostPropertiesResponse', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], ], ], 'WorkerSessionSummary' => [ 'type' => 'structure', 'required' => [ 'sessionId', 'queueId', 'jobId', 'startedAt', 'lifecycleStatus', ], 'members' => [ 'sessionId' => [ 'shape' => 'SessionId', ], 'queueId' => [ 'shape' => 'QueueId', ], 'jobId' => [ 'shape' => 'JobId', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'lifecycleStatus' => [ 'shape' => 'SessionLifecycleStatus', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'targetLifecycleStatus' => [ 'shape' => 'SessionLifecycleTargetStatus', ], ], ], 'WorkerStatus' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'STARTED', 'STOPPING', 'STOPPED', 'NOT_RESPONDING', 'NOT_COMPATIBLE', 'RUNNING', 'IDLE', ], ], 'WorkerSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkerSummary', ], ], 'WorkerSummary' => [ 'type' => 'structure', 'required' => [ 'workerId', 'farmId', 'fleetId', 'status', 'createdAt', 'createdBy', ], 'members' => [ 'workerId' => [ 'shape' => 'WorkerId', ], 'farmId' => [ 'shape' => 'FarmId', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'status' => [ 'shape' => 'WorkerStatus', ], 'hostProperties' => [ 'shape' => 'HostPropertiesResponse', ], 'log' => [ 'shape' => 'LogConfiguration', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], ],];
