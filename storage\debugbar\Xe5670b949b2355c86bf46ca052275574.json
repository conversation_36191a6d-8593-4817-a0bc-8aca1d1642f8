{"__meta": {"id": "Xe5670b949b2355c86bf46ca052275574", "datetime": "2025-06-30 22:40:00", "utime": **********.042568, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323199.596922, "end": **********.042587, "duration": 0.4456651210784912, "duration_str": "446ms", "measures": [{"label": "Booting", "start": 1751323199.596922, "relative_start": 0, "end": 1751323199.985645, "relative_end": 1751323199.985645, "duration": 0.38872313499450684, "duration_str": "389ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751323199.985654, "relative_start": 0.3887321949005127, "end": **********.04259, "relative_end": 2.86102294921875e-06, "duration": 0.056935787200927734, "duration_str": "56.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45026552, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00313, "accumulated_duration_str": "3.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.017752, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.939}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0288, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.939, "width_percent": 17.891}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.034864, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.831, "width_percent": 19.169}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management/25\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-2010298698 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2010298698\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-100744472 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-100744472\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2127714818 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2127714818\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1871615390 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751323195209%7C14%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImM1dUdJVTgzRWpJTGRFbENZSmdlb2c9PSIsInZhbHVlIjoiWFJLVDgraFhXdlgxSE5oc1gyM1FUdVloNk1UVGVzVlprV0F6b2VRSFYxTjBQRzh0UFlWdldGcUFiQlZDSm5NUVhxSExWbjFpaGNIcjlRbjlwNmxvT0hoU0hmckE3NFYyS3NNU1lBMlFTM3R3UVEzL2pqRlpRaTdKWTU1WnVLSGU3YmphK3JqZzF4MFVydmlWRUpTUlQyUDd2VE9MTlBGZ1pyM3Q0TVFXMjF2NXlXN045eDBVTmtkd1k4VHZCOXNUSjlWcjZOakp0ZFRQbzYxOVVUeXh0UHBEcElBRWRyVEJWWlZyTk53c2FycVViTU43Z2ViN05OSkFReFdNTWVhSmoxdDJEUjFRY08rdVZ3eWlsWm4xRmZIQjhhZWlUckI0NzhjWWpkc29QNUhBNkRoYUt0d0kvckJtNmZ4ZUtVRW1lRW5rLzF3MlB0K3BSQjk5SjczLzNZeEhUTktkRUU2R09JVjFEa0JPSW5TMTlOMkdpT1AzajQ0RUVMYnQxdkczUlVoVTA0eVBkMUd0dWNhWmZTZWdTb0RCUGU2U1FoeHEydFUvTHJLc3N3MmlUUmJOSzVEOUpCejRxRmVvTmNIejFNbkdpaFJHanhpNHlwWkdmZE5maVFoKy9URGZYL0tkczRxQnNCblF0REIvbkZOT3BrbWpNWjlGQytEaUhJNkQiLCJtYWMiOiI2ZWIyMjE4NWNkMThlOWY3Mjc3OTczNmYzOTljN2I1NjhiN2FlMzUzZjQ4OWEzM2ExODBhZjU3MWRlMzZiNWNmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InhKK250Y0FhOGJ5aXNNWXhjOVNyTlE9PSIsInZhbHVlIjoiaVRobTkrNU1QakVIeHdwTmdTanVpN2ZvajFSWllZbFMrZFZlNUVrS1JsQUh0QmZNdU1qN1V2UE5Fd2tVYk56aXhmMzJQZ2lMcHdQNW9hSXNvY0pYRitsSFVtbjZkNFR2cXNjQjZ2ZUlDU0o0cmNKUTJ1dEt0azRJYTFEdnZTbGJuWkU3RVJkbHRQRmQ2cVNwdG92TjFPWnd1bEZPQS9QWjJ4K25xY045T1I4NFJKTVhlYU52V0hGUGdsZVJQbGFPVy9Fbm5aQTVNTTMvUkVSS243NllkSU1lYm9XOU1xWGVzVTRKTzBrOGhBZlBBWEt5THhTOE9kbThRUkFSOXNSbS9HUEJFUjBIaFgwY0tiSDZmOFVKaE13U1IwSHdLR3QzbkFtWS9mSlFnRlZ2b0tvOXhwRUxGT1VLeFN3S3BzcEtOVzdNd0szRVRkTFlWM0Vya0FGT1NScTJWTWt1azYxY0tYWmhzcEVpeHM3TDlrOWI5eGV0VEZLekR6czZMSWdZMjlvbGJhMUR0cFJKbDdVMEI2YnN5bExOcVdtNjg0dGNLZWEzeUN0cGtKdEVGa2hQNkJENEk0Vm91aEFibTVUUWUzUXl5NEc2M3dMQy9MM3czTERkdmw0ci94eWIxN2Fqbm92WU5WRUNhMTRZSnFoL1RlQTlueExCVGM0aHRLamsiLCJtYWMiOiIyM2JjNmUxYTNlNjhiMmE1YTAxNjAwMGIxMzkzNDU3NDQwN2ZkODU1NmI5MDA4NTdlYTEzYzkxNzgyZmRiYjA2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1871615390\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0M1K0sdNadPD2LVrnt8LmBw227nA9F1U5e3ROaJK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1228833320 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:40:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNHTkJQZ2xuWXdDQjk4SndvUHUvQkE9PSIsInZhbHVlIjoiWFZGazdmZE03WW12M1JVQVZYU1U0bWJRV1V4aHVOWlBUYXNGYlZBYWJMQUdQU0RQRkdubnRSTk0rRVgzNGRNNDR6azhTcm5hQzdPUjZhMUsyVnFKdUh5NWhmV2ZUc1MzK3cyKzcvV2ZCOHBDdmdqSllwR0ZhQVMzVVdrajZ0WkJXVWVOS3BQVUdndTM0U2FLclFNL2grVlhEdDY2VDdDYm9Rc3pJTU1ZbXVtS0ZUbnRFOXowUExLNWFoWVB4UTE4Tm5qb04zU2dYTVlTemplSjVKRE5vSEp3TTBIZmpRUFVSTVQ5UVB2VStUOXpIc1cxMlFGbHRhYVNtTi9zQlNQMG9NK2VBUFdybEVmVG9jWk9qT2NGWTFLZTVwN3p1SjFQalI3TWh5RHp1VU13Q3pvN3BUQTNqSmtGYXlVQ3RPeUVWN2xBT0FDWll5cWxPbkFiSmx1b0lQSmpSZEZLd2VJNnB0cWVCQUpWVTFFZlNWZlgxTGc3Zk05VnRiRDNZb3MzamhzN2FENE5XZTd6QlorcU5ObmxTdDhaY3pKRlB5ZU1Kc1pzcEJzSTdnNkpLWnp3UWNiRmdiL0J4dmhoOFZCOUJvOVpyMmdJeXkxcFdpTXROQk1kbUdNZjlmaVFIenlVbUtzak5LYS81ODVRU0hlUS9VRmlIQk1ITyt0SVhSN3ciLCJtYWMiOiIzYjg0YzA0OTA1NGY2MWQzZjBmYTkzZTMzYWJhMmY3NDc1MzI3MTE0OGE1ZGI5NjQxZmZjYTk0ODFjYzRiOTc3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjBiVFVOZmc4bFpnS0tOeEsxNXcrbXc9PSIsInZhbHVlIjoiUnNhNHNtL01SdjlmN0xnQVlQMG9LMS9HVGlWNUhMZS9ZcGNrbzc4WmtlNHU1eEgwazIyR0liNlNOS1Y3bnZaVWtGbElpL1dpL1lDOVY2SWl3Y1RNZi93cFdNckFvcFFNYUplR09LZjBsR0tXdUdKblFrWEFlQkFld25rTStNSUR2eUFpbUZJcnkwRXNaUnNRRVRoS084KzdOZ2FtOHZ6Qll4aFJndGI0MGw5V0MzL2dCTFV4OXVUNHV4UDcreHhvRmJNMzU2SkxTb2laRnBjTXMwejZ2UUJycUc4S1p4dE1jVHl3MjcwK2owUFBsckJVTmtpVzlUZVhyVEhhNG9wTzNNYW5PeW9NQjNQVEt3cWdabU52bE83NFQrNHIyazdpQ1VnUjVnQ0VFQzdpUWdDb0Z5YUQ1SXBtV1l4UXFuVEgwbFBJRW5kcWVuZHdJUHJPQnB3OUdhWnFYM3hCR054eUhlUFU1eDRHSGkxRE1ZOVdESnVoeWhjR2Rra3d1b0ljSEJwVGsyeE1Bam5UNzhkdHRVNVJxL1RSbktFOTBObFk4cHVUQlNNdjFSQ2JEZGVlZzlXeFVFNDdDdWZDMHBVRjQycTNsTjZxUlVrSzA3Yjk2dy9sQzdKNzArZ1pBdDVzV3FoOXd5dUVBSTFOUkVZNnBHZ1VhMnBiYW5tWFd1OGIiLCJtYWMiOiIzY2YxMzgxN2JjMDExZjA3YTBiMWNlOTdlYjBmZDhhMTc3YmJlNGI5Yzk1M2Y2ZTQ1YzNkMTdlZTU2YWNkZmEzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNHTkJQZ2xuWXdDQjk4SndvUHUvQkE9PSIsInZhbHVlIjoiWFZGazdmZE03WW12M1JVQVZYU1U0bWJRV1V4aHVOWlBUYXNGYlZBYWJMQUdQU0RQRkdubnRSTk0rRVgzNGRNNDR6azhTcm5hQzdPUjZhMUsyVnFKdUh5NWhmV2ZUc1MzK3cyKzcvV2ZCOHBDdmdqSllwR0ZhQVMzVVdrajZ0WkJXVWVOS3BQVUdndTM0U2FLclFNL2grVlhEdDY2VDdDYm9Rc3pJTU1ZbXVtS0ZUbnRFOXowUExLNWFoWVB4UTE4Tm5qb04zU2dYTVlTemplSjVKRE5vSEp3TTBIZmpRUFVSTVQ5UVB2VStUOXpIc1cxMlFGbHRhYVNtTi9zQlNQMG9NK2VBUFdybEVmVG9jWk9qT2NGWTFLZTVwN3p1SjFQalI3TWh5RHp1VU13Q3pvN3BUQTNqSmtGYXlVQ3RPeUVWN2xBT0FDWll5cWxPbkFiSmx1b0lQSmpSZEZLd2VJNnB0cWVCQUpWVTFFZlNWZlgxTGc3Zk05VnRiRDNZb3MzamhzN2FENE5XZTd6QlorcU5ObmxTdDhaY3pKRlB5ZU1Kc1pzcEJzSTdnNkpLWnp3UWNiRmdiL0J4dmhoOFZCOUJvOVpyMmdJeXkxcFdpTXROQk1kbUdNZjlmaVFIenlVbUtzak5LYS81ODVRU0hlUS9VRmlIQk1ITyt0SVhSN3ciLCJtYWMiOiIzYjg0YzA0OTA1NGY2MWQzZjBmYTkzZTMzYWJhMmY3NDc1MzI3MTE0OGE1ZGI5NjQxZmZjYTk0ODFjYzRiOTc3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjBiVFVOZmc4bFpnS0tOeEsxNXcrbXc9PSIsInZhbHVlIjoiUnNhNHNtL01SdjlmN0xnQVlQMG9LMS9HVGlWNUhMZS9ZcGNrbzc4WmtlNHU1eEgwazIyR0liNlNOS1Y3bnZaVWtGbElpL1dpL1lDOVY2SWl3Y1RNZi93cFdNckFvcFFNYUplR09LZjBsR0tXdUdKblFrWEFlQkFld25rTStNSUR2eUFpbUZJcnkwRXNaUnNRRVRoS084KzdOZ2FtOHZ6Qll4aFJndGI0MGw5V0MzL2dCTFV4OXVUNHV4UDcreHhvRmJNMzU2SkxTb2laRnBjTXMwejZ2UUJycUc4S1p4dE1jVHl3MjcwK2owUFBsckJVTmtpVzlUZVhyVEhhNG9wTzNNYW5PeW9NQjNQVEt3cWdabU52bE83NFQrNHIyazdpQ1VnUjVnQ0VFQzdpUWdDb0Z5YUQ1SXBtV1l4UXFuVEgwbFBJRW5kcWVuZHdJUHJPQnB3OUdhWnFYM3hCR054eUhlUFU1eDRHSGkxRE1ZOVdESnVoeWhjR2Rra3d1b0ljSEJwVGsyeE1Bam5UNzhkdHRVNVJxL1RSbktFOTBObFk4cHVUQlNNdjFSQ2JEZGVlZzlXeFVFNDdDdWZDMHBVRjQycTNsTjZxUlVrSzA3Yjk2dy9sQzdKNzArZ1pBdDVzV3FoOXd5dUVBSTFOUkVZNnBHZ1VhMnBiYW5tWFd1OGIiLCJtYWMiOiIzY2YxMzgxN2JjMDExZjA3YTBiMWNlOTdlYjBmZDhhMTc3YmJlNGI5Yzk1M2Y2ZTQ1YzNkMTdlZTU2YWNkZmEzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1228833320\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1888640797 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1888640797\", {\"maxDepth\":0})</script>\n"}}