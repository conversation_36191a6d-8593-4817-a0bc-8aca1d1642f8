{"__meta": {"id": "X03480e391fac48f324dd54ee4331e271", "datetime": "2025-06-30 23:10:19", "utime": **********.66001, "method": "POST", "uri": "/pos-financial-record/closing-shift", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.197649, "end": **********.66003, "duration": 0.46238088607788086, "duration_str": "462ms", "measures": [{"label": "Booting", "start": **********.197649, "relative_start": 0, "end": **********.584481, "relative_end": **********.584481, "duration": 0.38683199882507324, "duration_str": "387ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.584491, "relative_start": 0.3868420124053955, "end": **********.660032, "relative_end": 2.1457672119140625e-06, "duration": 0.07554101943969727, "duration_str": "75.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50903096, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record/closing-shift", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@closeShift", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.closing.shift", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=169\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:169-188</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00823, "accumulated_duration_str": "8.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6156309, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 23.815}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.625771, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 23.815, "width_percent": 5.711}, {"sql": "update `shifts` set `is_closed` = 1, `closed_at` = '2025-06-30 23:10:19', `closed_by` = 22, `shifts`.`updated_at` = '2025-06-30 23:10:19' where `id` = '74' and `shifts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2025-06-30 23:10:19", "22", "2025-06-30 23:10:19", "74"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 176}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.637409, "duration": 0.0032400000000000003, "duration_str": "3.24ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:176", "source": "app/Http/Controllers/FinancialRecordController.php:176", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=176", "ajax": false, "filename": "FinancialRecordController.php", "line": "176"}, "connection": "kdmkjkqknb", "start_percent": 29.526, "width_percent": 39.368}, {"sql": "update `users` set `is_sale_session_new` = 1, `users`.`updated_at` = '2025-06-30 23:10:19' where `id` = 22", "type": "query", "params": [], "bindings": ["1", "2025-06-30 23:10:19", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.642303, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:183", "source": "app/Http/Controllers/FinancialRecordController.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=183", "ajax": false, "filename": "FinancialRecordController.php", "line": "183"}, "connection": "kdmkjkqknb", "start_percent": 68.894, "width_percent": 31.106}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "success": "تم إغلاق الشيفت بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record/closing-shift", "status_code": "<pre class=sf-dump id=sf-dump-250225905 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-250225905\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1183652904 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1183652904\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1932225542 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>shift_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">74</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1932225542\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1688509877 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751325016632%7C10%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkI0TmRIZ3RSTHNKOVhuQnNPQURYYlE9PSIsInZhbHVlIjoiV2QxdEphL2svcUVrVU1kUFNWVjZUTy9qZWxCOEgzVm5hL0pmTEo0Rk1TUHdYVTN3VERIYU9aclBKandYbjU0enQyVnFDMXRrbWtuL1NRWkNmeDFMQW9RNHMvdFFINzVHMDMyUlZCV1dnREpJSnBqUlVRSE91dGw1MmQ2QkhEcjUzU2lLQ3RLSEl5anNOWC93UGlJdE1KZW1CYTc5U0dJb1hBOXNBRk92QzdPcGFGTkNSNVZVWnNpbHdoM21qSHlTL2txMVg5ZjNTcUVEd2NMQitVZG9aSEdKRm16ZytzdnNlUW5CNTdVWTR5S0QycGVPOTJ2bWV0cS9xdTFvY0xSeXFTdXk2dk1VQWsvV2tDYWhjU1B6Zm9SYkFnTjFNb0ttMm5ZbEhxQjYxcHUxUk4zNFVRcThIams5QTBaMTc5M3hzR2dNbVdyNW94MFBjMTEwb1JqMnVmc0xieEd6SDFLYUY1dE84Y1plWE9CdlZFY1dCK3RyOG54b2VKekJiR05SN1FqSFZkdm9VWEZmdFlST2FmWkdGbVYwK0VjZGVaeG44YTltZEhOZmsrM1J4MW1mSU9nd1p5MEVHeXhOeFdJbkR0NE1QeHRCY2hydDlEdUZqRlM4K2FBcWpTTE1oUVhtV0Q2RWkrMWRlcXgrc1ZHaER6YTFCYnkybXJwcGIwVVEiLCJtYWMiOiJmYWYzNjZmZWIyNzQ1ZGRkNzdhYzllY2NhZmZhMTE3ZTViZjRiMzFjY2NlN2I5OGVlY2FiOWE1MWVlZDEyMWUyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InNnbnNKcmhRblBUejMzeGZmdXhaM3c9PSIsInZhbHVlIjoiZHJaWlhqOEZhYzdCZjJhOU50dEVnVnFGOGhoclRNQk1mRmpsVDY3RnlNenBQRHF4bndzUGRZcTgxcGpMVkU4YXZYRGRYcGlpalEwL0Zyd2JuSnhEeFl5SW14TE1QRU9EV0RkeG0zc1JlSHNxUVNWZkdrM1Q2cTMvS2dkaitFUmtzc0RpTmRIR2ZMbFhWUnE1UHJTdk5tWVV5dUk3bFNDUmxGSVR6QXhGR3crUWhnVXRML09NMXk2NWtGcFU3blNtMkZOVVhTYU1talpreXpqSTFOZWxoT3ppWnV5b3RhaFZQb004ZUU5bVdKUjNjQTZnZjR4Smt5Zzc0R29WeVZLWHlZK2ZqYjNyN01LLzdGOFdGa25wTUFBRGk2Nk9zSVZMS3NCMXQ2RmhldUVXb0padEFhZzB6d0NpZjdSb00zcnRpeEk5UG04TWpFd2lvTmR3amRhRFY0aCtKZ1RLRTN0ejBxdGtGZHBpRE9yWFlNVFRQYitzL1AxZDcxdGpQWktFWUt0alU5cUFkRytDS2xmc2FSTGZuVEt6cUEvOTBxSlN6cS9ZMTdKMGlqOGJVckE1cUFXNjhUTTRnSDBRMEM0RW1DZXJ5Zk56UTZta3VDSDVMZTIrdjJmeTNNV3BnVHpuRXJHME9ZR01OKzdsR083RVlOMWFTdkt6YVhRN0xNdkwiLCJtYWMiOiJmYTMxMDcwMmJmMGIwYzM3OTMzNzgzYWU0NjliNDc3ZDJkN2ZhYmZkYTFhOGNiOGVkMWRiYWVkYjM1ZWEyNzEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1688509877\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-866928584 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-866928584\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1384411663 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:10:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndoY2RBTEcyeG1RdVpMYUFpVkUzRGc9PSIsInZhbHVlIjoieEVlTHJkUFpRSjNtN0orUVU2ODh1VXQ2TmVvakJJQmtwTGRaUzFBYnpHczBmNGlDbWhzZmNXbmJrUlMySEczNnRKTXJYSWI5T3dDL3RTVVkreTk0dmtMM29CRUptZWZqOGpnVUJNNW5XNmdzeHd0OThlZFU0QXkrWEhnQWUyZWhFSEpnZHRUT3N3QVVDSkJsVWx6UHUyNUZuRzhmTlIvaWkvSkpOY3JVV0dqSkoxMVpQdDFlWUFPck5VL29IMkNYM3F6R1d6bmc0S2Vhd2prRlpYMzJZK2VaeURjeUpscFlzaXAwdkZBbUtiT043dDVFYlBROWRlM1kya1dQVnNjU0JNSmMySUpLNEU5OE02cXdneG9BNlJTa3hsSmRsOWV5REVMME5pWmtONldaYlVRMjg1d0hMRUtyT00yY0RDT0J2WmN5QS95Ylo4RC9yVjczeENLQmR4c0krU05PR0RYTnVlaUtXMHdmVE8vUEx6RGdhdmxsME15RitnSUx5WlhSTkpLSDlzWTRSMWkyZ2NPdzEwQlNaNkRCZ0d6WUFScjRhbWtucEVBeEpTRUNra2V6TU52SEJIODNzQ1BYNXdEV3ZaRlBXWDd3Z2N0Z2xNaThMVWRtaXlnMTdvN2xVMFlDbHpJN3NweXNjKzJHckpjWGs5WE1IUHNnMkhjOGhxSlQiLCJtYWMiOiI5YzliZTIwODcwZjVjODFlMmJmNzViYjcxYzEyMmU1OWNjZDI1YjI0ZGFkMzFiYjlhMjA0YzRjZGY2MmQ2ZWIwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImUwK1FER3cwMUI5RkY5b1VmVGF3QlE9PSIsInZhbHVlIjoiK2JRbXVwNzVaK0Y0bUEySS9KVk8xUDZWeU0zTzA2bmw5MndlQjVSNFB2LzZ5SVh4WVNnVmhmWndBeWErSWFlY2E0SVVXQ3BobWFKYmVsWldURWkxSnNrNERrdXUzenUrbmRtalhPcFZDOXN0RUtzOXJ0dVYzYy8vVjk1VFVMVUlPTlVFY0taWmdBMkovRGFUcUJDZmx5dlZqUjNoMHEvbzhhcnVvWnZCL04xN1IvSmt1WFdyTllvR1pnbVlTNEFJcDMvVERmb1IvUDFxRHhWMzdlK3FKWTJDYUtpcTdpRkR5WEtaU21QOXlqQ1dnZjVXRXhhTDFLbjMwNWpEdGZvSjRMUFRzazJVV21DcHVud1ovZDA0cHpick5RMkdScTFqNE1mczVOYmt1bDBCQXZOY2crYnl0NldTMmFRUFFQK3dpZGlMUTdabDRJZUdvZzU5eHBJcDVJOWtDTTZJM3F3ODJyd3BVUTBKcTZPVUZFeEtHV3RQWHZudys4OGQxYWF2S21HaGRpUmxpcGhKVXc5UTU0Y011UWdVcm82eWJENHhNV29yRUpzVkZCYnBJQ203SmFjOWhpcGlLaFZiT1FRYnVXOFRxNjBQRGRTWnRpM3FkaWs5QmloRlZYVTFsUHlnMzgrcUE5NEplK0xUVm1pZTNnWnBWVThjMzNLVW0zK3ciLCJtYWMiOiI2MDUxMjBiNmZjMjI5NTY3OWMwZmY2ODgyMGU3ZTUzZmFkNGE0ZWJkMWJmMDIwNjdhMGYxNmU1ZDk3NTQyMTZlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndoY2RBTEcyeG1RdVpMYUFpVkUzRGc9PSIsInZhbHVlIjoieEVlTHJkUFpRSjNtN0orUVU2ODh1VXQ2TmVvakJJQmtwTGRaUzFBYnpHczBmNGlDbWhzZmNXbmJrUlMySEczNnRKTXJYSWI5T3dDL3RTVVkreTk0dmtMM29CRUptZWZqOGpnVUJNNW5XNmdzeHd0OThlZFU0QXkrWEhnQWUyZWhFSEpnZHRUT3N3QVVDSkJsVWx6UHUyNUZuRzhmTlIvaWkvSkpOY3JVV0dqSkoxMVpQdDFlWUFPck5VL29IMkNYM3F6R1d6bmc0S2Vhd2prRlpYMzJZK2VaeURjeUpscFlzaXAwdkZBbUtiT043dDVFYlBROWRlM1kya1dQVnNjU0JNSmMySUpLNEU5OE02cXdneG9BNlJTa3hsSmRsOWV5REVMME5pWmtONldaYlVRMjg1d0hMRUtyT00yY0RDT0J2WmN5QS95Ylo4RC9yVjczeENLQmR4c0krU05PR0RYTnVlaUtXMHdmVE8vUEx6RGdhdmxsME15RitnSUx5WlhSTkpLSDlzWTRSMWkyZ2NPdzEwQlNaNkRCZ0d6WUFScjRhbWtucEVBeEpTRUNra2V6TU52SEJIODNzQ1BYNXdEV3ZaRlBXWDd3Z2N0Z2xNaThMVWRtaXlnMTdvN2xVMFlDbHpJN3NweXNjKzJHckpjWGs5WE1IUHNnMkhjOGhxSlQiLCJtYWMiOiI5YzliZTIwODcwZjVjODFlMmJmNzViYjcxYzEyMmU1OWNjZDI1YjI0ZGFkMzFiYjlhMjA0YzRjZGY2MmQ2ZWIwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImUwK1FER3cwMUI5RkY5b1VmVGF3QlE9PSIsInZhbHVlIjoiK2JRbXVwNzVaK0Y0bUEySS9KVk8xUDZWeU0zTzA2bmw5MndlQjVSNFB2LzZ5SVh4WVNnVmhmWndBeWErSWFlY2E0SVVXQ3BobWFKYmVsWldURWkxSnNrNERrdXUzenUrbmRtalhPcFZDOXN0RUtzOXJ0dVYzYy8vVjk1VFVMVUlPTlVFY0taWmdBMkovRGFUcUJDZmx5dlZqUjNoMHEvbzhhcnVvWnZCL04xN1IvSmt1WFdyTllvR1pnbVlTNEFJcDMvVERmb1IvUDFxRHhWMzdlK3FKWTJDYUtpcTdpRkR5WEtaU21QOXlqQ1dnZjVXRXhhTDFLbjMwNWpEdGZvSjRMUFRzazJVV21DcHVud1ovZDA0cHpick5RMkdScTFqNE1mczVOYmt1bDBCQXZOY2crYnl0NldTMmFRUFFQK3dpZGlMUTdabDRJZUdvZzU5eHBJcDVJOWtDTTZJM3F3ODJyd3BVUTBKcTZPVUZFeEtHV3RQWHZudys4OGQxYWF2S21HaGRpUmxpcGhKVXc5UTU0Y011UWdVcm82eWJENHhNV29yRUpzVkZCYnBJQ203SmFjOWhpcGlLaFZiT1FRYnVXOFRxNjBQRGRTWnRpM3FkaWs5QmloRlZYVTFsUHlnMzgrcUE5NEplK0xUVm1pZTNnWnBWVThjMzNLVW0zK3ciLCJtYWMiOiI2MDUxMjBiNmZjMjI5NTY3OWMwZmY2ODgyMGU3ZTUzZmFkNGE0ZWJkMWJmMDIwNjdhMGYxNmU1ZDk3NTQyMTZlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1384411663\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1258932576 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"21 characters\">&#1578;&#1605; &#1573;&#1594;&#1604;&#1575;&#1602; &#1575;&#1604;&#1588;&#1610;&#1601;&#1578; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1258932576\", {\"maxDepth\":0})</script>\n"}}