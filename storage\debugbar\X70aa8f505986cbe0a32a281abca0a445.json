{"__meta": {"id": "X70aa8f505986cbe0a32a281abca0a445", "datetime": "2025-06-30 22:40:55", "utime": **********.167114, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323254.674652, "end": **********.167131, "duration": 0.4924788475036621, "duration_str": "492ms", "measures": [{"label": "Booting", "start": 1751323254.674652, "relative_start": 0, "end": **********.087435, "relative_end": **********.087435, "duration": 0.4127829074859619, "duration_str": "413ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.087447, "relative_start": 0.412794828414917, "end": **********.167133, "relative_end": 2.1457672119140625e-06, "duration": 0.07968616485595703, "duration_str": "79.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45348480, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02221, "accumulated_duration_str": "22.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.11973, "duration": 0.02069, "duration_str": "20.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.156}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.154688, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.156, "width_percent": 2.611}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.15852, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 95.768, "width_percent": 4.232}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-79871841 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323250612%7C5%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpndTVjRHB3aEwxOThBZlh6WHNud0E9PSIsInZhbHVlIjoicDRwQW1MUGVzaSsrMUdrcmZzMG9XdlNPbXIya0ViOWo0UGoyRHpkbUEzVXBlcFR5QmFoVDVMVFBwRXpINWphcTBmL3BNV21OVWE3MXB5d0xkdEhWZGZWZjBwNUdmTXBNVVJQaTF2TlpqcUxjTHNiaHpGVlYzWjhuQk1oVFJFRTRRM1dKNUtjWnJ6clVBR3BsSUMrVnNFVXpmYjFISTlGNGRCNjVIckFFcUZ4MndKakhYZ2RuZVVGWVkrcmdVelNFWmNsanRrR1J1MWZxNUxETXZKN1o3K0FyZlpwOE1VbGxFVnBaQ3NIMmFBMGJyY1lIMUdOVHNQNTVWOFBuWUMrOExGalF4ZlNCcGZnelo3MGFLUU9lTWtVb0ZlQjM2NHZ5WDkwSGtKMi96bWNRbXlpbExYazd4UU1KOHQ1bllYZTJmckpFKzQ5M1c3a2ZRa0pGTXhJMnIwUDd2STVhSGhxeEZPZVhlK2RaeG0rRHU3NG5jRnprVndLalRENWtHbFQrOEVlTkNFdldpNGlLanh5MDg2ZTBUVndhcE00RDRmNzFlQTB5QzZRTHczRHJzUmlUVU1jSWEvVjZYQnBSdHN1aEU5QW1IY3dtVXQwaytnMUt1YVh1QVpWeFJRQkNabW5BcWdBUmpjd3BqbUs2SEdhRjNCZ3NoQzVOM3dwQnh6SVAiLCJtYWMiOiIyOTdmNWFhYjg4MmZmNzk2OTcxMTdhMmEzYzRjZmY5OTUyMDBiZTRiZWI1ZWFmZDc5YWE2YTAzY2FhNzhiMGRhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkhoZ3dXSTBpOUl3RWJkbEpEZlVLM0E9PSIsInZhbHVlIjoiNXJpaFNCTmgrK2RteTZLYVk3RTk1OFUxSVpDNzJzYzNWV3FXc3BwRy96ME9BNFU3SWlTQ1RDbFkyQUJMY2lFaTlja2JuN09DZEdQUHplOFloQTBKSjF4TTB1SGxZTDBqTVFlSFNURFU2dmFka0VFQS9VVTFrOHBoZEh5UUsxYzZSL3k0eEdlcTd4Y1BQUFJ4bVVjZ2wxK3VtdzhsOGRSbkFvakwyTWs5R0o4K3N5VFllUXdIWDdyRWdzbldOVU9lY3RnTENtMHZwUjAydElRRC9TcnRtTjZOSTRRcUtOeXEvbFczNjNST2ZEbmxFdVNiMEQrQUlOUXdkZ1A0LytZb2M2c0JSamJGQzRGbnNTOG84RHRQUmR2SE5mandwditRR3pSNmdjRWgycHdBZmFqNmVIcXhsejZNSTRoTW5WWFk5U244OFV6Q2FDLzNuY0J4NXh0ZmRKNjdSU2lRZ1lyZUZiZkZoL0dBakprVXUvSlBDVWRLZnVMM2NkRTR5K0FnZzFXMVFXbXdiUzNxbE1TZFJpZlFSNVJOeWFSMndtcDAvb1JuZTdRSlZVZVpjb3pRNGlFc2VtWjVXa1p2WTJ0dkw1aGdKRkMxU3NpdVdzOVlUWUExeHI0dlFoRko3N0s1UlZoUEtKUVR4dXgrVG1sMS8vanB1RGgrZW1rYS9BSlQiLCJtYWMiOiJkZmM2YmE4MWU2OWEwNTNjOTQ2ZmRkYjhhZTZlZGNkNjYwZjRkZTM1ZmFhNjE3NWNmYzNhZTQ1MTE0YjQwMTQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-79871841\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1850600157 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1850600157\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-226490990 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:40:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllFeHVtSkwrdWN3MFg0WHJjUFNUR2c9PSIsInZhbHVlIjoieFI0ZVpnY3MwNkJJZjErN0MyM3pJdUlnakJNay9peUxBWlEvNkd3ZTlia2pYRmVFRGtlaXJ3dmtNdFBleE9QeVVGZC9lcnFCQkppdllMamh1TGZ0ZlBHSFBGL3IxaFFZVC9nTHdCZ1drUmNEV0t5eFRxU0I4SzRYUUVDMGl1N0JvMzJ6R25aVUNLRjhnUTAvUHRUTTVuUnBSTnNjaEhrWU9tc2orbVF2S1VzekhVcUtzbm11NUMySnp6VHVza2lsUlRWUmtMYUprUWlxRjNYQXY1LzJtNDBXRU5kWEtOUDd6MzRDY3hxTVZRcTFLS1hWKzduTG44TGV5bThBWWpKdHJSQi8yUlVjLzRuc1ZlMnFhb0ZsUFJOTC9HNXlQelByanVJbUlDaVNiZ0p4SVlaWTJxUFdMWi81eDc2MjE4aktCQXF5S0prS242OUtja0RyL1IrU0xzODFramsrV3g2OG4wUk9hM1NGdTIva215blhoSUtVTDhmaGpQdm1CY0hsSmJZTE1vc3VhNkdzY3hUUTZDRXB5b3JvRmZreVBxbXB5QU1mdXR4ZkI0QWVnRUF1ZnNuU1kzYkFNODZYc240S1p1K2tjQnhkL29EL3ppNFNwcHZValoxRGdkdHhldCtqM29aZnhnWlEzRmRKWUd6M1cvQ0NaZnNpSGtQZ1R5SEoiLCJtYWMiOiI4NGNiMDRlNDhhMTNkNTQ4YWVkOThlZjY5ZWZkOWEwMThlMjk3NmU0ZTg4NDM5OGIxYzg2ZjAxNTMyZjk2YzYxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImhYQTFwWUlET1MxSml5WXRtelVSMEE9PSIsInZhbHVlIjoiV21oZXdVREFxQnhYQnpJVUZGeVU2VWtIQ0NYZklicS9relF1amtsNWR6ZmdsM3M5WFpyN1FMT2NiWGZNejZjMWlNTm1IUnBVVmFaRFNPWU9PbFdvWTdVNUFYTGRadEVidWZVY1dpTi9kMUd0aHhhM1RPbk1XcFdSSXpubmhNVFg5VUJ2dmtBVzFOdjZDbjViVlhYLzk5Rkp3alA2b3VBdVdLTmhBNzNWN0ZjVUNBYkxHdEhsREs4b1dkR051N0MrT28vSU1NMmFXcVFJbFhaUjFnQ1ViazdSczErYUQrTS8wcUR3aWRlNURpSXdVK3dDR2tPWk9ld2dPVHo3RmR1ZmVXeGJ5WFNvaEJaRjdSSlptcC9scDIxZEJmR1NzNCsvN0sydCtrVTVBRVdtOCtEN3kvUEIreW9oWjVFV0xqVW1KdWZuaHZvdkcrN2w5UTRZSU9oa3hTRllwczBzVjc5WWhqVStCNUl5bzE1emJMamhhRTA1ZkxOak0wV3MrcTZ3dUdVLzZoeGZXa3pHRFkzMURXRUFoWlhhelZHRWpRRlBtT3FEeE80cUhqRjBzOVR4MllHQlorQm9EWWxTc1FxdkxpbG1OdzVCaGNkTXBnK1JsL1BHemZPVkYrTk9TajVvZ1hiRWUyWVN2SmpNUld2bk56ZDVTd3VqNkxjbnBYcUsiLCJtYWMiOiJlYzI0NjZhNGQxOGZhMWQ1NmY1YmY4NTI1M2RkYmQ1NTVlNzIzNzE2OTI5MTQwYmM3YWE1Y2YxMWE2Y2Y5NDczIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllFeHVtSkwrdWN3MFg0WHJjUFNUR2c9PSIsInZhbHVlIjoieFI0ZVpnY3MwNkJJZjErN0MyM3pJdUlnakJNay9peUxBWlEvNkd3ZTlia2pYRmVFRGtlaXJ3dmtNdFBleE9QeVVGZC9lcnFCQkppdllMamh1TGZ0ZlBHSFBGL3IxaFFZVC9nTHdCZ1drUmNEV0t5eFRxU0I4SzRYUUVDMGl1N0JvMzJ6R25aVUNLRjhnUTAvUHRUTTVuUnBSTnNjaEhrWU9tc2orbVF2S1VzekhVcUtzbm11NUMySnp6VHVza2lsUlRWUmtMYUprUWlxRjNYQXY1LzJtNDBXRU5kWEtOUDd6MzRDY3hxTVZRcTFLS1hWKzduTG44TGV5bThBWWpKdHJSQi8yUlVjLzRuc1ZlMnFhb0ZsUFJOTC9HNXlQelByanVJbUlDaVNiZ0p4SVlaWTJxUFdMWi81eDc2MjE4aktCQXF5S0prS242OUtja0RyL1IrU0xzODFramsrV3g2OG4wUk9hM1NGdTIva215blhoSUtVTDhmaGpQdm1CY0hsSmJZTE1vc3VhNkdzY3hUUTZDRXB5b3JvRmZreVBxbXB5QU1mdXR4ZkI0QWVnRUF1ZnNuU1kzYkFNODZYc240S1p1K2tjQnhkL29EL3ppNFNwcHZValoxRGdkdHhldCtqM29aZnhnWlEzRmRKWUd6M1cvQ0NaZnNpSGtQZ1R5SEoiLCJtYWMiOiI4NGNiMDRlNDhhMTNkNTQ4YWVkOThlZjY5ZWZkOWEwMThlMjk3NmU0ZTg4NDM5OGIxYzg2ZjAxNTMyZjk2YzYxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImhYQTFwWUlET1MxSml5WXRtelVSMEE9PSIsInZhbHVlIjoiV21oZXdVREFxQnhYQnpJVUZGeVU2VWtIQ0NYZklicS9relF1amtsNWR6ZmdsM3M5WFpyN1FMT2NiWGZNejZjMWlNTm1IUnBVVmFaRFNPWU9PbFdvWTdVNUFYTGRadEVidWZVY1dpTi9kMUd0aHhhM1RPbk1XcFdSSXpubmhNVFg5VUJ2dmtBVzFOdjZDbjViVlhYLzk5Rkp3alA2b3VBdVdLTmhBNzNWN0ZjVUNBYkxHdEhsREs4b1dkR051N0MrT28vSU1NMmFXcVFJbFhaUjFnQ1ViazdSczErYUQrTS8wcUR3aWRlNURpSXdVK3dDR2tPWk9ld2dPVHo3RmR1ZmVXeGJ5WFNvaEJaRjdSSlptcC9scDIxZEJmR1NzNCsvN0sydCtrVTVBRVdtOCtEN3kvUEIreW9oWjVFV0xqVW1KdWZuaHZvdkcrN2w5UTRZSU9oa3hTRllwczBzVjc5WWhqVStCNUl5bzE1emJMamhhRTA1ZkxOak0wV3MrcTZ3dUdVLzZoeGZXa3pHRFkzMURXRUFoWlhhelZHRWpRRlBtT3FEeE80cUhqRjBzOVR4MllHQlorQm9EWWxTc1FxdkxpbG1OdzVCaGNkTXBnK1JsL1BHemZPVkYrTk9TajVvZ1hiRWUyWVN2SmpNUld2bk56ZDVTd3VqNkxjbnBYcUsiLCJtYWMiOiJlYzI0NjZhNGQxOGZhMWQ1NmY1YmY4NTI1M2RkYmQ1NTVlNzIzNzE2OTI5MTQwYmM3YWE1Y2YxMWE2Y2Y5NDczIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-226490990\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}