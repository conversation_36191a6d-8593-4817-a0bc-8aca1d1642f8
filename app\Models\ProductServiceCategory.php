<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class ProductServiceCategory extends Model
{
    protected $fillable = [
        'name',
        'type',
        'created_by',
        'chart_account_id',
        'color',
        'warehouse_ids',
        'show_in_pos',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'warehouse_ids' => 'json',
    ];

    public static $categoryType = [
        'Product & Service',
        'Income',
        'Expense',
    ];

    public static $catTypes = [
        'product & service' => 'Product & Service',
        'income' => 'Income',
        'expense' => 'Expense',
        'asset'=> 'Asset',
        'liability' => 'Liability',
        'equity' => 'Equity',
        'costs of good sold' => 'Costs of Goods Sold',
    ];




    public function categories()
    {
        return $this->hasMany('App\Models\Revenue', 'category_id', 'id');
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class, 'category_id');
    }

    public function bills()
    {
        return $this->hasMany(Bill::class, 'category_id');
    }


    // public function incomeCategoryRevenueAmount()
    // {
    //     $year    = date('Y');
    //     $revenue = $this->hasMany('App\Models\Revenue', 'category_id', 'id')->where('created_by', \Auth::user()->creatorId())->whereRAW('YEAR(date) =?', [$year])->sum('amount');

    //     $invoices     = $this->hasMany('App\Models\Invoice', 'category_id', 'id')->where('created_by', \Auth::user()->creatorId())->whereRAW('YEAR(send_date) =?', [$year])->get();
    //     $invoiceArray = array();
    //     foreach($invoices as $invoice)
    //     {
    //         $invoiceArray[] = $invoice->getTotal();
    //     }
    //     $totalIncome = (!empty($revenue) ? $revenue : 0) + (!empty($invoiceArray) ? array_sum($invoiceArray) : 0);

    //     return $totalIncome;

    // }

    public function incomeCategoryRevenueAmount()
    {
        $year    = date('Y');
        $revenue = $this->hasMany('App\Models\Revenue', 'category_id', 'id')->where('created_by', \Auth::user()->creatorId())->whereRAW('YEAR(date) =?', [$year])->sum('amount');
        $invoices     =  $this->invoices()->with('items')->get()
        ->sum->getTotal();

        $totalIncome = (!empty($revenue) ? $revenue : 0) + (!empty($invoices) ? ($invoices) : 0);

        return $totalIncome;
    }

    public function expenseCategoryAmount()
    {
        $year    = date('Y');
        $payment = $this->hasMany('App\Models\Payment', 'category_id', 'id')
            ->where('created_by', \Auth::user()->creatorId())
            ->whereRAW('YEAR(date) =?', [$year])
            ->sum('amount');

            $bills     =  $this->bills()->with(['accounts','items'])->get()
            ->sum->getTotal();
        $totalExpense = (!empty($payment) ? $payment : 0) + (!empty($bills) ? ($bills) : 0);

        return $totalExpense;

    }

    public static function getallCategories()
    {

        $cat = ProductServiceCategory::select('product_service_categories.*', \DB::raw("COUNT(pu.category_id) product_services"))
            ->leftjoin('product_services as pu','product_service_categories.id' ,'=','pu.category_id')
            ->where('product_service_categories.created_by', '=', Auth::user()->creatorId())
            ->where('product_service_categories.type', 'product & service')
            ->orderBy('product_service_categories.id', 'DESC')->groupBy('product_service_categories.id')->get();

        return $cat;
    }

    public function chartAccount()
    {
        return $this->hasOne('App\Models\ChartOfAccount', 'id', 'chart_account_id');
    }

    /**
     * Get the warehouses associated with this category.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function warehouses()
    {
        if (!$this->warehouse_ids || !is_array($this->warehouse_ids)) {
            return collect([]);
        }

        return warehouse::whereIn('id', $this->warehouse_ids)->get();
    }

    /**
     * Check if this category is available in the specified warehouse.
     *
     * @param int $warehouseId
     * @return bool
     */
    public function isAvailableInWarehouse($warehouseId)
    {
        if (!$this->warehouse_ids || !is_array($this->warehouse_ids) || empty($this->warehouse_ids)) {
            return true; // If no warehouses specified, category is available everywhere
        }

        return in_array($warehouseId, $this->warehouse_ids);
    }
}
