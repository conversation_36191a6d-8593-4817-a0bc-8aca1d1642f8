{"__meta": {"id": "Xf4bdc961d61702308e6c7e065fc73b0a", "datetime": "2025-06-30 23:10:27", "utime": **********.182949, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751325026.68546, "end": **********.182968, "duration": 0.49750781059265137, "duration_str": "498ms", "measures": [{"label": "Booting", "start": 1751325026.68546, "relative_start": 0, "end": **********.10467, "relative_end": **********.10467, "duration": 0.41920995712280273, "duration_str": "419ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.104679, "relative_start": 0.4192190170288086, "end": **********.18297, "relative_end": 2.1457672119140625e-06, "duration": 0.07829093933105469, "duration_str": "78.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45707168, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020549999999999995, "accumulated_duration_str": "20.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1372132, "duration": 0.019399999999999997, "duration_str": "19.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.404}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1657958, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.404, "width_percent": 2.92}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.172412, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.324, "width_percent": 2.676}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-766887617 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-766887617\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1871409456 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1871409456\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1798458217 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1798458217\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-40073255 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751325022992%7C13%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImFObGhTcG4raUNyYkxWejRUNm9RTHc9PSIsInZhbHVlIjoiUmQzR09ldGp3aXhaTlhMQTI1aDZZbGNzVVl0NkErSHJ4bFc1bG8rM2x4TW1RdEdpZkdlSEVMdzc3MXFCVVROUGIza1hDQTMwOW9OQW5GSjZLZzJXK1lnZFJTMnpSeUFuOXE0UU9aWTAyRjArNFdsay9wNmlNNlBUa1hhUUZralVYZUZ0OHoxU3JTNU1MUTZDRUlncloyT2E0TDNYaFQrdHY2SHlBaW03aE1mVUVieitvcWRsVXVwZ0V6cS9mZTBZSTN1Y1lqY09Ba1IrMFFGb25nUytyTkQrbGhidFV4ZFNHOFV1OVdUNzV4cEdzdmtveERmblk3blhXZjJDWG1Jb2hhMjdKeEJERkIvcUhyS3Y5bG8xbkFidlVrcXBvN1FPdkE1OG9SM2V3c0VoNVhTSk9nZlpoaGZRVGtDME55eXlMS2pvMXRGMTV3T0Zid2Ftdm54cDl6a3FHWUJKT2RvZWlycnpsZkNibkcvbUNNd1U3MktOQnJTMDEySk9NUFp0cGJ0RU54OEsrVVVoRTZyY2ZzdWViZEhtdUFNQnk4Ym5xYzNJcExXS1hRR0NRL3ZUb0ZrdytvdUZPc08zQmdIcHpidXIvS0h1dDlaWXVpRGlwZ29NMTRyYmhabWNnTXdqVTYxREM5Yit3aWFDWG9zbU9MY1RSU3UweVBsNURMVXEiLCJtYWMiOiJjYmY4YjE4NWExNDc1NDU5ZjQ0MTk0MjA1ZGU2M2JhN2M5NjhlYWQyN2Y3Y2U1MjJiNGEwYzVhNTNhMGVjMzQyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InJ5VW5td2phWUFHdURJMDdhbjJtcEE9PSIsInZhbHVlIjoiVit0OWtXeE1wMVJVQ1ZuZlJnejZQNjJvaStPSkwrdi9iN3ZiKytFWWI2K0FOdGc1cnhWTER4MmdWekJXTzRFcFkxaytobXFZbCtRcEZ6ckZ3VmhmU1oxQUJPV2gvUzFHb2hhM0lCZEJGSEtSMG5rWUVpQXlmMDZMbXhPK1VmdnZITDUxdUgwQUtwVzV1bVgreXEzbUxxeDI5dGRzUTBlODV1T20rZ2xBWmNzSmZqaS9ieDBwaTVpSUNpL3lHV1hpVnJINmY4V3psajdxTG4yTjdZRUlkUXlvRzVDclRtNEVzckdzMGVoTmh5TGpybGkrU21oeVAramVJY0R3L0RZWEYvZ3ZzY3F3RTdpSEZyQjFNRXpYdXlRWVZJMHcybWVjVUMxRmFsQ2xoc1Q5elZiK3A5b2Y4ZVIreDRXTkpVUkxiaWl5UWFnZ3hWME51eElDTk54YStvUkl5cEIyOXo4L25abmdVaVkzME96ZkJib0dqWUlEWU8rYy9ySzdwY2U4STRLZTFaekxSVVdERTVWcWVUenp4Q1d0dzFSOHBiSHpkT3BjZ3phZ3hGcE5iRnZUUjNnckg0ZlVLMTl1aWxldDF4eUp1ZWxkSHVlRjM4Wnd2SVJUQmNnVGsxVjVJL0FrREZ3U25jMTdJV1o2MTRsRktKL1ZsVG1pU3l5aXc1WkIiLCJtYWMiOiI1YzRjNDExNjY4NTZmZWEyYTRlN2QxZTA3YzA3OGZhNTJlNjYxZDk5YjgzODI1OWFjMTRkN2M1NDVhZjQ2OTlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-40073255\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1518584156 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1518584156\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-838997915 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:10:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJJcEhqR09EMzBYcGwweCttREM4VWc9PSIsInZhbHVlIjoiYjljNDJaWUtBbnRzaSsveCs0Z29hMFB4UjJkRXZycGhkUnN5OTJqdXZKK3VicEtjalRVaDEzUGRVaHU3QnBaYlFnRE5vUkozRWR6SWdaUis5N3JnM01Ra1NHVk5MRzVZNUlkV21IVEFnL1NzajdUVXJiZGRCTjhMUVpuQ2t3U3lFUWRmY0pYbXZWd3QwZW9hQTFMc1pud2tHMW5ZS3ZxNDFzTklTbGs5RzVZaFFjb2RxWkRVRkd4ZUdhdjc3bk9NV1MzQTFPT1F0Uk9TSUc2VkV3RmZFeFkrVnY5NEQ4ZkR3TGErai9EckRjcC82MHpvTDZpWHJkN1JhN0NUZU43YXVGSzArUnBMbFJQZFR1ODcwT1NuN2ZyL0JwU1JSYVdjMXFvNjhMYXpoRFo1RWFLUUVaa1prdmVtU281cEJ0Nis0anlWSllSUDU5T1JxczgxSE1KeHBrM0lHWVRLbEUxdDZTQXFZOUt3RWhRaTJCRDBmNHBrSk5uKzI1cVF4Tjl6cUp5WVN4N2g4MXQzbk83QThiNnd4ejd5T3FIQkhkMG1MZ2tYL1pYTzRmWXFUZkFGTkp0SkxLMklNbWs5U25uNm9DbjBOUXZmZzNxcDJGWGFvKzZKV0pGM3dJNmxxNUswYWZ4TlM2bC83N0lZVU9waktQRVJETk5xRzBsMVV2VUsiLCJtYWMiOiI5ODEzNjRiOWRjZGU3OTFhZDhhYjY5YTRmNmU3MjM1ZTc2OTJiNGM2YTg1NjJkZTJjZWRlMWZkNzUxYmU1YTUzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkRXSENxWDBjUEF0d3MxQTcyQ0F5OUE9PSIsInZhbHVlIjoiQ0RTYklXWXNHYXJCQnJGMlgrbUVlNXRuWDFpbkMzSzA4M2lQTVNPZlgrWlRHbEhucHNOaGFUT1F4R3l4OE82ck1hTVR0bHdCUDk5UFZDSDl0QlpFMm5IaTdNaEJhZ0ZYV05FT0tmTFpqb0JhdWZBTzdJS2tEZzRWODhQcVVMeXl6ZGtIYlBKUThDYTN5aktVWlFBNEE3VXVyNC9YcVFwT0ZRbDNTZFFqMkt2NDlFQThnbzNJTFM1Mmp1UitmZ1hZNU1hTlFKQzJ5Z2Q2TW1tSGdaYzNzcUlvdEt6L0l5ajQxajZ3aXNvUGpBM1pVOUFyaVRWd3M1V2hvZTRqbjJQMVlaS2NvWmp4TUZxV0hhR1JuTlcrMGJOUVdVb005ekN6M2Q4cnVsUXN0VUdsOFkzNU8xVSs3dDRvMHRYeVRwN3FQR3lXVHZpTzBYalJNSGVzNEFya3R4Sk1XWmN1UXRqQUFJeGtSQzJUT3JLc3pOM0VxOGtwa2E0LzlOSktYTS9mK3pXRm5mUWthcWsxcFhsY283aFNwK2NoZm11VXdlZTRVMWw3eVNEZnM2U1pGM0dRWHd3azhNR1I1V3p0eWFyVmV2Q0Eydksxd1JkcFRuU1JORmZpVVBwY1BmT2Ftd1ZVNkcvdjh5SDUvMERQZ3o2RXYrMFpNQTFHWHJHZnRtMWoiLCJtYWMiOiIyYTU3MGJlNDExNDM0YzBlNGFlM2U1YjU1YzliMjViNzgwYmQ1ZTA5ZDE4NjhmOWRkOWZkNGU2ZjYxNmIyZWFlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJJcEhqR09EMzBYcGwweCttREM4VWc9PSIsInZhbHVlIjoiYjljNDJaWUtBbnRzaSsveCs0Z29hMFB4UjJkRXZycGhkUnN5OTJqdXZKK3VicEtjalRVaDEzUGRVaHU3QnBaYlFnRE5vUkozRWR6SWdaUis5N3JnM01Ra1NHVk5MRzVZNUlkV21IVEFnL1NzajdUVXJiZGRCTjhMUVpuQ2t3U3lFUWRmY0pYbXZWd3QwZW9hQTFMc1pud2tHMW5ZS3ZxNDFzTklTbGs5RzVZaFFjb2RxWkRVRkd4ZUdhdjc3bk9NV1MzQTFPT1F0Uk9TSUc2VkV3RmZFeFkrVnY5NEQ4ZkR3TGErai9EckRjcC82MHpvTDZpWHJkN1JhN0NUZU43YXVGSzArUnBMbFJQZFR1ODcwT1NuN2ZyL0JwU1JSYVdjMXFvNjhMYXpoRFo1RWFLUUVaa1prdmVtU281cEJ0Nis0anlWSllSUDU5T1JxczgxSE1KeHBrM0lHWVRLbEUxdDZTQXFZOUt3RWhRaTJCRDBmNHBrSk5uKzI1cVF4Tjl6cUp5WVN4N2g4MXQzbk83QThiNnd4ejd5T3FIQkhkMG1MZ2tYL1pYTzRmWXFUZkFGTkp0SkxLMklNbWs5U25uNm9DbjBOUXZmZzNxcDJGWGFvKzZKV0pGM3dJNmxxNUswYWZ4TlM2bC83N0lZVU9waktQRVJETk5xRzBsMVV2VUsiLCJtYWMiOiI5ODEzNjRiOWRjZGU3OTFhZDhhYjY5YTRmNmU3MjM1ZTc2OTJiNGM2YTg1NjJkZTJjZWRlMWZkNzUxYmU1YTUzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkRXSENxWDBjUEF0d3MxQTcyQ0F5OUE9PSIsInZhbHVlIjoiQ0RTYklXWXNHYXJCQnJGMlgrbUVlNXRuWDFpbkMzSzA4M2lQTVNPZlgrWlRHbEhucHNOaGFUT1F4R3l4OE82ck1hTVR0bHdCUDk5UFZDSDl0QlpFMm5IaTdNaEJhZ0ZYV05FT0tmTFpqb0JhdWZBTzdJS2tEZzRWODhQcVVMeXl6ZGtIYlBKUThDYTN5aktVWlFBNEE3VXVyNC9YcVFwT0ZRbDNTZFFqMkt2NDlFQThnbzNJTFM1Mmp1UitmZ1hZNU1hTlFKQzJ5Z2Q2TW1tSGdaYzNzcUlvdEt6L0l5ajQxajZ3aXNvUGpBM1pVOUFyaVRWd3M1V2hvZTRqbjJQMVlaS2NvWmp4TUZxV0hhR1JuTlcrMGJOUVdVb005ekN6M2Q4cnVsUXN0VUdsOFkzNU8xVSs3dDRvMHRYeVRwN3FQR3lXVHZpTzBYalJNSGVzNEFya3R4Sk1XWmN1UXRqQUFJeGtSQzJUT3JLc3pOM0VxOGtwa2E0LzlOSktYTS9mK3pXRm5mUWthcWsxcFhsY283aFNwK2NoZm11VXdlZTRVMWw3eVNEZnM2U1pGM0dRWHd3azhNR1I1V3p0eWFyVmV2Q0Eydksxd1JkcFRuU1JORmZpVVBwY1BmT2Ftd1ZVNkcvdjh5SDUvMERQZ3o2RXYrMFpNQTFHWHJHZnRtMWoiLCJtYWMiOiIyYTU3MGJlNDExNDM0YzBlNGFlM2U1YjU1YzliMjViNzgwYmQ1ZTA5ZDE4NjhmOWRkOWZkNGU2ZjYxNmIyZWFlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-838997915\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-888844031 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-888844031\", {\"maxDepth\":0})</script>\n"}}