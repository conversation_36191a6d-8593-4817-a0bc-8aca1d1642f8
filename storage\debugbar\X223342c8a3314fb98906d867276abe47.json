{"__meta": {"id": "X223342c8a3314fb98906d867276abe47", "datetime": "2025-06-30 22:36:52", "utime": **********.040998, "method": "POST", "uri": "/pos-financial-record/closing-shift", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.444249, "end": **********.041017, "duration": 0.5967681407928467, "duration_str": "597ms", "measures": [{"label": "Booting", "start": **********.444249, "relative_start": 0, "end": **********.836668, "relative_end": **********.836668, "duration": 0.39241909980773926, "duration_str": "392ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.836678, "relative_start": 0.3924291133880615, "end": **********.041019, "relative_end": 1.9073486328125e-06, "duration": 0.20434093475341797, "duration_str": "204ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50898504, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record/closing-shift", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@closeShift", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.closing.shift", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=169\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:169-188</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.06079, "accumulated_duration_str": "60.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.876453, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 3.455}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8879418, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 3.455, "width_percent": 0.921}, {"sql": "update `shifts` set `is_closed` = 1, `closed_at` = '2025-06-30 22:36:51', `closed_by` = 22, `shifts`.`updated_at` = '2025-06-30 22:36:51' where `id` = '76' and `shifts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2025-06-30 22:36:51", "22", "2025-06-30 22:36:51", "76"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 176}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9672182, "duration": 0.054549999999999994, "duration_str": "54.55ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:176", "source": "app/Http/Controllers/FinancialRecordController.php:176", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=176", "ajax": false, "filename": "FinancialRecordController.php", "line": "176"}, "connection": "kdmkjkqknb", "start_percent": 4.376, "width_percent": 89.735}, {"sql": "update `users` set `is_sale_session_new` = 1, `users`.`updated_at` = '2025-06-30 22:36:52' where `id` = 22", "type": "query", "params": [], "bindings": ["1", "2025-06-30 22:36:52", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0241919, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:183", "source": "app/Http/Controllers/FinancialRecordController.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=183", "ajax": false, "filename": "FinancialRecordController.php", "line": "183"}, "connection": "kdmkjkqknb", "start_percent": 94.111, "width_percent": 5.889}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "success": "تم إغلاق الشيفت بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record/closing-shift", "status_code": "<pre class=sf-dump id=sf-dump-1888355357 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1888355357\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1653210835 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1653210835\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1058226445 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>shift_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">76</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1058226445\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=6nrx0y%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1aj6s00%7C1751323007905%7C5%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjN4T05Qc2xxaUc4SmdIb0t1aG1jc3c9PSIsInZhbHVlIjoiZ0FWZ3l3WVBkdlNEa2RlOVFiN0g4VGtwODY0U0l4dVVIVktjME00RU02WDNUSGIxeTJWd3hYOHRubXVmV0E0UVRuSUlXMFlCVnZWVmVoTVRHQVBHK1VSdFhRT2UyMERHWTBEUXY4UXFyQ20xaXl4Rk9POWo5SEdXVXBIanJETEhDK1pZOS8zSVpna292b2E2Qk4zNGN4ajI4RFlxVWNBZ2MxQ055a2h6NDBISm8vUmE2Vjh4OWFVYUM2Nzl6ak5IUjBxRUh1dXlxY0FycDRpUXBSSms3L3J5V095WWw5T3JVN3JEQ3dBaGVGWit0NVN2RVo4VGdNSytQTFhzdEl5eCtDdzFoS3Q0MVFwYU0yaGtmc3Z3c3JySVpadElobFE3WmlJbUVXSEo0VnpTM1djRjdDUFp0MnhHeC9STTZTRDdRQm1ZUkcwM3NZZjh1SlNyQk1XeDNmMG9zLzVvTXcxRFI3Wm9QT1FRTXBtRXRDNU5tSkMvRE5DaXlsaFpTcDJJc25wNGNaYWJXcE93WGtMOEtyeGtXOU5Za0lJcG5TVjB2Y3pYMmovL1F2ZmxjbTVUR1JMYWxnSU5CTlNXblBaMzlpT1l6aXVtb2JDbDhETHdlUGVzY0tDSkNmOXRmNkNKeTlFQVlOcldaWWNUSDVvMW9TQmVJS0dYQU01YzRUOGoiLCJtYWMiOiIzNzU3NDI5NWYyNjg4NjAyMDJlNGIzZGMyMGVkZWUzZmRmMGY0MjE2ODVhYmUxMDQwOWYwNDYxMjE5YjQyY2Y5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im9TcE1RN3dBRkVzYTRhTDlnRUNGZFE9PSIsInZhbHVlIjoiSndzSi9DZ1J2M1d1Q2FBOGl5OFBJTm9NQnpEL05GWWRVTE5JSmJ2YU9uZ2IvcUlPbkI5bTdsTWhYVkl4K2xrU3hxbVRZMjJvTUNCd3ZqcXNqV0JYR2lGN1MyZ2VQMlNXU29nM3ZZVHNnYTk0VVlIRXd4cEhCV2pJc0Y0YjE4NEd3UDdqbDJhV0NEcnJZT3g5QzdvQkV1ZnA5TmdPOXgveHdNK21PV0hiUDZ5RjRSK1djN3djb0tUcTdpSFIyMXdxQzNhSDVRZ3pPcFRPWWx5RmhKSU5hbm9uUHp0MWdRcVF1dXdkd21ick4zRHZGdzF2Vmt0Q2pEaUJLNkU2SzRFcFlEM01ndEZWVTFtSmdVb3ErWTVPN2xCaENHZ29lOW5oMlRONFUzckpOSXJOd21PMExSbU1vU1NLUmRRNVRERlBYdXdSM0RZdWduK3ptb1dhNEFmZHRUb0RuNUxyeVZPbU8zVUJvdVZPOHRGK21VY0RZaGdhWmtxVDkrL0hQK1BwUG9Sdk1EVXRpbHE3Z2daVG9mbW9POTB6VE4wQnZ6Y3RIOHNHQS9oWUQwaTJkbDBGblNubllWZkhJZkRXbXBPalpPM0JrY3ZoM21nZ0ZKZkdHckFVM041am5BRFM3bUFWRDA2OHM5VTU3M1QxMkhKZXdIam1kZDkyVDR6aTJ2OU0iLCJtYWMiOiI4ODQwNDcwNzc2ZDFmY2ExN2MzZGQzMWQ2NzlkMDY3MWIwYWFiN2UxNGFkOGU1YjQxZDVmYjc3Y2VjZGYyMDc1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A9rdyCRm7SKpfljuMnVO7IpcgTBMITMjowAdsmJo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2146845662 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:36:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjE2akRFNHV1ZEVHREVXUCtIUzI5a1E9PSIsInZhbHVlIjoiaWVNbVN1cktpek5ZRldlR29zNVZJbUl2VTgwUkgyU1E0cnUyWjd2cGxvZmYyeGJzOUJwenYreVlQWlE0U3p2VHdlVm9MTDM1QnZrdGZhUWlLbExkMnRlVGY4aG5wVW5zM3haZENkYXZwZ0JNQndkalVKWnQ4WGluMG1ydXA3bmkzL1RwRHMvZ0FQdzRXVUNIUGhKeEhrNFoxM3d2QjdkbHNFZ3o5WjZabkgvS05CVWFqOVJnZS96czJyMUF6WFZjQ0hlVXYrZjhoN0JPYWo5dUZUUllScjQwSWhCS1RzdVhGaG5yWlFOTk5ZYThzVG1QU0xZaW1qQXR0S1hhNXdyK3NML0dHRG8wS3Q4U3RHV3p3MXJIaHQ2YlBTWEhOczdRUWZJQWxhNGxuNTJ0U1U2TGtURTdJdVM4TGJiYkhwMXV5Um9EWE5IUUx0VHZJczNPa3lJWFVHMFBwWWZnbGJaZUdPeFM4ZFJCVnVGVGgwemhTbEgrRFBtWnl4UXdtMWhNaEpRWkN5ZTJDZ2dsbGczbmNGU2NEdk9PYXk4UWdPd2RLRFYybk01d0NNN0hWM0QrdDFWOG5TTG5VeTNCL09zdzFwdk1SUzFrY2hyWmFpTTJiSHplWVpMWFp4T2VDb2NGYmlOdXQ0VGFybmliRFFJR25talAxS1U1N2kwWko5dkciLCJtYWMiOiJiNmQyMDE1MWE1YjgxMWJiYWM1NTNmMTgwMjgxMTdmNmU3YWY4YTViYzczYzJkNTIwYmMzMTNmZTAxYWFiNGU2IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ii9iWkxlcHBwR2lQdVZBQnFiY0diM3c9PSIsInZhbHVlIjoiVmlMTjVXNm81RlpwenNMNE94TTlaVUNWeldEcjlFUmJNOGVjN3VYc1UxTStBenZ3VFByU3JqcjliSUNPS29RUUpIZG1TWCthcVlVTFdTbjc4NWwyWVAwb3IvR3VpYXoralpCWWt6TmtKalpDS2FYRXRjUjN3bzNRV1FVMW1Id0kwN0oxMS9hYUMvcVNlSG1Jby8yZWI2RnY3bitTYVpOL3B1SkNtbkZ4UHJSa3IxNDFlZUNCQlFGKzd1cTBmaysrejAzRVJSVXhYOFFKdGJUczZwYjExa3JjRndYSWNac2VmVkZrSWhmdHUvU1JWTldVbzhZdm95MElEazhVa3pIQU9kMXR6U0pXY2JVYjNXSEFYMVhpOVFIKzVCN1QzbDVQRnJ6MkZqQXRpZlE2Y3NjVHhXRCtlNHZEVTc2Q0Npdm9FRmhMYWdMc3lwOUpQVHpBbmlNWTFwRWVwMmN6UU1lYkx1NjJRYzVhMWVxdDBjS2QvdzZTQlNRV3hKcm5MZ2RxVXpIYTdPSGVzQ0ZIdVNYQ0pFOWJ6ditOWkNOZDZRNE1Uak1UM2FHekNkZHRIWDY1V01DUlpwYVlhSWlLaEVIenlOc2pGWkp1ckdYUzlNOTRvOTlmclFVK0k2cU8xS1M2MURlRkdBQU5vb1VSL2FQL01LOXUzOE00ZS9GMk1Ed1AiLCJtYWMiOiIzMDUzNWU3ZGJhN2MyMjM2ZTVmNTM5MTViNjRhN2E5Y2ViOTc0MTU1NjI2OWE0ZjNiNGI2OWQ2Yzg1NDYzNjQyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjE2akRFNHV1ZEVHREVXUCtIUzI5a1E9PSIsInZhbHVlIjoiaWVNbVN1cktpek5ZRldlR29zNVZJbUl2VTgwUkgyU1E0cnUyWjd2cGxvZmYyeGJzOUJwenYreVlQWlE0U3p2VHdlVm9MTDM1QnZrdGZhUWlLbExkMnRlVGY4aG5wVW5zM3haZENkYXZwZ0JNQndkalVKWnQ4WGluMG1ydXA3bmkzL1RwRHMvZ0FQdzRXVUNIUGhKeEhrNFoxM3d2QjdkbHNFZ3o5WjZabkgvS05CVWFqOVJnZS96czJyMUF6WFZjQ0hlVXYrZjhoN0JPYWo5dUZUUllScjQwSWhCS1RzdVhGaG5yWlFOTk5ZYThzVG1QU0xZaW1qQXR0S1hhNXdyK3NML0dHRG8wS3Q4U3RHV3p3MXJIaHQ2YlBTWEhOczdRUWZJQWxhNGxuNTJ0U1U2TGtURTdJdVM4TGJiYkhwMXV5Um9EWE5IUUx0VHZJczNPa3lJWFVHMFBwWWZnbGJaZUdPeFM4ZFJCVnVGVGgwemhTbEgrRFBtWnl4UXdtMWhNaEpRWkN5ZTJDZ2dsbGczbmNGU2NEdk9PYXk4UWdPd2RLRFYybk01d0NNN0hWM0QrdDFWOG5TTG5VeTNCL09zdzFwdk1SUzFrY2hyWmFpTTJiSHplWVpMWFp4T2VDb2NGYmlOdXQ0VGFybmliRFFJR25talAxS1U1N2kwWko5dkciLCJtYWMiOiJiNmQyMDE1MWE1YjgxMWJiYWM1NTNmMTgwMjgxMTdmNmU3YWY4YTViYzczYzJkNTIwYmMzMTNmZTAxYWFiNGU2IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ii9iWkxlcHBwR2lQdVZBQnFiY0diM3c9PSIsInZhbHVlIjoiVmlMTjVXNm81RlpwenNMNE94TTlaVUNWeldEcjlFUmJNOGVjN3VYc1UxTStBenZ3VFByU3JqcjliSUNPS29RUUpIZG1TWCthcVlVTFdTbjc4NWwyWVAwb3IvR3VpYXoralpCWWt6TmtKalpDS2FYRXRjUjN3bzNRV1FVMW1Id0kwN0oxMS9hYUMvcVNlSG1Jby8yZWI2RnY3bitTYVpOL3B1SkNtbkZ4UHJSa3IxNDFlZUNCQlFGKzd1cTBmaysrejAzRVJSVXhYOFFKdGJUczZwYjExa3JjRndYSWNac2VmVkZrSWhmdHUvU1JWTldVbzhZdm95MElEazhVa3pIQU9kMXR6U0pXY2JVYjNXSEFYMVhpOVFIKzVCN1QzbDVQRnJ6MkZqQXRpZlE2Y3NjVHhXRCtlNHZEVTc2Q0Npdm9FRmhMYWdMc3lwOUpQVHpBbmlNWTFwRWVwMmN6UU1lYkx1NjJRYzVhMWVxdDBjS2QvdzZTQlNRV3hKcm5MZ2RxVXpIYTdPSGVzQ0ZIdVNYQ0pFOWJ6ditOWkNOZDZRNE1Uak1UM2FHekNkZHRIWDY1V01DUlpwYVlhSWlLaEVIenlOc2pGWkp1ckdYUzlNOTRvOTlmclFVK0k2cU8xS1M2MURlRkdBQU5vb1VSL2FQL01LOXUzOE00ZS9GMk1Ed1AiLCJtYWMiOiIzMDUzNWU3ZGJhN2MyMjM2ZTVmNTM5MTViNjRhN2E5Y2ViOTc0MTU1NjI2OWE0ZjNiNGI2OWQ2Yzg1NDYzNjQyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2146845662\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-903208428 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"21 characters\">&#1578;&#1605; &#1573;&#1594;&#1604;&#1575;&#1602; &#1575;&#1604;&#1588;&#1610;&#1601;&#1578; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-903208428\", {\"maxDepth\":0})</script>\n"}}