{"__meta": {"id": "X3292a7096941f22c09cca6a043fd657d", "datetime": "2025-06-30 22:36:20", "utime": **********.174615, "method": "GET", "uri": "/add-to-cart/2352/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751322979.660211, "end": **********.174635, "duration": 0.5144238471984863, "duration_str": "514ms", "measures": [{"label": "Booting", "start": 1751322979.660211, "relative_start": 0, "end": **********.045358, "relative_end": **********.045358, "duration": 0.3851468563079834, "duration_str": "385ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.045367, "relative_start": 0.38515591621398926, "end": **********.174638, "relative_end": 3.0994415283203125e-06, "duration": 0.1292710304260254, "duration_str": "129ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48673752, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00792, "accumulated_duration_str": "7.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.089759, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 22.096}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.103212, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 22.096, "width_percent": 12.374}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.122469, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 34.47, "width_percent": 9.848}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.125415, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 44.318, "width_percent": 4.672}, {"sql": "select * from `product_services` where `product_services`.`id` = '2352' limit 1", "type": "query", "params": [], "bindings": ["2352"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1314862, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 48.99, "width_percent": 6.061}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2352 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2352", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.156427, "duration": 0.00304, "duration_str": "3.04ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 55.051, "width_percent": 38.384}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.16116, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 93.434, "width_percent": 6.566}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1674455938 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1674455938\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.130572, "xdebug_link": null}]}, "session": {"_token": "iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2352 => array:9 [\n    \"name\" => \"مجسم شخصيات مختلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"2352\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2352/pos", "status_code": "<pre class=sf-dump id=sf-dump-1096878032 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1096878032\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1136148357 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1136148357\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-82414069 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-82414069\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=6nrx0y%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1aj6s00%7C1751322883410%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFOSWVKdk9ka2l1cmNCR2o0SjRxS3c9PSIsInZhbHVlIjoiWlBTR0dwOGs2ZnkrQW81cXZxTVI3OUlOVHJ1dDlSVlMwYzRGMzZtNm5wNXR4bklCaVdJM2RIbjhpdjlabU0rOWVaenV3LzhDWDFMenVLV1lTYStDeEdGZXNvVGZzOEpUSFR5UDhkamtBTjBQK1Fnenl4TlFCY1hWeVhSSHZoWnk4VFYycW1WZm5sSU90UCtMOGVlLy9Dc2xLUFlqK2JQYXNGZzNxVGJBS2dmcDlZUzZsRjV4Q0t4ajBuOVlpQUE5blpMQWhGNEdKWEM5cTVJU3JyNDl2bmV5cGhYTGNYRkswMG13T3VpWEJFaUVJSUhGK2VCcFQ3aWI1MUUxMFVEZnYwQkxKRlVpaStsR3BpRmVKaHZSblVVRW43cmErWkFENjNmOVV0Ukl1ekEwUk0wOVBEd2ZjUFhKMnVUVEpWdjJzVlFWV1NzWThIR3dhRjNYOFhOZmIvOU9yNVRoUzQ1Y2FJdHY0T3dUSDJ6TnZyMUZXazkwSnhOZGtBaVlaVCtvNVQwTDVXU25LUXYwbmZ1ZE1Xb2RDV3ZhUXE5a2JFSlhSL0RBcWlkUGh6eHpvcGY5cDNUSzE2QTBzaENyWTArM01kNXF1eDhNU1BuTStkZnpBaG9sa0pSVnhVQ2ZodjBNZDJUSlB4YUI3d2g4dU14bmVEMnZaemVyQ2pYNkQvYVAiLCJtYWMiOiJlNWM5YTNlMDVhMzgzYzYzNWU1YzA4Y2MxNzYyMjg5ZmRiNTM0YWYyN2ZhMTdkNDRiMzU4YjI0MDczMTQ0NmE0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkpNQXdDbURCODVwNFNVaDNidE5oNGc9PSIsInZhbHVlIjoiSWRDakNUR3draytjekVmNXFTTTlpWjQ5dDZyR1dhMHpsd0VTZFk0MXpuUmRxanAxdHFGalkwcjloWHpWVFVaV3hFT2xuQkxHSXFjaDAyZ1RVTnE1NmRNeE5mTDVieGFkaDJCUHJ6SHRHakIySjZJMmxGbFRWSmJpblZFUWpXTEN1U1g0YWlSR3d1UThKSDF6T2RMYzA1YW1uWTU1KzVrMHZYcStaaU05c1grUUduTXhuOXFFWDc3UFNTUWY3THZwd1gxR1ZSakdlRGh0N091V21qWkYwSUJkYjBETlVEaHJ2TGptUVhIb0t3Qnhqc0gvNVpyVmtHOUswVlViOTB1TmU5S2xPc3dpNmFWZmxFdnc1R1ladHlab2FYNDFxR1ozcWp2UmdmenJTLyszU0laejFSU3dEMFkraitBd1VjVXZMR3NJSXgxZFI0YmxMVVl0aXIvMmdZRGo4bkU2MWppbzlGcjdWNWlkd05EemszNFVBVjZydE9iNVUzbE9SOWdiOUJRV3JVbFExK1Q0cWxGSWs2U1QzZjBhL0h4RlJDYmVRYmZ5RTBiZDFOSCtZRkFWOWlkOFg0d0RiRG9HdzVCbk03Y1JCdlE3UGhBNkhxODMyT1Z4ZjNhMjZibmhaMnZDdDEwb1NTSEsvUHF6NVl3Vkt3Z25JcEtla1VPTm90SisiLCJtYWMiOiIyMTk2OTc2ODc1MmE4MTQ3ZTM2MjE3NjA2NDBlZmVjYzgzM2Q0ODA3NzI1MDhlZTM0NDMyMGJhOWI2MWIzYzhmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2130448640 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A9rdyCRm7SKpfljuMnVO7IpcgTBMITMjowAdsmJo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2130448640\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-367527268 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:36:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlmMGxaRnJuMDlQZ3JNOUVJVGgreXc9PSIsInZhbHVlIjoiK3FxWGRYRUo2b2o3OEhqYkJ2Q0Z5cG02VXlmNXl5Sk14WFhjVHRzanN0L1JnRFdaMm5tRjRoaHdsMEErN2o5WWJqVm5rRUN5cFBrZTgvUE4xN3cza0NMa0YyTUpoVjBjZWkza0dCTU1GWWx5cVVkUGNIcFBpd1kyTCtRMmZvVk5pNGQ0eDNhTGJ3c0c2RjNkcEo1bHQ1TU11c1F4QkZoTzAvaUNGVmZPTEhzL3FmN3J6Y29yTysyVFF4U1ZGSkZYdUhocHZxTjZUaFlTdEV5ZTdzbHU5amczeEh4L2tjZ3hCQWpXbkRZcnJrbVNrUkYyYVhUaHNBdlZBWW1SSHRlTmgyby91M0NDRzFtRklmSHlXVG91c0Y0UGJ4VVBIQzhoa2dCWUc5T21TNG5ob0xCQ3pxcFREdVBPUm5YMERzQWZaajV6WE55NGtuS3hwRlkrdlA4aGxhektGMlJlOFNZRWdubVFDOWptTndRbmVwYXdWUll3L0h2ZlJ2TUpxNEppUU50UjBTaXhlbjlWeWNLTkJaS0llWThiWWQzQVN6S3VaMVdOVVRNdW9wdHdSTDR1OG9mcW9IQlY5N3RoWUsxeWxUUHkzdWJWY3RlZlZrb2pxR0JGTklKb1dNOHd5c29HV0JDTUhLK3FpL2VPNzhzc0Nnc2hjYkl3SXlVaDlVS3kiLCJtYWMiOiJjNzBkNDAyMDhiOTAzMzhmN2IwNDQ1MGMyNzBiMjhlM2MxN2U3ZjhjYjMzYTdhZDliMjNiNzE5MWQ5ZjAxMGE5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlpnY3hkU21nWElhUWVBV1dvN3ArNXc9PSIsInZhbHVlIjoiaExoZzJLeFdQUzdXcllxekI2ZXVudktRY2ZuS0puQ2VzYmVUSDdLcUFZRmd2VFNjdXI5MExPVVVXUk4vd3Y2MGVQZEIyZWxobm5QRDJDUDhSVDdBcDRpOFVtZzRtWDIzN3l4d2ZTY1VjcGxDWWJLOCs0bHpLWmEydS9nT2lWaHd6NnovcVVhRmJENFg0bWNJNTIzNGpaOVhnWHBnUi9GcS9KNnpjZ2V6WFNxSVJFeTg1d2xUYml6ZVd0bkpnRVBnSFNBalNIazh4NG9xdG1XdC9lRHZpeFVJMHgxRHJtNW9raUxpb2hPR2FteVRHci9kRTAwMVJ4R0NXdlppbEw1VCsvTTY3SFpjTlB4dFZacXdPMU5RREpJSGhadXFXaldUY2dMN2tHUWZPcGM3bExMMWNaMXlRTDdvaEo1ekxNb0V5eXJkeVpOMlgwM09EbytFckROUHhtWjMvWDVDSHdnSHlDc0tudCtBNkF0SVA4bHp0OFIySTVPY2VJTVVKWFQ4dmpKb0tVZFJJemgzdGJZMnZHVi9EQlRDR2YyU2l3NmNFMnlNditBWnk3amZ5U1BVK2pHVzZCODZtSExGWlVnMDdtUVROcDBGeVY0M1kvd2Mra2cvZ2xjSmZDdktLdHpmTEpjei9pSmhId0dIcU50NUR1NzN1cTcrWXFiUWl6Rm0iLCJtYWMiOiJlYmI2NDc0YmIzZmNkZjE3MjRkYjM0ODMyYzIxOTc5MTc2NzI1NTI1YzUzYzMzYzU0N2ZlNWVhY2IzMzlhNDIyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlmMGxaRnJuMDlQZ3JNOUVJVGgreXc9PSIsInZhbHVlIjoiK3FxWGRYRUo2b2o3OEhqYkJ2Q0Z5cG02VXlmNXl5Sk14WFhjVHRzanN0L1JnRFdaMm5tRjRoaHdsMEErN2o5WWJqVm5rRUN5cFBrZTgvUE4xN3cza0NMa0YyTUpoVjBjZWkza0dCTU1GWWx5cVVkUGNIcFBpd1kyTCtRMmZvVk5pNGQ0eDNhTGJ3c0c2RjNkcEo1bHQ1TU11c1F4QkZoTzAvaUNGVmZPTEhzL3FmN3J6Y29yTysyVFF4U1ZGSkZYdUhocHZxTjZUaFlTdEV5ZTdzbHU5amczeEh4L2tjZ3hCQWpXbkRZcnJrbVNrUkYyYVhUaHNBdlZBWW1SSHRlTmgyby91M0NDRzFtRklmSHlXVG91c0Y0UGJ4VVBIQzhoa2dCWUc5T21TNG5ob0xCQ3pxcFREdVBPUm5YMERzQWZaajV6WE55NGtuS3hwRlkrdlA4aGxhektGMlJlOFNZRWdubVFDOWptTndRbmVwYXdWUll3L0h2ZlJ2TUpxNEppUU50UjBTaXhlbjlWeWNLTkJaS0llWThiWWQzQVN6S3VaMVdOVVRNdW9wdHdSTDR1OG9mcW9IQlY5N3RoWUsxeWxUUHkzdWJWY3RlZlZrb2pxR0JGTklKb1dNOHd5c29HV0JDTUhLK3FpL2VPNzhzc0Nnc2hjYkl3SXlVaDlVS3kiLCJtYWMiOiJjNzBkNDAyMDhiOTAzMzhmN2IwNDQ1MGMyNzBiMjhlM2MxN2U3ZjhjYjMzYTdhZDliMjNiNzE5MWQ5ZjAxMGE5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlpnY3hkU21nWElhUWVBV1dvN3ArNXc9PSIsInZhbHVlIjoiaExoZzJLeFdQUzdXcllxekI2ZXVudktRY2ZuS0puQ2VzYmVUSDdLcUFZRmd2VFNjdXI5MExPVVVXUk4vd3Y2MGVQZEIyZWxobm5QRDJDUDhSVDdBcDRpOFVtZzRtWDIzN3l4d2ZTY1VjcGxDWWJLOCs0bHpLWmEydS9nT2lWaHd6NnovcVVhRmJENFg0bWNJNTIzNGpaOVhnWHBnUi9GcS9KNnpjZ2V6WFNxSVJFeTg1d2xUYml6ZVd0bkpnRVBnSFNBalNIazh4NG9xdG1XdC9lRHZpeFVJMHgxRHJtNW9raUxpb2hPR2FteVRHci9kRTAwMVJ4R0NXdlppbEw1VCsvTTY3SFpjTlB4dFZacXdPMU5RREpJSGhadXFXaldUY2dMN2tHUWZPcGM3bExMMWNaMXlRTDdvaEo1ekxNb0V5eXJkeVpOMlgwM09EbytFckROUHhtWjMvWDVDSHdnSHlDc0tudCtBNkF0SVA4bHp0OFIySTVPY2VJTVVKWFQ4dmpKb0tVZFJJemgzdGJZMnZHVi9EQlRDR2YyU2l3NmNFMnlNditBWnk3amZ5U1BVK2pHVzZCODZtSExGWlVnMDdtUVROcDBGeVY0M1kvd2Mra2cvZ2xjSmZDdktLdHpmTEpjei9pSmhId0dIcU50NUR1NzN1cTcrWXFiUWl6Rm0iLCJtYWMiOiJlYmI2NDc0YmIzZmNkZjE3MjRkYjM0ODMyYzIxOTc5MTc2NzI1NTI1YzUzYzMzYzU0N2ZlNWVhY2IzMzlhNDIyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-367527268\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2352</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1605;&#1580;&#1587;&#1605; &#1588;&#1582;&#1589;&#1610;&#1575;&#1578; &#1605;&#1582;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2352</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}