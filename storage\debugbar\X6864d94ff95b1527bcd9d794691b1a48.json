{"__meta": {"id": "X6864d94ff95b1527bcd9d794691b1a48", "datetime": "2025-06-30 23:10:26", "utime": **********.080621, "method": "POST", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751325025.650327, "end": **********.080643, "duration": 0.4303159713745117, "duration_str": "430ms", "measures": [{"label": "Booting", "start": 1751325025.650327, "relative_start": 0, "end": **********.001901, "relative_end": **********.001901, "duration": 0.3515739440917969, "duration_str": "352ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.00191, "relative_start": 0.35158300399780273, "end": **********.080645, "relative_end": 2.1457672119140625e-06, "duration": 0.0787351131439209, "duration_str": "78.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50965640, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@SetOpeningBalance", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=134\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:134-167</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.011389999999999999, "accumulated_duration_str": "11.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.032269, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 16.067}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.042417, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 16.067, "width_percent": 4.039}, {"sql": "insert into `shifts` (`shift_opening_balance`, `is_closed`, `created_by`, `warehouse_id`, `updated_at`, `created_at`) values ('1000', 0, 22, 8, '2025-06-30 23:10:26', '2025-06-30 23:10:26')", "type": "query", "params": [], "bindings": ["1000", "0", "22", "8", "2025-06-30 23:10:26", "2025-06-30 23:10:26"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 147}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.053736, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:147", "source": "app/Http/Controllers/FinancialRecordController.php:147", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=147", "ajax": false, "filename": "FinancialRecordController.php", "line": "147"}, "connection": "kdmkjkqknb", "start_percent": 20.105, "width_percent": 29.939}, {"sql": "select * from `financial_records` where (`shift_id` = 75) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["75"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.058809, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 50.044, "width_percent": 4.917}, {"sql": "insert into `financial_records` (`shift_id`, `opening_balance`, `created_by`, `updated_at`, `created_at`) values (75, '1000', 22, '2025-06-30 23:10:26', '2025-06-30 23:10:26')", "type": "query", "params": [], "bindings": ["75", "1000", "22", "2025-06-30 23:10:26", "2025-06-30 23:10:26"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0606902, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 54.96, "width_percent": 22.476}, {"sql": "update `users` set `is_sale_session_new` = 0, `users`.`updated_at` = '2025-06-30 23:10:26' where `id` = 22", "type": "query", "params": [], "bindings": ["0", "2025-06-30 23:10:26", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 162}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0647721, "duration": 0.00257, "duration_str": "2.57ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:162", "source": "app/Http/Controllers/FinancialRecordController.php:162", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=162", "ajax": false, "filename": "FinancialRecordController.php", "line": "162"}, "connection": "kdmkjkqknb", "start_percent": 77.436, "width_percent": 22.564}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "success": "تم تحديث الرصيد المفتوح بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-462675138 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-462675138\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1388913975 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1388913975\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1065278835 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>opening_balance</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1000</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1065278835\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1162457793 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">68</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlBZRmNVeVVmbzBMR3dkc3Z5U1F4OVE9PSIsInZhbHVlIjoiZUgyMmZhd0c5bFl3VHlwSHVKbjJoTDdLcnhoek9XSHZUUEtwSTdYV1h5cjZRWWVGM0tLQm8zWFZtRitnVzJXai9SY0VtVG9ldjJQa2V1RysxM2RHKysyM0ZwNndlaVFSNFo5emk3dmtBREFLaXc1a3Z1LzA2a1BvL1Q4cVRVRlV3TFMrWWhqZjg1OXQzWnRiS3ZoVkc2bEJmZGt3cUxkMzI4Q2hoYXJBZC9oZEVqbHdEVTN2dWxSWlg5c2x2ZG1VNzFNa3pnQ2pRQy9BVFRVdHBVVlhhWFB5MkNwcGlUNzJxRTZXUWdiWldVQWsxNkt0ODRrN1lBSFVMeGhHOHlHdWNaSzVYbnNrVmw0Ti9ZMTZyV2g1ME5pQUN3aVpsOHh2VjUzVDRTQWpDN1YydkhTcTlvcGZraXVuRG9abHRsWGI0Vm95M2p1V29HM1JYeWg1QzBzRHFyQ3FiQzdWbkZPSitWYU13dnA1aXNzdWFYQml5T1JTcDhPRVdFVmI3c1dzQ1N6Y1ZHaVR0ZitCYk1zeXlBRmx5WjNuM2ZOcmFUT09JTXJYdlA4cTQzREE2cmJNanRJV0Q5c05ZSHdoUEZhdlZKM3hOZEs0cFIrQ1psWnpsZ3BLZHUyL0V4M2VxTmVKcWVTeUVBb093UGNiSkFQamFBZzZ2aWR1SmMwMFJ3SEQiLCJtYWMiOiI2Zjk5M2FkY2NlMzA5NWNjMTIwNGYyOThiZGQxNGNlNjRhODZjYzlhM2JhODk1ZDk2MWVmMjdhZDIxYzcyZWNjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlpIU1lIWnNPb1Q1cHVtOVJhYUpIYWc9PSIsInZhbHVlIjoiYWdmeGl1ZXlWMHF4clhnMGM3T0pvVTdrL0hnOHpFVVFvVHlaNXA5YXZxdHFmN1R5UlNkNUpxS0NkVGRLRUdDMjNzZ291T0R0N1doWnhubmNRQm54VnZOS1ZlMDFyY3Zld3p5UkgyL1hKVWpENjZiTGlKbjFPQmNJN0JnMHdZZzNMODVoUWo5VFkrOWZML2VTTFFTb0xNbTdWNlRIZHdWcStXYnd4anRrK2h4SFJFcHpEaFZLU0k4SU9iZDhZZXJPVUs1ZjN1Z0RPWTQxaDFCNlBuT0t0SDFkcHlHUC8wTzRIaFYybmhscWxTK3hXVVhUeVROSlFBS01RV2RtbTlxcUlZVlB4eEFleCsxbUFMSUV1MktadzJiT2hRQ2p5NlBML2I2Q1NvREM0dkQ0NVVtMDFrZzduRGszUkd1elpETC9Zak8xeVI4Z3d3R0tiS01KT3JkTllxaUFVd3FWelRFTlVpa1lGQzg2bDI5UzJDUWR5dTRTNG5LZVBYSjVNSmtPNlZwMXBMZ3hmQm8rTUtGSGhCNmZZekpLM2NOMGNJemZFNEhvRk5rYzJXTlRhOFZxSFhlZlQyL0p3dG9aMjNZRHY4NWtaRlhoR1k1S0NORStmT2xjd2RxS2lqNFAxUEJObWRWa3hpbXQ2cW9XZi85RkdMMnlmSmo3b0F5R3U1QlQiLCJtYWMiOiI2NGI1OTFkOWFkZTczOWFiNzQyZmQ0MDkzODgxYzgzOGJiNThjODc5YWU3YmEzN2ExZDNkZTFlMTQzNGNlNzgzIiwidGFnIjoiIn0%3D; _clsk=eiqg7d%7C1751325022992%7C13%7C1%7Co.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1162457793\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1775478617 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1775478617\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1948818848 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:10:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkI1UFJOa3Q0R1NZbDZZUnpZTkV4R2c9PSIsInZhbHVlIjoic3hGRkdNeWNkWUVRT2JUQ0VEMTNJaDlMRUx2L25QQXJBN01LK083V252WVlVSmIwTkUxV2kvQ0tXajA2UHhaZ1RYSTV0K2plOHVKVXM5bjM2Qy9OWjNGclVOWm41WFJ5QXptckVCVWZNb2E5R0FUMVI1T2pyeTJBVDlLaXZ3UytHMlhuQ1FiT3I1cmFvR0IvWGVYZzJybEsrcG56WVdHYkZ4RVIzdzVrRnRFdmtaZ0FVRUNNNTJlNkJwaG5VR1E0R0owcGMyRUlXYTB2UlA3N3VrdmM0VVNEcHBBSFBUYWxQaGdmWmZ4NUpGT085QkNIa2txWUI2VU80dC9xblZiZFpoVUF1R2ZhRkloQVVkOTJOaGkvcmt2WUpmZFNFL2hRZjBTRmNsMm5DQjBCN005MTIzZjdCM0NXRHJJZTVzVkcvRTJVOHRPQ2hDMW5DQnZGWksvZCtsVW9Dd01HMmZON2FoQTlQeVh2UW80T1RPT3pySk4xdlcxNFhxMnI0NFRpUUU0Ly8xNTRidGpPOXgycURDeUYyNUFhS2FYalBvSDJvR1BDOWlrWE82Q1c2MXZ5U0dnQk5LNFVXRzg5amN1UlA3WVZYTlRldmJEUTNZZFJraTM5QlNxRXE2RGFTcmZxaC94VDcreHUybldZVUNKTkJjUWlXN1FoMGRKWC8vUVMiLCJtYWMiOiIyOWUzNTgwZDJiYjk0ZDliMjlhNGU0NmM3ZGE5N2U4ODAyYjgwZWIyM2Q2NmE2MmZkYTBkOTc0ZmJlNjZlMmRmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InZGWnlLb2xQaG1YYTcvV2k4OHZybHc9PSIsInZhbHVlIjoiNjF4Kzkwd01BL1RCNmY4OHdvNDdCMEdremwxUUU4K0VQN2xqR3hKM0g3VlhtWTJ5R2IwMTlXUGkvcU5hdmhNdnFzSTV5N3RSa20wZmtYWU9QWGRSR2U4T25ERktHeXp6eVNvd0lPbStHa3A4VTh3dVpRNElnZDk2RGYvMytzdEhXNWxxcnVEQTVCNW10Ui9uL2E3WTgvd3hGTG5xemM3b296a2p1VVMxdjhJTnU5QTI0RDgzNUVOclhSeEJKNm5mZWRwQ2hkbmcyKzg5bFJhOGZ6djhvYzNJa1F3bjF1ZGxoMFRTMDVCeUZrdzJEaWNjM0hGeWVSTklWUnJ2RXhnZUlyVVZpZUhES2gxNVFKRTByWll2enY2YjNhdjJqSTJvbFdnNGRBbWswWTMrVlJIY2x0SzFTbGdzQW93MUo2OG85LzZFUXQvdHZOejRQUUZQRHFkYVI3Sk1kaXo4SWY0VTRBNURHYUo2TEJnMnRMTlNJTkI3R3lOVENhK3Faa0RGOGtwRElvUjdsbEhqODRocHV2dEMxbHlKM2c1R2tITkY2ODRhVnNsZm1HNDNYZ2FlK1VFRlloalEzUEVhamo1bUlINENWK09NOEhiS1pRM0JGcGRsREYreTZOcEVXNVVqNWxZYThsdmdya3BSV1hITzJpNngxdXNEdWRiOUtJdDYiLCJtYWMiOiI0NGM0MGVmNzI4NzllMjFhYWVlOTBhNmI2Yzg3ZTIyODU4NWM2MzRkMDAzY2MyZGIwZWNmOWYwMjIzZTYyNDg5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkI1UFJOa3Q0R1NZbDZZUnpZTkV4R2c9PSIsInZhbHVlIjoic3hGRkdNeWNkWUVRT2JUQ0VEMTNJaDlMRUx2L25QQXJBN01LK083V252WVlVSmIwTkUxV2kvQ0tXajA2UHhaZ1RYSTV0K2plOHVKVXM5bjM2Qy9OWjNGclVOWm41WFJ5QXptckVCVWZNb2E5R0FUMVI1T2pyeTJBVDlLaXZ3UytHMlhuQ1FiT3I1cmFvR0IvWGVYZzJybEsrcG56WVdHYkZ4RVIzdzVrRnRFdmtaZ0FVRUNNNTJlNkJwaG5VR1E0R0owcGMyRUlXYTB2UlA3N3VrdmM0VVNEcHBBSFBUYWxQaGdmWmZ4NUpGT085QkNIa2txWUI2VU80dC9xblZiZFpoVUF1R2ZhRkloQVVkOTJOaGkvcmt2WUpmZFNFL2hRZjBTRmNsMm5DQjBCN005MTIzZjdCM0NXRHJJZTVzVkcvRTJVOHRPQ2hDMW5DQnZGWksvZCtsVW9Dd01HMmZON2FoQTlQeVh2UW80T1RPT3pySk4xdlcxNFhxMnI0NFRpUUU0Ly8xNTRidGpPOXgycURDeUYyNUFhS2FYalBvSDJvR1BDOWlrWE82Q1c2MXZ5U0dnQk5LNFVXRzg5amN1UlA3WVZYTlRldmJEUTNZZFJraTM5QlNxRXE2RGFTcmZxaC94VDcreHUybldZVUNKTkJjUWlXN1FoMGRKWC8vUVMiLCJtYWMiOiIyOWUzNTgwZDJiYjk0ZDliMjlhNGU0NmM3ZGE5N2U4ODAyYjgwZWIyM2Q2NmE2MmZkYTBkOTc0ZmJlNjZlMmRmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InZGWnlLb2xQaG1YYTcvV2k4OHZybHc9PSIsInZhbHVlIjoiNjF4Kzkwd01BL1RCNmY4OHdvNDdCMEdremwxUUU4K0VQN2xqR3hKM0g3VlhtWTJ5R2IwMTlXUGkvcU5hdmhNdnFzSTV5N3RSa20wZmtYWU9QWGRSR2U4T25ERktHeXp6eVNvd0lPbStHa3A4VTh3dVpRNElnZDk2RGYvMytzdEhXNWxxcnVEQTVCNW10Ui9uL2E3WTgvd3hGTG5xemM3b296a2p1VVMxdjhJTnU5QTI0RDgzNUVOclhSeEJKNm5mZWRwQ2hkbmcyKzg5bFJhOGZ6djhvYzNJa1F3bjF1ZGxoMFRTMDVCeUZrdzJEaWNjM0hGeWVSTklWUnJ2RXhnZUlyVVZpZUhES2gxNVFKRTByWll2enY2YjNhdjJqSTJvbFdnNGRBbWswWTMrVlJIY2x0SzFTbGdzQW93MUo2OG85LzZFUXQvdHZOejRQUUZQRHFkYVI3Sk1kaXo4SWY0VTRBNURHYUo2TEJnMnRMTlNJTkI3R3lOVENhK3Faa0RGOGtwRElvUjdsbEhqODRocHV2dEMxbHlKM2c1R2tITkY2ODRhVnNsZm1HNDNYZ2FlK1VFRlloalEzUEVhamo1bUlINENWK09NOEhiS1pRM0JGcGRsREYreTZOcEVXNVVqNWxZYThsdmdya3BSV1hITzJpNngxdXNEdWRiOUtJdDYiLCJtYWMiOiI0NGM0MGVmNzI4NzllMjFhYWVlOTBhNmI2Yzg3ZTIyODU4NWM2MzRkMDAzY2MyZGIwZWNmOWYwMjIzZTYyNDg5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1948818848\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1546694079 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"29 characters\">&#1578;&#1605; &#1578;&#1581;&#1583;&#1610;&#1579; &#1575;&#1604;&#1585;&#1589;&#1610;&#1583; &#1575;&#1604;&#1605;&#1601;&#1578;&#1608;&#1581; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1546694079\", {\"maxDepth\":0})</script>\n"}}