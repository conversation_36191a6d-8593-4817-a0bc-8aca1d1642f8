{"__meta": {"id": "X4081e44b7a6e7ba20e0741c243ccf769", "datetime": "2025-06-30 23:14:02", "utime": **********.770985, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.333965, "end": **********.770998, "duration": 0.*****************, "duration_str": "437ms", "measures": [{"label": "Booting", "start": **********.333965, "relative_start": 0, "end": **********.698333, "relative_end": **********.698333, "duration": 0.****************, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.698341, "relative_start": 0.****************, "end": **********.771, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "72.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022039999999999997, "accumulated_duration_str": "22.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.725993, "duration": 0.02094, "duration_str": "20.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.009}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.755801, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.009, "width_percent": 1.724}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.763684, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 96.733, "width_percent": 3.267}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325241921%7C32%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxDSldSMFBwNzF4WU5uc0xhcXhvaUE9PSIsInZhbHVlIjoiLzdOK3F3VTdNUUhTMnpiWG1XeDQ1VHJnNk1ud3hzUnVwVVIvQ29JUHAxRmkwUVVvYm1zVEYza0JJdjUxbm9rY0U4akJiOTJHem5SWDFYditlbGM1aGQzUmJ2amdWUDFObGlySUdONHR0VXQveHBZMWx0YllYUHl0TzRyNkFBL2FnbUhCbjhIa29BMk4xWVNpTU1wNW5GUCtvT2ZWKzZYS2tsMkl2R0J0aDRBOHgwWTFkK21BYXIzWkw3ZlMrUzM5S1BPUHpjZFUxOGQvUzY5VnpuSVJ6bmkxOXcvRjNzWi8rbVY1aHQ2b1dvVGNMYW53YVY2Y2ZBT2hBcVFwYzBpVmxFUWllMGh6ZXAyRnpwUkpKcGl0czk0ZFpTUHI4NyswVmt3bXZ5MTVpNEs2cmhva1hweTMrZHI2Tzc0MjhKeUNDL09aWldiZnlJRTRINlhuTTBjaCtRMjQ0SWZNRU5zRW1kQXVwOW9XbDVLT2l3d1BmTlFvcVhEcThoL0YxTnBQeHFoZEdJcFc4RlhxL2t2SkRsdUxpc3FHUkFKb1JPeXBBcG1vT3FFNDY4Yi9zMUNiQlh3TnVTTEtHTVVvaWZodHBpc2owckowVG5ZWWJnZlM4cWxKZ1pidE1QZlljZ3VPNUQ4Yk1OQVYxR1NVcmNMUEJ3K0pDMVdLL0NNa01BcW8iLCJtYWMiOiIyYjU5OWE5YmQ4ODE5MThkYTI1MTc1YTEzYjlmNmI4YTIzZjlmOTQ5OGIxNWNkY2JmYmYzYjMwNzExYThkZGZkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImhqRFYyTktCMG5hM2hvU3RWS1pHd2c9PSIsInZhbHVlIjoiTTNOM0g4cjVYeDRXVGdTWEdkeXozbUk0M3FlbjIwdGNURCtYaVM2M1daWXFyME9WaFF0TkRxQVFBelVTdDhpQytGMWhXdHQ5RWp5c0Jaa3BtS0paTmNUZFN6Skx6eXNWUTlDcUhBUHZVSTlpQ1pneHF0OXJBcCtiWDY3TDNDYWpLSGc0cElQNGlPckJWSkc0eXU0cS8zQXB6YW9UVWlCQlRMeXZEcTBpeUxBcUlXcTA2N3liVzZOZEtvTzc2eGhCN3ZRS1NmVHZCVDQ2K2p4NS9kd3hrdDhrb0VQZllJbXZ4ditHd2F6WkQ0Q1BveFZtM0x3L0Q5YWI4OUJCdGd6RUI2cjBlNFUxRTFpc0hoMTlReTMrWE5qdnIyaUhUaXM4eVlHOUVNa0t1VlA0Y1g4WmhXRlhxdmhlaVExcVNMa2FFTXo5TkNUZlMyaTdMOVorZldzbmRwTWdDbnFTRDdQaEdhcnJyK1hrUCtpQU10WWpRWWprYk1qTVhmcmNBVXF3elgxbWhkNEtPTUhzVmtJYXV2eHRSdkJIQVc1cDgxUE9obEQxUzFoR0RISThveDVQdWl6L2dMR1dCb3gwMHlCTVJKd3ZMeDNsZm1rcHp3WnRBbUU3ajRhR0l5WEJtUlNiVDlnUmwvUUpESVN1WGhsZXgwVnZncmZ1Y3JmWUJQcmsiLCJtYWMiOiI3YjEyMjc4ZDkxNWFkMDU1MjEwOTBlNDZlMWY1NjBlNWFjODEyMzI5NWZlMmVmNDA0MDg1OWZlMDViMzk2MmNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-357498660 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LzQkrwbVBtmiKyPqbktIsF6wiC8bftN1cyLybXUc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-357498660\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1113330206 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:14:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZmVmZJMUdKNEZibzdwT2pDWThUMnc9PSIsInZhbHVlIjoiQ0pIdVRkazk5RHJrS2pXU2VITFBMaU9ibWJqWGc1RldYYjJWWHVIYmZZL200ZklDZjViNStkRHVLN2pheHN2enpUekVPK0ptQnJKMGx2MW5ncjJleDI1Ni9zNW1ZWGJNbzVPRXd6UUdKbzRlakw1OFVER2ZrQTJ6blVPdDFCK1JzbUw5VUQzaEFZaFV2ZTYyY2lXdjhRdy8rTFpZVzlCQXlYUXdUUFZhalk2R3paS3V3aGhiZ2dMWGtzM2FEM2MyTURua3hwU3krOFIrQUZ3cHgwVjlTTllTQzBSaHdqRmJ1Z1I2bVNXWGNwL2dnMEFCQ3BTQ0xIM3NKNnk5VldKb1J6MG0yNzdsUGxEc3NjVXJqY0xjdmhjZDRPcW5NUlNoaVdLRHpoSmQ2UTQ2aTdiN1ZkQ1o4OGZwVjFoN1JGSHI2a0Nab2NTenRSYTV2M1FpeTdJV3RubEdWdDMyUWgreFJNVFZzalBrcXZrS3lQbTJ5aER4bmlXSG92WEVRdzFXemxCTG9WWGdZVlJITVMwWXUrYXlEMjNOL2toQ3NqUlgvamEwVDUyUGtvN2EyM2JBMTVLTVRqYzl1T3JZeUVzQldkU3dadzcvZGNpc25GVTV1Q1pzSG0xT0tmMytTQzh3UU84RFhML29zTDhKZll2SERTWmhlSTZ0cDNUZ25EWmUiLCJtYWMiOiJkZTVhMWVhOWQyOTRjMmZjNDJhNTBhMWEzY2E1ZDFmMDZkMDA5MjM3YTEwZTNkMWZiNGM2NjUxNDBkMDYwZGQ1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InM3WmcxUXFkZDEvLzJGckpXb0tQZFE9PSIsInZhbHVlIjoiVnB0N1FxQUI3Z1AxUmh5NkNtQTdYajZZdEgwaDFZcUltb1R6TFZ5STdOcW1XTUpnMW9HSUVwNU9aTFU5TEFaU3haSC83dXhCeWxTVlNVd08wY1FsRmNuZUxVT3VTNkJNUmdNRCtyc1VLRENaQzF2WGhvbmdhVWNYWGlCUitHdFplZmtxYmltMmIxRWJYQ0lrQVF3aStNdkI0ZUVnMGRFWXBEUEh2UGpwRCtKV2Y2MkI0WlNkTzUyZzlhM09XV1ZtamFNKzVndFpPeGF0WHl0MlU0VDMwa2ozYy9zZEVCREtONHlKSmZMRmFFZnFsWWMxL2d0TWhMcUtuSWZyN2xCRG5IUUpObFdpdW1rcE9YTXk2WFVNK3U1ai8wVGJXbXVsUzRMcUVZby9ycU1LR0VJVUcyeDIvSmF2cTAyZkFsanh3RXI2L3pvQjZMYTFxZXE3UzlvWTl4dUtUZ2RoUWdJUUlJSjJWSytmOW84WXh5d0VGZWIzRXZSSmRGRGRSejRrdUcvRnNkdmlNeDJ2cGRTUzlQMk1RanJHU2Z4bUo4T1F4TzExcFFFNm52WW82Qjdpb3k2Z0JEYWx0UytQNS83OVR3UFdTVVV5RVVYcUgwaG9yUEs0MWlSclJ3SEtCVHRTS2N6V0QxTVBYMXd4OFdyMEZJb096SmQycjYxVGZkd2oiLCJtYWMiOiJiNzYyY2ZjN2VhYTVmY2Y5MDViNTY2ODE5YTM4MDdkMDc3MTZkMWY0ZWUxODRlNjcwN2QzZjI5OTQyZmVhOTY5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZmVmZJMUdKNEZibzdwT2pDWThUMnc9PSIsInZhbHVlIjoiQ0pIdVRkazk5RHJrS2pXU2VITFBMaU9ibWJqWGc1RldYYjJWWHVIYmZZL200ZklDZjViNStkRHVLN2pheHN2enpUekVPK0ptQnJKMGx2MW5ncjJleDI1Ni9zNW1ZWGJNbzVPRXd6UUdKbzRlakw1OFVER2ZrQTJ6blVPdDFCK1JzbUw5VUQzaEFZaFV2ZTYyY2lXdjhRdy8rTFpZVzlCQXlYUXdUUFZhalk2R3paS3V3aGhiZ2dMWGtzM2FEM2MyTURua3hwU3krOFIrQUZ3cHgwVjlTTllTQzBSaHdqRmJ1Z1I2bVNXWGNwL2dnMEFCQ3BTQ0xIM3NKNnk5VldKb1J6MG0yNzdsUGxEc3NjVXJqY0xjdmhjZDRPcW5NUlNoaVdLRHpoSmQ2UTQ2aTdiN1ZkQ1o4OGZwVjFoN1JGSHI2a0Nab2NTenRSYTV2M1FpeTdJV3RubEdWdDMyUWgreFJNVFZzalBrcXZrS3lQbTJ5aER4bmlXSG92WEVRdzFXemxCTG9WWGdZVlJITVMwWXUrYXlEMjNOL2toQ3NqUlgvamEwVDUyUGtvN2EyM2JBMTVLTVRqYzl1T3JZeUVzQldkU3dadzcvZGNpc25GVTV1Q1pzSG0xT0tmMytTQzh3UU84RFhML29zTDhKZll2SERTWmhlSTZ0cDNUZ25EWmUiLCJtYWMiOiJkZTVhMWVhOWQyOTRjMmZjNDJhNTBhMWEzY2E1ZDFmMDZkMDA5MjM3YTEwZTNkMWZiNGM2NjUxNDBkMDYwZGQ1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InM3WmcxUXFkZDEvLzJGckpXb0tQZFE9PSIsInZhbHVlIjoiVnB0N1FxQUI3Z1AxUmh5NkNtQTdYajZZdEgwaDFZcUltb1R6TFZ5STdOcW1XTUpnMW9HSUVwNU9aTFU5TEFaU3haSC83dXhCeWxTVlNVd08wY1FsRmNuZUxVT3VTNkJNUmdNRCtyc1VLRENaQzF2WGhvbmdhVWNYWGlCUitHdFplZmtxYmltMmIxRWJYQ0lrQVF3aStNdkI0ZUVnMGRFWXBEUEh2UGpwRCtKV2Y2MkI0WlNkTzUyZzlhM09XV1ZtamFNKzVndFpPeGF0WHl0MlU0VDMwa2ozYy9zZEVCREtONHlKSmZMRmFFZnFsWWMxL2d0TWhMcUtuSWZyN2xCRG5IUUpObFdpdW1rcE9YTXk2WFVNK3U1ai8wVGJXbXVsUzRMcUVZby9ycU1LR0VJVUcyeDIvSmF2cTAyZkFsanh3RXI2L3pvQjZMYTFxZXE3UzlvWTl4dUtUZ2RoUWdJUUlJSjJWSytmOW84WXh5d0VGZWIzRXZSSmRGRGRSejRrdUcvRnNkdmlNeDJ2cGRTUzlQMk1RanJHU2Z4bUo4T1F4TzExcFFFNm52WW82Qjdpb3k2Z0JEYWx0UytQNS83OVR3UFdTVVV5RVVYcUgwaG9yUEs0MWlSclJ3SEtCVHRTS2N6V0QxTVBYMXd4OFdyMEZJb096SmQycjYxVGZkd2oiLCJtYWMiOiJiNzYyY2ZjN2VhYTVmY2Y5MDViNTY2ODE5YTM4MDdkMDc3MTZkMWY0ZWUxODRlNjcwN2QzZjI5OTQyZmVhOTY5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113330206\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-427578039 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-427578039\", {\"maxDepth\":0})</script>\n"}}