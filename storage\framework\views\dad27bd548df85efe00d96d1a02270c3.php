<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('تفاصيل معالجة النقد للفرع')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('الرئيسية')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('إدارة عمليات الفروع')); ?></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('branch.cash.management')); ?>"><?php echo e(__('معالجة النقد للفروع')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('تفاصيل')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('css-page'); ?>
    <style>
        /* General Styles */
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            box-shadow: 0 15px 30px rgba(0,0,0,0.12);
        }

        /* Card Headers */
        .card-header {
            background: linear-gradient(135deg, #6259ca, #8567f7);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 15px 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .card-header i {
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .card-body {
            padding: 25px;
        }

        /* Info Cards */
        .info-card {
            border-radius: 15px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        /* Status Badges */
        .status-badge {
            padding: 8px 15px;
            border-radius: 50px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            text-align: center;
            min-width: 100px;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
        }

        .status-open {
            background: linear-gradient(135deg, #28a745, #5fd778);
            color: white;
        }

        .status-closed {
            background: linear-gradient(135deg, #dc3545, #ff6b7d);
            color: white;
        }

        /* Tables */
        .table {
            margin-bottom: 0;
        }

        .table th {
            background-color: #f8f9fa;
            color: #4f4f4f;
            font-weight: 600;
            padding: 15px;
            border-top: none;
            border-bottom: 2px solid #e1e5ef;
        }

        .table td {
            padding: 15px;
            vertical-align: middle;
            border-top: 1px solid #f0f2f5;
        }

        .financial-data-table th {
            width: 40%;
        }

        /* Editable Cells */
        .editable-cell {
            cursor: pointer;
            position: relative;
            padding: 15px;
            border: 1px dashed transparent;
            transition: all 0.3s;
            border-radius: 8px;
        }

        .editable-cell:hover {
            background-color: #f8f9fa;
            border-color: #6259ca;
        }

        .editable-cell:hover::after {
            content: "\f044"; /* FontAwesome edit icon */
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            position: absolute;
            right: 10px;
            color: #6259ca;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }

        /* Modal Styles */
        .modal-content {
            border: none;
            border-radius: 15px;
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .modal-header {
            background: linear-gradient(135deg, #6259ca, #8567f7);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border-bottom: none;
        }

        .modal-title {
            font-weight: 600;
        }

        .modal-footer {
            border-top: 1px solid #f0f2f5;
        }

        /* Form Controls */
        .form-control {
            border-radius: 10px;
            border: 1px solid #e1e5ef;
            padding: 12px 15px;
            transition: all 0.3s;
            box-shadow: none;
        }

        .form-control:focus {
            border-color: #6259ca;
            box-shadow: 0 0 0 0.2rem rgba(98, 89, 202, 0.25);
        }

        /* Buttons */
        .btn {
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s;
        }

        .btn-primary {
            background: linear-gradient(135deg, #6259ca, #8567f7);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5349b5, #7456e6);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(98, 89, 202, 0.4);
        }

        .btn-secondary {
            background: #f0f2f5;
            color: #4f4f4f;
            border: none;
        }

        .btn-secondary:hover {
            background: #e1e5ef;
            color: #333;
            transform: translateY(-2px);
        }

        /* Summary Cards */
        .summary-card {
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s;
        }

        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.12);
        }

        .summary-card .icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #6259ca;
            opacity: 0.8;
        }

        .summary-card .title {
            font-size: 0.9rem;
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .summary-card .value {
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
        }

        /* Back Button */
        .back-button {
            display: inline-flex;
            align-items: center;
            margin-bottom: 20px;
            color: #6259ca;
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
        }

        .back-button:hover {
            transform: translateX(-5px);
            color: #5349b5;
        }

        .back-button i {
            margin-right: 5px;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-md-12 mb-4">
            <a href="<?php echo e(route('branch.cash.management')); ?>" class="back-button">
                <i class="fas fa-arrow-left"></i> <?php echo e(__('العودة إلى قائمة الورديات')); ?>

            </a>
        </div>

        <div class="col-md-12">
            <div class="card info-card mb-4">
                <div class="card-header">
                    <i class="fas fa-info-circle"></i>
                    <h5><?php echo e(__('معلومات الوردية')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="summary-card bg-light">
                                <div class="icon text-primary">
                                    <i class="fas fa-hashtag"></i>
                                </div>
                                <div class="title"><?php echo e(__('معرف الوردية')); ?></div>
                                <div class="value"><?php echo e($shift->id); ?></div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="summary-card bg-light">
                                <div class="icon text-primary">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div class="title"><?php echo e(__('الفرع')); ?></div>
                                <div class="value"><?php echo e($shift->warehouse ? $shift->warehouse->name : '-'); ?></div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="summary-card bg-light">
                                <div class="icon text-<?php echo e($shift->is_closed ? 'danger' : 'success'); ?>">
                                    <i class="fas <?php echo e($shift->is_closed ? 'fa-lock' : 'fa-unlock'); ?>"></i>
                                </div>
                                <div class="title"><?php echo e(__('الحالة')); ?></div>
                                <div class="value">
                                    <span class="status-badge <?php echo e($shift->is_closed ? 'status-closed' : 'status-open'); ?>">
                                        <i class="fas <?php echo e($shift->is_closed ? 'fa-lock' : 'fa-unlock'); ?> me-1"></i>
                                        <?php echo e($shift->is_closed ? __('مغلق') : __('مفتوح')); ?>

                                    </span>
                                </div>
                                <div class="mt-3 text-center">
                                    <?php if(!$shift->is_closed): ?>
                                        <button type="button" class="btn btn-danger btn-sm" id="closeShiftBtn" data-id="<?php echo e($shift->id); ?>">
                                            <i class="fas fa-lock me-1"></i> <?php echo e(__('إقفال الجلسة')); ?>

                                        </button>
                                    <?php else: ?>
                                        <button type="button" class="btn btn-success btn-sm" id="reopenShiftBtn" data-id="<?php echo e($shift->id); ?>">
                                            <i class="fas fa-unlock me-1"></i> <?php echo e(__('إعادة فتح الجلسة')); ?>

                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="summary-card bg-light">
                                <div class="icon text-primary">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="title"><?php echo e(__('تم إنشاؤه بواسطة')); ?></div>
                                <div class="value"><?php echo e($shift->creator ? $shift->creator->name : '-'); ?></div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="summary-card bg-light">
                                <div class="icon text-primary">
                                    <i class="fas fa-calendar-plus"></i>
                                </div>
                                <div class="title"><?php echo e(__('تم إنشاؤه في')); ?></div>
                                <div class="value"><?php echo e(\App\Models\Utility::getDateFormated($shift->created_at)); ?></div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="summary-card bg-light">
                                <div class="icon text-<?php echo e($shift->closed_at ? 'danger' : 'secondary'); ?>">
                                    <i class="fas fa-calendar-times"></i>
                                </div>
                                <div class="title"><?php echo e(__('تم إغلاقه في')); ?></div>
                                <div class="value"><?php echo e($shift->closed_at ? \App\Models\Utility::getDateFormated($shift->closed_at) : '-'); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php if($shift->financialRecord): ?>
        <div class="col-md-12 mb-4">
            <div class="card info-card">
                <div class="card-header">
                    <i class="fas fa-money-bill-wave"></i>
                    <h5><?php echo e(__('ملخص السجل المالي')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="summary-card bg-primary text-white">
                                <div class="icon text-white">
                                    <i class="fas fa-wallet"></i>
                                </div>
                                <div class="title text-white-50"><?php echo e(__('الرصيد الافتتاحي')); ?></div>
                                <div class="value"><?php echo e(number_format($shift->financialRecord->opening_balance, 2)); ?></div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="summary-card bg-success text-white">
                                <div class="icon text-white">
                                    <i class="fas fa-money-bill-alt"></i>
                                </div>
                                <div class="title text-white-50"><?php echo e(__('النقد الحالي')); ?></div>
                                <div class="value"><?php echo e(number_format($shift->financialRecord->current_cash, 2)); ?></div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="summary-card bg-info text-white">
                                <div class="icon text-white">
                                    <i class="fas fa-network-wired"></i>
                                </div>
                                <div class="title text-white-50"><?php echo e(__('النقد عبر الشبكة')); ?></div>
                                <div class="value"><?php echo e(number_format($shift->financialRecord->overnetwork_cash, 2)); ?></div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="summary-card bg-warning text-white">
                                <div class="icon text-white">
                                    <i class="fas fa-truck"></i>
                                </div>
                                <div class="title text-white-50"><?php echo e(__('نقد التوصيل')); ?></div>
                                <div class="value"><?php echo e(number_format($shift->financialRecord->delivery_cash, 2)); ?></div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3 mb-4">
                            <div class="summary-card bg-dark text-white">
                                <div class="icon text-white">
                                    <i class="fas fa-coins"></i>
                                </div>
                                <div class="title text-white-50"><?php echo e(__('إجمالي النقد')); ?> <small class="d-block">(<?php echo e(__('النقد الحالي')); ?> + <?php echo e(__('النقد عبر الشبكة')); ?>)</small></div>
                                <div class="value"><?php echo e(number_format($shift->financialRecord->total_cash, 2)); ?></div>
                            </div>
                        </div>

                        <div class="col-md-3 mb-4">
                            <div class="summary-card bg-danger text-white">
                                <div class="icon text-white">
                                    <i class="fas fa-minus-circle"></i>
                                </div>
                                <div class="title text-white-50"><?php echo e(__('العجز')); ?></div>
                                <div class="value"><?php echo e(number_format($shift->financialRecord->deficit, 2)); ?></div>
                            </div>
                        </div>

                        <div class="col-md-3 mb-4">
                            <div class="summary-card bg-secondary text-white">
                                <div class="icon text-white">
                                    <i class="fas fa-hand-holding-usd"></i>
                                </div>
                                <div class="title text-white-50"><?php echo e(__('العهدة المستلمة')); ?></div>
                                <div class="value"><?php echo e(number_format($shift->financialRecord->received_advance, 2)); ?></div>
                            </div>
                        </div>

                        <div class="col-md-3 mb-4">
                            <div class="summary-card bg-purple text-white" style="background: linear-gradient(135deg, #6259ca, #8567f7);">
                                <div class="icon text-white">
                                    <i class="fas fa-calculator"></i>
                                </div>
                                <div class="title text-white-50"><?php echo e(__('مجموع النقد')); ?></div>
                                <div class="value"><?php echo e(number_format($shift->financialRecord->current_cash + $shift->financialRecord->overnetwork_cash + $shift->financialRecord->delivery_cash, 2)); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-12">
            <div class="card info-card">
                <div class="card-header">
                    <i class="fas fa-edit"></i>
                    <h5><?php echo e(__('تفاصيل السجل المالي')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo e(__('انقر على أي قيمة لتعديلها')); ?>

                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered financial-data-table">
                            <tr>
                                <th><?php echo e(__('الرصيد الافتتاحي')); ?></th>
                                <td class="editable-cell" data-field="opening_balance" data-id="<?php echo e($shift->financialRecord->id); ?>">
                                    <?php echo e(number_format($shift->financialRecord->opening_balance, 2)); ?>

                                </td>
                            </tr>
                            <tr>
                                <th><?php echo e(__('النقد الحالي')); ?></th>
                                <td class="editable-cell" data-field="current_cash" data-id="<?php echo e($shift->financialRecord->id); ?>">
                                    <?php echo e(number_format($shift->financialRecord->current_cash, 2)); ?>

                                </td>
                            </tr>
                            <tr>
                                <th><?php echo e(__('النقد عبر الشبكة')); ?></th>
                                <td class="editable-cell" data-field="overnetwork_cash" data-id="<?php echo e($shift->financialRecord->id); ?>">
                                    <?php echo e(number_format($shift->financialRecord->overnetwork_cash, 2)); ?>

                                </td>
                            </tr>
                            <tr>
                                <th><?php echo e(__('نقد التوصيل')); ?></th>
                                <td class="editable-cell" data-field="delivery_cash" data-id="<?php echo e($shift->financialRecord->id); ?>">
                                    <?php echo e(number_format($shift->financialRecord->delivery_cash, 2)); ?>

                                </td>
                            </tr>
                            <tr>
                                <th><?php echo e(__('إجمالي النقد')); ?> <small class="text-muted d-block">(<?php echo e(__('النقد الحالي')); ?> + <?php echo e(__('النقد عبر الشبكة')); ?>)</small></th>
                                <td><?php echo e(number_format($shift->financialRecord->total_cash, 2)); ?></td>
                            </tr>
                            <tr>
                                <th><?php echo e(__('العجز')); ?></th>
                                <td class="editable-cell" data-field="deficit" data-id="<?php echo e($shift->financialRecord->id); ?>">
                                    <?php echo e(number_format($shift->financialRecord->deficit, 2)); ?>

                                </td>
                            </tr>
                            <tr>
                                <th><?php echo e(__('العهدة المستلمة')); ?></th>
                                <td class="editable-cell" data-field="received_advance" data-id="<?php echo e($shift->financialRecord->id); ?>">
                                    <?php echo e(number_format($shift->financialRecord->received_advance, 2)); ?>

                                </td>
                            </tr>
                            <tr>
                                <th><?php echo e(__('آخر تحديث بواسطة')); ?></th>
                                <td><?php echo e($shift->financialRecord->updater ? $shift->financialRecord->updater->name : '-'); ?></td>
                            </tr>
                            <tr>
                                <th><?php echo e(__('آخر تحديث في')); ?></th>
                                <td><?php echo e(\App\Models\Utility::getDateFormated($shift->financialRecord->updated_at)); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="col-md-12">
            <div class="card info-card">
                <div class="card-header">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h5><?php echo e(__('السجل المالي')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo e(__('لا يوجد سجل مالي لهذه الوردية')); ?>

                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Edit Modal -->
    <div class="modal fade" id="editModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel">
                        <i class="fas fa-edit me-2"></i>
                        <?php echo e(__('تعديل القيمة')); ?>

                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label for="editValue" id="fieldLabel" class="form-label"><?php echo e(__('القيمة')); ?></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-money-bill-alt"></i></span>
                            <input type="number" step="0.01" min="0" class="form-control" id="editValue" placeholder="0.00">
                        </div>
                        <input type="hidden" id="editField">
                        <input type="hidden" id="editId">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        <?php echo e(__('إلغاء')); ?>

                    </button>
                    <button type="button" class="btn btn-primary" id="saveChanges">
                        <i class="fas fa-save me-1"></i>
                        <?php echo e(__('حفظ التغييرات')); ?>

                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Close Shift Modal -->
    <div class="modal fade" id="closeShiftModal" tabindex="-1" role="dialog" aria-labelledby="closeShiftModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="closeShiftModalLabel">
                        <i class="fas fa-lock me-2"></i>
                        <?php echo e(__('تأكيد إقفال الجلسة')); ?>

                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo e(__('هل أنت متأكد من رغبتك في إقفال هذه الجلسة؟')); ?>

                    </div>
                    <p><?php echo e(__('سيتم إقفال الجلسة للمستخدم وسيحتاج إلى فتح جلسة جديدة للاستمرار في العمل.')); ?></p>
                    <input type="hidden" id="closeShiftId">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i><?php echo e(__('إلغاء')); ?>

                    </button>
                    <button type="button" class="btn btn-danger" id="confirmCloseShift">
                        <i class="fas fa-lock me-1"></i><?php echo e(__('تأكيد الإقفال')); ?>

                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Reopen Shift Modal -->
    <div class="modal fade" id="reopenShiftModal" tabindex="-1" role="dialog" aria-labelledby="reopenShiftModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="reopenShiftModalLabel">
                        <i class="fas fa-unlock me-2"></i>
                        <?php echo e(__('تأكيد إعادة فتح الجلسة')); ?>

                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo e(__('هل أنت متأكد من رغبتك في إعادة فتح هذه الجلسة؟')); ?>

                    </div>
                    <p><?php echo e(__('سيتم إعادة فتح الجلسة للمستخدم وسيتمكن من الاستمرار في العمل على نفس الجلسة.')); ?></p>
                    <input type="hidden" id="reopenShiftId">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i><?php echo e(__('إلغاء')); ?>

                    </button>
                    <button type="button" class="btn btn-success" id="confirmReopenShift">
                        <i class="fas fa-unlock me-1"></i><?php echo e(__('تأكيد إعادة الفتح')); ?>

                    </button>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
<script>
    $(document).ready(function() {
        // Format number function
        function formatNumber(number) {
            return new Intl.NumberFormat('ar-SA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(number);
        }

        // Parse formatted number
        function parseFormattedNumber(formattedNumber) {
            return parseFloat(formattedNumber.replace(/[^\d.-]/g, ''));
        }

        // Make cells editable
        $('.editable-cell').on('click', function() {
            var field = $(this).data('field');
            var id = $(this).data('id');
            var formattedValue = $(this).text().trim();
            var value = parseFormattedNumber(formattedValue);

            $('#editField').val(field);
            $('#editId').val(id);
            $('#editValue').val(value);

            // Set field label
            var fieldLabel = '';
            switch(field) {
                case 'opening_balance':
                    fieldLabel = '<?php echo e(__("الرصيد الافتتاحي")); ?>';
                    break;
                case 'current_cash':
                    fieldLabel = '<?php echo e(__("النقد الحالي")); ?>';
                    break;
                case 'overnetwork_cash':
                    fieldLabel = '<?php echo e(__("النقد عبر الشبكة")); ?>';
                    break;
                case 'delivery_cash':
                    fieldLabel = '<?php echo e(__("نقد التوصيل")); ?>';
                    break;
                case 'deficit':
                    fieldLabel = '<?php echo e(__("العجز")); ?>';
                    break;
                case 'received_advance':
                    fieldLabel = '<?php echo e(__("العهدة المستلمة")); ?>';
                    break;
                default:
                    fieldLabel = '<?php echo e(__("القيمة")); ?>';
            }

            $('#fieldLabel').text(fieldLabel);
            $('#editModal').modal('show');
        });

        // Save changes
        $('#saveChanges').on('click', function() {
            var field = $('#editField').val();
            var id = $('#editId').val();
            var value = $('#editValue').val();

            // Disable button and show loading state
            var $saveButton = $(this);
            var originalText = $saveButton.html();
            $saveButton.html('<i class="fas fa-spinner fa-spin me-1"></i> <?php echo e(__("جاري الحفظ...")); ?>');
            $saveButton.prop('disabled', true);

            $.ajax({
                url: '<?php echo e(route("branch.cash.management.update", ":id")); ?>'.replace(':id', id),
                type: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>',
                    field: field,
                    value: value
                },
                success: function(response) {
                    if(response.success) {
                        // Update the cell value with formatted number
                        $('[data-field="' + field + '"][data-id="' + id + '"]').text(formatNumber(response.data.value));

                        // Update total cash if needed (only when current_cash or overnetwork_cash changes)
                        if(field === 'current_cash' || field === 'overnetwork_cash') {
                            // Update the total cash in the table
                            $('th:contains("<?php echo e(__("إجمالي النقد")); ?>")').next('td').text(formatNumber(response.data.total_cash));

                            // Update the total cash in the summary card
                            $('.title:contains("<?php echo e(__("إجمالي النقد")); ?>")').next('.value').text(formatNumber(response.data.total_cash));

                            // Add a highlight effect to the total cash cells
                            var $totalCells = $('th:contains("<?php echo e(__("إجمالي النقد")); ?>")').next('td');
                            $totalCells.css('background-color', '#d4edda');
                            setTimeout(function() {
                                $totalCells.css('transition', 'background-color 1s ease');
                                $totalCells.css('background-color', '');
                            }, 100);
                        }

                        // Update the value in the summary card
                        $('.title:contains("' + fieldLabel + '")').next('.value').text(formatNumber(response.data.value));

                        // Show success message with animation
                        toastr.options = {
                            closeButton: true,
                            progressBar: true,
                            positionClass: "toast-top-right",
                            timeOut: 3000
                        };
                        toastr.success(response.message);

                        // Close modal
                        $('#editModal').modal('hide');

                        // Add highlight effect to the updated cell
                        var $cell = $('[data-field="' + field + '"][data-id="' + id + '"]');
                        $cell.css('background-color', '#d4edda');
                        setTimeout(function() {
                            $cell.css('transition', 'background-color 1s ease');
                            $cell.css('background-color', '');
                        }, 100);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function(xhr) {
                    var errorMessage = xhr.responseJSON && xhr.responseJSON.message
                        ? xhr.responseJSON.message
                        : '<?php echo e(__("حدث خطأ أثناء تحديث القيمة")); ?>';

                    toastr.options = {
                        closeButton: true,
                        progressBar: true,
                        positionClass: "toast-top-right",
                        timeOut: 5000
                    };
                    toastr.error(errorMessage);
                },
                complete: function() {
                    // Restore button state
                    $saveButton.html(originalText);
                    $saveButton.prop('disabled', false);
                }
            });
        });

        // Handle Enter key in the edit modal
        $('#editValue').on('keypress', function(e) {
            if(e.which === 13) { // Enter key
                e.preventDefault();
                $('#saveChanges').click();
            }
        });

        // Focus on input when modal is shown
        $('#editModal').on('shown.bs.modal', function() {
            $('#editValue').focus().select();
        });

        // Close Shift Button Click
        $('#closeShiftBtn').on('click', function() {
            var shiftId = $(this).data('id');
            $('#closeShiftId').val(shiftId);
            $('#closeShiftModal').modal('show');
        });

        // Reopen Shift Button Click
        $('#reopenShiftBtn').on('click', function() {
            var shiftId = $(this).data('id');
            $('#reopenShiftId').val(shiftId);
            $('#reopenShiftModal').modal('show');
        });

        // Confirm Close Shift
        $('#confirmCloseShift').on('click', function() {
            var shiftId = $('#closeShiftId').val();

            // Disable button and show loading state
            var $button = $(this);
            var originalText = $button.html();
            $button.html('<i class="fas fa-spinner fa-spin me-1"></i> <?php echo e(__("جاري الإقفال...")); ?>');
            $button.prop('disabled', true);

            $.ajax({
                url: '<?php echo e(route("branch.cash.management.close.shift", ":id")); ?>'.replace(':id', shiftId),
                type: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>'
                },
                success: function(response) {
                    if(response.success) {
                        // Show success message
                        toastr.options = {
                            closeButton: true,
                            progressBar: true,
                            positionClass: "toast-top-right",
                            timeOut: 3000
                        };
                        toastr.success(response.message);

                        // Close modal
                        $('#closeShiftModal').modal('hide');

                        // Update UI to reflect closed status
                        $('.status-badge').removeClass('status-open').addClass('status-closed');
                        $('.status-badge i').removeClass('fa-unlock').addClass('fa-lock');
                        $('.status-badge').html('<i class="fas fa-lock me-1"></i> <?php echo e(__("مغلق")); ?>');

                        // Update icon in summary card
                        $('.summary-card .icon.text-success').removeClass('text-success').addClass('text-danger');
                        $('.summary-card .icon i.fa-unlock').removeClass('fa-unlock').addClass('fa-lock');

                        // Update closed date
                        $('.title:contains("<?php echo e(__("تم إغلاقه في")); ?>")').next('.value').text(response.data.closed_at);

                        // Replace close button with reopen button
                        $('#closeShiftBtn').replaceWith('<button type="button" class="btn btn-success btn-sm" id="reopenShiftBtn" data-id="' + shiftId + '"><i class="fas fa-unlock me-1"></i> <?php echo e(__("إعادة فتح الجلسة")); ?></button>');

                        // Bind event to new button
                        $('#reopenShiftBtn').on('click', function() {
                            var shiftId = $(this).data('id');
                            $('#reopenShiftId').val(shiftId);
                            $('#reopenShiftModal').modal('show');
                        });
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function(xhr) {
                    var errorMessage = xhr.responseJSON && xhr.responseJSON.message
                        ? xhr.responseJSON.message
                        : '<?php echo e(__("حدث خطأ أثناء إقفال الجلسة")); ?>';

                    toastr.options = {
                        closeButton: true,
                        progressBar: true,
                        positionClass: "toast-top-right",
                        timeOut: 5000
                    };
                    toastr.error(errorMessage);
                },
                complete: function() {
                    // Restore button state
                    $button.html(originalText);
                    $button.prop('disabled', false);
                }
            });
        });

        // Confirm Reopen Shift
        $('#confirmReopenShift').on('click', function() {
            var shiftId = $('#reopenShiftId').val();

            // Disable button and show loading state
            var $button = $(this);
            var originalText = $button.html();
            $button.html('<i class="fas fa-spinner fa-spin me-1"></i> <?php echo e(__("جاري إعادة الفتح...")); ?>');
            $button.prop('disabled', true);

            $.ajax({
                url: '<?php echo e(route("branch.cash.management.reopen.shift", ":id")); ?>'.replace(':id', shiftId),
                type: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>'
                },
                success: function(response) {
                    if(response.success) {
                        // Show success message
                        toastr.options = {
                            closeButton: true,
                            progressBar: true,
                            positionClass: "toast-top-right",
                            timeOut: 3000
                        };
                        toastr.success(response.message);

                        // Close modal
                        $('#reopenShiftModal').modal('hide');

                        // Update UI to reflect open status
                        $('.status-badge').removeClass('status-closed').addClass('status-open');
                        $('.status-badge i').removeClass('fa-lock').addClass('fa-unlock');
                        $('.status-badge').html('<i class="fas fa-unlock me-1"></i> <?php echo e(__("مفتوح")); ?>');

                        // Update icon in summary card
                        $('.summary-card .icon.text-danger').removeClass('text-danger').addClass('text-success');
                        $('.summary-card .icon i.fa-lock').removeClass('fa-lock').addClass('fa-unlock');

                        // Clear closed date
                        $('.title:contains("<?php echo e(__("تم إغلاقه في")); ?>")').next('.value').text('-');

                        // Replace reopen button with close button
                        $('#reopenShiftBtn').replaceWith('<button type="button" class="btn btn-danger btn-sm" id="closeShiftBtn" data-id="' + shiftId + '"><i class="fas fa-lock me-1"></i> <?php echo e(__("إقفال الجلسة")); ?></button>');

                        // Bind event to new button
                        $('#closeShiftBtn').on('click', function() {
                            var shiftId = $(this).data('id');
                            $('#closeShiftId').val(shiftId);
                            $('#closeShiftModal').modal('show');
                        });
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function(xhr) {
                    var errorMessage = xhr.responseJSON && xhr.responseJSON.message
                        ? xhr.responseJSON.message
                        : '<?php echo e(__("حدث خطأ أثناء إعادة فتح الجلسة")); ?>';

                    toastr.options = {
                        closeButton: true,
                        progressBar: true,
                        positionClass: "toast-top-right",
                        timeOut: 5000
                    };
                    toastr.error(errorMessage);
                },
                complete: function() {
                    // Restore button state
                    $button.html(originalText);
                    $button.prop('disabled', false);
                }
            });
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\erpq22\resources\views/company_operations/branch_cash_management/show.blade.php ENDPATH**/ ?>