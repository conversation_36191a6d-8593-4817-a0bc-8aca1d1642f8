<?php
// This file was auto-generated from sdk-root/src/data/chatbot/2017-10-11/paginators-1.json
return [ 'pagination' => [ 'DescribeChimeWebhookConfigurations' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'WebhookConfigurations', ], 'DescribeSlackChannelConfigurations' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'SlackChannelConfigurations', ], 'DescribeSlackUserIdentities' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'SlackUserIdentities', ], 'DescribeSlackWorkspaces' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'SlackWorkspaces', ], 'ListMicrosoftTeamsChannelConfigurations' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'TeamChannelConfigurations', ], 'ListMicrosoftTeamsConfiguredTeams' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'ConfiguredTeams', ], 'ListMicrosoftTeamsUserIdentities' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'TeamsUserIdentities', ], ],];
