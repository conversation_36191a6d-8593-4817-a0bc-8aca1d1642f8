{"__meta": {"id": "X2a1dcaad2a512f9bf22dac61dfcb25b4", "datetime": "2025-06-30 22:40:02", "utime": **********.38549, "method": "POST", "uri": "/branch-cash-management/close-shift/25", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323201.848795, "end": **********.385505, "duration": 0.5367100238800049, "duration_str": "537ms", "measures": [{"label": "Booting", "start": 1751323201.848795, "relative_start": 0, "end": **********.238479, "relative_end": **********.238479, "duration": 0.38968396186828613, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.238489, "relative_start": 0.3896939754486084, "end": **********.385506, "relative_end": 9.5367431640625e-07, "duration": 0.1470170021057129, "duration_str": "147ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45677752, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST branch-cash-management/close-shift/{id}", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\BranchCashManagementController@closeShift", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "branch.cash.management.close.shift", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=151\" onclick=\"\">app/Http/Controllers/BranchCashManagementController.php:151-193</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02096, "accumulated_duration_str": "20.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2752938, "duration": 0.01815, "duration_str": "18.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 86.594}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.306467, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 86.594, "width_percent": 2.719}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 154}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.311691, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:154", "source": "app/Http/Controllers/BranchCashManagementController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=154", "ajax": false, "filename": "BranchCashManagementController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 89.313, "width_percent": 0}, {"sql": "select * from `shifts` where `shifts`.`id` = '25' and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["25"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 157}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.31285, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:157", "source": "app/Http/Controllers/BranchCashManagementController.php:157", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=157", "ajax": false, "filename": "BranchCashManagementController.php", "line": "157"}, "connection": "kdmkjkqknb", "start_percent": 89.313, "width_percent": 3.626}, {"sql": "update `shifts` set `is_closed` = 1, `closed_at` = '2025-06-30 22:40:02', `closed_by` = 15, `shifts`.`updated_at` = '2025-06-30 22:40:02' where `id` = 25", "type": "query", "params": [], "bindings": ["1", "2025-06-30 22:40:02", "15", "2025-06-30 22:40:02", "25"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 168}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.315233, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:168", "source": "app/Http/Controllers/BranchCashManagementController.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=168", "ajax": false, "filename": "BranchCashManagementController.php", "line": "168"}, "connection": "kdmkjkqknb", "start_percent": 92.939, "width_percent": 2.719}, {"sql": "select * from `users` where `users`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.317425, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:171", "source": "app/Http/Controllers/BranchCashManagementController.php:171", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=171", "ajax": false, "filename": "BranchCashManagementController.php", "line": "171"}, "connection": "kdmkjkqknb", "start_percent": 95.658, "width_percent": 2.147}, {"sql": "update `users` set `is_sale_session_new` = 1, `users`.`updated_at` = '2025-06-30 22:40:02' where `id` = 22", "type": "query", "params": [], "bindings": ["1", "2025-06-30 22:40:02", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 174}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.319355, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:174", "source": "app/Http/Controllers/BranchCashManagementController.php:174", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=174", "ajax": false, "filename": "BranchCashManagementController.php", "line": "174"}, "connection": "kdmkjkqknb", "start_percent": 97.805, "width_percent": 2.195}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 177}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.378662, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:177", "source": "app/Http/Controllers/BranchCashManagementController.php:177", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=177", "ajax": false, "filename": "BranchCashManagementController.php", "line": "177"}, "connection": "kdmkjkqknb", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management/25\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/branch-cash-management/close-shift/25", "status_code": "<pre class=sf-dump id=sf-dump-87405019 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-87405019\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-766779815 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-766779815\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2075733167 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2075733167\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1037155473 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751323200249%7C15%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlI1VGI4TWJrTGJtbnI5SGJVanYycWc9PSIsInZhbHVlIjoicFp5VlZMNnRUdENPSWZVU3NFM0xiVmxGVnBwdFRjMG12Wk5EbTlZYnljd3c4R01HYjluT3FsOXczWDV2VG1aaFFXYURhWHVWQ29qZ2lBajZqMFdzcVU5OERuR2w4VlQrNzNINDVyVG5QejMrTjBWR1gzem85NWpkWkRXc2dCdHJ4RCtJV2d4VkRhMTQvTHpHRU1XV25OMG1uTjJzRnBsSmQ3ZnEraVhVNHdVdGc1aW9GZDE2d0t1Ukh2RjZSRFFaZU81Q2sxcFZjU3lJRkw3UDZDWi8wTCtDTndLdjFnZlFmMGV0M0pJUFV5b3UxeW9CMyt5VzZPN2dJTWZIVG43ZjlIWXhTQVUzdlAwQUVtT2hsZ0taNXJzOEYwYWxQekQ2Z095OHFYQ1IxTzFvdWk2eGpwQXdhd3JsR0ZibnhrTzQ5YVpQQXdHek9Ca0VuODNjbEFoTkV1ZTlmTnBCYnFuYnJIdnVaNkU5UXRvN1pRTEtpZ3F1QjJDVlE4V3VJYnQ0SlNlUDZrZ1F3MytEeXFJRmVVNjFQUlJwSmI4Nzd6dER5RFUzVk1XT1JBSUNmUXJralBkd1VGRjZucHBDYnEyRno5WkU2SHBRWU9VNHB3N1ovVUp2Ym00aDlQbVphcVJvL0pUQlQ2UWxSMlJKOHdWNThiYnhQWlVIQTloUm1mKzMiLCJtYWMiOiI0OWYyMDI3Njk3YWJiNDIwOGFlMDliOGI1ZjcxMDBiZTZmYWFjZGI4ZjhhNWI3ZTIwNGQ4ODM0MTU4NmQ5NDc2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjV3THhYNHhyYUNLcWhEUzVNSHVQSnc9PSIsInZhbHVlIjoic21IWG9kazhwUWtLSDFBTWdnKzNHWE9NdmRkZVBKOUc2K3UvVzlzR0c2Z0xOcWJNYVc0TCtVeEpZKzdLcTNOUFFFL2UrMmw1MTB3bnQvbmFBU1oxMVNaMmpqcEFsYytZbHF0dktSeEs0MGovRnBUUWZrU1crWXp0N2M1N1ZGcjJ5bHNQWjBnYlRETTVwRUY5RW5ucUkzMUh4Tkhia0gzMlgzc2tzOEZZS1JSclkzWTZ2bkdoSk9MaHk1QkF1ZmpxU0Z1RWR6cjRsVlovdGo3Y21IeUU3bEYyTDZwcFZkTWtwRjJYdGN3NFlheFZaY3laTWo0VXVsdHVvcmRWelZ3VlFOTUtTTWphMlo0c3VEdlo0cXZmblJzRUdLL1pJV3JlMWlNT3o3WGZyTnlncFNtOGY1TWQ0RGJoVHBUbGxiWUUyOHVabmlPM3dnTmxyLy9qQ2R6bGxaTHlma2JDRC9zTGNldFV1clFNenNoRmtrYkphdDkrUFJFTmltdjRTdzkzZXd1ZWFsVHJGZnZ4ajFncHJMamwrUFpvVEZFeVNuZ1hVKzZVNUNYVkJ4RXJpc1l0V1pVd1ZhZVVHT3ViWnR6L1B4em93NnRuZzVGN1o3aTlpcEdseHpTT2pGcjJBSDYraVBjdDRrdE1kVm1YNXNXa2RqaFBkclpzTloyV0wwZjciLCJtYWMiOiJlMzdhY2IwMDhmZjNjNjYyNWYzMjk4MjA2ODU1MTZhMTc1YmVmNzdlYjk4NzJiMmFkODg0ZDkyMTg5MmQ1MTAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1037155473\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-686360383 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0M1K0sdNadPD2LVrnt8LmBw227nA9F1U5e3ROaJK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-686360383\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1283691855 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:40:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldMd25rcGgrSFdCV2VibWN4bzlVT3c9PSIsInZhbHVlIjoiTXg3ejdlQmVCbFBrRmNsKzdxYm45S25qQk44SmRCckJyNU5yVUZ6ekxSTjJ0aXJnSGVMbEtuOVF5M0hFa2xON3U4TkhGcDdsV1dSWlgrOThpSzVLRm1RcTJFd1RxRUxBWTNFTHVFRkY1Uk45YjAwbURURFJaNS9OMTQyeEs4UkNCZnhsdXJaZG5iaU9GSFJnellQd1cwSHkydWZJb25TSjVHTnd5MjNkM3hCZmhJaEtjeGp4T2tZQjZuM0pEb08zYnlvaGNtVHVPMnU3c2RxMEJvUEtIdW1mY2tNMFM4am1MblRSdnFNMlcyOCtVU0c3dGplaTByQ0xjdkNkZkF0VW5aVVFDSUpiVm5sc2VSTHh1cTdCVTYzTXYrQWtCc3VqVkZqSkVvMlpUYzFCQ2pPdGlTQVhURzFBaGZaZzliK1ExRk1DbHpjV09zM1lDN3FXSjFUUFp0VWw0QkJzMzhST3o4N1NNcjJENUh4K0R0RWdtaExEdjBISkxyb1VmQXBSYWVERUVuOWdVNVA0R2U2VXYyN2Vub0Q4VVZoS0hiRFgvVjNQRDhVWldBeWl4WmVmRlJqR2pIQW1nalpwczFPUlZmT3E0U1M3SmVpT09MZ0syZTZpVHJtcElaNTBCNEIyc1dCMnYvNkJNcG9HYWlvSUJOWk45bmVuZjNHb2pDWmkiLCJtYWMiOiIzOTM4MWZiZDhjZjJjZWYwYTE5MzE3OGYxNzdlYWE2NzBiMmRjMzczMDQzNGFkYTlhMTYyMWM1NWQ5NWFiN2Y0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik10OTVud0xkcHlUSzZBVmRaOWtLQUE9PSIsInZhbHVlIjoiMEtTK2NuZEYxaXJNYmZwaUg3aXZTK3R0ZzRidHQwYmNQVkcwMGZuaHhuaG5qTXRIQkVpbVl3L1hTWjBGTm5zcFRGQUhZVExSQk1kWkVHOFA4KzYzU0orU3NhUVNrQ0N2ZGZGZGVLcHJyUG1jcWNlT0t1d0tTMFFnQ1piNXdndmVpRWFQdWlHU2o5Q0draWwybWZPdkF5cFU4L3FpNThrTW1DRlIzY0VwZVdBQ01NK2IwckZ2SUJRenVmUEVCQXhsbzdCWDU4b0R5RmhLbVlYQmwwUVlWUjBUdXNRNStLT0FyQlArZWt1RjJTdXJzazJTZklIVVg0WURpK0RhdmhVN0JGSjAyTWwxUy9lSXViSE8rMFI4c1ZBeFRIVlNwUXR6Z05sdS9IaGZLWlo2TTdvWFFGcUZmVUxJWkFWMXNDMWVDQUUrR21xVWVPVU9wZFlhS2g5Y2JZUzBSR21td1BDOUpETXpBVVg1UUlVZXAxU0dFLysxZU5Ebk54S0g5U3Ywd1lCcXlWSDdKcVcwOFFSZ21kMkNTTzVqMWNhUWtIdzg3V3NoZ01EMlVUUFdGTGxqMmFGZm1LR3FPVi9LeW5lYmZLUzRYUW9xeXNoWklqTHNIZHZmVVFWV3g4MHExeFptMGp4dlZFUWwrY0pxL2Q3NVRPSjZKa1krSml6RjZzZmUiLCJtYWMiOiI3ZTYyMDE1MWI0YjhmMmQ2MjZlNDg5NzY3NGRkNjRhNWVkOTlkZGIyNGY3NmJjNzk2OTU0ZTc1ZDhjY2Q4OWM2IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:40:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldMd25rcGgrSFdCV2VibWN4bzlVT3c9PSIsInZhbHVlIjoiTXg3ejdlQmVCbFBrRmNsKzdxYm45S25qQk44SmRCckJyNU5yVUZ6ekxSTjJ0aXJnSGVMbEtuOVF5M0hFa2xON3U4TkhGcDdsV1dSWlgrOThpSzVLRm1RcTJFd1RxRUxBWTNFTHVFRkY1Uk45YjAwbURURFJaNS9OMTQyeEs4UkNCZnhsdXJaZG5iaU9GSFJnellQd1cwSHkydWZJb25TSjVHTnd5MjNkM3hCZmhJaEtjeGp4T2tZQjZuM0pEb08zYnlvaGNtVHVPMnU3c2RxMEJvUEtIdW1mY2tNMFM4am1MblRSdnFNMlcyOCtVU0c3dGplaTByQ0xjdkNkZkF0VW5aVVFDSUpiVm5sc2VSTHh1cTdCVTYzTXYrQWtCc3VqVkZqSkVvMlpUYzFCQ2pPdGlTQVhURzFBaGZaZzliK1ExRk1DbHpjV09zM1lDN3FXSjFUUFp0VWw0QkJzMzhST3o4N1NNcjJENUh4K0R0RWdtaExEdjBISkxyb1VmQXBSYWVERUVuOWdVNVA0R2U2VXYyN2Vub0Q4VVZoS0hiRFgvVjNQRDhVWldBeWl4WmVmRlJqR2pIQW1nalpwczFPUlZmT3E0U1M3SmVpT09MZ0syZTZpVHJtcElaNTBCNEIyc1dCMnYvNkJNcG9HYWlvSUJOWk45bmVuZjNHb2pDWmkiLCJtYWMiOiIzOTM4MWZiZDhjZjJjZWYwYTE5MzE3OGYxNzdlYWE2NzBiMmRjMzczMDQzNGFkYTlhMTYyMWM1NWQ5NWFiN2Y0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik10OTVud0xkcHlUSzZBVmRaOWtLQUE9PSIsInZhbHVlIjoiMEtTK2NuZEYxaXJNYmZwaUg3aXZTK3R0ZzRidHQwYmNQVkcwMGZuaHhuaG5qTXRIQkVpbVl3L1hTWjBGTm5zcFRGQUhZVExSQk1kWkVHOFA4KzYzU0orU3NhUVNrQ0N2ZGZGZGVLcHJyUG1jcWNlT0t1d0tTMFFnQ1piNXdndmVpRWFQdWlHU2o5Q0draWwybWZPdkF5cFU4L3FpNThrTW1DRlIzY0VwZVdBQ01NK2IwckZ2SUJRenVmUEVCQXhsbzdCWDU4b0R5RmhLbVlYQmwwUVlWUjBUdXNRNStLT0FyQlArZWt1RjJTdXJzazJTZklIVVg0WURpK0RhdmhVN0JGSjAyTWwxUy9lSXViSE8rMFI4c1ZBeFRIVlNwUXR6Z05sdS9IaGZLWlo2TTdvWFFGcUZmVUxJWkFWMXNDMWVDQUUrR21xVWVPVU9wZFlhS2g5Y2JZUzBSR21td1BDOUpETXpBVVg1UUlVZXAxU0dFLysxZU5Ebk54S0g5U3Ywd1lCcXlWSDdKcVcwOFFSZ21kMkNTTzVqMWNhUWtIdzg3V3NoZ01EMlVUUFdGTGxqMmFGZm1LR3FPVi9LeW5lYmZLUzRYUW9xeXNoWklqTHNIZHZmVVFWV3g4MHExeFptMGp4dlZFUWwrY0pxL2Q3NVRPSjZKa1krSml6RjZzZmUiLCJtYWMiOiI3ZTYyMDE1MWI0YjhmMmQ2MjZlNDg5NzY3NGRkNjRhNWVkOTlkZGIyNGY3NmJjNzk2OTU0ZTc1ZDhjY2Q4OWM2IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:40:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1283691855\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-84266338 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-84266338\", {\"maxDepth\":0})</script>\n"}}