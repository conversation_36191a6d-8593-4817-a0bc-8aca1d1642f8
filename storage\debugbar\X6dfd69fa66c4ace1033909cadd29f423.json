{"__meta": {"id": "X6dfd69fa66c4ace1033909cadd29f423", "datetime": "2025-06-30 23:13:07", "utime": 1751325187.001265, "method": "GET", "uri": "/customer/check/warehouse?customer_id=11&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.586196, "end": 1751325187.001281, "duration": 0.4150850772857666, "duration_str": "415ms", "measures": [{"label": "Booting", "start": **********.586196, "relative_start": 0, "end": **********.939864, "relative_end": **********.939864, "duration": 0.3536679744720459, "duration_str": "354ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.939873, "relative_start": 0.35367703437805176, "end": 1751325187.001282, "relative_end": 9.5367431640625e-07, "duration": 0.06140899658203125, "duration_str": "61.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45141872, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.016810000000000002, "accumulated_duration_str": "16.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.966925, "duration": 0.016210000000000002, "duration_str": "16.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.431}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.991987, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.431, "width_percent": 1.904}, {"sql": "select * from `customers` where `customers`.`id` = '11' limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.99481, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 98.334, "width_percent": 1.666}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1860172903 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1860172903\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2006057169 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2006057169\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2351911 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2351911\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2009775775 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2818 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; laravel_session=eyJpdiI6ImpxTklOWXhTYU1XWEJkQTBqVTRXZlE9PSIsInZhbHVlIjoiTXFGZEpQRXlGa212dDlvcjMyZUgzOTJTdTZUUld1Y0RIbVM4elBEck42T2RTa0tjV2NoaTJNekRxM3Vkc3lrR1dOeGt5blA0Ym55U2pNZWY5am1QNC9lMm5SZE1VcGh6OVBneXMxN2Y4Qmx1TWhKckg0OXNOdzJBV1ZmTzVubEw3cmlyUDRwVnhxYzd4NmhqaEJiZThRcHlESndxYTY0WWNKZ1pqeVdFSTZuZnRmZ1BDZy91c3ZCbWloYytBQnpaczBrTmh2S3Z1dllBcHJYMkhWOXM3alFFWENqVkhpc2Rnb1lHSlpwM2dNb0s5cUxpakE5OVNEbEZRZ0VUVDJBWFZKWmZyYjZRM2ZNL1BuR0l5L3BsYXhVbzEwQ1ZlaXYySVUrQnk5a0dvY1BKK2dwQnEzM3ppZnFlaW9XTW9Ga1pSQ29mZktuT3BjSnc5ampzYU1SdTIxNzd4Wms4eUJ6eWdrajg1UUhYS2puSkhMNDVRbEh3WENJazQ2Qzk0Vlk5cE5NYW1qQ0xCbWJBVHZ5ZUlzUzBGRTJCWGliL1ovUHlOejhGSHJzRS9UOC8wK0QvTTQ4WHdveVBkTE1CdHlWcnFrQWdDcU1jR0hscFd0SUgzYWRtbFRaT0pzTGUzSkVDS2k4YlhiU0s3b29aMU5tbWtnL1NUaWZ1VmhxYnZ3ZFoiLCJtYWMiOiJjNTgzZDkxNDlmYmY2OTAwMmNmYTUwNTg0OWMwNDQ3MTVmZWQ0YWFiNjk4ZTJkZDM4YTkzNjhiMzE3YzIyNjlmIiwidGFnIjoiIn0%3D; _clsk=eiqg7d%7C1751325181289%7C15%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjIwREo2TFlFbFRNaHk4Y25XMXdEOFE9PSIsInZhbHVlIjoiNGlXYTFlYXIrUlkzSVlrbm5PMWNNN3B5ZkRSL1pKWkxLUjhDYi93VEJJOUNreURCVEs5MjFMNko5YXd4cGFJQ2dNbmFOVGY3QUMyRWRyMlJKcERLdSs0b1ZNNXZpMEw2SWJaeHV3clRZOC9HWDkxMlV6QjQ4dFpxMi9MR1poT09xY2tBSmxFRklvYURPcHdHcm45dFlTd1VReTF4QjlKSitIVk1iV2QwN0NpdU1TOTZUTlNLaVE5UnN3aXljanYxTERwWmF6VlhNbU4rTzFlRDZDc2RrZGlzV2FwWm9CL2NSNHJkaUREemVSR2FtdzF2RjdjSytFbkhpZ2RRa01PdW1vMFIzTk1TN3JIVkZtc2NrcVIvc3FmNjd1b1J4RkorSG14N2RWbEVxTXkwWnd0b3JxNVBhNURlY0JDeXkzQWVoT29zZnU4M0tDMFk4dUNxaXM0QWxmZnpNMjdBUndyOXdSTk1xRkV3a0pHWldiWUhVa3JKRHhEdkFBVlJZdzRtbXRtZG11aW5MbnlUY3AzVmtEYTJ1Sk9VbE03U2w3amw4NnJQQW5SMEdHdi9hQXNVT3ZRK2p3eVh5MGk3Z1B6MWhScEFQZjFrQnZlc2t2Yi9XTGd5NHgxa0huQ0xqYTBBOW5NTXV2OG1hUEpCVnV0SFpOdFRhTElPQnpxT3JxWlMiLCJtYWMiOiJlYmFhYWM1Y2MzMjgzNWUwNDNlNDc2MmUxOTViOTM0YWMwYjFjOGZkNWNiZjBkM2ZkNGIwODA2NjVlOTNiYzExIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjU2YXVJYm5jR1NXV1dIMWRqcmIzVlE9PSIsInZhbHVlIjoieUY4d0ZybFp0d3J6V1E1ci9JU0dMRzYzS1k4ekJQaStWSWhtSURpWjdEZ3NDRXNSSlVtSzM4VnBGdzNxZ1JsWjd3SSt1Q0RyNmVTK2wvNkdobTJmSFJhVnZLVWFQVU94SURZZjhzeVJQSnU5ZndRNU42WDJlelNXakpxRFJUWjZmdzRzdzlzL2dtakVSUnhpSDRSbmNJNll4dVFFOVhMVklGeks2c3podEQrb3FzY2hJaUgzQytocklwdWhSQmF6RmZqZFUzZTQ5RDBpQm5nVnhadGFRWmlwV00xQzRSRUZwdFVPZVFyc0xNbW0rSG1MS3FZMFhUMHUzYUtmU2lSZEZHcEYzaVVyQ1JJUW56UVA3bUtuR1VsRTNYeFh2a1ZKTkRic0ZWNFZJUWRpOUNWZ1NkdXZ5aDNTNlhiUFlEN0lETUV5dTN6d0lHYUN4ZEQ0MXpvNGZBanI1a2hQWmc2SUg1OEF0SG9CNU9vd2lLa2ZqMmlCdEJEczl0V2x0SzF3QngxT0s3ZDVSMzhSZDRwdVR3QjBFOUhaT0ptbUNnWU1MOUg5blF4aS81dlBhZWxudDVRWFRmZE1OZUw4b2JHc29YWUM5TnhRTjlJVk9RNVlJdnNCS0dRTUgxWnFMM1FjdGQ1Q043V0RmUVpmUWpHRHdKanpFaVdnWXZpMVRJOWYiLCJtYWMiOiJiNWM3MTc2MzMzY2U2MzA1NzYxYjEyYzE1NDkyMWU3MzYwMjgxNjM3NmI3ZjQ2ZjA5NDg4MjczNTJlMTU0ZTRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2009775775\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-268692321 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nMXQGGGSGQFIfGwlrnEYCl0LIB0DtCvcZqQRlbGj</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-268692321\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2091329070 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:13:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktldVpWdWQzcEVIbGFJS2dDN05GSVE9PSIsInZhbHVlIjoieERuZ1dVL0VqMTlETFBQdWpSanJZbTRQSnZBeUZXQUN0ZW0xVHk3UFFESWsrUENMMmRUNUpvTGtQSk9vOWhlRGN4SHM1RVg5cko2QkJWeEJmSksvTDRmblhqbm9aZFAwb0t6TW1yM1lCNlJISzBlYVoxbzFyTEZTNnRZbEhZZlV0RjRVUVF6eXZaUjA4bU5PV0gzUFA2b3M2SkExRFdaQlhuZTA4U1dEUmdJdXl2WUhTMHY1a1FLWjR5bFZUdXVOQXAzT1pMWDhnWVdFWlI3ZjNtZllPdUhxZ1kwbFFPN0p1RVdGV1BjUU1vTHRYMThrUjlsQ0poRXVNTEltb1oxZGtsK0xHdnBBZURncmZJUCtPY05IdGRIZVQzNU80ejZPZXFGK3Zpb0Z5SlRoQmhxK2RZMFhTRnN0d3JhTm5SYWJYc3UzdGxyN25Ia3VFNGJaT21ISGNGaDVNV1BqVU9xTmdtUTF4aWZxSFhWSWlGMWxkK2tEdDNDMkJkaEZCVHNWZFZKZkVpTGZMdE5ielNtOEdEK0VoOVBpeGF4L3g5THR0T24wbG5xcDhMRGloaGgzbUIvb1JLWjR0SGNmc0krTGRaK1RMWkN0WEVuQUxsR1kwakk5RkdGQ1k5QUZEa2t2Qm5CY2hlT0dyWEN3bmo2Ni9LOGxBSU9yM2VlbHYwVHIiLCJtYWMiOiI1NzNkNmJmNDZkOTIxNmFlNjE4OTgwODk2ZjhkMDBiNTA2ZmMzNWQ2MTE2ZmEzYTAyZDdiNzA0MDZiZThhNzVlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:06 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik5TSlI1Mk1TaG1wc25DbEFZRkJRQVE9PSIsInZhbHVlIjoia0RySTRJbWFYK25sUHd3OUYxeDJjeit0R1FHblRrTkFRUUE5TXR5UkUxZUJRM2ptUGk3OGZoZXJwS3lDSXBmaUx3blFPbnVGNFQ4cFpuZTZocngwQjFTUjJiSlA0eitHeC9CL2pHUFlweTZiQTFOMFhlblZXbXh5allVajhKVG5pL2FoTmJ1MFBoTUhhMTN1aCtDVkh1NWhqT2c2bE5iMy9JbXRscXlhYzkyZnlNWTBXSy9yRWxoS1FnWDAyZzFVeXJHNXZFUkxUSGx5UXdVbzhReFp6RGxtUmErb3FISjNENmMyMGhkTzVqaFM3RWlFOXNXYmVTWVVsOFJMQVNiME5KZkRpTXhmbWRKSkNpSTMzeElPMkFHajB5SXUwNFl3V2pmY2xzeHVJR0NSeVVHY1ZWbm02bU1OYTB3ODNzK3dvczZGWVZTWm9pRTFYOTlKdGlnYis4eW54empGWE5pSGpSb1VUTFo1VGh2bkJLS0t5STZoMkVOS0FldTNNODN2MldNYVpURU9WNFRWcFQ1cWZUeHNra3hTOHdMdDNKc0hCNXpoQ1NtYkJmd3plWEV2akwzc3I3MGliVWdpU1Q1UFNWVG1BN1M2T2c1Si85UUkzKzFPYVR6UDN2R3hvU3R3Qm9FSEI2K1lwV1BhdW5NUi9CK3AxWXlSaERJaE1nc0oiLCJtYWMiOiI1MThmYjBlM2YwMzkyYWYzYjljN2E5MDFmNjQ4ZDZhZDY1MDVjNTA5Y2UzZGI1NTZjMGU3YWI2N2IyOWYyYjliIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:13:06 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktldVpWdWQzcEVIbGFJS2dDN05GSVE9PSIsInZhbHVlIjoieERuZ1dVL0VqMTlETFBQdWpSanJZbTRQSnZBeUZXQUN0ZW0xVHk3UFFESWsrUENMMmRUNUpvTGtQSk9vOWhlRGN4SHM1RVg5cko2QkJWeEJmSksvTDRmblhqbm9aZFAwb0t6TW1yM1lCNlJISzBlYVoxbzFyTEZTNnRZbEhZZlV0RjRVUVF6eXZaUjA4bU5PV0gzUFA2b3M2SkExRFdaQlhuZTA4U1dEUmdJdXl2WUhTMHY1a1FLWjR5bFZUdXVOQXAzT1pMWDhnWVdFWlI3ZjNtZllPdUhxZ1kwbFFPN0p1RVdGV1BjUU1vTHRYMThrUjlsQ0poRXVNTEltb1oxZGtsK0xHdnBBZURncmZJUCtPY05IdGRIZVQzNU80ejZPZXFGK3Zpb0Z5SlRoQmhxK2RZMFhTRnN0d3JhTm5SYWJYc3UzdGxyN25Ia3VFNGJaT21ISGNGaDVNV1BqVU9xTmdtUTF4aWZxSFhWSWlGMWxkK2tEdDNDMkJkaEZCVHNWZFZKZkVpTGZMdE5ielNtOEdEK0VoOVBpeGF4L3g5THR0T24wbG5xcDhMRGloaGgzbUIvb1JLWjR0SGNmc0krTGRaK1RMWkN0WEVuQUxsR1kwakk5RkdGQ1k5QUZEa2t2Qm5CY2hlT0dyWEN3bmo2Ni9LOGxBSU9yM2VlbHYwVHIiLCJtYWMiOiI1NzNkNmJmNDZkOTIxNmFlNjE4OTgwODk2ZjhkMDBiNTA2ZmMzNWQ2MTE2ZmEzYTAyZDdiNzA0MDZiZThhNzVlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik5TSlI1Mk1TaG1wc25DbEFZRkJRQVE9PSIsInZhbHVlIjoia0RySTRJbWFYK25sUHd3OUYxeDJjeit0R1FHblRrTkFRUUE5TXR5UkUxZUJRM2ptUGk3OGZoZXJwS3lDSXBmaUx3blFPbnVGNFQ4cFpuZTZocngwQjFTUjJiSlA0eitHeC9CL2pHUFlweTZiQTFOMFhlblZXbXh5allVajhKVG5pL2FoTmJ1MFBoTUhhMTN1aCtDVkh1NWhqT2c2bE5iMy9JbXRscXlhYzkyZnlNWTBXSy9yRWxoS1FnWDAyZzFVeXJHNXZFUkxUSGx5UXdVbzhReFp6RGxtUmErb3FISjNENmMyMGhkTzVqaFM3RWlFOXNXYmVTWVVsOFJMQVNiME5KZkRpTXhmbWRKSkNpSTMzeElPMkFHajB5SXUwNFl3V2pmY2xzeHVJR0NSeVVHY1ZWbm02bU1OYTB3ODNzK3dvczZGWVZTWm9pRTFYOTlKdGlnYis4eW54empGWE5pSGpSb1VUTFo1VGh2bkJLS0t5STZoMkVOS0FldTNNODN2MldNYVpURU9WNFRWcFQ1cWZUeHNra3hTOHdMdDNKc0hCNXpoQ1NtYkJmd3plWEV2akwzc3I3MGliVWdpU1Q1UFNWVG1BN1M2T2c1Si85UUkzKzFPYVR6UDN2R3hvU3R3Qm9FSEI2K1lwV1BhdW5NUi9CK3AxWXlSaERJaE1nc0oiLCJtYWMiOiI1MThmYjBlM2YwMzkyYWYzYjljN2E5MDFmNjQ4ZDZhZDY1MDVjNTA5Y2UzZGI1NTZjMGU3YWI2N2IyOWYyYjliIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:13:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2091329070\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1113082801 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113082801\", {\"maxDepth\":0})</script>\n"}}