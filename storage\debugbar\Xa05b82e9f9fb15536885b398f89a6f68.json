{"__meta": {"id": "Xa05b82e9f9fb15536885b398f89a6f68", "datetime": "2025-06-30 23:06:16", "utime": **********.292179, "method": "GET", "uri": "/add-to-cart/2353/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751324775.806305, "end": **********.292192, "duration": 0.4858870506286621, "duration_str": "486ms", "measures": [{"label": "Booting", "start": 1751324775.806305, "relative_start": 0, "end": **********.18533, "relative_end": **********.18533, "duration": 0.3790249824523926, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.185339, "relative_start": 0.37903404235839844, "end": **********.292194, "relative_end": 1.9073486328125e-06, "duration": 0.10685491561889648, "duration_str": "107ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48659264, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.02598, "accumulated_duration_str": "25.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.219683, "duration": 0.02094, "duration_str": "20.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.6}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2502568, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 80.6, "width_percent": 2.348}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.266634, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 82.948, "width_percent": 2.04}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.2686222, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.988, "width_percent": 1.078}, {"sql": "select * from `product_services` where `product_services`.`id` = '2353' limit 1", "type": "query", "params": [], "bindings": ["2353"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.274293, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 86.066, "width_percent": 1.655}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2353 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2353", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.27891, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 87.721, "width_percent": 10.393}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.283117, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 98.114, "width_percent": 1.886}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-753834720 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-753834720\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.273178, "xdebug_link": null}]}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2353 => array:9 [\n    \"name\" => \"مفتاح علبه\"\n    \"quantity\" => 1\n    \"price\" => \"6.00\"\n    \"id\" => \"2353\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2353/pos", "status_code": "<pre class=sf-dump id=sf-dump-1128891563 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1128891563\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2105713325 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2105713325\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323255132%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik96Z0M3V1czY2ovN3VVb0ZIdjAyVnc9PSIsInZhbHVlIjoiUC9IV3gyU1FBRGk3WUZwdVZRWGVzNjFaNVdCa2tVdFdhNjNWaFBaallidUE3WUlMVi9BNDVYYjNSVGJuZGp5KzNpaGVnR2xIUFRhVFIvcXJ3N01XZGh0dmdZd29WQ1k0SmpHK1doL09lOGVUejUxcVZpTmpPYzlYRzVGSlFTdXp5ZVdYRytuYVE5ZitDVVFpRU56dU9mSmEybFlKQUQ2eUNNVXRZVGpIREd0bmd4TTVaa3JRTW5veTladk9Zd2ZNTWx0RVJzVjVST2d4Zmo5V3hPQ25rc2o2emxHOUp1WE5EYXNjN1p3ZnMvTVJhQWVnbkRZOVRXM054b3lIbU9KOGh3eVJhaDBCVUdPRjRITE1tY1RYbGtDYnBlZUd2QTQ3WTc3Z0x0R1lYSHVaZ2Z3UlBkNkd4bzRLaUJhQXRtYldLQVc5QjMzdmR2RGU3em55b3VoanpqS0FNd0pPS3ZZYW1VcWRBYmVqVGgvek02bmlIMHgzYSt2V1hGNzNqemhaQnZ1VEo3a1JnM3NtMW00Mm8ycDU3U2FIWnNVemwyeC9QZ0xKMW5zcDlRd0ZqeDlhU3BZV3RWVUVoam54eFRjcmhHWnJqZkdmUnNTZjh1bVlaU0hNWDFNWVBQNnkvUitNQXdranJxamRleml4a1V3SE8vRFgzOWNpM2JTdm9OTDAiLCJtYWMiOiJlODFmMTkyNzM2OTZjODc5NjZmYmVhNWY4Mzg3MGNkMDRkOGJiMzM1NzlhNTRiY2Q1ODAzYzliMWM5ZDk2ODQ1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InQ2eFdIQ0VqQnFVaU5EcEhTVVJHWnc9PSIsInZhbHVlIjoiTWI1dk90cHIyUkllWDZFemJCS0lIa056WEw1Y2V2ZWVnWkpxUkE0YUxZYVhEV29KQUFuVFdwZFYwVzk4U3gwSk5SeXJwQVlrY2lYdTVxU2VScWUyWVZ3MHlILzdIazBIZDVwN25qRTl1QjlaSUg3RGc1cE5IMHFSQ3BNeDNNb28vZklhZWdYcUQyY2tJRE1EOUJ1NTNEV09Xb1lzU1U5VWR2TFlzY01YcTFBa2hIT1FOemlRZHBmcm9tVU9RNjk5cEZHd1F5bE1rWG5SVWtmUStQSWRjckZFcGVHekpvZFBUMzZvTDZlOXNrMEphdVY3aDhXTktMejRqRmg1ZzhTWmNYNWdnMU90czR3VVloQnl3QzRxMkU1dzNyVWkzcUUrUksyQS9rTjMwTTUvN2RkeHZRUFZEMi8zOFp3SDhZekx0bk1zckhMbFZKdzFJWnJTeXM5Z2pLN3V0K0NqaGxkVHBHakROUk5LUEVBUHZkRmZDZnJpMklSUjJ5Z0pIQ1EyRmQrSUp4UDkxUzVoR2k4VkFEUGJsTkpjajZ6SVc5SmVCV2lIUmc1S3B5VmI1NG8rRzVsM0dlNmNiaW9jWnp1WXEzV3FtM3ZDWFczRGRzaVlIWUNTemROemZIWWZ1enZ4cUlKUUM2N0phby9sVGVWVi95T2FjZjNQNG1RR0I3WVIiLCJtYWMiOiI4YTZiNzlmNGQ1MmEzNGZlYjAzOWVjZDA5MzQ1MDAwM2NlMjY2OGM0NzRmNzBkN2ZmNGFjYjVlODllN2RiMTE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-942216999 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-942216999\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1418714349 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:06:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InB3MGZuVVA2aG9ZZk83Y2ZLKzRMTWc9PSIsInZhbHVlIjoiWitwVW1PWkNVSm9DWGFpc0UxMkI2SVp5S0V4ellJa1IrUDZrSFc0TGxCNi9FencvU1UybVIrbDFhU3REc3FqbWgwSWswaWJvWXYzaUNmRW5CYUFqMFkxTWFWTUlMVmdBT3VzaXMxV0cvZFVJYlJTK3JMUUZKSWd6S3BHblhHaVF3WU8wd2V6K041dWV0RUx5UHJEVllBQ2poS2N4YXZIeFYvVEplNWhueWxUK0lSZ3kwTTRYYzAvOHhFY3BiWm43K1V2TzVzSTJSNFRXMzNZQUJtRFlhYUp5dDQwcWIyWnUrT1pWN3YxejhKREwzY1R3QUdpeEdDaytqNC80eHk2NGdJNytNK3U2QzZvZEtFQS85dzA5UkJhNWVNNHMrVWs4cWhoUUhtZ0NVT3pNbTFFN2dNVkpWN21tTis3UklMTTI3SmUyV0FIWG1zNDZucUN3Yzd3MmVXUGJNVkJ2UzNEWFdKYU41TUZxMHFFUFFlZnBiV3c1akZ3eHdyY2RSVGx1SVhWZGI2OGtnODI1eW1OaXBab3BNd3ZoN3dWSGxMU0VJeENYWE9GVVBiYjhsdUdKVUVLM0pNWDk1blUwM3lGVHUySGhYVTBhdFd0ZjdSU0RpbTVOUU9MbjR4MW9DdElFTmNIZjdOT2NXbENMdXNmNWRpRkxBQ2t0UEplaGZaUUQiLCJtYWMiOiIwMmUwNGUyNTYxNjBmODEwMTI3ZmM2ZTJiNWNhMjAxNTQ1MmIxOGM0Yzg2YWIwODI4YzNhOWZhYTc2ZDc2ZDEyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:06:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InRXdlZMY2g4MUEwdkQwNThPWFVOTkE9PSIsInZhbHVlIjoiSCsyNFd6eklZeGUyeGxHT3VWdHZYcmErbmRUK1ZyRi96R1VEQUd5TitPWWVxdmV3U3c3SmxsalB4eDdFWFBEd2xZZkhYc2JQdzNpWkxaT2E1YnhBYVQ4aEtWSzRHejAwVW1pYVRFd2duNVBpR0RwU3lPTDk3MkFmM3Z4c1pzM3kwSW1ydlErMkV6V1E5R2svekt0Y1lML1daZ2wzSGY4WUJnV0Nqa3hZOW1rWUw3ZkdVdC80cnVGVERrMDRKTHg5blBiUlZ3NkpTaU84UXNWSi8yMlp2VndNdjdubzE1YTNMbnc5M3pHVmlGdG1EZmVNYXllYll4cTZ4NFpadGRRTmxpNzlpdjlMV2Foa0o3S0c4Mi9IL250SGZiSGJ5WjY5N1B6ZFRrV3Y1VDJPSld0WEFhUDV2U2JoOVdlbVhhWUNEc2tVZytzU1BXYzNOazVtRThvRFBjV0pONFVvWEVWb0dUVkd2OUF4NTIvYXZKQjF4WmxzaFRuQ0VlN1MzcERZSURCZjU1bXJIOGtoSHQxSU9xZHNrRDJGSE5tRUg3ZUJvSmVhV1h1TE85ZlV2NGRNWlJHMjN5a0FxZlJZdGJ6aHZxeXhiWFRWU3hRRXZEZzJEcHJCR0NTSTVmMEVQQkZNUjI3Nk82NVZvcElsMEJCV2ZDREFFeE80dFpDSXpWZ0QiLCJtYWMiOiIyZjgxYjU3YTc0NDljYzI3Y2JkNTc5OWU0YTczMWJmMjUzNGUxZWY2OGFmZDNiOWEwNzE4NTVmMTA3YzUzNjY0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:06:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InB3MGZuVVA2aG9ZZk83Y2ZLKzRMTWc9PSIsInZhbHVlIjoiWitwVW1PWkNVSm9DWGFpc0UxMkI2SVp5S0V4ellJa1IrUDZrSFc0TGxCNi9FencvU1UybVIrbDFhU3REc3FqbWgwSWswaWJvWXYzaUNmRW5CYUFqMFkxTWFWTUlMVmdBT3VzaXMxV0cvZFVJYlJTK3JMUUZKSWd6S3BHblhHaVF3WU8wd2V6K041dWV0RUx5UHJEVllBQ2poS2N4YXZIeFYvVEplNWhueWxUK0lSZ3kwTTRYYzAvOHhFY3BiWm43K1V2TzVzSTJSNFRXMzNZQUJtRFlhYUp5dDQwcWIyWnUrT1pWN3YxejhKREwzY1R3QUdpeEdDaytqNC80eHk2NGdJNytNK3U2QzZvZEtFQS85dzA5UkJhNWVNNHMrVWs4cWhoUUhtZ0NVT3pNbTFFN2dNVkpWN21tTis3UklMTTI3SmUyV0FIWG1zNDZucUN3Yzd3MmVXUGJNVkJ2UzNEWFdKYU41TUZxMHFFUFFlZnBiV3c1akZ3eHdyY2RSVGx1SVhWZGI2OGtnODI1eW1OaXBab3BNd3ZoN3dWSGxMU0VJeENYWE9GVVBiYjhsdUdKVUVLM0pNWDk1blUwM3lGVHUySGhYVTBhdFd0ZjdSU0RpbTVOUU9MbjR4MW9DdElFTmNIZjdOT2NXbENMdXNmNWRpRkxBQ2t0UEplaGZaUUQiLCJtYWMiOiIwMmUwNGUyNTYxNjBmODEwMTI3ZmM2ZTJiNWNhMjAxNTQ1MmIxOGM0Yzg2YWIwODI4YzNhOWZhYTc2ZDc2ZDEyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:06:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InRXdlZMY2g4MUEwdkQwNThPWFVOTkE9PSIsInZhbHVlIjoiSCsyNFd6eklZeGUyeGxHT3VWdHZYcmErbmRUK1ZyRi96R1VEQUd5TitPWWVxdmV3U3c3SmxsalB4eDdFWFBEd2xZZkhYc2JQdzNpWkxaT2E1YnhBYVQ4aEtWSzRHejAwVW1pYVRFd2duNVBpR0RwU3lPTDk3MkFmM3Z4c1pzM3kwSW1ydlErMkV6V1E5R2svekt0Y1lML1daZ2wzSGY4WUJnV0Nqa3hZOW1rWUw3ZkdVdC80cnVGVERrMDRKTHg5blBiUlZ3NkpTaU84UXNWSi8yMlp2VndNdjdubzE1YTNMbnc5M3pHVmlGdG1EZmVNYXllYll4cTZ4NFpadGRRTmxpNzlpdjlMV2Foa0o3S0c4Mi9IL250SGZiSGJ5WjY5N1B6ZFRrV3Y1VDJPSld0WEFhUDV2U2JoOVdlbVhhWUNEc2tVZytzU1BXYzNOazVtRThvRFBjV0pONFVvWEVWb0dUVkd2OUF4NTIvYXZKQjF4WmxzaFRuQ0VlN1MzcERZSURCZjU1bXJIOGtoSHQxSU9xZHNrRDJGSE5tRUg3ZUJvSmVhV1h1TE85ZlV2NGRNWlJHMjN5a0FxZlJZdGJ6aHZxeXhiWFRWU3hRRXZEZzJEcHJCR0NTSTVmMEVQQkZNUjI3Nk82NVZvcElsMEJCV2ZDREFFeE80dFpDSXpWZ0QiLCJtYWMiOiIyZjgxYjU3YTc0NDljYzI3Y2JkNTc5OWU0YTczMWJmMjUzNGUxZWY2OGFmZDNiOWEwNzE4NTVmMTA3YzUzNjY0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:06:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1418714349\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2082386884 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2353</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1605;&#1601;&#1578;&#1575;&#1581; &#1593;&#1604;&#1576;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2353</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2082386884\", {\"maxDepth\":0})</script>\n"}}