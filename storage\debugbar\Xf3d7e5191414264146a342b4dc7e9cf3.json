{"__meta": {"id": "Xf3d7e5191414264146a342b4dc7e9cf3", "datetime": "2025-06-30 22:38:56", "utime": **********.224131, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323135.758065, "end": **********.224153, "duration": 0.46608805656433105, "duration_str": "466ms", "measures": [{"label": "Booting", "start": 1751323135.758065, "relative_start": 0, "end": **********.175418, "relative_end": **********.175418, "duration": 0.41735291481018066, "duration_str": "417ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.175428, "relative_start": 0.41736292839050293, "end": **********.224155, "relative_end": 1.9073486328125e-06, "duration": 0.04872703552246094, "duration_str": "48.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43346552, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.0026, "accumulated_duration_str": "2.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.208869, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "RtHbcRqD8V7q1fDzvV41Fzq9te9B2VUCpYlSqZiL", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-550718412 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-550718412\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1563434088 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1563434088\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1300335551 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1300335551\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2012065351 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2012065351\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1864069440 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:38:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRjOXJKbHpHSmQwQ3dNMjlZVGR6ZHc9PSIsInZhbHVlIjoicEVyaGExWDduV1JBekJFRlJZa05HOXZlWXc4ZjluRjJ3S0RFYnlkenBYdWFCdUEybk03RU51UHhhcDZLeW4vZENtUUdyZ3FDeTRjNFZBeWhXRVI4OEhaamlqa2lUVi9NdUl6bSs0U3FlUXQxdHptSWZnMndqY3FHV2IzWUxNSGZLUjQ3M01WLzZPNzBsdDZQSGxxOWd4TW1OTzRodDkyL2J4MXpRU0J6Z3owMThSVmkxUHowR201eVZhTHJpZ2lwTzd5eVlSNVZzOW1hNUdabk9TcXpCaHJScWt0YkRMQ2NnSCsxYkFadkhkcHFqQnMvQmF2TVByR0x6OU1uRTJlaEVnYkFBZGNnM0Q4anRMaWdlNHFiaW5LTnRaY1B6b0gxekxUVHZZbDB0ZnF3bGl6Wk1vdGZYQnBtYVVpY04rWEVRTFdMbHhqaUlWTUdtSEpPeEI1RUVCWWZSRWs2bTRZc3VDeUk3bWZGaWpyclpCRW54YXMvWlRRbmtYSUNGRmlGRVl2WlR1Uzd0QWlDVkJzWXpMVzFYMythNU5SczRiMHNhbFJFNisxL1ZDTThzTk1oODRoa1A3d2JSdlI2ajBreFl2bXlhZ0RwNWVVclhqa3lzODFCcmRTc0NzZ2p1YklLZ1YzY0dMbU5aeXdqVmJUVHY2QWZvWWZxZXBsamxkaVoiLCJtYWMiOiIxNDVkYzM0ZjE3NDYxYzdiYmExNmNkZjNmYjgyNTdmZjAwNTZjMGI1OTAzNWUyMWJjYzE3NjJiZDU3NDgyNjZhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:38:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im5YcmxPMTRjRVNNU2dtNDlHcU5nWGc9PSIsInZhbHVlIjoiM1lCem5NOUQyZkQ0dlM1QTlJclpwRTlBd09mWVRhSndFcFppeFlHbi9ma2ZlNHFXM1pzRkUvWGNUZTMvZWhTMlh1WWRXdFU0NyttdEsvVTg2VUpyOGd1ZllRRVZ3MzhQc0FJYVZhUUxRNmFnYzU5c1dVcjI4aTVGbTVtTVJGUGErelZYY0F6MGtZU2taTlRwOWIxeEdQQTRVeDlSemxqeUU1cmRlTWo4RUdMeERFV3dmUXdENHkrREVoWmovUXA3V0lyd1c4NDV1KzJweU9GdUtSMUZLblkxV1FycmU2T1cyeWdudlM4bkJ4d2Y3cHdPUG5rK1NWUHQ0M0lZRzlrb0hMYlNyTGVsTUNVWE9JMWpCcytRQXBOcWxGM0l1RC8yNENYVVIwMGlMTy9qQ3RTNENCcjZmaHBsZWZ6OU5ZdHo1V3RZdzNsdGJaSmhZVktQWGI2OWxodzIwVmFsRWhmM3BEV3lCSjVmTzh5QmNtZWU2L0xUNHRmVU8xM1F1a2VsZmFWMXFmYlZOWXdSWjlKTFU2OVp2Znl1NzYzZ2FEU1lEMUF0V1A3bjYrd1cyMnloMEcyZnNrQ0FiTTEyT2l6Vk9xdktvTXhJeS9KeCtDbUw4V1A2RWhjdDVRZHpNeE1iVGtoblZ3V2RmQndXWkxjOTlMUTJBaDZDUExEZWZhN3MiLCJtYWMiOiI2MjE2NTI0ODY4MWVjNjM5YWIxZDM4MWIwZTYwZjBjMmI3ZjIzYTk4Mzk1NTcxYWFmNDI3YjFhOTcwODVlY2YyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:38:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRjOXJKbHpHSmQwQ3dNMjlZVGR6ZHc9PSIsInZhbHVlIjoicEVyaGExWDduV1JBekJFRlJZa05HOXZlWXc4ZjluRjJ3S0RFYnlkenBYdWFCdUEybk03RU51UHhhcDZLeW4vZENtUUdyZ3FDeTRjNFZBeWhXRVI4OEhaamlqa2lUVi9NdUl6bSs0U3FlUXQxdHptSWZnMndqY3FHV2IzWUxNSGZLUjQ3M01WLzZPNzBsdDZQSGxxOWd4TW1OTzRodDkyL2J4MXpRU0J6Z3owMThSVmkxUHowR201eVZhTHJpZ2lwTzd5eVlSNVZzOW1hNUdabk9TcXpCaHJScWt0YkRMQ2NnSCsxYkFadkhkcHFqQnMvQmF2TVByR0x6OU1uRTJlaEVnYkFBZGNnM0Q4anRMaWdlNHFiaW5LTnRaY1B6b0gxekxUVHZZbDB0ZnF3bGl6Wk1vdGZYQnBtYVVpY04rWEVRTFdMbHhqaUlWTUdtSEpPeEI1RUVCWWZSRWs2bTRZc3VDeUk3bWZGaWpyclpCRW54YXMvWlRRbmtYSUNGRmlGRVl2WlR1Uzd0QWlDVkJzWXpMVzFYMythNU5SczRiMHNhbFJFNisxL1ZDTThzTk1oODRoa1A3d2JSdlI2ajBreFl2bXlhZ0RwNWVVclhqa3lzODFCcmRTc0NzZ2p1YklLZ1YzY0dMbU5aeXdqVmJUVHY2QWZvWWZxZXBsamxkaVoiLCJtYWMiOiIxNDVkYzM0ZjE3NDYxYzdiYmExNmNkZjNmYjgyNTdmZjAwNTZjMGI1OTAzNWUyMWJjYzE3NjJiZDU3NDgyNjZhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:38:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im5YcmxPMTRjRVNNU2dtNDlHcU5nWGc9PSIsInZhbHVlIjoiM1lCem5NOUQyZkQ0dlM1QTlJclpwRTlBd09mWVRhSndFcFppeFlHbi9ma2ZlNHFXM1pzRkUvWGNUZTMvZWhTMlh1WWRXdFU0NyttdEsvVTg2VUpyOGd1ZllRRVZ3MzhQc0FJYVZhUUxRNmFnYzU5c1dVcjI4aTVGbTVtTVJGUGErelZYY0F6MGtZU2taTlRwOWIxeEdQQTRVeDlSemxqeUU1cmRlTWo4RUdMeERFV3dmUXdENHkrREVoWmovUXA3V0lyd1c4NDV1KzJweU9GdUtSMUZLblkxV1FycmU2T1cyeWdudlM4bkJ4d2Y3cHdPUG5rK1NWUHQ0M0lZRzlrb0hMYlNyTGVsTUNVWE9JMWpCcytRQXBOcWxGM0l1RC8yNENYVVIwMGlMTy9qQ3RTNENCcjZmaHBsZWZ6OU5ZdHo1V3RZdzNsdGJaSmhZVktQWGI2OWxodzIwVmFsRWhmM3BEV3lCSjVmTzh5QmNtZWU2L0xUNHRmVU8xM1F1a2VsZmFWMXFmYlZOWXdSWjlKTFU2OVp2Znl1NzYzZ2FEU1lEMUF0V1A3bjYrd1cyMnloMEcyZnNrQ0FiTTEyT2l6Vk9xdktvTXhJeS9KeCtDbUw4V1A2RWhjdDVRZHpNeE1iVGtoblZ3V2RmQndXWkxjOTlMUTJBaDZDUExEZWZhN3MiLCJtYWMiOiI2MjE2NTI0ODY4MWVjNjM5YWIxZDM4MWIwZTYwZjBjMmI3ZjIzYTk4Mzk1NTcxYWFmNDI3YjFhOTcwODVlY2YyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:38:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864069440\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-259686030 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RtHbcRqD8V7q1fDzvV41Fzq9te9B2VUCpYlSqZiL</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-259686030\", {\"maxDepth\":0})</script>\n"}}