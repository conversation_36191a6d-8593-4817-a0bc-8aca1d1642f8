{"__meta": {"id": "X5c4a20b1a1218081810c45dffb2fb32b", "datetime": "2025-06-30 22:36:28", "utime": **********.688597, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.247996, "end": **********.68861, "duration": 0.4406139850616455, "duration_str": "441ms", "measures": [{"label": "Booting", "start": **********.247996, "relative_start": 0, "end": **********.640198, "relative_end": **********.640198, "duration": 0.39220190048217773, "duration_str": "392ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.640209, "relative_start": 0.3922128677368164, "end": **********.688612, "relative_end": 1.9073486328125e-06, "duration": 0.048403024673461914, "duration_str": "48.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43889608, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00277, "accumulated_duration_str": "2.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.676632, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.7}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.681877, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 78.7, "width_percent": 21.3}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:4 [\n  2352 => array:9 [\n    \"name\" => \"مجسم شخصيات مختلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"2352\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2353 => array:8 [\n    \"name\" => \"مفتاح علبه\"\n    \"quantity\" => 1\n    \"price\" => \"6.00\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"id\" => \"2353\"\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n  ]\n  2354 => array:8 [\n    \"name\" => \"العاب شكل بطه\"\n    \"quantity\" => 1\n    \"price\" => \"6.50\"\n    \"tax\" => 0\n    \"subtotal\" => 6.5\n    \"id\" => \"2354\"\n    \"originalquantity\" => 9\n    \"product_tax\" => \"-\"\n  ]\n  2355 => array:8 [\n    \"name\" => \"العاب اطفال\"\n    \"quantity\" => 1\n    \"price\" => \"6.50\"\n    \"tax\" => 0\n    \"subtotal\" => 6.5\n    \"id\" => \"2355\"\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1043140192 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1043140192\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1324843122 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1324843122\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=6nrx0y%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1aj6s00%7C1751322883410%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNYRnBDdFNpby9wUTVKVFhEQTNJZlE9PSIsInZhbHVlIjoiKzZ5MkZCZXZ0TlBDRUhSRjZ0TnYxVWhTMFFQeklQa3BzR2NpOTdrd2I1ajRlR3lseUFoMm5Cd0cwZWRuUkYrZU9ieDFHSGo1SnhMWmI4c1FVWmVJRzZORVFUSExIMEFKbkROWVlkOEx3L0p5bVRObnEvZEUzb3QwTjJFMkRvbFBhLzFpRjF1REUya0h0dmQ5SjhockduY3hiVGJxT2VpdGp4ZUEzOHpuN0EzMmlRRFhNcnNqVlJ5dnRiQmluTUdDRlMwR1BpbUpVZ0Fpd1N3RHZnOTVheTlWaXZ1cGczbDc0eEkrV3RrNldOUVlIU0g0aDZVY2VjNlNzS2o2S3oxR29keThINlp6MEFlMEZpajAxUHlCbVR4OWV5WCt5Q0lYM04zRU9lbWFMSkNoV25ZbzBSK0JvY1BKamFyMVpub3lYZW55UVU2c21JanZFeDFnNnozbjcySzZWU3VBamhOSTJrRTh0WGJmRFFFV3JzUFVkT3czRUJFa1pOU0ZuVUhka0sydFZIdGNZcm1iZGpKTUNqZTJpQmErSkdaTWJkN0NpTWtUS0RTSWtNR0kxUUYrclllTEg4WENscC82QjBsaFFvci91NFRMckpsWkVTQzk0MEZpVmcvQkZqakpaYmJ6T29tRUlYQ0dseE82TGdBdkN6NERGZTBJK3E4Sk5kNGQiLCJtYWMiOiI2M2RjYjM4OTNhNGY5ZmI4NzA4YTkwNTA2NTM3Yjg0NmQxMzBlMTRjMTY5N2Y4NzVjODg3NjllM2U3YWIzYmIxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ijl4aE95VFBmRENFUXJQMWFOZ3hZYlE9PSIsInZhbHVlIjoiTkFXSnJ5eStuVS9zM2ovY3JoMnhFQWs2RWtsdkdWM1ZqcDkybFZCeWFIWFp2S3RQZ1NCbUFmdk9venpmQ2VZZHdKcit4eXprR01NOWVWMzltanM5NkRBT0JrM0FHa3h1SzY1bjNPUDgvNjhYNnpNY1BZT0VsZFF0UlpWYTFTTklibHdyODJhT0xIcWZaZXVNelVMNUhDdmQ1MUJ2ZzZpT3kxSUloazlGbXZPOWNHQjByOXRiM3B5SmN4Y1E1WTJVZVpybHZPckJoQWk2Rk8xM3pwdHFKTG9ESk55ZlVuYTdaVDRpMTNuSmhlVXAvOUs0RzhNbHpOSmhmajkzOXJoQjQyZ2F4WmFyMWVBMm1GRm12Q1AySVFhTDM2MU5EaFVia2lXQzhjdTRiemZ2Q3J1Zi9jVUlzRFd6Tndua0NtVUtDbTlLYjVCcnBVR0ljZXBSRmJBaGFrQldvS1pMVTJkdS9ic0czNkRzUFZ3TWE2alI5b2cwV2dsWEttUi9rQjVzZDJGOXZBYmFESisxNXRCOGVmcW1SNDc4TU9RZVgweXlQRnZ2djdwQmVMdm5OMXllcWk0SEdQTGlaNkpBZGpiclNzUmJhYkhTOElFNGlyUHhjRWlzak9iMVp0WEV3OHJMSDZUMFFydlFYMGNZNXpSQmx3SCt1ajRSY2hpMTFKSkoiLCJtYWMiOiIyODVhNTI4MzBmMWM2YWUwMzRlMTdkMzk1MDk5NzZmZmZlMWNkNzcyZjRjYjJkNmQ4MWI3NzNiMDQwZTJjODg0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1212778328 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A9rdyCRm7SKpfljuMnVO7IpcgTBMITMjowAdsmJo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1212778328\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-454101468 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:36:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVMSTBmRzRIMXptMmJSRUtPWFJoTEE9PSIsInZhbHVlIjoieUl2TEdFaEtUcStaQzNURDI3QVhQbE9GeHc3Y1pyTnBOQm1oOHF4SExWaEp6OXh1QWJEVm1ESysxaER4UmNDb0dOVmk2MkJQMjRIeXY5NUJRRFliWGJIQnM1SWFJWmdYRFBBQTFXWUFpbm1Od0RLdHd3a21CWlRPcC9aTGlnVStjbHVFYmRibWxCOHBGU1czSndXVDJuZWFwOFNlM1hEcFhCNWZoazhTUXlJK0Q3SVFBcm9qWCtrSjNBakZtUEptWDhUTC82alFwUGZvOHB1ODlMUGg3YWZjSDNWaVh5M2dUeHF0cjVBV2UvT1VSaFppeUUvcXRkMWhSd1NFS0VYeEN2WmxIeGZwd2JKM0pNV3JHUXU5bHlZaWJOQXZLSjI3TS9lSCs4bEJYZGN6Y1AxUnVCK0x0WTRwRXV5NkN5S28wTURpdmtuc1ZBRmFrWml1ZkJoRkV1M2EyMVZFTXhxb1RhNFB0NVNoNjBvRzJDSWJxcW1aSURBZ2ZNcVFocjl6MUwxYy8zTk9BVUJpSWpaVzRqUWE2RGlLeVJEMEErTjh5YmVpY0xVdjNkYjlrYk56cXd5QXlmU2dxdUt4dUFpWHl4bkNtOW9aTkpNMmtEK1RmeDFWY0FnVjR5VXdMOEI4UDJ3cWZ1MFFnZFhoVTlQa0QzbjRucGRFNzBDOWUwM3MiLCJtYWMiOiIzNjJjY2Q0OGRiNTg5NDU1MDNkNTJiNjY0MTFhMjEwNzgyZjc0YTVmNDJmNWEzYzE2MWRiYTk4ZmRiNWViZGU1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlN0KzNtenNKTkkyM1BrVm05anFxY2c9PSIsInZhbHVlIjoiQWFZeWpYQlhaNjBERE5sTFd3SWFNd3dvMThxTmR5QlQ1WjVJSnJrTXJVYlI2bW9YWUZXSm1ScHNhV3JmQXZhTmV3eFRwL1EwUm5kSFN5V01IeFY0Rjh0eGNEb1c1M2lNUW5SQ0M2OGN2aG5VYXFhaEROYVljREdoaVBNTndTT1BTZHVUSFRQdCt3Q3U5WnVxQkxnUFlISHdDOFVuL1lvdDFGaEZ2Q3ZVWU55NlhsNCsrNmdPbEg0bVNxcWpXTXl3N3RqcXpSb0RZMTdUSVRzcFEwVXdTZWlaMHd6WGllOGFJeURYa0RFdjZyT3ZMWVV2OERyL2JBR2dBN2xjZXJPYXIvcHRlTjBub0hUaTUwczdubGUycDEyM2lrS1doQ3V4azFXU2kzM3lmQXpyNXhYUnpycTVhdS9NOTlBTWh3RitNQytLUEtRTVdzbUpoU3RSZW9qRjR0d0J4N3dZTnhlaUNRVncwQUJENmh5VzVBcGhSSzd4a2xqTGtQVVBGa3BhNkdpZUhiSWlPbWRDSSs0Q3ppakt6NVVwNEZxejdFV01ncE4rSyswQ3M3aitFVjhiZ2VxWmVqcG92UzRmRnUwN3BxaTFDOW5ENTRPZWdFM2JEZ2xGUy9sV2ZScGlmT3QvdFR1Tlp3OFZmSFhabEZxQjRReVBTNFZOKzB4djNNWmkiLCJtYWMiOiI2Y2U2Y2UzNWZlYjM1NmY0Y2Q1OTEyMWFhMzQ2ZjQ4YjM5YmQ3ZWU3ZTY0Y2ZmMjQyY2I5N2IyOTEwMDQ2YTM4IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:36:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVMSTBmRzRIMXptMmJSRUtPWFJoTEE9PSIsInZhbHVlIjoieUl2TEdFaEtUcStaQzNURDI3QVhQbE9GeHc3Y1pyTnBOQm1oOHF4SExWaEp6OXh1QWJEVm1ESysxaER4UmNDb0dOVmk2MkJQMjRIeXY5NUJRRFliWGJIQnM1SWFJWmdYRFBBQTFXWUFpbm1Od0RLdHd3a21CWlRPcC9aTGlnVStjbHVFYmRibWxCOHBGU1czSndXVDJuZWFwOFNlM1hEcFhCNWZoazhTUXlJK0Q3SVFBcm9qWCtrSjNBakZtUEptWDhUTC82alFwUGZvOHB1ODlMUGg3YWZjSDNWaVh5M2dUeHF0cjVBV2UvT1VSaFppeUUvcXRkMWhSd1NFS0VYeEN2WmxIeGZwd2JKM0pNV3JHUXU5bHlZaWJOQXZLSjI3TS9lSCs4bEJYZGN6Y1AxUnVCK0x0WTRwRXV5NkN5S28wTURpdmtuc1ZBRmFrWml1ZkJoRkV1M2EyMVZFTXhxb1RhNFB0NVNoNjBvRzJDSWJxcW1aSURBZ2ZNcVFocjl6MUwxYy8zTk9BVUJpSWpaVzRqUWE2RGlLeVJEMEErTjh5YmVpY0xVdjNkYjlrYk56cXd5QXlmU2dxdUt4dUFpWHl4bkNtOW9aTkpNMmtEK1RmeDFWY0FnVjR5VXdMOEI4UDJ3cWZ1MFFnZFhoVTlQa0QzbjRucGRFNzBDOWUwM3MiLCJtYWMiOiIzNjJjY2Q0OGRiNTg5NDU1MDNkNTJiNjY0MTFhMjEwNzgyZjc0YTVmNDJmNWEzYzE2MWRiYTk4ZmRiNWViZGU1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlN0KzNtenNKTkkyM1BrVm05anFxY2c9PSIsInZhbHVlIjoiQWFZeWpYQlhaNjBERE5sTFd3SWFNd3dvMThxTmR5QlQ1WjVJSnJrTXJVYlI2bW9YWUZXSm1ScHNhV3JmQXZhTmV3eFRwL1EwUm5kSFN5V01IeFY0Rjh0eGNEb1c1M2lNUW5SQ0M2OGN2aG5VYXFhaEROYVljREdoaVBNTndTT1BTZHVUSFRQdCt3Q3U5WnVxQkxnUFlISHdDOFVuL1lvdDFGaEZ2Q3ZVWU55NlhsNCsrNmdPbEg0bVNxcWpXTXl3N3RqcXpSb0RZMTdUSVRzcFEwVXdTZWlaMHd6WGllOGFJeURYa0RFdjZyT3ZMWVV2OERyL2JBR2dBN2xjZXJPYXIvcHRlTjBub0hUaTUwczdubGUycDEyM2lrS1doQ3V4azFXU2kzM3lmQXpyNXhYUnpycTVhdS9NOTlBTWh3RitNQytLUEtRTVdzbUpoU3RSZW9qRjR0d0J4N3dZTnhlaUNRVncwQUJENmh5VzVBcGhSSzd4a2xqTGtQVVBGa3BhNkdpZUhiSWlPbWRDSSs0Q3ppakt6NVVwNEZxejdFV01ncE4rSyswQ3M3aitFVjhiZ2VxWmVqcG92UzRmRnUwN3BxaTFDOW5ENTRPZWdFM2JEZ2xGUy9sV2ZScGlmT3QvdFR1Tlp3OFZmSFhabEZxQjRReVBTNFZOKzB4djNNWmkiLCJtYWMiOiI2Y2U2Y2UzNWZlYjM1NmY0Y2Q1OTEyMWFhMzQ2ZjQ4YjM5YmQ3ZWU3ZTY0Y2ZmMjQyY2I5N2IyOTEwMDQ2YTM4IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:36:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-454101468\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1731250935 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iBoO1yeTaa9ST29Kre2yerWxlSupfNOYrTqSlyUX</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2352</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1605;&#1580;&#1587;&#1605; &#1588;&#1582;&#1589;&#1610;&#1575;&#1578; &#1605;&#1582;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2352</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2353</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1605;&#1601;&#1578;&#1575;&#1581; &#1593;&#1604;&#1576;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2353</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2354</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1588;&#1603;&#1604; &#1576;&#1591;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2354</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2355</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1575;&#1591;&#1601;&#1575;&#1604;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2355</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1731250935\", {\"maxDepth\":0})</script>\n"}}