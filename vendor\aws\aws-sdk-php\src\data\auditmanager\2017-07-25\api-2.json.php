<?php
// This file was auto-generated from sdk-root/src/data/auditmanager/2017-07-25/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-07-25', 'endpointPrefix' => 'auditmanager', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS Audit Manager', 'serviceId' => 'AuditManager', 'signatureVersion' => 'v4', 'signingName' => 'auditmanager', 'uid' => 'auditmanager-2017-07-25', ], 'operations' => [ 'AssociateAssessmentReportEvidenceFolder' => [ 'name' => 'AssociateAssessmentReportEvidenceFolder', 'http' => [ 'method' => 'PUT', 'requestUri' => '/assessments/{assessmentId}/associateToAssessmentReport', ], 'input' => [ 'shape' => 'AssociateAssessmentReportEvidenceFolderRequest', ], 'output' => [ 'shape' => 'AssociateAssessmentReportEvidenceFolderResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'BatchAssociateAssessmentReportEvidence' => [ 'name' => 'BatchAssociateAssessmentReportEvidence', 'http' => [ 'method' => 'PUT', 'requestUri' => '/assessments/{assessmentId}/batchAssociateToAssessmentReport', ], 'input' => [ 'shape' => 'BatchAssociateAssessmentReportEvidenceRequest', ], 'output' => [ 'shape' => 'BatchAssociateAssessmentReportEvidenceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'BatchCreateDelegationByAssessment' => [ 'name' => 'BatchCreateDelegationByAssessment', 'http' => [ 'method' => 'POST', 'requestUri' => '/assessments/{assessmentId}/delegations', ], 'input' => [ 'shape' => 'BatchCreateDelegationByAssessmentRequest', ], 'output' => [ 'shape' => 'BatchCreateDelegationByAssessmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchDeleteDelegationByAssessment' => [ 'name' => 'BatchDeleteDelegationByAssessment', 'http' => [ 'method' => 'PUT', 'requestUri' => '/assessments/{assessmentId}/delegations', ], 'input' => [ 'shape' => 'BatchDeleteDelegationByAssessmentRequest', ], 'output' => [ 'shape' => 'BatchDeleteDelegationByAssessmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchDisassociateAssessmentReportEvidence' => [ 'name' => 'BatchDisassociateAssessmentReportEvidence', 'http' => [ 'method' => 'PUT', 'requestUri' => '/assessments/{assessmentId}/batchDisassociateFromAssessmentReport', ], 'input' => [ 'shape' => 'BatchDisassociateAssessmentReportEvidenceRequest', ], 'output' => [ 'shape' => 'BatchDisassociateAssessmentReportEvidenceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'BatchImportEvidenceToAssessmentControl' => [ 'name' => 'BatchImportEvidenceToAssessmentControl', 'http' => [ 'method' => 'POST', 'requestUri' => '/assessments/{assessmentId}/controlSets/{controlSetId}/controls/{controlId}/evidence', ], 'input' => [ 'shape' => 'BatchImportEvidenceToAssessmentControlRequest', ], 'output' => [ 'shape' => 'BatchImportEvidenceToAssessmentControlResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateAssessment' => [ 'name' => 'CreateAssessment', 'http' => [ 'method' => 'POST', 'requestUri' => '/assessments', ], 'input' => [ 'shape' => 'CreateAssessmentRequest', ], 'output' => [ 'shape' => 'CreateAssessmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateAssessmentFramework' => [ 'name' => 'CreateAssessmentFramework', 'http' => [ 'method' => 'POST', 'requestUri' => '/assessmentFrameworks', ], 'input' => [ 'shape' => 'CreateAssessmentFrameworkRequest', ], 'output' => [ 'shape' => 'CreateAssessmentFrameworkResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateAssessmentReport' => [ 'name' => 'CreateAssessmentReport', 'http' => [ 'method' => 'POST', 'requestUri' => '/assessments/{assessmentId}/reports', ], 'input' => [ 'shape' => 'CreateAssessmentReportRequest', ], 'output' => [ 'shape' => 'CreateAssessmentReportResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateControl' => [ 'name' => 'CreateControl', 'http' => [ 'method' => 'POST', 'requestUri' => '/controls', ], 'input' => [ 'shape' => 'CreateControlRequest', ], 'output' => [ 'shape' => 'CreateControlResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'DeleteAssessment' => [ 'name' => 'DeleteAssessment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/assessments/{assessmentId}', ], 'input' => [ 'shape' => 'DeleteAssessmentRequest', ], 'output' => [ 'shape' => 'DeleteAssessmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteAssessmentFramework' => [ 'name' => 'DeleteAssessmentFramework', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/assessmentFrameworks/{frameworkId}', ], 'input' => [ 'shape' => 'DeleteAssessmentFrameworkRequest', ], 'output' => [ 'shape' => 'DeleteAssessmentFrameworkResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteAssessmentFrameworkShare' => [ 'name' => 'DeleteAssessmentFrameworkShare', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/assessmentFrameworkShareRequests/{requestId}', ], 'input' => [ 'shape' => 'DeleteAssessmentFrameworkShareRequest', ], 'output' => [ 'shape' => 'DeleteAssessmentFrameworkShareResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteAssessmentReport' => [ 'name' => 'DeleteAssessmentReport', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/assessments/{assessmentId}/reports/{assessmentReportId}', ], 'input' => [ 'shape' => 'DeleteAssessmentReportRequest', ], 'output' => [ 'shape' => 'DeleteAssessmentReportResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteControl' => [ 'name' => 'DeleteControl', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/controls/{controlId}', ], 'input' => [ 'shape' => 'DeleteControlRequest', ], 'output' => [ 'shape' => 'DeleteControlResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeregisterAccount' => [ 'name' => 'DeregisterAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/account/deregisterAccount', ], 'input' => [ 'shape' => 'DeregisterAccountRequest', ], 'output' => [ 'shape' => 'DeregisterAccountResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeregisterOrganizationAdminAccount' => [ 'name' => 'DeregisterOrganizationAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/account/deregisterOrganizationAdminAccount', ], 'input' => [ 'shape' => 'DeregisterOrganizationAdminAccountRequest', ], 'output' => [ 'shape' => 'DeregisterOrganizationAdminAccountResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DisassociateAssessmentReportEvidenceFolder' => [ 'name' => 'DisassociateAssessmentReportEvidenceFolder', 'http' => [ 'method' => 'PUT', 'requestUri' => '/assessments/{assessmentId}/disassociateFromAssessmentReport', ], 'input' => [ 'shape' => 'DisassociateAssessmentReportEvidenceFolderRequest', ], 'output' => [ 'shape' => 'DisassociateAssessmentReportEvidenceFolderResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAccountStatus' => [ 'name' => 'GetAccountStatus', 'http' => [ 'method' => 'GET', 'requestUri' => '/account/status', ], 'input' => [ 'shape' => 'GetAccountStatusRequest', ], 'output' => [ 'shape' => 'GetAccountStatusResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], ], ], 'GetAssessment' => [ 'name' => 'GetAssessment', 'http' => [ 'method' => 'GET', 'requestUri' => '/assessments/{assessmentId}', ], 'input' => [ 'shape' => 'GetAssessmentRequest', ], 'output' => [ 'shape' => 'GetAssessmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetAssessmentFramework' => [ 'name' => 'GetAssessmentFramework', 'http' => [ 'method' => 'GET', 'requestUri' => '/assessmentFrameworks/{frameworkId}', ], 'input' => [ 'shape' => 'GetAssessmentFrameworkRequest', ], 'output' => [ 'shape' => 'GetAssessmentFrameworkResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetAssessmentReportUrl' => [ 'name' => 'GetAssessmentReportUrl', 'http' => [ 'method' => 'GET', 'requestUri' => '/assessments/{assessmentId}/reports/{assessmentReportId}/url', ], 'input' => [ 'shape' => 'GetAssessmentReportUrlRequest', ], 'output' => [ 'shape' => 'GetAssessmentReportUrlResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetChangeLogs' => [ 'name' => 'GetChangeLogs', 'http' => [ 'method' => 'GET', 'requestUri' => '/assessments/{assessmentId}/changelogs', ], 'input' => [ 'shape' => 'GetChangeLogsRequest', ], 'output' => [ 'shape' => 'GetChangeLogsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetControl' => [ 'name' => 'GetControl', 'http' => [ 'method' => 'GET', 'requestUri' => '/controls/{controlId}', ], 'input' => [ 'shape' => 'GetControlRequest', ], 'output' => [ 'shape' => 'GetControlResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetDelegations' => [ 'name' => 'GetDelegations', 'http' => [ 'method' => 'GET', 'requestUri' => '/delegations', ], 'input' => [ 'shape' => 'GetDelegationsRequest', ], 'output' => [ 'shape' => 'GetDelegationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetEvidence' => [ 'name' => 'GetEvidence', 'http' => [ 'method' => 'GET', 'requestUri' => '/assessments/{assessmentId}/controlSets/{controlSetId}/evidenceFolders/{evidenceFolderId}/evidence/{evidenceId}', ], 'input' => [ 'shape' => 'GetEvidenceRequest', ], 'output' => [ 'shape' => 'GetEvidenceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetEvidenceByEvidenceFolder' => [ 'name' => 'GetEvidenceByEvidenceFolder', 'http' => [ 'method' => 'GET', 'requestUri' => '/assessments/{assessmentId}/controlSets/{controlSetId}/evidenceFolders/{evidenceFolderId}/evidence', ], 'input' => [ 'shape' => 'GetEvidenceByEvidenceFolderRequest', ], 'output' => [ 'shape' => 'GetEvidenceByEvidenceFolderResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetEvidenceFileUploadUrl' => [ 'name' => 'GetEvidenceFileUploadUrl', 'http' => [ 'method' => 'GET', 'requestUri' => '/evidenceFileUploadUrl', ], 'input' => [ 'shape' => 'GetEvidenceFileUploadUrlRequest', ], 'output' => [ 'shape' => 'GetEvidenceFileUploadUrlResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetEvidenceFolder' => [ 'name' => 'GetEvidenceFolder', 'http' => [ 'method' => 'GET', 'requestUri' => '/assessments/{assessmentId}/controlSets/{controlSetId}/evidenceFolders/{evidenceFolderId}', ], 'input' => [ 'shape' => 'GetEvidenceFolderRequest', ], 'output' => [ 'shape' => 'GetEvidenceFolderResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetEvidenceFoldersByAssessment' => [ 'name' => 'GetEvidenceFoldersByAssessment', 'http' => [ 'method' => 'GET', 'requestUri' => '/assessments/{assessmentId}/evidenceFolders', ], 'input' => [ 'shape' => 'GetEvidenceFoldersByAssessmentRequest', ], 'output' => [ 'shape' => 'GetEvidenceFoldersByAssessmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetEvidenceFoldersByAssessmentControl' => [ 'name' => 'GetEvidenceFoldersByAssessmentControl', 'http' => [ 'method' => 'GET', 'requestUri' => '/assessments/{assessmentId}/evidenceFolders-by-assessment-control/{controlSetId}/{controlId}', ], 'input' => [ 'shape' => 'GetEvidenceFoldersByAssessmentControlRequest', ], 'output' => [ 'shape' => 'GetEvidenceFoldersByAssessmentControlResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetInsights' => [ 'name' => 'GetInsights', 'http' => [ 'method' => 'GET', 'requestUri' => '/insights', ], 'input' => [ 'shape' => 'GetInsightsRequest', ], 'output' => [ 'shape' => 'GetInsightsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetInsightsByAssessment' => [ 'name' => 'GetInsightsByAssessment', 'http' => [ 'method' => 'GET', 'requestUri' => '/insights/assessments/{assessmentId}', ], 'input' => [ 'shape' => 'GetInsightsByAssessmentRequest', ], 'output' => [ 'shape' => 'GetInsightsByAssessmentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetOrganizationAdminAccount' => [ 'name' => 'GetOrganizationAdminAccount', 'http' => [ 'method' => 'GET', 'requestUri' => '/account/organizationAdminAccount', ], 'input' => [ 'shape' => 'GetOrganizationAdminAccountRequest', ], 'output' => [ 'shape' => 'GetOrganizationAdminAccountResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetServicesInScope' => [ 'name' => 'GetServicesInScope', 'http' => [ 'method' => 'GET', 'requestUri' => '/services', ], 'input' => [ 'shape' => 'GetServicesInScopeRequest', ], 'output' => [ 'shape' => 'GetServicesInScopeResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetSettings' => [ 'name' => 'GetSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/settings/{attribute}', ], 'input' => [ 'shape' => 'GetSettingsRequest', ], 'output' => [ 'shape' => 'GetSettingsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListAssessmentControlInsightsByControlDomain' => [ 'name' => 'ListAssessmentControlInsightsByControlDomain', 'http' => [ 'method' => 'GET', 'requestUri' => '/insights/controls-by-assessment', ], 'input' => [ 'shape' => 'ListAssessmentControlInsightsByControlDomainRequest', ], 'output' => [ 'shape' => 'ListAssessmentControlInsightsByControlDomainResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListAssessmentFrameworkShareRequests' => [ 'name' => 'ListAssessmentFrameworkShareRequests', 'http' => [ 'method' => 'GET', 'requestUri' => '/assessmentFrameworkShareRequests', ], 'input' => [ 'shape' => 'ListAssessmentFrameworkShareRequestsRequest', ], 'output' => [ 'shape' => 'ListAssessmentFrameworkShareRequestsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListAssessmentFrameworks' => [ 'name' => 'ListAssessmentFrameworks', 'http' => [ 'method' => 'GET', 'requestUri' => '/assessmentFrameworks', ], 'input' => [ 'shape' => 'ListAssessmentFrameworksRequest', ], 'output' => [ 'shape' => 'ListAssessmentFrameworksResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListAssessmentReports' => [ 'name' => 'ListAssessmentReports', 'http' => [ 'method' => 'GET', 'requestUri' => '/assessmentReports', ], 'input' => [ 'shape' => 'ListAssessmentReportsRequest', ], 'output' => [ 'shape' => 'ListAssessmentReportsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListAssessments' => [ 'name' => 'ListAssessments', 'http' => [ 'method' => 'GET', 'requestUri' => '/assessments', ], 'input' => [ 'shape' => 'ListAssessmentsRequest', ], 'output' => [ 'shape' => 'ListAssessmentsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListControlDomainInsights' => [ 'name' => 'ListControlDomainInsights', 'http' => [ 'method' => 'GET', 'requestUri' => '/insights/control-domains', ], 'input' => [ 'shape' => 'ListControlDomainInsightsRequest', ], 'output' => [ 'shape' => 'ListControlDomainInsightsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListControlDomainInsightsByAssessment' => [ 'name' => 'ListControlDomainInsightsByAssessment', 'http' => [ 'method' => 'GET', 'requestUri' => '/insights/control-domains-by-assessment', ], 'input' => [ 'shape' => 'ListControlDomainInsightsByAssessmentRequest', ], 'output' => [ 'shape' => 'ListControlDomainInsightsByAssessmentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListControlInsightsByControlDomain' => [ 'name' => 'ListControlInsightsByControlDomain', 'http' => [ 'method' => 'GET', 'requestUri' => '/insights/controls', ], 'input' => [ 'shape' => 'ListControlInsightsByControlDomainRequest', ], 'output' => [ 'shape' => 'ListControlInsightsByControlDomainResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListControls' => [ 'name' => 'ListControls', 'http' => [ 'method' => 'GET', 'requestUri' => '/controls', ], 'input' => [ 'shape' => 'ListControlsRequest', ], 'output' => [ 'shape' => 'ListControlsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListKeywordsForDataSource' => [ 'name' => 'ListKeywordsForDataSource', 'http' => [ 'method' => 'GET', 'requestUri' => '/dataSourceKeywords', ], 'input' => [ 'shape' => 'ListKeywordsForDataSourceRequest', ], 'output' => [ 'shape' => 'ListKeywordsForDataSourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListNotifications' => [ 'name' => 'ListNotifications', 'http' => [ 'method' => 'GET', 'requestUri' => '/notifications', ], 'input' => [ 'shape' => 'ListNotificationsRequest', ], 'output' => [ 'shape' => 'ListNotificationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'RegisterAccount' => [ 'name' => 'RegisterAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/account/registerAccount', ], 'input' => [ 'shape' => 'RegisterAccountRequest', ], 'output' => [ 'shape' => 'RegisterAccountResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'RegisterOrganizationAdminAccount' => [ 'name' => 'RegisterOrganizationAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/account/registerOrganizationAdminAccount', ], 'input' => [ 'shape' => 'RegisterOrganizationAdminAccountRequest', ], 'output' => [ 'shape' => 'RegisterOrganizationAdminAccountResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartAssessmentFrameworkShare' => [ 'name' => 'StartAssessmentFrameworkShare', 'http' => [ 'method' => 'POST', 'requestUri' => '/assessmentFrameworks/{frameworkId}/shareRequests', ], 'input' => [ 'shape' => 'StartAssessmentFrameworkShareRequest', ], 'output' => [ 'shape' => 'StartAssessmentFrameworkShareResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateAssessment' => [ 'name' => 'UpdateAssessment', 'http' => [ 'method' => 'PUT', 'requestUri' => '/assessments/{assessmentId}', ], 'input' => [ 'shape' => 'UpdateAssessmentRequest', ], 'output' => [ 'shape' => 'UpdateAssessmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateAssessmentControl' => [ 'name' => 'UpdateAssessmentControl', 'http' => [ 'method' => 'PUT', 'requestUri' => '/assessments/{assessmentId}/controlSets/{controlSetId}/controls/{controlId}', ], 'input' => [ 'shape' => 'UpdateAssessmentControlRequest', ], 'output' => [ 'shape' => 'UpdateAssessmentControlResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateAssessmentControlSetStatus' => [ 'name' => 'UpdateAssessmentControlSetStatus', 'http' => [ 'method' => 'PUT', 'requestUri' => '/assessments/{assessmentId}/controlSets/{controlSetId}/status', ], 'input' => [ 'shape' => 'UpdateAssessmentControlSetStatusRequest', ], 'output' => [ 'shape' => 'UpdateAssessmentControlSetStatusResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateAssessmentFramework' => [ 'name' => 'UpdateAssessmentFramework', 'http' => [ 'method' => 'PUT', 'requestUri' => '/assessmentFrameworks/{frameworkId}', ], 'input' => [ 'shape' => 'UpdateAssessmentFrameworkRequest', ], 'output' => [ 'shape' => 'UpdateAssessmentFrameworkResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateAssessmentFrameworkShare' => [ 'name' => 'UpdateAssessmentFrameworkShare', 'http' => [ 'method' => 'PUT', 'requestUri' => '/assessmentFrameworkShareRequests/{requestId}', ], 'input' => [ 'shape' => 'UpdateAssessmentFrameworkShareRequest', ], 'output' => [ 'shape' => 'UpdateAssessmentFrameworkShareResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'UpdateAssessmentStatus' => [ 'name' => 'UpdateAssessmentStatus', 'http' => [ 'method' => 'PUT', 'requestUri' => '/assessments/{assessmentId}/status', ], 'input' => [ 'shape' => 'UpdateAssessmentStatusRequest', ], 'output' => [ 'shape' => 'UpdateAssessmentStatusResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'UpdateControl' => [ 'name' => 'UpdateControl', 'http' => [ 'method' => 'PUT', 'requestUri' => '/controls/{controlId}', ], 'input' => [ 'shape' => 'UpdateControlRequest', ], 'output' => [ 'shape' => 'UpdateControlResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateSettings' => [ 'name' => 'UpdateSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/settings', ], 'input' => [ 'shape' => 'UpdateSettingsRequest', ], 'output' => [ 'shape' => 'UpdateSettingsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ValidateAssessmentReportIntegrity' => [ 'name' => 'ValidateAssessmentReportIntegrity', 'http' => [ 'method' => 'POST', 'requestUri' => '/assessmentReports/integrity', ], 'input' => [ 'shape' => 'ValidateAssessmentReportIntegrityRequest', ], 'output' => [ 'shape' => 'ValidateAssessmentReportIntegrityResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], ], 'shapes' => [ 'AWSAccount' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'AccountId', ], 'emailAddress' => [ 'shape' => 'EmailAddress', ], 'name' => [ 'shape' => 'AccountName', ], ], ], 'AWSAccounts' => [ 'type' => 'list', 'member' => [ 'shape' => 'AWSAccount', ], 'max' => 200, 'min' => 1, 'sensitive' => true, ], 'AWSService' => [ 'type' => 'structure', 'members' => [ 'serviceName' => [ 'shape' => 'AWSServiceName', ], ], ], 'AWSServiceName' => [ 'type' => 'string', 'max' => 40, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-\\s().]+$', ], 'AWSServices' => [ 'type' => 'list', 'member' => [ 'shape' => 'AWSService', ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^[0-9]{12}$', ], 'AccountName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '^[\\u0020-\\u007E]+$', ], 'AccountStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', 'PENDING_ACTIVATION', ], ], 'ActionEnum' => [ 'type' => 'string', 'enum' => [ 'CREATE', 'UPDATE_METADATA', 'ACTIVE', 'INACTIVE', 'DELETE', 'UNDER_REVIEW', 'REVIEWED', 'IMPORT_EVIDENCE', ], ], 'ActionPlanInstructions' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '^[\\w\\W\\s\\S]*$', 'sensitive' => true, ], 'ActionPlanTitle' => [ 'type' => 'string', 'max' => 300, 'pattern' => '^[\\w\\W\\s\\S]*$', 'sensitive' => true, ], 'Assessment' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AuditManagerArn', ], 'awsAccount' => [ 'shape' => 'AWSAccount', ], 'metadata' => [ 'shape' => 'AssessmentMetadata', ], 'framework' => [ 'shape' => 'AssessmentFramework', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'AssessmentControl' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'name' => [ 'shape' => 'ControlName', ], 'description' => [ 'shape' => 'ControlDescription', ], 'status' => [ 'shape' => 'ControlStatus', ], 'response' => [ 'shape' => 'ControlResponse', ], 'comments' => [ 'shape' => 'ControlComments', ], 'evidenceSources' => [ 'shape' => 'EvidenceSources', ], 'evidenceCount' => [ 'shape' => 'Integer', ], 'assessmentReportEvidenceCount' => [ 'shape' => 'Integer', ], ], ], 'AssessmentControlSet' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ControlSetId', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'status' => [ 'shape' => 'ControlSetStatus', ], 'roles' => [ 'shape' => 'Roles', ], 'controls' => [ 'shape' => 'AssessmentControls', ], 'delegations' => [ 'shape' => 'Delegations', ], 'systemEvidenceCount' => [ 'shape' => 'Integer', ], 'manualEvidenceCount' => [ 'shape' => 'Integer', ], ], ], 'AssessmentControlSets' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentControlSet', ], ], 'AssessmentControls' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentControl', ], ], 'AssessmentDescription' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '^[\\w\\W\\s\\S]*$', 'sensitive' => true, ], 'AssessmentEvidenceFolder' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AssessmentEvidenceFolderName', ], 'date' => [ 'shape' => 'Timestamp', ], 'assessmentId' => [ 'shape' => 'UUID', ], 'controlSetId' => [ 'shape' => 'ControlSetId', ], 'controlId' => [ 'shape' => 'UUID', ], 'id' => [ 'shape' => 'UUID', ], 'dataSource' => [ 'shape' => 'String', ], 'author' => [ 'shape' => 'String', ], 'totalEvidence' => [ 'shape' => 'Integer', ], 'assessmentReportSelectionCount' => [ 'shape' => 'Integer', ], 'controlName' => [ 'shape' => 'ControlName', ], 'evidenceResourcesIncludedCount' => [ 'shape' => 'Integer', ], 'evidenceByTypeConfigurationDataCount' => [ 'shape' => 'Integer', ], 'evidenceByTypeManualCount' => [ 'shape' => 'Integer', ], 'evidenceByTypeComplianceCheckCount' => [ 'shape' => 'Integer', ], 'evidenceByTypeComplianceCheckIssuesCount' => [ 'shape' => 'Integer', ], 'evidenceByTypeUserActivityCount' => [ 'shape' => 'Integer', ], 'evidenceAwsServiceSourceCount' => [ 'shape' => 'Integer', ], ], ], 'AssessmentEvidenceFolderName' => [ 'type' => 'string', 'max' => 300, 'min' => 1, 'pattern' => '^[\\w\\W\\s\\S]*$', ], 'AssessmentEvidenceFolders' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentEvidenceFolder', ], ], 'AssessmentFramework' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'arn' => [ 'shape' => 'AuditManagerArn', ], 'metadata' => [ 'shape' => 'FrameworkMetadata', ], 'controlSets' => [ 'shape' => 'AssessmentControlSets', ], ], 'sensitive' => true, ], 'AssessmentFrameworkDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[\\w\\W\\s\\S]*$', ], 'AssessmentFrameworkMetadata' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AuditManagerArn', ], 'id' => [ 'shape' => 'UUID', ], 'type' => [ 'shape' => 'FrameworkType', ], 'name' => [ 'shape' => 'FrameworkName', ], 'description' => [ 'shape' => 'FrameworkDescription', ], 'logo' => [ 'shape' => 'Filename', ], 'complianceType' => [ 'shape' => 'ComplianceType', ], 'controlsCount' => [ 'shape' => 'ControlsCount', ], 'controlSetsCount' => [ 'shape' => 'ControlSetsCount', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'AssessmentFrameworkShareRequest' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'frameworkId' => [ 'shape' => 'UUID', ], 'frameworkName' => [ 'shape' => 'FrameworkName', ], 'frameworkDescription' => [ 'shape' => 'FrameworkDescription', ], 'status' => [ 'shape' => 'ShareRequestStatus', ], 'sourceAccount' => [ 'shape' => 'AccountId', ], 'destinationAccount' => [ 'shape' => 'AccountId', ], 'destinationRegion' => [ 'shape' => 'Region', ], 'expirationTime' => [ 'shape' => 'Timestamp', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastUpdated' => [ 'shape' => 'Timestamp', ], 'comment' => [ 'shape' => 'ShareRequestComment', ], 'standardControlsCount' => [ 'shape' => 'NullableInteger', ], 'customControlsCount' => [ 'shape' => 'NullableInteger', ], 'complianceType' => [ 'shape' => 'ComplianceType', ], ], ], 'AssessmentFrameworkShareRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentFrameworkShareRequest', ], ], 'AssessmentMetadata' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AssessmentName', ], 'id' => [ 'shape' => 'UUID', ], 'description' => [ 'shape' => 'AssessmentDescription', ], 'complianceType' => [ 'shape' => 'ComplianceType', ], 'status' => [ 'shape' => 'AssessmentStatus', ], 'assessmentReportsDestination' => [ 'shape' => 'AssessmentReportsDestination', ], 'scope' => [ 'shape' => 'Scope', ], 'roles' => [ 'shape' => 'Roles', ], 'delegations' => [ 'shape' => 'Delegations', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastUpdated' => [ 'shape' => 'Timestamp', ], ], ], 'AssessmentMetadataItem' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AssessmentName', ], 'id' => [ 'shape' => 'UUID', ], 'complianceType' => [ 'shape' => 'ComplianceType', ], 'status' => [ 'shape' => 'AssessmentStatus', ], 'roles' => [ 'shape' => 'Roles', ], 'delegations' => [ 'shape' => 'Delegations', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastUpdated' => [ 'shape' => 'Timestamp', ], ], ], 'AssessmentName' => [ 'type' => 'string', 'max' => 300, 'min' => 1, 'pattern' => '^[^\\\\]*$', 'sensitive' => true, ], 'AssessmentReport' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'name' => [ 'shape' => 'AssessmentReportName', ], 'description' => [ 'shape' => 'AssessmentReportDescription', ], 'awsAccountId' => [ 'shape' => 'AccountId', ], 'assessmentId' => [ 'shape' => 'UUID', ], 'assessmentName' => [ 'shape' => 'AssessmentName', ], 'author' => [ 'shape' => 'Username', ], 'status' => [ 'shape' => 'AssessmentReportStatus', ], 'creationTime' => [ 'shape' => 'Timestamp', ], ], ], 'AssessmentReportDescription' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '^[\\w\\W\\s\\S]*$', 'sensitive' => true, ], 'AssessmentReportDestinationType' => [ 'type' => 'string', 'enum' => [ 'S3', ], ], 'AssessmentReportEvidenceError' => [ 'type' => 'structure', 'members' => [ 'evidenceId' => [ 'shape' => 'UUID', ], 'errorCode' => [ 'shape' => 'ErrorCode', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'AssessmentReportEvidenceErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentReportEvidenceError', ], ], 'AssessmentReportMetadata' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'name' => [ 'shape' => 'AssessmentReportName', ], 'description' => [ 'shape' => 'AssessmentReportDescription', ], 'assessmentId' => [ 'shape' => 'UUID', ], 'assessmentName' => [ 'shape' => 'AssessmentName', ], 'author' => [ 'shape' => 'Username', ], 'status' => [ 'shape' => 'AssessmentReportStatus', ], 'creationTime' => [ 'shape' => 'Timestamp', ], ], ], 'AssessmentReportName' => [ 'type' => 'string', 'max' => 300, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-_\\.]+$', ], 'AssessmentReportStatus' => [ 'type' => 'string', 'enum' => [ 'COMPLETE', 'IN_PROGRESS', 'FAILED', ], ], 'AssessmentReportsDestination' => [ 'type' => 'structure', 'members' => [ 'destinationType' => [ 'shape' => 'AssessmentReportDestinationType', ], 'destination' => [ 'shape' => 'S3Url', ], ], 'sensitive' => true, ], 'AssessmentReportsMetadata' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentReportMetadata', ], ], 'AssessmentStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'AssociateAssessmentReportEvidenceFolderRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', 'evidenceFolderId', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], 'evidenceFolderId' => [ 'shape' => 'UUID', ], ], ], 'AssociateAssessmentReportEvidenceFolderResponse' => [ 'type' => 'structure', 'members' => [], ], 'AuditManagerArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:.*:auditmanager:.*', ], 'BatchAssociateAssessmentReportEvidenceRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', 'evidenceFolderId', 'evidenceIds', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], 'evidenceFolderId' => [ 'shape' => 'UUID', ], 'evidenceIds' => [ 'shape' => 'EvidenceIds', ], ], ], 'BatchAssociateAssessmentReportEvidenceResponse' => [ 'type' => 'structure', 'members' => [ 'evidenceIds' => [ 'shape' => 'EvidenceIds', ], 'errors' => [ 'shape' => 'AssessmentReportEvidenceErrors', ], ], ], 'BatchCreateDelegationByAssessmentError' => [ 'type' => 'structure', 'members' => [ 'createDelegationRequest' => [ 'shape' => 'CreateDelegationRequest', ], 'errorCode' => [ 'shape' => 'ErrorCode', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'BatchCreateDelegationByAssessmentErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchCreateDelegationByAssessmentError', ], 'sensitive' => true, ], 'BatchCreateDelegationByAssessmentRequest' => [ 'type' => 'structure', 'required' => [ 'createDelegationRequests', 'assessmentId', ], 'members' => [ 'createDelegationRequests' => [ 'shape' => 'CreateDelegationRequests', ], 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], ], ], 'BatchCreateDelegationByAssessmentResponse' => [ 'type' => 'structure', 'members' => [ 'delegations' => [ 'shape' => 'Delegations', ], 'errors' => [ 'shape' => 'BatchCreateDelegationByAssessmentErrors', ], ], ], 'BatchDeleteDelegationByAssessmentError' => [ 'type' => 'structure', 'members' => [ 'delegationId' => [ 'shape' => 'UUID', ], 'errorCode' => [ 'shape' => 'ErrorCode', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'BatchDeleteDelegationByAssessmentErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDeleteDelegationByAssessmentError', ], 'sensitive' => true, ], 'BatchDeleteDelegationByAssessmentRequest' => [ 'type' => 'structure', 'required' => [ 'delegationIds', 'assessmentId', ], 'members' => [ 'delegationIds' => [ 'shape' => 'DelegationIds', ], 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], ], ], 'BatchDeleteDelegationByAssessmentResponse' => [ 'type' => 'structure', 'members' => [ 'errors' => [ 'shape' => 'BatchDeleteDelegationByAssessmentErrors', ], ], ], 'BatchDisassociateAssessmentReportEvidenceRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', 'evidenceFolderId', 'evidenceIds', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], 'evidenceFolderId' => [ 'shape' => 'UUID', ], 'evidenceIds' => [ 'shape' => 'EvidenceIds', ], ], ], 'BatchDisassociateAssessmentReportEvidenceResponse' => [ 'type' => 'structure', 'members' => [ 'evidenceIds' => [ 'shape' => 'EvidenceIds', ], 'errors' => [ 'shape' => 'AssessmentReportEvidenceErrors', ], ], ], 'BatchImportEvidenceToAssessmentControlError' => [ 'type' => 'structure', 'members' => [ 'manualEvidence' => [ 'shape' => 'ManualEvidence', ], 'errorCode' => [ 'shape' => 'ErrorCode', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'BatchImportEvidenceToAssessmentControlErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchImportEvidenceToAssessmentControlError', ], ], 'BatchImportEvidenceToAssessmentControlRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', 'controlSetId', 'controlId', 'manualEvidence', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], 'controlSetId' => [ 'shape' => 'ControlSetId', 'location' => 'uri', 'locationName' => 'controlSetId', ], 'controlId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'controlId', ], 'manualEvidence' => [ 'shape' => 'ManualEvidenceList', ], ], ], 'BatchImportEvidenceToAssessmentControlResponse' => [ 'type' => 'structure', 'members' => [ 'errors' => [ 'shape' => 'BatchImportEvidenceToAssessmentControlErrors', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'ChangeLog' => [ 'type' => 'structure', 'members' => [ 'objectType' => [ 'shape' => 'ObjectTypeEnum', ], 'objectName' => [ 'shape' => 'NonEmptyString', ], 'action' => [ 'shape' => 'ActionEnum', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'createdBy' => [ 'shape' => 'IamArn', ], ], ], 'ChangeLogs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChangeLog', ], ], 'CloudTrailArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:.*:cloudtrail:.*', ], 'ComplianceType' => [ 'type' => 'string', 'max' => 100, 'pattern' => '^[\\w\\W\\s\\S]*$', 'sensitive' => true, ], 'Control' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AuditManagerArn', ], 'id' => [ 'shape' => 'UUID', ], 'type' => [ 'shape' => 'ControlType', ], 'name' => [ 'shape' => 'ControlName', ], 'description' => [ 'shape' => 'ControlDescription', ], 'testingInformation' => [ 'shape' => 'TestingInformation', ], 'actionPlanTitle' => [ 'shape' => 'ActionPlanTitle', ], 'actionPlanInstructions' => [ 'shape' => 'ActionPlanInstructions', ], 'controlSources' => [ 'shape' => 'ControlSources', ], 'controlMappingSources' => [ 'shape' => 'ControlMappingSources', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'lastUpdatedBy' => [ 'shape' => 'LastUpdatedBy', ], 'tags' => [ 'shape' => 'TagMap', ], 'state' => [ 'shape' => 'ControlState', ], ], ], 'ControlCatalogId' => [ 'type' => 'string', 'max' => 2048, 'min' => 13, 'pattern' => '^arn:.*:controlcatalog:.*|UNCATEGORIZED', ], 'ControlComment' => [ 'type' => 'structure', 'members' => [ 'authorName' => [ 'shape' => 'Username', ], 'commentBody' => [ 'shape' => 'ControlCommentBody', ], 'postedDate' => [ 'shape' => 'Timestamp', ], ], ], 'ControlCommentBody' => [ 'type' => 'string', 'max' => 500, 'pattern' => '^[\\w\\W\\s\\S]*$', 'sensitive' => true, ], 'ControlComments' => [ 'type' => 'list', 'member' => [ 'shape' => 'ControlComment', ], ], 'ControlDescription' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '^[\\w\\W\\s\\S]*$', 'sensitive' => true, ], 'ControlDomainId' => [ 'type' => 'string', 'max' => 2048, 'min' => 13, 'pattern' => '^arn:.*:controlcatalog:.*:.*:domain/.*|UNCATEGORIZED|^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'ControlDomainInsights' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'ControlDomainId', ], 'controlsCountByNoncompliantEvidence' => [ 'shape' => 'NullableInteger', ], 'totalControlsCount' => [ 'shape' => 'NullableInteger', ], 'evidenceInsights' => [ 'shape' => 'EvidenceInsights', ], 'lastUpdated' => [ 'shape' => 'Timestamp', ], ], ], 'ControlDomainInsightsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ControlDomainInsights', ], ], 'ControlInsightsMetadata' => [ 'type' => 'list', 'member' => [ 'shape' => 'ControlInsightsMetadataItem', ], ], 'ControlInsightsMetadataByAssessment' => [ 'type' => 'list', 'member' => [ 'shape' => 'ControlInsightsMetadataByAssessmentItem', ], ], 'ControlInsightsMetadataByAssessmentItem' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'ControlDomainId', ], 'evidenceInsights' => [ 'shape' => 'EvidenceInsights', ], 'controlSetName' => [ 'shape' => 'NonEmptyString', ], 'lastUpdated' => [ 'shape' => 'Timestamp', ], ], ], 'ControlInsightsMetadataItem' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'ControlDomainId', ], 'evidenceInsights' => [ 'shape' => 'EvidenceInsights', ], 'lastUpdated' => [ 'shape' => 'Timestamp', ], ], ], 'ControlMappingSource' => [ 'type' => 'structure', 'members' => [ 'sourceId' => [ 'shape' => 'UUID', ], 'sourceName' => [ 'shape' => 'SourceName', ], 'sourceDescription' => [ 'shape' => 'SourceDescription', ], 'sourceSetUpOption' => [ 'shape' => 'SourceSetUpOption', ], 'sourceType' => [ 'shape' => 'SourceType', ], 'sourceKeyword' => [ 'shape' => 'SourceKeyword', ], 'sourceFrequency' => [ 'shape' => 'SourceFrequency', ], 'troubleshootingText' => [ 'shape' => 'TroubleshootingText', ], ], ], 'ControlMappingSources' => [ 'type' => 'list', 'member' => [ 'shape' => 'ControlMappingSource', ], 'min' => 1, ], 'ControlMetadata' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AuditManagerArn', ], 'id' => [ 'shape' => 'UUID', ], 'name' => [ 'shape' => 'ControlName', ], 'controlSources' => [ 'shape' => 'ControlSources', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'ControlMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ControlMetadata', ], ], 'ControlName' => [ 'type' => 'string', 'max' => 300, 'min' => 1, 'pattern' => '^[^\\\\]*$', ], 'ControlResponse' => [ 'type' => 'string', 'enum' => [ 'MANUAL', 'AUTOMATE', 'DEFER', 'IGNORE', ], ], 'ControlSet' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'name' => [ 'shape' => 'ControlSetName', ], 'controls' => [ 'shape' => 'Controls', ], ], ], 'ControlSetId' => [ 'type' => 'string', 'max' => 300, 'min' => 1, 'pattern' => '^[\\w\\W\\s\\S]*$', ], 'ControlSetName' => [ 'type' => 'string', 'max' => 300, 'min' => 1, 'pattern' => '^[^\\\\\\_]*$', ], 'ControlSetStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'UNDER_REVIEW', 'REVIEWED', ], ], 'ControlSets' => [ 'type' => 'list', 'member' => [ 'shape' => 'ControlSet', ], 'min' => 1, 'sensitive' => true, ], 'ControlSetsCount' => [ 'type' => 'integer', ], 'ControlSources' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z_0-9-\\s.,]+$', ], 'ControlState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'END_OF_SUPPORT', ], ], 'ControlStatus' => [ 'type' => 'string', 'enum' => [ 'UNDER_REVIEW', 'REVIEWED', 'INACTIVE', ], ], 'ControlType' => [ 'type' => 'string', 'enum' => [ 'Standard', 'Custom', 'Core', ], ], 'Controls' => [ 'type' => 'list', 'member' => [ 'shape' => 'Control', ], 'min' => 1, ], 'ControlsCount' => [ 'type' => 'integer', ], 'CreateAssessmentFrameworkControl' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'UUID', ], ], ], 'CreateAssessmentFrameworkControlSet' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'ControlSetName', ], 'controls' => [ 'shape' => 'CreateAssessmentFrameworkControls', ], ], ], 'CreateAssessmentFrameworkControlSets' => [ 'type' => 'list', 'member' => [ 'shape' => 'CreateAssessmentFrameworkControlSet', ], 'min' => 1, ], 'CreateAssessmentFrameworkControls' => [ 'type' => 'list', 'member' => [ 'shape' => 'CreateAssessmentFrameworkControl', ], 'min' => 1, ], 'CreateAssessmentFrameworkRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'controlSets', ], 'members' => [ 'name' => [ 'shape' => 'FrameworkName', ], 'description' => [ 'shape' => 'FrameworkDescription', ], 'complianceType' => [ 'shape' => 'ComplianceType', ], 'controlSets' => [ 'shape' => 'CreateAssessmentFrameworkControlSets', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateAssessmentFrameworkResponse' => [ 'type' => 'structure', 'members' => [ 'framework' => [ 'shape' => 'Framework', ], ], ], 'CreateAssessmentReportRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'assessmentId', ], 'members' => [ 'name' => [ 'shape' => 'AssessmentReportName', ], 'description' => [ 'shape' => 'AssessmentReportDescription', ], 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], 'queryStatement' => [ 'shape' => 'QueryStatement', ], ], ], 'CreateAssessmentReportResponse' => [ 'type' => 'structure', 'members' => [ 'assessmentReport' => [ 'shape' => 'AssessmentReport', ], ], ], 'CreateAssessmentRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'assessmentReportsDestination', 'scope', 'roles', 'frameworkId', ], 'members' => [ 'name' => [ 'shape' => 'AssessmentName', ], 'description' => [ 'shape' => 'AssessmentDescription', ], 'assessmentReportsDestination' => [ 'shape' => 'AssessmentReportsDestination', ], 'scope' => [ 'shape' => 'Scope', ], 'roles' => [ 'shape' => 'Roles', ], 'frameworkId' => [ 'shape' => 'UUID', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateAssessmentResponse' => [ 'type' => 'structure', 'members' => [ 'assessment' => [ 'shape' => 'Assessment', ], ], ], 'CreateControlMappingSource' => [ 'type' => 'structure', 'members' => [ 'sourceName' => [ 'shape' => 'SourceName', ], 'sourceDescription' => [ 'shape' => 'SourceDescription', ], 'sourceSetUpOption' => [ 'shape' => 'SourceSetUpOption', ], 'sourceType' => [ 'shape' => 'SourceType', ], 'sourceKeyword' => [ 'shape' => 'SourceKeyword', ], 'sourceFrequency' => [ 'shape' => 'SourceFrequency', ], 'troubleshootingText' => [ 'shape' => 'TroubleshootingText', ], ], ], 'CreateControlMappingSources' => [ 'type' => 'list', 'member' => [ 'shape' => 'CreateControlMappingSource', ], 'min' => 1, ], 'CreateControlRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'controlMappingSources', ], 'members' => [ 'name' => [ 'shape' => 'ControlName', ], 'description' => [ 'shape' => 'ControlDescription', ], 'testingInformation' => [ 'shape' => 'TestingInformation', ], 'actionPlanTitle' => [ 'shape' => 'ActionPlanTitle', ], 'actionPlanInstructions' => [ 'shape' => 'ActionPlanInstructions', ], 'controlMappingSources' => [ 'shape' => 'CreateControlMappingSources', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateControlResponse' => [ 'type' => 'structure', 'members' => [ 'control' => [ 'shape' => 'Control', ], ], ], 'CreateDelegationRequest' => [ 'type' => 'structure', 'members' => [ 'comment' => [ 'shape' => 'DelegationComment', ], 'controlSetId' => [ 'shape' => 'ControlSetId', ], 'roleArn' => [ 'shape' => 'IamArn', ], 'roleType' => [ 'shape' => 'RoleType', ], ], ], 'CreateDelegationRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'CreateDelegationRequest', ], 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'CreatedBy' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\s-_()\\[\\]]+$', 'sensitive' => true, ], 'DataSourceType' => [ 'type' => 'string', 'enum' => [ 'AWS_Cloudtrail', 'AWS_Config', 'AWS_Security_Hub', 'AWS_API_Call', 'MANUAL', ], ], 'DefaultExportDestination' => [ 'type' => 'structure', 'members' => [ 'destinationType' => [ 'shape' => 'ExportDestinationType', ], 'destination' => [ 'shape' => 'S3Url', ], ], ], 'Delegation' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'assessmentName' => [ 'shape' => 'AssessmentName', ], 'assessmentId' => [ 'shape' => 'UUID', ], 'status' => [ 'shape' => 'DelegationStatus', ], 'roleArn' => [ 'shape' => 'IamArn', ], 'roleType' => [ 'shape' => 'RoleType', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastUpdated' => [ 'shape' => 'Timestamp', ], 'controlSetId' => [ 'shape' => 'ControlSetId', ], 'comment' => [ 'shape' => 'DelegationComment', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], ], 'sensitive' => true, ], 'DelegationComment' => [ 'type' => 'string', 'max' => 350, 'pattern' => '^[\\w\\W\\s\\S]*$', 'sensitive' => true, ], 'DelegationIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'UUID', ], 'max' => 50, 'min' => 1, ], 'DelegationMetadata' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'assessmentName' => [ 'shape' => 'AssessmentName', ], 'assessmentId' => [ 'shape' => 'UUID', ], 'status' => [ 'shape' => 'DelegationStatus', ], 'roleArn' => [ 'shape' => 'IamArn', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'controlSetName' => [ 'shape' => 'NonEmptyString', ], ], ], 'DelegationMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DelegationMetadata', ], ], 'DelegationStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'UNDER_REVIEW', 'COMPLETE', ], ], 'Delegations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Delegation', ], ], 'DeleteAssessmentFrameworkRequest' => [ 'type' => 'structure', 'required' => [ 'frameworkId', ], 'members' => [ 'frameworkId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'frameworkId', ], ], ], 'DeleteAssessmentFrameworkResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAssessmentFrameworkShareRequest' => [ 'type' => 'structure', 'required' => [ 'requestId', 'requestType', ], 'members' => [ 'requestId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'requestId', ], 'requestType' => [ 'shape' => 'ShareRequestType', 'location' => 'querystring', 'locationName' => 'requestType', ], ], ], 'DeleteAssessmentFrameworkShareResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAssessmentReportRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', 'assessmentReportId', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], 'assessmentReportId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentReportId', ], ], ], 'DeleteAssessmentReportResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAssessmentRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], ], ], 'DeleteAssessmentResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteControlRequest' => [ 'type' => 'structure', 'required' => [ 'controlId', ], 'members' => [ 'controlId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'controlId', ], ], ], 'DeleteControlResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteResources' => [ 'type' => 'string', 'enum' => [ 'ALL', 'DEFAULT', ], ], 'DeregisterAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'DeregisterAccountResponse' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'AccountStatus', ], ], ], 'DeregisterOrganizationAdminAccountRequest' => [ 'type' => 'structure', 'members' => [ 'adminAccountId' => [ 'shape' => 'AccountId', ], ], ], 'DeregisterOrganizationAdminAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeregistrationPolicy' => [ 'type' => 'structure', 'members' => [ 'deleteResources' => [ 'shape' => 'DeleteResources', ], ], ], 'DisassociateAssessmentReportEvidenceFolderRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', 'evidenceFolderId', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], 'evidenceFolderId' => [ 'shape' => 'UUID', ], ], ], 'DisassociateAssessmentReportEvidenceFolderResponse' => [ 'type' => 'structure', 'members' => [], ], 'EmailAddress' => [ 'type' => 'string', 'max' => 320, 'min' => 1, 'pattern' => '^.*@.*$', 'sensitive' => true, ], 'ErrorCode' => [ 'type' => 'string', 'max' => 3, 'min' => 3, 'pattern' => '[0-9]{3}', ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 300, 'pattern' => '^[\\w\\W\\s\\S]*$', ], 'EventName' => [ 'type' => 'string', 'max' => 100, 'pattern' => '^[\\w\\W\\s\\S]*$', ], 'Evidence' => [ 'type' => 'structure', 'members' => [ 'dataSource' => [ 'shape' => 'String', ], 'evidenceAwsAccountId' => [ 'shape' => 'AccountId', ], 'time' => [ 'shape' => 'Timestamp', ], 'eventSource' => [ 'shape' => 'AWSServiceName', ], 'eventName' => [ 'shape' => 'EventName', ], 'evidenceByType' => [ 'shape' => 'String', ], 'resourcesIncluded' => [ 'shape' => 'Resources', ], 'attributes' => [ 'shape' => 'EvidenceAttributes', ], 'iamId' => [ 'shape' => 'IamArn', ], 'complianceCheck' => [ 'shape' => 'String', ], 'awsOrganization' => [ 'shape' => 'String', ], 'awsAccountId' => [ 'shape' => 'AccountId', ], 'evidenceFolderId' => [ 'shape' => 'UUID', ], 'id' => [ 'shape' => 'UUID', ], 'assessmentReportSelection' => [ 'shape' => 'String', ], ], ], 'EvidenceAttributeKey' => [ 'type' => 'string', 'max' => 100, 'pattern' => '^[\\w\\W\\s\\S]*$', ], 'EvidenceAttributeValue' => [ 'type' => 'string', 'max' => 200, 'pattern' => '^[\\w\\W\\s\\S]*$', ], 'EvidenceAttributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'EvidenceAttributeKey', ], 'value' => [ 'shape' => 'EvidenceAttributeValue', ], ], 'EvidenceFinderBackfillStatus' => [ 'type' => 'string', 'enum' => [ 'NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', ], ], 'EvidenceFinderEnablement' => [ 'type' => 'structure', 'members' => [ 'eventDataStoreArn' => [ 'shape' => 'CloudTrailArn', ], 'enablementStatus' => [ 'shape' => 'EvidenceFinderEnablementStatus', ], 'backfillStatus' => [ 'shape' => 'EvidenceFinderBackfillStatus', ], 'error' => [ 'shape' => 'ErrorMessage', ], ], ], 'EvidenceFinderEnablementStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', 'ENABLE_IN_PROGRESS', 'DISABLE_IN_PROGRESS', ], ], 'EvidenceIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'UUID', ], 'max' => 50, 'min' => 0, ], 'EvidenceInsights' => [ 'type' => 'structure', 'members' => [ 'noncompliantEvidenceCount' => [ 'shape' => 'NullableInteger', ], 'compliantEvidenceCount' => [ 'shape' => 'NullableInteger', ], 'inconclusiveEvidenceCount' => [ 'shape' => 'NullableInteger', ], ], ], 'EvidenceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Evidence', ], ], 'EvidenceSources' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'ExportDestinationType' => [ 'type' => 'string', 'enum' => [ 'S3', ], ], 'Filename' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[\\w,\\s-]+\\.[A-Za-z]+$', ], 'Framework' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AuditManagerArn', ], 'id' => [ 'shape' => 'UUID', ], 'name' => [ 'shape' => 'FrameworkName', ], 'type' => [ 'shape' => 'FrameworkType', ], 'complianceType' => [ 'shape' => 'ComplianceType', ], 'description' => [ 'shape' => 'FrameworkDescription', ], 'logo' => [ 'shape' => 'Filename', ], 'controlSources' => [ 'shape' => 'ControlSources', ], 'controlSets' => [ 'shape' => 'ControlSets', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'lastUpdatedBy' => [ 'shape' => 'LastUpdatedBy', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'FrameworkDescription' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^[\\w\\W\\s\\S]*$', ], 'FrameworkMetadata' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AssessmentName', ], 'description' => [ 'shape' => 'AssessmentFrameworkDescription', ], 'logo' => [ 'shape' => 'Filename', ], 'complianceType' => [ 'shape' => 'ComplianceType', ], ], ], 'FrameworkMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentFrameworkMetadata', ], ], 'FrameworkName' => [ 'type' => 'string', 'max' => 300, 'min' => 1, 'pattern' => '^[^\\\\]*$', ], 'FrameworkType' => [ 'type' => 'string', 'enum' => [ 'Standard', 'Custom', ], ], 'GenericArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:.*', ], 'GetAccountStatusRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetAccountStatusResponse' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'AccountStatus', ], ], ], 'GetAssessmentFrameworkRequest' => [ 'type' => 'structure', 'required' => [ 'frameworkId', ], 'members' => [ 'frameworkId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'frameworkId', ], ], ], 'GetAssessmentFrameworkResponse' => [ 'type' => 'structure', 'members' => [ 'framework' => [ 'shape' => 'Framework', ], ], ], 'GetAssessmentReportUrlRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentReportId', 'assessmentId', ], 'members' => [ 'assessmentReportId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentReportId', ], 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], ], ], 'GetAssessmentReportUrlResponse' => [ 'type' => 'structure', 'members' => [ 'preSignedUrl' => [ 'shape' => 'URL', ], ], ], 'GetAssessmentRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], ], ], 'GetAssessmentResponse' => [ 'type' => 'structure', 'members' => [ 'assessment' => [ 'shape' => 'Assessment', ], 'userRole' => [ 'shape' => 'Role', ], ], ], 'GetChangeLogsRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], 'controlSetId' => [ 'shape' => 'ControlSetId', 'location' => 'querystring', 'locationName' => 'controlSetId', ], 'controlId' => [ 'shape' => 'UUID', 'location' => 'querystring', 'locationName' => 'controlId', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'GetChangeLogsResponse' => [ 'type' => 'structure', 'members' => [ 'changeLogs' => [ 'shape' => 'ChangeLogs', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'GetControlRequest' => [ 'type' => 'structure', 'required' => [ 'controlId', ], 'members' => [ 'controlId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'controlId', ], ], ], 'GetControlResponse' => [ 'type' => 'structure', 'members' => [ 'control' => [ 'shape' => 'Control', ], ], ], 'GetDelegationsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'GetDelegationsResponse' => [ 'type' => 'structure', 'members' => [ 'delegations' => [ 'shape' => 'DelegationMetadataList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'GetEvidenceByEvidenceFolderRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', 'controlSetId', 'evidenceFolderId', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], 'controlSetId' => [ 'shape' => 'ControlSetId', 'location' => 'uri', 'locationName' => 'controlSetId', ], 'evidenceFolderId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'evidenceFolderId', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'GetEvidenceByEvidenceFolderResponse' => [ 'type' => 'structure', 'members' => [ 'evidence' => [ 'shape' => 'EvidenceList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'GetEvidenceFileUploadUrlRequest' => [ 'type' => 'structure', 'required' => [ 'fileName', ], 'members' => [ 'fileName' => [ 'shape' => 'ManualEvidenceLocalFileName', 'location' => 'querystring', 'locationName' => 'fileName', ], ], ], 'GetEvidenceFileUploadUrlResponse' => [ 'type' => 'structure', 'members' => [ 'evidenceFileName' => [ 'shape' => 'NonEmptyString', ], 'uploadUrl' => [ 'shape' => 'NonEmptyString', ], ], 'sensitive' => true, ], 'GetEvidenceFolderRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', 'controlSetId', 'evidenceFolderId', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], 'controlSetId' => [ 'shape' => 'ControlSetId', 'location' => 'uri', 'locationName' => 'controlSetId', ], 'evidenceFolderId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'evidenceFolderId', ], ], ], 'GetEvidenceFolderResponse' => [ 'type' => 'structure', 'members' => [ 'evidenceFolder' => [ 'shape' => 'AssessmentEvidenceFolder', ], ], ], 'GetEvidenceFoldersByAssessmentControlRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', 'controlSetId', 'controlId', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], 'controlSetId' => [ 'shape' => 'ControlSetId', 'location' => 'uri', 'locationName' => 'controlSetId', ], 'controlId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'controlId', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'GetEvidenceFoldersByAssessmentControlResponse' => [ 'type' => 'structure', 'members' => [ 'evidenceFolders' => [ 'shape' => 'AssessmentEvidenceFolders', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'GetEvidenceFoldersByAssessmentRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'GetEvidenceFoldersByAssessmentResponse' => [ 'type' => 'structure', 'members' => [ 'evidenceFolders' => [ 'shape' => 'AssessmentEvidenceFolders', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'GetEvidenceRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', 'controlSetId', 'evidenceFolderId', 'evidenceId', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], 'controlSetId' => [ 'shape' => 'ControlSetId', 'location' => 'uri', 'locationName' => 'controlSetId', ], 'evidenceFolderId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'evidenceFolderId', ], 'evidenceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'evidenceId', ], ], ], 'GetEvidenceResponse' => [ 'type' => 'structure', 'members' => [ 'evidence' => [ 'shape' => 'Evidence', ], ], ], 'GetInsightsByAssessmentRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], ], ], 'GetInsightsByAssessmentResponse' => [ 'type' => 'structure', 'members' => [ 'insights' => [ 'shape' => 'InsightsByAssessment', ], ], ], 'GetInsightsRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetInsightsResponse' => [ 'type' => 'structure', 'members' => [ 'insights' => [ 'shape' => 'Insights', ], ], ], 'GetOrganizationAdminAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetOrganizationAdminAccountResponse' => [ 'type' => 'structure', 'members' => [ 'adminAccountId' => [ 'shape' => 'AccountId', ], 'organizationId' => [ 'shape' => 'organizationId', ], ], ], 'GetServicesInScopeRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetServicesInScopeResponse' => [ 'type' => 'structure', 'members' => [ 'serviceMetadata' => [ 'shape' => 'ServiceMetadataList', ], ], ], 'GetSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'attribute', ], 'members' => [ 'attribute' => [ 'shape' => 'SettingAttribute', 'location' => 'uri', 'locationName' => 'attribute', ], ], ], 'GetSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'settings' => [ 'shape' => 'Settings', ], ], ], 'HyperlinkName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[\\w\\W\\s\\S]*$', ], 'IamArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:.*:iam:.*', ], 'Insights' => [ 'type' => 'structure', 'members' => [ 'activeAssessmentsCount' => [ 'shape' => 'NullableInteger', ], 'noncompliantEvidenceCount' => [ 'shape' => 'NullableInteger', ], 'compliantEvidenceCount' => [ 'shape' => 'NullableInteger', ], 'inconclusiveEvidenceCount' => [ 'shape' => 'NullableInteger', ], 'assessmentControlsCountByNoncompliantEvidence' => [ 'shape' => 'NullableInteger', ], 'totalAssessmentControlsCount' => [ 'shape' => 'NullableInteger', ], 'lastUpdated' => [ 'shape' => 'Timestamp', ], ], ], 'InsightsByAssessment' => [ 'type' => 'structure', 'members' => [ 'noncompliantEvidenceCount' => [ 'shape' => 'NullableInteger', ], 'compliantEvidenceCount' => [ 'shape' => 'NullableInteger', ], 'inconclusiveEvidenceCount' => [ 'shape' => 'NullableInteger', ], 'assessmentControlsCountByNoncompliantEvidence' => [ 'shape' => 'NullableInteger', ], 'totalAssessmentControlsCount' => [ 'shape' => 'NullableInteger', ], 'lastUpdated' => [ 'shape' => 'Timestamp', ], ], ], 'Integer' => [ 'type' => 'integer', ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'KeywordInputType' => [ 'type' => 'string', 'enum' => [ 'SELECT_FROM_LIST', 'UPLOAD_FILE', 'INPUT_TEXT', ], ], 'KeywordValue' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z_0-9-\\s().:\\/]+$', ], 'Keywords' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeywordValue', ], ], 'KmsKey' => [ 'type' => 'string', 'max' => 2048, 'min' => 7, 'pattern' => '^arn:.*:kms:.*|DEFAULT', ], 'LastUpdatedBy' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\s-_()\\[\\]]+$', 'sensitive' => true, ], 'ListAssessmentControlInsightsByControlDomainRequest' => [ 'type' => 'structure', 'required' => [ 'controlDomainId', 'assessmentId', ], 'members' => [ 'controlDomainId' => [ 'shape' => 'ControlDomainId', 'location' => 'querystring', 'locationName' => 'controlDomainId', ], 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'querystring', 'locationName' => 'assessmentId', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAssessmentControlInsightsByControlDomainResponse' => [ 'type' => 'structure', 'members' => [ 'controlInsightsByAssessment' => [ 'shape' => 'ControlInsightsMetadataByAssessment', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListAssessmentFrameworkShareRequestsRequest' => [ 'type' => 'structure', 'required' => [ 'requestType', ], 'members' => [ 'requestType' => [ 'shape' => 'ShareRequestType', 'location' => 'querystring', 'locationName' => 'requestType', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAssessmentFrameworkShareRequestsResponse' => [ 'type' => 'structure', 'members' => [ 'assessmentFrameworkShareRequests' => [ 'shape' => 'AssessmentFrameworkShareRequestList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListAssessmentFrameworksRequest' => [ 'type' => 'structure', 'required' => [ 'frameworkType', ], 'members' => [ 'frameworkType' => [ 'shape' => 'FrameworkType', 'location' => 'querystring', 'locationName' => 'frameworkType', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAssessmentFrameworksResponse' => [ 'type' => 'structure', 'members' => [ 'frameworkMetadataList' => [ 'shape' => 'FrameworkMetadataList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListAssessmentMetadata' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentMetadataItem', ], ], 'ListAssessmentReportsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAssessmentReportsResponse' => [ 'type' => 'structure', 'members' => [ 'assessmentReports' => [ 'shape' => 'AssessmentReportsMetadata', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListAssessmentsRequest' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'AssessmentStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAssessmentsResponse' => [ 'type' => 'structure', 'members' => [ 'assessmentMetadata' => [ 'shape' => 'ListAssessmentMetadata', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListControlDomainInsightsByAssessmentRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'querystring', 'locationName' => 'assessmentId', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListControlDomainInsightsByAssessmentResponse' => [ 'type' => 'structure', 'members' => [ 'controlDomainInsights' => [ 'shape' => 'ControlDomainInsightsList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListControlDomainInsightsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListControlDomainInsightsResponse' => [ 'type' => 'structure', 'members' => [ 'controlDomainInsights' => [ 'shape' => 'ControlDomainInsightsList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListControlInsightsByControlDomainRequest' => [ 'type' => 'structure', 'required' => [ 'controlDomainId', ], 'members' => [ 'controlDomainId' => [ 'shape' => 'ControlDomainId', 'location' => 'querystring', 'locationName' => 'controlDomainId', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListControlInsightsByControlDomainResponse' => [ 'type' => 'structure', 'members' => [ 'controlInsightsMetadata' => [ 'shape' => 'ControlInsightsMetadata', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListControlsRequest' => [ 'type' => 'structure', 'required' => [ 'controlType', ], 'members' => [ 'controlType' => [ 'shape' => 'ControlType', 'location' => 'querystring', 'locationName' => 'controlType', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'controlCatalogId' => [ 'shape' => 'ControlCatalogId', 'location' => 'querystring', 'locationName' => 'controlCatalogId', ], ], ], 'ListControlsResponse' => [ 'type' => 'structure', 'members' => [ 'controlMetadataList' => [ 'shape' => 'ControlMetadataList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListKeywordsForDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'source', ], 'members' => [ 'source' => [ 'shape' => 'DataSourceType', 'location' => 'querystring', 'locationName' => 'source', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListKeywordsForDataSourceResponse' => [ 'type' => 'structure', 'members' => [ 'keywords' => [ 'shape' => 'Keywords', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListNotificationsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListNotificationsResponse' => [ 'type' => 'structure', 'members' => [ 'notifications' => [ 'shape' => 'Notifications', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AuditManagerArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ManualEvidence' => [ 'type' => 'structure', 'members' => [ 's3ResourcePath' => [ 'shape' => 'S3Url', ], 'textResponse' => [ 'shape' => 'ManualEvidenceTextResponse', ], 'evidenceFileName' => [ 'shape' => 'ManualEvidenceLocalFileName', ], ], ], 'ManualEvidenceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManualEvidence', ], 'max' => 50, 'min' => 1, ], 'ManualEvidenceLocalFileName' => [ 'type' => 'string', 'max' => 300, 'min' => 1, 'pattern' => '[^\\/]*', 'sensitive' => true, ], 'ManualEvidenceTextResponse' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^[\\w\\W\\s\\S]*$', 'sensitive' => true, ], 'MaxResults' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'NonEmptyString' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '.*\\S.*', ], 'Notification' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'TimestampUUID', ], 'assessmentId' => [ 'shape' => 'UUID', ], 'assessmentName' => [ 'shape' => 'AssessmentName', ], 'controlSetId' => [ 'shape' => 'ControlSetId', ], 'controlSetName' => [ 'shape' => 'NonEmptyString', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'eventTime' => [ 'shape' => 'Timestamp', ], 'source' => [ 'shape' => 'NonEmptyString', ], ], ], 'Notifications' => [ 'type' => 'list', 'member' => [ 'shape' => 'Notification', ], ], 'NullableInteger' => [ 'type' => 'integer', ], 'ObjectTypeEnum' => [ 'type' => 'string', 'enum' => [ 'ASSESSMENT', 'CONTROL_SET', 'CONTROL', 'DELEGATION', 'ASSESSMENT_REPORT', ], ], 'QueryStatement' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, 'pattern' => '(?s).*', ], 'Region' => [ 'type' => 'string', 'pattern' => '^[a-z]{2}-[a-z]+-[0-9]{1}$', ], 'RegisterAccountRequest' => [ 'type' => 'structure', 'members' => [ 'kmsKey' => [ 'shape' => 'KmsKey', ], 'delegatedAdminAccount' => [ 'shape' => 'AccountId', ], ], ], 'RegisterAccountResponse' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'AccountStatus', ], ], ], 'RegisterOrganizationAdminAccountRequest' => [ 'type' => 'structure', 'required' => [ 'adminAccountId', ], 'members' => [ 'adminAccountId' => [ 'shape' => 'AccountId', ], ], ], 'RegisterOrganizationAdminAccountResponse' => [ 'type' => 'structure', 'members' => [ 'adminAccountId' => [ 'shape' => 'AccountId', ], 'organizationId' => [ 'shape' => 'organizationId', ], ], ], 'Resource' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'GenericArn', ], 'value' => [ 'shape' => 'String', ], 'complianceCheck' => [ 'shape' => 'String', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'Resources' => [ 'type' => 'list', 'member' => [ 'shape' => 'Resource', ], ], 'Role' => [ 'type' => 'structure', 'required' => [ 'roleType', 'roleArn', ], 'members' => [ 'roleType' => [ 'shape' => 'RoleType', ], 'roleArn' => [ 'shape' => 'IamArn', ], ], ], 'RoleType' => [ 'type' => 'string', 'enum' => [ 'PROCESS_OWNER', 'RESOURCE_OWNER', ], ], 'Roles' => [ 'type' => 'list', 'member' => [ 'shape' => 'Role', ], 'sensitive' => true, ], 'S3Url' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^(S|s)3:\\/\\/[a-zA-Z0-9\\-\\.\\(\\)\\\'\\*\\_\\!\\/]+$', ], 'SNSTopic' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-_\\(\\)\\[\\]]+$', 'sensitive' => true, ], 'Scope' => [ 'type' => 'structure', 'members' => [ 'awsAccounts' => [ 'shape' => 'AWSAccounts', ], 'awsServices' => [ 'shape' => 'AWSServices', 'deprecated' => true, 'deprecatedMessage' => 'You can\'t specify services in scope when creating/updating an assessment. If you use the parameter to specify one or more AWS services, Audit Manager ignores the input. Instead the value of the parameter will show as empty indicating that the services are defined and managed by Audit Manager.', ], ], 'sensitive' => true, ], 'ServiceMetadata' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AWSServiceName', ], 'displayName' => [ 'shape' => 'NonEmptyString', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'category' => [ 'shape' => 'NonEmptyString', ], ], ], 'ServiceMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceMetadata', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'SettingAttribute' => [ 'type' => 'string', 'enum' => [ 'ALL', 'IS_AWS_ORG_ENABLED', 'SNS_TOPIC', 'DEFAULT_ASSESSMENT_REPORTS_DESTINATION', 'DEFAULT_PROCESS_OWNERS', 'EVIDENCE_FINDER_ENABLEMENT', 'DEREGISTRATION_POLICY', 'DEFAULT_EXPORT_DESTINATION', ], ], 'Settings' => [ 'type' => 'structure', 'members' => [ 'isAwsOrgEnabled' => [ 'shape' => 'Boolean', ], 'snsTopic' => [ 'shape' => 'SNSTopic', ], 'defaultAssessmentReportsDestination' => [ 'shape' => 'AssessmentReportsDestination', ], 'defaultProcessOwners' => [ 'shape' => 'Roles', ], 'kmsKey' => [ 'shape' => 'KmsKey', ], 'evidenceFinderEnablement' => [ 'shape' => 'EvidenceFinderEnablement', ], 'deregistrationPolicy' => [ 'shape' => 'DeregistrationPolicy', ], 'defaultExportDestination' => [ 'shape' => 'DefaultExportDestination', ], ], ], 'ShareRequestAction' => [ 'type' => 'string', 'enum' => [ 'ACCEPT', 'DECLINE', 'REVOKE', ], ], 'ShareRequestComment' => [ 'type' => 'string', 'max' => 500, 'pattern' => '^[\\w\\W\\s\\S]*$', ], 'ShareRequestStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'REPLICATING', 'SHARED', 'EXPIRING', 'FAILED', 'EXPIRED', 'DECLINED', 'REVOKED', ], ], 'ShareRequestType' => [ 'type' => 'string', 'enum' => [ 'SENT', 'RECEIVED', ], ], 'SnsArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 4, 'pattern' => '^arn:.*:sns:.*|NONE', ], 'SourceDescription' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '^[\\w\\W\\s\\S]*$', ], 'SourceFrequency' => [ 'type' => 'string', 'enum' => [ 'DAILY', 'WEEKLY', 'MONTHLY', ], ], 'SourceKeyword' => [ 'type' => 'structure', 'members' => [ 'keywordInputType' => [ 'shape' => 'KeywordInputType', ], 'keywordValue' => [ 'shape' => 'KeywordValue', ], ], ], 'SourceName' => [ 'type' => 'string', 'max' => 300, 'min' => 1, ], 'SourceSetUpOption' => [ 'type' => 'string', 'enum' => [ 'System_Controls_Mapping', 'Procedural_Controls_Mapping', ], ], 'SourceType' => [ 'type' => 'string', 'enum' => [ 'AWS_Cloudtrail', 'AWS_Config', 'AWS_Security_Hub', 'AWS_API_Call', 'MANUAL', 'Common_Control', 'Core_Control', ], ], 'StartAssessmentFrameworkShareRequest' => [ 'type' => 'structure', 'required' => [ 'frameworkId', 'destinationAccount', 'destinationRegion', ], 'members' => [ 'frameworkId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'frameworkId', ], 'destinationAccount' => [ 'shape' => 'AccountId', ], 'destinationRegion' => [ 'shape' => 'Region', ], 'comment' => [ 'shape' => 'ShareRequestComment', ], ], ], 'StartAssessmentFrameworkShareResponse' => [ 'type' => 'structure', 'members' => [ 'assessmentFrameworkShareRequest' => [ 'shape' => 'AssessmentFrameworkShareRequest', ], ], ], 'String' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '.*', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AuditManagerArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '.{0,255}', ], 'TestingInformation' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '^[\\w\\W\\s\\S]*$', 'sensitive' => true, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TimestampUUID' => [ 'type' => 'string', 'max' => 50, 'min' => 47, 'pattern' => '^[0-9]{10,13}_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'Token' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^[A-Za-z0-9+\\/=]*$', ], 'TroubleshootingText' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '^[\\w\\W\\s\\S]*$', 'sensitive' => true, ], 'URL' => [ 'type' => 'structure', 'members' => [ 'hyperlinkName' => [ 'shape' => 'HyperlinkName', ], 'link' => [ 'shape' => 'UrlLink', ], ], ], 'UUID' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AuditManagerArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAssessmentControlRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', 'controlSetId', 'controlId', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], 'controlSetId' => [ 'shape' => 'ControlSetId', 'location' => 'uri', 'locationName' => 'controlSetId', ], 'controlId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'controlId', ], 'controlStatus' => [ 'shape' => 'ControlStatus', ], 'commentBody' => [ 'shape' => 'ControlCommentBody', ], ], ], 'UpdateAssessmentControlResponse' => [ 'type' => 'structure', 'members' => [ 'control' => [ 'shape' => 'AssessmentControl', ], ], ], 'UpdateAssessmentControlSetStatusRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', 'controlSetId', 'status', 'comment', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], 'controlSetId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'controlSetId', ], 'status' => [ 'shape' => 'ControlSetStatus', ], 'comment' => [ 'shape' => 'DelegationComment', ], ], ], 'UpdateAssessmentControlSetStatusResponse' => [ 'type' => 'structure', 'members' => [ 'controlSet' => [ 'shape' => 'AssessmentControlSet', ], ], ], 'UpdateAssessmentFrameworkControlSet' => [ 'type' => 'structure', 'required' => [ 'name', 'controls', ], 'members' => [ 'id' => [ 'shape' => 'ControlSetName', ], 'name' => [ 'shape' => 'ControlSetName', ], 'controls' => [ 'shape' => 'CreateAssessmentFrameworkControls', ], ], ], 'UpdateAssessmentFrameworkControlSets' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateAssessmentFrameworkControlSet', ], 'min' => 1, ], 'UpdateAssessmentFrameworkRequest' => [ 'type' => 'structure', 'required' => [ 'frameworkId', 'name', 'controlSets', ], 'members' => [ 'frameworkId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'frameworkId', ], 'name' => [ 'shape' => 'FrameworkName', ], 'description' => [ 'shape' => 'FrameworkDescription', ], 'complianceType' => [ 'shape' => 'ComplianceType', ], 'controlSets' => [ 'shape' => 'UpdateAssessmentFrameworkControlSets', ], ], ], 'UpdateAssessmentFrameworkResponse' => [ 'type' => 'structure', 'members' => [ 'framework' => [ 'shape' => 'Framework', ], ], ], 'UpdateAssessmentFrameworkShareRequest' => [ 'type' => 'structure', 'required' => [ 'requestId', 'requestType', 'action', ], 'members' => [ 'requestId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'requestId', ], 'requestType' => [ 'shape' => 'ShareRequestType', ], 'action' => [ 'shape' => 'ShareRequestAction', ], ], ], 'UpdateAssessmentFrameworkShareResponse' => [ 'type' => 'structure', 'members' => [ 'assessmentFrameworkShareRequest' => [ 'shape' => 'AssessmentFrameworkShareRequest', ], ], ], 'UpdateAssessmentRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', 'scope', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], 'assessmentName' => [ 'shape' => 'AssessmentName', ], 'assessmentDescription' => [ 'shape' => 'AssessmentDescription', ], 'scope' => [ 'shape' => 'Scope', ], 'assessmentReportsDestination' => [ 'shape' => 'AssessmentReportsDestination', ], 'roles' => [ 'shape' => 'Roles', ], ], ], 'UpdateAssessmentResponse' => [ 'type' => 'structure', 'members' => [ 'assessment' => [ 'shape' => 'Assessment', ], ], ], 'UpdateAssessmentStatusRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', 'status', ], 'members' => [ 'assessmentId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'assessmentId', ], 'status' => [ 'shape' => 'AssessmentStatus', ], ], ], 'UpdateAssessmentStatusResponse' => [ 'type' => 'structure', 'members' => [ 'assessment' => [ 'shape' => 'Assessment', ], ], ], 'UpdateControlRequest' => [ 'type' => 'structure', 'required' => [ 'controlId', 'name', 'controlMappingSources', ], 'members' => [ 'controlId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'controlId', ], 'name' => [ 'shape' => 'ControlName', ], 'description' => [ 'shape' => 'ControlDescription', ], 'testingInformation' => [ 'shape' => 'TestingInformation', ], 'actionPlanTitle' => [ 'shape' => 'ActionPlanTitle', ], 'actionPlanInstructions' => [ 'shape' => 'ActionPlanInstructions', ], 'controlMappingSources' => [ 'shape' => 'ControlMappingSources', ], ], ], 'UpdateControlResponse' => [ 'type' => 'structure', 'members' => [ 'control' => [ 'shape' => 'Control', ], ], ], 'UpdateSettingsRequest' => [ 'type' => 'structure', 'members' => [ 'snsTopic' => [ 'shape' => 'SnsArn', ], 'defaultAssessmentReportsDestination' => [ 'shape' => 'AssessmentReportsDestination', ], 'defaultProcessOwners' => [ 'shape' => 'Roles', ], 'kmsKey' => [ 'shape' => 'KmsKey', ], 'evidenceFinderEnabled' => [ 'shape' => 'Boolean', ], 'deregistrationPolicy' => [ 'shape' => 'DeregistrationPolicy', ], 'defaultExportDestination' => [ 'shape' => 'DefaultExportDestination', ], ], ], 'UpdateSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'settings' => [ 'shape' => 'Settings', ], ], ], 'UrlLink' => [ 'type' => 'string', 'max' => 8192, 'min' => 1, 'pattern' => '^(https?:\\/\\/)?(www\\.)?[a-zA-Z0-9-_]+([\\.]+[a-zA-Z]+)+[\\/\\w]*$', ], 'Username' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-_()\\s\\+=,.@]+$', 'sensitive' => true, ], 'ValidateAssessmentReportIntegrityRequest' => [ 'type' => 'structure', 'required' => [ 's3RelativePath', ], 'members' => [ 's3RelativePath' => [ 'shape' => 'S3Url', ], ], ], 'ValidateAssessmentReportIntegrityResponse' => [ 'type' => 'structure', 'members' => [ 'signatureValid' => [ 'shape' => 'Boolean', ], 'signatureAlgorithm' => [ 'shape' => 'String', ], 'signatureDateTime' => [ 'shape' => 'String', ], 'signatureKeyId' => [ 'shape' => 'String', ], 'validationErrors' => [ 'shape' => 'ValidationErrors', ], ], ], 'ValidationErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'fields' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'unknownOperation', 'cannotParse', 'fieldValidationFailed', 'other', ], ], 'organizationId' => [ 'type' => 'string', 'max' => 34, 'min' => 12, 'pattern' => 'o-[a-z0-9]{10,32}', ], ],];
