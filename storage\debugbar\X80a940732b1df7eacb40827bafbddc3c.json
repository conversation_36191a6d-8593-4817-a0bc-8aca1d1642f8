{"__meta": {"id": "X80a940732b1df7eacb40827bafbddc3c", "datetime": "2025-06-30 23:10:53", "utime": **********.705517, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.321902, "end": **********.705529, "duration": 0.38362693786621094, "duration_str": "384ms", "measures": [{"label": "Booting", "start": **********.321902, "relative_start": 0, "end": **********.640606, "relative_end": **********.640606, "duration": 0.31870388984680176, "duration_str": "319ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.640615, "relative_start": 0.3187129497528076, "end": **********.705531, "relative_end": 1.9073486328125e-06, "duration": 0.06491589546203613, "duration_str": "64.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45144792, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02195, "accumulated_duration_str": "21.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.667156, "duration": 0.02118, "duration_str": "21.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.492}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.696399, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.492, "width_percent": 1.777}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6991339, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 98.269, "width_percent": 1.731}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:3 [\n  2352 => array:9 [\n    \"name\" => \"مجسم شخصيات مختلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"2352\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2353 => array:8 [\n    \"name\" => \"مفتاح علبه\"\n    \"quantity\" => 1\n    \"price\" => \"6.00\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"id\" => \"2353\"\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n  ]\n  2354 => array:8 [\n    \"name\" => \"العاب شكل بطه\"\n    \"quantity\" => 1\n    \"price\" => \"6.50\"\n    \"tax\" => 0\n    \"subtotal\" => 6.5\n    \"id\" => \"2354\"\n    \"originalquantity\" => 9\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1495299907 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1495299907\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-562470886 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2818 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751325027058%7C14%7C1%7Co.clarity.ms%2Fcollect; quickly24erp_session=eyJpdiI6ImREZDBIa08yVElHNXI1bkhqMWIzMWc9PSIsInZhbHVlIjoia3gwNEg3bFlLdXlrY044ejdLbkFKWVc3YlRLOEo2QjhnZUZwdytZT1FYc1o1NVJsaG1lK1djZm53QWNVTk1SVitSVWs0T280SE9BeS9tcnJtMGpzckpLckxGWHhlcERZdjlaQjBCMVgwYjhtcFVkWVRPTGJmbksxTEZBSUVVTVZHbWhPK1JIdHhZYW8rRVRtUXNvVXFETEZXdGoyR0hEUTlNejVHUjMySkk5SWtYQkNIcTNKRVV0bktPdXJLd0FRUFp3U3hidVVSREFsSUZIWXFjVkp1cks2ZmRuQTN3djl6R2V0UTlMTXhDaUlGeWZNS2RLSElVMnlHNG16UmNRZWZIWUdIK1VQQWJ4WWRQZ0dsV3hKSGIvTndLdS9KYUNvSzhPeXNEVFp2dkV1UDV0Z29iVEFpK09CLzNFdEdEa3VHY2NvTWR3RjRKRnBLbDVTempxN2xqSTJrWHBGNXRCaXRUcHNPcW1qb1MvVDhRcytobFlGc0gzRVRMdUpRWHhYcDNkcnk5bjc2NDdyVERPK20rWEhRTHRCSUlra1BocmM5d2hmQnR3Q2F0Mzd2N1JhNmR6K1V1c0ZJU3BUQi9SWWNJTUFiQ0lleGxtbUtCK1R3ckxVREhCbHlMMVpiek9Ja2Q0UHV1L2hZSFkwaUdibVpXd2o1bk5zb2lWQmNURGMiLCJtYWMiOiIzMjkxOTZiMjJkNDFjOTlmYzExNTA0ZDY5YTY1MzYzOWQ5OTI4NzA2OGU1Y2JlNTFhN2UxZmNlMWIxZGY3MTI0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImpGWHlzai9xMlE1NU9KdWlzcEQxRXc9PSIsInZhbHVlIjoiT2xoblAzV2hKVkRxUlhYdXcyOC9qRlcybDhyTk5TTVRQeUI2N2pKcFJ5cm8vMFY5VHhHU2tYd04zQVdLZGVoQnBCR2NNSTZBS3RrSlZCMmlpK2VsY2JKVEpWNVA0U0dEWVNjQXRmWGFvb2s2NHNPR1pSaVM5REhIOVkwVUQyVUx1aTRJZ2JGTHJtbWNHRHVYTVBPK0RUVmtBb0hyQ3R1eFFyS3dlK001eFRuT1Vva2lSdUttK09HYnBXOUlXNjdjbWxHdFJlV0NWQk9URzNkaU9Tejd6WFMxTUxuYmxHYk9MWUlodmNkaHpwQXVTQ2hJUEFXSm5MdWpLQkxRWUZjUjVVSWRQbENxZzJRY1ZLc0d1RFJkMHpaYUpsT2RXUzcvZFJvSUdnd011NHBLbjZWVWV0Ri8rZ2ozRG1BcWRMR0Y5N0JYUU9nWGVsMjFFYnhqSW5QQUNxUldYdWxSTkdwemZPQ2ZFMDBSRVlCRE1sRjJQRzhLc21KcGRGRlR0a2p6a2VKUVFqU0E3MVlHQm1kTlluMXBJTTR2NFFSMUlNazRPK1VzMlVsc3hnelA3MmpaR1JXcGE3V1pyTzY4M01ITmVBWDlSenBUQW1teEYvOGZOLzIvc1p2Z1FhZWo1R08yQTJaQTFDbHZveDlWREl0WjcrWDVGU3lIbWZUU1RXeEYiLCJtYWMiOiJiMTIzOGMzYTA4NDA0NTY5MGRkNThmNzVhZmFhZWI0YTU4ZGJmYmU3NGJkYmMwNmRhN2ZhYmNlNDRlNWRiMGNiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImpxTklOWXhTYU1XWEJkQTBqVTRXZlE9PSIsInZhbHVlIjoiTXFGZEpQRXlGa212dDlvcjMyZUgzOTJTdTZUUld1Y0RIbVM4elBEck42T2RTa0tjV2NoaTJNekRxM3Vkc3lrR1dOeGt5blA0Ym55U2pNZWY5am1QNC9lMm5SZE1VcGh6OVBneXMxN2Y4Qmx1TWhKckg0OXNOdzJBV1ZmTzVubEw3cmlyUDRwVnhxYzd4NmhqaEJiZThRcHlESndxYTY0WWNKZ1pqeVdFSTZuZnRmZ1BDZy91c3ZCbWloYytBQnpaczBrTmh2S3Z1dllBcHJYMkhWOXM3alFFWENqVkhpc2Rnb1lHSlpwM2dNb0s5cUxpakE5OVNEbEZRZ0VUVDJBWFZKWmZyYjZRM2ZNL1BuR0l5L3BsYXhVbzEwQ1ZlaXYySVUrQnk5a0dvY1BKK2dwQnEzM3ppZnFlaW9XTW9Ga1pSQ29mZktuT3BjSnc5ampzYU1SdTIxNzd4Wms4eUJ6eWdrajg1UUhYS2puSkhMNDVRbEh3WENJazQ2Qzk0Vlk5cE5NYW1qQ0xCbWJBVHZ5ZUlzUzBGRTJCWGliL1ovUHlOejhGSHJzRS9UOC8wK0QvTTQ4WHdveVBkTE1CdHlWcnFrQWdDcU1jR0hscFd0SUgzYWRtbFRaT0pzTGUzSkVDS2k4YlhiU0s3b29aMU5tbWtnL1NUaWZ1VmhxYnZ3ZFoiLCJtYWMiOiJjNTgzZDkxNDlmYmY2OTAwMmNmYTUwNTg0OWMwNDQ3MTVmZWQ0YWFiNjk4ZTJkZDM4YTkzNjhiMzE3YzIyNjlmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-562470886\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1070134411 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aTqxfkAEkhMI0fxOQbQMM92eSERP8cAlL4TrbOIh</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nMXQGGGSGQFIfGwlrnEYCl0LIB0DtCvcZqQRlbGj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1070134411\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1101121044 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:10:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik15SUZuYWxnNXdaQk9QREhnK2xSQ3c9PSIsInZhbHVlIjoiaDBHM1NNU3N6RVVjUFZGWHV1NGI1NGFyTXhwYWkvUm1PT3NiNExVaHlOQkVHc1lYOGwxcksrdG1DTkgva0swbUpjNWtoOERrbXROUTB1T0RDQ08wb3NkV2hIUktONTVPOXgrUDZwWGg4KzlXcmdTRGN2aitsZnlRWFdDUzRmRks4TFBuQ21UdFgxWk91OGJwb1BOQzZUek02ek00b21TTWEyOGlkQ0lIemdaNStUTmFaNUdEMmJzY1p4a2hQeTgvMWFxdGlwMmFuZEo3MDVFejJIdUM4TUdhNzJITFo0RVNmM1J6ZGVGb3I0NWQrVlE5cUlkQ1ZRNDNvUkQvVEdIR1lPZld4eElUY21oWjNMZDdDRlZKV291U0lwcHhuTXpPV25ycUp6djVnYWhTZmFlYytZVEtHZjNsODBibzk0SndwNVNzVU0wTFFONGxIYm9qaEZ2ZmFpTzkvUTN3d1dFNzdBdXMvVldjT3VJWGd1K09KN3hTaUowS2VZK2g1YXI4cUhmZXlpTTBybjl2V3B4enNHdVBEU1UwcmFiQUM1cU4ybG5SYnMwRUMxY0dpV2gyWU9mMUM2WXRTU1NLdHBnYnZNbW80MHJPaDFEUU9xY1FRS2JoQTZOOTFkUnYzczhTZk1ubFRHM0VIZERtRys1WDZHMWl6Y0lmNFhhYnF2OEMiLCJtYWMiOiI1ZTAyZmQ3ODg2MDk3N2Q4ZTFiY2U2NDU4MjE5YTJmMzdjOGVjNDY0OWQ1NjVhYWE4NGQxMWY4MDc3NjJkYTg4IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjNNcE9xYUVjcWpzWFptSFNUSkgrc1E9PSIsInZhbHVlIjoiN2QvSWY2bTMrS2sxL1hNcHlDY2p4MHkvbEsrV20xeXRGdXVldkJWR0xNKzkwTDhyekpsZ1lVdXE4cWhlRmpqZlE3R2F6T2FQa0kvbUR3S0R3dktMUE5TSHA2WHhlcEM5YXY5bnViTVNRdU9lbm05ZjY5MDVqdGpPaVFhck81WVNYSzRydjkwdGJzVnlYd1F3VzZQQmxudDBMdGhnTFdCU2w4WjRuRG5JZDFScndIZkF6bXltaElkLzhpUjRTQW5mcVNlVytzaXVvMjlzU25MSE15dGpjb21vdVNNQU9sajFHMmVGSlJWcTNJdm1JOGdORXdXaDE2VnhYaUJaYXNWcUlERnFEN2FtWWxaRkhjNkNCK2ZOTkVTdG1LeUs4aXVNZ0tCZWlQZWowQ1pMdWJjUHNwVHBST01CNmU0bmVCRGQ5Z0NpcVYxREE3eEttcE9FaTJkOGJJT1o3a2FKTzdPUngreFF4bmJHcjVVWml5dGZ2aWRNNzBWakZoaFh1U21tMUhmSm4vMXJIZmwrLzZPODdXK2hmN3ZlbTRBd2FZcC9uSTdRNkJpdEY1d1U5Q0laSmhFbUNDWWZqWWZqVnVoQ1d5RWJCc3VyREdNOG91Zmh2SDYyYzRlaFNNZEZrM05Tb0JHNzhVTnFLVmlVdnhCTG1RNzdmNWlkYjVhQURVb0QiLCJtYWMiOiI4OTA5MThhYzk4YzRkNzA4NjUzNzhmZGM3YjlhOTQ2ODFmMWJlZTRhMGMwY2MzMTRlNzk4Njc2MjQ0NTRmNWQ0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:10:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik15SUZuYWxnNXdaQk9QREhnK2xSQ3c9PSIsInZhbHVlIjoiaDBHM1NNU3N6RVVjUFZGWHV1NGI1NGFyTXhwYWkvUm1PT3NiNExVaHlOQkVHc1lYOGwxcksrdG1DTkgva0swbUpjNWtoOERrbXROUTB1T0RDQ08wb3NkV2hIUktONTVPOXgrUDZwWGg4KzlXcmdTRGN2aitsZnlRWFdDUzRmRks4TFBuQ21UdFgxWk91OGJwb1BOQzZUek02ek00b21TTWEyOGlkQ0lIemdaNStUTmFaNUdEMmJzY1p4a2hQeTgvMWFxdGlwMmFuZEo3MDVFejJIdUM4TUdhNzJITFo0RVNmM1J6ZGVGb3I0NWQrVlE5cUlkQ1ZRNDNvUkQvVEdIR1lPZld4eElUY21oWjNMZDdDRlZKV291U0lwcHhuTXpPV25ycUp6djVnYWhTZmFlYytZVEtHZjNsODBibzk0SndwNVNzVU0wTFFONGxIYm9qaEZ2ZmFpTzkvUTN3d1dFNzdBdXMvVldjT3VJWGd1K09KN3hTaUowS2VZK2g1YXI4cUhmZXlpTTBybjl2V3B4enNHdVBEU1UwcmFiQUM1cU4ybG5SYnMwRUMxY0dpV2gyWU9mMUM2WXRTU1NLdHBnYnZNbW80MHJPaDFEUU9xY1FRS2JoQTZOOTFkUnYzczhTZk1ubFRHM0VIZERtRys1WDZHMWl6Y0lmNFhhYnF2OEMiLCJtYWMiOiI1ZTAyZmQ3ODg2MDk3N2Q4ZTFiY2U2NDU4MjE5YTJmMzdjOGVjNDY0OWQ1NjVhYWE4NGQxMWY4MDc3NjJkYTg4IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjNNcE9xYUVjcWpzWFptSFNUSkgrc1E9PSIsInZhbHVlIjoiN2QvSWY2bTMrS2sxL1hNcHlDY2p4MHkvbEsrV20xeXRGdXVldkJWR0xNKzkwTDhyekpsZ1lVdXE4cWhlRmpqZlE3R2F6T2FQa0kvbUR3S0R3dktMUE5TSHA2WHhlcEM5YXY5bnViTVNRdU9lbm05ZjY5MDVqdGpPaVFhck81WVNYSzRydjkwdGJzVnlYd1F3VzZQQmxudDBMdGhnTFdCU2w4WjRuRG5JZDFScndIZkF6bXltaElkLzhpUjRTQW5mcVNlVytzaXVvMjlzU25MSE15dGpjb21vdVNNQU9sajFHMmVGSlJWcTNJdm1JOGdORXdXaDE2VnhYaUJaYXNWcUlERnFEN2FtWWxaRkhjNkNCK2ZOTkVTdG1LeUs4aXVNZ0tCZWlQZWowQ1pMdWJjUHNwVHBST01CNmU0bmVCRGQ5Z0NpcVYxREE3eEttcE9FaTJkOGJJT1o3a2FKTzdPUngreFF4bmJHcjVVWml5dGZ2aWRNNzBWakZoaFh1U21tMUhmSm4vMXJIZmwrLzZPODdXK2hmN3ZlbTRBd2FZcC9uSTdRNkJpdEY1d1U5Q0laSmhFbUNDWWZqWWZqVnVoQ1d5RWJCc3VyREdNOG91Zmh2SDYyYzRlaFNNZEZrM05Tb0JHNzhVTnFLVmlVdnhCTG1RNzdmNWlkYjVhQURVb0QiLCJtYWMiOiI4OTA5MThhYzk4YzRkNzA4NjUzNzhmZGM3YjlhOTQ2ODFmMWJlZTRhMGMwY2MzMTRlNzk4Njc2MjQ0NTRmNWQ0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:10:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1101121044\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2352</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1605;&#1580;&#1587;&#1605; &#1588;&#1582;&#1589;&#1610;&#1575;&#1578; &#1605;&#1582;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2352</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2353</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1605;&#1601;&#1578;&#1575;&#1581; &#1593;&#1604;&#1576;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2353</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2354</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1588;&#1603;&#1604; &#1576;&#1591;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2354</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}