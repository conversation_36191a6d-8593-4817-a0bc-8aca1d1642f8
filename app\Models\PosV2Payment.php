<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PosV2Payment extends Model
{
    protected $table = 'pos_v2_payments';
    
    protected $fillable = [
        'pos_id',
        'date',
        'amount',
        'discount',
        'discount_amount',
        'created_by',
        'payment_type',
        'cash_amount',
        'network_amount',
        'transaction_number',
    ];

    public function posV2()
    {
        return $this->belongsTo('App\Models\PosV2', 'pos_id', 'id');
    }

    public function createdBy()
    {
        return $this->hasOne('App\Models\User', 'id', 'created_by');
    }

    public function bankAccount()
    {
        return $this->hasOne('App\Models\BankAccount', 'id', 'account_id');
    }
}
