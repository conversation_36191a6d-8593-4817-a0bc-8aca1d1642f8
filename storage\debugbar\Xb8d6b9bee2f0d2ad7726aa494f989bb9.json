{"__meta": {"id": "Xb8d6b9bee2f0d2ad7726aa494f989bb9", "datetime": "2025-06-30 23:14:04", "utime": **********.504896, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.107975, "end": **********.504922, "duration": 0.39694690704345703, "duration_str": "397ms", "measures": [{"label": "Booting", "start": **********.107975, "relative_start": 0, "end": **********.443391, "relative_end": **********.443391, "duration": 0.3354160785675049, "duration_str": "335ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.443399, "relative_start": 0.33542394638061523, "end": **********.504924, "relative_end": 2.1457672119140625e-06, "duration": 0.06152510643005371, "duration_str": "61.53ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43369072, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.021079999999999998, "accumulated_duration_str": "21.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.471289, "duration": 0.021079999999999998, "duration_str": "21.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "6DOf3LRIRxH3sOgteNJGho7yggpQCjM4JOiRU4Kx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1938916333 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1938916333\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2121184276 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2121184276\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751325241921%7C32%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpmYWtiWUYvS29pb2Q3TDRCZHFTM3c9PSIsInZhbHVlIjoicmhYdzBPb1ZoajF0UDRCWEZaa0JQN2dQRHZWOEdaa3ZJZTNwdEZIc0pDSDJJVDBmN0J4ZmRiek5nNkJpOW0vaGN1N2llbjNUc21wd2tKNlBJY2lCNEU2by9id212ckE5VXlsVDBtQ1FYOG93dVVsYmpkaFVjTlJSZDZFYVMrcmFTcUFRWVNjYnZIb0lSVFBUdUlzUm1rNHNJRzlTZ0ZVVVFYcG1pdm1NOEhjVjFqL0ZoZ1k3VjN4Ym5NNUZYblVyQnBMMU9zTVRsVVc3UU9hSWZYOXJxb0FjRHlubWVHTFZIQTJQSktseHRsQnhNcEpucjJoZmcxNE16UVhXWXdVOFdVcEVZaXpRYXJiWTVvaXBlc3NZVzVjZG0wY0psZm01azhEYUlZbmJGbjdKYk9EQ3pHbFZ1cXlhaXlPY2doREpJKzlrWC9wUlQ5QmZnQStKK0dabkNxQTg2WmVuMFRqY2lGcXlMZ1BqRjhlc21TQ3U3QWFGOFF4MmhMckZFenhmNEp0Nk5Xa25MVFJ5VmZHczBjMUZ1S2lGSC9STFcyQ0IrQzhNNUc0ZDllTnBNN2tYOVFTOFk5N0NjN0JMeDFYSHA1d2FqL0FrUXpmV29lY1J5WUxVYkVpTm9RbEpzT21BRXJZVXBVUyt0UWE2OVYxYWNBbU8zRlpmRThoQzVzZGciLCJtYWMiOiJiZTc1YzY1OGRkODJkODdjNTg1YTM1MTMzZGE4Y2M4N2I0ZTcyZmI0MjAxN2RhNzY0MTUyOTMwODM0YmM1OTU3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlpBcyt2ZHNPbWlMSFRvTjI4dEtEWVE9PSIsInZhbHVlIjoiMExHOUJvUkFKM2ZQT2R0Tm01UTBGU3lJTnFIQW10VFJ1eTUva0pxcFVWMVlYMGRoSVRyMExJRmZxd1hNRkpBblNWcXpYOWo3VmEwb3RtMGZUZEVscTVJVFRNK2dFSUtpWDljZGZyRWFZMUM2VXR5a2J4NENudUlLSVY4Z0cvS1F3Q3pmOUJXUUs0eXg3L1g0NjFpcWxURW1pdFlFYUY0c2FGaENrbzkvaGU4cWYyTSt6bkRNMG1BMzNpS1BoWXc2WFBIc2tYUUFLR1g4TElBdGhRQXloME5ENFVXQU9jT3RUa2QydDA3OVVwVnZrVFdXSDV2RVl4MTNKMUlzWU1XT3NTanNzb2E4T2QydGdNTG9wU0RoZkFRSHQyMnN4b1M4MFBWZEdhczFBNE92M1d2YWxDMGNQVSs3VlkxNUpTMFlvWDhNQlRtY01Xa2xKR2tSejNYVm83QTJON0FKOEpsN0pISzNjUlJ5eVA2VTUyS2lpOUxaR3VuTGxlQ0NHSmFiaFJtQXZwL21xMWM4MnRReVdURWkrMHIvSVducTZSOEpGcXhUelVCUUhZSEFjZ0tvNGdpaHVmbEVTR0tINDlVY3NQVU9hTG1RZWFWRWVma0ljU1ZsNWdyRHJFcEt6K2Yrc1h4NXBHTnphaUsveTF6LzlxaFprRkc0dmVKcUNRK0wiLCJtYWMiOiI3ZjgwZmY2YzExYjgzYzQwN2U1YTNjZjJjZDQ0NTFjMWFhMDA5MWNkNDk3MWJhNTVjNDJmMjM4MmU4Nzc5NzdkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6DOf3LRIRxH3sOgteNJGho7yggpQCjM4JOiRU4Kx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sMb5jzTWJNq3hLmJyShrQXVAdtXYkO6Vq50wL9dH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-383152500 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:14:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVvMU5NaHE3L29NQTFJcE5MaEtqbUE9PSIsInZhbHVlIjoibFNRMzRvQlNURlZHdW5SVWY3akJONW1GMDZjUCtIek4zSlVYdCtYYWZzZi90b21rWGZKN3NwU1BwWWVpVjBOY0JKbFRGWXNwTzBDU3lidHY4Tm1lN3hOZVdGcFcyR2lwZDF4Y09SeXNvbFd5NVZpT09EYlUwaHpMbkZHRnVpMzhTV1U3VUxQK0hoQmM3eHR5ZXMxTmdNTHg5aGNvU3Q5QzZ5UXBTM08xcU93ZDY5dVo3VUtJdzJxanhaeG9XREFPaEoxdnhyam9kUmJPYyt0b0J5WXpseVhIaDB6eGFkQlhLS3hESEV0N0pkSzhkRU1Na005WjdMZmI3cnpEM29RRm1yTjFQZ1c1elZ4Q1R4UmxUN012aStVWlJ4b1Bnb3BBUGRSV0tWR3M3QklOZXRxTzhmbVlzM3pJeGM0RGRPNUxIOWpBcmJjRHoyK3NHdnRxVHByRU9JMFJYRTUraHgxWFgrNkliOE04YXVxMGs4QTluRDRHdFMxS3lVcC9VcmszUWF5eWVQTmpRdVR6VVBCOHJNVE96QTFteDl0RUdMK3o3QVNmV1lEUnZLZHRMN3pwcEZQRjJyb3VQTnFvdE4rbVdsR1Nqcnl6YnBZNnIvUkFqQVVFQjhrMGdoaERUVjVsQlZMT3R6dmx6WTlvTlloTU82RFZpS01tUGVGRXRReVciLCJtYWMiOiJjY2NkOWIxOTI5YWQxZWMzOTVlMGViNmZkM2VlMzFhNTU4MzM1MzIzMmZhMmIxODRkNDkzNjQwZmJjNDFjZWE2IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlVVR2dsMEJ3Z3lKYnFyak0rQXcyTEE9PSIsInZhbHVlIjoiR3Nic1VpMUluRUVmMVB3b1pydnU3MkJGVnhKTzlYMzB2bzBlektUWmlaUnJWeDRaZjBnemxjT1V0OGtsamVQd1ErNDQzQ08yQURrbW5ubEpFMGZhdXcyVEU4WGlUUFVmMS9LVlpiVGc4NXp1Z1FxNnlpaHduZXA0SE0xYVV2bkpyWFd4NCtOZkljUUZJNnFsNjdBK2s4dWZvMHNKbUxFWDZMRWZxaGxkZnVUYnlSU0NKcXlWU0tTN01qM3hFSGI0QnNhbithNk1QQUdSV2k0T3ZHWUFwMmdvL2ZwYzZkcXowWWduaXZRZmo2WXVvWXR0VXhGK09QN3Y5N0JwR0Jhc2o3RFVNMmI5MjVtVzQrcjZxRmlackVzSHBMc1pkTGZob3RGM2hlM3FkcktkbVZOUHUvQVFPbUdVdEZQZVNCOTZEOTdxYzM2azUrNjFsZExLeCt6TzllenFQeVZiYzVNV2lKeWNjK2dnVVg0UXdlNzhVaWZReFZ4Z0hUWWRhQTk2K2wwTFkzTWFFWi94cDlvaFdpOWc0QkFRSG1iSENabnltYmxNY3VEYUhYKyszS2Vjdk40bmJuVVlPaWI3ZlQ1bmxRamV5VFFQQ25NNG1RZlFoUmcza0dvQlNmbXFFelVxNVBDdGRXekxmM0hBamx1d1pVYmdWR2U0VEx6MzlIa2kiLCJtYWMiOiJhNjY1N2I1YjJjNjNhOTdmODZlNmU3ZjBmZmI2ODllYTQ0NDYzMDRmY2M0YWVlY2JlNTliYmEwYWQ5ZDBlOTZlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:14:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVvMU5NaHE3L29NQTFJcE5MaEtqbUE9PSIsInZhbHVlIjoibFNRMzRvQlNURlZHdW5SVWY3akJONW1GMDZjUCtIek4zSlVYdCtYYWZzZi90b21rWGZKN3NwU1BwWWVpVjBOY0JKbFRGWXNwTzBDU3lidHY4Tm1lN3hOZVdGcFcyR2lwZDF4Y09SeXNvbFd5NVZpT09EYlUwaHpMbkZHRnVpMzhTV1U3VUxQK0hoQmM3eHR5ZXMxTmdNTHg5aGNvU3Q5QzZ5UXBTM08xcU93ZDY5dVo3VUtJdzJxanhaeG9XREFPaEoxdnhyam9kUmJPYyt0b0J5WXpseVhIaDB6eGFkQlhLS3hESEV0N0pkSzhkRU1Na005WjdMZmI3cnpEM29RRm1yTjFQZ1c1elZ4Q1R4UmxUN012aStVWlJ4b1Bnb3BBUGRSV0tWR3M3QklOZXRxTzhmbVlzM3pJeGM0RGRPNUxIOWpBcmJjRHoyK3NHdnRxVHByRU9JMFJYRTUraHgxWFgrNkliOE04YXVxMGs4QTluRDRHdFMxS3lVcC9VcmszUWF5eWVQTmpRdVR6VVBCOHJNVE96QTFteDl0RUdMK3o3QVNmV1lEUnZLZHRMN3pwcEZQRjJyb3VQTnFvdE4rbVdsR1Nqcnl6YnBZNnIvUkFqQVVFQjhrMGdoaERUVjVsQlZMT3R6dmx6WTlvTlloTU82RFZpS01tUGVGRXRReVciLCJtYWMiOiJjY2NkOWIxOTI5YWQxZWMzOTVlMGViNmZkM2VlMzFhNTU4MzM1MzIzMmZhMmIxODRkNDkzNjQwZmJjNDFjZWE2IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlVVR2dsMEJ3Z3lKYnFyak0rQXcyTEE9PSIsInZhbHVlIjoiR3Nic1VpMUluRUVmMVB3b1pydnU3MkJGVnhKTzlYMzB2bzBlektUWmlaUnJWeDRaZjBnemxjT1V0OGtsamVQd1ErNDQzQ08yQURrbW5ubEpFMGZhdXcyVEU4WGlUUFVmMS9LVlpiVGc4NXp1Z1FxNnlpaHduZXA0SE0xYVV2bkpyWFd4NCtOZkljUUZJNnFsNjdBK2s4dWZvMHNKbUxFWDZMRWZxaGxkZnVUYnlSU0NKcXlWU0tTN01qM3hFSGI0QnNhbithNk1QQUdSV2k0T3ZHWUFwMmdvL2ZwYzZkcXowWWduaXZRZmo2WXVvWXR0VXhGK09QN3Y5N0JwR0Jhc2o3RFVNMmI5MjVtVzQrcjZxRmlackVzSHBMc1pkTGZob3RGM2hlM3FkcktkbVZOUHUvQVFPbUdVdEZQZVNCOTZEOTdxYzM2azUrNjFsZExLeCt6TzllenFQeVZiYzVNV2lKeWNjK2dnVVg0UXdlNzhVaWZReFZ4Z0hUWWRhQTk2K2wwTFkzTWFFWi94cDlvaFdpOWc0QkFRSG1iSENabnltYmxNY3VEYUhYKyszS2Vjdk40bmJuVVlPaWI3ZlQ1bmxRamV5VFFQQ25NNG1RZlFoUmcza0dvQlNmbXFFelVxNVBDdGRXekxmM0hBamx1d1pVYmdWR2U0VEx6MzlIa2kiLCJtYWMiOiJhNjY1N2I1YjJjNjNhOTdmODZlNmU3ZjBmZmI2ODllYTQ0NDYzMDRmY2M0YWVlY2JlNTliYmEwYWQ5ZDBlOTZlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:14:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-383152500\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-918912597 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6DOf3LRIRxH3sOgteNJGho7yggpQCjM4JOiRU4Kx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-918912597\", {\"maxDepth\":0})</script>\n"}}