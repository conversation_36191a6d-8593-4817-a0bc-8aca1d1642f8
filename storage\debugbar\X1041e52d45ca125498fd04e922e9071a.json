{"__meta": {"id": "X1041e52d45ca125498fd04e922e9071a", "datetime": "2025-06-30 22:39:55", "utime": **********.375009, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323194.873807, "end": **********.375025, "duration": 0.5012180805206299, "duration_str": "501ms", "measures": [{"label": "Booting", "start": 1751323194.873807, "relative_start": 0, "end": **********.309827, "relative_end": **********.309827, "duration": 0.4360201358795166, "duration_str": "436ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.309836, "relative_start": 0.43602895736694336, "end": **********.375027, "relative_end": 1.9073486328125e-06, "duration": 0.06519103050231934, "duration_str": "65.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041104, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01527, "accumulated_duration_str": "15.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3377302, "duration": 0.01416, "duration_str": "14.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.731}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.360715, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.731, "width_percent": 3.471}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3672318, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.202, "width_percent": 3.798}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management?end_date=&start_date=&status=all&warehouse_id=8\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-932447126 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-932447126\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2023801418 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2023801418\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1716500808 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1716500808\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"87 characters\">http://localhost/branch-cash-management?status=all&amp;warehouse_id=8&amp;start_date=&amp;end_date=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751323184510%7C13%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJReGJ2a3FacFlhZjdFeDlXQW9CS1E9PSIsInZhbHVlIjoiV2xsUXNUSHZ3Sml6R3Z6bVRsdU9NQjg2cGEvTWVrTk1xbUxyV05jWGpjVVJlVEowcGwvTVE5cVdkN0cxQk1OOGhRNXRjalpGMEVNTkQ5YTd2Wlkrdi9wRmVDU0QxNTNLVmlmemJ5aW94NUR2R2NINXBJMnAvOCtWUDhWWmRBYXd4KzhTd2d4U3o3c3lrbmhvbW5jZ3pyeWh6a2tMTG5FYXZudW5aWWFuUkxvZC80MUd0by9QTkNBaENnT1dVMXhZSVpENE45ZitEaGJGWURGdHEyR3E2VzIwZVN6b3BWWitkYTJHNjF3UmVteUkxY2ZQeTlWQ0UxUGtoRU8wQXc1REN5OXROL2xxa2dJcnU2QUg4SGltVzVKWWY3czkrSWJyVHJWTEdsUjhYdGY0V3VkSERLOW8zeXNsMGhEMW00UU5qOG16NmlSZHVkbVpNVHAzbnEvbkpIUU5GUzRWc29KNFUrU1JtSkVzbkc3MklzRm5YaTZXSmlpRUdkaUY4UStYRStQZDVkcjdSS2E3cmkvWkN2YTg2aVhqUXlsanN5MEJmUEpQdkdDdWptUGt0MHlJVi8yaWlJM1E2SFJULzVQZmpVSmJzbTROZ0ZUaGViRkZaUC9BTFJJK2RvTzVsYTYzMXBGeThUNyt3eTRyN25tQmtOMGlKM1M5U3dCb1A3TTQiLCJtYWMiOiI4NmFmYjEyZTQ2ZTgzNzMzMzBkMDY2Y2MxM2NiYjIwM2IwMDgwZmY2MmM5MTc4NzZiYjYxZjE3YTczMzE5M2JjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ilo2dVRFT1dpcjZiUFFvQmMxNVhOSmc9PSIsInZhbHVlIjoiQjM2emxQaXF5UGpQUkhsOXh3YmR1Z0RBT1ZtVEF0OHNhc2wwWjVBc1hadm1wek85V1ZiaUFabys0TzNMc0VNa2xFbnp3akplVWNxbUFSRFpkOWcwUEpFeFFkUU8wakFMMksrMDlWL29Zc0FQdXd3YjIzQ3hvOU9LdG52NjNlZzNFcWJ0NDFySHlXM3J6SXlTTkZWUHpacFp1R0JhTm5IVWRjQjYwUzM3TVBCN01XQVUvWkxFM2F5ZGs2OGd4L1U5OTBQZWIvT3grQjFJMnB1ZUc3Z1hIeEhSTHlxUkVtMUdjK3BIZUErYjF4b2dadWVWMEdSTytFaUtRakJ1bmJtbFNQQy95QmovRkVhTWlwVTlRTXJyaHNJa2lsS3liYmplVkJlUFRWT0gwUzYxeWttWkEzOVRhMVMrYmc5cGdkcDJnOFFTcGRJbHd5cEI2WDIzaXlBamxGRTNtaEVBNnM1UEU5a2tMazFUS2hrRVRNa2pobVVjd2YvZ3R1bnBJS0ltU0FaS2RON2VHSWJrVXNSblNQbE1BN0Q1Z0lkWEpmMHhOSnN2Z0J4S0JSZXpGNnZEbSs1bGQ0M21jQlZsQXRGY0hDaUcvQ2hzTVN3T3BFdVBSM2tQcGpBZ2JwTDEzSWNIampkU1JobjRMRzVzQitQeklJUUZ2QjE1UEt6d2pYck4iLCJtYWMiOiI0NTY0YzFjZTA5ZjZhZjAzMDBiOTk4MmI0YjBiMWZmMWY1NWM0YjM1OWEwMjc0OTJlZWY2NzIwMzJhMzc0YmQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0M1K0sdNadPD2LVrnt8LmBw227nA9F1U5e3ROaJK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1678522581 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtwZ2lOekFaM1FJbHBqWGM4S0tQdXc9PSIsInZhbHVlIjoiNEpGYUpSWHpncGY1TFJFd1k1aXVUWGgyWjUvQXVuUnNtOURCaE5CenF6ZThraWQwZjZKN0hzbHhqWjBCL0pPY250cU54Z2p1NmU5c1J2TzU1V1duNWcxSXRZeGh6azQ4NFQ3VVFCMW9KY2pPVEV0VGhHLytmQ0dxa2RveHRGU0Nyd0xsWGcvTjB6bW9mTGhNdEs0WlRld1dmL1lNbXpxZTFPQXc2QnlBQjJuZDFxUXgydDlBRXBQTVZWY0UyUEJKVDUvQ1p6Nnk3RWZBL3RQamRjL2doMDNwMmNWaUFDOGZrV2RJUHA5eXdST1pHc1BtV0JYb2E0SjRVZU1aNjVYQ2NCeUxya0xLT3dLWjBYcWoyQWNkZ3lIZnVHL3grd2Yxc05hV0hWbnVBc091amFmZW9RUlJibEI1Mk9GTFgwUm05TFZoWXJWSHQxOG1ZcjlqTUFyMEE2Zk9YK00xTGM0UURMbXY1WURxNkVjZVlxWTFQS1lHVks5blVNejZWcXUyRUQwNGQrWU5zaFBjQnFIZHFVN1psMS9PL2NVaFEvdWZNc3hKOXJkblIwSnpuQWgxV09hSUw5R0ZHOTV3ZXVHdXFTZER5Q21WVUlZdFFRenB2bWY3RVYvZityMlhqYndLcXlURWxieTh0eHdLN1JkdXBjTktoUlZpcGxxb2llTHYiLCJtYWMiOiJkYmE5ZGFmNmE1NjYzMjBiYzg3NjQ3ODBiMjVjMzVmZWZmZWI1MTc2MzIzN2IxODZiOWFkZjQ4OGVjZjUwYjg0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlluWTAwVVIrWmxoMWdBVWQxeWJQOWc9PSIsInZhbHVlIjoiSGJxSUZUUEFBMnRZczQ0NUl2VGV3ZVFlK1RSVzFDWWpuVnlDZFdWVkdNZ3RCR09wMmxjODNoV2NyVW1ZejhMWWZQTEd0OXlYSk9rU3JOZ0pwTGVxR0ZxV2FudzRPd2dyVXNNVWx2a3B4LytXRXlrSktYRzc5MXF1TGg5TTVadWlZVHhzbDlzc2NlcW8ydTBFOHJXN2ZPVklaK1RIOFBnMWlacjdaQkVyTU1BWU5FYnoyS1hNdCtBRmREMlhmNG1waGtWV2RGa3pENGdTb1pLVzdJL3NJWFdCTlJsekJaakdZQWxjb1pvR0ZBRFVib2ZMZWpYZTJGNHptbHI0NGFvdUg2TS91WHZlNDZhdkQxS0hjdzZwaVVQU003Tkl6OWN6QjhOMmZxZHRZODB0dzNCSnRxWlRJSnFTTHpLcGF0bUpEY2JwZ2hLNzhodE1lRE9yMndCQjM1bFU4b1UwcXpKSUd2cGh2YVdWbFJQMmR5a0RNaHhjMkx5UGwwOFcrTzNxUW85eDVOaXR1Tld3b0dOWjBoQWU0eVRaMm5VeU9EOFZjM28wazZrNmZkYVczOVZHSjloV2I3YmU1bUhUQmlra3VPQzJaWjMrTUwvay81SnBqbGR5MXpzSTBYWE9RcGFxT0Z0eVBhSU1LaUluenhTTUFkd0JpdVB3cGtkaEJBSXgiLCJtYWMiOiIzNzc5NGExN2I4ZTI4N2YxMmU3YTc3MTk3NDNjZTE0MGJiZWYyMGM4MzA4NWQ3NjU5NTBiOWFmOTcxMjE5Yzc0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtwZ2lOekFaM1FJbHBqWGM4S0tQdXc9PSIsInZhbHVlIjoiNEpGYUpSWHpncGY1TFJFd1k1aXVUWGgyWjUvQXVuUnNtOURCaE5CenF6ZThraWQwZjZKN0hzbHhqWjBCL0pPY250cU54Z2p1NmU5c1J2TzU1V1duNWcxSXRZeGh6azQ4NFQ3VVFCMW9KY2pPVEV0VGhHLytmQ0dxa2RveHRGU0Nyd0xsWGcvTjB6bW9mTGhNdEs0WlRld1dmL1lNbXpxZTFPQXc2QnlBQjJuZDFxUXgydDlBRXBQTVZWY0UyUEJKVDUvQ1p6Nnk3RWZBL3RQamRjL2doMDNwMmNWaUFDOGZrV2RJUHA5eXdST1pHc1BtV0JYb2E0SjRVZU1aNjVYQ2NCeUxya0xLT3dLWjBYcWoyQWNkZ3lIZnVHL3grd2Yxc05hV0hWbnVBc091amFmZW9RUlJibEI1Mk9GTFgwUm05TFZoWXJWSHQxOG1ZcjlqTUFyMEE2Zk9YK00xTGM0UURMbXY1WURxNkVjZVlxWTFQS1lHVks5blVNejZWcXUyRUQwNGQrWU5zaFBjQnFIZHFVN1psMS9PL2NVaFEvdWZNc3hKOXJkblIwSnpuQWgxV09hSUw5R0ZHOTV3ZXVHdXFTZER5Q21WVUlZdFFRenB2bWY3RVYvZityMlhqYndLcXlURWxieTh0eHdLN1JkdXBjTktoUlZpcGxxb2llTHYiLCJtYWMiOiJkYmE5ZGFmNmE1NjYzMjBiYzg3NjQ3ODBiMjVjMzVmZWZmZWI1MTc2MzIzN2IxODZiOWFkZjQ4OGVjZjUwYjg0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlluWTAwVVIrWmxoMWdBVWQxeWJQOWc9PSIsInZhbHVlIjoiSGJxSUZUUEFBMnRZczQ0NUl2VGV3ZVFlK1RSVzFDWWpuVnlDZFdWVkdNZ3RCR09wMmxjODNoV2NyVW1ZejhMWWZQTEd0OXlYSk9rU3JOZ0pwTGVxR0ZxV2FudzRPd2dyVXNNVWx2a3B4LytXRXlrSktYRzc5MXF1TGg5TTVadWlZVHhzbDlzc2NlcW8ydTBFOHJXN2ZPVklaK1RIOFBnMWlacjdaQkVyTU1BWU5FYnoyS1hNdCtBRmREMlhmNG1waGtWV2RGa3pENGdTb1pLVzdJL3NJWFdCTlJsekJaakdZQWxjb1pvR0ZBRFVib2ZMZWpYZTJGNHptbHI0NGFvdUg2TS91WHZlNDZhdkQxS0hjdzZwaVVQU003Tkl6OWN6QjhOMmZxZHRZODB0dzNCSnRxWlRJSnFTTHpLcGF0bUpEY2JwZ2hLNzhodE1lRE9yMndCQjM1bFU4b1UwcXpKSUd2cGh2YVdWbFJQMmR5a0RNaHhjMkx5UGwwOFcrTzNxUW85eDVOaXR1Tld3b0dOWjBoQWU0eVRaMm5VeU9EOFZjM28wazZrNmZkYVczOVZHSjloV2I3YmU1bUhUQmlra3VPQzJaWjMrTUwvay81SnBqbGR5MXpzSTBYWE9RcGFxT0Z0eVBhSU1LaUluenhTTUFkd0JpdVB3cGtkaEJBSXgiLCJtYWMiOiIzNzc5NGExN2I4ZTI4N2YxMmU3YTc3MTk3NDNjZTE0MGJiZWYyMGM4MzA4NWQ3NjU5NTBiOWFmOTcxMjE5Yzc0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1678522581\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-745007473 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"87 characters\">http://localhost/branch-cash-management?end_date=&amp;start_date=&amp;status=all&amp;warehouse_id=8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-745007473\", {\"maxDepth\":0})</script>\n"}}