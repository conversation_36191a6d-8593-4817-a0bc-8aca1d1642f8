<?php
// This file was auto-generated from sdk-root/src/data/cleanrooms/2022-02-17/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2022-02-17', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'cleanrooms', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS Clean Rooms Service', 'serviceId' => 'CleanRooms', 'signatureVersion' => 'v4', 'signingName' => 'cleanrooms', 'uid' => 'cleanrooms-2022-02-17', ], 'operations' => [ 'BatchGetCollaborationAnalysisTemplate' => [ 'name' => 'BatchGetCollaborationAnalysisTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/collaborations/{collaborationIdentifier}/batch-analysistemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetCollaborationAnalysisTemplateInput', ], 'output' => [ 'shape' => 'BatchGetCollaborationAnalysisTemplateOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'BatchGetSchema' => [ 'name' => 'BatchGetSchema', 'http' => [ 'method' => 'POST', 'requestUri' => '/collaborations/{collaborationIdentifier}/batch-schema', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetSchemaInput', ], 'output' => [ 'shape' => 'BatchGetSchemaOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'BatchGetSchemaAnalysisRule' => [ 'name' => 'BatchGetSchemaAnalysisRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/collaborations/{collaborationIdentifier}/batch-schema-analysis-rule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetSchemaAnalysisRuleInput', ], 'output' => [ 'shape' => 'BatchGetSchemaAnalysisRuleOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateAnalysisTemplate' => [ 'name' => 'CreateAnalysisTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/memberships/{membershipIdentifier}/analysistemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAnalysisTemplateInput', ], 'output' => [ 'shape' => 'CreateAnalysisTemplateOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateCollaboration' => [ 'name' => 'CreateCollaboration', 'http' => [ 'method' => 'POST', 'requestUri' => '/collaborations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateCollaborationInput', ], 'output' => [ 'shape' => 'CreateCollaborationOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateConfiguredAudienceModelAssociation' => [ 'name' => 'CreateConfiguredAudienceModelAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/memberships/{membershipIdentifier}/configuredaudiencemodelassociations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateConfiguredAudienceModelAssociationInput', ], 'output' => [ 'shape' => 'CreateConfiguredAudienceModelAssociationOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateConfiguredTable' => [ 'name' => 'CreateConfiguredTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/configuredTables', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateConfiguredTableInput', ], 'output' => [ 'shape' => 'CreateConfiguredTableOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateConfiguredTableAnalysisRule' => [ 'name' => 'CreateConfiguredTableAnalysisRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/configuredTables/{configuredTableIdentifier}/analysisRule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateConfiguredTableAnalysisRuleInput', ], 'output' => [ 'shape' => 'CreateConfiguredTableAnalysisRuleOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateConfiguredTableAssociation' => [ 'name' => 'CreateConfiguredTableAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/memberships/{membershipIdentifier}/configuredTableAssociations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateConfiguredTableAssociationInput', ], 'output' => [ 'shape' => 'CreateConfiguredTableAssociationOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateConfiguredTableAssociationAnalysisRule' => [ 'name' => 'CreateConfiguredTableAssociationAnalysisRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/memberships/{membershipIdentifier}/configuredTableAssociations/{configuredTableAssociationIdentifier}/analysisRule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateConfiguredTableAssociationAnalysisRuleInput', ], 'output' => [ 'shape' => 'CreateConfiguredTableAssociationAnalysisRuleOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateIdMappingTable' => [ 'name' => 'CreateIdMappingTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/memberships/{membershipIdentifier}/idmappingtables', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateIdMappingTableInput', ], 'output' => [ 'shape' => 'CreateIdMappingTableOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateIdNamespaceAssociation' => [ 'name' => 'CreateIdNamespaceAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/memberships/{membershipIdentifier}/idnamespaceassociations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateIdNamespaceAssociationInput', ], 'output' => [ 'shape' => 'CreateIdNamespaceAssociationOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateMembership' => [ 'name' => 'CreateMembership', 'http' => [ 'method' => 'POST', 'requestUri' => '/memberships', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMembershipInput', ], 'output' => [ 'shape' => 'CreateMembershipOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreatePrivacyBudgetTemplate' => [ 'name' => 'CreatePrivacyBudgetTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/memberships/{membershipIdentifier}/privacybudgettemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePrivacyBudgetTemplateInput', ], 'output' => [ 'shape' => 'CreatePrivacyBudgetTemplateOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteAnalysisTemplate' => [ 'name' => 'DeleteAnalysisTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/memberships/{membershipIdentifier}/analysistemplates/{analysisTemplateIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAnalysisTemplateInput', ], 'output' => [ 'shape' => 'DeleteAnalysisTemplateOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteCollaboration' => [ 'name' => 'DeleteCollaboration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/collaborations/{collaborationIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteCollaborationInput', ], 'output' => [ 'shape' => 'DeleteCollaborationOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteConfiguredAudienceModelAssociation' => [ 'name' => 'DeleteConfiguredAudienceModelAssociation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/memberships/{membershipIdentifier}/configuredaudiencemodelassociations/{configuredAudienceModelAssociationIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteConfiguredAudienceModelAssociationInput', ], 'output' => [ 'shape' => 'DeleteConfiguredAudienceModelAssociationOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteConfiguredTable' => [ 'name' => 'DeleteConfiguredTable', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/configuredTables/{configuredTableIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteConfiguredTableInput', ], 'output' => [ 'shape' => 'DeleteConfiguredTableOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteConfiguredTableAnalysisRule' => [ 'name' => 'DeleteConfiguredTableAnalysisRule', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/configuredTables/{configuredTableIdentifier}/analysisRule/{analysisRuleType}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteConfiguredTableAnalysisRuleInput', ], 'output' => [ 'shape' => 'DeleteConfiguredTableAnalysisRuleOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteConfiguredTableAssociation' => [ 'name' => 'DeleteConfiguredTableAssociation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/memberships/{membershipIdentifier}/configuredTableAssociations/{configuredTableAssociationIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteConfiguredTableAssociationInput', ], 'output' => [ 'shape' => 'DeleteConfiguredTableAssociationOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteConfiguredTableAssociationAnalysisRule' => [ 'name' => 'DeleteConfiguredTableAssociationAnalysisRule', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/memberships/{membershipIdentifier}/configuredTableAssociations/{configuredTableAssociationIdentifier}/analysisRule/{analysisRuleType}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteConfiguredTableAssociationAnalysisRuleInput', ], 'output' => [ 'shape' => 'DeleteConfiguredTableAssociationAnalysisRuleOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteIdMappingTable' => [ 'name' => 'DeleteIdMappingTable', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/memberships/{membershipIdentifier}/idmappingtables/{idMappingTableIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteIdMappingTableInput', ], 'output' => [ 'shape' => 'DeleteIdMappingTableOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteIdNamespaceAssociation' => [ 'name' => 'DeleteIdNamespaceAssociation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/memberships/{membershipIdentifier}/idnamespaceassociations/{idNamespaceAssociationIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteIdNamespaceAssociationInput', ], 'output' => [ 'shape' => 'DeleteIdNamespaceAssociationOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteMember' => [ 'name' => 'DeleteMember', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/collaborations/{collaborationIdentifier}/member/{accountId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteMemberInput', ], 'output' => [ 'shape' => 'DeleteMemberOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteMembership' => [ 'name' => 'DeleteMembership', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/memberships/{membershipIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteMembershipInput', ], 'output' => [ 'shape' => 'DeleteMembershipOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeletePrivacyBudgetTemplate' => [ 'name' => 'DeletePrivacyBudgetTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/memberships/{membershipIdentifier}/privacybudgettemplates/{privacyBudgetTemplateIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeletePrivacyBudgetTemplateInput', ], 'output' => [ 'shape' => 'DeletePrivacyBudgetTemplateOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'GetAnalysisTemplate' => [ 'name' => 'GetAnalysisTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/analysistemplates/{analysisTemplateIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAnalysisTemplateInput', ], 'output' => [ 'shape' => 'GetAnalysisTemplateOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetCollaboration' => [ 'name' => 'GetCollaboration', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCollaborationInput', ], 'output' => [ 'shape' => 'GetCollaborationOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetCollaborationAnalysisTemplate' => [ 'name' => 'GetCollaborationAnalysisTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/analysistemplates/{analysisTemplateArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCollaborationAnalysisTemplateInput', ], 'output' => [ 'shape' => 'GetCollaborationAnalysisTemplateOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetCollaborationConfiguredAudienceModelAssociation' => [ 'name' => 'GetCollaborationConfiguredAudienceModelAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/configuredaudiencemodelassociations/{configuredAudienceModelAssociationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCollaborationConfiguredAudienceModelAssociationInput', ], 'output' => [ 'shape' => 'GetCollaborationConfiguredAudienceModelAssociationOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetCollaborationIdNamespaceAssociation' => [ 'name' => 'GetCollaborationIdNamespaceAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/idnamespaceassociations/{idNamespaceAssociationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCollaborationIdNamespaceAssociationInput', ], 'output' => [ 'shape' => 'GetCollaborationIdNamespaceAssociationOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetCollaborationPrivacyBudgetTemplate' => [ 'name' => 'GetCollaborationPrivacyBudgetTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/privacybudgettemplates/{privacyBudgetTemplateIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCollaborationPrivacyBudgetTemplateInput', ], 'output' => [ 'shape' => 'GetCollaborationPrivacyBudgetTemplateOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetConfiguredAudienceModelAssociation' => [ 'name' => 'GetConfiguredAudienceModelAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/configuredaudiencemodelassociations/{configuredAudienceModelAssociationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConfiguredAudienceModelAssociationInput', ], 'output' => [ 'shape' => 'GetConfiguredAudienceModelAssociationOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetConfiguredTable' => [ 'name' => 'GetConfiguredTable', 'http' => [ 'method' => 'GET', 'requestUri' => '/configuredTables/{configuredTableIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConfiguredTableInput', ], 'output' => [ 'shape' => 'GetConfiguredTableOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetConfiguredTableAnalysisRule' => [ 'name' => 'GetConfiguredTableAnalysisRule', 'http' => [ 'method' => 'GET', 'requestUri' => '/configuredTables/{configuredTableIdentifier}/analysisRule/{analysisRuleType}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConfiguredTableAnalysisRuleInput', ], 'output' => [ 'shape' => 'GetConfiguredTableAnalysisRuleOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetConfiguredTableAssociation' => [ 'name' => 'GetConfiguredTableAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/configuredTableAssociations/{configuredTableAssociationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConfiguredTableAssociationInput', ], 'output' => [ 'shape' => 'GetConfiguredTableAssociationOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetConfiguredTableAssociationAnalysisRule' => [ 'name' => 'GetConfiguredTableAssociationAnalysisRule', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/configuredTableAssociations/{configuredTableAssociationIdentifier}/analysisRule/{analysisRuleType}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConfiguredTableAssociationAnalysisRuleInput', ], 'output' => [ 'shape' => 'GetConfiguredTableAssociationAnalysisRuleOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetIdMappingTable' => [ 'name' => 'GetIdMappingTable', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/idmappingtables/{idMappingTableIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIdMappingTableInput', ], 'output' => [ 'shape' => 'GetIdMappingTableOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetIdNamespaceAssociation' => [ 'name' => 'GetIdNamespaceAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/idnamespaceassociations/{idNamespaceAssociationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIdNamespaceAssociationInput', ], 'output' => [ 'shape' => 'GetIdNamespaceAssociationOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetMembership' => [ 'name' => 'GetMembership', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMembershipInput', ], 'output' => [ 'shape' => 'GetMembershipOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetPrivacyBudgetTemplate' => [ 'name' => 'GetPrivacyBudgetTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/privacybudgettemplates/{privacyBudgetTemplateIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPrivacyBudgetTemplateInput', ], 'output' => [ 'shape' => 'GetPrivacyBudgetTemplateOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetProtectedQuery' => [ 'name' => 'GetProtectedQuery', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/protectedQueries/{protectedQueryIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetProtectedQueryInput', ], 'output' => [ 'shape' => 'GetProtectedQueryOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetSchema' => [ 'name' => 'GetSchema', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/schemas/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSchemaInput', ], 'output' => [ 'shape' => 'GetSchemaOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetSchemaAnalysisRule' => [ 'name' => 'GetSchemaAnalysisRule', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/schemas/{name}/analysisRule/{type}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSchemaAnalysisRuleInput', ], 'output' => [ 'shape' => 'GetSchemaAnalysisRuleOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAnalysisTemplates' => [ 'name' => 'ListAnalysisTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/analysistemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAnalysisTemplatesInput', ], 'output' => [ 'shape' => 'ListAnalysisTemplatesOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListCollaborationAnalysisTemplates' => [ 'name' => 'ListCollaborationAnalysisTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/analysistemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCollaborationAnalysisTemplatesInput', ], 'output' => [ 'shape' => 'ListCollaborationAnalysisTemplatesOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListCollaborationConfiguredAudienceModelAssociations' => [ 'name' => 'ListCollaborationConfiguredAudienceModelAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/configuredaudiencemodelassociations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCollaborationConfiguredAudienceModelAssociationsInput', ], 'output' => [ 'shape' => 'ListCollaborationConfiguredAudienceModelAssociationsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListCollaborationIdNamespaceAssociations' => [ 'name' => 'ListCollaborationIdNamespaceAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/idnamespaceassociations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCollaborationIdNamespaceAssociationsInput', ], 'output' => [ 'shape' => 'ListCollaborationIdNamespaceAssociationsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListCollaborationPrivacyBudgetTemplates' => [ 'name' => 'ListCollaborationPrivacyBudgetTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/privacybudgettemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCollaborationPrivacyBudgetTemplatesInput', ], 'output' => [ 'shape' => 'ListCollaborationPrivacyBudgetTemplatesOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListCollaborationPrivacyBudgets' => [ 'name' => 'ListCollaborationPrivacyBudgets', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/privacybudgets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCollaborationPrivacyBudgetsInput', ], 'output' => [ 'shape' => 'ListCollaborationPrivacyBudgetsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListCollaborations' => [ 'name' => 'ListCollaborations', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCollaborationsInput', ], 'output' => [ 'shape' => 'ListCollaborationsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListConfiguredAudienceModelAssociations' => [ 'name' => 'ListConfiguredAudienceModelAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/configuredaudiencemodelassociations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConfiguredAudienceModelAssociationsInput', ], 'output' => [ 'shape' => 'ListConfiguredAudienceModelAssociationsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListConfiguredTableAssociations' => [ 'name' => 'ListConfiguredTableAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/configuredTableAssociations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConfiguredTableAssociationsInput', ], 'output' => [ 'shape' => 'ListConfiguredTableAssociationsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListConfiguredTables' => [ 'name' => 'ListConfiguredTables', 'http' => [ 'method' => 'GET', 'requestUri' => '/configuredTables', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConfiguredTablesInput', ], 'output' => [ 'shape' => 'ListConfiguredTablesOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListIdMappingTables' => [ 'name' => 'ListIdMappingTables', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/idmappingtables', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIdMappingTablesInput', ], 'output' => [ 'shape' => 'ListIdMappingTablesOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListIdNamespaceAssociations' => [ 'name' => 'ListIdNamespaceAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/idnamespaceassociations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIdNamespaceAssociationsInput', ], 'output' => [ 'shape' => 'ListIdNamespaceAssociationsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListMembers' => [ 'name' => 'ListMembers', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/members', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMembersInput', ], 'output' => [ 'shape' => 'ListMembersOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListMemberships' => [ 'name' => 'ListMemberships', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMembershipsInput', ], 'output' => [ 'shape' => 'ListMembershipsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListPrivacyBudgetTemplates' => [ 'name' => 'ListPrivacyBudgetTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/privacybudgettemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPrivacyBudgetTemplatesInput', ], 'output' => [ 'shape' => 'ListPrivacyBudgetTemplatesOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListPrivacyBudgets' => [ 'name' => 'ListPrivacyBudgets', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/privacybudgets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPrivacyBudgetsInput', ], 'output' => [ 'shape' => 'ListPrivacyBudgetsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListProtectedQueries' => [ 'name' => 'ListProtectedQueries', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/protectedQueries', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListProtectedQueriesInput', ], 'output' => [ 'shape' => 'ListProtectedQueriesOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListSchemas' => [ 'name' => 'ListSchemas', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/schemas', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSchemasInput', ], 'output' => [ 'shape' => 'ListSchemasOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceInput', ], 'output' => [ 'shape' => 'ListTagsForResourceOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'PopulateIdMappingTable' => [ 'name' => 'PopulateIdMappingTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/memberships/{membershipIdentifier}/idmappingtables/{idMappingTableIdentifier}/populate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PopulateIdMappingTableInput', ], 'output' => [ 'shape' => 'PopulateIdMappingTableOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PreviewPrivacyImpact' => [ 'name' => 'PreviewPrivacyImpact', 'http' => [ 'method' => 'POST', 'requestUri' => '/memberships/{membershipIdentifier}/previewprivacyimpact', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PreviewPrivacyImpactInput', ], 'output' => [ 'shape' => 'PreviewPrivacyImpactOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartProtectedQuery' => [ 'name' => 'StartProtectedQuery', 'http' => [ 'method' => 'POST', 'requestUri' => '/memberships/{membershipIdentifier}/protectedQueries', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartProtectedQueryInput', ], 'output' => [ 'shape' => 'StartProtectedQueryOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceInput', ], 'output' => [ 'shape' => 'TagResourceOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceInput', ], 'output' => [ 'shape' => 'UntagResourceOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateAnalysisTemplate' => [ 'name' => 'UpdateAnalysisTemplate', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/memberships/{membershipIdentifier}/analysistemplates/{analysisTemplateIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAnalysisTemplateInput', ], 'output' => [ 'shape' => 'UpdateAnalysisTemplateOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateCollaboration' => [ 'name' => 'UpdateCollaboration', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/collaborations/{collaborationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCollaborationInput', ], 'output' => [ 'shape' => 'UpdateCollaborationOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateConfiguredAudienceModelAssociation' => [ 'name' => 'UpdateConfiguredAudienceModelAssociation', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/memberships/{membershipIdentifier}/configuredaudiencemodelassociations/{configuredAudienceModelAssociationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateConfiguredAudienceModelAssociationInput', ], 'output' => [ 'shape' => 'UpdateConfiguredAudienceModelAssociationOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateConfiguredTable' => [ 'name' => 'UpdateConfiguredTable', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/configuredTables/{configuredTableIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateConfiguredTableInput', ], 'output' => [ 'shape' => 'UpdateConfiguredTableOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateConfiguredTableAnalysisRule' => [ 'name' => 'UpdateConfiguredTableAnalysisRule', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/configuredTables/{configuredTableIdentifier}/analysisRule/{analysisRuleType}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateConfiguredTableAnalysisRuleInput', ], 'output' => [ 'shape' => 'UpdateConfiguredTableAnalysisRuleOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateConfiguredTableAssociation' => [ 'name' => 'UpdateConfiguredTableAssociation', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/memberships/{membershipIdentifier}/configuredTableAssociations/{configuredTableAssociationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateConfiguredTableAssociationInput', ], 'output' => [ 'shape' => 'UpdateConfiguredTableAssociationOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateConfiguredTableAssociationAnalysisRule' => [ 'name' => 'UpdateConfiguredTableAssociationAnalysisRule', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/memberships/{membershipIdentifier}/configuredTableAssociations/{configuredTableAssociationIdentifier}/analysisRule/{analysisRuleType}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateConfiguredTableAssociationAnalysisRuleInput', ], 'output' => [ 'shape' => 'UpdateConfiguredTableAssociationAnalysisRuleOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateIdMappingTable' => [ 'name' => 'UpdateIdMappingTable', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/memberships/{membershipIdentifier}/idmappingtables/{idMappingTableIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateIdMappingTableInput', ], 'output' => [ 'shape' => 'UpdateIdMappingTableOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateIdNamespaceAssociation' => [ 'name' => 'UpdateIdNamespaceAssociation', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/memberships/{membershipIdentifier}/idnamespaceassociations/{idNamespaceAssociationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateIdNamespaceAssociationInput', ], 'output' => [ 'shape' => 'UpdateIdNamespaceAssociationOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateMembership' => [ 'name' => 'UpdateMembership', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/memberships/{membershipIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMembershipInput', ], 'output' => [ 'shape' => 'UpdateMembershipOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdatePrivacyBudgetTemplate' => [ 'name' => 'UpdatePrivacyBudgetTemplate', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/memberships/{membershipIdentifier}/privacybudgettemplates/{privacyBudgetTemplateIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePrivacyBudgetTemplateInput', ], 'output' => [ 'shape' => 'UpdatePrivacyBudgetTemplateOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateProtectedQuery' => [ 'name' => 'UpdateProtectedQuery', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/memberships/{membershipIdentifier}/protectedQueries/{protectedQueryIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateProtectedQueryInput', ], 'output' => [ 'shape' => 'UpdateProtectedQueryOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'AccessDeniedExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccessDeniedExceptionReason' => [ 'type' => 'string', 'enum' => [ 'INSUFFICIENT_PERMISSIONS', ], ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '\\d+', ], 'AdditionalAnalyses' => [ 'type' => 'string', 'enum' => [ 'ALLOWED', 'REQUIRED', 'NOT_ALLOWED', ], ], 'AdditionalAnalysesResourceArn' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => 'arn:aws:cleanrooms:[\\w]{2}-[\\w]{4,9}-[\\d]:([\\d]{12}|\\*):membership/[\\*\\d\\w-]+/configuredaudiencemodelassociation/[\\*\\d\\w-]+', ], 'AggregateColumn' => [ 'type' => 'structure', 'required' => [ 'columnNames', 'function', ], 'members' => [ 'columnNames' => [ 'shape' => 'AggregateColumnColumnNamesList', ], 'function' => [ 'shape' => 'AggregateFunctionName', ], ], ], 'AggregateColumnColumnNamesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalysisRuleColumnName', ], 'min' => 1, ], 'AggregateFunctionName' => [ 'type' => 'string', 'enum' => [ 'SUM', 'SUM_DISTINCT', 'COUNT', 'COUNT_DISTINCT', 'AVG', ], ], 'AggregationConstraint' => [ 'type' => 'structure', 'required' => [ 'columnName', 'minimum', 'type', ], 'members' => [ 'columnName' => [ 'shape' => 'AnalysisRuleColumnName', ], 'minimum' => [ 'shape' => 'AggregationConstraintMinimumInteger', ], 'type' => [ 'shape' => 'AggregationType', ], ], ], 'AggregationConstraintMinimumInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100000, 'min' => 2, ], 'AggregationConstraints' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregationConstraint', ], 'min' => 1, ], 'AggregationType' => [ 'type' => 'string', 'enum' => [ 'COUNT_DISTINCT', ], ], 'AllowedAdditionalAnalyses' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdditionalAnalysesResourceArn', ], 'max' => 25, 'min' => 0, ], 'AllowedColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnName', ], 'max' => 225, 'min' => 1, ], 'AllowedResultReceivers' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], ], 'AnalysisFormat' => [ 'type' => 'string', 'enum' => [ 'SQL', ], ], 'AnalysisMethod' => [ 'type' => 'string', 'enum' => [ 'DIRECT_QUERY', ], ], 'AnalysisParameter' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'name' => [ 'shape' => 'ParameterName', ], 'type' => [ 'shape' => 'ParameterType', ], 'defaultValue' => [ 'shape' => 'ParameterValue', ], ], 'sensitive' => true, ], 'AnalysisParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalysisParameter', ], 'max' => 10, 'min' => 0, ], 'AnalysisRule' => [ 'type' => 'structure', 'required' => [ 'collaborationId', 'type', 'name', 'createTime', 'updateTime', 'policy', ], 'members' => [ 'collaborationId' => [ 'shape' => 'CollaborationIdentifier', ], 'type' => [ 'shape' => 'AnalysisRuleType', ], 'name' => [ 'shape' => 'TableAlias', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'policy' => [ 'shape' => 'AnalysisRulePolicy', ], ], ], 'AnalysisRuleAggregation' => [ 'type' => 'structure', 'required' => [ 'aggregateColumns', 'joinColumns', 'dimensionColumns', 'scalarFunctions', 'outputConstraints', ], 'members' => [ 'aggregateColumns' => [ 'shape' => 'AnalysisRuleAggregationAggregateColumnsList', ], 'joinColumns' => [ 'shape' => 'AnalysisRuleColumnList', ], 'joinRequired' => [ 'shape' => 'JoinRequiredOption', ], 'allowedJoinOperators' => [ 'shape' => 'JoinOperatorsList', ], 'dimensionColumns' => [ 'shape' => 'AnalysisRuleColumnList', ], 'scalarFunctions' => [ 'shape' => 'ScalarFunctionsList', ], 'outputConstraints' => [ 'shape' => 'AggregationConstraints', ], 'additionalAnalyses' => [ 'shape' => 'AdditionalAnalyses', ], ], ], 'AnalysisRuleAggregationAggregateColumnsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregateColumn', ], 'min' => 1, ], 'AnalysisRuleColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalysisRuleColumnName', ], ], 'AnalysisRuleColumnName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '[a-z0-9_](([a-z0-9_ ]+-)*([a-z0-9_ ]+))?', ], 'AnalysisRuleCustom' => [ 'type' => 'structure', 'required' => [ 'allowedAnalyses', ], 'members' => [ 'allowedAnalyses' => [ 'shape' => 'AnalysisRuleCustomAllowedAnalysesList', ], 'allowedAnalysisProviders' => [ 'shape' => 'AnalysisRuleCustomAllowedAnalysisProvidersList', ], 'additionalAnalyses' => [ 'shape' => 'AdditionalAnalyses', ], 'disallowedOutputColumns' => [ 'shape' => 'AnalysisRuleColumnList', ], 'differentialPrivacy' => [ 'shape' => 'DifferentialPrivacyConfiguration', ], ], ], 'AnalysisRuleCustomAllowedAnalysesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalysisTemplateArnOrQueryWildcard', ], 'min' => 0, ], 'AnalysisRuleCustomAllowedAnalysisProvidersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], 'min' => 0, ], 'AnalysisRuleIdMappingTable' => [ 'type' => 'structure', 'required' => [ 'joinColumns', 'queryConstraints', ], 'members' => [ 'joinColumns' => [ 'shape' => 'AnalysisRuleIdMappingTableJoinColumnsList', ], 'queryConstraints' => [ 'shape' => 'QueryConstraintList', ], 'dimensionColumns' => [ 'shape' => 'AnalysisRuleColumnList', ], ], ], 'AnalysisRuleIdMappingTableJoinColumnsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalysisRuleColumnName', ], 'max' => 2, 'min' => 2, ], 'AnalysisRuleList' => [ 'type' => 'structure', 'required' => [ 'joinColumns', 'listColumns', ], 'members' => [ 'joinColumns' => [ 'shape' => 'AnalysisRuleListJoinColumnsList', ], 'allowedJoinOperators' => [ 'shape' => 'JoinOperatorsList', ], 'listColumns' => [ 'shape' => 'AnalysisRuleColumnList', ], 'additionalAnalyses' => [ 'shape' => 'AdditionalAnalyses', ], ], ], 'AnalysisRuleListJoinColumnsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalysisRuleColumnName', ], 'min' => 1, ], 'AnalysisRulePolicy' => [ 'type' => 'structure', 'members' => [ 'v1' => [ 'shape' => 'AnalysisRulePolicyV1', ], ], 'union' => true, ], 'AnalysisRulePolicyV1' => [ 'type' => 'structure', 'members' => [ 'list' => [ 'shape' => 'AnalysisRuleList', ], 'aggregation' => [ 'shape' => 'AnalysisRuleAggregation', ], 'custom' => [ 'shape' => 'AnalysisRuleCustom', ], 'idMappingTable' => [ 'shape' => 'AnalysisRuleIdMappingTable', ], ], 'union' => true, ], 'AnalysisRuleType' => [ 'type' => 'string', 'enum' => [ 'AGGREGATION', 'LIST', 'CUSTOM', 'ID_MAPPING_TABLE', ], ], 'AnalysisRuleTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalysisRuleType', ], ], 'AnalysisSchema' => [ 'type' => 'structure', 'members' => [ 'referencedTables' => [ 'shape' => 'QueryTables', ], ], ], 'AnalysisSource' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'AnalysisTemplateText', ], ], 'sensitive' => true, 'union' => true, ], 'AnalysisTemplate' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'collaborationId', 'collaborationArn', 'membershipId', 'membershipArn', 'name', 'createTime', 'updateTime', 'schema', 'format', 'source', ], 'members' => [ 'id' => [ 'shape' => 'AnalysisTemplateIdentifier', ], 'arn' => [ 'shape' => 'AnalysisTemplateArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'membershipId' => [ 'shape' => 'UUID', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'name' => [ 'shape' => 'ResourceAlias', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'schema' => [ 'shape' => 'AnalysisSchema', ], 'format' => [ 'shape' => 'AnalysisFormat', ], 'source' => [ 'shape' => 'AnalysisSource', ], 'analysisParameters' => [ 'shape' => 'AnalysisParameterList', ], 'validations' => [ 'shape' => 'AnalysisTemplateValidationStatusDetailList', ], ], ], 'AnalysisTemplateArn' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'pattern' => 'arn:aws:cleanrooms:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:membership/[\\d\\w-]+/analysistemplate/[\\d\\w-]+', ], 'AnalysisTemplateArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalysisTemplateArn', ], 'max' => 10, 'min' => 1, ], 'AnalysisTemplateArnOrQueryWildcard' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'pattern' => '(ANY_QUERY|arn:aws:cleanrooms:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:membership/[\\d\\w-]+/analysistemplate/[\\d\\w-]+)', ], 'AnalysisTemplateIdentifier' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'AnalysisTemplateSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createTime', 'id', 'name', 'updateTime', 'membershipArn', 'membershipId', 'collaborationArn', 'collaborationId', ], 'members' => [ 'arn' => [ 'shape' => 'AnalysisTemplateArn', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'AnalysisTemplateIdentifier', ], 'name' => [ 'shape' => 'ResourceAlias', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], 'membershipId' => [ 'shape' => 'UUID', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'AnalysisTemplateSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalysisTemplateSummary', ], ], 'AnalysisTemplateText' => [ 'type' => 'string', 'max' => 90000, 'min' => 0, ], 'AnalysisTemplateValidationStatus' => [ 'type' => 'string', 'enum' => [ 'VALID', 'INVALID', 'UNABLE_TO_VALIDATE', ], ], 'AnalysisTemplateValidationStatusDetail' => [ 'type' => 'structure', 'required' => [ 'type', 'status', ], 'members' => [ 'type' => [ 'shape' => 'AnalysisTemplateValidationType', ], 'status' => [ 'shape' => 'AnalysisTemplateValidationStatus', ], 'reasons' => [ 'shape' => 'AnalysisTemplateValidationStatusReasonList', ], ], ], 'AnalysisTemplateValidationStatusDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalysisTemplateValidationStatusDetail', ], ], 'AnalysisTemplateValidationStatusReason' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], ], 'AnalysisTemplateValidationStatusReasonList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalysisTemplateValidationStatusReason', ], ], 'AnalysisTemplateValidationType' => [ 'type' => 'string', 'enum' => [ 'DIFFERENTIAL_PRIVACY', ], ], 'AnalysisType' => [ 'type' => 'string', 'enum' => [ 'DIRECT_ANALYSIS', 'ADDITIONAL_ANALYSIS', ], ], 'BatchGetCollaborationAnalysisTemplateError' => [ 'type' => 'structure', 'required' => [ 'arn', 'code', 'message', ], 'members' => [ 'arn' => [ 'shape' => 'AnalysisTemplateArn', ], 'code' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'BatchGetCollaborationAnalysisTemplateErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetCollaborationAnalysisTemplateError', ], 'max' => 10, 'min' => 0, ], 'BatchGetCollaborationAnalysisTemplateInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', 'analysisTemplateArns', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'analysisTemplateArns' => [ 'shape' => 'AnalysisTemplateArnList', ], ], ], 'BatchGetCollaborationAnalysisTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'collaborationAnalysisTemplates', 'errors', ], 'members' => [ 'collaborationAnalysisTemplates' => [ 'shape' => 'CollaborationAnalysisTemplateList', ], 'errors' => [ 'shape' => 'BatchGetCollaborationAnalysisTemplateErrorList', ], ], ], 'BatchGetSchemaAnalysisRuleError' => [ 'type' => 'structure', 'required' => [ 'name', 'type', 'code', 'message', ], 'members' => [ 'name' => [ 'shape' => 'TableAlias', ], 'type' => [ 'shape' => 'AnalysisRuleType', ], 'code' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'BatchGetSchemaAnalysisRuleErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetSchemaAnalysisRuleError', ], 'max' => 25, 'min' => 0, ], 'BatchGetSchemaAnalysisRuleInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', 'schemaAnalysisRuleRequests', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'schemaAnalysisRuleRequests' => [ 'shape' => 'SchemaAnalysisRuleRequestList', ], ], ], 'BatchGetSchemaAnalysisRuleOutput' => [ 'type' => 'structure', 'required' => [ 'analysisRules', 'errors', ], 'members' => [ 'analysisRules' => [ 'shape' => 'SchemaAnalysisRuleList', ], 'errors' => [ 'shape' => 'BatchGetSchemaAnalysisRuleErrorList', ], ], ], 'BatchGetSchemaError' => [ 'type' => 'structure', 'required' => [ 'name', 'code', 'message', ], 'members' => [ 'name' => [ 'shape' => 'TableAlias', ], 'code' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'BatchGetSchemaErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetSchemaError', ], 'max' => 25, 'min' => 0, ], 'BatchGetSchemaInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', 'names', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'names' => [ 'shape' => 'TableAliasList', ], ], ], 'BatchGetSchemaOutput' => [ 'type' => 'structure', 'required' => [ 'schemas', 'errors', ], 'members' => [ 'schemas' => [ 'shape' => 'SchemaList', ], 'errors' => [ 'shape' => 'BatchGetSchemaErrorList', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CleanroomsArn' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => 'arn:aws:cleanrooms:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:[\\d\\w/-]+', ], 'Collaboration' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'name', 'creatorAccountId', 'creatorDisplayName', 'createTime', 'updateTime', 'memberStatus', 'queryLogStatus', ], 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'arn' => [ 'shape' => 'CollaborationArn', ], 'name' => [ 'shape' => 'CollaborationName', ], 'description' => [ 'shape' => 'CollaborationDescription', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], 'creatorDisplayName' => [ 'shape' => 'DisplayName', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'memberStatus' => [ 'shape' => 'MemberStatus', ], 'membershipId' => [ 'shape' => 'UUID', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], 'dataEncryptionMetadata' => [ 'shape' => 'DataEncryptionMetadata', ], 'queryLogStatus' => [ 'shape' => 'CollaborationQueryLogStatus', ], ], ], 'CollaborationAnalysisTemplate' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'collaborationId', 'collaborationArn', 'creatorAccountId', 'name', 'createTime', 'updateTime', 'schema', 'format', 'source', ], 'members' => [ 'id' => [ 'shape' => 'AnalysisTemplateIdentifier', ], 'arn' => [ 'shape' => 'AnalysisTemplateArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], 'name' => [ 'shape' => 'ResourceAlias', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'schema' => [ 'shape' => 'AnalysisSchema', ], 'format' => [ 'shape' => 'AnalysisFormat', ], 'source' => [ 'shape' => 'AnalysisSource', ], 'analysisParameters' => [ 'shape' => 'AnalysisParameterList', ], 'validations' => [ 'shape' => 'AnalysisTemplateValidationStatusDetailList', ], ], ], 'CollaborationAnalysisTemplateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollaborationAnalysisTemplate', ], 'max' => 10, 'min' => 0, ], 'CollaborationAnalysisTemplateSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createTime', 'id', 'name', 'updateTime', 'collaborationArn', 'collaborationId', 'creatorAccountId', ], 'members' => [ 'arn' => [ 'shape' => 'AnalysisTemplateArn', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'AnalysisTemplateIdentifier', ], 'name' => [ 'shape' => 'ResourceAlias', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'CollaborationAnalysisTemplateSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollaborationAnalysisTemplateSummary', ], ], 'CollaborationArn' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => 'arn:aws:[\\w]+:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:collaboration/[\\d\\w-]+', ], 'CollaborationConfiguredAudienceModelAssociation' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'collaborationId', 'collaborationArn', 'configuredAudienceModelArn', 'name', 'creatorAccountId', 'createTime', 'updateTime', ], 'members' => [ 'id' => [ 'shape' => 'ConfiguredAudienceModelAssociationIdentifier', ], 'arn' => [ 'shape' => 'ConfiguredAudienceModelAssociationArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'name' => [ 'shape' => 'ConfiguredAudienceModelAssociationName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CollaborationConfiguredAudienceModelAssociationSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createTime', 'id', 'name', 'updateTime', 'collaborationArn', 'collaborationId', 'creatorAccountId', ], 'members' => [ 'arn' => [ 'shape' => 'ConfiguredAudienceModelAssociationArn', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'ConfiguredAudienceModelAssociationIdentifier', ], 'name' => [ 'shape' => 'ConfiguredAudienceModelAssociationName', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'CollaborationConfiguredAudienceModelAssociationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollaborationConfiguredAudienceModelAssociationSummary', ], ], 'CollaborationDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '(?!\\s*$)[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t\\r\\n]*', ], 'CollaborationIdNamespaceAssociation' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'collaborationId', 'collaborationArn', 'name', 'creatorAccountId', 'createTime', 'updateTime', 'inputReferenceConfig', 'inputReferenceProperties', ], 'members' => [ 'id' => [ 'shape' => 'IdNamespaceAssociationIdentifier', ], 'arn' => [ 'shape' => 'IdNamespaceAssociationArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'name' => [ 'shape' => 'GenericResourceName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'inputReferenceConfig' => [ 'shape' => 'IdNamespaceAssociationInputReferenceConfig', ], 'inputReferenceProperties' => [ 'shape' => 'IdNamespaceAssociationInputReferenceProperties', ], 'idMappingConfig' => [ 'shape' => 'IdMappingConfig', ], ], ], 'CollaborationIdNamespaceAssociationSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createTime', 'id', 'updateTime', 'collaborationArn', 'collaborationId', 'creatorAccountId', 'inputReferenceConfig', 'name', 'inputReferenceProperties', ], 'members' => [ 'arn' => [ 'shape' => 'IdNamespaceAssociationArn', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'IdNamespaceAssociationIdentifier', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], 'inputReferenceConfig' => [ 'shape' => 'IdNamespaceAssociationInputReferenceConfig', ], 'name' => [ 'shape' => 'GenericResourceName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'inputReferenceProperties' => [ 'shape' => 'IdNamespaceAssociationInputReferencePropertiesSummary', ], ], ], 'CollaborationIdNamespaceAssociationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollaborationIdNamespaceAssociationSummary', ], ], 'CollaborationIdentifier' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'CollaborationName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '(?!\\s*$)[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t]*', ], 'CollaborationPrivacyBudgetSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'privacyBudgetTemplateId', 'privacyBudgetTemplateArn', 'collaborationId', 'collaborationArn', 'creatorAccountId', 'type', 'createTime', 'updateTime', 'budget', ], 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'privacyBudgetTemplateId' => [ 'shape' => 'PrivacyBudgetTemplateIdentifier', ], 'privacyBudgetTemplateArn' => [ 'shape' => 'PrivacyBudgetTemplateArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], 'type' => [ 'shape' => 'PrivacyBudgetType', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'budget' => [ 'shape' => 'PrivacyBudget', ], ], ], 'CollaborationPrivacyBudgetSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollaborationPrivacyBudgetSummary', ], ], 'CollaborationPrivacyBudgetTemplate' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'collaborationId', 'collaborationArn', 'creatorAccountId', 'createTime', 'updateTime', 'privacyBudgetType', 'autoRefresh', 'parameters', ], 'members' => [ 'id' => [ 'shape' => 'PrivacyBudgetTemplateIdentifier', ], 'arn' => [ 'shape' => 'PrivacyBudgetTemplateArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'privacyBudgetType' => [ 'shape' => 'PrivacyBudgetType', ], 'autoRefresh' => [ 'shape' => 'PrivacyBudgetTemplateAutoRefresh', ], 'parameters' => [ 'shape' => 'PrivacyBudgetTemplateParametersOutput', ], ], ], 'CollaborationPrivacyBudgetTemplateSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'collaborationId', 'collaborationArn', 'creatorAccountId', 'privacyBudgetType', 'createTime', 'updateTime', ], 'members' => [ 'id' => [ 'shape' => 'PrivacyBudgetTemplateIdentifier', ], 'arn' => [ 'shape' => 'PrivacyBudgetTemplateArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], 'privacyBudgetType' => [ 'shape' => 'PrivacyBudgetType', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CollaborationPrivacyBudgetTemplateSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollaborationPrivacyBudgetTemplateSummary', ], ], 'CollaborationQueryLogStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'CollaborationSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'name', 'creatorAccountId', 'creatorDisplayName', 'createTime', 'updateTime', 'memberStatus', ], 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'arn' => [ 'shape' => 'CollaborationArn', ], 'name' => [ 'shape' => 'CollaborationName', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], 'creatorDisplayName' => [ 'shape' => 'DisplayName', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'memberStatus' => [ 'shape' => 'MemberStatus', ], 'membershipId' => [ 'shape' => 'UUID', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], ], ], 'CollaborationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollaborationSummary', ], ], 'Column' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'name' => [ 'shape' => 'ColumnName', ], 'type' => [ 'shape' => 'ColumnTypeString', ], ], ], 'ColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Column', ], ], 'ColumnName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '[a-z0-9_](([a-z0-9_ ]+-)*([a-z0-9_ ]+))?', ], 'ColumnTypeString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t]*', ], 'ConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'directAnalysisConfigurationDetails' => [ 'shape' => 'DirectAnalysisConfigurationDetails', ], ], 'union' => true, ], 'ConfiguredAudienceModelArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:configured-audience-model/[-a-zA-Z0-9_/.]+', ], 'ConfiguredAudienceModelAssociation' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'configuredAudienceModelArn', 'membershipId', 'membershipArn', 'collaborationId', 'collaborationArn', 'name', 'manageResourcePolicies', 'createTime', 'updateTime', ], 'members' => [ 'id' => [ 'shape' => 'ConfiguredAudienceModelAssociationIdentifier', ], 'arn' => [ 'shape' => 'ConfiguredAudienceModelAssociationArn', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'membershipId' => [ 'shape' => 'UUID', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'name' => [ 'shape' => 'ConfiguredAudienceModelAssociationName', ], 'manageResourcePolicies' => [ 'shape' => 'Boolean', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ConfiguredAudienceModelAssociationArn' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => 'arn:aws:cleanrooms:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:membership/[\\d\\w-]+/configuredaudiencemodelassociation/[\\d\\w-]+', ], 'ConfiguredAudienceModelAssociationIdentifier' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'ConfiguredAudienceModelAssociationName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '(?!\\s*$)[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t]*', ], 'ConfiguredAudienceModelAssociationSummary' => [ 'type' => 'structure', 'required' => [ 'membershipId', 'membershipArn', 'collaborationArn', 'collaborationId', 'createTime', 'updateTime', 'id', 'arn', 'name', 'configuredAudienceModelArn', ], 'members' => [ 'membershipId' => [ 'shape' => 'MembershipIdentifier', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'UUID', ], 'arn' => [ 'shape' => 'ConfiguredAudienceModelAssociationArn', ], 'name' => [ 'shape' => 'ConfiguredAudienceModelAssociationName', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'ConfiguredAudienceModelAssociationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfiguredAudienceModelAssociationSummary', ], ], 'ConfiguredTable' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'name', 'tableReference', 'createTime', 'updateTime', 'analysisRuleTypes', 'analysisMethod', 'allowedColumns', ], 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'arn' => [ 'shape' => 'ConfiguredTableArn', ], 'name' => [ 'shape' => 'DisplayName', ], 'description' => [ 'shape' => 'TableDescription', ], 'tableReference' => [ 'shape' => 'TableReference', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'analysisRuleTypes' => [ 'shape' => 'ConfiguredTableAnalysisRuleTypeList', ], 'analysisMethod' => [ 'shape' => 'AnalysisMethod', ], 'allowedColumns' => [ 'shape' => 'AllowedColumnList', ], ], ], 'ConfiguredTableAnalysisRule' => [ 'type' => 'structure', 'required' => [ 'configuredTableId', 'configuredTableArn', 'policy', 'type', 'createTime', 'updateTime', ], 'members' => [ 'configuredTableId' => [ 'shape' => 'UUID', ], 'configuredTableArn' => [ 'shape' => 'ConfiguredTableArn', ], 'policy' => [ 'shape' => 'ConfiguredTableAnalysisRulePolicy', ], 'type' => [ 'shape' => 'ConfiguredTableAnalysisRuleType', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ConfiguredTableAnalysisRulePolicy' => [ 'type' => 'structure', 'members' => [ 'v1' => [ 'shape' => 'ConfiguredTableAnalysisRulePolicyV1', ], ], 'union' => true, ], 'ConfiguredTableAnalysisRulePolicyV1' => [ 'type' => 'structure', 'members' => [ 'list' => [ 'shape' => 'AnalysisRuleList', ], 'aggregation' => [ 'shape' => 'AnalysisRuleAggregation', ], 'custom' => [ 'shape' => 'AnalysisRuleCustom', ], ], 'union' => true, ], 'ConfiguredTableAnalysisRuleType' => [ 'type' => 'string', 'enum' => [ 'AGGREGATION', 'LIST', 'CUSTOM', ], ], 'ConfiguredTableAnalysisRuleTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfiguredTableAnalysisRuleType', ], ], 'ConfiguredTableArn' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => 'arn:aws:[\\w]+:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:configuredtable/[\\d\\w-]+', ], 'ConfiguredTableAssociation' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'configuredTableId', 'configuredTableArn', 'membershipId', 'membershipArn', 'roleArn', 'name', 'createTime', 'updateTime', ], 'members' => [ 'arn' => [ 'shape' => 'ConfiguredTableAssociationArn', ], 'id' => [ 'shape' => 'UUID', ], 'configuredTableId' => [ 'shape' => 'UUID', ], 'configuredTableArn' => [ 'shape' => 'ConfiguredTableArn', ], 'membershipId' => [ 'shape' => 'UUID', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'name' => [ 'shape' => 'TableAlias', ], 'description' => [ 'shape' => 'TableDescription', ], 'analysisRuleTypes' => [ 'shape' => 'ConfiguredTableAssociationAnalysisRuleTypeList', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ConfiguredTableAssociationAnalysisRule' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'configuredTableAssociationId', 'configuredTableAssociationArn', 'policy', 'type', 'createTime', 'updateTime', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', ], 'configuredTableAssociationId' => [ 'shape' => 'ConfiguredTableAssociationIdentifier', ], 'configuredTableAssociationArn' => [ 'shape' => 'ConfiguredTableAssociationArn', ], 'policy' => [ 'shape' => 'ConfiguredTableAssociationAnalysisRulePolicy', ], 'type' => [ 'shape' => 'ConfiguredTableAssociationAnalysisRuleType', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ConfiguredTableAssociationAnalysisRuleAggregation' => [ 'type' => 'structure', 'members' => [ 'allowedResultReceivers' => [ 'shape' => 'AllowedResultReceivers', ], 'allowedAdditionalAnalyses' => [ 'shape' => 'AllowedAdditionalAnalyses', ], ], ], 'ConfiguredTableAssociationAnalysisRuleCustom' => [ 'type' => 'structure', 'members' => [ 'allowedResultReceivers' => [ 'shape' => 'AllowedResultReceivers', ], 'allowedAdditionalAnalyses' => [ 'shape' => 'AllowedAdditionalAnalyses', ], ], ], 'ConfiguredTableAssociationAnalysisRuleList' => [ 'type' => 'structure', 'members' => [ 'allowedResultReceivers' => [ 'shape' => 'AllowedResultReceivers', ], 'allowedAdditionalAnalyses' => [ 'shape' => 'AllowedAdditionalAnalyses', ], ], ], 'ConfiguredTableAssociationAnalysisRulePolicy' => [ 'type' => 'structure', 'members' => [ 'v1' => [ 'shape' => 'ConfiguredTableAssociationAnalysisRulePolicyV1', ], ], 'union' => true, ], 'ConfiguredTableAssociationAnalysisRulePolicyV1' => [ 'type' => 'structure', 'members' => [ 'list' => [ 'shape' => 'ConfiguredTableAssociationAnalysisRuleList', ], 'aggregation' => [ 'shape' => 'ConfiguredTableAssociationAnalysisRuleAggregation', ], 'custom' => [ 'shape' => 'ConfiguredTableAssociationAnalysisRuleCustom', ], ], 'union' => true, ], 'ConfiguredTableAssociationAnalysisRuleType' => [ 'type' => 'string', 'enum' => [ 'AGGREGATION', 'LIST', 'CUSTOM', ], ], 'ConfiguredTableAssociationAnalysisRuleTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfiguredTableAssociationAnalysisRuleType', ], ], 'ConfiguredTableAssociationArn' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => 'arn:aws:[\\w]+:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:configuredtableassociation/[\\d\\w-]+/[\\d\\w-]+', ], 'ConfiguredTableAssociationIdentifier' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'ConfiguredTableAssociationSummary' => [ 'type' => 'structure', 'required' => [ 'configuredTableId', 'membershipId', 'membershipArn', 'name', 'createTime', 'updateTime', 'id', 'arn', ], 'members' => [ 'configuredTableId' => [ 'shape' => 'UUID', ], 'membershipId' => [ 'shape' => 'MembershipIdentifier', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], 'name' => [ 'shape' => 'TableAlias', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'UUID', ], 'arn' => [ 'shape' => 'ConfiguredTableAssociationArn', ], ], ], 'ConfiguredTableAssociationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfiguredTableAssociationSummary', ], ], 'ConfiguredTableIdentifier' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'ConfiguredTableSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'name', 'createTime', 'updateTime', 'analysisRuleTypes', 'analysisMethod', ], 'members' => [ 'id' => [ 'shape' => 'ConfiguredTableIdentifier', ], 'arn' => [ 'shape' => 'ConfiguredTableArn', ], 'name' => [ 'shape' => 'DisplayName', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'analysisRuleTypes' => [ 'shape' => 'ConfiguredTableAnalysisRuleTypeList', ], 'analysisMethod' => [ 'shape' => 'AnalysisMethod', ], ], ], 'ConfiguredTableSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfiguredTableSummary', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'reason' => [ 'shape' => 'ConflictExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ConflictExceptionReason' => [ 'type' => 'string', 'enum' => [ 'ALREADY_EXISTS', 'SUBRESOURCES_EXIST', 'INVALID_STATE', ], ], 'CreateAnalysisTemplateInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'name', 'format', 'source', ], 'members' => [ 'description' => [ 'shape' => 'ResourceDescription', ], 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'name' => [ 'shape' => 'TableAlias', ], 'format' => [ 'shape' => 'AnalysisFormat', ], 'source' => [ 'shape' => 'AnalysisSource', ], 'tags' => [ 'shape' => 'TagMap', ], 'analysisParameters' => [ 'shape' => 'AnalysisParameterList', ], ], ], 'CreateAnalysisTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'analysisTemplate', ], 'members' => [ 'analysisTemplate' => [ 'shape' => 'AnalysisTemplate', ], ], ], 'CreateCollaborationInput' => [ 'type' => 'structure', 'required' => [ 'members', 'name', 'description', 'creatorMemberAbilities', 'creatorDisplayName', 'queryLogStatus', ], 'members' => [ 'members' => [ 'shape' => 'MemberList', ], 'name' => [ 'shape' => 'CollaborationName', ], 'description' => [ 'shape' => 'CollaborationDescription', ], 'creatorMemberAbilities' => [ 'shape' => 'MemberAbilities', ], 'creatorDisplayName' => [ 'shape' => 'DisplayName', ], 'dataEncryptionMetadata' => [ 'shape' => 'DataEncryptionMetadata', ], 'queryLogStatus' => [ 'shape' => 'CollaborationQueryLogStatus', ], 'tags' => [ 'shape' => 'TagMap', ], 'creatorPaymentConfiguration' => [ 'shape' => 'PaymentConfiguration', ], ], ], 'CreateCollaborationOutput' => [ 'type' => 'structure', 'required' => [ 'collaboration', ], 'members' => [ 'collaboration' => [ 'shape' => 'Collaboration', ], ], ], 'CreateConfiguredAudienceModelAssociationInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'configuredAudienceModelArn', 'configuredAudienceModelAssociationName', 'manageResourcePolicies', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'configuredAudienceModelAssociationName' => [ 'shape' => 'ConfiguredAudienceModelAssociationName', ], 'manageResourcePolicies' => [ 'shape' => 'Boolean', ], 'tags' => [ 'shape' => 'TagMap', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'CreateConfiguredAudienceModelAssociationOutput' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelAssociation', ], 'members' => [ 'configuredAudienceModelAssociation' => [ 'shape' => 'ConfiguredAudienceModelAssociation', ], ], ], 'CreateConfiguredTableAnalysisRuleInput' => [ 'type' => 'structure', 'required' => [ 'configuredTableIdentifier', 'analysisRuleType', 'analysisRulePolicy', ], 'members' => [ 'configuredTableIdentifier' => [ 'shape' => 'ConfiguredTableIdentifier', 'location' => 'uri', 'locationName' => 'configuredTableIdentifier', ], 'analysisRuleType' => [ 'shape' => 'ConfiguredTableAnalysisRuleType', ], 'analysisRulePolicy' => [ 'shape' => 'ConfiguredTableAnalysisRulePolicy', ], ], ], 'CreateConfiguredTableAnalysisRuleOutput' => [ 'type' => 'structure', 'required' => [ 'analysisRule', ], 'members' => [ 'analysisRule' => [ 'shape' => 'ConfiguredTableAnalysisRule', ], ], ], 'CreateConfiguredTableAssociationAnalysisRuleInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'configuredTableAssociationIdentifier', 'analysisRuleType', 'analysisRulePolicy', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'configuredTableAssociationIdentifier' => [ 'shape' => 'ConfiguredTableAssociationIdentifier', 'location' => 'uri', 'locationName' => 'configuredTableAssociationIdentifier', ], 'analysisRuleType' => [ 'shape' => 'ConfiguredTableAssociationAnalysisRuleType', ], 'analysisRulePolicy' => [ 'shape' => 'ConfiguredTableAssociationAnalysisRulePolicy', ], ], ], 'CreateConfiguredTableAssociationAnalysisRuleOutput' => [ 'type' => 'structure', 'required' => [ 'analysisRule', ], 'members' => [ 'analysisRule' => [ 'shape' => 'ConfiguredTableAssociationAnalysisRule', ], ], ], 'CreateConfiguredTableAssociationInput' => [ 'type' => 'structure', 'required' => [ 'name', 'membershipIdentifier', 'configuredTableIdentifier', 'roleArn', ], 'members' => [ 'name' => [ 'shape' => 'TableAlias', ], 'description' => [ 'shape' => 'TableDescription', ], 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'configuredTableIdentifier' => [ 'shape' => 'ConfiguredTableIdentifier', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateConfiguredTableAssociationOutput' => [ 'type' => 'structure', 'required' => [ 'configuredTableAssociation', ], 'members' => [ 'configuredTableAssociation' => [ 'shape' => 'ConfiguredTableAssociation', ], ], ], 'CreateConfiguredTableInput' => [ 'type' => 'structure', 'required' => [ 'name', 'tableReference', 'allowedColumns', 'analysisMethod', ], 'members' => [ 'name' => [ 'shape' => 'DisplayName', ], 'description' => [ 'shape' => 'TableDescription', ], 'tableReference' => [ 'shape' => 'TableReference', ], 'allowedColumns' => [ 'shape' => 'AllowedColumnList', ], 'analysisMethod' => [ 'shape' => 'AnalysisMethod', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateConfiguredTableOutput' => [ 'type' => 'structure', 'required' => [ 'configuredTable', ], 'members' => [ 'configuredTable' => [ 'shape' => 'ConfiguredTable', ], ], ], 'CreateIdMappingTableInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'name', 'inputReferenceConfig', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'name' => [ 'shape' => 'ResourceAlias', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'inputReferenceConfig' => [ 'shape' => 'IdMappingTableInputReferenceConfig', ], 'tags' => [ 'shape' => 'TagMap', ], 'kmsKeyArn' => [ 'shape' => 'KMSKeyArn', ], ], ], 'CreateIdMappingTableOutput' => [ 'type' => 'structure', 'required' => [ 'idMappingTable', ], 'members' => [ 'idMappingTable' => [ 'shape' => 'IdMappingTable', ], ], ], 'CreateIdNamespaceAssociationInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'inputReferenceConfig', 'name', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'inputReferenceConfig' => [ 'shape' => 'IdNamespaceAssociationInputReferenceConfig', ], 'tags' => [ 'shape' => 'TagMap', ], 'name' => [ 'shape' => 'GenericResourceName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'idMappingConfig' => [ 'shape' => 'IdMappingConfig', ], ], ], 'CreateIdNamespaceAssociationOutput' => [ 'type' => 'structure', 'required' => [ 'idNamespaceAssociation', ], 'members' => [ 'idNamespaceAssociation' => [ 'shape' => 'IdNamespaceAssociation', ], ], ], 'CreateMembershipInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', 'queryLogStatus', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', ], 'queryLogStatus' => [ 'shape' => 'MembershipQueryLogStatus', ], 'tags' => [ 'shape' => 'TagMap', ], 'defaultResultConfiguration' => [ 'shape' => 'MembershipProtectedQueryResultConfiguration', ], 'paymentConfiguration' => [ 'shape' => 'MembershipPaymentConfiguration', ], ], ], 'CreateMembershipOutput' => [ 'type' => 'structure', 'required' => [ 'membership', ], 'members' => [ 'membership' => [ 'shape' => 'Membership', ], ], ], 'CreatePrivacyBudgetTemplateInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'autoRefresh', 'privacyBudgetType', 'parameters', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'autoRefresh' => [ 'shape' => 'PrivacyBudgetTemplateAutoRefresh', ], 'privacyBudgetType' => [ 'shape' => 'PrivacyBudgetType', ], 'parameters' => [ 'shape' => 'PrivacyBudgetTemplateParametersInput', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreatePrivacyBudgetTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'privacyBudgetTemplate', ], 'members' => [ 'privacyBudgetTemplate' => [ 'shape' => 'PrivacyBudgetTemplate', ], ], ], 'DataEncryptionMetadata' => [ 'type' => 'structure', 'required' => [ 'allowCleartext', 'allowDuplicates', 'allowJoinsOnColumnsWithDifferentNames', 'preserveNulls', ], 'members' => [ 'allowCleartext' => [ 'shape' => 'Boolean', ], 'allowDuplicates' => [ 'shape' => 'Boolean', ], 'allowJoinsOnColumnsWithDifferentNames' => [ 'shape' => 'Boolean', ], 'preserveNulls' => [ 'shape' => 'Boolean', ], ], ], 'DeleteAnalysisTemplateInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'analysisTemplateIdentifier', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'analysisTemplateIdentifier' => [ 'shape' => 'AnalysisTemplateIdentifier', 'location' => 'uri', 'locationName' => 'analysisTemplateIdentifier', ], ], ], 'DeleteAnalysisTemplateOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteCollaborationInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], ], ], 'DeleteCollaborationOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteConfiguredAudienceModelAssociationInput' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelAssociationIdentifier', 'membershipIdentifier', ], 'members' => [ 'configuredAudienceModelAssociationIdentifier' => [ 'shape' => 'ConfiguredAudienceModelAssociationIdentifier', 'location' => 'uri', 'locationName' => 'configuredAudienceModelAssociationIdentifier', ], 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'DeleteConfiguredAudienceModelAssociationOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteConfiguredTableAnalysisRuleInput' => [ 'type' => 'structure', 'required' => [ 'configuredTableIdentifier', 'analysisRuleType', ], 'members' => [ 'configuredTableIdentifier' => [ 'shape' => 'ConfiguredTableIdentifier', 'location' => 'uri', 'locationName' => 'configuredTableIdentifier', ], 'analysisRuleType' => [ 'shape' => 'ConfiguredTableAnalysisRuleType', 'location' => 'uri', 'locationName' => 'analysisRuleType', ], ], ], 'DeleteConfiguredTableAnalysisRuleOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteConfiguredTableAssociationAnalysisRuleInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'configuredTableAssociationIdentifier', 'analysisRuleType', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'configuredTableAssociationIdentifier' => [ 'shape' => 'ConfiguredTableAssociationIdentifier', 'location' => 'uri', 'locationName' => 'configuredTableAssociationIdentifier', ], 'analysisRuleType' => [ 'shape' => 'ConfiguredTableAssociationAnalysisRuleType', 'location' => 'uri', 'locationName' => 'analysisRuleType', ], ], ], 'DeleteConfiguredTableAssociationAnalysisRuleOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteConfiguredTableAssociationInput' => [ 'type' => 'structure', 'required' => [ 'configuredTableAssociationIdentifier', 'membershipIdentifier', ], 'members' => [ 'configuredTableAssociationIdentifier' => [ 'shape' => 'ConfiguredTableAssociationIdentifier', 'location' => 'uri', 'locationName' => 'configuredTableAssociationIdentifier', ], 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'DeleteConfiguredTableAssociationOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteConfiguredTableInput' => [ 'type' => 'structure', 'required' => [ 'configuredTableIdentifier', ], 'members' => [ 'configuredTableIdentifier' => [ 'shape' => 'ConfiguredTableIdentifier', 'location' => 'uri', 'locationName' => 'configuredTableIdentifier', ], ], ], 'DeleteConfiguredTableOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteIdMappingTableInput' => [ 'type' => 'structure', 'required' => [ 'idMappingTableIdentifier', 'membershipIdentifier', ], 'members' => [ 'idMappingTableIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'idMappingTableIdentifier', ], 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'DeleteIdMappingTableOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteIdNamespaceAssociationInput' => [ 'type' => 'structure', 'required' => [ 'idNamespaceAssociationIdentifier', 'membershipIdentifier', ], 'members' => [ 'idNamespaceAssociationIdentifier' => [ 'shape' => 'IdNamespaceAssociationIdentifier', 'location' => 'uri', 'locationName' => 'idNamespaceAssociationIdentifier', ], 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'DeleteIdNamespaceAssociationOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMemberInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', 'accountId', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'accountId' => [ 'shape' => 'AccountId', 'location' => 'uri', 'locationName' => 'accountId', ], ], ], 'DeleteMemberOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMembershipInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'DeleteMembershipOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeletePrivacyBudgetTemplateInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'privacyBudgetTemplateIdentifier', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'privacyBudgetTemplateIdentifier' => [ 'shape' => 'PrivacyBudgetTemplateIdentifier', 'location' => 'uri', 'locationName' => 'privacyBudgetTemplateIdentifier', ], ], ], 'DeletePrivacyBudgetTemplateOutput' => [ 'type' => 'structure', 'members' => [], ], 'DifferentialPrivacyAggregationExpression' => [ 'type' => 'string', 'min' => 1, ], 'DifferentialPrivacyAggregationType' => [ 'type' => 'string', 'enum' => [ 'AVG', 'COUNT', 'COUNT_DISTINCT', 'SUM', 'STDDEV', ], ], 'DifferentialPrivacyColumn' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'String', ], ], ], 'DifferentialPrivacyColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DifferentialPrivacyColumn', ], 'max' => 1, 'min' => 1, ], 'DifferentialPrivacyConfiguration' => [ 'type' => 'structure', 'required' => [ 'columns', ], 'members' => [ 'columns' => [ 'shape' => 'DifferentialPrivacyColumnList', ], ], ], 'DifferentialPrivacyParameters' => [ 'type' => 'structure', 'required' => [ 'sensitivityParameters', ], 'members' => [ 'sensitivityParameters' => [ 'shape' => 'DifferentialPrivacySensitivityParametersList', ], ], ], 'DifferentialPrivacyPreviewAggregation' => [ 'type' => 'structure', 'required' => [ 'type', 'maxCount', ], 'members' => [ 'type' => [ 'shape' => 'DifferentialPrivacyAggregationType', ], 'maxCount' => [ 'shape' => 'DifferentialPrivacyPreviewAggregationMaxCountInteger', ], ], ], 'DifferentialPrivacyPreviewAggregationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DifferentialPrivacyPreviewAggregation', ], ], 'DifferentialPrivacyPreviewAggregationMaxCountInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'DifferentialPrivacyPreviewParametersInput' => [ 'type' => 'structure', 'required' => [ 'epsilon', 'usersNoisePerQuery', ], 'members' => [ 'epsilon' => [ 'shape' => 'Epsilon', ], 'usersNoisePerQuery' => [ 'shape' => 'UsersNoisePerQuery', ], ], ], 'DifferentialPrivacyPrivacyBudget' => [ 'type' => 'structure', 'required' => [ 'aggregations', 'epsilon', ], 'members' => [ 'aggregations' => [ 'shape' => 'DifferentialPrivacyPrivacyBudgetAggregationList', ], 'epsilon' => [ 'shape' => 'Epsilon', ], ], ], 'DifferentialPrivacyPrivacyBudgetAggregation' => [ 'type' => 'structure', 'required' => [ 'type', 'maxCount', 'remainingCount', ], 'members' => [ 'type' => [ 'shape' => 'DifferentialPrivacyAggregationType', ], 'maxCount' => [ 'shape' => 'DifferentialPrivacyPrivacyBudgetAggregationMaxCountInteger', ], 'remainingCount' => [ 'shape' => 'DifferentialPrivacyPrivacyBudgetAggregationRemainingCountInteger', ], ], ], 'DifferentialPrivacyPrivacyBudgetAggregationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DifferentialPrivacyPrivacyBudgetAggregation', ], ], 'DifferentialPrivacyPrivacyBudgetAggregationMaxCountInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'DifferentialPrivacyPrivacyBudgetAggregationRemainingCountInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'DifferentialPrivacyPrivacyImpact' => [ 'type' => 'structure', 'required' => [ 'aggregations', ], 'members' => [ 'aggregations' => [ 'shape' => 'DifferentialPrivacyPreviewAggregationList', ], ], ], 'DifferentialPrivacySensitivityParameters' => [ 'type' => 'structure', 'required' => [ 'aggregationType', 'aggregationExpression', 'userContributionLimit', ], 'members' => [ 'aggregationType' => [ 'shape' => 'DifferentialPrivacyAggregationType', ], 'aggregationExpression' => [ 'shape' => 'DifferentialPrivacyAggregationExpression', ], 'userContributionLimit' => [ 'shape' => 'DifferentialPrivacySensitivityParametersUserContributionLimitInteger', ], 'minColumnValue' => [ 'shape' => 'Float', ], 'maxColumnValue' => [ 'shape' => 'Float', ], ], ], 'DifferentialPrivacySensitivityParametersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DifferentialPrivacySensitivityParameters', ], ], 'DifferentialPrivacySensitivityParametersUserContributionLimitInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'DifferentialPrivacyTemplateParametersInput' => [ 'type' => 'structure', 'required' => [ 'epsilon', 'usersNoisePerQuery', ], 'members' => [ 'epsilon' => [ 'shape' => 'Epsilon', ], 'usersNoisePerQuery' => [ 'shape' => 'UsersNoisePerQuery', ], ], ], 'DifferentialPrivacyTemplateParametersOutput' => [ 'type' => 'structure', 'required' => [ 'epsilon', 'usersNoisePerQuery', ], 'members' => [ 'epsilon' => [ 'shape' => 'Epsilon', ], 'usersNoisePerQuery' => [ 'shape' => 'UsersNoisePerQuery', ], ], ], 'DifferentialPrivacyTemplateUpdateParameters' => [ 'type' => 'structure', 'members' => [ 'epsilon' => [ 'shape' => 'Epsilon', ], 'usersNoisePerQuery' => [ 'shape' => 'UsersNoisePerQuery', ], ], ], 'DirectAnalysisConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'receiverAccountIds' => [ 'shape' => 'ReceiverAccountIds', ], ], ], 'DisplayName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '(?!\\s*$)[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t]*', ], 'Document' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'Epsilon' => [ 'type' => 'integer', 'box' => true, 'max' => 20, 'min' => 1, ], 'FilterableMemberStatus' => [ 'type' => 'string', 'enum' => [ 'INVITED', 'ACTIVE', ], ], 'Float' => [ 'type' => 'float', 'box' => true, ], 'GenericResourceName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '(?!\\s*$)[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t]*', ], 'GetAnalysisTemplateInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'analysisTemplateIdentifier', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'analysisTemplateIdentifier' => [ 'shape' => 'AnalysisTemplateIdentifier', 'location' => 'uri', 'locationName' => 'analysisTemplateIdentifier', ], ], ], 'GetAnalysisTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'analysisTemplate', ], 'members' => [ 'analysisTemplate' => [ 'shape' => 'AnalysisTemplate', ], ], ], 'GetCollaborationAnalysisTemplateInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', 'analysisTemplateArn', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'analysisTemplateArn' => [ 'shape' => 'AnalysisTemplateArn', 'location' => 'uri', 'locationName' => 'analysisTemplateArn', ], ], ], 'GetCollaborationAnalysisTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'collaborationAnalysisTemplate', ], 'members' => [ 'collaborationAnalysisTemplate' => [ 'shape' => 'CollaborationAnalysisTemplate', ], ], ], 'GetCollaborationConfiguredAudienceModelAssociationInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', 'configuredAudienceModelAssociationIdentifier', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'configuredAudienceModelAssociationIdentifier' => [ 'shape' => 'ConfiguredAudienceModelAssociationIdentifier', 'location' => 'uri', 'locationName' => 'configuredAudienceModelAssociationIdentifier', ], ], ], 'GetCollaborationConfiguredAudienceModelAssociationOutput' => [ 'type' => 'structure', 'required' => [ 'collaborationConfiguredAudienceModelAssociation', ], 'members' => [ 'collaborationConfiguredAudienceModelAssociation' => [ 'shape' => 'CollaborationConfiguredAudienceModelAssociation', ], ], ], 'GetCollaborationIdNamespaceAssociationInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', 'idNamespaceAssociationIdentifier', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'idNamespaceAssociationIdentifier' => [ 'shape' => 'IdNamespaceAssociationIdentifier', 'location' => 'uri', 'locationName' => 'idNamespaceAssociationIdentifier', ], ], ], 'GetCollaborationIdNamespaceAssociationOutput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdNamespaceAssociation', ], 'members' => [ 'collaborationIdNamespaceAssociation' => [ 'shape' => 'CollaborationIdNamespaceAssociation', ], ], ], 'GetCollaborationInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], ], ], 'GetCollaborationOutput' => [ 'type' => 'structure', 'required' => [ 'collaboration', ], 'members' => [ 'collaboration' => [ 'shape' => 'Collaboration', ], ], ], 'GetCollaborationPrivacyBudgetTemplateInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', 'privacyBudgetTemplateIdentifier', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'privacyBudgetTemplateIdentifier' => [ 'shape' => 'PrivacyBudgetTemplateIdentifier', 'location' => 'uri', 'locationName' => 'privacyBudgetTemplateIdentifier', ], ], ], 'GetCollaborationPrivacyBudgetTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'collaborationPrivacyBudgetTemplate', ], 'members' => [ 'collaborationPrivacyBudgetTemplate' => [ 'shape' => 'CollaborationPrivacyBudgetTemplate', ], ], ], 'GetConfiguredAudienceModelAssociationInput' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelAssociationIdentifier', 'membershipIdentifier', ], 'members' => [ 'configuredAudienceModelAssociationIdentifier' => [ 'shape' => 'ConfiguredAudienceModelAssociationIdentifier', 'location' => 'uri', 'locationName' => 'configuredAudienceModelAssociationIdentifier', ], 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'GetConfiguredAudienceModelAssociationOutput' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelAssociation', ], 'members' => [ 'configuredAudienceModelAssociation' => [ 'shape' => 'ConfiguredAudienceModelAssociation', ], ], ], 'GetConfiguredTableAnalysisRuleInput' => [ 'type' => 'structure', 'required' => [ 'configuredTableIdentifier', 'analysisRuleType', ], 'members' => [ 'configuredTableIdentifier' => [ 'shape' => 'ConfiguredTableIdentifier', 'location' => 'uri', 'locationName' => 'configuredTableIdentifier', ], 'analysisRuleType' => [ 'shape' => 'ConfiguredTableAnalysisRuleType', 'location' => 'uri', 'locationName' => 'analysisRuleType', ], ], ], 'GetConfiguredTableAnalysisRuleOutput' => [ 'type' => 'structure', 'required' => [ 'analysisRule', ], 'members' => [ 'analysisRule' => [ 'shape' => 'ConfiguredTableAnalysisRule', ], ], ], 'GetConfiguredTableAssociationAnalysisRuleInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'configuredTableAssociationIdentifier', 'analysisRuleType', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'configuredTableAssociationIdentifier' => [ 'shape' => 'ConfiguredTableAssociationIdentifier', 'location' => 'uri', 'locationName' => 'configuredTableAssociationIdentifier', ], 'analysisRuleType' => [ 'shape' => 'ConfiguredTableAssociationAnalysisRuleType', 'location' => 'uri', 'locationName' => 'analysisRuleType', ], ], ], 'GetConfiguredTableAssociationAnalysisRuleOutput' => [ 'type' => 'structure', 'required' => [ 'analysisRule', ], 'members' => [ 'analysisRule' => [ 'shape' => 'ConfiguredTableAssociationAnalysisRule', ], ], ], 'GetConfiguredTableAssociationInput' => [ 'type' => 'structure', 'required' => [ 'configuredTableAssociationIdentifier', 'membershipIdentifier', ], 'members' => [ 'configuredTableAssociationIdentifier' => [ 'shape' => 'ConfiguredTableAssociationIdentifier', 'location' => 'uri', 'locationName' => 'configuredTableAssociationIdentifier', ], 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'GetConfiguredTableAssociationOutput' => [ 'type' => 'structure', 'required' => [ 'configuredTableAssociation', ], 'members' => [ 'configuredTableAssociation' => [ 'shape' => 'ConfiguredTableAssociation', ], ], ], 'GetConfiguredTableInput' => [ 'type' => 'structure', 'required' => [ 'configuredTableIdentifier', ], 'members' => [ 'configuredTableIdentifier' => [ 'shape' => 'ConfiguredTableIdentifier', 'location' => 'uri', 'locationName' => 'configuredTableIdentifier', ], ], ], 'GetConfiguredTableOutput' => [ 'type' => 'structure', 'required' => [ 'configuredTable', ], 'members' => [ 'configuredTable' => [ 'shape' => 'ConfiguredTable', ], ], ], 'GetIdMappingTableInput' => [ 'type' => 'structure', 'required' => [ 'idMappingTableIdentifier', 'membershipIdentifier', ], 'members' => [ 'idMappingTableIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'idMappingTableIdentifier', ], 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'GetIdMappingTableOutput' => [ 'type' => 'structure', 'required' => [ 'idMappingTable', ], 'members' => [ 'idMappingTable' => [ 'shape' => 'IdMappingTable', ], ], ], 'GetIdNamespaceAssociationInput' => [ 'type' => 'structure', 'required' => [ 'idNamespaceAssociationIdentifier', 'membershipIdentifier', ], 'members' => [ 'idNamespaceAssociationIdentifier' => [ 'shape' => 'IdNamespaceAssociationIdentifier', 'location' => 'uri', 'locationName' => 'idNamespaceAssociationIdentifier', ], 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'GetIdNamespaceAssociationOutput' => [ 'type' => 'structure', 'required' => [ 'idNamespaceAssociation', ], 'members' => [ 'idNamespaceAssociation' => [ 'shape' => 'IdNamespaceAssociation', ], ], ], 'GetMembershipInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'GetMembershipOutput' => [ 'type' => 'structure', 'required' => [ 'membership', ], 'members' => [ 'membership' => [ 'shape' => 'Membership', ], ], ], 'GetPrivacyBudgetTemplateInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'privacyBudgetTemplateIdentifier', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'privacyBudgetTemplateIdentifier' => [ 'shape' => 'PrivacyBudgetTemplateIdentifier', 'location' => 'uri', 'locationName' => 'privacyBudgetTemplateIdentifier', ], ], ], 'GetPrivacyBudgetTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'privacyBudgetTemplate', ], 'members' => [ 'privacyBudgetTemplate' => [ 'shape' => 'PrivacyBudgetTemplate', ], ], ], 'GetProtectedQueryInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'protectedQueryIdentifier', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'protectedQueryIdentifier' => [ 'shape' => 'ProtectedQueryIdentifier', 'location' => 'uri', 'locationName' => 'protectedQueryIdentifier', ], ], ], 'GetProtectedQueryOutput' => [ 'type' => 'structure', 'required' => [ 'protectedQuery', ], 'members' => [ 'protectedQuery' => [ 'shape' => 'ProtectedQuery', ], ], ], 'GetSchemaAnalysisRuleInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', 'name', 'type', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'name' => [ 'shape' => 'TableAlias', 'location' => 'uri', 'locationName' => 'name', ], 'type' => [ 'shape' => 'AnalysisRuleType', 'location' => 'uri', 'locationName' => 'type', ], ], ], 'GetSchemaAnalysisRuleOutput' => [ 'type' => 'structure', 'required' => [ 'analysisRule', ], 'members' => [ 'analysisRule' => [ 'shape' => 'AnalysisRule', ], ], ], 'GetSchemaInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', 'name', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'name' => [ 'shape' => 'TableAlias', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetSchemaOutput' => [ 'type' => 'structure', 'required' => [ 'schema', ], 'members' => [ 'schema' => [ 'shape' => 'Schema', ], ], ], 'GlueDatabaseName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '[a-zA-Z0-9_](([a-zA-Z0-9_]+-)*([a-zA-Z0-9_]+))?', ], 'GlueTableName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '[a-zA-Z0-9_](([a-zA-Z0-9_ ]+-)*([a-zA-Z0-9_ ]+))?', ], 'GlueTableReference' => [ 'type' => 'structure', 'required' => [ 'tableName', 'databaseName', ], 'members' => [ 'tableName' => [ 'shape' => 'GlueTableName', ], 'databaseName' => [ 'shape' => 'GlueDatabaseName', ], ], ], 'IdMappingConfig' => [ 'type' => 'structure', 'required' => [ 'allowUseAsDimensionColumn', ], 'members' => [ 'allowUseAsDimensionColumn' => [ 'shape' => 'Boolean', ], ], ], 'IdMappingTable' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'inputReferenceConfig', 'membershipId', 'membershipArn', 'collaborationId', 'collaborationArn', 'name', 'createTime', 'updateTime', 'inputReferenceProperties', ], 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'arn' => [ 'shape' => 'IdMappingTableArn', ], 'inputReferenceConfig' => [ 'shape' => 'IdMappingTableInputReferenceConfig', ], 'membershipId' => [ 'shape' => 'UUID', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'name' => [ 'shape' => 'ResourceAlias', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'inputReferenceProperties' => [ 'shape' => 'IdMappingTableInputReferenceProperties', ], 'kmsKeyArn' => [ 'shape' => 'KMSKeyArn', ], ], ], 'IdMappingTableArn' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'pattern' => 'arn:aws:cleanrooms:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:membership/[\\d\\w-]+/idmappingtable/[\\d\\w-]+', ], 'IdMappingTableInputReferenceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:(aws|aws-us-gov|aws-cn):entityresolution:.*:[0-9]+:(idmappingworkflow/.*)', ], 'IdMappingTableInputReferenceConfig' => [ 'type' => 'structure', 'required' => [ 'inputReferenceArn', 'manageResourcePolicies', ], 'members' => [ 'inputReferenceArn' => [ 'shape' => 'IdMappingTableInputReferenceArn', ], 'manageResourcePolicies' => [ 'shape' => 'Boolean', ], ], ], 'IdMappingTableInputReferenceProperties' => [ 'type' => 'structure', 'required' => [ 'idMappingTableInputSource', ], 'members' => [ 'idMappingTableInputSource' => [ 'shape' => 'IdMappingTableInputSourceList', ], ], ], 'IdMappingTableInputSource' => [ 'type' => 'structure', 'required' => [ 'idNamespaceAssociationId', 'type', ], 'members' => [ 'idNamespaceAssociationId' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'IdNamespaceType', ], ], ], 'IdMappingTableInputSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdMappingTableInputSource', ], 'max' => 2, 'min' => 2, ], 'IdMappingTableSchemaTypeProperties' => [ 'type' => 'structure', 'required' => [ 'idMappingTableInputSource', ], 'members' => [ 'idMappingTableInputSource' => [ 'shape' => 'IdMappingTableInputSourceList', ], ], ], 'IdMappingTableSummary' => [ 'type' => 'structure', 'required' => [ 'collaborationArn', 'collaborationId', 'membershipId', 'membershipArn', 'createTime', 'updateTime', 'id', 'arn', 'inputReferenceConfig', 'name', ], 'members' => [ 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'membershipId' => [ 'shape' => 'MembershipIdentifier', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'UUID', ], 'arn' => [ 'shape' => 'IdMappingTableArn', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'inputReferenceConfig' => [ 'shape' => 'IdMappingTableInputReferenceConfig', ], 'name' => [ 'shape' => 'ResourceAlias', ], ], ], 'IdMappingTableSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdMappingTableSummary', ], ], 'IdMappingWorkflowsSupported' => [ 'type' => 'list', 'member' => [ 'shape' => 'Document', ], ], 'IdNamespaceAssociation' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'membershipId', 'membershipArn', 'collaborationId', 'collaborationArn', 'name', 'createTime', 'updateTime', 'inputReferenceConfig', 'inputReferenceProperties', ], 'members' => [ 'id' => [ 'shape' => 'IdNamespaceAssociationIdentifier', ], 'arn' => [ 'shape' => 'IdNamespaceAssociationArn', ], 'membershipId' => [ 'shape' => 'UUID', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'name' => [ 'shape' => 'GenericResourceName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'inputReferenceConfig' => [ 'shape' => 'IdNamespaceAssociationInputReferenceConfig', ], 'inputReferenceProperties' => [ 'shape' => 'IdNamespaceAssociationInputReferenceProperties', ], 'idMappingConfig' => [ 'shape' => 'IdMappingConfig', ], ], ], 'IdNamespaceAssociationArn' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => 'arn:aws:cleanrooms:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:membership/[\\d\\w-]+/idnamespaceassociation/[\\d\\w-]+', ], 'IdNamespaceAssociationIdentifier' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'IdNamespaceAssociationInputReferenceArn' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => 'arn:aws:entityresolution:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:idnamespace/[\\d\\w-]+', ], 'IdNamespaceAssociationInputReferenceConfig' => [ 'type' => 'structure', 'required' => [ 'inputReferenceArn', 'manageResourcePolicies', ], 'members' => [ 'inputReferenceArn' => [ 'shape' => 'IdNamespaceAssociationInputReferenceArn', ], 'manageResourcePolicies' => [ 'shape' => 'Boolean', ], ], ], 'IdNamespaceAssociationInputReferenceProperties' => [ 'type' => 'structure', 'required' => [ 'idNamespaceType', 'idMappingWorkflowsSupported', ], 'members' => [ 'idNamespaceType' => [ 'shape' => 'IdNamespaceType', ], 'idMappingWorkflowsSupported' => [ 'shape' => 'IdMappingWorkflowsSupported', ], ], ], 'IdNamespaceAssociationInputReferencePropertiesSummary' => [ 'type' => 'structure', 'required' => [ 'idNamespaceType', ], 'members' => [ 'idNamespaceType' => [ 'shape' => 'IdNamespaceType', ], ], ], 'IdNamespaceAssociationSummary' => [ 'type' => 'structure', 'required' => [ 'membershipId', 'membershipArn', 'collaborationArn', 'collaborationId', 'createTime', 'updateTime', 'id', 'arn', 'inputReferenceConfig', 'name', 'inputReferenceProperties', ], 'members' => [ 'membershipId' => [ 'shape' => 'MembershipIdentifier', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'UUID', ], 'arn' => [ 'shape' => 'IdNamespaceAssociationArn', ], 'inputReferenceConfig' => [ 'shape' => 'IdNamespaceAssociationInputReferenceConfig', ], 'name' => [ 'shape' => 'GenericResourceName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'inputReferenceProperties' => [ 'shape' => 'IdNamespaceAssociationInputReferencePropertiesSummary', ], ], ], 'IdNamespaceAssociationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdNamespaceAssociationSummary', ], ], 'IdNamespaceType' => [ 'type' => 'string', 'enum' => [ 'SOURCE', 'TARGET', ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'JoinOperator' => [ 'type' => 'string', 'enum' => [ 'OR', 'AND', ], ], 'JoinOperatorsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JoinOperator', ], 'max' => 2, 'min' => 0, ], 'JoinRequiredOption' => [ 'type' => 'string', 'enum' => [ 'QUERY_RUNNER', ], ], 'KMSKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws:kms:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:key/[a-zA-Z0-9-]+', ], 'KeyPrefix' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '[\\w!.=*/-]*', ], 'ListAnalysisTemplatesInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAnalysisTemplatesOutput' => [ 'type' => 'structure', 'required' => [ 'analysisTemplateSummaries', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'analysisTemplateSummaries' => [ 'shape' => 'AnalysisTemplateSummaryList', ], ], ], 'ListCollaborationAnalysisTemplatesInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListCollaborationAnalysisTemplatesOutput' => [ 'type' => 'structure', 'required' => [ 'collaborationAnalysisTemplateSummaries', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'collaborationAnalysisTemplateSummaries' => [ 'shape' => 'CollaborationAnalysisTemplateSummaryList', ], ], ], 'ListCollaborationConfiguredAudienceModelAssociationsInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListCollaborationConfiguredAudienceModelAssociationsOutput' => [ 'type' => 'structure', 'required' => [ 'collaborationConfiguredAudienceModelAssociationSummaries', ], 'members' => [ 'collaborationConfiguredAudienceModelAssociationSummaries' => [ 'shape' => 'CollaborationConfiguredAudienceModelAssociationSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListCollaborationIdNamespaceAssociationsInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListCollaborationIdNamespaceAssociationsOutput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdNamespaceAssociationSummaries', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'collaborationIdNamespaceAssociationSummaries' => [ 'shape' => 'CollaborationIdNamespaceAssociationSummaryList', ], ], ], 'ListCollaborationPrivacyBudgetTemplatesInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListCollaborationPrivacyBudgetTemplatesOutput' => [ 'type' => 'structure', 'required' => [ 'collaborationPrivacyBudgetTemplateSummaries', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'collaborationPrivacyBudgetTemplateSummaries' => [ 'shape' => 'CollaborationPrivacyBudgetTemplateSummaryList', ], ], ], 'ListCollaborationPrivacyBudgetsInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', 'privacyBudgetType', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'privacyBudgetType' => [ 'shape' => 'PrivacyBudgetType', 'location' => 'querystring', 'locationName' => 'privacyBudgetType', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListCollaborationPrivacyBudgetsOutput' => [ 'type' => 'structure', 'required' => [ 'collaborationPrivacyBudgetSummaries', ], 'members' => [ 'collaborationPrivacyBudgetSummaries' => [ 'shape' => 'CollaborationPrivacyBudgetSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListCollaborationsInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'memberStatus' => [ 'shape' => 'FilterableMemberStatus', 'location' => 'querystring', 'locationName' => 'memberStatus', ], ], ], 'ListCollaborationsOutput' => [ 'type' => 'structure', 'required' => [ 'collaborationList', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'collaborationList' => [ 'shape' => 'CollaborationSummaryList', ], ], ], 'ListConfiguredAudienceModelAssociationsInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListConfiguredAudienceModelAssociationsOutput' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelAssociationSummaries', ], 'members' => [ 'configuredAudienceModelAssociationSummaries' => [ 'shape' => 'ConfiguredAudienceModelAssociationSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListConfiguredTableAssociationsInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListConfiguredTableAssociationsOutput' => [ 'type' => 'structure', 'required' => [ 'configuredTableAssociationSummaries', ], 'members' => [ 'configuredTableAssociationSummaries' => [ 'shape' => 'ConfiguredTableAssociationSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListConfiguredTablesInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListConfiguredTablesOutput' => [ 'type' => 'structure', 'required' => [ 'configuredTableSummaries', ], 'members' => [ 'configuredTableSummaries' => [ 'shape' => 'ConfiguredTableSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListIdMappingTablesInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListIdMappingTablesOutput' => [ 'type' => 'structure', 'required' => [ 'idMappingTableSummaries', ], 'members' => [ 'idMappingTableSummaries' => [ 'shape' => 'IdMappingTableSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListIdNamespaceAssociationsInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListIdNamespaceAssociationsOutput' => [ 'type' => 'structure', 'required' => [ 'idNamespaceAssociationSummaries', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'idNamespaceAssociationSummaries' => [ 'shape' => 'IdNamespaceAssociationSummaryList', ], ], ], 'ListMembersInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListMembersOutput' => [ 'type' => 'structure', 'required' => [ 'memberSummaries', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'memberSummaries' => [ 'shape' => 'MemberSummaryList', ], ], ], 'ListMembershipsInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'status' => [ 'shape' => 'MembershipStatus', 'location' => 'querystring', 'locationName' => 'status', ], ], ], 'ListMembershipsOutput' => [ 'type' => 'structure', 'required' => [ 'membershipSummaries', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'membershipSummaries' => [ 'shape' => 'MembershipSummaryList', ], ], ], 'ListPrivacyBudgetTemplatesInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListPrivacyBudgetTemplatesOutput' => [ 'type' => 'structure', 'required' => [ 'privacyBudgetTemplateSummaries', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'privacyBudgetTemplateSummaries' => [ 'shape' => 'PrivacyBudgetTemplateSummaryList', ], ], ], 'ListPrivacyBudgetsInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'privacyBudgetType', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'privacyBudgetType' => [ 'shape' => 'PrivacyBudgetType', 'location' => 'querystring', 'locationName' => 'privacyBudgetType', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListPrivacyBudgetsOutput' => [ 'type' => 'structure', 'required' => [ 'privacyBudgetSummaries', ], 'members' => [ 'privacyBudgetSummaries' => [ 'shape' => 'PrivacyBudgetSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListProtectedQueriesInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'status' => [ 'shape' => 'ProtectedQueryStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListProtectedQueriesOutput' => [ 'type' => 'structure', 'required' => [ 'protectedQueries', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'protectedQueries' => [ 'shape' => 'ProtectedQuerySummaryList', ], ], ], 'ListSchemasInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'schemaType' => [ 'shape' => 'SchemaType', 'location' => 'querystring', 'locationName' => 'schemaType', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListSchemasOutput' => [ 'type' => 'structure', 'required' => [ 'schemaSummaries', ], 'members' => [ 'schemaSummaries' => [ 'shape' => 'SchemaSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTagsForResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'CleanroomsArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceOutput' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MemberAbilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberAbility', ], ], 'MemberAbility' => [ 'type' => 'string', 'enum' => [ 'CAN_QUERY', 'CAN_RECEIVE_RESULTS', ], ], 'MemberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberSpecification', ], 'max' => 9, 'min' => 0, ], 'MemberSpecification' => [ 'type' => 'structure', 'required' => [ 'accountId', 'memberAbilities', 'displayName', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'memberAbilities' => [ 'shape' => 'MemberAbilities', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'paymentConfiguration' => [ 'shape' => 'PaymentConfiguration', ], ], ], 'MemberStatus' => [ 'type' => 'string', 'enum' => [ 'INVITED', 'ACTIVE', 'LEFT', 'REMOVED', ], ], 'MemberSummary' => [ 'type' => 'structure', 'required' => [ 'accountId', 'status', 'displayName', 'abilities', 'createTime', 'updateTime', 'paymentConfiguration', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'status' => [ 'shape' => 'MemberStatus', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'abilities' => [ 'shape' => 'MemberAbilities', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'membershipId' => [ 'shape' => 'UUID', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], 'paymentConfiguration' => [ 'shape' => 'PaymentConfiguration', ], ], ], 'MemberSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberSummary', ], ], 'Membership' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'collaborationArn', 'collaborationId', 'collaborationCreatorAccountId', 'collaborationCreatorDisplayName', 'collaborationName', 'createTime', 'updateTime', 'status', 'memberAbilities', 'queryLogStatus', 'paymentConfiguration', ], 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'arn' => [ 'shape' => 'MembershipArn', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'collaborationCreatorAccountId' => [ 'shape' => 'AccountId', ], 'collaborationCreatorDisplayName' => [ 'shape' => 'DisplayName', ], 'collaborationName' => [ 'shape' => 'CollaborationName', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'MembershipStatus', ], 'memberAbilities' => [ 'shape' => 'MemberAbilities', ], 'queryLogStatus' => [ 'shape' => 'MembershipQueryLogStatus', ], 'defaultResultConfiguration' => [ 'shape' => 'MembershipProtectedQueryResultConfiguration', ], 'paymentConfiguration' => [ 'shape' => 'MembershipPaymentConfiguration', ], ], ], 'MembershipArn' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => 'arn:aws:[\\w]+:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:membership/[\\d\\w-]+', ], 'MembershipIdentifier' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'MembershipPaymentConfiguration' => [ 'type' => 'structure', 'required' => [ 'queryCompute', ], 'members' => [ 'queryCompute' => [ 'shape' => 'MembershipQueryComputePaymentConfig', ], ], ], 'MembershipProtectedQueryOutputConfiguration' => [ 'type' => 'structure', 'members' => [ 's3' => [ 'shape' => 'ProtectedQueryS3OutputConfiguration', ], ], 'union' => true, ], 'MembershipProtectedQueryResultConfiguration' => [ 'type' => 'structure', 'required' => [ 'outputConfiguration', ], 'members' => [ 'outputConfiguration' => [ 'shape' => 'MembershipProtectedQueryOutputConfiguration', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'MembershipQueryComputePaymentConfig' => [ 'type' => 'structure', 'required' => [ 'isResponsible', ], 'members' => [ 'isResponsible' => [ 'shape' => 'Boolean', ], ], ], 'MembershipQueryLogStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'MembershipStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'REMOVED', 'COLLABORATION_DELETED', ], ], 'MembershipSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'collaborationArn', 'collaborationId', 'collaborationCreatorAccountId', 'collaborationCreatorDisplayName', 'collaborationName', 'createTime', 'updateTime', 'status', 'memberAbilities', 'paymentConfiguration', ], 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'arn' => [ 'shape' => 'MembershipArn', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'collaborationId' => [ 'shape' => 'CollaborationIdentifier', ], 'collaborationCreatorAccountId' => [ 'shape' => 'AccountId', ], 'collaborationCreatorDisplayName' => [ 'shape' => 'DisplayName', ], 'collaborationName' => [ 'shape' => 'CollaborationName', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'MembershipStatus', ], 'memberAbilities' => [ 'shape' => 'MemberAbilities', ], 'paymentConfiguration' => [ 'shape' => 'MembershipPaymentConfiguration', ], ], ], 'MembershipSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MembershipSummary', ], ], 'PaginationToken' => [ 'type' => 'string', 'max' => 10240, 'min' => 0, ], 'ParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ParameterName', ], 'value' => [ 'shape' => 'ParameterValue', ], ], 'ParameterName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[0-9a-zA-Z_]+', ], 'ParameterType' => [ 'type' => 'string', 'enum' => [ 'SMALLINT', 'INTEGER', 'BIGINT', 'DECIMAL', 'REAL', 'DOUBLE_PRECISION', 'BOOLEAN', 'CHAR', 'VARCHAR', 'DATE', 'TIMESTAMP', 'TIMESTAMPTZ', 'TIME', 'TIMETZ', 'VARBYTE', ], ], 'ParameterValue' => [ 'type' => 'string', 'max' => 250, 'min' => 0, ], 'PaymentConfiguration' => [ 'type' => 'structure', 'required' => [ 'queryCompute', ], 'members' => [ 'queryCompute' => [ 'shape' => 'QueryComputePaymentConfig', ], ], ], 'PopulateIdMappingTableInput' => [ 'type' => 'structure', 'required' => [ 'idMappingTableIdentifier', 'membershipIdentifier', ], 'members' => [ 'idMappingTableIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'idMappingTableIdentifier', ], 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'PopulateIdMappingTableOutput' => [ 'type' => 'structure', 'required' => [ 'idMappingJobId', ], 'members' => [ 'idMappingJobId' => [ 'shape' => 'UUID', ], ], ], 'PreviewPrivacyImpactInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'parameters', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'parameters' => [ 'shape' => 'PreviewPrivacyImpactParametersInput', ], ], ], 'PreviewPrivacyImpactOutput' => [ 'type' => 'structure', 'required' => [ 'privacyImpact', ], 'members' => [ 'privacyImpact' => [ 'shape' => 'PrivacyImpact', ], ], ], 'PreviewPrivacyImpactParametersInput' => [ 'type' => 'structure', 'members' => [ 'differentialPrivacy' => [ 'shape' => 'DifferentialPrivacyPreviewParametersInput', ], ], 'union' => true, ], 'PrivacyBudget' => [ 'type' => 'structure', 'members' => [ 'differentialPrivacy' => [ 'shape' => 'DifferentialPrivacyPrivacyBudget', ], ], 'union' => true, ], 'PrivacyBudgetSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'privacyBudgetTemplateId', 'privacyBudgetTemplateArn', 'membershipId', 'membershipArn', 'collaborationId', 'collaborationArn', 'type', 'createTime', 'updateTime', 'budget', ], 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'privacyBudgetTemplateId' => [ 'shape' => 'PrivacyBudgetTemplateIdentifier', ], 'privacyBudgetTemplateArn' => [ 'shape' => 'PrivacyBudgetTemplateArn', ], 'membershipId' => [ 'shape' => 'MembershipIdentifier', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'type' => [ 'shape' => 'PrivacyBudgetType', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'budget' => [ 'shape' => 'PrivacyBudget', ], ], ], 'PrivacyBudgetSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrivacyBudgetSummary', ], ], 'PrivacyBudgetTemplate' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'membershipId', 'membershipArn', 'collaborationId', 'collaborationArn', 'createTime', 'updateTime', 'privacyBudgetType', 'autoRefresh', 'parameters', ], 'members' => [ 'id' => [ 'shape' => 'PrivacyBudgetTemplateIdentifier', ], 'arn' => [ 'shape' => 'PrivacyBudgetTemplateArn', ], 'membershipId' => [ 'shape' => 'UUID', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'privacyBudgetType' => [ 'shape' => 'PrivacyBudgetType', ], 'autoRefresh' => [ 'shape' => 'PrivacyBudgetTemplateAutoRefresh', ], 'parameters' => [ 'shape' => 'PrivacyBudgetTemplateParametersOutput', ], ], ], 'PrivacyBudgetTemplateArn' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => 'arn:aws:[\\w]+:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:privacybudgettemplate/[\\d\\w-]+', ], 'PrivacyBudgetTemplateAutoRefresh' => [ 'type' => 'string', 'enum' => [ 'CALENDAR_MONTH', 'NONE', ], ], 'PrivacyBudgetTemplateIdentifier' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'PrivacyBudgetTemplateParametersInput' => [ 'type' => 'structure', 'members' => [ 'differentialPrivacy' => [ 'shape' => 'DifferentialPrivacyTemplateParametersInput', ], ], 'union' => true, ], 'PrivacyBudgetTemplateParametersOutput' => [ 'type' => 'structure', 'members' => [ 'differentialPrivacy' => [ 'shape' => 'DifferentialPrivacyTemplateParametersOutput', ], ], 'union' => true, ], 'PrivacyBudgetTemplateSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'membershipId', 'membershipArn', 'collaborationId', 'collaborationArn', 'privacyBudgetType', 'createTime', 'updateTime', ], 'members' => [ 'id' => [ 'shape' => 'PrivacyBudgetTemplateIdentifier', ], 'arn' => [ 'shape' => 'PrivacyBudgetTemplateArn', ], 'membershipId' => [ 'shape' => 'UUID', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'privacyBudgetType' => [ 'shape' => 'PrivacyBudgetType', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], ], ], 'PrivacyBudgetTemplateSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrivacyBudgetTemplateSummary', ], ], 'PrivacyBudgetTemplateUpdateParameters' => [ 'type' => 'structure', 'members' => [ 'differentialPrivacy' => [ 'shape' => 'DifferentialPrivacyTemplateUpdateParameters', ], ], 'union' => true, ], 'PrivacyBudgetType' => [ 'type' => 'string', 'enum' => [ 'DIFFERENTIAL_PRIVACY', ], ], 'PrivacyImpact' => [ 'type' => 'structure', 'members' => [ 'differentialPrivacy' => [ 'shape' => 'DifferentialPrivacyPrivacyImpact', ], ], 'union' => true, ], 'ProtectedQuery' => [ 'type' => 'structure', 'required' => [ 'id', 'membershipId', 'membershipArn', 'createTime', 'status', ], 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'membershipId' => [ 'shape' => 'UUID', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'sqlParameters' => [ 'shape' => 'ProtectedQuerySQLParameters', ], 'status' => [ 'shape' => 'ProtectedQueryStatus', ], 'resultConfiguration' => [ 'shape' => 'ProtectedQueryResultConfiguration', ], 'statistics' => [ 'shape' => 'ProtectedQueryStatistics', ], 'result' => [ 'shape' => 'ProtectedQueryResult', ], 'error' => [ 'shape' => 'ProtectedQueryError', ], 'differentialPrivacy' => [ 'shape' => 'DifferentialPrivacyParameters', ], ], ], 'ProtectedQueryError' => [ 'type' => 'structure', 'required' => [ 'message', 'code', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], ], 'ProtectedQueryIdentifier' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'ProtectedQueryMemberOutputConfiguration' => [ 'type' => 'structure', 'required' => [ 'accountId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], ], ], 'ProtectedQueryMemberOutputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProtectedQuerySingleMemberOutput', ], ], 'ProtectedQueryOutput' => [ 'type' => 'structure', 'members' => [ 's3' => [ 'shape' => 'ProtectedQueryS3Output', ], 'memberList' => [ 'shape' => 'ProtectedQueryMemberOutputList', ], ], 'union' => true, ], 'ProtectedQueryOutputConfiguration' => [ 'type' => 'structure', 'members' => [ 's3' => [ 'shape' => 'ProtectedQueryS3OutputConfiguration', ], 'member' => [ 'shape' => 'ProtectedQueryMemberOutputConfiguration', ], ], 'union' => true, ], 'ProtectedQueryResult' => [ 'type' => 'structure', 'required' => [ 'output', ], 'members' => [ 'output' => [ 'shape' => 'ProtectedQueryOutput', ], ], ], 'ProtectedQueryResultConfiguration' => [ 'type' => 'structure', 'required' => [ 'outputConfiguration', ], 'members' => [ 'outputConfiguration' => [ 'shape' => 'ProtectedQueryOutputConfiguration', ], ], ], 'ProtectedQueryS3Output' => [ 'type' => 'structure', 'required' => [ 'location', ], 'members' => [ 'location' => [ 'shape' => 'String', ], ], ], 'ProtectedQueryS3OutputConfiguration' => [ 'type' => 'structure', 'required' => [ 'resultFormat', 'bucket', ], 'members' => [ 'resultFormat' => [ 'shape' => 'ResultFormat', ], 'bucket' => [ 'shape' => 'ProtectedQueryS3OutputConfigurationBucketString', ], 'keyPrefix' => [ 'shape' => 'KeyPrefix', ], ], ], 'ProtectedQueryS3OutputConfigurationBucketString' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '.*(?!^(\\d+\\.)+\\d+$)(^(([a-z0-9]|[a-z0-9][a-z0-9\\-]*[a-z0-9])\\.)*([a-z0-9]|[a-z0-9][a-z0-9\\-]*[a-z0-9])$).*', ], 'ProtectedQuerySQLParameters' => [ 'type' => 'structure', 'members' => [ 'queryString' => [ 'shape' => 'ProtectedQuerySQLParametersQueryStringString', ], 'analysisTemplateArn' => [ 'shape' => 'AnalysisTemplateArn', ], 'parameters' => [ 'shape' => 'ParameterMap', ], ], 'sensitive' => true, ], 'ProtectedQuerySQLParametersQueryStringString' => [ 'type' => 'string', 'max' => 500000, 'min' => 0, ], 'ProtectedQuerySingleMemberOutput' => [ 'type' => 'structure', 'required' => [ 'accountId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], ], ], 'ProtectedQueryStatistics' => [ 'type' => 'structure', 'members' => [ 'totalDurationInMillis' => [ 'shape' => 'Long', ], ], ], 'ProtectedQueryStatus' => [ 'type' => 'string', 'enum' => [ 'SUBMITTED', 'STARTED', 'CANCELLED', 'CANCELLING', 'FAILED', 'SUCCESS', 'TIMED_OUT', ], ], 'ProtectedQuerySummary' => [ 'type' => 'structure', 'required' => [ 'id', 'membershipId', 'membershipArn', 'createTime', 'status', 'receiverConfigurations', ], 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'membershipId' => [ 'shape' => 'UUID', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'ProtectedQueryStatus', ], 'receiverConfigurations' => [ 'shape' => 'ReceiverConfigurationsList', ], ], ], 'ProtectedQuerySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProtectedQuerySummary', ], ], 'ProtectedQueryType' => [ 'type' => 'string', 'enum' => [ 'SQL', ], ], 'QueryComputePaymentConfig' => [ 'type' => 'structure', 'required' => [ 'isResponsible', ], 'members' => [ 'isResponsible' => [ 'shape' => 'Boolean', ], ], ], 'QueryConstraint' => [ 'type' => 'structure', 'members' => [ 'requireOverlap' => [ 'shape' => 'QueryConstraintRequireOverlap', ], ], 'union' => true, ], 'QueryConstraintList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueryConstraint', ], 'max' => 1, 'min' => 0, ], 'QueryConstraintRequireOverlap' => [ 'type' => 'structure', 'members' => [ 'columns' => [ 'shape' => 'AnalysisRuleColumnList', ], ], ], 'QueryTables' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableAlias', ], ], 'ReceiverAccountIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], ], 'ReceiverConfiguration' => [ 'type' => 'structure', 'required' => [ 'analysisType', ], 'members' => [ 'analysisType' => [ 'shape' => 'AnalysisType', ], 'configurationDetails' => [ 'shape' => 'ConfigurationDetails', ], ], ], 'ReceiverConfigurationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReceiverConfiguration', ], ], 'ResourceAlias' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '[a-zA-Z0-9_](([a-zA-Z0-9_ ]+-)*([a-zA-Z0-9_ ]+))?', ], 'ResourceDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t\\r\\n]*', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'CONFIGURED_TABLE', 'COLLABORATION', 'MEMBERSHIP', 'CONFIGURED_TABLE_ASSOCIATION', ], ], 'ResultFormat' => [ 'type' => 'string', 'enum' => [ 'CSV', 'PARQUET', ], ], 'RoleArn' => [ 'type' => 'string', 'max' => 512, 'min' => 32, 'pattern' => 'arn:aws:iam::[\\w]+:role/[\\w+=./@-]+', ], 'ScalarFunctions' => [ 'type' => 'string', 'enum' => [ 'ABS', 'CAST', 'CEILING', 'COALESCE', 'CONVERT', 'CURRENT_DATE', 'DATEADD', 'EXTRACT', 'FLOOR', 'GETDATE', 'LN', 'LOG', 'LOWER', 'ROUND', 'RTRIM', 'SQRT', 'SUBSTRING', 'TO_CHAR', 'TO_DATE', 'TO_NUMBER', 'TO_TIMESTAMP', 'TRIM', 'TRUNC', 'UPPER', ], ], 'ScalarFunctionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScalarFunctions', ], ], 'Schema' => [ 'type' => 'structure', 'required' => [ 'columns', 'partitionKeys', 'analysisRuleTypes', 'creatorAccountId', 'name', 'collaborationId', 'collaborationArn', 'description', 'createTime', 'updateTime', 'type', 'schemaStatusDetails', ], 'members' => [ 'columns' => [ 'shape' => 'ColumnList', ], 'partitionKeys' => [ 'shape' => 'ColumnList', ], 'analysisRuleTypes' => [ 'shape' => 'AnalysisRuleTypeList', ], 'analysisMethod' => [ 'shape' => 'AnalysisMethod', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], 'name' => [ 'shape' => 'TableAlias', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'description' => [ 'shape' => 'TableDescription', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'type' => [ 'shape' => 'SchemaType', ], 'schemaStatusDetails' => [ 'shape' => 'SchemaStatusDetailList', ], 'schemaTypeProperties' => [ 'shape' => 'SchemaTypeProperties', ], ], ], 'SchemaAnalysisRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalysisRule', ], 'max' => 25, 'min' => 0, ], 'SchemaAnalysisRuleRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'name' => [ 'shape' => 'TableAlias', ], 'type' => [ 'shape' => 'AnalysisRuleType', ], ], ], 'SchemaAnalysisRuleRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaAnalysisRuleRequest', ], 'max' => 25, 'min' => 1, ], 'SchemaConfiguration' => [ 'type' => 'string', 'enum' => [ 'DIFFERENTIAL_PRIVACY', ], ], 'SchemaConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaConfiguration', ], ], 'SchemaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Schema', ], 'max' => 25, 'min' => 0, ], 'SchemaStatus' => [ 'type' => 'string', 'enum' => [ 'READY', 'NOT_READY', ], ], 'SchemaStatusDetail' => [ 'type' => 'structure', 'required' => [ 'status', 'analysisType', ], 'members' => [ 'status' => [ 'shape' => 'SchemaStatus', ], 'reasons' => [ 'shape' => 'SchemaStatusReasonList', ], 'analysisRuleType' => [ 'shape' => 'AnalysisRuleType', ], 'configurations' => [ 'shape' => 'SchemaConfigurationList', ], 'analysisType' => [ 'shape' => 'AnalysisType', ], ], ], 'SchemaStatusDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaStatusDetail', ], ], 'SchemaStatusReason' => [ 'type' => 'structure', 'required' => [ 'code', 'message', ], 'members' => [ 'code' => [ 'shape' => 'SchemaStatusReasonCode', ], 'message' => [ 'shape' => 'String', ], ], ], 'SchemaStatusReasonCode' => [ 'type' => 'string', 'enum' => [ 'ANALYSIS_RULE_MISSING', 'ANALYSIS_TEMPLATES_NOT_CONFIGURED', 'ANALYSIS_PROVIDERS_NOT_CONFIGURED', 'DIFFERENTIAL_PRIVACY_POLICY_NOT_CONFIGURED', 'ID_MAPPING_TABLE_NOT_POPULATED', 'COLLABORATION_ANALYSIS_RULE_NOT_CONFIGURED', 'ADDITIONAL_ANALYSES_NOT_CONFIGURED', 'RESULT_RECEIVERS_NOT_CONFIGURED', 'ADDITIONAL_ANALYSES_NOT_ALLOWED', 'RESULT_RECEIVERS_NOT_ALLOWED', 'ANALYSIS_RULE_TYPES_NOT_COMPATIBLE', ], ], 'SchemaStatusReasonList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaStatusReason', ], ], 'SchemaSummary' => [ 'type' => 'structure', 'required' => [ 'name', 'type', 'creatorAccountId', 'createTime', 'updateTime', 'collaborationId', 'collaborationArn', 'analysisRuleTypes', ], 'members' => [ 'name' => [ 'shape' => 'TableAlias', ], 'type' => [ 'shape' => 'SchemaType', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'updateTime' => [ 'shape' => 'Timestamp', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'collaborationArn' => [ 'shape' => 'CollaborationArn', ], 'analysisRuleTypes' => [ 'shape' => 'AnalysisRuleTypeList', ], 'analysisMethod' => [ 'shape' => 'AnalysisMethod', ], ], ], 'SchemaSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaSummary', ], ], 'SchemaType' => [ 'type' => 'string', 'enum' => [ 'TABLE', 'ID_MAPPING_TABLE', ], ], 'SchemaTypeProperties' => [ 'type' => 'structure', 'members' => [ 'idMappingTable' => [ 'shape' => 'IdMappingTableSchemaTypeProperties', ], ], 'union' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'quotaName', 'quotaValue', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'quotaName' => [ 'shape' => 'String', ], 'quotaValue' => [ 'shape' => 'Double', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'StartProtectedQueryInput' => [ 'type' => 'structure', 'required' => [ 'type', 'membershipIdentifier', 'sqlParameters', ], 'members' => [ 'type' => [ 'shape' => 'ProtectedQueryType', ], 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'sqlParameters' => [ 'shape' => 'ProtectedQuerySQLParameters', ], 'resultConfiguration' => [ 'shape' => 'ProtectedQueryResultConfiguration', ], ], ], 'StartProtectedQueryOutput' => [ 'type' => 'structure', 'required' => [ 'protectedQuery', ], 'members' => [ 'protectedQuery' => [ 'shape' => 'ProtectedQuery', ], ], ], 'String' => [ 'type' => 'string', ], 'TableAlias' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '[a-zA-Z0-9_](([a-zA-Z0-9_ ]+-)*([a-zA-Z0-9_ ]+))?', ], 'TableAliasList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableAlias', ], 'max' => 25, 'min' => 1, ], 'TableDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t\\r\\n]*', ], 'TableReference' => [ 'type' => 'structure', 'members' => [ 'glue' => [ 'shape' => 'GlueTableReference', ], ], 'union' => true, ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'CleanroomsArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TargetProtectedQueryStatus' => [ 'type' => 'string', 'enum' => [ 'CANCELLED', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UUID' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'UntagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'CleanroomsArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAnalysisTemplateInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'analysisTemplateIdentifier', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'analysisTemplateIdentifier' => [ 'shape' => 'AnalysisTemplateIdentifier', 'location' => 'uri', 'locationName' => 'analysisTemplateIdentifier', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'UpdateAnalysisTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'analysisTemplate', ], 'members' => [ 'analysisTemplate' => [ 'shape' => 'AnalysisTemplate', ], ], ], 'UpdateCollaborationInput' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', ], 'members' => [ 'collaborationIdentifier' => [ 'shape' => 'CollaborationIdentifier', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'name' => [ 'shape' => 'CollaborationName', ], 'description' => [ 'shape' => 'CollaborationDescription', ], ], ], 'UpdateCollaborationOutput' => [ 'type' => 'structure', 'required' => [ 'collaboration', ], 'members' => [ 'collaboration' => [ 'shape' => 'Collaboration', ], ], ], 'UpdateConfiguredAudienceModelAssociationInput' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelAssociationIdentifier', 'membershipIdentifier', ], 'members' => [ 'configuredAudienceModelAssociationIdentifier' => [ 'shape' => 'ConfiguredAudienceModelAssociationIdentifier', 'location' => 'uri', 'locationName' => 'configuredAudienceModelAssociationIdentifier', ], 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'name' => [ 'shape' => 'ConfiguredAudienceModelAssociationName', ], ], ], 'UpdateConfiguredAudienceModelAssociationOutput' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelAssociation', ], 'members' => [ 'configuredAudienceModelAssociation' => [ 'shape' => 'ConfiguredAudienceModelAssociation', ], ], ], 'UpdateConfiguredTableAnalysisRuleInput' => [ 'type' => 'structure', 'required' => [ 'configuredTableIdentifier', 'analysisRuleType', 'analysisRulePolicy', ], 'members' => [ 'configuredTableIdentifier' => [ 'shape' => 'ConfiguredTableIdentifier', 'location' => 'uri', 'locationName' => 'configuredTableIdentifier', ], 'analysisRuleType' => [ 'shape' => 'ConfiguredTableAnalysisRuleType', 'location' => 'uri', 'locationName' => 'analysisRuleType', ], 'analysisRulePolicy' => [ 'shape' => 'ConfiguredTableAnalysisRulePolicy', ], ], ], 'UpdateConfiguredTableAnalysisRuleOutput' => [ 'type' => 'structure', 'required' => [ 'analysisRule', ], 'members' => [ 'analysisRule' => [ 'shape' => 'ConfiguredTableAnalysisRule', ], ], ], 'UpdateConfiguredTableAssociationAnalysisRuleInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'configuredTableAssociationIdentifier', 'analysisRuleType', 'analysisRulePolicy', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'configuredTableAssociationIdentifier' => [ 'shape' => 'ConfiguredTableAssociationIdentifier', 'location' => 'uri', 'locationName' => 'configuredTableAssociationIdentifier', ], 'analysisRuleType' => [ 'shape' => 'ConfiguredTableAssociationAnalysisRuleType', 'location' => 'uri', 'locationName' => 'analysisRuleType', ], 'analysisRulePolicy' => [ 'shape' => 'ConfiguredTableAssociationAnalysisRulePolicy', ], ], ], 'UpdateConfiguredTableAssociationAnalysisRuleOutput' => [ 'type' => 'structure', 'required' => [ 'analysisRule', ], 'members' => [ 'analysisRule' => [ 'shape' => 'ConfiguredTableAssociationAnalysisRule', ], ], ], 'UpdateConfiguredTableAssociationInput' => [ 'type' => 'structure', 'required' => [ 'configuredTableAssociationIdentifier', 'membershipIdentifier', ], 'members' => [ 'configuredTableAssociationIdentifier' => [ 'shape' => 'ConfiguredTableAssociationIdentifier', 'location' => 'uri', 'locationName' => 'configuredTableAssociationIdentifier', ], 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'description' => [ 'shape' => 'TableDescription', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'UpdateConfiguredTableAssociationOutput' => [ 'type' => 'structure', 'required' => [ 'configuredTableAssociation', ], 'members' => [ 'configuredTableAssociation' => [ 'shape' => 'ConfiguredTableAssociation', ], ], ], 'UpdateConfiguredTableInput' => [ 'type' => 'structure', 'required' => [ 'configuredTableIdentifier', ], 'members' => [ 'configuredTableIdentifier' => [ 'shape' => 'ConfiguredTableIdentifier', 'location' => 'uri', 'locationName' => 'configuredTableIdentifier', ], 'name' => [ 'shape' => 'DisplayName', ], 'description' => [ 'shape' => 'TableDescription', ], ], ], 'UpdateConfiguredTableOutput' => [ 'type' => 'structure', 'required' => [ 'configuredTable', ], 'members' => [ 'configuredTable' => [ 'shape' => 'ConfiguredTable', ], ], ], 'UpdateIdMappingTableInput' => [ 'type' => 'structure', 'required' => [ 'idMappingTableIdentifier', 'membershipIdentifier', ], 'members' => [ 'idMappingTableIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'idMappingTableIdentifier', ], 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'kmsKeyArn' => [ 'shape' => 'KMSKeyArn', ], ], ], 'UpdateIdMappingTableOutput' => [ 'type' => 'structure', 'required' => [ 'idMappingTable', ], 'members' => [ 'idMappingTable' => [ 'shape' => 'IdMappingTable', ], ], ], 'UpdateIdNamespaceAssociationInput' => [ 'type' => 'structure', 'required' => [ 'idNamespaceAssociationIdentifier', 'membershipIdentifier', ], 'members' => [ 'idNamespaceAssociationIdentifier' => [ 'shape' => 'IdNamespaceAssociationIdentifier', 'location' => 'uri', 'locationName' => 'idNamespaceAssociationIdentifier', ], 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'name' => [ 'shape' => 'GenericResourceName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'idMappingConfig' => [ 'shape' => 'IdMappingConfig', ], ], ], 'UpdateIdNamespaceAssociationOutput' => [ 'type' => 'structure', 'required' => [ 'idNamespaceAssociation', ], 'members' => [ 'idNamespaceAssociation' => [ 'shape' => 'IdNamespaceAssociation', ], ], ], 'UpdateMembershipInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'queryLogStatus' => [ 'shape' => 'MembershipQueryLogStatus', ], 'defaultResultConfiguration' => [ 'shape' => 'MembershipProtectedQueryResultConfiguration', ], ], ], 'UpdateMembershipOutput' => [ 'type' => 'structure', 'required' => [ 'membership', ], 'members' => [ 'membership' => [ 'shape' => 'Membership', ], ], ], 'UpdatePrivacyBudgetTemplateInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'privacyBudgetTemplateIdentifier', 'privacyBudgetType', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'privacyBudgetTemplateIdentifier' => [ 'shape' => 'PrivacyBudgetTemplateIdentifier', 'location' => 'uri', 'locationName' => 'privacyBudgetTemplateIdentifier', ], 'privacyBudgetType' => [ 'shape' => 'PrivacyBudgetType', ], 'parameters' => [ 'shape' => 'PrivacyBudgetTemplateUpdateParameters', ], ], ], 'UpdatePrivacyBudgetTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'privacyBudgetTemplate', ], 'members' => [ 'privacyBudgetTemplate' => [ 'shape' => 'PrivacyBudgetTemplate', ], ], ], 'UpdateProtectedQueryInput' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'protectedQueryIdentifier', 'targetStatus', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'MembershipIdentifier', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'protectedQueryIdentifier' => [ 'shape' => 'ProtectedQueryIdentifier', 'location' => 'uri', 'locationName' => 'protectedQueryIdentifier', ], 'targetStatus' => [ 'shape' => 'TargetProtectedQueryStatus', ], ], ], 'UpdateProtectedQueryOutput' => [ 'type' => 'structure', 'required' => [ 'protectedQuery', ], 'members' => [ 'protectedQuery' => [ 'shape' => 'ProtectedQuery', ], ], ], 'UsersNoisePerQuery' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 10, ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'FIELD_VALIDATION_FAILED', 'INVALID_CONFIGURATION', 'INVALID_QUERY', 'IAM_SYNCHRONIZATION_DELAY', ], ], ],];
