{"__meta": {"id": "Xbfdaabf8cadd1ccb9f4778e4a8d35fa0", "datetime": "2025-06-30 22:41:39", "utime": **********.488045, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.017707, "end": **********.488059, "duration": 0.4703519344329834, "duration_str": "470ms", "measures": [{"label": "Booting", "start": **********.017707, "relative_start": 0, "end": **********.441687, "relative_end": **********.441687, "duration": 0.4239799976348877, "duration_str": "424ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.441697, "relative_start": 0.42398977279663086, "end": **********.48806, "relative_end": 9.5367431640625e-07, "duration": 0.046363115310668945, "duration_str": "46.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43886640, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00229, "accumulated_duration_str": "2.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.474396, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 76.419}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.480055, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 76.419, "width_percent": 23.581}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2352 => array:9 [\n    \"name\" => \"مجسم شخصيات مختلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"2352\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 39\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1756410478 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1756410478\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1556163183 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1556163183\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1193184030 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323255132%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii85Y2IvdS9GcDI4Z1Vxc2o0UXFYUHc9PSIsInZhbHVlIjoidi9FSGVIb1RwMkU4cXd3TVRzRUJyeWtHa0xYR0w2aU5NdlJlQkZXbWVTOXAwWEZCZGdhOWxPMHVBN1J1N0R5ZTlxQmtxbFZZZS9nZGduYlJpYVZaMWRTUGRwTXpwR0M4L2JZaEFYa0dkek9WVVpOSHlnMTJxZHJhNzlOdkc0ODFOdmcxZTk0WC9iMWxFclB4Ky93aGRKUUg3V2ExWVRzZE4vWFNGaXAvcFFFOEdEbWlXOXpLNFI2dXMyWXFKQVpQb3BsKzlEWmVkcjVJNVEwL0hsMkJYcjM2dmZZaVArVExucEpqR2RhNGUrbUROS3JIallMZjc0NFJMWG9YZDFaTmMvYnlxYUNhTGpiZHpBM041dm1peFZ6L05HVkZKclEzdjhBaDNEZXExbFVSUGIvaHRtTlVCRm0rVEJab3ErZ2U0ckZ3U3FJU0ZGMDhLaTRLeG40aERxNkwyTVpLbWFaT0VOck15Wkpqc2FVZ3FnT1ptajBTMjkvTmZUNGNNb050anUzS3FhYXBIN0lLV2kwZGdqYlRGUUlsVWVVWGdJR0FoMUhYYTZSNzBzVzA5UWk2alZSbXhySlFDVHZTRmtRLzJTUEZDRmpqU0RBMHZLYnlUK1JON2VVSXI0aE93MmU5MXBEQlpBVEI5SmxKdlo3eWl0bW1MSWgzcHgyT0kzb24iLCJtYWMiOiI0MjM1NTZmZGI3NzM1Yjc3YzA3ZGMxZGE2NjczOGJjMTA4NTNlMjBjMTU3MjNkNDgwYmQ2OWY0ZWIwYzc3ZmRlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlJuUzhURkxWem5PMXZCUUsxTm5JZ2c9PSIsInZhbHVlIjoia3VqbmwvMjFDck1GOVBqMnpXeEdXNm9DTzI0cHZvRm8rOWMxTTBwbjBWZkUybW1CVW5EY2FHOVpXVU1OMW9Ob1B6Nnk3d2FZcHQvelFmNXRYakdtNEZoSWJxMzZuNmpTdmpaNDcyMDhKQnA4NDRWM3l6NXNCQWp3cTNyNW9Ba3dKOUlwRFZ3VkgwN0I4dnY1bytuTHlGVVZjajdSV01xWXdXaEFqUjhpN0s2dEVMRnprRkhjVGlIa0FOWkhicVdVK3gyT0JNUTVQT3ZYbXU2ZVFVZmRNT2xGNkVFS3V2N3VxaDZ1NkJrU21JY2JteDdQMThJWVBHSmtIb0hmN1ZGd080K2Y3Qk1sRkR2WXptZUw5YzE3YVgrNHN5MDdjdnZkLzYyRmVKVFptcDhzYnlCYksvR0N0ZGJSRVVLdHJFdUZERUZYZ1A5enZ3NEN5S3gwcVFjUU5OL0xEdXlHYVpMWHo4T3dPZmZ2cE5Qd3dpYVQwck0ySCtaSXpnR2JadzBITTB0NXM2V2VlYlZ3Uk9BbGI2enZWSW5yUXdTdWRpVnFSM0xvY0VTQ3k1TWV0cGFabFRaekYvMGFkRXFoVVpRMnpSd0JaOUlOYUlhY2JxVFl1OFhYMUxSNERZN00wcEh1KytGUzVrUWhycFFqRnJ3cnk3emNiU0s5VFVZOUo1WTUiLCJtYWMiOiJhYmY5MmE1ODUxNDc2MzI4NmI0NGJkMjNjMWY5ZjM1NTdlZGM0MTJkMGJlYTg0OTljMDQwZTcxZjNhM2FjNDYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1193184030\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-360411093 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-360411093\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-689903823 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:41:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFwSDNiaFdjcG82NHNZSCtrS0NiekE9PSIsInZhbHVlIjoiaWk2T25seDZJdXprVlE2N0o2VWR5VzRUSlRHVnN5QURadWk5ZW5RdFhDR2FUam5HYTVBN2tQWnF5QWdmRkxlQnFqYW1ZNExzT0EvRVZ6dGExZDR6V0xURkRtOEZrU1NLNXdSTWx5dVZ1ME9VbkM1UzQ3RkpiOHNZN3lhWFMzcXpaVElUOHZtL2dQWnIvVGMzSTRMbG1UMkd2SlVrSWZRQVI4QkZwSVNmeDBHd1BpWDByTGNRZzg0SXVFRVFuNlNRL3NjYmEvdS9rMklzY2xFalRhQ0pZNnlBNUtvbUl5UmdUamkxNjJ5SWRVVUtKNlAzM1lKUzJnT2ZrZzhVZk1wQjBFbmNlOXN2eDQ1NGNTNENOMzgzZlB0UW9xVFdHMGl1RmJ6VkNUQmJoK3JPTzdWVTUyNklxKzAyRG1ZM2RNQ09lWVRIOFBHbk95Q1ovL2Y3ZjNQKyt5VnFjaHpsdzZ6OU45SHlObUpuNnQyK1dzeTVmbDQ2dTVnSnJFa3drbkxjcjFlZ1hBSk5aZmNIOFNtYVZVSE4yLzJHOEZaQzMxV0Q0NEhZQWx1S0ovWjZ1TWJkb0Rtd2thVTlYK0JzTC8yRGIzMkJHTjdlWGVMdWkzL3d2Yzdod2xrdzh5QXMwVUlkY0lpeUh2YWN5NHlJb3pRbk9odUlHK2RQYkNKaWVEVloiLCJtYWMiOiIxZDRiZDIwYWNkN2ExMjRiMTExNGU5OWQwZGQ1ZGFhYWYxZDIwNDU3NTgyNzkyODllMmNhNDY4NzhiOGFhNmIwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:41:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InRvVjJtNk9tbnB5R2REVzhOQnJySmc9PSIsInZhbHVlIjoiVHVpbGVYVHJ2VUNnbjJUQTZkSGtxUmxIOFVNeUtFVnVtMnh5K3pqSEpPOWNFVDhFZXVwc2IwTmxnRlFQYlE5c0JsNUZuL1NvcHFBOGlIdHlaYjkyeTFBSncrY1ovSXpVY3d6Z1A3S1dpcHN3WEFPcFBzMHNuWGxQTVdlUnZjQ09qMStPSW5Pcmh0aHFKOGVBVzdUY0d3czljZnR2MXdNTXZxeWpqQ3U5VHU4RTRhVWpmMHpreUlVQTdqMkgvZUFDMXpSdC9PMEFtVlZqb1h5V2M1Q2psbWc5S0ZscE8rY2hJVFVsZDRzN0dxOFMxRktOS2p0TldxT29qMFY0UlV4U21MTTBGZjQ4TFE2ZE1Uc2RrY1M5c1pab1g1TkFoQWRqblNDZENvWDVNcDhiMTR3NlBEVWRlaVNJTlJYd1V1UDFXU05oNDIzKzIzb3dhWE1pbWFvWlJBd21HbEJCbHdDdVVWbCthSXBxMVRCa0NZVVNUMUFhc2NPTitXYjZqRmNZaEVJZVR5MXRocmRsSTkxU0dWVHRrenhuZSs1NjNTTi9SN2F6RldNS2hscDFXa1BIcmtlVU9xY1NkMGhRbGF5b2h3SmR5NFhjbUpqZUZ2QXcwdzlTL3U0b2xTZk9WVGJRaU9nT29nM2kzQW9XNzk1WEpvMzZQOE9GNVpHbVNWNHgiLCJtYWMiOiJiMzdmZTFhMjlmMmJkYjY2MjczYzUxMmEyNTdlODQ4NjM0MmU2ODI4ZTU4YjUwYTI4NGRjODdiMGQzMmJlMTBjIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:41:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFwSDNiaFdjcG82NHNZSCtrS0NiekE9PSIsInZhbHVlIjoiaWk2T25seDZJdXprVlE2N0o2VWR5VzRUSlRHVnN5QURadWk5ZW5RdFhDR2FUam5HYTVBN2tQWnF5QWdmRkxlQnFqYW1ZNExzT0EvRVZ6dGExZDR6V0xURkRtOEZrU1NLNXdSTWx5dVZ1ME9VbkM1UzQ3RkpiOHNZN3lhWFMzcXpaVElUOHZtL2dQWnIvVGMzSTRMbG1UMkd2SlVrSWZRQVI4QkZwSVNmeDBHd1BpWDByTGNRZzg0SXVFRVFuNlNRL3NjYmEvdS9rMklzY2xFalRhQ0pZNnlBNUtvbUl5UmdUamkxNjJ5SWRVVUtKNlAzM1lKUzJnT2ZrZzhVZk1wQjBFbmNlOXN2eDQ1NGNTNENOMzgzZlB0UW9xVFdHMGl1RmJ6VkNUQmJoK3JPTzdWVTUyNklxKzAyRG1ZM2RNQ09lWVRIOFBHbk95Q1ovL2Y3ZjNQKyt5VnFjaHpsdzZ6OU45SHlObUpuNnQyK1dzeTVmbDQ2dTVnSnJFa3drbkxjcjFlZ1hBSk5aZmNIOFNtYVZVSE4yLzJHOEZaQzMxV0Q0NEhZQWx1S0ovWjZ1TWJkb0Rtd2thVTlYK0JzTC8yRGIzMkJHTjdlWGVMdWkzL3d2Yzdod2xrdzh5QXMwVUlkY0lpeUh2YWN5NHlJb3pRbk9odUlHK2RQYkNKaWVEVloiLCJtYWMiOiIxZDRiZDIwYWNkN2ExMjRiMTExNGU5OWQwZGQ1ZGFhYWYxZDIwNDU3NTgyNzkyODllMmNhNDY4NzhiOGFhNmIwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:41:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InRvVjJtNk9tbnB5R2REVzhOQnJySmc9PSIsInZhbHVlIjoiVHVpbGVYVHJ2VUNnbjJUQTZkSGtxUmxIOFVNeUtFVnVtMnh5K3pqSEpPOWNFVDhFZXVwc2IwTmxnRlFQYlE5c0JsNUZuL1NvcHFBOGlIdHlaYjkyeTFBSncrY1ovSXpVY3d6Z1A3S1dpcHN3WEFPcFBzMHNuWGxQTVdlUnZjQ09qMStPSW5Pcmh0aHFKOGVBVzdUY0d3czljZnR2MXdNTXZxeWpqQ3U5VHU4RTRhVWpmMHpreUlVQTdqMkgvZUFDMXpSdC9PMEFtVlZqb1h5V2M1Q2psbWc5S0ZscE8rY2hJVFVsZDRzN0dxOFMxRktOS2p0TldxT29qMFY0UlV4U21MTTBGZjQ4TFE2ZE1Uc2RrY1M5c1pab1g1TkFoQWRqblNDZENvWDVNcDhiMTR3NlBEVWRlaVNJTlJYd1V1UDFXU05oNDIzKzIzb3dhWE1pbWFvWlJBd21HbEJCbHdDdVVWbCthSXBxMVRCa0NZVVNUMUFhc2NPTitXYjZqRmNZaEVJZVR5MXRocmRsSTkxU0dWVHRrenhuZSs1NjNTTi9SN2F6RldNS2hscDFXa1BIcmtlVU9xY1NkMGhRbGF5b2h3SmR5NFhjbUpqZUZ2QXcwdzlTL3U0b2xTZk9WVGJRaU9nT29nM2kzQW9XNzk1WEpvMzZQOE9GNVpHbVNWNHgiLCJtYWMiOiJiMzdmZTFhMjlmMmJkYjY2MjczYzUxMmEyNTdlODQ4NjM0MmU2ODI4ZTU4YjUwYTI4NGRjODdiMGQzMmJlMTBjIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:41:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-689903823\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1173552171 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2352</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1605;&#1580;&#1587;&#1605; &#1588;&#1582;&#1589;&#1610;&#1575;&#1578; &#1605;&#1582;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2352</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>39</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1173552171\", {\"maxDepth\":0})</script>\n"}}