{"__meta": {"id": "Xe8806d346ec22d081403c334e2630437", "datetime": "2025-06-30 22:41:39", "utime": **********.012532, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.619371, "end": **********.012545, "duration": 0.3931741714477539, "duration_str": "393ms", "measures": [{"label": "Booting", "start": **********.619371, "relative_start": 0, "end": **********.964221, "relative_end": **********.964221, "duration": 0.3448500633239746, "duration_str": "345ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.964231, "relative_start": 0.3448600769042969, "end": **********.012546, "relative_end": 9.5367431640625e-07, "duration": 0.04831504821777344, "duration_str": "48.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45139160, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00298, "accumulated_duration_str": "2.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.993024, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 58.725}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0028121, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 58.725, "width_percent": 22.148}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.006089, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 80.872, "width_percent": 19.128}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  2352 => array:9 [\n    \"name\" => \"مجسم شخصيات مختلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"2352\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 39\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1980528227 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1980528227\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1078473530 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1078473530\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-379626956 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323255132%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikk2cVVEMXA3UERIc3BqZzU3Vis3N0E9PSIsInZhbHVlIjoiMit1RUFBRjNyK2VnWDFET1NTVmdUOHFXVkdGSVdZUGhONmZ3bjVUV3V5cFlNMnlYMk1lUnp5aHEyaTVkTFBzaHFWdTJETmZGRDkvYnZVZ1lKb0hESkRvV1V6UmtnbW1ZUktabExaUGF6ZVZ4L29PeFNJdjFkd0I2NmRYbHR5TE1pVkZyOWRaRUg2a203Qk82Y3lUWnNPa3VDb0x5UEhVWmhGTVVLdjBqaDFJRisyTkR0NnZGZnhDUkNzYTlXUGxLdm1aU015UGFzT1N1Wm9mY0N5RFNIaXN5blBjMzJLSkdYM1B2R3FNZnE3V3QxZWtpVHd1WVZaaUpKUFBiUnk2eHA4WVoyY2pyc0dlVUtlT3V3UFowR0dYdzl1MGhBcURKbjRBWnZlcFQ5amU3aGd1WEV1Ny96ZkZtckpyMG1lbDk5YVV1WmFPSHZGTXhjVXJqWW80eG5qVi9kcFBSdlF4NXFuM09FTU9aeDBFNnZHc3cxOC9Wb2dSLzNBRXpKVlB3azhtVmNIODlZWllzTEFXbS9oTWJ0QnFKOUwybXNzKy9tNGo5L1NYT0tWK1N3b0dSc0d1MnBEUjNEZ1ZNOGpPLzZ3d1ZXR3dKdFFqYzV1MGtsQkYzZFZZeHovbHMwbHVQQXpXQmszOTNuRFdtQ0s5TUc1SXZjVzdNTmFSVW1GMlEiLCJtYWMiOiJmYWYzZGIxYmNmMTJiYTEyMWU3YzI5MDhiMzlkNzM4M2UwZWZjODI1MTY2ZWJhOWE0ZmViZDVkYTk3ZDU1ZDY1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkNTMGwzRXJRZ0FZTWk5TFpJak5NOHc9PSIsInZhbHVlIjoiUDR0SXZhZVhxWkQxZ0djNkpmUTdQT2hZQ0JISzU2clRTcHIwOElDMkcyZ1c0V2x2d2FnMUEyQnVOaUVaYmE1bXBldHhXNjYvaHk4TnlUZ2VxaWtLRXZVOEIvS0ZlRlJTZ2ZodzZtRnIycGIxVHlOcDQwa3ZtUjZtUDhKL2lzUUVNWDFyM3dmWDZTK0ljM283VlhaT2V2QkVTUlRpSjBXTUNhbW1YR1RBZ1o3d3Ryamg5NC9MM2pWU0dFOEVSSjhlb3V3WEJoYUx6Ulhmb2FTWG84WHFkT3N5VUZ6VFJpcjlxMnMyQXpmWlNtWmdaczBRQXQwbmRZM1huV1oxMWJibDRVL1NBZkRGK09zTlBZWHlkZEhZMjR0UStvUWVUbERnbUYxTWFlL09uVlNLT09NdlRZSFpWVEIrSXI3WHcwanV4Y2dVRlZFRWx0OVM0Z2lGT01uTWxoN3BJeWsrbUFiYnlQWWRxbzY3Tng0eUpyTy96WnJ5YXpTdDNyNTNlaWFiZkZIOERacldsakNOM2xUb3hvNFNaQVlOaHNSTDF5ZmQ2Y2loZlpBV0F3RnZLYjBmRFV1SHpPR2JVdVVveUlIZlVhWUFEcVRqMXFrazNPWUpaWGZPbUdLcTZqbTdiRVJ0T2NaNE1nejIrWUhmYldlbUFNTGJMbVB2QVFGcFpzajciLCJtYWMiOiJiMjc1MWQxZWJhMDZiMDAzNDc3MzY0NzFiYjJhMGQ0MThlNWQyODFkYTJiOWYzZjdjZWE3NDE5YmU5OGM4MWE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-379626956\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-264173666 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-264173666\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1013849668 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:41:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii85Y2IvdS9GcDI4Z1Vxc2o0UXFYUHc9PSIsInZhbHVlIjoidi9FSGVIb1RwMkU4cXd3TVRzRUJyeWtHa0xYR0w2aU5NdlJlQkZXbWVTOXAwWEZCZGdhOWxPMHVBN1J1N0R5ZTlxQmtxbFZZZS9nZGduYlJpYVZaMWRTUGRwTXpwR0M4L2JZaEFYa0dkek9WVVpOSHlnMTJxZHJhNzlOdkc0ODFOdmcxZTk0WC9iMWxFclB4Ky93aGRKUUg3V2ExWVRzZE4vWFNGaXAvcFFFOEdEbWlXOXpLNFI2dXMyWXFKQVpQb3BsKzlEWmVkcjVJNVEwL0hsMkJYcjM2dmZZaVArVExucEpqR2RhNGUrbUROS3JIallMZjc0NFJMWG9YZDFaTmMvYnlxYUNhTGpiZHpBM041dm1peFZ6L05HVkZKclEzdjhBaDNEZXExbFVSUGIvaHRtTlVCRm0rVEJab3ErZ2U0ckZ3U3FJU0ZGMDhLaTRLeG40aERxNkwyTVpLbWFaT0VOck15Wkpqc2FVZ3FnT1ptajBTMjkvTmZUNGNNb050anUzS3FhYXBIN0lLV2kwZGdqYlRGUUlsVWVVWGdJR0FoMUhYYTZSNzBzVzA5UWk2alZSbXhySlFDVHZTRmtRLzJTUEZDRmpqU0RBMHZLYnlUK1JON2VVSXI0aE93MmU5MXBEQlpBVEI5SmxKdlo3eWl0bW1MSWgzcHgyT0kzb24iLCJtYWMiOiI0MjM1NTZmZGI3NzM1Yjc3YzA3ZGMxZGE2NjczOGJjMTA4NTNlMjBjMTU3MjNkNDgwYmQ2OWY0ZWIwYzc3ZmRlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:41:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJuUzhURkxWem5PMXZCUUsxTm5JZ2c9PSIsInZhbHVlIjoia3VqbmwvMjFDck1GOVBqMnpXeEdXNm9DTzI0cHZvRm8rOWMxTTBwbjBWZkUybW1CVW5EY2FHOVpXVU1OMW9Ob1B6Nnk3d2FZcHQvelFmNXRYakdtNEZoSWJxMzZuNmpTdmpaNDcyMDhKQnA4NDRWM3l6NXNCQWp3cTNyNW9Ba3dKOUlwRFZ3VkgwN0I4dnY1bytuTHlGVVZjajdSV01xWXdXaEFqUjhpN0s2dEVMRnprRkhjVGlIa0FOWkhicVdVK3gyT0JNUTVQT3ZYbXU2ZVFVZmRNT2xGNkVFS3V2N3VxaDZ1NkJrU21JY2JteDdQMThJWVBHSmtIb0hmN1ZGd080K2Y3Qk1sRkR2WXptZUw5YzE3YVgrNHN5MDdjdnZkLzYyRmVKVFptcDhzYnlCYksvR0N0ZGJSRVVLdHJFdUZERUZYZ1A5enZ3NEN5S3gwcVFjUU5OL0xEdXlHYVpMWHo4T3dPZmZ2cE5Qd3dpYVQwck0ySCtaSXpnR2JadzBITTB0NXM2V2VlYlZ3Uk9BbGI2enZWSW5yUXdTdWRpVnFSM0xvY0VTQ3k1TWV0cGFabFRaekYvMGFkRXFoVVpRMnpSd0JaOUlOYUlhY2JxVFl1OFhYMUxSNERZN00wcEh1KytGUzVrUWhycFFqRnJ3cnk3emNiU0s5VFVZOUo1WTUiLCJtYWMiOiJhYmY5MmE1ODUxNDc2MzI4NmI0NGJkMjNjMWY5ZjM1NTdlZGM0MTJkMGJlYTg0OTljMDQwZTcxZjNhM2FjNDYzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:41:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii85Y2IvdS9GcDI4Z1Vxc2o0UXFYUHc9PSIsInZhbHVlIjoidi9FSGVIb1RwMkU4cXd3TVRzRUJyeWtHa0xYR0w2aU5NdlJlQkZXbWVTOXAwWEZCZGdhOWxPMHVBN1J1N0R5ZTlxQmtxbFZZZS9nZGduYlJpYVZaMWRTUGRwTXpwR0M4L2JZaEFYa0dkek9WVVpOSHlnMTJxZHJhNzlOdkc0ODFOdmcxZTk0WC9iMWxFclB4Ky93aGRKUUg3V2ExWVRzZE4vWFNGaXAvcFFFOEdEbWlXOXpLNFI2dXMyWXFKQVpQb3BsKzlEWmVkcjVJNVEwL0hsMkJYcjM2dmZZaVArVExucEpqR2RhNGUrbUROS3JIallMZjc0NFJMWG9YZDFaTmMvYnlxYUNhTGpiZHpBM041dm1peFZ6L05HVkZKclEzdjhBaDNEZXExbFVSUGIvaHRtTlVCRm0rVEJab3ErZ2U0ckZ3U3FJU0ZGMDhLaTRLeG40aERxNkwyTVpLbWFaT0VOck15Wkpqc2FVZ3FnT1ptajBTMjkvTmZUNGNNb050anUzS3FhYXBIN0lLV2kwZGdqYlRGUUlsVWVVWGdJR0FoMUhYYTZSNzBzVzA5UWk2alZSbXhySlFDVHZTRmtRLzJTUEZDRmpqU0RBMHZLYnlUK1JON2VVSXI0aE93MmU5MXBEQlpBVEI5SmxKdlo3eWl0bW1MSWgzcHgyT0kzb24iLCJtYWMiOiI0MjM1NTZmZGI3NzM1Yjc3YzA3ZGMxZGE2NjczOGJjMTA4NTNlMjBjMTU3MjNkNDgwYmQ2OWY0ZWIwYzc3ZmRlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:41:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJuUzhURkxWem5PMXZCUUsxTm5JZ2c9PSIsInZhbHVlIjoia3VqbmwvMjFDck1GOVBqMnpXeEdXNm9DTzI0cHZvRm8rOWMxTTBwbjBWZkUybW1CVW5EY2FHOVpXVU1OMW9Ob1B6Nnk3d2FZcHQvelFmNXRYakdtNEZoSWJxMzZuNmpTdmpaNDcyMDhKQnA4NDRWM3l6NXNCQWp3cTNyNW9Ba3dKOUlwRFZ3VkgwN0I4dnY1bytuTHlGVVZjajdSV01xWXdXaEFqUjhpN0s2dEVMRnprRkhjVGlIa0FOWkhicVdVK3gyT0JNUTVQT3ZYbXU2ZVFVZmRNT2xGNkVFS3V2N3VxaDZ1NkJrU21JY2JteDdQMThJWVBHSmtIb0hmN1ZGd080K2Y3Qk1sRkR2WXptZUw5YzE3YVgrNHN5MDdjdnZkLzYyRmVKVFptcDhzYnlCYksvR0N0ZGJSRVVLdHJFdUZERUZYZ1A5enZ3NEN5S3gwcVFjUU5OL0xEdXlHYVpMWHo4T3dPZmZ2cE5Qd3dpYVQwck0ySCtaSXpnR2JadzBITTB0NXM2V2VlYlZ3Uk9BbGI2enZWSW5yUXdTdWRpVnFSM0xvY0VTQ3k1TWV0cGFabFRaekYvMGFkRXFoVVpRMnpSd0JaOUlOYUlhY2JxVFl1OFhYMUxSNERZN00wcEh1KytGUzVrUWhycFFqRnJ3cnk3emNiU0s5VFVZOUo1WTUiLCJtYWMiOiJhYmY5MmE1ODUxNDc2MzI4NmI0NGJkMjNjMWY5ZjM1NTdlZGM0MTJkMGJlYTg0OTljMDQwZTcxZjNhM2FjNDYzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:41:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1013849668\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1722101454 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2352</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1605;&#1580;&#1587;&#1605; &#1588;&#1582;&#1589;&#1610;&#1575;&#1578; &#1605;&#1582;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2352</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>39</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1722101454\", {\"maxDepth\":0})</script>\n"}}