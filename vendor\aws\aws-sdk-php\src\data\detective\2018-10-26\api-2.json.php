<?php
// This file was auto-generated from sdk-root/src/data/detective/2018-10-26/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-10-26', 'endpointPrefix' => 'api.detective', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon Detective', 'serviceId' => 'Detective', 'signatureVersion' => 'v4', 'signingName' => 'detective', 'uid' => 'detective-2018-10-26', ], 'operations' => [ 'AcceptInvitation' => [ 'name' => 'AcceptInvitation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/invitation', ], 'input' => [ 'shape' => 'AcceptInvitationRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'BatchGetGraphMemberDatasources' => [ 'name' => 'BatchGetGraphMemberDatasources', 'http' => [ 'method' => 'POST', 'requestUri' => '/graph/datasources/get', ], 'input' => [ 'shape' => 'BatchGetGraphMemberDatasourcesRequest', ], 'output' => [ 'shape' => 'BatchGetGraphMemberDatasourcesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'BatchGetMembershipDatasources' => [ 'name' => 'BatchGetMembershipDatasources', 'http' => [ 'method' => 'POST', 'requestUri' => '/membership/datasources/get', ], 'input' => [ 'shape' => 'BatchGetMembershipDatasourcesRequest', ], 'output' => [ 'shape' => 'BatchGetMembershipDatasourcesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateGraph' => [ 'name' => 'CreateGraph', 'http' => [ 'method' => 'POST', 'requestUri' => '/graph', ], 'input' => [ 'shape' => 'CreateGraphRequest', ], 'output' => [ 'shape' => 'CreateGraphResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateMembers' => [ 'name' => 'CreateMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/graph/members', ], 'input' => [ 'shape' => 'CreateMembersRequest', ], 'output' => [ 'shape' => 'CreateMembersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'DeleteGraph' => [ 'name' => 'DeleteGraph', 'http' => [ 'method' => 'POST', 'requestUri' => '/graph/removal', ], 'input' => [ 'shape' => 'DeleteGraphRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteMembers' => [ 'name' => 'DeleteMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/graph/members/removal', ], 'input' => [ 'shape' => 'DeleteMembersRequest', ], 'output' => [ 'shape' => 'DeleteMembersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeOrganizationConfiguration' => [ 'name' => 'DescribeOrganizationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/orgs/describeOrganizationConfiguration', ], 'input' => [ 'shape' => 'DescribeOrganizationConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeOrganizationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DisableOrganizationAdminAccount' => [ 'name' => 'DisableOrganizationAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/orgs/disableAdminAccount', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DisassociateMembership' => [ 'name' => 'DisassociateMembership', 'http' => [ 'method' => 'POST', 'requestUri' => '/membership/removal', ], 'input' => [ 'shape' => 'DisassociateMembershipRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'EnableOrganizationAdminAccount' => [ 'name' => 'EnableOrganizationAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/orgs/enableAdminAccount', ], 'input' => [ 'shape' => 'EnableOrganizationAdminAccountRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetInvestigation' => [ 'name' => 'GetInvestigation', 'http' => [ 'method' => 'POST', 'requestUri' => '/investigations/getInvestigation', ], 'input' => [ 'shape' => 'GetInvestigationRequest', ], 'output' => [ 'shape' => 'GetInvestigationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetMembers' => [ 'name' => 'GetMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/graph/members/get', ], 'input' => [ 'shape' => 'GetMembersRequest', ], 'output' => [ 'shape' => 'GetMembersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListDatasourcePackages' => [ 'name' => 'ListDatasourcePackages', 'http' => [ 'method' => 'POST', 'requestUri' => '/graph/datasources/list', ], 'input' => [ 'shape' => 'ListDatasourcePackagesRequest', ], 'output' => [ 'shape' => 'ListDatasourcePackagesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListGraphs' => [ 'name' => 'ListGraphs', 'http' => [ 'method' => 'POST', 'requestUri' => '/graphs/list', ], 'input' => [ 'shape' => 'ListGraphsRequest', ], 'output' => [ 'shape' => 'ListGraphsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListIndicators' => [ 'name' => 'ListIndicators', 'http' => [ 'method' => 'POST', 'requestUri' => '/investigations/listIndicators', ], 'input' => [ 'shape' => 'ListIndicatorsRequest', ], 'output' => [ 'shape' => 'ListIndicatorsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListInvestigations' => [ 'name' => 'ListInvestigations', 'http' => [ 'method' => 'POST', 'requestUri' => '/investigations/listInvestigations', ], 'input' => [ 'shape' => 'ListInvestigationsRequest', ], 'output' => [ 'shape' => 'ListInvestigationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListInvitations' => [ 'name' => 'ListInvitations', 'http' => [ 'method' => 'POST', 'requestUri' => '/invitations/list', ], 'input' => [ 'shape' => 'ListInvitationsRequest', ], 'output' => [ 'shape' => 'ListInvitationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListMembers' => [ 'name' => 'ListMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/graph/members/list', ], 'input' => [ 'shape' => 'ListMembersRequest', ], 'output' => [ 'shape' => 'ListMembersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListOrganizationAdminAccounts' => [ 'name' => 'ListOrganizationAdminAccounts', 'http' => [ 'method' => 'POST', 'requestUri' => '/orgs/adminAccountslist', ], 'input' => [ 'shape' => 'ListOrganizationAdminAccountsRequest', ], 'output' => [ 'shape' => 'ListOrganizationAdminAccountsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'RejectInvitation' => [ 'name' => 'RejectInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/invitation/removal', ], 'input' => [ 'shape' => 'RejectInvitationRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'StartInvestigation' => [ 'name' => 'StartInvestigation', 'http' => [ 'method' => 'POST', 'requestUri' => '/investigations/startInvestigation', ], 'input' => [ 'shape' => 'StartInvestigationRequest', ], 'output' => [ 'shape' => 'StartInvestigationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartMonitoringMember' => [ 'name' => 'StartMonitoringMember', 'http' => [ 'method' => 'POST', 'requestUri' => '/graph/member/monitoringstate', ], 'input' => [ 'shape' => 'StartMonitoringMemberRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateDatasourcePackages' => [ 'name' => 'UpdateDatasourcePackages', 'http' => [ 'method' => 'POST', 'requestUri' => '/graph/datasources/update', ], 'input' => [ 'shape' => 'UpdateDatasourcePackagesRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateInvestigationState' => [ 'name' => 'UpdateInvestigationState', 'http' => [ 'method' => 'POST', 'requestUri' => '/investigations/updateInvestigationState', ], 'input' => [ 'shape' => 'UpdateInvestigationStateRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateOrganizationConfiguration' => [ 'name' => 'UpdateOrganizationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/orgs/updateOrganizationConfiguration', ], 'input' => [ 'shape' => 'UpdateOrganizationConfigurationRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], ], 'shapes' => [ 'APIFailureCount' => [ 'type' => 'long', ], 'APIName' => [ 'type' => 'string', ], 'APISuccessCount' => [ 'type' => 'long', ], 'AcceptInvitationRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArn', ], 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorCodeReason' => [ 'shape' => 'ErrorCodeReason', ], 'SubErrorCode' => [ 'shape' => 'ErrorCode', ], 'SubErrorCodeReason' => [ 'shape' => 'ErrorCodeReason', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'Account' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'EmailAddress', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'EmailAddress' => [ 'shape' => 'EmailAddress', ], ], ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^[0-9]+$', ], 'AccountIdExtendedList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], 'max' => 200, 'min' => 1, ], 'AccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], 'max' => 50, 'min' => 1, ], 'AccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Account', ], 'max' => 50, 'min' => 1, ], 'Administrator' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'GraphArn' => [ 'shape' => 'GraphArn', ], 'DelegationTime' => [ 'shape' => 'Timestamp', ], ], ], 'AdministratorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Administrator', ], ], 'AiPaginationToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'Aso' => [ 'type' => 'string', ], 'BatchGetGraphMemberDatasourcesRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArn', 'AccountIds', ], 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], 'AccountIds' => [ 'shape' => 'AccountIdExtendedList', ], ], ], 'BatchGetGraphMemberDatasourcesResponse' => [ 'type' => 'structure', 'members' => [ 'MemberDatasources' => [ 'shape' => 'MembershipDatasourcesList', ], 'UnprocessedAccounts' => [ 'shape' => 'UnprocessedAccountList', ], ], ], 'BatchGetMembershipDatasourcesRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArns', ], 'members' => [ 'GraphArns' => [ 'shape' => 'GraphArnList', ], ], ], 'BatchGetMembershipDatasourcesResponse' => [ 'type' => 'structure', 'members' => [ 'MembershipDatasources' => [ 'shape' => 'MembershipDatasourcesList', ], 'UnprocessedGraphs' => [ 'shape' => 'UnprocessedGraphList', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'ByteValue' => [ 'type' => 'long', ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CreateGraphRequest' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateGraphResponse' => [ 'type' => 'structure', 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], ], ], 'CreateMembersRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArn', 'Accounts', ], 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], 'Message' => [ 'shape' => 'EmailMessage', ], 'DisableEmailNotification' => [ 'shape' => 'Boolean', ], 'Accounts' => [ 'shape' => 'AccountList', ], ], ], 'CreateMembersResponse' => [ 'type' => 'structure', 'members' => [ 'Members' => [ 'shape' => 'MemberDetailList', ], 'UnprocessedAccounts' => [ 'shape' => 'UnprocessedAccountList', ], ], ], 'DatasourcePackage' => [ 'type' => 'string', 'enum' => [ 'DETECTIVE_CORE', 'EKS_AUDIT', 'ASFF_SECURITYHUB_FINDING', ], ], 'DatasourcePackageIngestDetail' => [ 'type' => 'structure', 'members' => [ 'DatasourcePackageIngestState' => [ 'shape' => 'DatasourcePackageIngestState', ], 'LastIngestStateChange' => [ 'shape' => 'LastIngestStateChangeDates', ], ], ], 'DatasourcePackageIngestDetails' => [ 'type' => 'map', 'key' => [ 'shape' => 'DatasourcePackage', ], 'value' => [ 'shape' => 'DatasourcePackageIngestDetail', ], ], 'DatasourcePackageIngestHistory' => [ 'type' => 'map', 'key' => [ 'shape' => 'DatasourcePackage', ], 'value' => [ 'shape' => 'LastIngestStateChangeDates', ], ], 'DatasourcePackageIngestState' => [ 'type' => 'string', 'enum' => [ 'STARTED', 'STOPPED', 'DISABLED', ], ], 'DatasourcePackageIngestStates' => [ 'type' => 'map', 'key' => [ 'shape' => 'DatasourcePackage', ], 'value' => [ 'shape' => 'DatasourcePackageIngestState', ], ], 'DatasourcePackageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasourcePackage', ], 'max' => 25, 'min' => 1, ], 'DatasourcePackageUsageInfo' => [ 'type' => 'structure', 'members' => [ 'VolumeUsageInBytes' => [ 'shape' => 'ByteValue', ], 'VolumeUsageUpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DateFilter' => [ 'type' => 'structure', 'required' => [ 'StartInclusive', 'EndInclusive', ], 'members' => [ 'StartInclusive' => [ 'shape' => 'Timestamp', ], 'EndInclusive' => [ 'shape' => 'Timestamp', ], ], ], 'DeleteGraphRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArn', ], 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], ], ], 'DeleteMembersRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArn', 'AccountIds', ], 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], 'AccountIds' => [ 'shape' => 'AccountIdList', ], ], ], 'DeleteMembersResponse' => [ 'type' => 'structure', 'members' => [ 'AccountIds' => [ 'shape' => 'AccountIdList', ], 'UnprocessedAccounts' => [ 'shape' => 'UnprocessedAccountList', ], ], ], 'DescribeOrganizationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArn', ], 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], ], ], 'DescribeOrganizationConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'AutoEnable' => [ 'shape' => 'Boolean', ], ], ], 'DisassociateMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArn', ], 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], ], ], 'EmailAddress' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^.+@(?:(?:(?!-)[A-Za-z0-9-]{1,62})?[A-Za-z0-9]{1}\\.)+[A-Za-z]{2,63}$', 'sensitive' => true, ], 'EmailMessage' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'sensitive' => true, ], 'EnableOrganizationAdminAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], ], ], 'EntityArn' => [ 'type' => 'string', 'pattern' => '^arn:.*', ], 'EntityType' => [ 'type' => 'string', 'enum' => [ 'IAM_ROLE', 'IAM_USER', ], ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'INVALID_GRAPH_ARN', 'INVALID_REQUEST_BODY', 'INTERNAL_ERROR', ], ], 'ErrorCodeReason' => [ 'type' => 'string', ], 'ErrorMessage' => [ 'type' => 'string', ], 'Field' => [ 'type' => 'string', 'enum' => [ 'SEVERITY', 'STATUS', 'CREATED_TIME', ], ], 'FilterCriteria' => [ 'type' => 'structure', 'members' => [ 'Severity' => [ 'shape' => 'StringFilter', ], 'Status' => [ 'shape' => 'StringFilter', ], 'State' => [ 'shape' => 'StringFilter', ], 'EntityArn' => [ 'shape' => 'StringFilter', ], 'CreatedTime' => [ 'shape' => 'DateFilter', ], ], ], 'FlaggedIpAddressDetail' => [ 'type' => 'structure', 'members' => [ 'IpAddress' => [ 'shape' => 'IpAddress', ], 'Reason' => [ 'shape' => 'Reason', ], ], ], 'GetInvestigationRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArn', 'InvestigationId', ], 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], 'InvestigationId' => [ 'shape' => 'InvestigationId', ], ], ], 'GetInvestigationResponse' => [ 'type' => 'structure', 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], 'InvestigationId' => [ 'shape' => 'InvestigationId', ], 'EntityArn' => [ 'shape' => 'EntityArn', ], 'EntityType' => [ 'shape' => 'EntityType', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'ScopeStartTime' => [ 'shape' => 'Timestamp', ], 'ScopeEndTime' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'Status', ], 'Severity' => [ 'shape' => 'Severity', ], 'State' => [ 'shape' => 'State', ], ], ], 'GetMembersRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArn', 'AccountIds', ], 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], 'AccountIds' => [ 'shape' => 'AccountIdList', ], ], ], 'GetMembersResponse' => [ 'type' => 'structure', 'members' => [ 'MemberDetails' => [ 'shape' => 'MemberDetailList', ], 'UnprocessedAccounts' => [ 'shape' => 'UnprocessedAccountList', ], ], ], 'Graph' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'GraphArn', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], ], ], 'GraphArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[-\\w]{0,10}?:detective:[-\\w]{2,20}?:\\d{12}?:graph:[abcdef\\d]{32}?$', ], 'GraphArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GraphArn', ], 'max' => 50, 'min' => 1, ], 'GraphList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Graph', ], ], 'HourlyTimeDelta' => [ 'type' => 'integer', 'box' => true, ], 'Id' => [ 'type' => 'string', ], 'ImpossibleTravelDetail' => [ 'type' => 'structure', 'members' => [ 'StartingIpAddress' => [ 'shape' => 'IpAddress', ], 'EndingIpAddress' => [ 'shape' => 'IpAddress', ], 'StartingLocation' => [ 'shape' => 'Location', ], 'EndingLocation' => [ 'shape' => 'Location', ], 'HourlyTimeDelta' => [ 'shape' => 'HourlyTimeDelta', ], ], ], 'Indicator' => [ 'type' => 'structure', 'members' => [ 'IndicatorType' => [ 'shape' => 'IndicatorType', ], 'IndicatorDetail' => [ 'shape' => 'IndicatorDetail', ], ], ], 'IndicatorDetail' => [ 'type' => 'structure', 'members' => [ 'TTPsObservedDetail' => [ 'shape' => 'TTPsObservedDetail', ], 'ImpossibleTravelDetail' => [ 'shape' => 'ImpossibleTravelDetail', ], 'FlaggedIpAddressDetail' => [ 'shape' => 'FlaggedIpAddressDetail', ], 'NewGeolocationDetail' => [ 'shape' => 'NewGeolocationDetail', ], 'NewAsoDetail' => [ 'shape' => 'NewAsoDetail', ], 'NewUserAgentDetail' => [ 'shape' => 'NewUserAgentDetail', ], 'RelatedFindingDetail' => [ 'shape' => 'RelatedFindingDetail', ], 'RelatedFindingGroupDetail' => [ 'shape' => 'RelatedFindingGroupDetail', ], ], ], 'IndicatorType' => [ 'type' => 'string', 'enum' => [ 'TTP_OBSERVED', 'IMPOSSIBLE_TRAVEL', 'FLAGGED_IP_ADDRESS', 'NEW_GEOLOCATION', 'NEW_ASO', 'NEW_USER_AGENT', 'RELATED_FINDING', 'RELATED_FINDING_GROUP', ], ], 'Indicators' => [ 'type' => 'list', 'member' => [ 'shape' => 'Indicator', ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'InvestigationDetail' => [ 'type' => 'structure', 'members' => [ 'InvestigationId' => [ 'shape' => 'InvestigationId', ], 'Severity' => [ 'shape' => 'Severity', ], 'Status' => [ 'shape' => 'Status', ], 'State' => [ 'shape' => 'State', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'EntityArn' => [ 'shape' => 'EntityArn', ], 'EntityType' => [ 'shape' => 'EntityType', ], ], ], 'InvestigationDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'InvestigationDetail', ], ], 'InvestigationId' => [ 'type' => 'string', 'max' => 21, 'min' => 21, 'pattern' => '^[0-9]+$', ], 'InvitationType' => [ 'type' => 'string', 'enum' => [ 'INVITATION', 'ORGANIZATION', ], ], 'IpAddress' => [ 'type' => 'string', ], 'IsNewForEntireAccount' => [ 'type' => 'boolean', ], 'LastIngestStateChangeDates' => [ 'type' => 'map', 'key' => [ 'shape' => 'DatasourcePackageIngestState', ], 'value' => [ 'shape' => 'TimestampForCollection', ], ], 'ListDatasourcePackagesRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArn', ], 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'MemberResultsLimit', ], ], ], 'ListDatasourcePackagesResponse' => [ 'type' => 'structure', 'members' => [ 'DatasourcePackages' => [ 'shape' => 'DatasourcePackageIngestDetails', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListGraphsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'MemberResultsLimit', ], ], ], 'ListGraphsResponse' => [ 'type' => 'structure', 'members' => [ 'GraphList' => [ 'shape' => 'GraphList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListIndicatorsRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArn', 'InvestigationId', ], 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], 'InvestigationId' => [ 'shape' => 'InvestigationId', ], 'IndicatorType' => [ 'shape' => 'IndicatorType', ], 'NextToken' => [ 'shape' => 'AiPaginationToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListIndicatorsResponse' => [ 'type' => 'structure', 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], 'InvestigationId' => [ 'shape' => 'InvestigationId', ], 'NextToken' => [ 'shape' => 'AiPaginationToken', ], 'Indicators' => [ 'shape' => 'Indicators', ], ], ], 'ListInvestigationsRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArn', ], 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], 'NextToken' => [ 'shape' => 'AiPaginationToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'FilterCriteria' => [ 'shape' => 'FilterCriteria', ], 'SortCriteria' => [ 'shape' => 'SortCriteria', ], ], ], 'ListInvestigationsResponse' => [ 'type' => 'structure', 'members' => [ 'InvestigationDetails' => [ 'shape' => 'InvestigationDetails', ], 'NextToken' => [ 'shape' => 'AiPaginationToken', ], ], ], 'ListInvitationsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'MemberResultsLimit', ], ], ], 'ListInvitationsResponse' => [ 'type' => 'structure', 'members' => [ 'Invitations' => [ 'shape' => 'MemberDetailList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListMembersRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArn', ], 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'MemberResultsLimit', ], ], ], 'ListMembersResponse' => [ 'type' => 'structure', 'members' => [ 'MemberDetails' => [ 'shape' => 'MemberDetailList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListOrganizationAdminAccountsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'MemberResultsLimit', ], ], ], 'ListOrganizationAdminAccountsResponse' => [ 'type' => 'structure', 'members' => [ 'Administrators' => [ 'shape' => 'AdministratorList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'GraphArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'Location' => [ 'type' => 'string', ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MemberDetail' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'EmailAddress' => [ 'shape' => 'EmailAddress', ], 'GraphArn' => [ 'shape' => 'GraphArn', ], 'MasterId' => [ 'shape' => 'AccountId', 'deprecated' => true, 'deprecatedMessage' => 'This property is deprecated. Use AdministratorId instead.', ], 'AdministratorId' => [ 'shape' => 'AccountId', ], 'Status' => [ 'shape' => 'MemberStatus', ], 'DisabledReason' => [ 'shape' => 'MemberDisabledReason', ], 'InvitedTime' => [ 'shape' => 'Timestamp', ], 'UpdatedTime' => [ 'shape' => 'Timestamp', ], 'VolumeUsageInBytes' => [ 'shape' => 'ByteValue', 'deprecated' => true, 'deprecatedMessage' => 'This property is deprecated. Use VolumeUsageByDatasourcePackage instead.', ], 'VolumeUsageUpdatedTime' => [ 'shape' => 'Timestamp', 'deprecated' => true, 'deprecatedMessage' => 'This property is deprecated. Use VolumeUsageByDatasourcePackage instead.', ], 'PercentOfGraphUtilization' => [ 'shape' => 'Percentage', 'deprecated' => true, 'deprecatedMessage' => 'This property is deprecated. Use VolumeUsageByDatasourcePackage instead.', ], 'PercentOfGraphUtilizationUpdatedTime' => [ 'shape' => 'Timestamp', 'deprecated' => true, 'deprecatedMessage' => 'This property is deprecated. Use VolumeUsageByDatasourcePackage instead.', ], 'InvitationType' => [ 'shape' => 'InvitationType', ], 'VolumeUsageByDatasourcePackage' => [ 'shape' => 'VolumeUsageByDatasourcePackage', ], 'DatasourcePackageIngestStates' => [ 'shape' => 'DatasourcePackageIngestStates', ], ], ], 'MemberDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberDetail', ], ], 'MemberDisabledReason' => [ 'type' => 'string', 'enum' => [ 'VOLUME_TOO_HIGH', 'VOLUME_UNKNOWN', ], ], 'MemberResultsLimit' => [ 'type' => 'integer', 'box' => true, 'max' => 200, 'min' => 1, ], 'MemberStatus' => [ 'type' => 'string', 'enum' => [ 'INVITED', 'VERIFICATION_IN_PROGRESS', 'VERIFICATION_FAILED', 'ENABLED', 'ACCEPTED_BUT_DISABLED', ], ], 'MembershipDatasources' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'GraphArn' => [ 'shape' => 'GraphArn', ], 'DatasourcePackageIngestHistory' => [ 'shape' => 'DatasourcePackageIngestHistory', ], ], ], 'MembershipDatasourcesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MembershipDatasources', ], ], 'NewAsoDetail' => [ 'type' => 'structure', 'members' => [ 'Aso' => [ 'shape' => 'Aso', ], 'IsNewForEntireAccount' => [ 'shape' => 'IsNewForEntireAccount', ], ], ], 'NewGeolocationDetail' => [ 'type' => 'structure', 'members' => [ 'Location' => [ 'shape' => 'Location', ], 'IpAddress' => [ 'shape' => 'IpAddress', ], 'IsNewForEntireAccount' => [ 'shape' => 'IsNewForEntireAccount', ], ], ], 'NewUserAgentDetail' => [ 'type' => 'structure', 'members' => [ 'UserAgent' => [ 'shape' => 'UserAgent', ], 'IsNewForEntireAccount' => [ 'shape' => 'IsNewForEntireAccount', ], ], ], 'PaginationToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'Percentage' => [ 'type' => 'double', ], 'Procedure' => [ 'type' => 'string', ], 'Reason' => [ 'type' => 'string', 'enum' => [ 'AWS_THREAT_INTELLIGENCE', ], ], 'RejectInvitationRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArn', ], 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], ], ], 'RelatedFindingDetail' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'EntityArn', ], 'Type' => [ 'shape' => 'Type', ], 'IpAddress' => [ 'shape' => 'IpAddress', ], ], ], 'RelatedFindingGroupDetail' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'Id', ], ], ], 'Resource' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Resource', ], 'max' => 50, 'min' => 1, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], 'Resources' => [ 'shape' => 'ResourceList', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'Severity' => [ 'type' => 'string', 'enum' => [ 'INFORMATIONAL', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL', ], ], 'SortCriteria' => [ 'type' => 'structure', 'members' => [ 'Field' => [ 'shape' => 'Field', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'StartInvestigationRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArn', 'EntityArn', 'ScopeStartTime', 'ScopeEndTime', ], 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], 'EntityArn' => [ 'shape' => 'EntityArn', ], 'ScopeStartTime' => [ 'shape' => 'Timestamp', ], 'ScopeEndTime' => [ 'shape' => 'Timestamp', ], ], ], 'StartInvestigationResponse' => [ 'type' => 'structure', 'members' => [ 'InvestigationId' => [ 'shape' => 'InvestigationId', ], ], ], 'StartMonitoringMemberRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArn', 'AccountId', ], 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], 'AccountId' => [ 'shape' => 'AccountId', ], ], ], 'State' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'ARCHIVED', ], ], 'Status' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'FAILED', 'SUCCESSFUL', ], ], 'StringFilter' => [ 'type' => 'structure', 'required' => [ 'Value', ], 'members' => [ 'Value' => [ 'shape' => 'Value', ], ], ], 'TTPsObservedDetail' => [ 'type' => 'structure', 'members' => [ 'Tactic' => [ 'shape' => 'Tactic', ], 'Technique' => [ 'shape' => 'Technique', ], 'Procedure' => [ 'shape' => 'Procedure', ], 'IpAddress' => [ 'shape' => 'IpAddress', ], 'APIName' => [ 'shape' => 'APIName', ], 'APISuccessCount' => [ 'shape' => 'APISuccessCount', ], 'APIFailureCount' => [ 'shape' => 'APIFailureCount', ], ], ], 'Tactic' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'GraphArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, ], 'Technique' => [ 'type' => 'string', ], 'Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TimestampForCollection' => [ 'type' => 'structure', 'members' => [ 'Timestamp' => [ 'shape' => 'Timestamp', ], ], ], 'TooManyRequestsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'Type' => [ 'type' => 'string', ], 'UnprocessedAccount' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'Reason' => [ 'shape' => 'UnprocessedReason', ], ], ], 'UnprocessedAccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnprocessedAccount', ], ], 'UnprocessedGraph' => [ 'type' => 'structure', 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], 'Reason' => [ 'shape' => 'UnprocessedReason', ], ], ], 'UnprocessedGraphList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnprocessedGraph', ], ], 'UnprocessedReason' => [ 'type' => 'string', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'GraphArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDatasourcePackagesRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArn', 'DatasourcePackages', ], 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], 'DatasourcePackages' => [ 'shape' => 'DatasourcePackageList', ], ], ], 'UpdateInvestigationStateRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArn', 'InvestigationId', 'State', ], 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], 'InvestigationId' => [ 'shape' => 'InvestigationId', ], 'State' => [ 'shape' => 'State', ], ], ], 'UpdateOrganizationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'GraphArn', ], 'members' => [ 'GraphArn' => [ 'shape' => 'GraphArn', ], 'AutoEnable' => [ 'shape' => 'Boolean', ], ], ], 'UserAgent' => [ 'type' => 'string', ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorCodeReason' => [ 'shape' => 'ErrorCodeReason', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Value' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'VolumeUsageByDatasourcePackage' => [ 'type' => 'map', 'key' => [ 'shape' => 'DatasourcePackage', ], 'value' => [ 'shape' => 'DatasourcePackageUsageInfo', ], ], ],];
