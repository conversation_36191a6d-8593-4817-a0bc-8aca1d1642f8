{"__meta": {"id": "X4964f3ced4e87974b7e6d9edce2bf7c5", "datetime": "2025-06-30 22:39:31", "utime": **********.935534, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.523296, "end": **********.935549, "duration": 0.*****************, "duration_str": "412ms", "measures": [{"label": "Booting", "start": **********.523296, "relative_start": 0, "end": **********.882464, "relative_end": **********.882464, "duration": 0.*****************, "duration_str": "359ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.882473, "relative_start": 0.****************, "end": **********.93555, "relative_end": 9.5367431640625e-07, "duration": 0.053076982498168945, "duration_str": "53.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00307, "accumulated_duration_str": "3.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.910958, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.215}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.920982, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.215, "width_percent": 13.029}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.928222, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 75.244, "width_percent": 24.756}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751323019449%7C9%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxyLzI3Ny9CaXF3bWRSM0JXNWppbVE9PSIsInZhbHVlIjoiaTU5UmZheUJERXAyRWNrVWkrZm1jZ1l0K0dFYUpPV283M3hQS3luNWFoOGtja29Eamt4bElSaldFK0FScUVLL3lNNG9BYlFqeWc4SGZoczY3bnZwQ1pNVVlxd3VEQlJrczZhUnpRajFGaWl6SlRVcCtSWmJ3UkhibzZEZWUzK05ISVVMWmI0ZlhlV05Ec1NwVm13dUtWWWE1UDY1WUMwS3d2dmVFRWlSUlhTYWZQcG5XY2czQmVvSTlrRE5yK1BaZ0dCSEUrMkZrY3dvdk5lKysrekxmRG9MRUEvWk9hMkIwdlBHVW8wcER6dEdwTjRSdTFKM3VuNW5pTC9HeHNMSGtKRW5hejNDdG8xRzZiYWlqSndlR3ozaGhlbUtDenZzM2tCNjNjaGcwTEZIZ3lNOG4yQkNpQjBzTmIwM1dmcVptQmpIclE5VFBLWmpNMkJ4WUxkTU5KVlBmZUVMdVo1TlAyYXJ1KzlpZUNYaXh5THlsdGJYZ0ZFeFpEN05kdDhycnNZcis2WTFrWFhzQ1djZEtWMG41TkxITXdWR0MxSDBkMnhRWWd5MWc3VVJraXd6SkF6Zk5iejErRVFNKzF1Q2pnSWNyVlVVcXVPYUhUN1pEczk0VnZHcjhDZnF0NDJSQWpXbFYwaHhYRWg3RDdPUlhJeUJMNzhacGM0YXRieTYiLCJtYWMiOiI5M2JhMThiOGQzMjEyNjI3ZThiNWNhMTE1ZWUzMWE1OTBkY2Y3Zjc2NTdiNGYwY2NmNmFiZDU4MDgzYzliYTRhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjJrdSttWGRrM3VhK2JyUGZMNTZWcUE9PSIsInZhbHVlIjoiK3YxeHVxbWRWenlwNHZWeVlzWTRRanZrTktmTkQvVUp4bzJYTFlWblBZTFdhMEQxWDcxYWo4ak1vUDJLZE5oRjVJdlFSNmVhTW1adjZKRGVwb2RWbFpybVI3enhLYkFsc2E4cHNNUGxPdjR4bFNPQXNuYjhyZGw4SUVMbmpyNlRGWmtweGpCK2E5R1pGVjh0SFQ2NElhV1Vqejgrd3FSOEIxd1BMOWZqMisyM29VaW5yaEdBVHd2UHFITnplMzE2U2xyWUJwZHZLWXBYNTVxbytNTDlmclVZNm9DN2tYdjBLSm1ZMDF2amtFRmFySXRRU3FEWXdXdlhYZXBsU2tCaG1UZG5RZ1IzNFRQNVRneC8xbU12cnFZMS9qUEU4emNoVTl0cHQwZ1AvYUczSEZwOStuVUI4Wml0aG1CdWYycHc3N0swTXpobWdXQkRKQ1hGdXRXdHhzR09CZ2VpdmU3R2M2ZC9xR2d1NDVYR0NTOUVxc0NGNE1OenRMS1hCOUlZNnl1YkZVcGZmckFybmNXa0lyQ2hkR0IrZTZUY0dYOWx3TWdGYUpxUUp1d2xISXkrQ2hnUndHZldFT1ZOd2duZ2hUYlgvdk10TjRIOUpJa0RzQ045STRuNjIwOU00dHRUN3U1UVVwQVRBcnJLMkREOTRqWE90OHZPaFhKYlZ0aXciLCJtYWMiOiI3NGFjN2QxYjk0ZGRjNzhjZjQzMTdjN2IzZmYzZmZmMzRhNjI5NWRjMTE2ZTU4NjllMzE1NmRlZWNmMGRhMDc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2082091475 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">K7EIYCLnalanTBUASCKMEOK1yUmooVr7ha2cCSMP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2082091475\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1893460215 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Img1SXZCcHZnbnhiNnIxWHpGOGdnNUE9PSIsInZhbHVlIjoiR1puV0JRcXJCVVVnS3dKb2JmdmV5dURRZEdVdXlPRnphT1JxbWV6Ym5iSWxHaG1oS3RkNWdRaUhHTHBCQ0VTNlVVQWwxRjhTMXBNK250TGYvOHR3SGNyMEFyNFYxUHRIZHdhc3hGY2IzbStHZWVJQVIxbjV5N0xyNGZOcVoxZEZzUkFZNis0VGhHa2s1d3dOVHFwazh4ZzdKM25WM1pOZUdkQ004ei8vNGRsZ3drM3QrQU5GeWJ6YzUyOWYweDM2VEt4dDVOQklXUlNJUEFuTTh3UjZLeW5oaTR6b3dDMjF6c1NkTXI0OUZtSUp0REx3MVZnRlJhaUZ2R091dGp2Q0htcmdodTl3M2VURHV1YlZXeGg2YTZURjZrU1hqSWttU2FDSG03ZFJiaHEyR3lJSEdWYXIxWmRUUjBxZU1tMnNLdjB3M0UvWU02OFdrZGFydjB5QWdaWHJwQm5JcUlNVmN0bHF4dWFXYjA0a2xwZFBzSXQzaW1rMGlucUMxQTlKTEpCQWV5ZnpNNW5TSWlWb0ZBOWxqZlJTRm9ua080bVFJMjJpZTQ5T3laNG1jQk9sNmtkZjNJNjA5cnJ2UXlPZVZ6ZW9BaUduY2x2bmlWK0NVZTFYSmlKaGVUYVVHSWVSbWcwZnB0N0RCZ1lGZUIybU43MEZXMUtqam16cEkyVEciLCJtYWMiOiJiYjFkMzZiMWVjMmUyMDk2ZGRhY2M0Njk4Yjg4YTRiYTkwOTFhYmI0MDNkYjRhNWJkNjY2NGZiMzg0YTAwMTQ3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Imp0ZTZyYjh1M3lFTWNOWCtxbzFlQVE9PSIsInZhbHVlIjoicE0yRFdzR0VCRWFOZlZFYVY2QXVhSmc5ekY5ZkxVdTh3bU9xOGFYQzRScUhYdW42NXlYeVd0WFdEWk82Mi9JTjZaSHlqWE1OZU5paVphZ1VCZDZaMHQraW5IdCt1UGpNNzBJN2hxekw0TWRZcmp0c1YxSHU1MDBxRXRpSnNmaCtEM3NKb3Y1UVN2STlIeWpEU1p3RlBBdHI5Z1pKNFZNU2xicEVXWDRZdWFFMFIySkc3aXliTXZZSkhEWVowWHgzMFdmVU5QRmhOUjBKSmd6RE5ESW1yd0M0cFBwSEtKRU5jN29TbWp6ZDdNMHBmZ0YvRVhHTXUxWWdJSmVxaHA0a29MUHZUWXI3M3pUclloR2ZPL1VIMnZKc2NNQUtjTW12TnZXelNyRVRJclRMWTRrc2hlQzJWV1hybjBJUllSMDYvY29SNjBROGtYaG1TdzlnVHFzN3NIbll1MmMrWVVSNVZxbFdrcVo3OXlEZ1BPM25JOXY3YXh5VDdUYThPK1BheUg2ZGJZLy91aWdicGdwUkQ2NHh4YnJiWFN0cVBkcEFXOWVWdlE4SzdiY2t3ZGhTalptWDFZcUxic1NKRVR4dmVIdzA4VjhWNmdTNWdjQ3IrWVJFdHpFTFROR0JNV005ckl2VUdKeHo0L0NYMFpRSVhKcmtFLzZFd0wrMkFjY2EiLCJtYWMiOiI0NzFhYmMyMjkzMTAyOTFkM2FmOTk5OWU4OTE1ZDFiYTRmYjcwYjFhZGM0MzcxNjY4MWQ5ZjM4NTZiZDI5NDg5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Img1SXZCcHZnbnhiNnIxWHpGOGdnNUE9PSIsInZhbHVlIjoiR1puV0JRcXJCVVVnS3dKb2JmdmV5dURRZEdVdXlPRnphT1JxbWV6Ym5iSWxHaG1oS3RkNWdRaUhHTHBCQ0VTNlVVQWwxRjhTMXBNK250TGYvOHR3SGNyMEFyNFYxUHRIZHdhc3hGY2IzbStHZWVJQVIxbjV5N0xyNGZOcVoxZEZzUkFZNis0VGhHa2s1d3dOVHFwazh4ZzdKM25WM1pOZUdkQ004ei8vNGRsZ3drM3QrQU5GeWJ6YzUyOWYweDM2VEt4dDVOQklXUlNJUEFuTTh3UjZLeW5oaTR6b3dDMjF6c1NkTXI0OUZtSUp0REx3MVZnRlJhaUZ2R091dGp2Q0htcmdodTl3M2VURHV1YlZXeGg2YTZURjZrU1hqSWttU2FDSG03ZFJiaHEyR3lJSEdWYXIxWmRUUjBxZU1tMnNLdjB3M0UvWU02OFdrZGFydjB5QWdaWHJwQm5JcUlNVmN0bHF4dWFXYjA0a2xwZFBzSXQzaW1rMGlucUMxQTlKTEpCQWV5ZnpNNW5TSWlWb0ZBOWxqZlJTRm9ua080bVFJMjJpZTQ5T3laNG1jQk9sNmtkZjNJNjA5cnJ2UXlPZVZ6ZW9BaUduY2x2bmlWK0NVZTFYSmlKaGVUYVVHSWVSbWcwZnB0N0RCZ1lGZUIybU43MEZXMUtqam16cEkyVEciLCJtYWMiOiJiYjFkMzZiMWVjMmUyMDk2ZGRhY2M0Njk4Yjg4YTRiYTkwOTFhYmI0MDNkYjRhNWJkNjY2NGZiMzg0YTAwMTQ3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Imp0ZTZyYjh1M3lFTWNOWCtxbzFlQVE9PSIsInZhbHVlIjoicE0yRFdzR0VCRWFOZlZFYVY2QXVhSmc5ekY5ZkxVdTh3bU9xOGFYQzRScUhYdW42NXlYeVd0WFdEWk82Mi9JTjZaSHlqWE1OZU5paVphZ1VCZDZaMHQraW5IdCt1UGpNNzBJN2hxekw0TWRZcmp0c1YxSHU1MDBxRXRpSnNmaCtEM3NKb3Y1UVN2STlIeWpEU1p3RlBBdHI5Z1pKNFZNU2xicEVXWDRZdWFFMFIySkc3aXliTXZZSkhEWVowWHgzMFdmVU5QRmhOUjBKSmd6RE5ESW1yd0M0cFBwSEtKRU5jN29TbWp6ZDdNMHBmZ0YvRVhHTXUxWWdJSmVxaHA0a29MUHZUWXI3M3pUclloR2ZPL1VIMnZKc2NNQUtjTW12TnZXelNyRVRJclRMWTRrc2hlQzJWV1hybjBJUllSMDYvY29SNjBROGtYaG1TdzlnVHFzN3NIbll1MmMrWVVSNVZxbFdrcVo3OXlEZ1BPM25JOXY3YXh5VDdUYThPK1BheUg2ZGJZLy91aWdicGdwUkQ2NHh4YnJiWFN0cVBkcEFXOWVWdlE4SzdiY2t3ZGhTalptWDFZcUxic1NKRVR4dmVIdzA4VjhWNmdTNWdjQ3IrWVJFdHpFTFROR0JNV005ckl2VUdKeHo0L0NYMFpRSVhKcmtFLzZFd0wrMkFjY2EiLCJtYWMiOiI0NzFhYmMyMjkzMTAyOTFkM2FmOTk5OWU4OTE1ZDFiYTRmYjcwYjFhZGM0MzcxNjY4MWQ5ZjM4NTZiZDI5NDg5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893460215\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1285841820 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1285841820\", {\"maxDepth\":0})</script>\n"}}