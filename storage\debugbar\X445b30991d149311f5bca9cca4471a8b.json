{"__meta": {"id": "X445b30991d149311f5bca9cca4471a8b", "datetime": "2025-06-30 22:39:29", "utime": **********.120451, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323168.699785, "end": **********.120468, "duration": 0.4206829071044922, "duration_str": "421ms", "measures": [{"label": "Booting", "start": 1751323168.699785, "relative_start": 0, "end": **********.063432, "relative_end": **********.063432, "duration": 0.3636469841003418, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.063444, "relative_start": 0.3636589050292969, "end": **********.120469, "relative_end": 1.1920928955078125e-06, "duration": 0.05702519416809082, "duration_str": "57.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45106224, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0034399999999999995, "accumulated_duration_str": "3.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 26}], "start": **********.093917, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 56.686}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.103426, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 56.686, "width_percent": 16.57}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.112604, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 73.256, "width_percent": 26.744}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-565977080 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-565977080\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-844652252 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-844652252\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-699477074 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-699477074\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1kg512v%7C1751323019449%7C9%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im44bXJlUVJTQlJ0eXNTeGd6dUlhUlE9PSIsInZhbHVlIjoieFhwZEJCcFBUS3hDakV0UkdNM0VCMGNiUlZuL2RqUVVnR2VvZ0h4bUlTZTlpTG1ySUVzZlhJWHI1QUhGWHJRaU40OTdGR3c5cXlKL29XMW5GQ29PRFIwVENJUTIwMllrWVdteG9tdGFFeTlkYlhkbTNJMEpIU3VmVEZveHN6TTBXSVo1UGVWNTY2YnRnQjZUUldkOWtSd0dqNTVNb01uMFNNbUY4ZWdkNjRRdDVUSjQxZjJvZGZoU2t0L0dqaFVseG1uNXVRZWRRVlhtbzB4bFVCZU1qLytiU0h4Ry95QkUxbGNJbXNGM1NOWkhuOWZNZnZJMG1oM0YxQzNMQW1RS0dVYjJRYmZ1MlpaR3A2NG5DSlRwK1ZNOEFrNXQ4SW4vdWFhbjZIMjNpUUt0T3hIUXNqOHlzOXFIUEZEN1N5b08wNm82RnpmeWFHNEZZdWc3UGNmdVJNY01NOHQvRTQzTGdZOFh2Y1dzZUZTZ25SVE5mR1haUGxUUHIvYnpqNUJ3dzhBVFVEQk41R0tzaXZxZVRIbnVSOWZabUE1MitHT1VIeXI4ODNpSDdhd3pjRUo4VWxtY094aStSWVBaTWkvSnFtUExOWTh6SmkrQlRROEIrUlFNTzFpbEJ5azB3UysrYXpRUmRLa3FmMmtFcS9lQVJydk9YY2xWUWp1QityN0giLCJtYWMiOiIwZTUzYTFiNWNjNjIwOTNhNmNmZjQ4MjZmM2IwODYzZjVjY2Y4YzhkOWM4MWNhODlmN2IzMmM1Y2FiM2Y4YTdhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjhiZEVFYi9uSjBhdDFrUzZMWW5zeUE9PSIsInZhbHVlIjoiY1BLemJnQ2l4STM5N2Z4TE1YV01ZdWdtVE9tWmJITStEMXN4RjQvZEFBRjc5VmxlQm1CekZaNWlxZ2lPWGZTQlUyTFNOU3Nrakhac0xoQ1BnQTR3Nll2V29kK1VkRG1tZHpTM29qSWFxQ0RQVVJjUHpubkR6VFJSdkprakk4UlJhbUx0aUl6TVNEeUZtMnFOekNuaGttOVc5N2xmUGg0SVJqWEhmODFIZWp3K1hVQ2JkN1YybXZxR3owRG9qWkdlcXpzeDB6M2NBN0hpTzExRjBORUR4azVJa2tMUWEzbnhNQkM2TGg1aUpQb3g5ZHJMamg1T20zSUZPNWc5UU9oZExTdE03djRJL29rY2tpSlVQOGpGSFUvMldwQ3p6WS9wWHdPb0g1ek4wcG9PVm91MUlPQ2Ercy9RM3ZZTitLWU5yOGdOcThxcExnUTRNQnQrNGI0SU1LTDlhLy9oNHlLQ1pRZWZjSnZNbkJKS3Z4ZjIzZ1I1cnQ3UzNZVStZc3RMKy96ZTZVMTlhL0tXcTZEUXEwTE9aYTlqVWkrYXBjVjdyOVdERmgwVForT29rQmhIS1BtYjhBdEVNblRmMTM5TFdoc0xZaDJlenZnaUVIYzc4UTh4K01OMlNxc3hQN2cxOG0zZlkwSnlRL0JoVmV6QVV0TG9BRnZYeklPeUZ6Z3YiLCJtYWMiOiIyNDExMTIxODdjZTY1MzE4NjM0OTEzNjE0NmI4MzYxNGRmYjQ1MTQ5YzM2ZDAxNzAxNzBlN2FkNDIzNTEzZjVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">K7EIYCLnalanTBUASCKMEOK1yUmooVr7ha2cCSMP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-440620654 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:39:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik12VTFuUGc0ck1OYUtKOWZvVDY5Umc9PSIsInZhbHVlIjoiUkFnZDFOMXBmb1RXenlhdjRWaDVoNTVOclk3OEY5dkE5emJGenNFblcxT09vSWNGS2tyQUxyTWgyU3Fqay9LOVNMUTZPdU5KZm4remc1cEFWL2ZMOE0rLzkwbVVvemk5YUtydXRLUkdMQU05KzY2MGx1K01OYmRxK1c2VU80K3hPZWxBMC91UWRhM1pvYnRsUWF0MXcxUWdVOFYxaTVjUUtYOCtSbkN6dTJFU013T3k2UTdXTkoyOUJVWUhOYUViRVVyUzNNbHhMdWVKeUpTMmJPNjFRWW5CZEpMR21CMkN4VHUzSnlhSnZkZzRaV0c2MGlOS2dtc1VGV1QwQXlEQ1ZYVUNaRDF1Q29HUXpHdzlyK0JVUTV6eW5aWjl1RmsyQXVKMWZEaUV0S1cvdG5EQWQwTnZIY0IwUDFFTU1KZTRqK0R3ODBCZ2h3cCt1cTdnVlZYd3RrSHRSd0dVUGFLU1JNWnNtNzdhdmNCeW1pNHZXa3VVRElZM09PZjFUNnVwMkx4M2lNai9pVG9XZFdoYkJrY1VpNm9sT1FiUURTNWZOekJGYzFwMStuaC81MTgrM1Qxb1l4NmQwSDZhZXhxSWU0TG5ObjhjczBkdDI3TFAvaHUxR0JlRjVCU0VJeTVUTVVZKzBmc2xnd2l3akFBR21SakVJU0ticlFBblovTTYiLCJtYWMiOiIxZTk0Nzc4ODIwMWY2ODU0ODQ5NWJiY2IyNWYzMmU5ZTI0YzQ3ZmM5ZTE1ZDExNzBkZTFiN2Y3ZmFiODllYTA0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InBDaXhRVjErUnVrY3FiK1B0K3ZjakE9PSIsInZhbHVlIjoielE2ZWZKZkFvYUtOVitZSnB3dmRCREFodmg1RmY3R1dXUjdPTkhZRWIzUHpzRjBJcjhKTG9VRE5LQUZEeTRwMDRsbkVqRTJEa0JJbzc0ZDlTY3RZUTNhdi9EbUcxenhpUzdHdmxIWWRiaFhXNTFrZGkzMDFEeElIVEcrUVp0MHFoNStpOUZOSTBQZU82V3BIZkdaUnVNVktDNUV3VHNVME02dzkwRk8xeUlSZit4RDFYVG5LNVJGTWFPdWtxOFowa1IvTE1Va3Z3MVA3OUR2aXRqMVVwRHBqOXRKYjJYbzQ0WjdoamI1Z1lTVE51NkVPWmJydzdVYVh5ekw4VXVyUkJTQTNEcy95cWdEK2hYd1JrOXpIWmt5NzZxdjFZYXZiWjB6alplcVIycVdNY0d0aGRHQklldGE5RVhxR0NOdVRWckFSRzJFdnl6bW1lM1Ewc0VNQitVZkJyRFptZ3ROVHBWek01RE1hcVptcUhqekJiajFqR2lrWm0za1c5WVV0L1VSdmU0UkZLTFhlNEZ4eVE0cjJKUVNtNndtaDVJTmM0ZEtBejM4dHFLWTFxR1dtTklxNkUrTkxqZkMwL3pnaWxKUGU3aDBuYnhGRStMWmh5aGVYNFd4RnRrM0V4akhXa0lod1dkTTZkVC9zM2MxVnArMHExVU56QVRBbXNmQUwiLCJtYWMiOiI2MWFkYmU5NWVmZWJiMDljZGNkMjI1ODk1ZGJhNzJlYzhjZTllY2FjMzhhNzU0ODFmYzFhYTVhZTQwMmMzNmFjIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:39:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik12VTFuUGc0ck1OYUtKOWZvVDY5Umc9PSIsInZhbHVlIjoiUkFnZDFOMXBmb1RXenlhdjRWaDVoNTVOclk3OEY5dkE5emJGenNFblcxT09vSWNGS2tyQUxyTWgyU3Fqay9LOVNMUTZPdU5KZm4remc1cEFWL2ZMOE0rLzkwbVVvemk5YUtydXRLUkdMQU05KzY2MGx1K01OYmRxK1c2VU80K3hPZWxBMC91UWRhM1pvYnRsUWF0MXcxUWdVOFYxaTVjUUtYOCtSbkN6dTJFU013T3k2UTdXTkoyOUJVWUhOYUViRVVyUzNNbHhMdWVKeUpTMmJPNjFRWW5CZEpMR21CMkN4VHUzSnlhSnZkZzRaV0c2MGlOS2dtc1VGV1QwQXlEQ1ZYVUNaRDF1Q29HUXpHdzlyK0JVUTV6eW5aWjl1RmsyQXVKMWZEaUV0S1cvdG5EQWQwTnZIY0IwUDFFTU1KZTRqK0R3ODBCZ2h3cCt1cTdnVlZYd3RrSHRSd0dVUGFLU1JNWnNtNzdhdmNCeW1pNHZXa3VVRElZM09PZjFUNnVwMkx4M2lNai9pVG9XZFdoYkJrY1VpNm9sT1FiUURTNWZOekJGYzFwMStuaC81MTgrM1Qxb1l4NmQwSDZhZXhxSWU0TG5ObjhjczBkdDI3TFAvaHUxR0JlRjVCU0VJeTVUTVVZKzBmc2xnd2l3akFBR21SakVJU0ticlFBblovTTYiLCJtYWMiOiIxZTk0Nzc4ODIwMWY2ODU0ODQ5NWJiY2IyNWYzMmU5ZTI0YzQ3ZmM5ZTE1ZDExNzBkZTFiN2Y3ZmFiODllYTA0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InBDaXhRVjErUnVrY3FiK1B0K3ZjakE9PSIsInZhbHVlIjoielE2ZWZKZkFvYUtOVitZSnB3dmRCREFodmg1RmY3R1dXUjdPTkhZRWIzUHpzRjBJcjhKTG9VRE5LQUZEeTRwMDRsbkVqRTJEa0JJbzc0ZDlTY3RZUTNhdi9EbUcxenhpUzdHdmxIWWRiaFhXNTFrZGkzMDFEeElIVEcrUVp0MHFoNStpOUZOSTBQZU82V3BIZkdaUnVNVktDNUV3VHNVME02dzkwRk8xeUlSZit4RDFYVG5LNVJGTWFPdWtxOFowa1IvTE1Va3Z3MVA3OUR2aXRqMVVwRHBqOXRKYjJYbzQ0WjdoamI1Z1lTVE51NkVPWmJydzdVYVh5ekw4VXVyUkJTQTNEcy95cWdEK2hYd1JrOXpIWmt5NzZxdjFZYXZiWjB6alplcVIycVdNY0d0aGRHQklldGE5RVhxR0NOdVRWckFSRzJFdnl6bW1lM1Ewc0VNQitVZkJyRFptZ3ROVHBWek01RE1hcVptcUhqekJiajFqR2lrWm0za1c5WVV0L1VSdmU0UkZLTFhlNEZ4eVE0cjJKUVNtNndtaDVJTmM0ZEtBejM4dHFLWTFxR1dtTklxNkUrTkxqZkMwL3pnaWxKUGU3aDBuYnhGRStMWmh5aGVYNFd4RnRrM0V4akhXa0lod1dkTTZkVC9zM2MxVnArMHExVU56QVRBbXNmQUwiLCJtYWMiOiI2MWFkYmU5NWVmZWJiMDljZGNkMjI1ODk1ZGJhNzJlYzhjZTllY2FjMzhhNzU0ODFmYzFhYTVhZTQwMmMzNmFjIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:39:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-440620654\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1314577711 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1314577711\", {\"maxDepth\":0})</script>\n"}}