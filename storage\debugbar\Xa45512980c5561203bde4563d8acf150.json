{"__meta": {"id": "Xa45512980c5561203bde4563d8acf150", "datetime": "2025-06-30 23:12:45", "utime": **********.679179, "method": "PUT", "uri": "/customer/11", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.183065, "end": **********.6792, "duration": 0.49613499641418457, "duration_str": "496ms", "measures": [{"label": "Booting", "start": **********.183065, "relative_start": 0, "end": **********.52115, "relative_end": **********.52115, "duration": 0.3380851745605469, "duration_str": "338ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.521158, "relative_start": 0.3380930423736572, "end": **********.679201, "relative_end": 9.5367431640625e-07, "duration": 0.15804290771484375, "duration_str": "158ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51652160, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT customer/{customer}", "middleware": "web, verified, auth, XSS, revalidate", "as": "customer.update", "controller": "App\\Http\\Controllers\\CustomerController@update", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=194\" onclick=\"\">app/Http/Controllers/CustomerController.php:194-248</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.08244, "accumulated_duration_str": "82.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5479841, "duration": 0.02597, "duration_str": "25.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 31.502}, {"sql": "select * from `customers` where `id` = '11' limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 961}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 64}], "start": **********.5772, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:61", "source": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=61", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "61"}, "connection": "kdmkjkqknb", "start_percent": 31.502, "width_percent": 0.473}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.583847, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 31.975, "width_percent": 0.412}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.596826, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 32.387, "width_percent": 0.619}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.598593, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 33.006, "width_percent": 0.461}, {"sql": "update `customers` set `is_delivery` = 1, `warehouse_id` = '8', `customers`.`updated_at` = '2025-06-30 23:12:45' where `id` = 11", "type": "query", "params": [], "bindings": ["1", "8", "2025-06-30 23:12:45", "11"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\CustomerController.php", "line": 238}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6086059, "duration": 0.05485, "duration_str": "54.85ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:238", "source": "app/Http/Controllers/CustomerController.php:238", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=238", "ajax": false, "filename": "CustomerController.php", "line": "238"}, "connection": "kdmkjkqknb", "start_percent": 33.467, "width_percent": 66.533}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1282195035 data-indent-pad=\"  \"><span class=sf-dump-note>edit customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">edit customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1282195035\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.6021, "xdebug_link": null}]}, "session": {"_token": "c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "تم تحديث العميل بنجاح.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/customer/11", "status_code": "<pre class=sf-dump id=sf-dump-389331100 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-389331100\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2011648185 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2011648185\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1182147156 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Ham<PERSON>oud Qasim</span>\"\n  \"<span class=sf-dump-key>contact</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>tax_number</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>is_delivery</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>billing_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Debt account</span>\"\n  \"<span class=sf-dump-key>billing_phone</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_address</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_city</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_country</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_zip</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_name</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_phone</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_address</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_city</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_country</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_zip</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1182147156\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-675182215 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">370</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; XSRF-TOKEN=eyJpdiI6IjVLbXZQYjJ3dDRYNTJ5RmdKUmxXREE9PSIsInZhbHVlIjoiSFVoS2FGaERKT1dRU1djSFkra1Y2OFh2bUJGdEI2eC9ScjFjR2JRQzlPclhZWWcxOGpNc1huMVlPVFJNemUwU1AvbnJscGpxcmhEZjBGN3VXQytGSFpTMGE0d3lhTElKZURtSFJhMDB6VWdRRmR2ZWVzVTU3TFBJaU92cURFMkRvdWE5VEF1VXo1T3ZwMzVCVkVrRWVmanpKSy9nN2I5ZjJpTzlyVnZEZG9IOWp1VXRLMHhIRkJad0tFYS9WNzlYR2I2a2d0aCt3U0FmZmo3RG9lMkFJMVB2K21xMm5BZ3V5bDAyZEtzSnpqNFFZTWV0bENyTFlIalZxTXNPdzMvNTZOWS92ck8razFCbDRkZXVSZjFXS2VTMVlSL3QyU2lubmFURmFuazgySFhZdjZBTDNhdkJSN3h0aHNpd1RjNDBUbHludE1FM3BIZG1IVmlEYndaOTI1di9aVitlb1FPeUEzRE5FdzZBZjM1U01OTlBqMGNxWVlDcGtnZFhKSXFKd0kwVDMvREJkVjBTczUzOXZJY3FQcklvdDNBOUtiUFJBYTNPUDRPMmMvRStVclBpaGlSdy9iaktSTVo5dTNkemFUZ0UwVHFPL2F1YS9XbG5zL05zMHNFamh2MmczRFlEMnNyVUx2Q2VLZXZ4clpSMWcydkFhdUZBQlB4ckE5dm4iLCJtYWMiOiI4ZjhiZWYyMmVhOWIzY2MzNjE5ZTJlNDhkY2FmZmE0ZGUzNmU5NmM3MDk1NjE5MWNjMmY1ZjE3YmRhNmY1ZmNiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im1qeU1VYjBmOU9tM3phMGtkNkw2T1E9PSIsInZhbHVlIjoiTExvU3ZqbGJwQ3Z0cDkxVEMrM20vL3VEbWd2N0FzcXBndjJZSDA2ak9jRFNFNTQ1cmxmd3RSTW9xc0FMQVpkVXVld2NTcGpyeHM0SGJObnRnM29MQnpQTnJHRDhFNkdxN200ZW1tblJJaHFxYnFmWjZ3MEN3eDcvc1dBaGdQU2hmb1Nwbml6ZnJOTStQbHkyM2JYZUZRSWRKZUlrZnZEU21TcVYxUFZpWkJDU0VDVUl4NFRmMHdDR2tTNU5TQmIzOUIrMXpYQlhJRXVVY3ZrMWRWUHlYblZ1dCtSVC9lRmptZ1JDeGQzckhRYkVwQWpLKzVXaGZ1b0VmTVd4VGc4b2FVbHpiQXZzMTNwR0xGWUhYMDNkamN3WHowUlltcno0Y1djYzdkSHdLNzZ5UTlHclNDUHZKZjFQSzVxaWZ0OTh3amcvemZVRkp5MUNLZWV1cjNnMjdkeGpyY2FoQ1ZSOXNPb1M2VmFJVjc3SE5adW1Wbk5WekNYeUpFL1dMNjlhMFlMY0pIcCs1N3FUZ1pEamNBcW94b2gwdGUrTCt1UTlJVUdJRlAyOXRqdThWUlp0L1ZKOGJjdThRWFdrUDU2QU04bmdEbUpSdmtFZXA0MGlTNnM1MXhFSmd5MTd4VjVDWDBxcUJMbkMwaXZzZVB2Y1lGWDF3eUJtMVJFMHdaTEsiLCJtYWMiOiI5OWQ2NDhlZTY4ZDg3MTRjM2VhNTIzYTQzZjllYzNiNmUzNjEwNWU2ODYxMDVmYjcxMzg3Y2IxMWJkNTEwNjZlIiwidGFnIjoiIn0%3D; _clsk=1kg512v%7C1751325159599%7C24%7C1%7Co.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-675182215\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1122476873 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0M1K0sdNadPD2LVrnt8LmBw227nA9F1U5e3ROaJK</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1122476873\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1883417431 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:12:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlEzMWxXdy9Ob2pOdS9MSm5Gb1Y0d0E9PSIsInZhbHVlIjoiYkpka2JsZlVHSFlyTVllRGZrTWIrTHpQTGN0K3JDQWd1SmI4OUFWZkl1WmJvc2RhTysyWXJWSTlJTndXRWV4KzAxVGRFRkhqL0ZPOFhWR3lsVHBoWUNWYWFDR0ZjTHBZZk9tbWRRMzg4WHNCaWtQd3FiQXhFTUF4eVJyV2U4YldpSjRLWHExYjlucEJzcjcvd1hFU29hYVM1OEtyTFZxZFFodUhWYWRSWUk4Y3VpRTd4R2M2Y2F0VWRFbTlqM1BsSkNYWlZBeGRZQ05jbjZoTTZKSk5sSlJQV1NwZFpJQTVCWStnWFkxaVU4YkxZemJ4eWtnTmxHaXRFN0JmMDdFa1FGSE93Q3h5RjM1R0l5MUYvTmo2bGk2eVpkUHl0Q1piSGxhMWxHTTl0TXBQak40SWRlSVVtNWdlcDlFWVI4c1MrRjZJUkYvZW9JN1d0TEtZcXQxS0oyTlVBaFVHVDlwbVdoY3R3encvbkYvNVF3djRMY0FYZ21lcFVmWG12N003ZFJybm9yOVRibXB6VUxpVWJ3anZyamlETWNiaU9sTjlBT0ZiZll4UHZTdkROS0tpM1lHUG9BRzJsRDhveUs0SVc0WkVvVVdGcVlpUU1SdHBqaXFDdEpDM2xNU1I5R1JyUDBmbzkydERsSmJMR25yUENKelNnWld6RVByVlIyR1MiLCJtYWMiOiJkY2JiZjliNTg1ZTA0OTllMzdlODk1NjJmYmVjN2I1YzM3NDNlOTIzZjg1ODY5NDU4YTUxNGRlNjdmYjZkODM5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:12:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlhOa0QzUllGSFdSMDZSR0tSdTQyU3c9PSIsInZhbHVlIjoidWlSQTZBWUFSN1paWlRReDZmRzR6R201WFUxS05NUHI0UVVqR3BzdHNzbzVWbUk2OVIxdkhtYXBpWURac2pMSkxmQzhrZlYwMHA3dHNjWFhObEF2cEpHckRWNzQ3bGRLcG5DN2tXL3VkdU1SRTNFU2FwaDF6Y3pOd053MCt0d0JNcjFLZHBsQXNEMi9jMzdOTGZJejZBa2FLV2FGWk9LR2NJMlVYd1k3OTVzNWxTWG5QTnFoR2d5TW5HYVJpYUJDamNTVC9zUmZvTXFKMk1zemFkOHhVVnZ1V3ppS2JBTytaQkFwa1NIajdqQktUVUpGSmd4V1lINzJmbEJIZmtqRHpsNU1Uajd5YmMrNTBEWWZzSDVva1lPZ3dXUlRlY2lrK1MrWEtTYVI5anZRZk15K3VRN0RpY0pRbkZrYzczNzFCN2o3QThwNVlIZ3l0TEU0QjA2YWxjVjVqTjloZEVVU0d6cTFJMS82ZGdLNnZzSWJEY2h0KzNPMXFOb3owR3NMeThUY1lCSDd1TTFoWjF4dUE3WHNPY2dTMmV3T0pwV1JrbFVWUENkYXJnVDRUSnZtRmxLWDEwbHFuL2tkbmlRdUg4SVllc2R1U0Z3elQwQnpYbFZxckZvUXNKZEZuRUpUT2hkaCtIMVYzSUdsVmRhbktVR2JOM0p6cUtrMmM4Sy8iLCJtYWMiOiJmY2Y5OTFkNzE5ZTE4YzI1YjU4YjI1NTc0OTk0OTNkNWZhNzZkMGM0MzhlNGQ2NTk4OGM3ODAxY2ViNzNjNTc2IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:12:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlEzMWxXdy9Ob2pOdS9MSm5Gb1Y0d0E9PSIsInZhbHVlIjoiYkpka2JsZlVHSFlyTVllRGZrTWIrTHpQTGN0K3JDQWd1SmI4OUFWZkl1WmJvc2RhTysyWXJWSTlJTndXRWV4KzAxVGRFRkhqL0ZPOFhWR3lsVHBoWUNWYWFDR0ZjTHBZZk9tbWRRMzg4WHNCaWtQd3FiQXhFTUF4eVJyV2U4YldpSjRLWHExYjlucEJzcjcvd1hFU29hYVM1OEtyTFZxZFFodUhWYWRSWUk4Y3VpRTd4R2M2Y2F0VWRFbTlqM1BsSkNYWlZBeGRZQ05jbjZoTTZKSk5sSlJQV1NwZFpJQTVCWStnWFkxaVU4YkxZemJ4eWtnTmxHaXRFN0JmMDdFa1FGSE93Q3h5RjM1R0l5MUYvTmo2bGk2eVpkUHl0Q1piSGxhMWxHTTl0TXBQak40SWRlSVVtNWdlcDlFWVI4c1MrRjZJUkYvZW9JN1d0TEtZcXQxS0oyTlVBaFVHVDlwbVdoY3R3encvbkYvNVF3djRMY0FYZ21lcFVmWG12N003ZFJybm9yOVRibXB6VUxpVWJ3anZyamlETWNiaU9sTjlBT0ZiZll4UHZTdkROS0tpM1lHUG9BRzJsRDhveUs0SVc0WkVvVVdGcVlpUU1SdHBqaXFDdEpDM2xNU1I5R1JyUDBmbzkydERsSmJMR25yUENKelNnWld6RVByVlIyR1MiLCJtYWMiOiJkY2JiZjliNTg1ZTA0OTllMzdlODk1NjJmYmVjN2I1YzM3NDNlOTIzZjg1ODY5NDU4YTUxNGRlNjdmYjZkODM5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:12:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlhOa0QzUllGSFdSMDZSR0tSdTQyU3c9PSIsInZhbHVlIjoidWlSQTZBWUFSN1paWlRReDZmRzR6R201WFUxS05NUHI0UVVqR3BzdHNzbzVWbUk2OVIxdkhtYXBpWURac2pMSkxmQzhrZlYwMHA3dHNjWFhObEF2cEpHckRWNzQ3bGRLcG5DN2tXL3VkdU1SRTNFU2FwaDF6Y3pOd053MCt0d0JNcjFLZHBsQXNEMi9jMzdOTGZJejZBa2FLV2FGWk9LR2NJMlVYd1k3OTVzNWxTWG5QTnFoR2d5TW5HYVJpYUJDamNTVC9zUmZvTXFKMk1zemFkOHhVVnZ1V3ppS2JBTytaQkFwa1NIajdqQktUVUpGSmd4V1lINzJmbEJIZmtqRHpsNU1Uajd5YmMrNTBEWWZzSDVva1lPZ3dXUlRlY2lrK1MrWEtTYVI5anZRZk15K3VRN0RpY0pRbkZrYzczNzFCN2o3QThwNVlIZ3l0TEU0QjA2YWxjVjVqTjloZEVVU0d6cTFJMS82ZGdLNnZzSWJEY2h0KzNPMXFOb3owR3NMeThUY1lCSDd1TTFoWjF4dUE3WHNPY2dTMmV3T0pwV1JrbFVWUENkYXJnVDRUSnZtRmxLWDEwbHFuL2tkbmlRdUg4SVllc2R1U0Z3elQwQnpYbFZxckZvUXNKZEZuRUpUT2hkaCtIMVYzSUdsVmRhbktVR2JOM0p6cUtrMmM4Sy8iLCJtYWMiOiJmY2Y5OTFkNzE5ZTE4YzI1YjU4YjI1NTc0OTk0OTNkNWZhNzZkMGM0MzhlNGQ2NTk4OGM3ODAxY2ViNzNjNTc2IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:12:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1883417431\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1591557240 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c89qUV33Y0hqm4eWrYjWRlsvWoqbenpXUhIEyDgN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"22 characters\">&#1578;&#1605; &#1578;&#1581;&#1583;&#1610;&#1579; &#1575;&#1604;&#1593;&#1605;&#1610;&#1604; &#1576;&#1606;&#1580;&#1575;&#1581;.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1591557240\", {\"maxDepth\":0})</script>\n"}}