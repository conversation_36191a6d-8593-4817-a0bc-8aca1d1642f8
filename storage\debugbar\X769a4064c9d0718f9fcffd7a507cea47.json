{"__meta": {"id": "X769a4064c9d0718f9fcffd7a507cea47", "datetime": "2025-06-30 23:08:11", "utime": **********.425871, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751324890.899573, "end": **********.425895, "duration": 0.5263218879699707, "duration_str": "526ms", "measures": [{"label": "Booting", "start": 1751324890.899573, "relative_start": 0, "end": **********.330804, "relative_end": **********.330804, "duration": 0.4312310218811035, "duration_str": "431ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.330815, "relative_start": 0.4312419891357422, "end": **********.425898, "relative_end": 3.0994415283203125e-06, "duration": 0.09508299827575684, "duration_str": "95.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45707192, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02566, "accumulated_duration_str": "25.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.369925, "duration": 0.02462, "duration_str": "24.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.947}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.404632, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.947, "width_percent": 2.065}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.413795, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.012, "width_percent": 1.988}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1865508250 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323255132%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhkMUZpQVZLazdqRmFCeEtQVkNkWHc9PSIsInZhbHVlIjoiRVN6SDM4S0V3TjBhMVNhdHM4aW9Idkc0TnNZdnZxZjUwVUZqUTI0K0srTldJSkduN1lyVlNMMm90cFJhZEFpQkJBMmVremg4Ums2WjNxbk9rNWE4T2VIVUcxWFRnT3NJeW5LbS9KWkJPZFZvUEZSOWVENGFGamlqeDhtdEJaTEJObmJUVjhRbFFvcEliU3JaYjlNb1dsWE9FSTc3cGt4VkZOeVB5MHlocVBzaVBzbVczOWFUbTd6eXd6bmlYS0FuUlI0VStPUWI0T3dVT0Q2cGd0WHVTU3dtVGVDbmJ4UlRnQ2hLMTdad3BETzZZRU1td3BIU0w2V1E1czFYS1h0Y21uMDdVQzFsc3p5KzljdXNKbm1FRFZUNHRMcEtHRStVdFVpelVGSFFhSENMcFNETDkvUFJKRXExbVE0ZHBOY0N4QlpRQS9xdlNHa1ExNUw0SUQ4d1RvOTRaREc4MUNJQkg2Nmx4Rzk3L01IdlZ2K2V1WVl2S1A1eVBzaXVvdzRDM2xCbTZyaGlyRUJ3ek02b1VSUVJjaTFsVWZmV3d4TmEyQ3Qyb2dwNXYrMHlBMEtzTUF5UlZWNUVhQytUellBdFc3dWNnaXlDZjJjZVYvV3BESGMvRjRRSkdSWXEzc3pJVEJLVHBlK242T2dxUzJENnlDQkNQaDUycXNFTG9vMlIiLCJtYWMiOiIwZjcyM2QxZTg2MzU1YjIxOTZiNDQxYmYxZDlkNDNmOWU5OTVjNzc2MmUyMTZkMGRiNTMyYmJjOWY5ZDEyMDQ1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IitmaWVMczBMaEtPN0lUbUZDT2s5alE9PSIsInZhbHVlIjoidGNWVUpOWHBOa0VMQkVGckNnbGV3MFJuQzlwdmxjZ3lOTXJMelI1MDF2OUdMa2YzeklkRlExd2RWVlZlSThZVzFub3ozMXdiejNkOVZqU3pUUjJ0eFlmUW85RlYxbkNoMzc0MXVKdXJmQk5GL1dtakc2WnV2UXF4QmtvL3JOU3BsclR1eDdVYUphamJrQmo5YVYyZ1hxeG1MNlZmbnRKK244NkMyRXJBWXpjR05oVGJCc1EySC9PckFwMDhIL3QvenEvZU52RWRpMnZYMzVINHplVVJXK05oTG1mazFVRXVyZmxMcU42U1JlcFR6a2FHM2JHcTBDbkJDMVV0VCt6bnRUZUJZMkFRMSt3S1BkVFBQdlMzRFB3V1JtMHJaQ2IzT0ttdFh2MjhSbFVaTTUrR1pTeE93MVpvOU1ISVd4cjkvbXdSMC9LT0JpdC9qNzJoRWFXVysyVm1MOVkrZmxmdGxkMkwvRStTeHpKazBUWGdwVjNlRWJhUXpWcGNKV0lyVW9NSEpFWHNjaHVYdFVCU1VTWHcyb2x5c2pVVTNUaWNUN0RBUTlnbzA5MHU4NE1lOE02UTVUaU5ROVhZbVZDckJ0dHNHaGM0Zm9ueUlNSno3eERMSDhQb28xOFNWazVzSHY0MzJxaDhocHY5THlGbnYyWlUzR2pRbXRudTJSZHciLCJtYWMiOiI1MzA4ZjI2NTJiMTNkZDljYmNmOWRhYjVkZTlhZTg4N2UxMTNhMjJjOGFkZTBiNjJhNGMyYjIzODkxZmJkMDRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1865508250\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1846597108 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1846597108\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-403351737 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 23:08:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZCWjdCb3g2cGVrOGZFSGNnOWZMT1E9PSIsInZhbHVlIjoiUk40YmtMK2R1RE9YMTRkZjdCeFNPMmNjbkM1dUdoeU5VNUJFQW9XNWRrNEYrRDRjdDMwODVKZmprcFJjWVN3Smk3c3R4NnRXbUlJZFg2UjBhM3I4THFBNkZ4cjh0R1RPMmNvc1NlNmdUeHYzVlpWRC80ek5XM21qbWQ2YzIxNW5heTFycVlOWW9DVys0cHA1MHhxVEZYOE4rN0kvMUF3NFBzL3IyTUdOVHhUd0IzanRDQkVRa3JqMWxJNXdXYUt3K1E5c21jS0RiZHJRZTBXK2ZaWEhGQzFRR0pqVFJ0OHhFSWVubGZkdUMydS9qTGVSdFJXK1ZnTUVXK0I4UE9oZHo4TmtqMmdGSmNQTDBMdm45Q3NYYk9LNjVnSEtXaVQzUElRL0lKQnZsYzRjL0hTbVgrZ3lkS2pVejdlTjI5N01ZZ2djaU9VeklSaytISllBYTlGakFBUitpZnVXL084U0Y3SldGZWdxWnZLTkhtVHl6bit5UHdVSUFldnNpREpZdDFpUURnekJKb0R0N0xMRVhnb2NuUW1iY2cyNXZhTDZyUjFTUGpoNmIxZkkwbGhveHZlRVloaTh1QkxqY1BqT3lMcHFnZVZ2U3I4cW42QTRVWW5sVExqN1JKTTg2cStSa2wrcWxmNEkvSTlUMG02d1UrL292VEQycFNKRjRKeW8iLCJtYWMiOiIwY2M0OGNmNDMzNzRiNTRjMzhkMWVmMTY3OGE5MzA3ZGI3OWNlMDY0NTE1ZDMwMjY4ZDUxZTAxOWVlYzMxMTNiIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:08:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik8wY2dRSS9SQkpISk93Umo2WDdURHc9PSIsInZhbHVlIjoiSi9CckVwT3ZCMUcrVGFNbnBVQzkwbW4wYlZIWWJqcXg0MFpCNGZHWUI4eWkrbWpIa2pWK2NGekcrblJqYmJnZTRRT3QyT3FILzlzOVpFMEcxKzFURU9Dc1dvWE5YYXIrL1kxZkZNTFhuZ2RDaHpLSGxOVFFEYm91Q1YyWUo1U2xMY0VoTWxZZ0k4SDY4aVgrWUlzMlZXL2l3MS96SHVyam5DZmtiUjRpYUJmR0QxRWdpaFBzcTM3VDJXZkt3Y2pTa2V1ZTl2TVR5Y1NsY3BUMjdJK0pReWxXZE1CTnozaVZMMXdTL0JvSjdRcWJjOSs5blkwOG5oSGtyaE5jKzdsWFlERVFrYm4zQndtbGhyTTRGUjZubGN4aGQ4OW5VeUkzOC9KTDdoNllmOXN6dU1BREFxSlR0NUR6SGxxTXk4OGo2NFdQc1V2YURVOFk4K2h3LzFlZ2lxQjR1UnRoVVhtOGpTVDNpMVV5ajNUTDhTY25xa1I5Y1Z2VmQrZmtBR3prVjc4Q3dGY0NRZW1MWDJ3SWZSc1hYZWs2S3RrbUN5U2h3WGRXWC9ES1k5eHU3ZmFnK1ZSMTY3Vk12V2FrR2poZVNRUEJSUTB6WnJlRDkwdXo5ZjV1UEd4MEtKNTZHRUUvMTJxT0gzNWkwNThaWWIyMURsWUswOGdUZ3hwVlJOUTIiLCJtYWMiOiI4YjdiYjlkMjM2M2M3OTkyMDg0ODQ0MWY2MmJlZTMzNGQ0MzgzYTFhNjM1MTc5NjIyM2VjODdhMWNiNTMwYmNlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 01:08:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZCWjdCb3g2cGVrOGZFSGNnOWZMT1E9PSIsInZhbHVlIjoiUk40YmtMK2R1RE9YMTRkZjdCeFNPMmNjbkM1dUdoeU5VNUJFQW9XNWRrNEYrRDRjdDMwODVKZmprcFJjWVN3Smk3c3R4NnRXbUlJZFg2UjBhM3I4THFBNkZ4cjh0R1RPMmNvc1NlNmdUeHYzVlpWRC80ek5XM21qbWQ2YzIxNW5heTFycVlOWW9DVys0cHA1MHhxVEZYOE4rN0kvMUF3NFBzL3IyTUdOVHhUd0IzanRDQkVRa3JqMWxJNXdXYUt3K1E5c21jS0RiZHJRZTBXK2ZaWEhGQzFRR0pqVFJ0OHhFSWVubGZkdUMydS9qTGVSdFJXK1ZnTUVXK0I4UE9oZHo4TmtqMmdGSmNQTDBMdm45Q3NYYk9LNjVnSEtXaVQzUElRL0lKQnZsYzRjL0hTbVgrZ3lkS2pVejdlTjI5N01ZZ2djaU9VeklSaytISllBYTlGakFBUitpZnVXL084U0Y3SldGZWdxWnZLTkhtVHl6bit5UHdVSUFldnNpREpZdDFpUURnekJKb0R0N0xMRVhnb2NuUW1iY2cyNXZhTDZyUjFTUGpoNmIxZkkwbGhveHZlRVloaTh1QkxqY1BqT3lMcHFnZVZ2U3I4cW42QTRVWW5sVExqN1JKTTg2cStSa2wrcWxmNEkvSTlUMG02d1UrL292VEQycFNKRjRKeW8iLCJtYWMiOiIwY2M0OGNmNDMzNzRiNTRjMzhkMWVmMTY3OGE5MzA3ZGI3OWNlMDY0NTE1ZDMwMjY4ZDUxZTAxOWVlYzMxMTNiIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:08:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik8wY2dRSS9SQkpISk93Umo2WDdURHc9PSIsInZhbHVlIjoiSi9CckVwT3ZCMUcrVGFNbnBVQzkwbW4wYlZIWWJqcXg0MFpCNGZHWUI4eWkrbWpIa2pWK2NGekcrblJqYmJnZTRRT3QyT3FILzlzOVpFMEcxKzFURU9Dc1dvWE5YYXIrL1kxZkZNTFhuZ2RDaHpLSGxOVFFEYm91Q1YyWUo1U2xMY0VoTWxZZ0k4SDY4aVgrWUlzMlZXL2l3MS96SHVyam5DZmtiUjRpYUJmR0QxRWdpaFBzcTM3VDJXZkt3Y2pTa2V1ZTl2TVR5Y1NsY3BUMjdJK0pReWxXZE1CTnozaVZMMXdTL0JvSjdRcWJjOSs5blkwOG5oSGtyaE5jKzdsWFlERVFrYm4zQndtbGhyTTRGUjZubGN4aGQ4OW5VeUkzOC9KTDdoNllmOXN6dU1BREFxSlR0NUR6SGxxTXk4OGo2NFdQc1V2YURVOFk4K2h3LzFlZ2lxQjR1UnRoVVhtOGpTVDNpMVV5ajNUTDhTY25xa1I5Y1Z2VmQrZmtBR3prVjc4Q3dGY0NRZW1MWDJ3SWZSc1hYZWs2S3RrbUN5U2h3WGRXWC9ES1k5eHU3ZmFnK1ZSMTY3Vk12V2FrR2poZVNRUEJSUTB6WnJlRDkwdXo5ZjV1UEd4MEtKNTZHRUUvMTJxT0gzNWkwNThaWWIyMURsWUswOGdUZ3hwVlJOUTIiLCJtYWMiOiI4YjdiYjlkMjM2M2M3OTkyMDg0ODQ0MWY2MmJlZTMzNGQ0MzgzYTFhNjM1MTc5NjIyM2VjODdhMWNiNTMwYmNlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 01:08:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-403351737\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}