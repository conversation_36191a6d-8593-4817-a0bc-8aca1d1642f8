{"__meta": {"id": "Xf7c63ca4e1c4c7cadffa2876f1af0e98", "datetime": "2025-06-30 22:41:52", "utime": **********.425651, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751323311.971105, "end": **********.425667, "duration": 0.4545619487762451, "duration_str": "455ms", "measures": [{"label": "Booting", "start": 1751323311.971105, "relative_start": 0, "end": **********.341266, "relative_end": **********.341266, "duration": 0.3701608180999756, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.341276, "relative_start": 0.37017083168029785, "end": **********.425669, "relative_end": 1.9073486328125e-06, "duration": 0.08439302444458008, "duration_str": "84.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48184888, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.009739999999999999, "accumulated_duration_str": "9.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3759298, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 40.041}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.388613, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 40.041, "width_percent": 4.723}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.402143, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 44.764, "width_percent": 9.343}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.4048939, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 54.107, "width_percent": 5.749}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.409878, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 59.856, "width_percent": 25.975}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.414777, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 85.832, "width_percent": 14.168}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-335627316 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-335627316\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.408797, "xdebug_link": null}]}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1925706790 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1925706790\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-263554616 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-263554616\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2003961828 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2003961828\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-505241017 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323255132%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNzWkcvK3JOLzdTQU1BbjRPanEyRmc9PSIsInZhbHVlIjoiSUt6cFZNbE15VjdIVVVDdVZBQktyaXllYnYrdmhhS2kybkY0WDhNVklsbTNwNzZPTjcyZDhkbXMycHorYVVjSVAvVDBzVU1vcVBBYk1kb1g1NTl6azlmNCtXbFR6Z28zV3I1Z1pzYnVtZWdoTEtzdFg0Y1lZM2NVTG9LVndWRDFVQVVmMkhveFcrLzlFTndWL2wwMFpYdGRvNk9LVXl1OHgwS2plMGtYNmdtVVFhaGw3SDNPdXFscU5SdkphRmREMDZBdnMvOFUrSTJJbDA3Z3IxSmxOdmhNV0ZpQ2s1WTF5SlJ6d3BLeFhvc0g2d3h5ZW5pcm1raUpObU1GYTVXQU5GS1ZrZnMwM3ZheVB0UjEwUnVMR2tTZEMzblFLS2hKR2MwVURKeWcybGFSUzNabHlTRTEwOCtwTHdvSlBxcDNTci83dEIzd2x5aVg0TzVnYWoyVG03bGxHdUloNlg1NFZJdGVvWnQ1dHZHSmIzZnNyZUVST3ZGbVd4QVA2VUpZUS9ZdXdJV2pFcERncEpRTEw5TU92ZEdKamtOaE9ObU8wb1F0cUIrZENLeTRlcGYrODFucjBoV2g5WjBnMnM0M0trSEgvMWJGaHNzUCtEVXFrOEs0VmNNNEdUdmxreWRucFlVMTFHZWRTclIwM0l4c0VuS3RKeEY4Qk5VZTBNTjciLCJtYWMiOiI4OWI2MGQ1Y2JlZDk2NTNjZTk1NmZhODE1ZDY1YjFmNjEwODI4OTk0YTY3YTBiOGUxZDUwZjk1NDc5OTBhMjZhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InF3ZnBXVkhIb0NTVGYrMjdnZ3l4dkE9PSIsInZhbHVlIjoiMDdjWjNMa25ZNFcxRWFyS2hkRmlROUVaZWt3NDBPUVF6RDJpa3lWQ0pjMmtxT0loYlNTanRUNEhJZHEySzNwQnpwbzhFSnJDR3RVaUEyM2NxaVUvNGVFMHNDTG9ZRHZaby9hU1NIZUxzZUlnSVU5N0VBSE94bjllNy82Rno1QTB4M09HZWt2QTRseTg3VkhhK1h0TWF3SFkxUmRHZHhLbjYxR2RtZVlndjkybUZQN05xZkFNSWJQZDJTdHFacDJmK0RxMEpFUTZKeXVJaG1OeDVHVjJwczdpRzJRZVdrUGZ4YTZsbmtVZU01eW02MkpDOFVmbUNSYnkrRU8yemcxK3RHV2hMdXZrS1FLcUZhU2hGdGp1LytZa0d0cmY4dmVqNDJGcjZxcnNPTXllamhsQkJIWmQxSjR5MnJKNlhBSVR3bTJBaGFjd2F1amQ3Mk4vN1BqVW15Vlp6U0lVOWpyeVp3Vm1vTTdwWVV0RVVvbTRmSDczUS90MG9GSU1Eb0E0YUpFNUNOT29paFZ0K3hHcTVMMmFuMUhSKzRVTHA1cVFFVFVpSFI5OGxmL0FoRG1BZXpaMitYekZqMWVHeVZQdldqR1EwRTExUDZSYi9BQnhUOHdwZEhiSmc1NjR6aEhGUjhNa3h1a3hIWTlYWkNpdXo4am9IbnJFU2xrR2ZpbVgiLCJtYWMiOiJlMDM0NDhkZTJlYzhlOGRiYjUxMWUxMDM1ODNhZjQ3NjZmMjQ1MzdlZTAyNmQ0MDIzOWY3MzY3NTdiM2UzMWZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-505241017\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-131321038 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-131321038\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1318952058 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:41:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFFdXFXeEJOWU5XVk00Y2NYQW5lTFE9PSIsInZhbHVlIjoiajUxalJDTnpFNEZJTGNVVUVQODlYQ280S3RCYmtkZEY4aDh0WEx6dS9ldUFicHl0OGVVOFBEdE1LUkUrVVZ4L2FBakFWZFhpV2prNG5JVmc1UjEzRFcxYThiODBjN2VRZVk4NDZ6aWE3ejF5cHF0L2xhSTV2SDFxMUpwOHBqckRhUit3c2hEN2ZFZjhZbktINWkveEJaMzJ2MnQzVUxZTFpOM2g0dEkvWmhObXNlN096YUs0TXUxaDIxeWpXbUltNHl6dkZ0Tlg2UHVBWXFuRlBSd1BQRUNGSmlUOHIxRnRUNGdUUTI4UDliOTVGRlpZYzA0N3gyL3hEMVd1ejBZSi90RnlQcWNycUxJNjV3ZGpPdmpDd2VHNVpsQlE3V2xXZ2F5MndKOHdtSnVjL1QwZ3ZpbEY3dUYxRnVHa3paUGZacnAxay9YUjlHMlZhY0RId2w0Q2MvYjFzVWhNUE4xUCsvMURkRTc0Zm5TMTlpUkV4WDNPR1ptMHpwc3ovYnRHaEdrcVpqN2hDcjBobnMvVy9xRW96bGlHWVZyb2lNY1k3R3hBQ0NUN2djQ3dIY2lYbGlUbVZXcVNIZkVFb2puTHh5N0RMU3A5NzdEN2ErdG1TTkxpbjFibk9wVG00RmpiMGEwSzVhQ083NENiTkZiY1NvWUdnbkxUcjBXUEdXdEYiLCJtYWMiOiI5NDYwZjBlM2UyMzkzNjY2ZjdkYzIwY2FhODYxYjIyNmFkNDg5ZTIyNDBiYmE3ZGU0YjQ5NTQzN2ZkNGRiNWM5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:41:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZnckliK1dFL1NLUXdyVjJIV0lnTlE9PSIsInZhbHVlIjoiME5qYkhmc3JXY0dPdkRsaTV5T2U3cEVVK3JWcWJhdmVRNjhUa2dwQ2xEbXpmSUFxamxqcEJVNHI3OURqRHUyR1R1anhGVTRrMmJvbWNZaDQ4MlRlZUFLUUNhempBOGVNQkFsb0NIMm9tZGRZYjFmelJURGVtQXNVR09WVXpNZGhUTWk3OFBKell0ZWVqTW9DazEvZUNFbTA0cTNkbTRFVFQ4NEpSMytwUGovQTI5cUxhYXNTSklrWk9HTHZrelByNmhiUE1SdEJpc0VUZk1QdlBTTU8veWltVlhqeW4zbWppYmpSV2pHczROWlAzbEpLN2xpQllMZnVVSktWZ2EyNGdnZ25nMGxydUtyeDVQeUVnRWFjMjJhZ1AwOUZTa0w5bWtsKy9XWU4yenNhZXU3QXBrdDdZRkFqUDU5Z1c4c2RkckRhNXExOXNWQkZ1anpjdHVnaVFyTCtRelI0dlpPUm5tV2EyRXZlVDNzZisrcU96N3NXNElWM3dGUEREN3FFRTRTNC9YRERFT1FRWGVnUkx0MStycnhLME1PREtJdzYxK2crRE1FdXVFZWpROUR4NFBTTjRrdDk1VEtTcXBSTE5LN1FGNmx1K1NuZ0R4K1c5U2dDYTlMRTdVNFpyTmpXbjhlVWlzWXVzK2VodWl0M1FIdW93dFJBUStVeDlDbjEiLCJtYWMiOiIxYjIzMWU0Mzk5NTFmNDcyOGE4ZmI2Y2ViZGUzYzQ0ZWRhOTczNTRhM2Q1YWFhNWVkY2I3ZDVlMjRjMGMyZTgyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:41:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFFdXFXeEJOWU5XVk00Y2NYQW5lTFE9PSIsInZhbHVlIjoiajUxalJDTnpFNEZJTGNVVUVQODlYQ280S3RCYmtkZEY4aDh0WEx6dS9ldUFicHl0OGVVOFBEdE1LUkUrVVZ4L2FBakFWZFhpV2prNG5JVmc1UjEzRFcxYThiODBjN2VRZVk4NDZ6aWE3ejF5cHF0L2xhSTV2SDFxMUpwOHBqckRhUit3c2hEN2ZFZjhZbktINWkveEJaMzJ2MnQzVUxZTFpOM2g0dEkvWmhObXNlN096YUs0TXUxaDIxeWpXbUltNHl6dkZ0Tlg2UHVBWXFuRlBSd1BQRUNGSmlUOHIxRnRUNGdUUTI4UDliOTVGRlpZYzA0N3gyL3hEMVd1ejBZSi90RnlQcWNycUxJNjV3ZGpPdmpDd2VHNVpsQlE3V2xXZ2F5MndKOHdtSnVjL1QwZ3ZpbEY3dUYxRnVHa3paUGZacnAxay9YUjlHMlZhY0RId2w0Q2MvYjFzVWhNUE4xUCsvMURkRTc0Zm5TMTlpUkV4WDNPR1ptMHpwc3ovYnRHaEdrcVpqN2hDcjBobnMvVy9xRW96bGlHWVZyb2lNY1k3R3hBQ0NUN2djQ3dIY2lYbGlUbVZXcVNIZkVFb2puTHh5N0RMU3A5NzdEN2ErdG1TTkxpbjFibk9wVG00RmpiMGEwSzVhQ083NENiTkZiY1NvWUdnbkxUcjBXUEdXdEYiLCJtYWMiOiI5NDYwZjBlM2UyMzkzNjY2ZjdkYzIwY2FhODYxYjIyNmFkNDg5ZTIyNDBiYmE3ZGU0YjQ5NTQzN2ZkNGRiNWM5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:41:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZnckliK1dFL1NLUXdyVjJIV0lnTlE9PSIsInZhbHVlIjoiME5qYkhmc3JXY0dPdkRsaTV5T2U3cEVVK3JWcWJhdmVRNjhUa2dwQ2xEbXpmSUFxamxqcEJVNHI3OURqRHUyR1R1anhGVTRrMmJvbWNZaDQ4MlRlZUFLUUNhempBOGVNQkFsb0NIMm9tZGRZYjFmelJURGVtQXNVR09WVXpNZGhUTWk3OFBKell0ZWVqTW9DazEvZUNFbTA0cTNkbTRFVFQ4NEpSMytwUGovQTI5cUxhYXNTSklrWk9HTHZrelByNmhiUE1SdEJpc0VUZk1QdlBTTU8veWltVlhqeW4zbWppYmpSV2pHczROWlAzbEpLN2xpQllMZnVVSktWZ2EyNGdnZ25nMGxydUtyeDVQeUVnRWFjMjJhZ1AwOUZTa0w5bWtsKy9XWU4yenNhZXU3QXBrdDdZRkFqUDU5Z1c4c2RkckRhNXExOXNWQkZ1anpjdHVnaVFyTCtRelI0dlpPUm5tV2EyRXZlVDNzZisrcU96N3NXNElWM3dGUEREN3FFRTRTNC9YRERFT1FRWGVnUkx0MStycnhLME1PREtJdzYxK2crRE1FdXVFZWpROUR4NFBTTjRrdDk1VEtTcXBSTE5LN1FGNmx1K1NuZ0R4K1c5U2dDYTlMRTdVNFpyTmpXbjhlVWlzWXVzK2VodWl0M1FIdW93dFJBUStVeDlDbjEiLCJtYWMiOiIxYjIzMWU0Mzk5NTFmNDcyOGE4ZmI2Y2ViZGUzYzQ0ZWRhOTczNTRhM2Q1YWFhNWVkY2I3ZDVlMjRjMGMyZTgyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:41:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1318952058\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1310096165 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1310096165\", {\"maxDepth\":0})</script>\n"}}