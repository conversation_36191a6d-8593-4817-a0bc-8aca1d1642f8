{"__meta": {"id": "X08f4682308b11c5de7c2699400049825", "datetime": "2025-06-30 22:42:51", "utime": **********.870869, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.392753, "end": **********.870884, "duration": 0.4781310558319092, "duration_str": "478ms", "measures": [{"label": "Booting", "start": **********.392753, "relative_start": 0, "end": **********.782977, "relative_end": **********.782977, "duration": 0.3902242183685303, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.782987, "relative_start": 0.39023423194885254, "end": **********.870885, "relative_end": 9.5367431640625e-07, "duration": 0.08789777755737305, "duration_str": "87.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48170176, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.008020000000000001, "accumulated_duration_str": "8.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8232262, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 23.067}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.83339, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 23.067, "width_percent": 10.848}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.848782, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 33.915, "width_percent": 5.736}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.8513129, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 39.651, "width_percent": 6.733}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq22\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8562288, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 46.384, "width_percent": 33.666}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq22\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq22\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.86131, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 80.05, "width_percent": 19.95}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq22%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1225497294 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1225497294\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.855282, "xdebug_link": null}]}, "session": {"_token": "owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-747558875 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-747558875\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-761246951 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-761246951\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1722812612 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1722812612\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1392307779 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=133h63t%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=eiqg7d%7C1751323255132%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5CSUVORnNNUjhxenpDaGc4dGt1aHc9PSIsInZhbHVlIjoiajZlaWxoekhQTjdodUV0WVRIakV5RFlUSUdLY0xESi92R0ZidkFUa1FXRHhzMklIM3Uyem1WZ1EwdWtNZmozSm9PdElUS0oyNXMwcFRybjhlS0I1cFpiUldNdStDK0lEZWRxcGd6OGdMT0V0ZzZjb05hVXV6Wk5RMnlLYnhlYnIrM2pwcng1aTZzUFZkRjRLVVkyVTk2KzE3YTcrMWVydXpKV21mK0ZrZW11dHdLZVVpZ2puNWZjSFRnajljbXNkZk9sRExLNmZqUnNGNzNDd09nVGtWeFhobUltUjlKaE5NbVZhc1E3RWJtc0tMUVkva1E0K1JQOVkvbEtuZlZCRFIxbzBneWJlcE5CSGZiWEcxUExyMXBXMUd5aDZpdVFIWlZiK2xRNktzRnlROVpQR1VHSnZ0a25LL1lyVUJMSUJWd3JBTnNsYWRhNWxPb01scVRkZnZkbEVZeWl6L09YN3RCSHBYTkdHYW5QSzQydXJDUFhETnJZTGVSQ1Vvc3c1bHNNcUVUVUxqa252UkFvMHBzbncxdDlWNWp3RzQ4KzRiN3o0amFBY2daNzVTbUhXM1dVYVQrdXh5VWxESDludWpMNmNqT1g5MzlxR3RTVFJLa09Kb0hTc2lOdmtEZkdTTU5UWENqMnA3aG5MQ0FPaC8xam9FakFZK3laRnJZSTgiLCJtYWMiOiJiMTU5ZGYxN2Q1ODUwMzg3YmY5ZGEzNTMxMGMyYWE3YjJkNWJkYjAxNTRkNWU1NDI1NTBlMzk1OWFhMmVmMDgxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IktPRVhRdnNnYXZUQWRySXBuZ3QrQ0E9PSIsInZhbHVlIjoickpjZFpGVlB0RmhRblM3VlBaRnJWR2dKQnZRRnh0b3hGOWxBTDJ0a081Uk5sdDBNUXNyZEFmRUdsbGNieGd2dFdIbG9XMUQybk1heEZCWTFCcmlWK1BOZHVBNzNCd2JYQmNvUWtrNFRwSUxXb1h3aXBCejdRSDAvYTFUN3liSDZIOHZsOC81NEZkUVdhcUJudVhUR05WREZ6RVRRR0MzOHVjbEE4Z2UzU204bUh0MGtUMTF5YXBXVDRXZi9YOFhCbWl6QnZNdFJHbHcwQ2hFOHRaOGw5aFRYS2FaZHFuMHgwSnpPYTFVTnNaWC9mbStmUjZDNHc4N0JwTkdwSU4rYTdEbUluQ1lZYWhuR3dydmxsWWR0dUxCNVExaFpZcE56VWlKTkNrclVJekE5NTBLZG41SVJJblhua3RUTmZWdHJELzVnN3FJM09LQUx5aVhKb1AzdENINVJBcDNjT1I3NSt1MHVEQWc1a1prOVFHYVhaSDlFeGdadUpVeTZkandrdlUxTFQ5V05qdktHY1ZyWVIyVlZ3S0hkZWc5ekJBcGFKSGpVRXpZV0dzRzZSTkI0dTJnblNnQnpzTk1FVGxoT05OVm9rZjd2UjVzVDEzdXdzRVlhTE5ydXNCazRLY0VFTk1Cb1pFdDUyTDNSUWVldlk2eDMxQnF2WFgyRzg1WFUiLCJtYWMiOiI3NTEyZjY3NTQ3ODFhZTlhYzA2ODhhY2U1MGRjNWM1YTdjNmYwYmYyYmRjZjEzYTBkM2IyODg3NWQ0OTMxZTA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1392307779\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1500351945 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I3i0TW8kW6Waimgip40nuZgd0DSGm1REB1ZvHiAX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1500351945\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1452347640 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 22:42:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBuVFNTODdyMnRUTEU3SWpueEFZaGc9PSIsInZhbHVlIjoiLzlWZ2VSUGxvNnd6ZkJQd0hwSWhLQ003WXJhbU16Mm43RFNpRlp6c2J1L0lHMnZJMWNObFVqNnZ1R2lzYjF3NmdOZUM5QW9NQ0luMHprSU5yeFgyWHlXcFJmSVJLb0s5Y1AyMDZFeVNYVVFpSGcxSUhNSS94YkNjUWhwc1lEVG9PbUgrL2ZqMVBVQ2xueENUQldXSHhSakZZOThHMDIwQkVXdXRsTkw5bE0zUTFSYVYzVS9PWjlGcmpZT1JmSysvMHBIemlvRVBoRFdwcUdrTVBMQTl3UW93TFBhbkVPVmRsZ3BLZ09lK1VCa1JiOVdnY0NIYmZSN0d3aE94aGVWWmt5a0ptbmlMTlZ3TEFVc0tZSDFLV0hXdHc0aGdMM2hKK3JVRk1CdERYaWpCM29mcVBUaWYrNFAxcDQwdlZSRVRWanpZZTRXVmFzdUtWc2JCSE5GcFhjSTN5R29lMFRDL0tLaCtCV21oUVBLOVA3bS8wOSs1bmRjK0dCQ2hLS1R4a0xYUXlBejRuUVVWWFJMaVRKTEFvVW51TVQ5OXBxeFlwemFaRXRJeWNpVUhJeWVOYmUreDRhTzB4bkpuSWplZ0FMVUErVXllSGRyYURpdDJ1M3l0anZxaldPRmc5Z0lHODIzVFlPOVNOdm90ZnVBVUh6YmJvOHd4Ry9ML3B5RXQiLCJtYWMiOiI1ZTRjZjhkMTcxNWM3NmFmNTA3MjJmNmVkNGMyODYyYWU5NTY4MmYyZDZlZWVkMjlmMjFiNzlmYmU2NDY5NjZjIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:42:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik9taE1NL25GN3VtRG5tOW4zQ2dSQ3c9PSIsInZhbHVlIjoiSjBUZGVzNWljcVRldUYxTzVVOVZjUXZuTUdsOVdYUmtyWU04L3o5NW9NVjV3TW5VVFdRazQwNFNVSnlOZnVQWFNMQ0s0d2F3UDUxMWdpbTFYSFJ4R1JLbmVWa2hlRnU1Sm00MHUrMVVyVm85UHUzRGdodlR4cWdjejNWenliUjJ4WFlqaEE2dnhyNVQwZVZDZGJrcFE5TUlGaXhDSVhQRk5lT09KbUpDbWJpRW9nMjNPMUlOR3FFZjlDMlJGUm43UktvcmNJdkdlbEt6THlXcWd5LzFheVliUWdjRHFBdFNFSTJaVituS3loeWhnc2tqMFdicTFPcmU2cnQ5RDBUdXBOdS85c2tNYmRwQmRsT202NDlTQlg2Vk1RV3FoeUc4YUtBNE5NZEZZdDhVK2I5SDNSdW5CMVJpY2lwZ01pRXc3QmU2NFpBRUVvdHNIaHVqU1RTSzhhUHZEdFZwa01tMFBsQTM4Z2g2QnJVRkQ1UmpnQ3lLL3J3Tkp1cXM2S2dIM1d3UEx1UWFHaG8zd1VNa3VFcUU2ci9aTi9yWjlmQ0Z3LzFqMnQ3bHRiaXhjSU1BRUYwNTd2NVBxUHR5L3B6UStLRm9RaVdqd3N3bjJHbG9IU09UY0NMMlRHaFpkWFZHQlpWYUdBT1dPZDcrbk5OQzVXN0tsNjdLeXMyK0JVZEsiLCJtYWMiOiI0YmZkMjEyODJmZjYyZDE0N2E1MDQzZWYzNTE2NmYwNjBlZGEwYjAxODZkZGYxMDk1ZjdjZjU3ODMyYjc2NjU3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 00:42:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBuVFNTODdyMnRUTEU3SWpueEFZaGc9PSIsInZhbHVlIjoiLzlWZ2VSUGxvNnd6ZkJQd0hwSWhLQ003WXJhbU16Mm43RFNpRlp6c2J1L0lHMnZJMWNObFVqNnZ1R2lzYjF3NmdOZUM5QW9NQ0luMHprSU5yeFgyWHlXcFJmSVJLb0s5Y1AyMDZFeVNYVVFpSGcxSUhNSS94YkNjUWhwc1lEVG9PbUgrL2ZqMVBVQ2xueENUQldXSHhSakZZOThHMDIwQkVXdXRsTkw5bE0zUTFSYVYzVS9PWjlGcmpZT1JmSysvMHBIemlvRVBoRFdwcUdrTVBMQTl3UW93TFBhbkVPVmRsZ3BLZ09lK1VCa1JiOVdnY0NIYmZSN0d3aE94aGVWWmt5a0ptbmlMTlZ3TEFVc0tZSDFLV0hXdHc0aGdMM2hKK3JVRk1CdERYaWpCM29mcVBUaWYrNFAxcDQwdlZSRVRWanpZZTRXVmFzdUtWc2JCSE5GcFhjSTN5R29lMFRDL0tLaCtCV21oUVBLOVA3bS8wOSs1bmRjK0dCQ2hLS1R4a0xYUXlBejRuUVVWWFJMaVRKTEFvVW51TVQ5OXBxeFlwemFaRXRJeWNpVUhJeWVOYmUreDRhTzB4bkpuSWplZ0FMVUErVXllSGRyYURpdDJ1M3l0anZxaldPRmc5Z0lHODIzVFlPOVNOdm90ZnVBVUh6YmJvOHd4Ry9ML3B5RXQiLCJtYWMiOiI1ZTRjZjhkMTcxNWM3NmFmNTA3MjJmNmVkNGMyODYyYWU5NTY4MmYyZDZlZWVkMjlmMjFiNzlmYmU2NDY5NjZjIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:42:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik9taE1NL25GN3VtRG5tOW4zQ2dSQ3c9PSIsInZhbHVlIjoiSjBUZGVzNWljcVRldUYxTzVVOVZjUXZuTUdsOVdYUmtyWU04L3o5NW9NVjV3TW5VVFdRazQwNFNVSnlOZnVQWFNMQ0s0d2F3UDUxMWdpbTFYSFJ4R1JLbmVWa2hlRnU1Sm00MHUrMVVyVm85UHUzRGdodlR4cWdjejNWenliUjJ4WFlqaEE2dnhyNVQwZVZDZGJrcFE5TUlGaXhDSVhQRk5lT09KbUpDbWJpRW9nMjNPMUlOR3FFZjlDMlJGUm43UktvcmNJdkdlbEt6THlXcWd5LzFheVliUWdjRHFBdFNFSTJaVituS3loeWhnc2tqMFdicTFPcmU2cnQ5RDBUdXBOdS85c2tNYmRwQmRsT202NDlTQlg2Vk1RV3FoeUc4YUtBNE5NZEZZdDhVK2I5SDNSdW5CMVJpY2lwZ01pRXc3QmU2NFpBRUVvdHNIaHVqU1RTSzhhUHZEdFZwa01tMFBsQTM4Z2g2QnJVRkQ1UmpnQ3lLL3J3Tkp1cXM2S2dIM1d3UEx1UWFHaG8zd1VNa3VFcUU2ci9aTi9yWjlmQ0Z3LzFqMnQ3bHRiaXhjSU1BRUYwNTd2NVBxUHR5L3B6UStLRm9RaVdqd3N3bjJHbG9IU09UY0NMMlRHaFpkWFZHQlpWYUdBT1dPZDcrbk5OQzVXN0tsNjdLeXMyK0JVZEsiLCJtYWMiOiI0YmZkMjEyODJmZjYyZDE0N2E1MDQzZWYzNTE2NmYwNjBlZGEwYjAxODZkZGYxMDk1ZjdjZjU3ODMyYjc2NjU3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 00:42:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1452347640\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1339689728 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">owOgfrqIy416e0Ff4ZcWc8SplYgCDxitVWPVFbwk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1339689728\", {\"maxDepth\":0})</script>\n"}}